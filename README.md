

## MSC与美团本地联调

对于需要频繁修改msc并在本地打美团来验证的需求，可以先将组件发布到 mavenLocal() 中，再在美团中使用新版本包打包：

1. 将 `gradle.properties` 中的 `enableUploadToLocalMaven` 变量改为true，然后sync一下msc的项目；
2. 在Android Studio中的Gradle面板中执行 msc-android > Tasks > upload > uploadArchives 的任务打包；
3. 在美团中修改msc相关组件的版本，版本号为 `gradle.properties` 中的 `VERSION_NAME` 值加上 `-SNAPSHOT` 即可；
4. 每次修改MSC之后，再次执行 uploadArchives 任务即可，然后在美团中重新打包，无需再次修改美团中的版本号。


其他信息：

- 打包后的组件文件会放在 `~/.m2/repository/com/meituan/android/msc` 目录中。
- **上面的步骤1和步骤2也可以通过在命令行中执行 `JAVA_HOME=$(/usr/libexec/java_home -v1.8) ./gradlew uploadArchives  -PenableUploadToLocalMaven=true` 来打包。**


注意：

- **如果执行gradlew命令失败的话，通过`java -version`看下当前使用的java版本是否是1.8，如果不是，要确保是。**
- 不建议在美团中配置project依赖msc项目，配置繁琐，且问题较多。
