# Copyright (c) Facebook, Inc. and its affiliates.
#
# This source code is licensed under the MIT license found in the
# LICENSE file in the root directory of this source tree.

LOCAL_PATH := $(call my-dir)

include $(CLEAR_VARS)

LOCAL_MODULE := msc

LOCAL_SRC_FILES := $(wildcard $(LOCAL_PATH)/*.cpp)

LOCAL_C_INCLUDES := $(LOCAL_PATH)/..
LOCAL_EXPORT_C_INCLUDES := $(LOCAL_C_INCLUDES)

LOCAL_CFLAGS := \
  -DLOG_TAG=\"ReactNative\"

LOCAL_CFLAGS += -fexceptions -frtti -Wno-unused-lambda-capture

ifeq ($(ENABLE_FULL_LTO),false)
  ifeq ($(ENABLE_MSC_LTO),true)
      LOCAL_CFLAGS += -flto
      LOCAL_LDFLAGS += -flto
  endif
endif

LOCAL_STATIC_LIBRARIES := boost
LOCAL_SHARED_LIBRARIES := folly_json glog

ifeq ($(ENABLE_MSCJSI_SHARED),true)
  LOCAL_SHARED_LIBRARIES += mscjsi
else
  LOCAL_STATIC_LIBRARIES += mscjsi
endif

include $(BUILD_SHARED_LIBRARY)

$(call import-module,fb)
$(call import-module,folly)
$(call import-module,mtv8)# TODO chdc 变量切换JSC
# $(call import-module,jsc)
$(call import-module,glog)
$(call import-module,mscjsi)
# TODO chdc 通过变量配置编译哪个JS引擎
# $(call import-module,hermes/inspector)
