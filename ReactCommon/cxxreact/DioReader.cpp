#include "DioReader.h"

#include <iostream>
#include <fstream>
#include <memory>
#include <ios>
#include <fcntl.h>
#include <sys/stat.h>

// [MRN60: chendacai] DIO 适配，详见：https://km.sankuai.com/page/349814196
namespace dio {
    static const byte FILE_DATA_XOR_EIGENVALUE = (byte)0xAB;

    void xorByteArray(byte* bytes, uint32_t length, byte eigenvalue) {
        for (uint32_t i = 0; i < length; i++) {
            bytes[i] ^= eigenvalue;
        }
    }

    typedef std::basic_filebuf<char, std::char_traits<char>> FileBuffer;
    class DioFileBuffer : public FileBuffer {
    public:
        DioFileBuffer(const std::string& filePath, DioReader::DioEntry entry):
                mEntry(entry)
        {

        }

        DioFileBuffer(DioFileBuffer&& __rhs)
            : FileBuffer((FileBuffer&&)__rhs),
            mEntry(__rhs.mEntry)  {

        }

        DioFileBuffer& operator=(DioFileBuffer&& __rhs) {
            FileBuffer::operator=(std::move(__rhs));
            this -> mEntry = __rhs.mEntry;
            return *this;
        }

        virtual int_type underflow() {
            if (mEntry.size <= 0 || mEntry.address < 0) {
                return traits_type::eof();
            }
            // TODO dio: 限制数据读取边界
            bool needToXor = false;
            if (gptr() == egptr()) {
                needToXor = mEntry.flag.guardData;
            }
            int_type result = FileBuffer::underflow();
            // TODO dio: 将初始的buffer设置为size大小
            if (needToXor) {
                xorByteArray(reinterpret_cast<byte *>(eback()), egptr() - eback(), FILE_DATA_XOR_EIGENVALUE);
            }
            return result;
        }

        virtual pos_type seekoff(off_type __off, std::ios_base::seekdir __way,
                                 std::ios_base::openmode __wch = std::ios_base::in) {
            if (mEntry.size <= 0 || mEntry.address < 0) {
                return pos_type(off_type(-1));
            }
            pos_type curPos = FileBuffer::seekoff(__off, __way, __wch);
            if (curPos < mEntry.address || curPos >= mEntry.address + mEntry.size) {
                return pos_type(off_type(-1));
            }
            curPos -= mEntry.address;
            return curPos;
        }

        virtual pos_type seekpos(pos_type __sp, std::ios_base::openmode __wch = std::ios_base::in) {
            if (mEntry.size <= 0 || mEntry.address < 0) {
                return pos_type(off_type(-1));
            }
            if (__sp >= mEntry.size) {
                return pos_type(off_type(-1));
            }
            __sp += mEntry.address;
            pos_type curPos = FileBuffer::seekpos(__sp, __wch);
            curPos -= mEntry.address;
            return curPos;
        }

    protected:
        inline FileBuffer::pos_type getCurrentPosition() {
            char_type* curEgptr = egptr();
            char_type* curEback = eback();
            char_type* curGptr = gptr();
            pos_type result = FileBuffer::seekoff(0, std::ios_base::cur, std::ios_base::in);
            setg(curEback, curGptr, curEgptr);
            return result;
        }

        DioReader::DioEntry mEntry;
    };

    class DioFileInputStream : public std::istream {
    public:

        DioFileInputStream(const std::string& filePath, DioReader::DioEntry entry):
                std::istream(&__sb_), __sb_(filePath, entry)
        {
            if (__sb_.open(filePath, std::ifstream::binary | ios_base::in) == 0)
                this->setstate(ios_base::failbit);
            // seek
            if (!seekg(0)) {
                throw std::ios_base::failure("Error reading file: " + std::to_string(rdstate()));
            }
        }

        inline DioFileInputStream(DioFileInputStream&& __rhs):
                std::istream(std::move(__rhs)),
                __sb_(std::move(__rhs.__sb_))
        {
            this->set_rdbuf(&__sb_);
        }

        inline DioFileInputStream& operator=(DioFileInputStream&& __rhs)
        {
            std::istream::operator=(std::move(__rhs));
            __sb_ = std::move(__rhs.__sb_);
            return *this;
        }

        inline void close()
        {
            if (__sb_.close() == 0)
                this->setstate(ios_base::failbit);
        }

    protected:
        DioFileBuffer __sb_;
    };

    /*********************** helper ******************/

    bool isBigEndian() {
        int i = 1;
        char *a = (char*)&i;
        return *a != 1;
    }

    // 大小端转换
    void convertEndian(uint32_t& x)
    {
        unsigned char a,b,c,d;
        a = (char)(x & 0xffu);
        b = (char)((x & 0xff00u) >> 8u);
        c = (char)((x & 0xff0000u) >> 16u);
        d = (char)((x & 0xff000000u )>> 24u);
        x = (a << 24u) | (b << 16u ) | (c << 8u) | d;
    }

    // 将长度为 4 的 byte 转换为整型
    uint32_t byteArrayToInt32(byte* bytes) {
        static bool isBig = isBigEndian();
        if (isBig) {
            convertEndian(*(uint32_t *) bytes);
        }
        return *(uint32_t*)bytes;
    }

    // 更新dio文件的最近访问时间
    int updateFileAccessTime(const char* filePath) {
        struct timespec new_times[2];
        new_times[0].tv_nsec = UTIME_NOW; // 设置访问时间为 UTIME_NOW，即当前时间
        new_times[1].tv_nsec = UTIME_OMIT; // 设置最后修改时间为 UTIME_OMIT，不进行修改
        if (utimensat(AT_FDCWD, filePath, new_times, 0) < 0) {
            return -1;
        }
        return 0;
    }

    /*********************** static value ******************/

    static const uint32_t XOR_EIGENVALUE = 0xe128d2f9;
    static const uint32_t HEADER_LENGTH = 16;

    /*********************** class method ******************/

    DioReader::DioReader(const std::string& filePath) {
        this->mFilePath = filePath;
    }

    DioReader::Header DioReader::readHeader() const {
        // read
        std::ifstream file(this->mFilePath, std::ifstream::binary);
        if (!file.is_open()) {
            throw std::ios_base::failure("File " + mFilePath + " cannot be opened: " + std::to_string(file.rdstate()));
        }
        Header header = readHeader(file);
        if (header.magic != 0x9f2d821eu) {
            throw std::ios_base::failure("File " + mFilePath + " is not dio file");
        }
        return header;
    }

    void DioReader::readEntries() {
        if (mEntries != nullptr && mNumEntries >= 0) {
            return;
        }
        // read
        std::ifstream file(this->mFilePath, std::ifstream::binary);
        if (!file.is_open()) {
            throw std::ios_base::failure("File " + mFilePath + " cannot be opened: " + std::to_string(file.rdstate()));
        }
        Header header = readHeader(file);
        if (header.magic != 0x9f2d821eu) {
            throw std::ios_base::failure("File " + mFilePath + " is not dio file");
        }
        mNumEntries = header.fileCount;
        mEntries = readEntries(file, header);
    }

    DioReader::Header DioReader::readHeader(std::ifstream& file) {
        Header header;
        static_assert(
                sizeof(header) == HEADER_LENGTH,
                "header size must exactly match the input file format");

        readData(file, reinterpret_cast<byte *>(&header), HEADER_LENGTH);
        // 处理大小端问题
        if (isBigEndian()) {
            convertEndian(header.magic);
            convertEndian(header.fileCount);
            convertEndian(header.indexSize);
            convertEndian(header.version);
        }
        // 反混淆
        header.fileCount ^= XOR_EIGENVALUE;
        header.indexSize ^= XOR_EIGENVALUE;
        header.version ^= XOR_EIGENVALUE;
        return header;
    }

    bool DioReader::isEntriesSorted() {
        return mIsEntriesSorted;
    }

    void DioReader::sortEntries() {

    }

    DioReader::~DioReader() {

    }

    bool DioReader::isDioFile(const std::string &filePath) {
        DioReader dioReader(filePath);
        return isDioFile(dioReader);
    }

    bool DioReader::isDioFile(const DioReader &dioFile) {
        try {
            dioFile.readHeader();
            return true;
        } catch (...) {
            return false;
        }
    }

    int DioReader::indexOfDioEntryByPath(const std::string &childFilePath) {
        readEntries();
        if (mIsEntriesSorted) {
            // TODO dio: 二分查找
        } else {
            for (int i = 0; i < mNumEntries; i++) {
                DioEntry& entry = mEntries[i];
                if (entry.path == childFilePath) {
                    return i;
                }
            }
        }
        return -1;
    }

    std::unique_ptr<DioReader::DioEntry[]> DioReader::readEntries(std::ifstream &file, const DioReader::Header & header) {
        std::unique_ptr<byte[]> data(new byte[header.indexSize]);
        std::unique_ptr<DioReader::DioEntry[]> entries(new DioReader::DioEntry[header.fileCount]);
        uint32_t curPos = 0;
        uint32_t curAddress = HEADER_LENGTH + header.indexSize;
        readData(file, data.get(), header.indexSize);
        for (uint32_t i = 0; i < header.fileCount; i++) {
            uint32_t flagInt = byteArrayToInt32(data.get() + curPos) ^ XOR_EIGENVALUE;
            DioEntryFlag flag = buildDioEntryFlagFromUInt32(flagInt);
            curPos += 4;
            uint32_t fileSize = byteArrayToInt32(data.get() + curPos) ^ XOR_EIGENVALUE;
            curPos += 4;
            uint32_t pathLength = byteArrayToInt32(data.get() + curPos) ^ XOR_EIGENVALUE;
            curPos += 4;
            if (flag.guardPath) {
                xorByteArray(data.get() + curPos, pathLength, FILE_DATA_XOR_EIGENVALUE);
            }
            std::string filePath((char*)data.get() + curPos, pathLength);
            curPos += pathLength;
            curAddress += 1; // 跳过 FILE_SEP 字段

            DioEntry dioEntry;
            dioEntry.flag = flag;
            dioEntry.path = filePath;
            dioEntry.size = fileSize;
            dioEntry.address = curAddress;
            entries[i] = dioEntry;
            curAddress += fileSize;
        }
        return entries;
    }

    void DioReader::readData(std::ifstream &file, byte *buffer, const long bytes, std::fpos<mbstate_t> position) {
        if (!file.seekg(position)) {
            throw std::ios_base::failure("Error reading file: " + std::to_string(file.rdstate()));
        }
        readData(file, buffer, bytes);
    }

    void DioReader::readData(std::ifstream &file, byte *buffer, const long bytes) {
        if (!file.read((char*)buffer, bytes)) {
            if (file.rdstate() & std::ios::eofbit) {
                throw std::ios_base::failure("Unexpected end of file");
            }
            throw std::ios_base::failure("Error reading file: " + std::to_string(file.rdstate()));
        }
    }

    DioReader::DioEntryFlag DioReader::buildDioEntryFlagFromUInt32(uint32_t data) {
        return DioEntryFlag{
            (data & 0x1u) != 0,
            (data & 0x2u) != 0
        };
    }

    std::unique_ptr<std::istream> DioReader::getChildFileStream(const std::string &childFilePath) {
        int i = indexOfDioEntryByPath(childFilePath);
        if (i < 0) {
            throw std::ios_base::failure("Can't find the entry: " + childFilePath);
        }
        return getChildFileStream(mEntries[i]);
    }

    std::unique_ptr<std::istream> DioReader::getChildFileStream(DioReader::DioEntry entry) {
        updateFileAccessTime(mFilePath.c_str());
        return std::make_unique<DioFileInputStream>(mFilePath, entry);
    }

    DioReader::DioFileData DioReader::getChildFileData(const DioReader::DioEntry &entry) {
        std::ifstream file(this->mFilePath, std::ifstream::binary);
        if (!file.is_open()) {
            throw std::ios_base::failure("File " + mFilePath + " cannot be opened: " + std::to_string(file.rdstate()));
        }
        updateFileAccessTime(mFilePath.c_str());
        std::unique_ptr<byte[]> data(new byte[entry.size]);
        readData(file, data.get(), entry.size, entry.address);
        if (entry.flag.guardData) {
            xorByteArray(data.get(), entry.size, FILE_DATA_XOR_EIGENVALUE);
        }
        return DioFileData{entry.size, std::move(data)};
    }

    DioReader::DioFileData DioReader::getChildFileData(const std::string &childFilePath) {
        int i = indexOfDioEntryByPath(childFilePath);
        if (i < 0) {
            throw std::ios_base::failure("Can't find the entry: " + childFilePath);
        }
        return getChildFileData(mEntries[i]);
    }

    DioReader::DioEntry DioReader::findDioEntryByPath(const std::string &childFilePath) {
        int i = indexOfDioEntryByPath(childFilePath);
        if (i < 0) {
            throw std::ios_base::failure("Can't find the entry: " + childFilePath);
        }
        return mEntries[i];
    }
}