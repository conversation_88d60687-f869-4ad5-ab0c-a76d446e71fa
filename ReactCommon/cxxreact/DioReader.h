#pragma once

#include <string>

// [MRN60: chendacai] DIO 适配，详见：https://km.sankuai.com/page/349814196
namespace dio {
    typedef unsigned char byte;

    class DioReader {
    public:

        struct DioEntryFlag {
            bool guardPath = false;
            bool guardData = false;
        };

        struct DioEntry {
            DioEntryFlag flag;
            std::string path;
            uint32_t size = 0;
            uint32_t address = 0;
        };

        struct DioFileData {
            size_t size = 0;
            std::unique_ptr<byte[]> data;
        };

        static bool isDioFile(const std::string& filePath);

        static bool isDioFile(const DioReader& dioFile);

        explicit DioReader(const std::string& filePath);

        ~DioReader();

        bool isEntriesSorted();

        void readEntries();

        std::unique_ptr<std::istream> getChildFileStream(const std::string& childFilePath);

        std::unique_ptr<std::istream> getChildFileStream(DioEntry entry);

        DioFileData getChildFileData(const std::string& childFilePath);

        DioReader::DioFileData getChildFileData(const DioEntry& entry);

        void sortEntries();

        int indexOfDioEntryByPath(const std::string& childFilePath);

        DioReader::DioEntry findDioEntryByPath(const std::string& childFilePath);

    protected:

        struct Header {
            uint32_t magic = 0;
            uint32_t version = 0;
            uint32_t indexSize = 0;
            uint32_t fileCount = 0;
        };

        Header readHeader() const;

        static Header readHeader(std::ifstream& file);
        static std::unique_ptr<DioEntry[]> readEntries(std::ifstream& file, const Header& header);

        std::string mFilePath;
        std::unique_ptr<DioEntry[]> mEntries;
        size_t mNumEntries = 0;
        bool mIsEntriesSorted = false;

        static DioEntryFlag buildDioEntryFlagFromUInt32(uint32_t data);

        static void readData(std::ifstream& file, byte *buffer, long bytes);

        static void readData(std::ifstream& file, byte *buffer, long bytes, std::fpos<mbstate_t> position);
    };

}
