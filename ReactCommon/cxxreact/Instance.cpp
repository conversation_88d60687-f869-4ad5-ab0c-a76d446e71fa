/*
 * Copyright (c) Facebook, Inc. and its affiliates.
 *
 * This source code is licensed under the MIT license found in the
 * LICENSE file in the root directory of this source tree.
 */

#include "Instance.h"

#include "JSBigString.h"
#include "JSExecutor.h"
#include "MessageQueueThread.h"
#include "NativeToJsBridge.h"
#include "SystraceSection.h"

#include <folly/MoveWrapper.h>
#include <folly/json.h>

#include <glog/logging.h>

#include <condition_variable>
#include <exception>
#include <fstream>
#include <memory>
#include <mutex>
#include <string>
#include "DioReader.h"

namespace facebook {
namespace react {

Instance::~Instance() {
  if (nativeToJsBridge_) {
    nativeToJsBridge_->destroy();
  }
}

void Instance::initializeBridge(
    std::unique_ptr<InstanceCallback> callback,
    std::shared_ptr<JSExecutorFactory> jsef,
    std::shared_ptr<MessageQueueThread> jsQueue,
    std::string name,
    bool useQuickJS) {
  callback_ = std::move(callback);
  jsQueue->runOnQueueSync([this, &jsef, jsQueue, name, useQuickJS]() mutable {
    nativeToJsBridge_ = std::make_shared<NativeToJsBridge>(
        jsef.get(), jsQueue, callback_, name, useQuickJS);

    nativeToJsBridge_->initializeRuntime();

    /**
     * After NativeToJsBridge is created, the msc::jsi::Runtime should exist.
     * Also, the JS message queue thread exists. So, it's safe to
     * schedule all queued up js Calls.
     */

    std::lock_guard<std::mutex> lock(m_syncMutex);
    m_syncReady = true;
    m_syncCV.notify_all();
  });

  CHECK(nativeToJsBridge_);
}

void Instance::loadBundle(
    std::unique_ptr<const JSBigString> string,
    std::string sourceURL) {
  callback_->incrementPendingJSCalls();
  SystraceSection s("Instance::loadBundle", "sourceURL", sourceURL);
  nativeToJsBridge_->loadBundle( std::move(string), std::move(sourceURL));
}

void Instance::loadBundleSync(
    std::unique_ptr<const JSBigString> string,
    std::string sourceURL) {
  std::unique_lock<std::mutex> lock(m_syncMutex);
  m_syncCV.wait(lock, [this] { return m_syncReady; });

  SystraceSection s("Instance::loadBundleSync", "sourceURL", sourceURL);
  nativeToJsBridge_->loadBundleSync(
      std::move(string), std::move(sourceURL));
}

void Instance::setSourceURL(std::string sourceURL) {
  callback_->incrementPendingJSCalls();
  SystraceSection s("Instance::setSourceURL", "sourceURL", sourceURL);

  nativeToJsBridge_->loadBundle(nullptr, std::move(sourceURL));
}

void Instance::loadScriptFromString(
    std::unique_ptr<const JSBigString> string,
    std::string sourceURL,
    bool loadSynchronously) {
  SystraceSection s("Instance::loadScriptFromString", "sourceURL", sourceURL);
  if (loadSynchronously) {
    loadBundleSync(std::move(string), std::move(sourceURL));
  } else {
    loadBundle(std::move(string), std::move(sourceURL));
  }
}

// [MRN60: chendacai] CodeCache，详见：https://ones.sankuai.com/ones/product/8432/workItem/task/detail/6222592
void Instance::loadBundleWithCodeCache(
                               std::unique_ptr<const JSBigString> string,
                               std::string sourceURL,
                               std::string jsCodeCachePath,
                              msc::jsi::LoadJSCodeCacheCallback loadJSCodeCacheCallback) {
  callback_->incrementPendingJSCalls();
  SystraceSection s("Instance::loadBundleWithCodeCache", "sourceURL", sourceURL);
  nativeToJsBridge_->loadBundleWithCodeCache(std::move(string),
                                     std::move(sourceURL), std::move(jsCodeCachePath), loadJSCodeCacheCallback);
}

// [MRN60: chendacai] CodeCache，详见：https://ones.sankuai.com/ones/product/8432/workItem/task/detail/6222592
void Instance::loadBundleWithCodeCacheSync(
                                   std::unique_ptr<const JSBigString> string,
                                   std::string sourceURL,
                                   std::string jsCodeCachePath,
                                  msc::jsi::LoadJSCodeCacheCallback loadJSCodeCacheCallback) {
  std::unique_lock<std::mutex> lock(m_syncMutex);
  m_syncCV.wait(lock, [this] { return m_syncReady; });

  SystraceSection s("Instance::loadBundleWithCodeCacheSync", "sourceURL",
                    sourceURL);
  nativeToJsBridge_->loadBundleWithCodeCacheSync(std::move(string),
                                         std::move(sourceURL), std::move(jsCodeCachePath), loadJSCodeCacheCallback);
}

// [MRN60: chendacai] CodeCache，详见：https://ones.sankuai.com/ones/product/8432/workItem/task/detail/6222592
void Instance::loadScriptFromStringWithCodeCache(std::unique_ptr<const JSBigString> string,
                                    std::string sourceURL,
                                    std::string jsCodeCachePath,
                                   msc::jsi::LoadJSCodeCacheCallback loadJSCodeCacheCallback,
                                    bool loadSynchronously) {
  SystraceSection s("Instance::loadScriptFromStringWithCodeCache", "sourceURL", sourceURL);
  if (loadSynchronously) {
    loadBundleWithCodeCacheSync(std::move(string), std::move(sourceURL), std::move(jsCodeCachePath), loadJSCodeCacheCallback);
  } else {
    loadBundleWithCodeCache(std::move(string), std::move(sourceURL), std::move(jsCodeCachePath), loadJSCodeCacheCallback);
  }
}

void Instance::setGlobalVariable(
    std::string propName,
    std::unique_ptr<const JSBigString> jsonValue) {
  nativeToJsBridge_->setGlobalVariable(
      std::move(propName), std::move(jsonValue));
}

// [MRN63: leipengchao] 无引擎渲染表达式支持
void Instance::setGlobalVariableSync(
    std::string propName,
    std::unique_ptr<const JSBigString> jsonValue) {
  nativeToJsBridge_->setGlobalVariableSync(
      std::move(propName), std::move(jsonValue));
}

void *Instance::getJavaScriptContext() {
  return nativeToJsBridge_ ? nativeToJsBridge_->getJavaScriptContext()
                           : nullptr;
}

bool Instance::isInspectable() {
  return nativeToJsBridge_ ? nativeToJsBridge_->isInspectable() : false;
}

bool Instance::isBatchActive() {
  return nativeToJsBridge_ ? nativeToJsBridge_->isBatchActive() : false;
}

void Instance::callJSFunction(
    std::string &&module,
    std::string &&method,
    std::string &&params) {
    // LOG(ERROR) << "[MSC_LOG]Instance.cpp callJSFunction:"
    //               << module.c_str() << "." << method.c_str();
  callback_->incrementPendingJSCalls();
  nativeToJsBridge_->callFunction(
      std::move(module), std::move(method), std::move(params));
}

void Instance::callJSCallback(uint64_t callbackId, std::string &&params) {
  SystraceSection s("Instance::callJSCallback");
  callback_->incrementPendingJSCalls();
  nativeToJsBridge_->invokeCallback((double)callbackId, std::move(params));
}

void Instance::callJSCallbackWithDynamic(uint64_t callbackId, folly::dynamic &&params) {
  SystraceSection s("Instance::callJSCallback");
  callback_->incrementPendingJSCalls();
  nativeToJsBridge_->invokeCallbackWithDynamic((double)callbackId, std::move(params));
}

void Instance::handleMemoryPressure(int pressureLevel) {
  nativeToJsBridge_->handleMemoryPressure(pressureLevel);
}


  /**
   * Why is is necessary to queue up async work?
   *
   * 1. TurboModuleManager must be created synchronously after the Instance,
   *    before we load the source code. This is when the NativeModule system
   *    is initialized. RCTDevLoadingView shows bundle download progress.
   * 2. TurboModuleManager requires a JS CallInvoker.
   * 3. The JS CallInvoker requires the NativeToJsBridge, which is created on
   *    the JS thread in Instance::initializeBridge.
   *
   * Therefore, although we don't call invokeAsync before the JS bundle is
   * executed, this buffering is implemented anyways to ensure that work
   * isn't discarded.
   */

} // namespace react
} // namespace facebook
