/*
 * Copyright (c) Facebook, Inc. and its affiliates.
 *
 * This source code is licensed under the MIT license found in the
 * LICENSE file in the root directory of this source tree.
 */

#pragma once

#include <condition_variable>
#include <list>
#include <memory>
#include <mutex>
#include <msc-jsi/jsi.h>

#include <cxxreact/NativeToJsBridge.h>

#ifndef RN_EXPORT
#define RN_EXPORT __attribute__((visibility("default")))
#endif

namespace folly {
struct dynamic;
}

namespace facebook {
namespace react {

class JSBigString;
class JSExecutorFactory;
class MessageQueueThread;

struct ModuleConfig {
  std::string name;
  folly::dynamic config;
};

struct InstanceCallback {
  virtual ~InstanceCallback() {}
  virtual void onBatchComplete() {}
  virtual void incrementPendingJSCalls() {}
  virtual void decrementPendingJSCalls() {}
  virtual void callNativeModules(std::string &&queue) {}
  virtual folly::dynamic callSerializableNativeHook(std::string &&moduleName, std::string &&methodName, std::string &&params) {
    return folly::dynamic::object();
  }
  virtual folly::Optional<ModuleConfig> getConfig(const std::string &name){
    return folly::none;
  }
};

class RN_EXPORT Instance {
 public:
  ~Instance();
  void initializeBridge(
      std::unique_ptr<InstanceCallback> callback,
      std::shared_ptr<JSExecutorFactory> jsef,
      std::shared_ptr<MessageQueueThread> jsQueue,
      std::string name,
      bool useQuickJS);

  void initializeRuntime();

  long getJSRuntimePtr(){
      return jsRuntimePtr_;
  }

  void setSourceURL(std::string sourceURL);

  void loadScriptFromString(
      std::unique_ptr<const JSBigString> string,
      std::string sourceURL,
      bool loadSynchronously);


  // [MRN60: chendacai] CodeCache，详见：https://ones.sankuai.com/ones/product/8432/workItem/task/detail/6222592
  void loadBundleWithCodeCache(
                    std::unique_ptr<const JSBigString> string,
                    std::string sourceURL,
                    std::string jsCodeCachePath,
                   msc::jsi::LoadJSCodeCacheCallback loadJSCodeCacheCallback);
  // [MRN63: leipengchao] 无引擎渲染表达式支持
  void setGlobalVariableSync(
      std::string propName,
      std::unique_ptr<const JSBigString> jsonValue);
  // [MRN60: chendacai] CodeCache，详见：https://ones.sankuai.com/ones/product/8432/workItem/task/detail/6222592
  void loadBundleWithCodeCacheSync(
                                   std::unique_ptr<const JSBigString> string,
                                   std::string sourceURL,
                                   std::string jsCodeCachePath,
                                  msc::jsi::LoadJSCodeCacheCallback loadJSCodeCacheCallback);
  // [MRN60: chendacai] CodeCache，详见：https://ones.sankuai.com/ones/product/8432/workItem/task/detail/6222592
  void loadScriptFromStringWithCodeCache(std::unique_ptr<const JSBigString> string,
                    std::string sourceURL, std::string jsCodeCachePath,
                   msc::jsi::LoadJSCodeCacheCallback loadJSCodeCacheCallback,
                    bool loadSynchronously);
  bool supportsProfiling();
  void setGlobalVariable(
      std::string propName,
      std::unique_ptr<const JSBigString> jsonValue);
  void *getJavaScriptContext();
  bool isInspectable();
  bool isBatchActive();
  void callJSFunction(
      std::string &&module,
      std::string &&method,
      std::string &&params);
  void callJSCallback(uint64_t callbackId, std::string &&params);
  void callJSCallbackWithDynamic(uint64_t callbackId, folly::dynamic &&params);

  const InstanceCallback &getCallback() const;
  InstanceCallback &getCallback();

  void handleMemoryPressure(int pressureLevel);

  /**
   * JS CallInvoker is used by TurboModules to schedule work on the JS thread.
   *
   * Why is the bridge creating JS CallInvoker?
   *
   * - After every Native -> JS call in the TurboModule system, the bridge
   *   needs to flush all queued NativeModule method calls. The bridge must
   *   also dispatch onBatchComplete if the queue of NativeModule method calls
   *   was not empty.
   */

  /**
   * Native CallInvoker is used by TurboModules to schedule work on the
   * NativeModule thread(s).
   *
   * Why is the bridge decorating native CallInvoker?
   *
   * - The bridge must be informed of all TurboModule async method calls. Why?
   *   When all queued NativeModule method calls are flushed by a call from
   *   Native -> JS, if that queue was non-zero in size, JsToNativeBridge
   *   dispatches onBatchComplete. When we turn our NativeModules to
   *   TurboModuels, there will be less and less pending NativeModule method
   *   calls, so onBatchComplete will not fire as often. Therefore, the bridge
   *   needs to know how many TurboModule async method calls have been completed
   *   since the last time the bridge was flushed. If this number is non-zero,
   *   we fire onBatchComplete.
   *
   * Why can't we just create and return a new native CallInvoker?
   *
   * - On Android, we have one NativeModule thread. That thread is created and
   *   managed outisde of NativeToJsBridge. On iOS, we have one MethodQueue per
   *   module. Those MethodQueues are also created and managed outside of
   *   NativeToJsBridge. Therefore, we need to pass in a CallInvoker that
   *   schedules work on the respective thread.
   */

 private:
  void callNativeModules(std::string &&calls, bool isEndOfBatch);
  void loadBundle(
      std::unique_ptr<const JSBigString> startupScript,
      std::string startupScriptSourceURL);
  void loadBundleSync(
      std::unique_ptr<const JSBigString> startupScript,
      std::string startupScriptSourceURL);

  std::shared_ptr<InstanceCallback> callback_;
  std::shared_ptr<NativeToJsBridge> nativeToJsBridge_;

  std::mutex m_syncMutex;
  std::condition_variable m_syncCV;
  long jsRuntimePtr_ = 0;
  bool m_syncReady = false;
};

} // namespace react
} // namespace facebook
