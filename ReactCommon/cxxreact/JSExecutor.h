/*
 * Copyright (c) Facebook, Inc. and its affiliates.
 *
 * This source code is licensed under the MIT license found in the
 * LICENSE file in the root directory of this source tree.
 */

#pragma once

#include <memory>
#include <string>

#include <folly/dynamic.h>
#include <msc-jsi/jsi.h>

#ifndef RN_EXPORT
#define RN_EXPORT __attribute__((visibility("default")))
#endif

namespace facebook {
namespace react {

class JSBigString;
class JSExecutor;
class MessageQueueThread;
struct InstanceCallback;

// This interface describes the delegate interface required by
// Executor implementations to call from JS into native code.
class ExecutorDelegate {
 public:
  virtual ~ExecutorDelegate() {}

  virtual std::shared_ptr<InstanceCallback> getCallback() = 0;

  virtual void callNativeModules(
      JSExecutor &executor,
      std::string &&calls,
      bool isEndOfBatch) = 0;
  virtual folly::Optional<folly::dynamic> callSerializableNativeHook(
      JSExecutor &executor,
      std::string &&module,
      std::string &&method,
      std::string &&args) = 0;
};

using NativeExtensionsProvider =
    std::function<folly::dynamic(const std::string &)>;

class JSExecutorFactory {
 public:
  virtual std::unique_ptr<JSExecutor> createJSExecutor(
      std::shared_ptr<ExecutorDelegate> delegate,
      std::shared_ptr<MessageQueueThread> jsQueue,
      std::function<void (std::function<void ()>)> callbackFunc = nullptr,
      std::string name = nullptr, bool useQuickJS = false) = 0;
  virtual ~JSExecutorFactory() {}
};

class RN_EXPORT JSExecutor {
 public:
  JSExecutor(std::shared_ptr<msc::jsi::Runtime> runtime): runtime_(runtime) {}
  /**
   * Prepares the JS runtime for React Native by installing global variables.
   * Called once before any JS is evaluated.
   */
  virtual void initializeRuntime() = 0;

  long getJSRuntimePtr(){
      return reinterpret_cast<long>(runtime_.get());
  }

  /**
   * Execute an application script bundle in the JS context.
   */
  virtual void loadBundle(
      std::unique_ptr<const JSBigString> script,
      std::string sourceURL) = 0;

  // [MRN60: chendacai] CodeCache，详见：https://ones.sankuai.com/ones/product/8432/workItem/task/detail/6222592
  /**
   * Execute an application script bundle in the JS context, if there is code cache, priority to execute it
   */
  virtual void loadBundleWithCodeCache(std::unique_ptr<const JSBigString> script,
                                     std::string sourceURL, std::string jsCodeCachePath,
                                    msc::jsi::LoadJSCodeCacheCallback loadJSCodeCacheCallback) {
    // this -> loadBundle(std::move(script), sourceURL); // TODO chdc 默认实现，回调不支持
  }


  /**
   * Executes BatchedBridge.callFunctionReturnFlushedQueue with the module ID,
   * method ID and optional additional arguments in JS. The executor is
   * responsible for using Bridge->callNativeModules to invoke any necessary
   * native modules methods.
   */
  virtual void callFunction(
      const std::string &moduleId,
      const std::string &methodId,
      const std::string &arguments) = 0;

  /**
   * Executes BatchedBridge.invokeCallbackAndReturnFlushedQueue with the cbID,
   * and optional additional arguments in JS and returns the next queue. The
   * executor is responsible for using Bridge->callNativeModules to invoke any
   * necessary native modules methods.
   */
  virtual void invokeCallback(
      const double callbackId,
      const std::string &&arguments) = 0;

  virtual void invokeCallbackWithDynamic(
      const double callbackId,
      const folly::dynamic &arguments) = 0;

  virtual void setGlobalVariable(
      std::string propName,
      std::unique_ptr<const JSBigString> jsonValue) = 0;

  virtual void *getJavaScriptContext() {
    return nullptr;
  }

  /**
   * Returns whether or not the underlying executor supports debugging via the
   * Chrome remote debugging protocol.
   */
  virtual bool isInspectable() {
    return false;
  }

  /**
   * The description is displayed in the dev menu, if there is one in
   * this build.  There is a default, but if this method returns a
   * non-empty string, it will be used instead.
   */
  virtual std::string getDescription() = 0;

  virtual void handleMemoryPressure(__unused int pressureLevel) {}

  virtual void destroy() {}
  virtual ~JSExecutor() {}

  virtual void flush() {}
  protected:
    std::shared_ptr<msc::jsi::Runtime> runtime_;
};

} // namespace react
} // namespace facebook
