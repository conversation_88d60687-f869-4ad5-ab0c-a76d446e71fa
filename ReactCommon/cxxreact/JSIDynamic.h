/*
 * Copyright (c) Facebook, Inc. and its affiliates.
 *
 * This source code is licensed under the MIT license found in the
 * LICENSE file in the root directory of this source tree.
 */

#pragma once

#include <folly/dynamic.h>
#include <msc-jsi/jsi.h>

namespace msc {
    namespace jsi {

        Value valueFromDynamic(
                Runtime& runtime,
                const folly::dynamic& dyn);

        folly::dynamic dynamicFromValue(
                Runtime& runtime,
                const Value& value);

    } // namespace jsi
} // namespace cip
