/*
 * Copyright (c) Facebook, Inc. and its affiliates.
 *
 * This source code is licensed under the MIT license found in the
 * LICENSE file in the root directory of this source tree.
 */

#include "NativeToJsBridge.h"

#include <folly/MoveWrapper.h>
#include <folly/json.h>
#include <glog/logging.h>

#include "Instance.h"
#include "JSBigString.h"
#include "MessageQueueThread.h"
#include "SystraceSection.h"

#include <memory>

#ifdef WITH_FBSYSTRACE
#include <fbsystrace.h>
using fbsystrace::FbSystraceAsyncFlow;
#endif

namespace facebook {
namespace react {

// This class manages calls from JS to native code.
class JsToNativeBridge : public react::ExecutorDelegate {
 public:
  JsToNativeBridge(
      std::shared_ptr<InstanceCallback> callback)
      : m_callback(callback) {}

  std::shared_ptr<InstanceCallback> getCallback() override {
      return m_callback;
  }

  bool isBatchActive() {
    return m_batchHadNativeModuleOrTurboModuleCalls;
  }

  void callNativeModules(
      __unused JSExecutor &executor,
      std::string &&calls,
      bool isEndOfBatch) override {
      // LOG(ERROR) << "[MSC_LOG]NativeToJsBridge.cpp callNativeModules:" << calls.c_str();
    CHECK(m_callback || calls.empty())
        << "native module calls cannot be completed with no native modules";
    m_batchHadNativeModuleOrTurboModuleCalls =
        m_batchHadNativeModuleOrTurboModuleCalls || !calls.empty();

    // An exception anywhere in here stops processing of the batch.  This
    // was the behavior of the Android bridge, and since exception handling
    // terminates the whole bridge, there's not much point in continuing.
    //for (auto &call : parseMethodCalls(std::move(calls))) {
    //  m_registry->callNativeMethod(
    //      std::move(call.module), std::move(call.method), std::move(call.arguments), call.callId);
    //}
    m_callback->callNativeModules(std::move(calls)); //调native侧的处理队列的方法
    if (isEndOfBatch) {
        // LOG(ERROR) << "[MSC_LOG]NativeToJsBridge.cpp callNativeModules isEndOfBatch true";
      // onBatchComplete will be called on the native (module) queue, but
      // decrementPendingJSCalls will be called sync. Be aware that the bridge
      // may still be processing native calls when the bridge idle signaler
      // fires.
      if (m_batchHadNativeModuleOrTurboModuleCalls) {
        m_callback->onBatchComplete();
        m_batchHadNativeModuleOrTurboModuleCalls = false;
      }
      m_callback->decrementPendingJSCalls();
    }
  }

  folly::Optional<folly::dynamic> callSerializableNativeHook(
      __unused JSExecutor &executor,
      std::string &&module,
      std::string &&method,
      std::string &&args) override {
    folly::dynamic ret = m_callback->callSerializableNativeHook(std::move(module), std::move(method), std::move(args)); //交给native处理
    if(!ret.isArray()){
        throw std::invalid_argument(
                folly::to<std::string>("callSerializableNativeHook result should be array"));
    }
    if(ret.size()==1){
        return ret[0];
    } else {
        return folly::none;
    }
    //return ret[0];
    //return m_registry->callSerializableNativeHook(
    //    std::move(module), std::move(method), std::move(args));
  }

  void recordTurboModuleAsyncMethodCall() {
    m_batchHadNativeModuleOrTurboModuleCalls = true;
  }

 private:
  // These methods are always invoked from an Executor.  The NativeToJsBridge
  // keeps a reference to the executor, and when destroy() is called, the
  // executor is destroyed synchronously on its queue.
  std::shared_ptr<InstanceCallback> m_callback;
  bool m_batchHadNativeModuleOrTurboModuleCalls = false;
};

// 将inspect线程将要传入的函数指针传入 在runOnExecutorQueue内部执行。
void NativeToJsBridge::run (std::function<void ()> callback) {
     runOnExecutorQueue([=](JSExecutor *executor) {
       // LOG(ERROR) << "NativeToJsBridge callback.";
       callback();
     });
}

NativeToJsBridge::NativeToJsBridge(
    JSExecutorFactory *jsExecutorFactory,
    std::shared_ptr<MessageQueueThread> jsQueue,
    std::shared_ptr<InstanceCallback> callback,
    std::string name, bool useQuickJS)
    : m_destroyed(std::make_shared<bool>(false)),
      m_delegate(std::make_shared<JsToNativeBridge>(callback)),
      m_executor(jsExecutorFactory->createJSExecutor(m_delegate, jsQueue, std::bind(&NativeToJsBridge::run, this, std::placeholders::_1), name, useQuickJS)),
      m_executorMessageQueueThread(std::move(jsQueue)),
      m_inspectable(m_executor->isInspectable()) {
       //LOG(ERROR) << "NativeToJsBridge init";
}




// This must be called on the same thread on which the constructor was called.
NativeToJsBridge::~NativeToJsBridge() {
  CHECK(*m_destroyed)
      << "NativeToJsBridge::destroy() must be called before deallocating the NativeToJsBridge!";
}

void NativeToJsBridge::initializeRuntime() {
  runOnExecutorQueue(
      [](JSExecutor *executor) mutable { executor->initializeRuntime(); });
}

void NativeToJsBridge::loadBundle(
    std::unique_ptr<const JSBigString> startupScript,
    std::string startupScriptSourceURL) {
  runOnExecutorQueue(
      [this,
       startupScript = folly::makeMoveWrapper(std::move(startupScript)),
       startupScriptSourceURL =
           std::move(startupScriptSourceURL)](JSExecutor *executor) mutable {
        try {
          executor->loadBundle(
              std::move(*startupScript), std::move(startupScriptSourceURL));
        } catch (...) {
          m_applicationScriptHasFailure = true;
          throw;
        }
      });
}

void NativeToJsBridge::loadBundleSync(
    std::unique_ptr<const JSBigString> startupScript,
    std::string startupScriptSourceURL) {
  try {
    m_executor->loadBundle(
        std::move(startupScript), std::move(startupScriptSourceURL));
  } catch (...) {
    m_applicationScriptHasFailure = true;
    throw;
  }
}

// [MRN63: leipengchao] 无引擎渲染表达式，支持同步设置全局变量
void NativeToJsBridge::setGlobalVariableSync(
    std::string propName,
    std::unique_ptr<const JSBigString> jsonValue) {
  // runOnExecutorQueue([propName = std::move(propName),
  //                     jsonValue = folly::makeMoveWrapper(std::move(jsonValue))](
  //                        JSExecutor *executor) mutable {
  //   executor->setGlobalVariable(propName, jsonValue.move());
  // });
  //不走异步队列，直接同步设置global variable
  m_executor->setGlobalVariable(propName, std::move(jsonValue));
}

// [MRN60: chendacai] CodeCache，详见：https://ones.sankuai.com/ones/product/8432/workItem/task/detail/6222592
void NativeToJsBridge::loadBundleWithCodeCache(
    std::unique_ptr<const JSBigString> startupScript,
    std::string startupScriptSourceURL,
    std::string jsCodeCachePath,
   msc::jsi::LoadJSCodeCacheCallback loadJSCodeCacheCallback) {
  runOnExecutorQueue(
      [this,
       startupScript = folly::makeMoveWrapper(std::move(startupScript)),
       startupScriptSourceURL = std::move(startupScriptSourceURL),
       jsCodeCachePath = std::move(jsCodeCachePath),
       loadJSCodeCacheCallback = loadJSCodeCacheCallback] (JSExecutor *executor) mutable {
        try {
          executor->loadBundleWithCodeCache(
              std::move(*startupScript), std::move(startupScriptSourceURL),
              std::move(jsCodeCachePath), loadJSCodeCacheCallback);
        } catch (...) {
          m_applicationScriptHasFailure = true;
          throw;
        }
      });
}

// [MRN60: chendacai] CodeCache，详见：https://ones.sankuai.com/ones/product/8432/workItem/task/detail/6222592
void NativeToJsBridge::loadBundleWithCodeCacheSync(
    std::unique_ptr<const JSBigString> startupScript,
    std::string startupScriptSourceURL,
    std::string jsCodeCachePath,
   msc::jsi::LoadJSCodeCacheCallback loadJSCodeCacheCallback) {
  try {
    m_executor->loadBundleWithCodeCache(
      std::move(startupScript), std::move(startupScriptSourceURL),
      std::move(jsCodeCachePath), loadJSCodeCacheCallback);
  } catch (...) {
    m_applicationScriptHasFailure = true;
    throw;
  }
}
// 在cpp文件中初始化静态成员
// std::mutex facebook::react::NativeToJsBridge::s_queuedTasksMutex;
//std::vector<std::pair<std::string, std::string>> facebook::react::NativeToJsBridge::s_queuedTasks;
//std::vector<std::pair<std::string, std::string>> NativeToJsBridge::getQueuedTasks() {
//    std::lock_guard<std::mutex> lock(s_queuedTasksMutex);
//    return s_queuedTasks;
//}

void NativeToJsBridge::callFunction(
    std::string &&module,
    std::string &&method,
    std::string &&arguments) {
    // LOG(ERROR) << "[MSC_LOG]NativeToJsBridge callFunction:"
    //       << module.c_str() << "." << method.c_str();
// 记录任务到静态容器
    // {
    //        std::lock_guard<std::mutex> lock(facebook::react::NativeToJsBridge::s_queuedTasksMutex);
    //        facebook::react::NativeToJsBridge::s_queuedTasks.push_back({module, method});
    //        LOG(ERROR) << "[MSC_LOG]Adding to queue: " << module.c_str() << "." << method.c_str()
    //                   << " (queue size: " << facebook::react::NativeToJsBridge::s_queuedTasks.size() << ")";
    //    }
  int systraceCookie = -1;
#ifdef WITH_FBSYSTRACE
  systraceCookie = m_systraceCookie++;
  FbSystraceAsyncFlow::begin(
      TRACE_TAG_REACT_CXX_BRIDGE, "JSCall", systraceCookie);
#endif
  runOnExecutorQueue([this,
                      module = std::move(module),
                      method = std::move(method),
                      arguments = std::move(arguments),
                      systraceCookie](JSExecutor *executor) {
      // LOG(ERROR) << "[MSC_LOG]runOnExecutorQueue callFunction:"
      //       << module.c_str() << "." << method.c_str();
      // 任务执行时从队列中移除
      // {
      //          std::lock_guard<std::mutex> lock(facebook::react::NativeToJsBridge::s_queuedTasksMutex);
      //          // 查找并移除对应的任务
      //          auto it = std::find_if(facebook::react::NativeToJsBridge::s_queuedTasks.begin(), facebook::react::NativeToJsBridge::s_queuedTasks.end(),
      //                                 [&module, &method](const auto& pair) {
      //                                     return pair.first == module && pair.second == method;
      //                                 });
      //          if (it != facebook::react::NativeToJsBridge::s_queuedTasks.end()) {
      //              s_queuedTasks.erase(it);
      //              LOG(ERROR) << "[MSC_LOG]Removing from queue: " << module.c_str() << "." << method.c_str()
      //                         << " (queue size: " << facebook::react::NativeToJsBridge::s_queuedTasks.size() << ")";
      //          }
      //      }
    if (m_applicationScriptHasFailure) {
      LOG(ERROR)
          << "Attempting to call JS function on a bad application bundle: "
          << module.c_str() << "." << method.c_str() << "()";
      throw std::runtime_error(
          "Attempting to call JS function on a bad application bundle: " +
          module + "." + method + "()");
    }

#ifdef WITH_FBSYSTRACE
    FbSystraceAsyncFlow::end(
        TRACE_TAG_REACT_CXX_BRIDGE, "JSCall", systraceCookie);
    SystraceSection s(
        "NativeToJsBridge::callFunction", "module", module, "method", method);
#else
    (void)(systraceCookie);
#endif
    // This is safe because we are running on the executor's thread: it won't
    // destruct until after it's been unregistered (which we check above) and
    // that will happen on this thread
    // LOG(ERROR) << "[MSC_LOG]executor->callFunction:"
      //       << module.c_str() << "." << method.c_str();
    executor->callFunction(module, method, arguments);
  });
}

void NativeToJsBridge::invokeCallbackWithDynamic(
    double callbackId,
    folly::dynamic &&arguments) {
  int systraceCookie = -1;
#ifdef WITH_FBSYSTRACE
  systraceCookie = m_systraceCookie++;
  FbSystraceAsyncFlow::begin(
      TRACE_TAG_REACT_CXX_BRIDGE, "<callback>", systraceCookie);
#endif

  runOnExecutorQueue(
      [this, callbackId, arguments = std::move(arguments), systraceCookie](
          JSExecutor *executor) {
        if (m_applicationScriptHasFailure) {
          LOG(ERROR)
              << "Attempting to call JS callback on a bad application bundle: "
              << callbackId;
          throw std::runtime_error(
              "Attempting to invoke JS callback on a bad application bundle.");
        }
#ifdef WITH_FBSYSTRACE
        FbSystraceAsyncFlow::end(
            TRACE_TAG_REACT_CXX_BRIDGE, "<callback>", systraceCookie);
        SystraceSection s("NativeToJsBridge::invokeCallback");
#else
        (void)(systraceCookie);
#endif
        executor->invokeCallbackWithDynamic(callbackId, arguments);
      });
}

void NativeToJsBridge::invokeCallback(
    double callbackId,
    std::string &&arguments) {
  int systraceCookie = -1;
#ifdef WITH_FBSYSTRACE
  systraceCookie = m_systraceCookie++;
  FbSystraceAsyncFlow::begin(
      TRACE_TAG_REACT_CXX_BRIDGE, "<callback>", systraceCookie);
#endif

  runOnExecutorQueue(
      [this, callbackId, arguments = std::move(arguments), systraceCookie](
          JSExecutor *executor) {
        if (m_applicationScriptHasFailure) {
          LOG(ERROR)
              << "Attempting to call JS callback on a bad application bundle: "
              << callbackId;
          throw std::runtime_error(
              "Attempting to invoke JS callback on a bad application bundle.");
        }
#ifdef WITH_FBSYSTRACE
        FbSystraceAsyncFlow::end(
            TRACE_TAG_REACT_CXX_BRIDGE, "<callback>", systraceCookie);
        SystraceSection s("NativeToJsBridge::invokeCallback");
#else
        (void)(systraceCookie);
#endif
        executor->invokeCallback(callbackId, std::move(arguments));
      });
}

void NativeToJsBridge::setGlobalVariable(
    std::string propName,
    std::unique_ptr<const JSBigString> jsonValue) {
  runOnExecutorQueue([propName = std::move(propName),
                      jsonValue = folly::makeMoveWrapper(std::move(jsonValue))](
                         JSExecutor *executor) mutable {
    executor->setGlobalVariable(propName, jsonValue.move());
  });
}

void *NativeToJsBridge::getJavaScriptContext() {
  // TODO(cjhopman): this seems unsafe unless we require that it is only called
  // on the main js queue.
  return m_executor->getJavaScriptContext();
}

bool NativeToJsBridge::isInspectable() {
  return m_inspectable;
}

bool NativeToJsBridge::isBatchActive() {
  return m_delegate->isBatchActive();
}

void NativeToJsBridge::handleMemoryPressure(int pressureLevel) {
  runOnExecutorQueue([=](JSExecutor *executor) {
    executor->handleMemoryPressure(pressureLevel);
  });
}

void NativeToJsBridge::destroy() {
  // All calls made through runOnExecutorQueue have an early exit if
  // m_destroyed is true. Setting this before the runOnQueueSync will cause
  // pending work to be cancelled and we won't have to wait for it.
  *m_destroyed = true;
  m_executorMessageQueueThread->runOnQueueSync([this] {
    m_executor->destroy();
    m_executorMessageQueueThread->quitSynchronous();
    m_executor = nullptr;
  });
}

void NativeToJsBridge::runOnExecutorQueue(
    std::function<void(JSExecutor *)> task) {
  if (*m_destroyed) {
      //LOG(ERROR) << "[MSC_LOG]runOnExecutorQueue, destroy return..";
    return;
  }

  std::shared_ptr<bool> isDestroyed = m_destroyed;
    // 在执行任务前打印队列信息
    // LOG(ERROR) << "[MSC_LOG]runOnExecutorQueue queue start executing task"
    //                << " (queue size: " << s_queuedTasks.size() << ")";
  m_executorMessageQueueThread->runOnQueue(
      [this, isDestroyed, task = std::move(task)] {
        if (*isDestroyed) {
            //LOG(ERROR) << "[MSC_LOG]runOnExecutorQueue queue start executing task"
            //                << " (queue size: " << s_queuedTasks.size() << ")";
          return;
        }

          // 执行任务前再次打印
          // LOG(ERROR) << "[MSC_LOG]runOnExecutorQueue queue task executing on JS thread"
          //                    << " (queue size: " << s_queuedTasks.size() << ")";
        // The executor is guaranteed to be valid for the duration of the task
        // because:
        // 1. the executor is only destroyed after it is unregistered
        // 2. the executor is unregistered on this queue
        // 3. we just confirmed that the executor hasn't been unregistered above
        task(m_executor.get());
          // 任务执行完成后打印
          // LOG(ERROR) << "[MSC_LOG]runOnExecutorQueue queue task completed";
      });
}


} // namespace react
} // namespace facebook
