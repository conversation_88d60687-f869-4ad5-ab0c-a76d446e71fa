load("//tools/build_defs/oss:rn_defs.bzl", "ANDROID", "APPLE", "cxx_library", "react_native_xplat_dep", "react_native_xplat_target")

cxx_library(
    name = "jsiexecutor",
    srcs = [
        "jsireact/JSIExecutor.cpp",
        "jsireact/JSINativeModules.cpp",
    ],
    header_namespace = "",
    exported_headers = {
        "jsireact/JSIExecutor.h": "jsireact/JSIExecutor.h",
        "jsireact/JSINativeModules.h": "jsireact/JSINativeModules.h",
    },
    compiler_flags = [
        "-fexceptions",
        "-frtti",
    ],
    fbandroid_deps = [
        "//xplat/folly:molly",
        "//xplat/third-party/glog:glog",
        "//xplat/third-party/linker_lib:atomic",
    ],
    fbobjc_force_static = True,
    fbobjc_header_path_prefix = "",
    fbobjc_labels = ["supermodule:ios/default/public.react_native.infra"],
    platforms = (ANDROID, APPLE),
    preprocessor_flags = [
        "-DLOG_TAG=\"ReactNative\"",
        "-DWITH_FBSYSTRACE=1",
    ],
    visibility = [
        "PUBLIC",
    ],
    xcode_public_headers_symlinks = True,
    deps = [
        "//xplat/fbsystrace:fbsystrace",
        react_native_xplat_dep("jsi:jsi"),
        react_native_xplat_dep("jsi:JSIDynamic"),
        react_native_xplat_target("cxxreact:bridge"),
        react_native_xplat_target("cxxreact:jsbigstring"),
    ],
)
