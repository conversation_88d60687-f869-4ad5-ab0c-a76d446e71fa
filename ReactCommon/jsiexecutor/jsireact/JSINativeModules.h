/*
 * Copyright (c) Facebook, Inc. and its affiliates.
 *
 * This source code is licensed under the MIT license found in the
 * LICENSE file in the root directory of this source tree.
 */

#pragma once

#include <memory>
#include <string>

#include <folly/Optional.h>
#include <msc-jsi/jsi.h>
#include <cxxreact/Instance.h>

namespace facebook {
namespace react {

class JSINativeModules {
 public:
  explicit JSINativeModules(std::shared_ptr<InstanceCallback> callback);
 msc::jsi::Value getModule(msc::jsi::Runtime &rt, const msc::jsi::PropNameID &name);
  void reset();

 private:
  folly::Optional<msc::jsi::Function> m_genNativeModuleJS;
  std::shared_ptr<InstanceCallback> m_callback;
  std::unordered_map<std::string, msc::jsi::Object> m_objects;

  folly::Optional<msc::jsi::Object> createModule(
     msc::jsi::Runtime &rt,
      const std::string &name);
};

} // namespace react
} // namespace facebook
