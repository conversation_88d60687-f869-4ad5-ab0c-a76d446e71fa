// Top-level build file where you can add configuration options common to all sub-projects/modules.
buildscript {
    repositories {
        //本地缓存资源
        mavenLocal()
        //内网 nexus仓库 （代理了大多数外网资源）
        maven { url 'http://depot.sankuai.com/nexus/content/groups/public/' }
        //内网 pixel仓库 （代理了更多外网资源）
        maven { url "http://pixel.sankuai.com/repository/mtdp" }
        //外网资源（外网资源加载不稳定 依赖组件内网都存在就删除相关配置）
//        google()
//        jcenter()
//        mavenCentral()
    }
    dependencies {
        // 用于解决HPX打Release找不到NDK问题
        classpath 'com.meituan.android.gradle:plugin-ndkcompat:1.0.0'
        classpath 'com.android.tools.build:gradle:3.3.3'
        classpath 'com.sankuai.meituan.serviceloader:plugin:2.2.27'
        classpath 'me.tatarka:gradle-retrolambda:3.7.1'
        classpath 'me.tatarka.retrolambda.projectlombok:lombok.ast:0.2.3.a2'
        classpath "com.meituan.android.loader:dynloader-uploader:${DYNLOADER_VERSION}"
//        classpath "com.sankuai.waimai.router:plugin:1.0.56"
        classpath("de.undercouch:gradle-download-task:4.0.2")
        classpath "com.sankuai.waimai:bytecode-manipulator:${WAIMAI_MANIPULATOR_VERSION}"
        classpath "com.meituan.msc.trace:plugin:${MSC_TRACE_VERSION}"
        // force guava 自动化测试插件需要
        classpath('com.google.guava:guava:28.2-jre') {
            force = true
        }
        classpath 'com.meituan.android.mscplugin:mscpreset:********-agp3'
        classpath 'com.vanniktech:gradle-android-junit-jacoco-plugin:0.14.0'
    }
}

repositories {
    //本地缓存资源
    mavenLocal()
    //内网 nexus仓库 （代理了大多数外网资源）
    maven { url 'http://depot.sankuai.com/nexus/content/groups/public/' }
    //内网 pixel仓库 （代理了更多外网资源）
    maven { url "http://pixel.sankuai.com/repository/mtdp" }
    //外网资源（外网资源加载不稳定 依赖组件内网都存在就删除相关配置）
//        google()
//        jcenter()
//        mavenCentral()
}

subprojects {
    repositories {
        //本地缓存资源
        mavenLocal()
        //内网 nexus仓库 （代理了大多数外网资源）
        maven { url 'http://depot.sankuai.com/nexus/content/groups/public/' }
        //内网 pixel仓库 （代理了更多外网资源）
        maven { url "http://pixel.sankuai.com/repository/mtdp" }
        //外网资源（外网资源加载不稳定 依赖组件内网都存在就删除相关配置）
//        google()
//        jcenter()
//        mavenCentral()
    }

    ext {
        compileSdkVersion = 29
        buildToolsVersion = "28.0.3"
        //Android 5.0
        minSdkVersion = 21
        targetSdkVersion = 29
//        supportLibVersion = "25.3.1"

        // 隐私组件版本号
        privacySdkVersion = "0.6.7"
        msiVersion = "12.37.202"
        csslibVersion = "12.34.201"
        babelVersion = '4.17.15'
        metricxVersion = '12.32.201'
        renderVersion = '1.69.211.5-grey1.843615249-SNAPSHOT'
    }
}

apply from: './local_maven.gradle'

apply plugin: 'NdkCompat'
NdkCompat {
    debug false
    ndkVersion "21.4.7075529"
}