# Project-wide Gradle settings.

# IDE (e.g. Android Studio) users:
# Gradle settings configured through the IDE *will override*
# any settings specified in this file.

# For more details on how to configure your build environment visit
# http://www.gradle.org/docs/current/userguide/build_environment.html

# Specifies the JVM arguments used for the daemon process.
# The setting is particularly useful for tweaking memory settings.
org.gradle.jvmargs=-Xmx4096M -XX:MaxPermSize=512m -XX:+HeapDumpOnOutOfMemoryError -Dfile.encoding=UTF-8 -Dorg.gradle.parallel.intra=true

# android.enableD8.desugaring=true
# android.enableD8=true
# android.enableR8=true
# # å¯ç¨Java 8ç¹æ§
# android.enableDesugar=true
# android.enableIncrementalDesugaring=false

# When configured, Gradle will run in incubating parallel mode.
# This option should only be used with decoupled projects. More details, visit
# http://www.gradle.org/docs/current/userguide/multi_project_builds.html#sec:decoupled_projects
# org.gradle.parallel=true
org.gradle.daemon=true
org.gradle.configureondemand=true
org.gradle.caching=true
android.injected.testOnly=false
android.enableBuildCache=false

# æSnapshotå
enableSnapshotBuild=false

# åå¸å°æ¬å°Mavenï¼ç¨äºæ¬å°éªè¯å®¿ä¸»
enableUploadToLocalMaven=false

GROUP=com.meituan.android.msc
# ææ¡£è¿½æº¯ https://km.sankuai.com/page/643986106
MSC_SDK_VERSION=1.0

POM_DESCRIPTION=
POM_URL=
POM_SCM_URL=
POM_SCM_CONNECTION=
POM_SCM_DEV_CONNECTION=
POM_LICENCE_NAME=
POM_LICENCE_URL=
POM_LICENCE_DIST=repo
POM_DEVELOPER_ID=msc
POM_DEVELOPER_NAME=msc
RELEASE_REPOSITORY_URL=http://depot.sankuai.com/nexus/content/repositories/releases/
SNAPSHOT_REPOSITORY_URL=http://depot.sankuai.com/nexus/content/repositories/snapshots/
VERSION_NAME=********-grey1

#æ§å¶appid
share=false

# ä¾èµçç»ä»¶çæ¬
MSC_TRACE_VERSION=0.2.11
FFP_VERSION=0.1.92
WAIMAI_MANIPULATOR_VERSION=0.3.22
DYNLOADER_VERSION=1.1.18
MT_WEBVIEW_VERSION=0.1.94

# æµè¯ä¾èµçæ¬
ROBOLECTRIC_VERSION=4.3.1
EXPRESSO_VERSION = 3.0.2
MOCKITO_CORE_VERSION=2.8.9
POWERMOCK_VERSION=2.0.9
JUNIT_VERSION=4.12
ANDROIDX_TEST_VERSION=1.0.2
MTV8_VERSION=0.2.27-JTT
#ä¸è½½åæ·»å å°ç¨åºä¿¡æ¯
DD_MSC_ADAPTER_VERSION=0.1.34