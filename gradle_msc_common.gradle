/*
 * Copyright 2015 achellies
 *
 * Licensed under the Apache License, Version 2.0 (the "License");
 * you may not use this file except in compliance with the License.
 * You may obtain a copy of the License at
 *
 *     http://www.apache.org/licenses/LICENSE-2.0
 *
 * Unless required by applicable law or agreed to in writing, software
 * distributed under the License is distributed on an "AS IS" BASIS,
 * WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
 * See the License for the specific language governing permissions and
 * limitations under the License.
 */
apply plugin: 'com.android.library'
apply plugin: 'me.tatarka.retrolambda'
apply plugin: 'maven'
apply plugin: 'signing'

def isRunOnHPX = hasProperty("isRunOnHPX") && isRunOnHPX.toBoolean()

if (!isRunOnHPX) {
//    apply plugin: "com.vanniktech.android.junit.jacoco"
//
//    junitJacoco {
//        jacocoVersion = '0.7.1.201405082137'
//        ignoreProjects = []
//        excludes
//        includeNoLocationClasses = false
//        includeInstrumentationCoverageInMergedReport = false
//    }
}

dependencies {

    /******************************* 自动化测试相关依赖 ***********************************************/

    // junit
    testImplementation("junit:junit:${JUNIT_VERSION}")
    androidTestImplementation("junit:junit:${JUNIT_VERSION}")
    // mockito + powermock
    testImplementation("org.mockito:mockito-core:${MOCKITO_CORE_VERSION}")
    testImplementation ("org.powermock:powermock-module-junit4:${POWERMOCK_VERSION}")
    testImplementation ("org.powermock:powermock-api-mockito2:${POWERMOCK_VERSION}")
    // 可选
    testImplementation("org.powermock:powermock-module-junit4-rule:${POWERMOCK_VERSION}")
    testImplementation("org.powermock:powermock-classloading-xstream:${POWERMOCK_VERSION}")
    // robolectric
    testImplementation("org.robolectric:robolectric:${ROBOLECTRIC_VERSION}")
    // json
    testImplementation('org.json:json:20180130')

    // androidJunitRunner
    //noinspection GradleCompatible
    androidTestImplementation("com.android.support.test:runner:${ANDROIDX_TEST_VERSION}")
    androidTestImplementation("com.android.support.test:rules:${ANDROIDX_TEST_VERSION}")
    // espresso UI测试
    androidTestImplementation ("com.android.support.test.espresso:espresso-core:${EXPRESSO_VERSION}")
}

afterEvaluate { project ->
    if (project.getPlugins().hasPlugin('com.android.application') || project.getPlugins().hasPlugin('com.android.library')) {
        task installs(type: Upload, dependsOn: assemble) {
            repositories.mavenInstaller {
                configuration = configurations.archives

                pom.groupId = GROUP
                pom.artifactId = POM_ARTIFACT_ID
                pom.version = VERSION_NAME

                pom.project {
                    name POM_NAME
                    packaging POM_PACKAGING
                    description POM_DESCRIPTION
                    url POM_URL

                    scm {
                        url POM_SCM_URL
                        connection POM_SCM_CONNECTION
                        developerConnection POM_SCM_DEV_CONNECTION
                    }

                    licenses {
                        license {
                            name POM_LICENCE_NAME
                            url POM_LICENCE_URL
                            distribution POM_LICENCE_DIST
                        }
                    }

                    developers {
                        developer {
                            id POM_DEVELOPER_ID
                            name POM_DEVELOPER_NAME
                        }
                    }
                }
            }
        }
        task androidSourcesJarX(type: Jar) {
            classifier = 'sources'
            from android.sourceSets.main.java.source
        }
    } else {
        task installs(type: Upload, dependsOn: assemble) {
            repositories.mavenInstaller {
                configuration = configurations.archives

                pom.groupId = GROUP
                pom.artifactId = POM_ARTIFACT_ID
                pom.version = VERSION_NAME

                pom.project {
                    name POM_NAME
                    packaging POM_PACKAGING
                    description POM_DESCRIPTION
                    url POM_URL

                    scm {
                        url POM_SCM_URL
                        connection POM_SCM_CONNECTION
                        developerConnection POM_SCM_DEV_CONNECTION
                    }

                    licenses {
                        license {
                            name POM_LICENCE_NAME
                            url POM_LICENCE_URL
                            distribution POM_LICENCE_DIST
                        }
                    }

                    developers {
                        developer {
                            id POM_DEVELOPER_ID
                            name POM_DEVELOPER_NAME
                        }
                    }
                }
            }
        }

//        task sourcesJarX(type: Jar, dependsOn: classes) {
//            classifier = 'sources'
//            from sourceSets.main.allSource
//        }
    }

    artifacts {
        if (project.getPlugins().hasPlugin('com.android.application') || project.getPlugins().hasPlugin('com.android.library')) {
            archives androidSourcesJarX
        } else {
            //archives sourcesJarX
        }
    }
}

/**
 * 获取当前分支名
 * @return
 */
def getGitBranchName() {
    return 'git symbolic-ref --short -q HEAD'.execute([], project.rootDir).text.trim()
}

/**
 * 获取最近一次提交的 Commit Id
 * @return
 */
def getGitLastShorCommitId() {
    return 'git rev-parse --short HEAD'.execute([], project.rootDir).text.trim()
}

/**
 * 获取当前分支上最近一个tag
 * @return
 */
def getGitLastTag() {
    return 'git describe --abbrev=0 --tags'.execute([], project.rootDir).text.trim()
}

/**
 * 获取当前分支上最近一个tag创建的时时间戳
 * @return
 */
def getGitLastTagTimeStamp() {
    return Long.parseLong("git log -1 --format=%ct ${getGitLastTag()}".execute([], project.rootDir).text.trim()) * 1000
}

/**
 * 获取开始构建任务的时间戳
 * @return
 */
def getTimeStampOfStartTask() {
    // 只有HPX需要走下面的逻辑
    // 由于HPX的多Module打包是分次执行的task，所以需要借助本地文件来标示
    def startTimeFile = new File(rootDir, 'startTime.tmp')
    if (startTimeFile.exists()) {
        return Long.parseLong(startTimeFile.text)
    } else {
        def time = System.currentTimeMillis()
        startTimeFile.write(String.valueOf(time))
        return time
    }
}

if (hasProperty("VERSION_NAME")) {
    group = GROUP

    def enableSnapshotBuild = hasProperty("enableSnapshotBuild") ? enableSnapshotBuild.toBoolean() : false
    def enableLocalMaven = hasProperty("enableUploadToLocalMaven") ? enableUploadToLocalMaven.toBoolean() : false
    if (enableLocalMaven) {
        // 如果启用发布到MavenLocal功能的话，版本号后面自动添加 -SNAPSHOT，以避免每次打包都修改宿主App中的版本号
        VERSION_NAME += "-SNAPSHOT"
    } else if (enableSnapshotBuild) {
        def useTimeRelatedVersion = hasProperty("useTimeRelatedVersion") ? useTimeRelatedVersion.toBoolean() : false
        def identifier
        if (useTimeRelatedVersion) {
            // 打快照包，会在版本号中间自动加上标识符，以区分不同功能的快照包
            // 使用距离最近一个Tag的时间间隔的毫秒数作为标识
            identifier = getTimeStampOfStartTask() - getGitLastTagTimeStamp()
        } else {
            // 打快照包，会在版本号中间自动加上标识符，以区分不同功能的快照包
            // 使用分支名和最后一个commit的 commit id的hashCode作为版本标识
            identifier = Math.abs((getGitBranchName() + getGitLastShorCommitId()).hashCode())
        }
        VERSION_NAME += ".${identifier}-SNAPSHOT"
    }

    version = VERSION_NAME
}

android {
    testOptions {
        unitTests.all {
            // 修改Robolectric的maven源为内网源，解决单侧外网资源下载慢的问题
            systemProperty 'robolectric.dependency.repo.url', 'http://depot.sankuai.com/nexus/content/groups/public/'
            systemProperty 'robolectric.dependency.repo.id', 'mt'
        }
        unitTests {
            includeAndroidResources = true
        }
    }
}