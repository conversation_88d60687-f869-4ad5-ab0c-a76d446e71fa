/*
 * Copyright (c) Facebook, Inc. and its affiliates.
 *
 * This source code is licensed under the MIT license found in the
 * LICENSE file in the root directory of this source tree.
 */

// [MRN54] 加入美团的源
// 放到这里而不是放到根工程是因为经常通过模块依赖来调试RN
buildscript {
    repositories {
        mavenLocal()
        maven { url 'http://depot.sankuai.com/nexus/content/groups/public' }
        maven { url 'http://depot.sankuai.com/nexus/content/groups/public-snapshots' }
        jcenter()
    }
    dependencies {
//        classpath("de.undercouch:gradle-download-task:4.0.2")
    }
}

plugins {
    id("com.android.library")
    id("maven")
    id("de.undercouch.download")
}

apply from: '../gradle_msc_common.gradle'

import java.nio.file.Paths

import de.undercouch.gradle.tasks.download.Download
import org.apache.tools.ant.taskdefs.condition.Os
import org.apache.tools.ant.filters.ReplaceTokens

// We download various C++ open-source dependencies into downloads.
// We then copy both the downloaded code and our custom makefiles and headers into third-party-ndk.
// After that we build native code from src/main/jni with module path pointing at third-party-ndk.

// MRN63 chendacai 从 downloads 获取 glog 等源文件，而不是直接下载
def customDownloadsDir = "$projectDir/downloads"
def downloadsDir = customDownloadsDir ? new File(customDownloadsDir) : new File("$buildDir/downloads")
def thirdPartyNdkDir = new File("$buildDir/third-party-ndk")

// You need to have following folders in this directory:
//   - boost_1_63_0
//   - double-conversion-1.1.6
//   - folly-deprecate-dynamic-initializer
//   - glog-0.3.5
def dependenciesPath = System.getenv("REACT_NATIVE_DEPENDENCIES")

// The Boost library is a very large download (>100MB).
// If Boost is already present on your system, define the REACT_NATIVE_BOOST_PATH env variable
// and the build will use that.
def boostPath = dependenciesPath ?: System.getenv("REACT_NATIVE_BOOST_PATH")

// Setup build type for NDK, supported values: {debug, release}
//def nativeBuildType = System.getenv("NATIVE_BUILD_TYPE") ?: "release"
// MRN63 chendacai 通过参数来决定是否打可调试的包
def nativeBuildType = project.ext.has('enableNativeDebug') && enableNativeDebug.toBoolean() ? "debug" : "release"

task createNativeDepsDirectories {
    downloadsDir.mkdirs()
    thirdPartyNdkDir.mkdirs()
}

task downloadBoost(dependsOn: createNativeDepsDirectories, type: Download) {
    src("https://github.com/react-native-community/boost-for-react-native/releases/download/v${BOOST_VERSION.replace("_", ".")}-0/boost_${BOOST_VERSION}.tar.gz")
    onlyIfNewer(true)
    overwrite(false)
    dest(new File(downloadsDir, "boost_${BOOST_VERSION}.tar.gz"))
}

task prepareBoost(dependsOn: boostPath ? [] : [downloadBoost], type: Copy) {
    from(boostPath ?: tarTree(resources.gzip(downloadBoost.dest)))
    from("src/main/jni/third-party/boost/Android.mk")
    include("Android.mk", "boost_${BOOST_VERSION}/boost/**/*.hpp", "boost/boost/**/*.hpp")
    includeEmptyDirs = false
    into("$thirdPartyNdkDir/boost")
    doLast {
        file("$thirdPartyNdkDir/boost/boost").renameTo("$thirdPartyNdkDir/boost/boost_${BOOST_VERSION}")
    }
}

task downloadDoubleConversion(dependsOn: createNativeDepsDirectories, type: Download) {
    src("https://github.com/google/double-conversion/archive/v${DOUBLE_CONVERSION_VERSION}.tar.gz")
    onlyIfNewer(true)
    overwrite(false)
    dest(new File(downloadsDir, "double-conversion-${DOUBLE_CONVERSION_VERSION}.tar.gz"))
}

task prepareDoubleConversion(dependsOn: dependenciesPath ? [] : [downloadDoubleConversion], type: Copy) {
    from(dependenciesPath ?: tarTree(downloadDoubleConversion.dest))
    from("src/main/jni/third-party/double-conversion/Android.mk")
    include("double-conversion-${DOUBLE_CONVERSION_VERSION}/src/**/*", "Android.mk")
    filesMatching("*/src/**/*", { fname -> fname.path = "double-conversion/${fname.name}" })
    includeEmptyDirs = false
    into("$thirdPartyNdkDir/double-conversion")
}

task downloadFolly(dependsOn: createNativeDepsDirectories, type: Download) {
    src("https://github.com/facebook/folly/archive/v${FOLLY_VERSION}.tar.gz")
    onlyIfNewer(true)
    overwrite(false)
    dest(new File(downloadsDir, "folly-${FOLLY_VERSION}.tar.gz"))
}
def follyReplaceContent = '''
  ssize_t r;
  do {
    r = open(name, flags, mode);
  } while (r == -1 && errno == EINTR);
  return r;
'''
task prepareFolly(dependsOn: dependenciesPath ? [] : [downloadFolly], type: Copy) {
    from(dependenciesPath ?: tarTree(downloadFolly.dest))
    from("src/main/jni/third-party/folly/Android.mk")
    include("folly-${FOLLY_VERSION}/folly/**/*", "Android.mk")
    eachFile { fname -> fname.path = (fname.path - "folly-${FOLLY_VERSION}/") }
    filter { line -> line.replaceAll('return int\\(wrapNoInt\\(open, name, flags, mode\\)\\);', follyReplaceContent) }
    includeEmptyDirs = false
    into("$thirdPartyNdkDir/folly")
}

//TODO chdc 改回来
//task prepareHermes() {
//    def hermesPackagePath = findNodeModulePath(projectDir, "hermes-engine")
//    if (!hermesPackagePath) {
//        throw new GradleScriptException("Could not find the hermes-engine npm package")
//    }
//
//    def hermesAAR = file("$hermesPackagePath/android/hermes-debug.aar")
//    if (!hermesAAR.exists()) {
//        throw new GradleScriptException("The hermes-engine npm package is missing \"android/hermes-debug.aar\"")
//    }
//
//    def soFiles = zipTree(hermesAAR).matching({ it.include "**/*.so" })
//
//    copy {
//        from soFiles
//        from "src/main/jni/first-party/hermes/Android.mk"
//        into "$thirdPartyNdkDir/hermes"
//    }
//}

task downloadGlog(dependsOn: createNativeDepsDirectories, type: Download) {
    src("https://github.com/google/glog/archive/v${GLOG_VERSION}.tar.gz")
    onlyIfNewer(true)
    overwrite(false)
    dest(new File(downloadsDir, "glog-${GLOG_VERSION}.tar.gz"))
}

// Prepare glog sources to be compiled, this task will perform steps that normally should've been
// executed by automake. This way we can avoid dependencies on make/automake
task prepareGlog(dependsOn: dependenciesPath ? [] : [downloadGlog], type: Copy) {
    from(dependenciesPath ?: tarTree(downloadGlog.dest))
    from("src/main/jni/third-party/glog/")
    include("glog-${GLOG_VERSION}/src/**/*", "Android.mk", "config.h")
    includeEmptyDirs = false
    filesMatching("**/*.h.in") {
        filter(ReplaceTokens, tokens: [
                ac_cv_have_unistd_h           : "1",
                ac_cv_have_stdint_h           : "1",
                ac_cv_have_systypes_h         : "1",
                ac_cv_have_inttypes_h         : "1",
                ac_cv_have_libgflags          : "0",
                ac_google_start_namespace     : "namespace google {",
                ac_cv_have_uint16_t           : "1",
                ac_cv_have_u_int16_t          : "1",
                ac_cv_have___uint16           : "0",
                ac_google_end_namespace       : "}",
                ac_cv_have___builtin_expect   : "1",
                ac_google_namespace           : "google",
                ac_cv___attribute___noinline  : "__attribute__ ((noinline))",
                ac_cv___attribute___noreturn  : "__attribute__ ((noreturn))",
                ac_cv___attribute___printf_4_5: "__attribute__((__format__ (__printf__, 4, 5)))"
        ])
        it.path = (it.name - ".in")
    }
    into("$thirdPartyNdkDir/glog")

    doLast {
        copy {
            from(fileTree(dir: "$thirdPartyNdkDir/glog", includes: ["stl_logging.h", "logging.h", "raw_logging.h", "vlog_is_on.h", "**/src/glog/log_severity.h"]).files)
            includeEmptyDirs = false
            into("$thirdPartyNdkDir/glog/exported/glog")
        }
    }
}

// Create Android.mk library module based on jsc from npm
task prepareJSC {
    doLast {
        def jscPackagePath = findNodeModulePath(projectDir, "jsc-android")
        if (!jscPackagePath) {
            throw new GradleScriptException("Could not find the jsc-android npm package")
        }

        def jscDist = file("$jscPackagePath/dist")
        if (!jscDist.exists()) {
            throw new GradleScriptException("The jsc-android npm package is missing its \"dist\" directory")
        }

        def jscAAR = fileTree(jscDist).matching({ it.include "**/android-jsc/**/*.aar" }).singleFile
        def soFiles = zipTree(jscAAR).matching({ it.include "**/*.so" })

        def headerFiles = fileTree(jscDist).matching({ it.include "**/include/*.h" })

        copy {
            from(soFiles)
            from(headerFiles)
            from("src/main/jni/third-party/jsc/Android.mk")

            filesMatching("**/*.h", { it.path = "JavaScriptCore/${it.name}" })

            includeEmptyDirs(false)
            into("$thirdPartyNdkDir/jsc")
        }
    }
}

// MRN63 chendacai 准备V8，将头文件拷贝到build目录中
task prepareMTV8 {
    doLast {
        copy {
            from("src/main/jni/third-party/mtv8")
            include 'include/**', 'Android.mk'
            includeEmptyDirs(false)
            into("$thirdPartyNdkDir/mtv8")
        }
    }
}

task prepareMSCJSI {
    doLast {
        copy {
            from("src/main/jni/third-party/mscjsi")
            include 'Android.mk'
            includeEmptyDirs(false)
            into("$thirdPartyNdkDir/mscjsi")
        }

        configurations.extractJSIHeaders.files.each {
            def file = it.absoluteFile
            def packageName = file.name.tokenize('-')[0]
            copy {
                from zipTree(file)
                into "$thirdPartyNdkDir/$packageName/headers/msc-jsi"
                includeEmptyDirs = false
                include "**/*.h"
                eachFile { fileDetails ->
                    fileDetails.path = fileDetails.name
                }
            }
        }
    }
}

task downloadNdkBuildDependencies {
    if (!boostPath) {
        dependsOn(downloadBoost)
    }
    dependsOn(downloadDoubleConversion)
    dependsOn(downloadFolly)
    dependsOn(downloadGlog)
}

/**
 * Finds the path of the installed npm package with the given name using Node's
 * module resolution algorithm, which searches "node_modules" directories up to
 * the file system root. This handles various cases, including:
 *
 *   - Working in the open-source RN repo:
 *       Gradle: /path/to/react-native/ReactAndroid
 *       Node module: /path/to/react-native/node_modules/[package]
 *
 *   - Installing RN as a dependency of an app and searching for hoisted
 *     dependencies:
 *       Gradle: /path/to/app/node_modules/react-native/ReactAndroid
 *       Node module: /path/to/app/node_modules/[package]
 *
 *   - Working in a larger repo (e.g., Facebook) that contains RN:
 *       Gradle: /path/to/repo/path/to/react-native/ReactAndroid
 *       Node module: /path/to/repo/node_modules/[package]
 *
 * The search begins at the given base directory (a File object). The returned
 * path is a string.
 */
def findNodeModulePath(baseDir, packageName) {
    def basePath = baseDir.toPath().normalize()
    // Node's module resolution algorithm searches up to the root directory,
    // after which the base path will be null 
    while (basePath) {
        def candidatePath = Paths.get(basePath.toString(), "node_modules", packageName)
        if (candidatePath.toFile().exists()) {
            return candidatePath.toString()
        }
        basePath = basePath.getParent()
    }
    return null
}

def getNdkBuildName() {
    if (Os.isFamily(Os.FAMILY_WINDOWS)) {
        return "ndk-build.cmd"
    } else {
        return "ndk-build"
    }
}

def findNdkBuildFullPath() {
    // we allow to provide full path to ndk-build tool
    if (hasProperty("ndk.command")) {
        return property("ndk.command")
    }
    // or just a path to the containing directory
    if (hasProperty("ndk.path")) {
        def ndkDir = property("ndk.path")
        return new File(ndkDir, getNdkBuildName()).getAbsolutePath()
    }

    if (System.getenv("ANDROID_NDK") != null) {
        def ndkDir = System.getenv("ANDROID_NDK")
        return new File(ndkDir, getNdkBuildName()).getAbsolutePath()
    }

    def ndkDir = android.ndkDirectory ? android.ndkDirectory.absolutePath : null

    if (ndkDir) {
        return new File(ndkDir, getNdkBuildName()).getAbsolutePath()
    }
    return null
}

def reactNativeDevServerPort() {
    def value = project.getProperties().get("reactNativeDevServerPort")
    return value != null ? value : "8081"
}

def reactNativeInspectorProxyPort() {
    def value = project.getProperties().get("reactNativeInspectorProxyPort")
    return value != null ? value : reactNativeDevServerPort()
}

def getNdkBuildFullPath() {
    def ndkBuildFullPath = findNdkBuildFullPath()
    if (ndkBuildFullPath == null) {
        throw new GradleScriptException(
                "ndk-build binary cannot be found, check if you've set " +
                        "\$ANDROID_NDK environment variable correctly or if ndk.dir is " +
                        "setup in local.properties",
                null)
    }
    if (!new File(ndkBuildFullPath).canExecute()) {
        throw new GradleScriptException(
                "ndk-build binary " + ndkBuildFullPath + " doesn't exist or isn't executable.\n" +
                        "Check that the \$ANDROID_NDK environment variable, or ndk.dir in local.properties, is set correctly.\n" +
                        "(On Windows, make sure you escape backslashes in local.properties or use forward slashes, e.g. C:\\\\ndk or C:/ndk rather than C:\\ndk)",
                null)
    }
    return ndkBuildFullPath
}

def buildReactNdkLib = tasks.register("buildReactNdkLib", Exec) {
    dependsOn(prepareMTV8, prepareMSCJSI, prepareBoost, prepareDoubleConversion, prepareFolly, prepareGlog, extractAARHeaders, extractJNIFiles)

    inputs.dir("$projectDir/../ReactCommon")
    inputs.dir("src/main/jni")
    //inputs.dir("src/main/java/com/facebook/react/modules/blob")
    outputs.dir("$buildDir/react-ndk/all")

    commandLine(getNdkBuildFullPath(),
            "NDK_DEBUG=" + (nativeBuildType.equalsIgnoreCase("debug") ? "1" : "0"),
            "NDK_PROJECT_PATH=null",
            "NDK_APPLICATION_MK=$projectDir/src/main/jni/Application.mk",
            "ENABLE_FULL_LTO=" + (project.hasProperty("enableFullLTO") ? project.property("enableFullLTO") : "false"),
            "ENABLE_MSC_LTO=" + (project.hasProperty("enableMscLTO") ? project.property("enableMscLTO") : "false"),
            "ENABLE_MSCEXECUTOR_LTO=" + (project.hasProperty("enableMscexecutorLTO") ? project.property("enableMscexecutorLTO") : "false"),
            "ENABLE_MSCJNI_LTO=" + (project.hasProperty("enableMscjniLTO") ? project.property("enableMscjniLTO") : "false"),
            "ENABLE_BOOST_LTO=" + (project.hasProperty("enableBoostLTO") ? project.property("enableBoostLTO") : "false"),
            "ENABLE_FOLLY_LTO=" + (project.hasProperty("enableFollyLTO") ? project.property("enableFollyLTO") : "false"),
            "ENABLE_MTV8RUNTIME_LTO=" + (project.hasProperty("enableMtv8runtimeLTO") ? project.property("enableMtv8runtimeLTO") : "false"),
            "ENABLE_MSCJSI_LTO=" + (project.hasProperty("enableMscjsiLTO") ? project.property("enableMscjsiLTO") : "false"),
            "ENABLE_MSCJSI_SHARED=" + (project.hasProperty("enableMscjsiShared") ? project.property("enableMscjsiShared") : "false"),
            "NDK_OUT=" + temporaryDir,
            "NDK_LIBS_OUT=$buildDir/react-ndk/all",
            "THIRD_PARTY_NDK_DIR=$buildDir/third-party-ndk",
            "REACT_COMMON_DIR=$projectDir/../ReactCommon",
            "REACT_SRC_DIR=$projectDir/src/main/java/com/meituan/msc/jse",
            "-C", file("src/main/jni/react/jni").absolutePath,
            "--jobs", project.findProperty("jobs") ?: Runtime.runtime.availableProcessors(),
            // "V=1"
    )
}

def cleanReactNdkLib = tasks.register("cleanReactNdkLib", Exec) {
    ignoreExitValue(true)
    errorOutput(new ByteArrayOutputStream())
    commandLine(getNdkBuildFullPath(),
            "NDK_APPLICATION_MK=$projectDir/src/main/jni/Application.mk",
            "THIRD_PARTY_NDK_DIR=$buildDir/third-party-ndk",
            "REACT_COMMON_DIR=$projectDir/../ReactCommon",
            "-C", file("src/main/jni/react/jni").absolutePath,
            "clean")
    // doLast {
    //     file(AAR_OUTPUT_URL).delete()
    //     println("Deleted aar output dir at ${file(AAR_OUTPUT_URL)}")
    // }
}

// MRN60 luojiani 将armeabi-v7a的so拷贝到armeabi里，以支持老旧的 ARM v5
def copySoIntoArmeabi = tasks.register("copySoIntoArmeabi", Copy) {
    dependsOn(buildReactNdkLib)
    from "$buildDir/react-ndk/all/armeabi-v7a"
    into "$buildDir/react-ndk/all/armeabi"
}

// MRN63 chendacai 将没有 strip 的so拷贝出来
def saveNotStrippedSo = tasks.register("saveNotStrippedSo", Copy) {
    dependsOn(buildReactNdkLib)
    from("$buildDir/tmp/buildReactNdkLib/local")
    include "**/*.so"
    // into("$buildDir/react-ndk/exported")
    // MRN60 luojiani 将打好的so直接存起来，以避免重复每次编译RN都需要打so
    into "not-stripped-so"
    exclude("**/libfb.so")
    exclude("**/libmtv8.so")
}

// MRN63 chendacai 将没有 strip 的so拷贝出来
def copyNotStrippedSoIntoArmeabi = tasks.register("copyNotStrippedSoIntoArmeabi", Copy) {
    dependsOn(saveNotStrippedSo)
    from "not-stripped-so/armeabi-v7a"
    into "not-stripped-so/armeabi"
}

def packageReactNdkLibs = tasks.register("packageReactNdkLibs", Copy) {
    dependsOn(copyNotStrippedSoIntoArmeabi, copySoIntoArmeabi)
    from("$buildDir/react-ndk/all")
    include "**/*.so"
    // into("$buildDir/react-ndk/exported")
    // MRN60 luojiani 将打好的so直接存起来，以避免重复每次编译RN都需要打so
    into "jniLibs"
    exclude("**/libjsc.so")
    exclude("**/libhermes.so")
    // MRN60 chendacai 不将 fb.so 和 mtv8.so 放进去，因为它们都在其他的aar中存在了
    exclude("**/libfb.so")
    exclude("**/libmtv8.so")
//    if (project.ext.has('disableMrnJSC') && disableMrnJSC.toBoolean()) {
//        exclude("**/libmrnjscexecutor.so")
//    }
}

def packageReactNdkLibsForBuck = tasks.register("packageReactNdkLibsForBuck", Copy) {
    dependsOn(packageReactNdkLibs)
    from("$buildDir/react-ndk/exported")
    into("src/main/jni/prebuilt/lib")
}

task extractAARHeaders {
    doLast {
        configurations.extractHeaders.files.each {
            def file = it.absoluteFile
            def packageName = file.name.tokenize('-')[0]
            copy {
                from zipTree(file)
                // MRN60 chendacai 将 jar 中的头文件直接拷贝到 build 目录，打包时直接使用
                into "$thirdPartyNdkDir/$packageName/headers"
                includeEmptyDirs = false
                include "**/*.h"
            }
        }
    }
}

task extractJNIFiles {
    doLast {
        configurations.extractJNI.files.each {
            def file = it.absoluteFile
            def packageName = file.name.tokenize('-')[0]
            copy {
                from zipTree(file)
                // MRN60 chendacai 将 aar 中的 so 直接拷贝到 build 目录，打包时直接使用
                into "$thirdPartyNdkDir/$packageName/"
                include "jni/**/*"
            }
        }
    }
}

android {
    compileSdkVersion project.compileSdkVersion
    buildToolsVersion project.buildToolsVersion

    compileOptions {
        sourceCompatibility JavaVersion.VERSION_1_8
        targetCompatibility JavaVersion.VERSION_1_8
    }

    defaultConfig {
        minSdkVersion(16)
        targetSdkVersion(28)
        versionCode(1)
        versionName("1.0")

        consumerProguardFiles("proguard-rules.pro")

        ndk {
            moduleName("reactnativejni")
        }

        buildConfigField("boolean", "IS_INTERNAL_BUILD", "false")
        buildConfigField("int", "EXOPACKAGE_FLAGS", "0")

        resValue "integer", "react_native_dev_server_port", reactNativeDevServerPort()
        resValue "integer", "react_native_inspector_proxy_port", reactNativeInspectorProxyPort()

       // testApplicationId("com.facebook.react.tests.gradle")
        //testInstrumentationRunner("androidx.test.runner.AndroidJUnitRunner")
    }

    sourceSets.main {
        jni.srcDirs = []
        jniLibs.srcDirs = ["jniLibs", "libs"]
        res.srcDirs = ["src/main/res/devsupport", "src/main/res/shell", "src/main/res/views/modal", "src/main/res/views/uimanager"]
        res.excludes = ["xml/rn_dev_preferences.xml"]
        java {
            srcDirs = ["src/main/java", "src/main/libraries/soloader/java", "src/main/jni/first-party/fb/jni/java"]
            // TODO chdc 由于该配置会在项目依赖时影响Yoga的合入，所以在项目依赖时需要将下面的配置失效

        }
    }

    tasks.withType(JavaCompile) {
        compileTask ->
            compileTask.dependsOn(extractJNIFiles)
    }

    if (project.ext.has('enableNativeCompile') && enableNativeCompile.toBoolean()) {
        tasks.withType(JavaCompile) {
            compileTask ->
                compileTask.dependsOn(packageReactNdkLibs)
        }

        clean.dependsOn(cleanReactNdkLib)
    }

    lintOptions {
        abortOnError(false)
    }

    packagingOptions {
        exclude("META-INF/NOTICE")
        exclude("META-INF/LICENSE")
        // libfb.so 和 libc++_shared.so 两个库在 yoga 中已经存在了，所以排除掉
        exclude '**/libfb.so'
        exclude '**/libfbjni.so'
        exclude '**/libc++_shared.so'
        exclude '**/libv8.so'
        exclude '**/libj2v8.so'
        exclude '**/libhermes*.so'
        exclude '**/libmtv8.so'
        exclude '**/libfolly_futures.so'
        exclude '**/libmsc_jsi.so'
        exclude '**/libmtquickjsi.so'
        exclude '**/libmtv8jsi.so'
    }

    configurations {
        extractHeaders
        extractJNI
        extractJSIHeaders
    }
}

dependencies {
    implementation project(path: ':msc-util')
    api("com.facebook.infer.annotation:infer-annotation:0.11.2")
    api("javax.inject:javax.inject:1")
//    api("androidx.appcompat:appcompat:1.0.2") // TODO chdc 使用插件替换
    // api("androidx.swiperefreshlayout:swiperefreshlayout:1.0.0")
    // api("com.facebook.fresco:fresco:${FRESCO_VERSION}")
    // api("com.facebook.fresco:imagepipeline-okhttp3:${FRESCO_VERSION}")
    // api("com.meituan.android.soloader:soloader:*******")
    api("com.google.code.findbugs:jsr305:3.0.2")
    api("com.squareup.okhttp3:okhttp:${OKHTTP_VERSION}")
    api("com.squareup.okio:okio:1.15.0")
    extractHeaders("com.meituan.android.mrn:yoga:${YOGA_VERSION}:headers")
    // extractJNI("com.facebook.fbjni:fbjni:0.0.2")

    api('com.android.support:appcompat-v7:26.0.2')
    implementation 'com.android.support:support-annotations:28.0.0'
    api("com.meituan.android.loader:dynloader-interface:${DYNLOADER_VERSION}")
    api("com.meituan.android.mtv8:mtv8:${MTV8_VERSION}")
    extractJNI("com.meituan.android.mtv8:mtv8:${MTV8_VERSION}") {
        transitive = false
    }
    api('com.meituan.android.cipstorage:library:0.5.8-embedarm64')
    api("com.meituan.android.mrn:yoga:${YOGA_VERSION}")
    extractJNI("com.meituan.android.mrn:yoga:${YOGA_VERSION}") {
        transitive = false
    }
    api('com.sankuai.android.jarvis:library:0.1.4')
    api('com.meituan.dio:dio:0.1.5')
//    api('com.meituan.android.common:unionid:*********')
//    api('com.meituan.android.common.locate:impl:********') {
//        exclude group: 'com.meituan.android.sniffer', module: 'sniffer'
//        exclude group: 'com.squareup.okhttp3', module: 'okhttp'
    api 'com.meituan.android.privacy:interface:0.1.20'
    implementation 'com.meituan.android.mscjsi:mscjsi:0.0.29'
    extractJNI("com.meituan.android.mscjsi:mscjsi:0.0.29") {
        transitive = false
    }
    extractJSIHeaders("com.meituan.android.mscjsi:mscjsi:0.0.29") {
        transitive = false
    }
}
