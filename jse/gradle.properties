GROUP=com.meituan.msc

POM_NAME=jse
POM_ARTIFACT_ID=jse
POM_PACKAGING=aar
POM_DESCRIPTION=RN library
POM_URL=
POM_SCM_URL=
POM_SCM_CONNECTION=
POM_SCM_DEV_CONNECTION=
POM_LICENCE_NAME=
POM_LICENCE_URL=
POM_LICENCE_DIST=repo
POM_DEVELOPER_ID=mrn
POM_DEVELOPER_NAME=mrn
RELEASE_REPOSITORY_URL=http://depot.sankuai.com/nexus/content/repositories/releases/
SNAPSHOT_REPOSITORY_URL=http://depot.sankuai.com/nexus/content/repositories/snapshots/

#MOCKITO_CORE_VERSION=2.19.1
#POWERMOCK_VERSION=1.6.2
#JUNIT_VERSION=4.12
#FEST_ASSERT_CORE_VERSION=2.0M10

#ANDROIDX_TEST_VERSION=1.1.0
#FRESCO_VERSION=2.0.0
OKHTTP_VERSION=3.12.1
SO_LOADER_VERSION=0.8.2
YOGA_VERSION=3.0.6

BOOST_VERSION=1_63_0
DOUBLE_CONVERSION_VERSION=1.1.6
FOLLY_VERSION=2020.01.13.00
GLOG_VERSION=0.3.5

# TODO chdc éè¿æå»ºèæ¬è§£å³AndroidXé®é¢
android.useAndroidX=false
android.enableJetifier=false
android.enableR8=false

# TODO chdc éæ©ä½¿ç¨ç JS å¼æï¼æ¯æ v8, jsc, mtv8, hermes
useJSRuntime=mtv8

# å¦æéè¦ç¼è¾NDKï¼åæ³¨éä¸é¢çéç½®
enableNativeCompile=true

# NativeåºLTOå¨éä¼åå¼å³
enableFullLTO=false
# Nativeåºåæ¨¡åLTOå¼å³ï¼ä»å¨enableFullLTO=falseæ¶çæ
enableMscLTO=true
enableMscexecutorLTO=false
enableMscjniLTO=false
enableBoostLTO=false
enableFollyLTO=false
## enableMtv8runtimeLTOä»å¨enableMscexecutorLTO=trueæ¶çæï¼å¦åæé¾æ¥éè¯¯
enableMtv8runtimeLTO=false
## enableMscjsiLTOå¨enableMscexecutorLTO=trueåenableMscjniLTO=trueæ¶çæï¼å¦åæé¾æ¥éè¯¯
enableMscjsiLTO=true

# æ§å¶jsiåºæ¯å¦çæå¨æåº
enableMscjsiShared=true

# é»è®¤ä¸æå¥mrnjscexecutor.so,æç¬ç«mrnjscexecutor.soæ¶å³é­å¼å³,çº¿ä¸è¦æå¼
disableMrnJSC=true

# å¦æè¦æå»ºDebugæ¨¡å¼çsoï¼åæ³¨éä¸é¢çéç½®
#enableNativeDebug=true
