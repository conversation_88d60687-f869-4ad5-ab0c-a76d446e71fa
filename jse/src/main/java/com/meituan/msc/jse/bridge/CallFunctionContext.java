package com.meituan.msc.jse.bridge;

public class CallFunctionContext implements ICallFunctionContext {
    public final static ICallFunctionContext DO_NOTHING_CONTEXT = new ICallFunctionContext() {
        @Override
        public IBridgeTrace getTrace() {
            return BridgeTrace.DO_NOTHING_TRACE;
        }
    };
    private IBridgeTrace trace;

    @Override
    public IBridgeTrace getTrace() {
        if (trace == null) {
            trace = new BridgeTrace();
        }
        return trace;
    }
}
