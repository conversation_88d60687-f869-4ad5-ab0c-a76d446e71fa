/*
 * Copyright (c) Facebook, Inc. and its affiliates.
 *
 * This source code is licensed under the MIT license found in the
 * LICENSE file in the root directory of this source tree.
 */

package com.meituan.msc.jse.bridge;

import com.meituan.msc.jse.bridge.queue.ReactQueueConfiguration;
import com.meituan.msc.jse.common.annotations.DoNotStrip;

/**
 * A higher level API on top of the asynchronous JSC bridge. This provides an environment allowing
 * the invocation of JavaScript methods and lets a set of Java APIs be invokable from JavaScript as
 * well.
 */
@DoNotStrip
public interface CatalystInstance
    extends MemoryPressureListener, JSInstance {
  /**
   * Destroys this catalyst instance, waiting for any other threads in ReactQueueConfiguration
   * (besides the UI thread) to finish running. Must be called from the UI thread so that we can
   * fully shut down other threads.
   */
  void destroy();

  long getJSRuntimePtr();

  ReactQueueConfiguration getReactQueueConfiguration();

  void changeV8InspectorName(String name);

  void notifyContextReady();

  void setMessageInterface(IMessageInterface messageInterface);

  String getName();
}
