/*
 * Copyright (c) Facebook, Inc. and its affiliates.
 *
 * This source code is licensed under the MIT license found in the
 * LICENSE file in the root directory of this source tree.
 */

package com.meituan.msc.jse.bridge;

import android.os.AsyncTask;
import android.text.TextUtils;

import com.facebook.infer.annotation.ThreadConfined;
import com.facebook.jni.HybridData;
import com.meituan.msc.common.utils.MSCTraceUtil;
import com.meituan.msc.jse.bridge.queue.MessageQueueThread;
import com.meituan.msc.jse.bridge.queue.QueueThreadExceptionHandler;
import com.meituan.msc.jse.bridge.queue.ReactQueueConfiguration;
import com.meituan.msc.jse.bridge.queue.ReactQueueConfigurationImpl;
import com.meituan.msc.jse.bridge.queue.ReactQueueConfigurationSpec;
import com.meituan.msc.jse.common.ReactConstants;
import com.meituan.msc.jse.common.annotations.DoNotStrip;
import com.meituan.msc.modules.reporter.MSCLog;
import com.meituan.msc.systrace.Systrace;
import com.meituan.msc.util.perf.PerfTrace;

import org.json.JSONArray;
import org.json.JSONException;
import org.json.JSONObject;

import java.lang.ref.WeakReference;
import java.util.HashMap;
import java.util.Map;

import static com.facebook.infer.annotation.ThreadConfined.UI;
import static com.meituan.msc.systrace.Systrace.TRACE_TAG_REACT_JAVA_BRIDGE;


/**
 * This provides an implementation of the public CatalystInstance instance. It is public because it
 * is built by XReactInstanceManager which is in a different package.
 */
@DoNotStrip
public class CatalystInstanceImpl implements CatalystInstance, PendingJSCallExecutor {
  private final String TAG = "CatalystInstanceImpl@" + Integer.toHexString(hashCode());

  static {
    ReactBridge.staticInit();
  }

  // Access from any thread
  private final ReactQueueConfigurationImpl mReactQueueConfiguration;
  private volatile boolean mDestroyed = false;
  private final JavaScriptModuleRegistry mJSModuleRegistry;
  private final PendingJSCallManager mPendingJSCallManager;

  private final NativeModuleCallExceptionHandler mNativeModuleCallExceptionHandler;
  private final MessageQueueThread mNativeModulesQueueThread;
  private final MessageQueueThread mJSQueueThread;
  private IMessageInterface messageInterface;

  // C++ parts
  private final HybridData mHybridData;
  private final JSFunctionCaller mJSFunctionCaller;

  private static native HybridData initHybrid();

  private final boolean mRollbackMainThreadEngineFilter;

  public CatalystInstanceImpl(
      final ReactQueueConfigurationSpec reactQueueConfigurationSpec,
      final JavaScriptExecutor jsExecutor,
      final JSFunctionCaller caller,
      final NativeModuleCallExceptionHandler nativeModuleCallExceptionHandler, final boolean rollbackMainThreadEngineFilter,
      final boolean useQuickJS) {
    MSCLog.d(ReactConstants.TAG, "Initializing React Xplat Bridge.");
    Systrace.beginSection(TRACE_TAG_REACT_JAVA_BRIDGE, "createCatalystInstanceImpl");
    mRollbackMainThreadEngineFilter = rollbackMainThreadEngineFilter;

    mHybridData = initHybrid();

    mReactQueueConfiguration =
        ReactQueueConfigurationImpl.create(
            reactQueueConfigurationSpec, new NativeExceptionHandler());
    mJSModuleRegistry = new JavaScriptModuleRegistry();
    mPendingJSCallManager = new PendingJSCallManager(this);
    mJSFunctionCaller = caller;
    mNativeModuleCallExceptionHandler = nativeModuleCallExceptionHandler;
    mNativeModulesQueueThread = mReactQueueConfiguration.getNativeModulesQueueThread();
    mJSQueueThread = mReactQueueConfiguration.getJSQueueThread();
    Systrace.endSection(TRACE_TAG_REACT_JAVA_BRIDGE);

    MSCLog.d(ReactConstants.TAG, "Initializing React Xplat Bridge before initializeBridge");
    Systrace.beginSection(TRACE_TAG_REACT_JAVA_BRIDGE, "initializeCxxBridge");
    // [MRN54 wangjing36] 兜住异常
    try {
      if (MSCTraceUtil.TRACE_LEVEL >= MSCTraceUtil.LEVEL_LIGHT) {
        PerfTrace.begin("CatalystInstanceImpl#initializeBridge");
      }
      initializeBridge(
          new BridgeCallback(this),
          jsExecutor,
          mJSQueueThread,
         jsExecutor.getName(),
              useQuickJS);
      jniStartTimeTracer();
      if (MSCTraceUtil.TRACE_LEVEL >= MSCTraceUtil.LEVEL_LIGHT) {
        PerfTrace.end("CatalystInstanceImpl#initializeBridge");
      }
    } catch (Throwable e) {
      // TODO chdc 初始化失败了后面不可能正确，应该 crash 掉吧？上报一下，如果线上没有问题就将catch放开
      MSCLog.e("CatalystInstanceImpl@initializeBridge", null, e);
    }
    MSCLog.d(ReactConstants.TAG, "Initializing React Xplat Bridge after initializeBridge");
    Systrace.endSection(TRACE_TAG_REACT_JAVA_BRIDGE);
  }

  @Override
  public void setMessageInterface(IMessageInterface messageInterface){
    this.messageInterface = messageInterface;
  }

  public String getSpendTime(long start, long end) {
    return jniGetSpendTimeFromTimeTracer(start, end);

  }

  public void stopAndClearTracer() {
    jniStopTimeTracer();
    jniClearTimeTracer();
  }

  private static class BridgeCallback implements ReactCallback {
    // We do this so the callback doesn't keep the CatalystInstanceImpl alive.
    // In this case, the callback is held in C++ code, so the GC can't see it
    // and determine there's an inaccessible cycle.
    private final WeakReference<CatalystInstanceImpl> mOuter;

    BridgeCallback(CatalystInstanceImpl outer) {
      mOuter = new WeakReference<>(outer);
    }

    @Override
    public void onBatchComplete() {
    }

    @Override
    public void incrementPendingJSCalls() {
    }

    @Override
    public void decrementPendingJSCalls() {
    }

    @Override
    public String invokeMSCCallback(String functionName, ReadableNativeArray array) {
      return mOuter.get().invokeMSCCallback(functionName, array);
    }

    @Override
    public String invokeMSCCallback(String objectName, String functionName, ReadableNativeArray array) {
      return mOuter.get().invokeMSCCallback(objectName, functionName, array);
    }

    @Override
    public NativeArray getModuleConfig(String moduleName) {
      return mOuter.get().getConfig(moduleName);
    }

    @Override
    public void callNativeModules(String queue) {
      mOuter.get().callNativeModules(queue);
    }

    @Override
    public NativeArray callSerializableNativeHook(String moduleName, String methodName, String params) {
      ICallFunctionContext context = new CallFunctionContext();
      context.getTrace().instant("nativeReceiveTime");
      NativeArray result = mOuter.get().callSerializableNativeHook(context, moduleName, methodName, params);
      context.getTrace().instant("jsThreadEnd");
      return result;
    }
  }

  private native void initializeBridge(
      ReactCallback callback,
      JavaScriptExecutor jsExecutor,
      MessageQueueThread jsQueue,
      String name,
      boolean useQuickJS);

  // [MRN63：leipengchao] MRN无引擎渲染表达式支持，通过V8计算表达式并且返回结果
  @Override
  public String evaluateJavaScript(String script, String assetURL, String codeCacheFile, LoadJSCodeCacheCallback loadJSCodeCacheCallback) {
    return jniEvaluateJavaScript(script, assetURL, codeCacheFile, loadJSCodeCacheCallback != null ? new LoadJSCodeCacheCallbackJNIWrapper(loadJSCodeCacheCallback) : null);
  }

  @Override
  public void changeV8InspectorName(String name) {
    jniChangeV8InspectorName(name);
  }

  @Deprecated
  private native void jniSetSourceURL(String sourceURL);

  @Override
  public String executeJSFunction(String moduleName, String methodName, String params) {
    if (!mRollbackMainThreadEngineFilter && isDestroyed()) {
      MSCLog.i(TAG, "executeJSFunction when destroy", methodName);
      return null;
    }
    if (TextUtils.isEmpty(moduleName) || TextUtils.isEmpty(methodName) || TextUtils.isEmpty(params)) {
      return null;
    }
    return jniExecuteJSModule(moduleName, methodName, params);
  }

  @Override
  public String executeListFunction(String moduleName, String methodName, String jsModuleName, String jsMethodName, String params) {
    if (!mRollbackMainThreadEngineFilter && isDestroyed()) {
      MSCLog.i(TAG, "executeListFunction when destroy", methodName, jsMethodName);
      return null;
    }
    if (TextUtils.isEmpty(moduleName) || TextUtils.isEmpty(methodName) || TextUtils.isEmpty(jsModuleName) || TextUtils.isEmpty(jsMethodName) || TextUtils.isEmpty(params)) {
      return null;
    }
    return jniExecuteListFunction(moduleName, methodName, jsModuleName, jsMethodName, params);
  }

  private native String jniExecuteJSFunction(String name, String params);

  public native long jniGetJSRuntimePtr();

  private native String jniExecuteJSModule(String moduleName, String methodName, String params);

  private native String jniExecuteListFunction(String moduleName, String methodName, String jsModuleName, String jsMethodName, String params);

  // [MRN63：leipengchao] MRN精简版引擎，详见：https://km.sankuai.com/page/516768147
  @Deprecated
  private native void jniLoadScriptFromString(
      String script, String assetURL, boolean loadSynchronously);

  // [MRN63：leipengchao] MRN无引擎渲染表达式支持，通过V8计算表达式并且返回结果
  private native String jniEvaluateJavaScript(
      String script, String assetURL, String codeCacheFile, LoadJSCodeCacheCallbackJNIWrapper loadJSCodeCacheCallback);

  // [MRN63：leipengchao] MRN无引擎渲染表达式，同步设置global变量
  private native void jniSetGlobalVariableSync(
        String script, String assetURL);

  private native void jniChangeV8InspectorName(
          String name);

  @Deprecated
  private native void jniLoadScriptFromFile(
      String fileName, String sourceURL, boolean loadSynchronously);

  @Override
  public void notifyContextReady() {
    mPendingJSCallManager.acceptCalls();
  }

  // Java打印时间戳，毫秒级，输出格式类似: 2023-11-15 14:30:45.123
  // public static final SimpleDateFormat TIME_FORMAT = new SimpleDateFormat("yyyy-MM-dd HH:mm:ss.SSS");

  @Override
  public void execute(PendingJSCall call) {
    try {
      // if (call.mMethod.equals("onAppRoute")) {
      //        MSCLog.i(TAG, "[MSC_LOG]execute 调用native方法jniCallJSFunction:", call.mModule, call.mMethod, TIME_FORMAT.format(new Date(System.currentTimeMillis())));
      //      }
      jniCallJSFunction(call.mModule, call.mMethod, call.argumentsString());
      // if (call.mMethod.equals("onAppRoute")) {
      //        MSCLog.i(TAG, "[MSC_LOG]execute 调用native方法jniCallJSFunction结束:", call.mModule, call.mMethod, TIME_FORMAT.format(new Date(System.currentTimeMillis())));
      //      }
    } catch (OutOfMemoryError e) {
      MSCLog.e(TAG, "CatalystInstanceImpl.execute OOM module:" + call.mModule + ", method:" + call.mMethod);
      // MSCLog.e(TAG, "[MSC_LOG]CatalystInstanceImpl.execute OOM module:" + call.mModule + ", method:" + call.mMethod, TIME_FORMAT.format(new Date(System.currentTimeMillis())));
      throw e;
    }
  }

  private native void jniCallJSFunction(String module, String method, String arguments);

  @Override
  public void callFunction(final String module, final String method, final JSONArray arguments) {
//    MSCLog.i(TAG, "callFunction ", module, method);
    // if (method.equals("onAppRoute")) {
    //      MSCLog.i(TAG, "[MSC_LOG]callFunction:", module, method, TIME_FORMAT.format(new Date(System.currentTimeMillis())));
    //    }
    callFunction(new PendingJSCall(module, method, arguments));
  }

  public void callFunction(PendingJSCall function) {
    if (mDestroyed) {
      final String call = function.toString();
      MSCLog.w(ReactConstants.TAG, "Calling JS function after bridge has been destroyed: " + call);
      return;
    }
    mPendingJSCallManager.cacheOrAcceptCall(function);
  }

  private native void jniCallJSCallback(int callbackID, String arguments);

  @Override
  public void invokeCallback(final int callbackID, final JSONArray arguments) {
    if (mDestroyed) {
      MSCLog.w(ReactConstants.TAG, "Invoking JS callback after bridge has been destroyed.");
      return;
    }
    jniCallJSCallback(callbackID, arguments.toString());
  }

  private native void jniCallJSCallbackWithDynamic(int callbackID, NativeArray arguments);

  @Override
  public void invokeCallback(final int callbackID, final NativeArray arguments) {
    if (mDestroyed) {
      MSCLog.w(ReactConstants.TAG, "Invoking JS callback after bridge has been destroyed.");
      return;
    }
    jniCallJSCallbackWithDynamic(callbackID, arguments);
  }

  /**
   * Destroys this catalyst instance, waiting for any other threads in ReactQueueConfiguration
   * (besides the UI thread) to finish running. Must be called from the UI thread so that we can
   * fully shut down other threads.
   */
  @Override
  @ThreadConfined(UI)
  public void destroy() {
    MSCLog.d(ReactConstants.TAG, "CatalystInstanceImpl.destroy() start");
    UiThreadUtil.assertOnUiThread();

    destroyV1();
  }
  
  public void runOnJs(Runnable jsRunnable, Runnable uiRunnable) {
    mJSQueueThread.runOnQueue(new Runnable() {
      @Override
      public void run() {
        jsRunnable.run();
        UiThreadUtil.runOnUiThread(uiRunnable);
      }
    });
  }

  @Override
  public long getJSRuntimePtr() {
    return jniGetJSRuntimePtr();
  }

  @ThreadConfined(UI)
  private void destroyV1() {
    MSCLog.d(ReactConstants.TAG, "CatalystInstanceImpl.destroyV1() start");
    UiThreadUtil.assertOnUiThread();

    if (mDestroyed) {
      return;
    }

    // TODO: tell all APIs to shut down
    ReactMarker.logMarker(ReactMarkerConstants.DESTROY_CATALYST_INSTANCE_START);
    mDestroyed = true;

    mNativeModulesQueueThread.runOnQueue(
        new Runnable() {
          @Override
          public void run() {
                mJSQueueThread.runOnQueue(
                    new Runnable() {
                      @Override
                      public void run() {
                        // MRN60 chendacai V8必须在当初创建V8的线程销毁
                        mHybridData.resetNative();
                        mReactQueueConfiguration
                            .getUIQueueThread()
                            .runOnQueue(
                                new Runnable() {
                                  @Override
                                  public void run() {
                                    // AsyncTask.execute must be executed from the UI Thread
                                    AsyncTask.execute(
                                        new Runnable() {
                                          @Override
                                          public void run() {
                                            mReactQueueConfiguration.destroy();
                                            MSCLog.d(
                                                ReactConstants.TAG,
                                                "CatalystInstanceImpl.destroy() end");
                                            ReactMarker.logMarker(
                                                ReactMarkerConstants.DESTROY_CATALYST_INSTANCE_END);
                                          }
                                        });
                                  }
                                });
                      }
                    });
          }
        });
  }

  @Override
  public boolean isDestroyed() {
    return mDestroyed;
  }

  @Override
  public ReactQueueConfiguration getReactQueueConfiguration() {
    return mReactQueueConfiguration;
  }

  @Override
  public <T extends JavaScriptModule> T getJSModule(Class<T> jsInterface) {
    return mJSModuleRegistry.getJavaScriptModule(mJSFunctionCaller == null ? this : mJSFunctionCaller, jsInterface);
  }

  private native void jniHandleMemoryPressure(int level);

  @Override
  public void handleMemoryPressure(int level) {
    if (mDestroyed) {
      return;
    }
    jniHandleMemoryPressure(level);
  }

  public native void setGlobalVariable(String propName, String jsonValue);

  @Override
  public native void setGlobalVariableString(String propName, String stringValue);

  // keep to avoid jni crash
  private native long getJavaScriptContext();

  private void onNativeException(Exception e) {
    // TODO *********: remove this after investigation
    MSCLog.e(ReactConstants.TAG, e, "CatalystInstanceImpl caught native exception");

    mNativeModuleCallExceptionHandler.handleException(e);
  }

  private class NativeExceptionHandler implements QueueThreadExceptionHandler {
    @Override
    public void handleException(Exception e) {
      // Any Exception caught here is because of something in JS. Even if it's a bug in the
      // framework/native code, it was triggered by JS and theoretically since we were able
      // to set up the bridge, JS could change its logic, reload, and not trigger that crash.
      onNativeException(e);
    }
  }

  // [MRN60: chendacai] DIO 适配，详见：https://km.sankuai.com/page/349814196
  @Deprecated
  private native void jniLoadScriptFromDioFile(String dioFilePath, String entryFilePath, String sourceURL, boolean loadSynchronously);

  // [MRN60: chendacai] 主动GC，详见：https://km.sankuai.com/page/504934851
  private native void jniJSIGarbageCollect();
  private native long jniGetJSIMemoryUsage();
  private native void jniJSIStartCPUProfiling(String profilerName, int interval);
  private native void jniJSIStopCPUProfiling(String profilerName, String traceFilePath);

  private native void jniStartTimeTracer();
  private native void jniStopTimeTracer();
  private native String jniGetSpendTimeFromTimeTracer(long start, long end);
  private native void jniClearTimeTracer();

  // [MRN60: chendacai] 主动GC，详见：https://km.sankuai.com/page/504934851
  @Override
  public void garbageCollect() {
    jniJSIGarbageCollect();
  }

  // [MRN60: chendacai] 主动GC，详见：https://km.sankuai.com/page/504934851
  @Override
  public long getMemoryUsage() {
    return jniGetJSIMemoryUsage();
  }

  @Override
  public void startCPUProfiling(String profilerName, int interval) {
    // MSCLog.i(TAG, "[MSC_LOG]startCpuProfiling profilerName:",profilerName, " time:", CatalystInstanceImpl.TIME_FORMAT.format(new Date(System.currentTimeMillis())));
    jniJSIStartCPUProfiling(profilerName, interval);
    // MSCLog.i(TAG, "[MSC_LOG]startCpuProfiling profilerName:",profilerName, ", jniJSIStartCPUProfiling call end, time:", CatalystInstanceImpl.TIME_FORMAT.format(new Date(System.currentTimeMillis())));
  }

  @Override
  public void stopCPUProfiling(String profilerName, String traceFilePath) {
    // MSCLog.i(TAG, "[MSC_LOG]stopCPUProfiling profilerName:",profilerName,",traceFilePath:",traceFilePath, " time:", CatalystInstanceImpl.TIME_FORMAT.format(new Date(System.currentTimeMillis())));
    jniJSIStopCPUProfiling(profilerName, traceFilePath);
    // MSCLog.i(TAG, "[MSC_LOG]stopCPUProfiling profilerName:",profilerName,",traceFilePath:",traceFilePath, ", jniJSIStopCPUProfiling call end, time:", CatalystInstanceImpl.TIME_FORMAT.format(new Date(System.currentTimeMillis())));
  }

  /*********************** 特殊支持mmp *****************/

  private final Map<String, JavaCallback> methodDescriptorMap = new HashMap<>();

  private final Map<String, Map<String, JavaCallback>> objectDescriptorMap = new HashMap<>();

  @Override
  public void registerJavaCallback(String functionName, JavaCallback callback) {
    if (TextUtils.isEmpty(functionName) || callback == null) {
      return;
    }
    if (methodDescriptorMap.containsKey(functionName)) {
      throw new RuntimeException("has register " + functionName + "!!!");
    }
    methodDescriptorMap.put(functionName, callback);
    registerMethod(functionName);
  }

  private native void registerMethod(String functionName);

  @Override
  public void registerJSObject(String name, JavaFunctionsInterface functionsInterface) {
    if (TextUtils.isEmpty(name) || functionsInterface == null) {
      return;
    }
    if (objectDescriptorMap.containsKey(name)) {
      throw new RuntimeException("has register " + name + "!!!");
    }
    Map<String, JavaCallback> methodDescriptorMap = new HashMap<>();
    objectDescriptorMap.put(name, methodDescriptorMap);
    String[] functionNames = functionsInterface.getFunctionNames();
    JavaCallback[] functions = functionsInterface.getFunctions();
    if (functionNames == null || functions == null || functionNames.length != functions.length) {
      return;
    }
    for (int i = 0; i < functionNames.length; i++) {
      methodDescriptorMap.put(functionNames[i], functions[i]);
    }
    registerJSObject(name);
  }

  private native void registerJSObject(String name);

  private String invokeMSCCallback(String objectName, String functionName, ReadableNativeArray array) {
    if (TextUtils.isEmpty(objectName) || TextUtils.isEmpty(functionName) || array == null) {
      return null;
    }
//    MSCLog.i(TAG, "invokeMSCCallback ", objectName, functionName);
    return objectDescriptorMap.get(objectName).get(functionName).invoke(array);
  }

  private String invokeMSCCallback(String functionName, ReadableNativeArray array) {
//    MSCLog.i(TAG, "invokeMSCCallback ", functionName);
    return methodDescriptorMap.get(functionName).invoke(array);
  }


  /*********************** 桥的注册的查找从C++转到Java *****************/

  /**
   * JS调用Native 桥，同步调用
   *
   * @param context
   * @param moduleName
   * @param methodName
   * @param params
   * @return
   */
  private NativeArray callSerializableNativeHook(ICallFunctionContext context, String moduleName, String methodName, String params) {
//    MSCLog.i(TAG, "callSerializableNativeHook ", moduleName, methodName);
    if (messageInterface != null) {
      Object result = messageInterface.invokeSync(context, moduleName, methodName, new LazyParseJSONArray(params));
      WritableNativeArray ret = null;
      try {
        if (result instanceof JSONObject) {
          WritableMap map = ConversionUtil.jsonToReact((JSONObject) result);
          if (map != null) {
            ret = RNArguments.fromJavaArgs(new Object[]{map});
          }
        } else if (result instanceof JSONArray) {
          WritableArray array = ConversionUtil.jsonToReact((JSONArray) result);
          if (array != null) {
            ret = RNArguments.fromJavaArgs(new Object[]{array});
          }
        } else {
          ret = RNArguments.fromJavaArgs(new Object[]{result});
        }
      } catch (JSONException e) {
        e.printStackTrace();
      }
      // TODO：抛到消息总线
      return ret == null ? new WritableNativeArray() : ret;
    }
    return new WritableNativeArray();
  }

  /**
   * JS调用Native桥，异步调用
   */
  private void callNativeModules(String queue) {
//    MSCLog.i(TAG, "callNativeModules");
    if (messageInterface == null) {
      return;
    }
    messageInterface.batchInvoke(queue);
  }

  /**
   * 获取桥的信息
   */
  private NativeArray getConfig(String moduleName) {
    JSONArray result = null;
    if (messageInterface != null) {
      try {
        result = messageInterface.getConfig(moduleName);
      } catch (Exception exception){
        // 对齐iOS ignore ModuleNotFoundException
      }
    }
    if (result == null) {
      return new WritableNativeArray();
    }
    try {
      return (NativeArray) ConversionUtil.jsonToReact(result);
    } catch (JSONException e) {
      e.printStackTrace();
    }
    return new WritableNativeArray();
  }

  @Override
  public String getName() {
    return "V8";
  }
}
