package com.meituan.msc.jse.bridge;

import android.os.Bundle;
import android.text.TextUtils;

import com.google.gson.Gson;
import com.google.gson.GsonBuilder;

import org.json.JSONArray;
import org.json.JSONException;
import org.json.JSONObject;

import java.io.Serializable;
import java.lang.ref.WeakReference;
import java.lang.reflect.Array;
import java.util.ArrayList;
import java.util.Collection;
import java.util.HashMap;
import java.util.Iterator;
import java.util.List;
import java.util.Map;

import javax.annotation.Nullable;

import static org.json.JSONObject.NULL;

/**
 * Helper utilities to convert from react-native types to POJOs
 * @api
 */
public final class ConversionUtil {
    /**
     * toObject extracts a value from a {@link ReadableMap} by its key,
     * and returns a POJO representing that object.
     *
     * @param readableMap The Map to containing the value to be converted
     * @param key         The key for the value to be converted
     * @return The converted POJO
     */
    public static Object toObject(@Nullable ReadableMap readableMap, String key) {
        if (readableMap == null) {
            return null;
        }

        Object result;

        ReadableType readableType = readableMap.getType(key);
        switch (readableType) {
            case Null:
                result = null;
                break;
            case Boolean:
                result = readableMap.getBoolean(key);
                break;
            case Number:
                // Can be int or double.
                try {
                    // 如果 int 能承载就用 int
                    // 如果 long 能承载就用 long
                    // 否则用 double
                    double tmp = readableMap.getDouble(key);
                    if (tmp == (int) tmp) {
                        result = (int) tmp;
                    } else if (tmp == (long) tmp) {
                        result = (long) tmp;
                    } else {
                        result = tmp;
                    }
                } catch (Exception e) {
                    result = readableMap.getInt(key);
                }
                break;
            case String:
                result = readableMap.getString(key);
                break;
            case Map:
                result = toMap(readableMap.getMap(key));
                break;
            case Array:
                result = toList(readableMap.getArray(key));
                break;
            default:
                throw new IllegalArgumentException(
                        "Could not convert object with key: " + key + ".");
        }

        return result;
    }

    /**
     * toMap converts a {@link ReadableMap} into a HashMap.
     * 因为 com.facebook.react.bridge.ReadableNativeMap#toHashMap() 在转换 Number 会有精度问题，所以使用该实现来修正
     *
     * @param readableMap The ReadableMap to be conveted.
     * @return A HashMap containing the data that was in the ReadableMap.
     */
    public static Map<String, Object> toMap(@Nullable ReadableMap readableMap) {
        if (readableMap == null) {
            return null;
        }

        ReadableMapKeySetIterator iterator = readableMap.keySetIterator();
        if (!iterator.hasNextKey()) {
            return new HashMap<>();
        }

        Map<String, Object> result = new HashMap<>();
        while (iterator.hasNextKey()) {
            String key = iterator.nextKey();
            result.put(key, toObject(readableMap, key));
        }

        return result;
    }

    /**
     * toList converts a {@link ReadableArray} into an ArrayList.
     * 因为 com.facebook.react.bridge.ReadableNativeArray#toArrayList() 在转换 Number 会有精度问题，所以使用该实现来修正
     *
     * @param readableArray The ReadableArray to be conveted.
     * @return An ArrayList containing the data that was in the ReadableArray.
     */
    public static List<Object> toList(@Nullable ReadableArray readableArray) {
        if (readableArray == null) {
            return null;
        }

        List<Object> result = new ArrayList<>(readableArray.size());
        for (int index = 0; index < readableArray.size(); index++) {
            ReadableType readableType = readableArray.getType(index);
            switch (readableType) {
                case Null:
                    result.add(null);
                    break;
                case Boolean:
                    result.add(readableArray.getBoolean(index));
                    break;
                case Number:
                    // Can be int or double.
                    double tmp = readableArray.getDouble(index);
                    if (tmp == (int) tmp) {
                        result.add((int) tmp);
                    } else {
                        result.add(tmp);
                    }
                    break;
                case String:
                    result.add(readableArray.getString(index));
                    break;
                case Map:
                    result.add(toMap(readableArray.getMap(index)));
                    break;
                case Array:
                    result = toList(readableArray.getArray(index));
                    break;
                default:
                    throw new IllegalArgumentException(
                            "Could not convert object with index: " + index + ".");
            }
        }

        return result;
    }

    /**
     * Json to react writable map.
     *
     * @param jsonObject the json object
     * @return writable map
     * @throws JSONException the json exception
     */
    public static WritableMap jsonToReact(JSONObject jsonObject) throws JSONException {
        if (jsonObject == null) {
            return null;
        }
        WritableMap writableMap = RNArguments.createMap();
        Iterator iterator = jsonObject.keys();
        while (iterator.hasNext()) {
            String key = (String) iterator.next();
            Object value = jsonObject.get(key);
            if (value instanceof Number) {
                if (value instanceof Integer) {
                    writableMap.putInt(key, (Integer) value);
                } else {
                    writableMap.putDouble(key, ((Number) value).doubleValue());
                }
            } else if (value instanceof String) {
                writableMap.putString(key, jsonObject.getString(key));
            } else if (value instanceof JSONObject) {
                writableMap.putMap(key, jsonToReact(jsonObject.getJSONObject(key)));
            } else if (value instanceof JSONArray) {
                writableMap.putArray(key, jsonToReact(jsonObject.getJSONArray(key)));
            } else if (value instanceof Boolean) {
                writableMap.putBoolean(key, jsonObject.getBoolean(key));
            } else if (value == JSONObject.NULL) {
                writableMap.putNull(key);
            }
        }

        return writableMap;
    }

    /**
     * Json to react writable array.
     *
     * @param jsonArray the json array
     * @return writable array
     * @throws JSONException the json exception
     */
    public static WritableArray jsonToReact(JSONArray jsonArray) throws JSONException {
        if (jsonArray == null) {
            return null;
        }
        WritableArray writableArray = RNArguments.createArray();
        for (int i = 0; i < jsonArray.length(); i++) {
            Object value = jsonArray.get(i);
            if (value instanceof Number) {
                if (value instanceof Integer) {
                    writableArray.pushInt((Integer) value);
                } else {
                    writableArray.pushDouble(((Number) value).doubleValue());
                }
            } else if (value instanceof String) {
                writableArray.pushString(jsonArray.getString(i));
            } else if (value instanceof JSONObject) {
                writableArray.pushMap(jsonToReact(jsonArray.getJSONObject(i)));
            } else if (value instanceof JSONArray) {
                writableArray.pushArray(jsonToReact(jsonArray.getJSONArray(i)));
            } else if (value instanceof Boolean) {
                writableArray.pushBoolean(jsonArray.getBoolean(i));
            } else if (value == JSONObject.NULL) {
                writableArray.pushNull();
            }
        }
        return writableArray;
    }

    /***
     * 将Bundle转成JSONObject
     * @param bundle the bundle
     * @return the json object
     */
    public static JSONObject fromBundle(Bundle bundle) {
        if (bundle == null) {
            return null;
        }
        JSONObject result = new JSONObject();
        for (String key : bundle.keySet()) {
            try {
                result.put(key, wrap(bundle.get(key)));
            } catch (JSONException e) {
            }
        }
        return result;
    }

    private static Object wrap(Object o) {
        if (o == null) {
            return NULL;
        }
        if (o instanceof JSONArray || o instanceof JSONObject) {
            return o;
        }
        if (o.equals(NULL)) {
            return o;
        }
        try {
            if (o instanceof Collection) {
                return new JSONArray((Collection) o);
            } else if (o.getClass().isArray()) {
                return toJSONArray(o);
            }
            if (o instanceof Map) {
                return new JSONObject((Map) o);
            }
            if (o instanceof Boolean ||
                    o instanceof Byte ||
                    o instanceof Character ||
                    o instanceof Double ||
                    o instanceof Float ||
                    o instanceof Integer ||
                    o instanceof Long ||
                    o instanceof Short ||
                    o instanceof String) {
                return o;
            }
            if (o.getClass().getPackage().getName().startsWith("java.")) {
                return o.toString();
            }
        } catch (Exception ignored) {
        }
        return null;
    }

    private static JSONArray toJSONArray(Object array) throws JSONException {
        JSONArray result = new JSONArray();
        if (!array.getClass().isArray()) {
            throw new JSONException("Not a primitive array: " + array.getClass());
        }
        final int length = Array.getLength(array);
        for (int i = 0; i < length; ++i) {
            result.put(wrap(Array.get(array, i)));
        }
        return result;
    }

    /**
     * JSONObject 转为 Map<String, Object>
     *
     * @param object the object
     * @return the map
     */
    public static Map<String, Object> convertJSONToMap(JSONObject object) {
        if (object == null) {
            return null;
        }
        Map<String, Object> result = new HashMap<>();
        if (object != null) {
            Iterator keys = object.keys();
            while (keys.hasNext()) {
                String key = (String) keys.next();
                try {
                    result.put(key, fromJson(object.get(key)));
                } catch (JSONException e) {
                    e.printStackTrace();
                }
            }
        }
        return result;
    }

    public static Object fromJson(Object json) throws JSONException {
        if (json == JSONObject.NULL) {
            return null;
        } else if (json instanceof JSONObject) {
            return convertJSONToMap((JSONObject) json);
        } else if (json instanceof JSONArray) {
            return toList((JSONArray) json);
        } else {
            return json;
        }
    }

    private static List toList(JSONArray array) throws JSONException {
        List list = new ArrayList();
        for (int i = 0; i < array.length(); i++) {
            list.add(fromJson(array.get(i)));
        }
        return list;
    }

    /**
     * 将 Map 转换为 Bundle
     *
     * @param data the data
     * @return bundle bundle
     */
    public static Bundle mapToBundle(Map<String, Object> data) {
        if (data == null) {
            return null;
        }
        Bundle bundle = new Bundle();
        for (Map.Entry<String, Object> entry : data.entrySet()) {
            Object value = entry.getValue();
            if (value instanceof Serializable) {
                bundle.putSerializable(entry.getKey(), (Serializable) value);
            }
        }
        return bundle;
    }

    private static volatile Gson gson;

    private static volatile Gson gsonWithNullValue;
    /**
     * Gets gson.
     *
     * @return the gson
     */
    public static Gson getGson() {
        if (gson == null) {
            synchronized (ConversionUtil.class) {
                if (gson == null) {
                    gson = new Gson();
                }
            }
        }
        return gson;
    }

    public static Gson getGsonWithNullValue() {
        if (gsonWithNullValue == null) {
            synchronized (ConversionUtil.class) {
                if (gsonWithNullValue == null) {
                    gsonWithNullValue = new GsonBuilder().serializeNulls().create();
                }
            }
        }
        return gsonWithNullValue;
    }

    /**
     * To json string string.
     *
     * @param object the object
     * @return the string
     */
    public static String toJsonString(Object object) {
        if (object == null) {
            return null;
        }
        try {
            return getGson().toJson(object);
        } catch (Throwable e) {
            e.printStackTrace();
            return null;
        }
    }

    public static String toJsonStringWithNullValue(Object object) {
        if (object == null) {
            return null;
        }
        try {
            return getGsonWithNullValue().toJson(object);
        } catch (Throwable e) {
            e.printStackTrace();
            return null;
        }
    }

    /**
     * From json string t.
     *
     * @param <T>        the type parameter
     * @param jsonString the json string
     * @param cls        the cls
     * @return the t
     */
    public static <T> T fromJsonString(String jsonString, Class<T> cls) {
        if (jsonString == null || cls == null) {
            return null;
        }
        try {
            return getGson().fromJson(jsonString, cls);
        } catch (Throwable e) {
            e.printStackTrace();
            return null;
        }
    }

    /**
     * Parse json string json object.
     *
     * @param json the json
     * @return the json object
     * @throws JSONException the json exception
     */
// 尝试解决 org.json.JSONException: Value �� of type java.lang.String cannot be converted to
    // JSONObject 错误
    public static JSONObject parseJsonString(String json) throws JSONException {
        if (TextUtils.isEmpty(json)) {
            return null;
        }
        try {
            return new JSONObject(json);
        } catch (JSONException e) {
            int leftBrace = json.indexOf('{');
            if (leftBrace < 0) {
                throw e;
            }
            int rightBrace = json.lastIndexOf('}');
            if (rightBrace < 0) {
                throw e;
            }
            String newJson = json.substring(leftBrace, rightBrace + 1);
            return new JSONObject(newJson);
        }
    }

    /**
     * 拷贝JSONObject、JSONArray
     *
     * @param object the object
     * @return object object
     */
    public static Object cloneJSONValue(Object object) {
        if (object == null) {
            return null;
        }
        if (object == JSONObject.NULL) {
            return JSONObject.NULL;
        } else if (object instanceof JSONObject) {
            return cloneJSONObject((JSONObject) object);
        } else if (object instanceof JSONArray) {
            return cloneJSONArray((JSONArray) object);
        } else {
            return object;
        }
    }

    /**
     * 深度拷贝数组
     *
     * @param array the array
     * @return json array
     */
    public static JSONArray cloneJSONArray(JSONArray array) {
        if (array == null) {
            return null;
        }
        JSONArray result = new JSONArray();
        for (int i = 0; i < array.length(); i++) {
            try {
                result.put(cloneJSONValue(array.get(i)));
            } catch (JSONException e) {
                e.printStackTrace();
            }
        }
        return result;
    }

    /**
     * 深度拷贝 JSONObject
     *
     * @param json the json
     * @return json object
     */
    public static JSONObject cloneJSONObject(JSONObject json) {
        if (json == null) {
            return null;
        }
        JSONObject result = new JSONObject();
        Iterator keys = json.keys();
        while (keys.hasNext()) {
            String key = (String) keys.next();
            try {
                result.put(key, cloneJSONValue(json.get(key)));
            } catch (JSONException e) {
                e.printStackTrace();
            }
        }
        return result;
    }
}