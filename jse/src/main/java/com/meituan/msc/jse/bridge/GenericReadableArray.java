package com.meituan.msc.jse.bridge;

import android.support.annotation.NonNull;
import android.support.annotation.Nullable;

import java.util.ArrayList;
import java.util.List;

/**
 * 支持ReadableType类型的ReadableArray
 *
 * @param <T>
 */
public class GenericReadableArray<T> implements ReadableArray {
    private final @NonNull List<T> data;

    public GenericReadableArray(@NonNull List<T> data) {
        this.data = data;
    }

    @Override
    public int size() {
        return data.size();
    }

    @Override
    public boolean isNull(int index) {
        if (index < 0 || index >= data.size()) {
            throw new RuntimeException("index is out of bounds");
        }
        return data.get(index) == null;
    }

    @Override
    public boolean getBoolean(int index) {
        checkDataValid(index);
        T value = data.get(index);
        if (value == null) {
            throw new RuntimeException("value is null");
        }
        if (value instanceof Boolean) {
            return (Boolean) value;
        } else {
            throw new RuntimeException("value is not boolean");
        }
    }

    @Override
    public double getDouble(int index) {
        checkDataValid(index);
        T value = data.get(index);
        if (value instanceof Number) {
            return ((Number) value).doubleValue();
        } else {
            throw new RuntimeException("value is not number");
        }
    }

    @Override
    public int getInt(int index) {
        checkDataValid(index);
        T value = data.get(index);
        if (value instanceof Number) {
            return ((Number) value).intValue();
        } else {
            throw new RuntimeException("value is not number");
        }
    }

    @Nullable
    @Override
    public String getString(int index) {
        checkDataValid(index);
        T value = data.get(index);
        if (value instanceof String) {
            return (String) value;
        } else {
            throw new RuntimeException("value is not string");
        }
    }

    @Nullable
    @Override
    public ReadableArray getArray(int index) {
        checkDataValid(index);
        T value = data.get(index);
        if (value instanceof ReadableArray) {
            return (ReadableArray) value;
        } else {
            throw new RuntimeException("value is not array");
        }
    }

    @Nullable
    @Override
    public ReadableMap getMap(int index) {
        checkDataValid(index);
        T value = data.get(index);
        if (value instanceof ReadableMap) {
            return (ReadableMap) value;
        } else {
            throw new RuntimeException("value is not map");
        }
    }

    @NonNull
    @Override
    public Dynamic getDynamic(int index) {
        checkDataValid(index);
        T value = data.get(index);
        if (value instanceof Dynamic) {
            return (Dynamic) value;
        } else {
            throw new RuntimeException("value is not dynamic");
        }
    }

    @NonNull
    @Override
    public ReadableType getType(int index) {
        checkDataValid(index);
        T object = data.get(index);
        if (object == null) {
            return ReadableType.Null;
        } else if (object instanceof Boolean) {
            return ReadableType.Boolean;
        } else if (object instanceof Double || object instanceof Float || object instanceof Integer) {
            return ReadableType.Number;
        } else if (object instanceof String) {
            return ReadableType.String;
        } else if (object instanceof ReadableArray) {
            return ReadableType.Array;
        } else if (object instanceof ReadableMap) {
            return ReadableType.Map;
        }
        return ReadableType.Null;
    }

    @NonNull
    @Override
    public ArrayList<Object> toArrayList() {
        if (data == null) {
            return new ArrayList<>();
        }
        return new ArrayList<>(data);
    }

    public void checkDataValid(int index) {
        if (index < 0 || index >= data.size()) {
            throw new RuntimeException("index is out of bounds");
        }
        if (data.get(index) == null) {
            throw new RuntimeException("value is null");
        }
    }
}
