package com.meituan.msc.jse.bridge;

import android.support.annotation.NonNull;
import android.support.annotation.Nullable;

import java.util.Collections;
import java.util.HashMap;
import java.util.Iterator;
import java.util.List;
import java.util.Map;

public class GenericReadableMap<T extends Map<String, ?>> implements ReadableMap {
    private final T rawMap;

    public GenericReadableMap(T rawMap) {
        this.rawMap = rawMap;
    }

    @Override
    public boolean hasKey(@NonNull String name) {
        return rawMap != null && rawMap.containsKey(name);
    }

    @Override
    public boolean isNull(@NonNull String name) {
        Object value = rawMap.get(name);
        return value == null;
    }

    @Override
    public boolean getBoolean(@NonNull String name) {
        Object value = rawMap.get(name);
        if (value instanceof Boolean) {
            return (Boolean) value;
        }
        return false;
    }

    @Override
    public double getDouble(@NonNull String name) {
        Object value = rawMap.get(name);
        if (value instanceof Number) {
            return ((Number) value).doubleValue();
        }
        return 0;
    }

    @Override
    public int getInt(@NonNull String name) {
        Object value = rawMap.get(name);
        if (value instanceof Number) {
            return ((Number) value).intValue();
        }
        return 0;
    }

    @Nullable
    @Override
    public String getString(@NonNull String name) {
        Object value = rawMap.get(name);
        if (value instanceof String) {
            return (String) value;
        }
        return "";
    }

    @Nullable
    @Override
    public ReadableArray getArray(@NonNull String name) {
        Object value = rawMap.get(name);
        if (value instanceof List) {
            return new GenericReadableArray((List) value);
        }
        return null;
    }

    @Nullable
    @Override
    public ReadableMap getMap(@NonNull String name) {
        Object value = rawMap.get(name);
        if (value instanceof Map) {
            return new GenericReadableMap((Map) value);
        }
        return null;
    }

    @NonNull
    @Override
    public Dynamic getDynamic(@NonNull String name) {
        Object value = rawMap.get(name);
        if (value instanceof Dynamic) {
            return (Dynamic) value;
        }
        return null;
    }

    @NonNull
    @Override
    public ReadableType getType(@NonNull String name) {
        Object object = rawMap.get(name);
        if (object == null) {
            return ReadableType.Null;
        } else if (object instanceof Boolean) {
            return ReadableType.Boolean;
        } else if (object instanceof Double || object instanceof Float || object instanceof Integer) {
            return ReadableType.Number;
        } else if (object instanceof String) {
            return ReadableType.String;
        } else if (object instanceof ReadableArray) {
            return ReadableType.Array;
        } else if (object instanceof ReadableMap) {
            return ReadableType.Map;
        }
        return ReadableType.Null;
    }

    @SuppressWarnings("unchecked")
    @NonNull
    @Override
    public Iterator<Map.Entry<String, Object>> getEntryIterator() {
        if (rawMap == null) {
            return Collections.emptyIterator();
        }
        // 进行安全的类型转换
        return (Iterator<Map.Entry<String, Object>>) (Iterator<?>) rawMap.entrySet().iterator();
    }

    @NonNull
    @Override
    public ReadableMapKeySetIterator keySetIterator() {
        final Iterator<String> iterator = rawMap != null ? rawMap.keySet().iterator() : null;
        return new ReadableMapKeySetIterator() {
            @Override
            public boolean hasNextKey() {
                return iterator != null && iterator.hasNext();
            }

            @Override
            public String nextKey() {
                return iterator == null ? null : iterator.next();
            }
        };
    }

    @NonNull
    @Override
    public HashMap<String, Object> toHashMap() {
        return rawMap == null ? new HashMap<>() : new HashMap<>(rawMap);
    }

    @Override
    public String removeString(@NonNull String name) {
        throw new UnsupportedOperationException("removeString is not supported");
    }

    @Override
    public int size() {
        return 0;
    }
}
