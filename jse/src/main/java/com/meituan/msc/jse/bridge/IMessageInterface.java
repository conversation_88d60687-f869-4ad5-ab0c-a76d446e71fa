package com.meituan.msc.jse.bridge;

import org.json.JSONArray;

public interface IMessageInterface {

    /**
     * JS调用Native桥，同步调用
     */
    Object invokeSync(ICallFunctionContext context, String moduleName, String methodName, JSONArray params);

    /**
     * JS调用Native桥，异步调用
     */
    void invoke(String moduleName, String methodName, JSONArray params);

    /**
     * JS批量调用Native桥，异步调用
     */
    void batchInvoke(String queue);

    /**
     * 获取桥的信息
     */
    JSONArray getConfig(String moduleName);
}
