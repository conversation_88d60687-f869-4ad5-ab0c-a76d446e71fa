package com.meituan.msc.jse.bridge;

import com.facebook.proguard.annotations.DoNotStrip;

import java.io.File;

@DoNotStrip
public class JSCodeCacheCreator {
    static {
        ReactBridge.staticInit();
    }

    private static native void jniCreateCodeCacheFromFile(String fileName, String sourceURL, String codeCacheFilePath);
    private static native void jniCreateCodeCacheFromDioFile(String dioFilePath, String entryFilePath, String sourceURL, String codeCacheFilePath);

    public static boolean createCodeCacheFromDioFile(String dioFilePath, String entryFilePath, String sourceURL, String codeCacheFilePath) {
        File codeCacheFile = new File(codeCacheFilePath);
        codeCacheFile.getParentFile().mkdirs();
        jniCreateCodeCacheFromDioFile(dioFilePath, entryFilePath, sourceURL, codeCacheFilePath);
        return codeCacheFile.exists();
    }

    public static boolean createCodeCacheFromFile(String filePath, String sourceURL, String codeCacheFilePath) {
        File codeCacheFile = new File(codeCacheFilePath);
        codeCacheFile.getParentFile().mkdirs();
        jniCreateCodeCacheFromFile(filePath, sourceURL, codeCacheFilePath);
        return codeCacheFile.exists();
    }
}
