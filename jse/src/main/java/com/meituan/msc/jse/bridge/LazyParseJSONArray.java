package com.meituan.msc.jse.bridge;

import android.os.Build;
import android.support.annotation.RequiresApi;

import org.json.JSONArray;
import org.json.JSONException;
import org.json.JSONObject;

/**
 * 支持懒解析的JSONArray，当需要从json中读取数据的时候才会解析
 * - 主要用于 V8 -> WebView 和 WebView -> V8 之间的桥通信实现不进行json序列化和反序列化的直接传输数据，避免json序列化和反序列化带来的性能损耗
 * - 注意：尽量不要使用该类的put操作，因为该类继承自JSONArray而非实现的接口，如果后面Android系统升级新增了方法，可能会绕过懒解析逻辑
 * - 为什么不使用ReadableArray呢？因为Android常用的json反序列化有org.json和Gson，它们的产物都不是ReadableArray。如果使用ReadableArray还需要包裹一层，且ReadableArray没有remove操作，MSCUIManagerModule中的remove操作不好实现，再且ReadableArray的接口使用体验不如org.json友好。
 */
public class LazyParseJSONArray extends JSONArray {
    private String stringData;
    private boolean isParsed = false;

    public LazyParseJSONArray(String data) {
        this.stringData = data;
    }

    public boolean isParsed() {
        return isParsed;
    }

    public void ensureParsed() {
        if (!isParsed) {
            JSONArray parsedData;
            try {
                if (stringData == null || stringData.length() == 0) {
                    parsedData = new JSONArray();
                } else {
                    parsedData = new JSONArray(stringData);
                }
                for (int i = 0; i < parsedData.length(); i++) {
                    super.put(parsedData.get(i));
                }
            } catch (JSONException e) {
                throw new RuntimeException(e);
            }
            stringData = null;
            isParsed = true;
        }
    }

    @Override
    public String toString() {
        if (!isParsed) {
            return stringData;
        }
        return super.toString();
    }

    @Override
    public String toString(int indentSpaces) throws JSONException {
        ensureParsed();
        return super.toString(indentSpaces);
    }

    @Override
    public int length() {
        ensureParsed();
        return super.length();
    }

    @Override
    public Object get(int index) throws JSONException {
        ensureParsed();
        return super.get(index);
    }

    @Override
    public Object opt(int index) {
        ensureParsed();
        return super.opt(index);
    }

    @RequiresApi(api = Build.VERSION_CODES.KITKAT)
    @Override
    public Object remove(int index) {
        ensureParsed();
        return super.remove(index);
    }

    @Override
    public JSONObject toJSONObject(JSONArray names) throws JSONException {
        ensureParsed();
        return super.toJSONObject(names);
    }

    @Override
    public String join(String separator) throws JSONException {
        ensureParsed();
        return super.join(separator);
    }

    @Override
    public JSONArray put(boolean value) {
        ensureParsed();
        return super.put(value);
    }

    @Override
    public JSONArray put(double value) throws JSONException {
        ensureParsed();
        return super.put(value);
    }

    @Override
    public JSONArray put(int value) {
        ensureParsed();
        return super.put(value);
    }

    @Override
    public JSONArray put(long value) {
        ensureParsed();
        return super.put(value);
    }

    @Override
    public JSONArray put(Object value) {
        ensureParsed();
        return super.put(value);
    }

    @Override
    public JSONArray put(int index, Object value) throws JSONException {
        ensureParsed();
        return super.put(index, value);
    }
}
