package com.meituan.msc.jse.bridge;

import android.support.annotation.Keep;

public interface LoadJSCodeCacheCallback {
    @Keep
    enum LoadStatus {
        /**
         * CodeCache 文件已加载
         * 对应Native层错误码 0
         */
        loaded,
        /**
         * CodeCache 文件不存在
         * 对应Native层错误码 1
         */
        nonexistent,
        /**
         * CodeCache 和 JS 源文件不匹配
         * 对应Native层错误码 2
         */
        invalid,
        /**
         * JS 引擎不支持该功能
         * 对应Native层错误码 3
         */
        unsupported,
    }

    void onLoad(String sourceURL, String jsCodeCachePath, LoadStatus status);
}
