package com.meituan.msc.jse.bridge;

import com.facebook.proguard.annotations.DoNotStrip;
import java.lang.ref.WeakReference;

@DoNotStrip
class LoadJSCodeCacheCallbackJNIWrapper {
    private final LoadJSCodeCacheCallback mCallback;

    LoadJSCodeCacheCallbackJNIWrapper(LoadJSCodeCacheCallback cacheCallback) {
        this.mCallback = cacheCallback;
    }

    @DoNotStrip
    void onLoad(String sourceURL, String jsCodeCachePath, int status) {
        if (mCallback != null) {
            LoadJSCodeCacheCallback.LoadStatus loadStatus;
            LoadJSCodeCacheCallback.LoadStatus[] values = LoadJSCodeCacheCallback.LoadStatus.values();
            if (status >= 0 && status < values.length) {
                loadStatus = values[status];
            } else {
                loadStatus = LoadJSCodeCacheCallback.LoadStatus.unsupported;
            }
            mCallback.onLoad(sourceURL, jsCodeCachePath, loadStatus);
        }
    }
}