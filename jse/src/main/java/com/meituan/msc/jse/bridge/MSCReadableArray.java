/*
 * Copyright (c) Facebook, Inc. and its affiliates.
 *
 * This source code is licensed under the MIT license found in the
 * LICENSE file in the root directory of this source tree.
 */

package com.meituan.msc.jse.bridge;

import android.support.annotation.NonNull;
import android.support.annotation.Nullable;

import com.meituan.msc.jse.common.annotations.DoNotStrip;

import org.json.JSONArray;
import org.json.JSONObject;

import java.util.ArrayList;
import java.util.Arrays;

/**
 * Implementation of a NativeArray that allows read-only access to its members. This will generally
 * be constructed and filled in native code so you shouldn't construct one yourself.
 */
@DoNotStrip
public class MSCReadableArray implements ReadableArray {
    protected JSONArray array;

    public MSCReadableArray() {
        this.array = new JSONArray();
    }

    public MSCReadableArray(JSONArray array) {
        if (array == null) {
            throw new RuntimeException("array should not be null");
        }
        this.array = array;
    }

    // WriteOnce but not in the constructor fields
    private @Nullable
    Object[] mLocalArray;
    private @Nullable
    ReadableType[] mLocalTypeArray;

    private Object[] getLocalArray() {
        if (mLocalArray != null) {
            return mLocalArray;
        }
        synchronized (this) {
            // Make sure no concurrent call already updated
            if (mLocalArray == null) {
                mLocalArray = new Object[array.length()];
                mLocalTypeArray = new ReadableType[array.length()];
                for (int i = 0; i < array.length(); i++) {
                    Object result = array.opt(i);
                    if (result == null || array.isNull(i)) {
                        mLocalArray[i] = null;
                        mLocalTypeArray[i] = ReadableType.Null;
                    } else if (result instanceof String) {
                        mLocalArray[i] = (String) result;
                        mLocalTypeArray[i] = ReadableType.String;
                    } else if (result instanceof Boolean) {
                        mLocalArray[i] = (Boolean) result;
                        mLocalTypeArray[i] = ReadableType.Boolean;
                    } else if (result instanceof Number) {
                        mLocalArray[i] = ((Number) result).doubleValue();
                        mLocalTypeArray[i] = ReadableType.Number;
                    } else if (result instanceof JSONObject) {
                        mLocalArray[i] = new MSCReadableMap((JSONObject) result);
                        mLocalTypeArray[i] = ReadableType.Map;
                    } else if (result instanceof JSONArray) {
                        mLocalArray[i] = new MSCReadableArray((JSONArray) result);
                        mLocalTypeArray[i] = ReadableType.Array;
                    } else {
                        throw new IllegalArgumentException("Could not convert object at index " + i);
                    }
                }
            }
        }
        return mLocalArray;
    }

    private @NonNull
    ReadableType[] getLocalTypeArray() {
        if (mLocalTypeArray != null) {
            return mLocalTypeArray;
        }
        synchronized (this) {
            getLocalArray();
        }
        return mLocalTypeArray;
    }

    @Override
    public int size() {
        return getLocalArray().length;
    }

    @Override
    public boolean isNull(int index) {
        return getLocalArray()[index] == null;
    }

    @Override
    public boolean getBoolean(int index) {
        return ((Boolean) getLocalArray()[index]).booleanValue();
    }

    @Override
    public double getDouble(int index) {
        return ((Double) getLocalArray()[index]).doubleValue();
    }

    @Override
    public int getInt(int index) {
        return ((Double) getLocalArray()[index]).intValue();
    }

    @Override
    public @Nullable
    String getString(int index) {
        return (String) getLocalArray()[index];
    }

    @Override
    public @Nullable
    MSCReadableArray getArray(int index) {
        return (MSCReadableArray) getLocalArray()[index];
    }

    @Override
    public @Nullable
    MSCReadableMap getMap(int index) {
        return (MSCReadableMap) getLocalArray()[index];
    }

    @Override
    public @NonNull
    ReadableType getType(int index) {
        return getLocalTypeArray()[index];
    }

    @Override
    public @NonNull
    Dynamic getDynamic(int index) {
        return DynamicFromArray.create(this, index);
    }

    @Override
    public int hashCode() {
        return getLocalArray().hashCode();
    }

    @Override
    public boolean equals(Object obj) {
        if (!(obj instanceof MSCReadableArray)) {
            return false;
        }
        MSCReadableArray other = (MSCReadableArray) obj;
        return Arrays.deepEquals(getLocalArray(), other.getLocalArray());
    }

    @Override
    public @NonNull
    ArrayList<Object> toArrayList() {
        ArrayList<Object> arrayList = new ArrayList<>();

        for (int i = 0; i < this.size(); i++) {
            switch (getType(i)) {
                case Null:
                    arrayList.add(null);
                    break;
                case Boolean:
                    arrayList.add(getBoolean(i));
                    break;
                case Number:
                    arrayList.add(getDouble(i));
                    break;
                case String:
                    arrayList.add(getString(i));
                    break;
                case Map:
                    arrayList.add(getMap(i).toHashMap());
                    break;
                case Array:
                    arrayList.add(getArray(i).toArrayList());
                    break;
                default:
                    throw new IllegalArgumentException("Could not convert object at index: " + i + ".");
            }
        }
        return arrayList;
    }

    public JSONArray getRealData() {
        return array;
    }

    @Override
    public String toString() {
        return array == null ? "" : array.toString();
    }
}
