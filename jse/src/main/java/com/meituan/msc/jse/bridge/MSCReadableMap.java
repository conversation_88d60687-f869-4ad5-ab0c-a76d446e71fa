/*
 * Copyright (c) Facebook, Inc. and its affiliates.
 *
 * This source code is licensed under the MIT license found in the
 * LICENSE file in the root directory of this source tree.
 */

package com.meituan.msc.jse.bridge;

import android.support.annotation.NonNull;
import android.support.annotation.Nullable;

import com.facebook.infer.annotation.Assertions;
import com.meituan.msc.jse.common.annotations.DoNotStrip;

import org.json.JSONArray;
import org.json.JSONObject;

import java.util.HashMap;
import java.util.Iterator;
import java.util.Map;

/**
 * Implementation of a read-only map in native memory. This will generally be constructed and filled
 * in native code so you shouldn't construct one yourself.
 */
@DoNotStrip
public class MSCReadableMap implements ReadableMap {
    protected JSONObject object;

    public MSCReadableMap() {
        this.object = new JSONObject();
    }

    public MSCReadableMap(JSONObject object) {
        if (object == null) {
            throw new RuntimeException("object should not be null");
        }
        this.object = object;
    }

    private @Nullable
    HashMap<String, Object> mLocalMap;
    private @Nullable
    HashMap<String, ReadableType> mLocalTypeMap;

    private @NonNull
    HashMap<String, Object> getLocalMap() {
        if (mLocalMap != null) {
            return mLocalMap;
        }
        synchronized (this) {
            // check that no other thread has already updated
            if (mLocalMap == null) {
                mLocalMap = new HashMap<>();
                mLocalTypeMap = new HashMap<>();
                Iterator<String> iterator = object.keys();
                while (iterator.hasNext()) {
                    String key = iterator.next();
                    Object result = object.opt(key);
                    if (result == null || object.isNull(key)) {
                        mLocalMap.put(key, null);
                        mLocalTypeMap.put(key, ReadableType.Null);
                    } else if (result instanceof String) {
                        mLocalMap.put(key, (String) result);
                        mLocalTypeMap.put(key, ReadableType.String);
                    } else if (result instanceof Boolean) {
                        mLocalMap.put(key, (Boolean) result);
                        mLocalTypeMap.put(key, ReadableType.Boolean);
                    } else if (result instanceof Number) {
                        mLocalMap.put(key, ((Number) result).doubleValue());
                        mLocalTypeMap.put(key, ReadableType.Number);
                    } else if (result instanceof JSONObject) {
                        mLocalMap.put(key, new MSCReadableMap((JSONObject) result));
                        mLocalTypeMap.put(key, ReadableType.Map);
                    } else if (result instanceof JSONArray) {
                        mLocalMap.put(key, new MSCReadableArray((JSONArray) result));
                        mLocalTypeMap.put(key, ReadableType.Array);
                    } else {
                        throw new IllegalArgumentException("Could not convert object with key: " + key + ".");
                    }
                }
            }
        }
        return mLocalMap;
    }

    private @NonNull
    HashMap<String, ReadableType> getLocalTypeMap() {
        if (mLocalTypeMap != null) {
            return mLocalTypeMap;
        }
        synchronized (this) {
            getLocalMap();
        }
        return mLocalTypeMap;
    }

    @Override
    public boolean hasKey(@NonNull String name) {
        return getLocalMap().containsKey(name);
    }

    @Override
    public boolean isNull(@NonNull String name) {
        if (getLocalMap().containsKey(name)) {
            return getLocalMap().get(name) == null;
        }
        throw new NoSuchKeyException(name);
    }

    private @NonNull
    Object getValue(@NonNull String name) {
        if (hasKey(name) && !(isNull(name))) {
            return Assertions.assertNotNull(getLocalMap().get(name));
        }
        throw new NoSuchKeyException(name);
    }

    private <T> T getValue(String name, Class<T> type) {
        Object value = getValue(name);
        checkInstance(name, value, type);
        return (T) value;
    }

    private @Nullable
    Object getNullableValue(String name) {
        if (hasKey(name)) {
            return getLocalMap().get(name);
        }
        throw new NoSuchKeyException(name);
    }

    private @Nullable
    <T> T getNullableValue(String name, Class<T> type) {
        Object value = getNullableValue(name);
        checkInstance(name, value, type);
        return (T) value;
    }

    private void checkInstance(String name, Object value, Class type) {
        if (value != null && !type.isInstance(value)) {
            throw new UnexpectedNativeTypeException(
                    "Value for "
                            + name
                            + " cannot be cast from "
                            + value.getClass().getSimpleName()
                            + " to "
                            + type.getSimpleName());
        }
    }

    @Override
    public boolean getBoolean(@NonNull String name) {
        return getValue(name, Boolean.class).booleanValue();
    }

    @Override
    public double getDouble(@NonNull String name) {
        return getValue(name, Double.class).doubleValue();
    }

    @Override
    public int getInt(@NonNull String name) {
        // All numbers coming out of native are doubles, so cast here then truncate
        return getValue(name, Double.class).intValue();
    }

    @Override
    public @Nullable
    String getString(@NonNull String name) {
        return getNullableValue(name, String.class);
    }

    @Override
    public @Nullable
    MSCReadableArray getArray(@NonNull String name) {
        return getNullableValue(name, MSCReadableArray.class);
    }

    @Override
    public @Nullable
    MSCReadableMap getMap(@NonNull String name) {
        return getNullableValue(name, MSCReadableMap.class);
    }

    @Override
    public @NonNull
    ReadableType getType(@NonNull String name) {
        if (getLocalTypeMap().containsKey(name)) {
            return Assertions.assertNotNull(getLocalTypeMap().get(name));
        }
        throw new NoSuchKeyException(name);
    }

    @Override
    public @NonNull
    Dynamic getDynamic(@NonNull String name) {
        return DynamicFromMap.create(this, name);
    }

    @Override
    public @NonNull
    Iterator<Map.Entry<String, Object>> getEntryIterator() {
        return getLocalMap().entrySet().iterator();
    }

    @Override
    public @NonNull
    ReadableMapKeySetIterator keySetIterator() {
        return new MSCReadableMapKeySetIterator(this);
    }

    @Override
    public int hashCode() {
        return getLocalMap().hashCode();
    }

    @Override
    public boolean equals(Object obj) {
        if (!(obj instanceof MSCReadableMap)) {
            return false;
        }
        MSCReadableMap other = (MSCReadableMap) obj;
        return getLocalMap().equals(other.getLocalMap());
    }

    @Override
    public @NonNull
    HashMap<String, Object> toHashMap() {
        HashMap<String, Object> hashMap = new HashMap<>(getLocalMap());
        Iterator iterator = hashMap.keySet().iterator();
        while (iterator.hasNext()) {
            String key = (String) iterator.next();
            switch (getType(key)) {
                case Null:
                case Boolean:
                case Number:
                case String:
                    break;
                case Map:
                    hashMap.put(key, Assertions.assertNotNull(getMap(key)).toHashMap());
                    break;
                case Array:
                    hashMap.put(key, Assertions.assertNotNull(getArray(key)).toArrayList());
                    break;
                default:
                    throw new IllegalArgumentException("Could not convert object with key: " + key + ".");
            }
        }
        return hashMap;
    }

    @Override
    public String removeString(@NonNull String name) {
        return (String)(getLocalMap().remove(name));
    }

    private static class MSCReadableMapKeySetIterator implements ReadableMapKeySetIterator {
        private final Iterator<String> mIterator;

        public MSCReadableMapKeySetIterator(MSCReadableMap readableMap) {
            mIterator = readableMap.getLocalMap().keySet().iterator();
        }

        @Override
        public boolean hasNextKey() {
            return mIterator.hasNext();
        }

        @Override
        public String nextKey() {
            return mIterator.next();
        }
    }

    public JSONObject getRealData() {
        return object;
    }

    @Override
    public String toString() {
        return null == object ? "" : object.toString();
    }

    @Override
    public int size() {
        return getLocalMap().size();
    }
}
