package com.meituan.msc.jse.bridge;

import android.support.annotation.Nullable;

import org.json.JSONArray;
import org.json.JSONException;
import org.json.JSONObject;

public class MSCWritableArray extends MSCReadableArray implements WritableArray {

    public MSCWritableArray() {
        super();
    }

    public MSCWritableArray(JSONArray array) {
        super(array);
    }

    @Override
    public void pushNull() {
        array.put(null);
    }

    @Override
    public void pushBoolean(boolean value) {
        array.put(value);
    }

    @Override
    public void pushDouble(double value) {
        try {
            array.put(value);
        } catch (JSONException e) {
            e.printStackTrace();
        }
    }

    @Override
    public void pushInt(int value) {
        array.put(value);
    }

    @Override
    public void pushString(@Nullable String value) {
        array.put(value);
    }

    @Override
    public void pushArray(@Nullable ReadableArray array) {
        if (!(array instanceof MSCReadableArray)) {
            throw new RuntimeException("value must be MSCReadableArray");
        }
        JSONArray realData = ((MSCReadableArray) array).getRealData();
        this.array.put(realData);
    }

    @Override
    public void pushMap(@Nullable ReadableMap map) {
        if (!(map instanceof MSCReadableMap)) {
            throw new RuntimeException("value must be MSCReadableMap");
        }
        JSONObject realData = ((MSCReadableMap) map).getRealData();
        this.array.put(realData);
    }

    @Override
    public void pushArray(@Nullable WritableArray array) {
        if (!(array instanceof MSCWritableArray)) {
            throw new RuntimeException("value must be MSCWritableArray");
        }
        JSONArray realData = ((MSCWritableArray) array).getRealData();
        this.array.put(realData);
    }

    @Override
    public void pushMap(@Nullable WritableMap map) {
        if (!(map instanceof MSCWritableMap)) {
            throw new RuntimeException("value must be MSCWritableMap");
        }
        JSONObject realData = ((MSCWritableMap) map).getRealData();
        this.array.put(realData);
    }
}
