package com.meituan.msc.jse.bridge;

import android.support.annotation.NonNull;
import android.support.annotation.Nullable;

import org.json.JSONException;
import org.json.JSONObject;

import java.util.Iterator;

public class MSCWritableMap extends MSCReadableMap implements WritableMap {
    public MSCWritableMap() {
        super();
    }

    public MSCWritableMap(JSONObject object) {
        super(object);
    }

    @Override
    public void putNull(@NonNull String key) {
        try {
            object.put(key, null);
        } catch (JSONException e) {
            e.printStackTrace();
        }
    }

    @Override
    public void putBoolean(@NonNull String key, boolean value) {
        try {
            object.put(key, value);
        } catch (JSONException e) {
            e.printStackTrace();
        }
    }

    @Override
    public void putDouble(@NonNull String key, double value) {
        try {
            object.put(key, value);
        } catch (JSONException e) {
            e.printStackTrace();
        }
    }

    @Override
    public void putInt(@NonNull String key, int value) {
        try {
            object.put(key, value);
        } catch (JSONException e) {
            e.printStackTrace();
        }
    }

    @Override
    public void putString(@NonNull String key, @Nullable String value) {
        try {
            object.put(key, value);
        } catch (JSONException e) {
            e.printStackTrace();
        }
    }

    @Override
    public void putArray(@NonNull String key, @Nullable ReadableArray value) {
        if (!(value instanceof MSCReadableArray)) {
            throw new RuntimeException("value must be MSCReadableArray");
        }
        try {
            object.put(key, ((MSCReadableArray) value).getRealData());
        } catch (JSONException e) {
            e.printStackTrace();
        }
    }

    @Override
    public void putMap(@NonNull String key, @Nullable ReadableMap value) {
        if (!(value instanceof MSCReadableMap)) {
            throw new RuntimeException("value must be MSCReadableMap");
        }
        try {
            object.put(key, ((MSCReadableMap) value).getRealData());
        } catch (JSONException e) {
            e.printStackTrace();
        }
    }

    @Override
    public void merge(@NonNull ReadableMap source) {
        if (!(source instanceof MSCReadableMap)) {
            throw new RuntimeException("value must be MSCReadableMap");
        }
        JSONObject realData = ((MSCReadableMap) source).object;
        Iterator<String> iterator = realData.keys();
        while (iterator.hasNext()) {
            String key = iterator.next();
            Object value = realData.opt(key);
            try {
                object.put(key, value);
            } catch (JSONException e) {
                e.printStackTrace();
            }
        }
    }

    @Override
    public MSCWritableMap copy() {
        MSCWritableMap target = new MSCWritableMap();
        target.merge(this);
        return target;
    }

    @Override
    public void putArray(@NonNull String key, @Nullable WritableArray value) {
        putArray(key, (MSCReadableArray) value);
    }

    @Override
    public void putMap(@NonNull String key, @Nullable WritableMap value) {
        putMap(key, (MSCReadableMap) value);
    }

    public JSONObject getRealData() {
        return object;
    }

}
