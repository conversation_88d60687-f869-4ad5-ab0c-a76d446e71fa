/*
 * Copyright (c) Facebook, Inc. and its affiliates.
 *
 * This source code is licensed under the MIT license found in the
 * LICENSE file in the root directory of this source tree.
 */

package com.meituan.msc.jse.bridge;

import com.meituan.msc.jse.common.annotations.DoNotStrip;

/** Exception thrown by {@link MSCReadableMap} when a key that does not exist is requested. */
@DoNotStrip
public class NoSuchKeyException extends RuntimeException {

  @DoNotStrip
  public NoSuchKeyException(String msg) {
    super(msg);
  }
}
