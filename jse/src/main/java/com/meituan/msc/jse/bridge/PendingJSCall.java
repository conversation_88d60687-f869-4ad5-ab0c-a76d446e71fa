package com.meituan.msc.jse.bridge;

import android.support.annotation.Nullable;

import org.json.JSONArray;

public class PendingJSCall {

    public final String mModule;
    public final String mMethod;
    @Nullable
    private final JSONArray mArguments;

    public PendingJSCall(String module, String method, @Nullable JSONArray arguments) {
        mModule = module;
        mMethod = method;
        mArguments = arguments;
    }

    public String argumentsString() {
        return mArguments != null ? mArguments.toString() : null;
    }

    @Override
    public String toString() {
        return mModule
                + "."
                + mMethod
                + "("
                + (mArguments == null ? "" : mArguments.toString())
                + ")";
    }
}
