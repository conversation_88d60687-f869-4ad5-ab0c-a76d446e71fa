package com.meituan.msc.jse.bridge;

import java.util.ArrayList;

public class PendingJSCallManager {
    private final String TAG = "PendingJSCallManager@" + Integer.toHexString(hashCode());

    private final ArrayList<PendingJSCall> mJSCallsPendingInit = new ArrayList<>();
    private final Object mJSCallsPendingInitLock = new Object();
    private volatile boolean mAcceptCalls = false;
    private final PendingJSCallExecutor mExecutor;

    public PendingJSCallManager(PendingJSCallExecutor executor) {
        mExecutor = executor;
    }

    public void cacheOrAcceptCall(PendingJSCall function) {
        // if (function.mMethod.equals("onAppRoute")) {
        //            MSCLog.i(TAG, "[MSC_LOG]cacheOrAcceptCall 缓存或执行 mAcceptCalls:", mAcceptCalls, function.mModule,
        //                    function.mMethod, CatalystInstanceImpl.TIME_FORMAT.format(new Date(System.currentTimeMillis())));
        //        }
        if (!mAcceptCalls) {
            // Most of the time the instance is initialized and we don't need to acquire the lock
            synchronized (mJSCallsPendingInitLock) {
                if (!mAcceptCalls) {
                    mJSCallsPendingInit.add(function);
                    return;
                }
            }
        }
        // if (function.mMethod.equals("onAppRoute")) {
        //            MSCLog.i(TAG, "[MSC_LOG]直接执行 mExecutor.execute", function.mModule,
        //                    function.mMethod, CatalystInstanceImpl.TIME_FORMAT.format(new Date(System.currentTimeMillis())));
        //        }
        mExecutor.execute(function);
    }

    public void acceptCalls() {
        synchronized (mJSCallsPendingInitLock) {
            // Loading the bundle is queued on the JS thread, but may not have
            // run yet.  It's safe to set this here, though, since any work it
            // gates will be queued on the JS thread behind the load.
            mAcceptCalls = true;
            for (PendingJSCall function : mJSCallsPendingInit) {
                mExecutor.execute(function);
            }
            mJSCallsPendingInit.clear();
        }
    }

    public boolean isCallsAccepted() {
        return mAcceptCalls;
    }
}
