/*
 * Copyright (c) Facebook, Inc. and its affiliates.
 *
 * This source code is licensed under the MIT license found in the
 * LICENSE file in the root directory of this source tree.
 */

package com.meituan.msc.jse.bridge;

import android.os.SystemClock;

import com.meituan.android.soloader.SoLoader;
import com.meituan.msc.common.utils.MSCTraceUtil;
import com.meituan.msc.modules.reporter.MSCLog;
import com.meituan.msc.systrace.Systrace;
import com.meituan.msc.util.perf.PerfTrace;

import java.util.ArrayList;
import java.util.Arrays;

import static com.meituan.msc.systrace.Systrace.TRACE_TAG_REACT_JAVA_BRIDGE;


public class ReactBridge {
  private static final String TAG = "ReactBridge";

  private static volatile long sLoadStartTime = 0;
  private static volatile long sLoadEndTime = 0;

  private static volatile boolean sDidInit = false;

  public static boolean isInitialized() {
    return sDidInit;
  }

  private static final String[] RN_SO_FILES = new String[]{
          "libc++_shared.so",
          "libfb.so",
          "libfolly_json.so",
          "libglog_init.so",
          "libglog.so",
          "libj2v8.so",
          "libmscexecutor.so",
          "libmtv8.so",
          "libv8.mt.so",
          "libmscjsi.so",
          "libmsc.so",
          "libmscjni.so",
          "libv8.so",
          "libyoga.so",
  };

  public static synchronized void staticInit() {
    if (sDidInit) {
      return;
    }
    sLoadStartTime = SystemClock.uptimeMillis();
    if (MSCTraceUtil.TRACE_LEVEL >= MSCTraceUtil.LEVEL_LIGHT) {
      PerfTrace.begin("load_jse_so_file");
    }
    Systrace.beginSection(
        TRACE_TAG_REACT_JAVA_BRIDGE, "ReactBridge.staticInit::load:reactnativejni");
    ReactMarker.logMarker(ReactMarkerConstants.LOAD_REACT_NATIVE_SO_FILE_START);

    // MRN63 chendacai 没赶上 dynloader-interface 的自动修复，再把兜底修复加回来，jscexecutor 依赖 reactnativejni，所以 reactnativejni 不用加
    // TODO chdc dynloader-interface 会在后面读取so文件中的依赖列表来自动修复依赖，记得后面check下
    SoLoader.loadLibraryWithRelink("mscexecutor", new ArrayList<>(Arrays.asList(RN_SO_FILES)));
    SoLoader.loadLibraryWithRelink("mscjni");
    ReactMarker.logMarker(ReactMarkerConstants.LOAD_REACT_NATIVE_SO_FILE_END);
    Systrace.endSection(TRACE_TAG_REACT_JAVA_BRIDGE);
    if (MSCTraceUtil.TRACE_LEVEL >= MSCTraceUtil.LEVEL_LIGHT) {
      PerfTrace.end("load_jse_so_file");
    }
    sLoadEndTime = SystemClock.uptimeMillis();
    sDidInit = true;
  }

  public static long getLoadStartTime() {
    return sLoadStartTime;
  }

  public static long getLoadEndTime() {
    return sLoadEndTime;
  }
}
