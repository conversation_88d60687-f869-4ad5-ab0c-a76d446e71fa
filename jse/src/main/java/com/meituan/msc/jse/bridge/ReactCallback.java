/*
 * Copyright (c) Facebook, Inc. and its affiliates.
 *
 * This source code is licensed under the MIT license found in the
 * LICENSE file in the root directory of this source tree.
 */

package com.meituan.msc.jse.bridge;

import com.meituan.msc.jse.common.annotations.DoNotStrip;

@DoNotStrip
/* package */ interface ReactCallback {
  @DoNotStrip
  void onBatchComplete();

  @DoNotStrip
  void incrementPendingJSCalls();

  @DoNotStrip
  void decrementPendingJSCalls();

  @DoNotStrip
  String invokeMSCCallback(String functionName, ReadableNativeArray array);

  @DoNotStrip
  String invokeMSCCallback(String objectName, String functionName, ReadableNativeArray array);

  @DoNotStrip
  NativeArray getModuleConfig(String moduleName);
  /**
   * @param queue 处理队列消息，异步调用
   */
  @DoNotStrip
  void callNativeModules(String queue);

  /**
   * 处理同步调用
   * @param moduleName
   * @param methodName
   * @param params
   */
  @DoNotStrip
  NativeArray callSerializableNativeHook(String moduleName, String methodName, String params);
}
