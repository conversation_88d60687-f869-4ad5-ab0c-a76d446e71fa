/*
 * Copyright (c) Facebook, Inc. and its affiliates.
 *
 * This source code is licensed under the MIT license found in the
 * LICENSE file in the root directory of this source tree.
 */

package com.meituan.msc.jse.bridge;

import com.meituan.msc.jse.common.annotations.DoNotStrip;
import com.meituan.msc.modules.reporter.MSCLog;

import java.util.List;
import java.util.concurrent.CopyOnWriteArrayList;

@DoNotStrip
@Deprecated
// TODO *********: unify ReactSoftException with a more generic exception-handling utility
public class ReactSoftException {
  public interface ReactSoftExceptionListener {
    void logSoftException(final String category, final Throwable cause);
  }

  // Use a list instead of a set here because we expect the number of listeners
  // to be very small, and we want listeners to be called in a deterministic
  // order.
  private static final List<ReactSoftExceptionListener> sListeners = new CopyOnWriteArrayList<>();

  @DoNotStrip
  public static void addListener(ReactSoftExceptionListener listener) {
    if (!sListeners.contains(listener)) {
      sListeners.add(listener);
    }
  }

  @DoNotStrip
  public static void removeListener(ReactSoftExceptionListener listener) {
    sListeners.remove(listener);
  }

  @DoNotStrip
  public static void clearListeners() {
    sListeners.clear();
  }

  @DoNotStrip
  public static void logSoftException(final String category, final Throwable cause) {
    if (sListeners.size() > 0) {
      for (ReactSoftExceptionListener listener : sListeners) {
        listener.logSoftException(category, cause);
      }
    } else {
      MSCLog.i(category, "Unhandled SoftException", cause);
    }
  }
}
