/*
 * Copyright (c) Facebook, Inc. and its affiliates.
 *
 * This source code is licensed under the MIT license found in the
 * LICENSE file in the root directory of this source tree.
 */

package com.meituan.msc.jse.bridge;

import android.support.annotation.Nullable;

import com.facebook.infer.annotation.Assertions;
import com.facebook.jni.HybridData;
import com.meituan.msc.jse.common.annotations.DoNotStrip;

/**
 * Implementation of a write-only array stored in native memory. Use {@link RNArguments#createArray()}
 * if you need to stub out creating this class in a test. TODO(5815532): Check if consumed on read
 */
@DoNotStrip
class WritableNativeArray extends ReadableNativeArray implements WritableArray {
  static {
    ReactBridge.staticInit();
  }

  public WritableNativeArray() {
    super(initHybrid());
  }

  @Override
  public native void pushNull();

  @Override
  public native void pushBoolean(boolean value);

  @Override
  public native void pushDouble(double value);

  @Override
  public native void pushInt(int value);

  @Override
  public native void pushString(@Nullable String value);

  // Note: this consumes the map so do not reuse it.
  @Override
  public void pushArray(@Nullable ReadableArray array) {
    Assertions.assertCondition(
        array == null || array instanceof WritableNativeArray, "Illegal type provided");
    pushNativeArray((WritableNativeArray) array);
  }

  // Note: this consumes the map so do not reuse it.
  @Override
  public void pushMap(@Nullable ReadableMap map) {
    Assertions.assertCondition(
        map == null || map instanceof WritableNativeMap, "Illegal type provided");
    pushNativeMap((WritableNativeMap) map);
  }

  // MRN63 chendacai 为了解决运行时找不到接口的问题
  @Override
  public void pushArray(@Nullable WritableArray array) {
    pushArray((ReadableArray) array);
  }

  // MRN63 chendacai 为了解决运行时找不到接口的问题
  @Override
  public void pushMap(@Nullable WritableMap map) {
    pushMap((ReadableMap)map);
  }

  private static native HybridData initHybrid();

  private native void pushNativeArray(WritableNativeArray array);

  private native void pushNativeMap(WritableNativeMap map);
}
