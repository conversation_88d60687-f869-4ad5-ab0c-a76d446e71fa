# Copyright (c) Facebook, Inc. and its affiliates.
#
# This source code is licensed under the MIT license found in the
# LICENSE file in the root directory of this source tree.

LOCAL_PATH := $(call my-dir)

include $(CLEAR_VARS)

LOCAL_MODULE := mscexecutor

LOCAL_SRC_FILES := $(wildcard $(LOCAL_PATH)/*.cpp)

LOCAL_C_INCLUDES := $(LOCAL_PATH)

LOCAL_CFLAGS += -fvisibility=hidden -fexceptions -frtti

ifeq ($(ENABLE_FULL_LTO),false)
  ifeq ($(ENABLE_MSCEXECUTOR_LTO),true)
    LOCAL_CFLAGS += -flto
    LOCAL_LDFLAGS += -flto
  endif
endif

LOCAL_STATIC_LIBRARIES := jsireact
LOCAL_SHARED_LIBRARIES := folly_json fb mscjni mscjsi mtv8jsi mtquickjsi

include $(BUILD_SHARED_LIBRARY)
