/*
 * Copyright (c) Facebook, Inc. and its affiliates.
 *
 * This source code is licensed under the MIT license found in the
 * LICENSE file in the root directory of this source tree.
 */

package com.meituan.msc.jse.jscexecutor;

import com.facebook.jni.HybridData;
import com.meituan.android.soloader.SoLoader;
import com.meituan.msc.jse.bridge.JavaScriptExecutor;
import com.meituan.msc.jse.common.annotations.DoNotStrip;

@DoNotStrip
/* package */ class JSCExecutor extends JavaScriptExecutor {
  static {
    SoLoader.loadLibraryWithRelink("mscexecutor");
  }

  /* package */ JSCExecutor() {
    super(initHybrid());
  }

  private String name;

  JSCExecutor(String name) {
    super(initHybrid());
    this.name = name;
  }

  @Override
  public String getName() {
    return this.name;
  }

  private static native HybridData initHybrid();
}
