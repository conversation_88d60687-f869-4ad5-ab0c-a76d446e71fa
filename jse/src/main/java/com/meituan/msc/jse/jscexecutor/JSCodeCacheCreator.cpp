
#include "JSCodeCacheCreator.h"
#include <msc-jsi/QuickJSRuntimeFactory.h>
#include <msc-jsi/MTV8RuntimeFactory.h>
#include <msc-jsi/jsilib.h>
#include <cxxreact/DioReader.h>
#include <cxxreact/JSBigString.h>
#include <jsireact/JSIExecutor.h>
#include <jni.h>

namespace facebook {
namespace react {

void JSCodeCacheCreator::registerNatives() {
  registerHybrid({
    makeNativeMethod("jniCreateCodeCacheFromFile", JSCodeCacheCreator::jniCreateCodeCacheFromFile),
    makeNativeMethod("jniCreateCodeCacheFromDioFile", JSCodeCacheCreator::jniCreateCodeCacheFromDioFile),
  });
}

void JSCodeCacheCreator::createCodeCacheFromFile(const std::string& fileName, const std::string& sourceURL, const std::string& codeCacheFilePath) {
  if (fileName.empty() || codeCacheFilePath.empty()) {
    return;
  }
  mtv8::createMTV8Runtime() -> createCodeCacheFile(std::make_unique<msc::jsi::FileBuffer>(fileName), sourceURL, codeCacheFilePath);
}

void JSCodeCacheCreator::createCodeCacheFromDioFile(const std::string& dioFilePath, const std::string& entryFilePath, const std::string& sourceURL, const std::string& codeCacheFilePath) {
  if (dioFilePath.empty() || codeCacheFilePath.empty()) {
    return;
  }
  auto bundle = std::make_unique<dio::DioReader>(dioFilePath);
  dio::DioReader::DioFileData data = bundle -> getChildFileData(entryFilePath);
  std::string ss((char*)data.data.get(), data.size);
  std::unique_ptr<const JSBigStdString> script(new JSBigStdString(ss, true));
  mtv8::createMTV8Runtime() -> createCodeCacheFile(std::make_unique<BigStringBuffer>(std::move(script)), sourceURL, codeCacheFilePath);
}

void JSCodeCacheCreator::jniCreateCodeCacheFromFile(jni::alias_ref<jclass>,
    const std::string& fileName, const std::string& sourceURL, const std::string& codeCacheFilePath) {
  JSCodeCacheCreator::createCodeCacheFromFile(fileName, sourceURL, codeCacheFilePath);
}

void JSCodeCacheCreator::jniCreateCodeCacheFromDioFile(jni::alias_ref<jclass>,
    const std::string& dioFilePath, const std::string& entryFilePath, const std::string& sourceURL, const std::string& codeCacheFilePath) {
  JSCodeCacheCreator::createCodeCacheFromDioFile(dioFilePath, entryFilePath, sourceURL, codeCacheFilePath);
}


} // facebook

} // react
