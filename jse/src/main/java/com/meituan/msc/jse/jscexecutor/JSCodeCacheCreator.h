#pragma once

#include <string>
#include <fb/fbjni.h>
#include <msc-jsi/jsi.h>

namespace facebook {
namespace react {

class JSCodeCacheCreator : public jni::HybridClass<JSCodeCacheCreator> {
public:
  constexpr static const char *const kJavaDescriptor =
    "Lcom/meituan/msc/jse/bridge/JSCodeCacheCreator;";

  static void registerNatives();

  static void jniCreateCodeCacheFromFile(jni::alias_ref<jclass>,
    const std::string& fileName, const std::string& sourceURL, const std::string& codeCacheFilePath);

  static void jniCreateCodeCacheFromDioFile(jni::alias_ref<jclass>,
    const std::string& dioFilePath, const std::string& entryFilePath, const std::string& sourceURL, const std::string& codeCacheFilePath);

  static void createCodeCacheFromFile(const std::string& fileName, const std::string& sourceURL, const std::string& codeCacheFilePath);

  static void createCodeCacheFromDioFile(const std::string& dioFilePath, const std::string& entryFilePath, const std::string& sourceURL, const std::string& codeCacheFilePath);

protected:
  friend HybridBase;
};

} // facebook

} // react
