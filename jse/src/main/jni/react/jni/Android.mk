# Copyright (c) Facebook, Inc. and its affiliates.
#
# This source code is licensed under the MIT license found in the
# LICENSE file in the root directory of this source tree.

LOCAL_PATH := $(call my-dir)

include $(CLEAR_VARS)

# Include . in the header search path for all source files in this module.
LOCAL_C_INCLUDES := $(LOCAL_PATH)

# Include ./../../ in the header search path for modules that depend on
# reactnativejni. This will allow external modules to require this module's
# headers using #include <react/jni/<header>.h>, assuming:
#   .     == jni
#   ./../ == react
LOCAL_EXPORT_C_INCLUDES := $(LOCAL_PATH)/../..

LOCAL_CFLAGS += -fexceptions -frtti -Wno-unused-lambda-capture

LOCAL_LDLIBS += -landroid

ifeq ($(ENABLE_FULL_LTO),false)
  ifeq ($(ENABLE_MSCJNI_LTO),true)
      LOCAL_CFLAGS += -flto
      LOCAL_LDFLAGS += -flto
  endif
endif

# The dynamic libraries (.so files) that this module depends on.
LOCAL_SHARED_LIBRARIES := folly_json fb glog_init msc glog

ifeq ($(ENABLE_MSCJSI_SHARED),true)
	LOCAL_SHARED_LIBRARIES += mscjsi
else
	LOCAL_STATIC_LIBRARIES += mscjsi
endif

# Name of this module.
#
# Other modules can depend on this one by adding libreactnativejni to their
# LOCAL_SHARED_LIBRARIES variable.
LOCAL_MODULE := mscjni

# Compile all local c++ files.
LOCAL_SRC_FILES := $(wildcard *.cpp)

ifeq ($(APP_OPTIM),debug)
  # Keep symbols by overriding the strip command invoked by ndk-build.
  # Note that this will apply to all shared libraries,
  # i.e. shared libraries will NOT be stripped
  # even though we override it in this Android.mk
  cmd-strip :=
endif

# Build the files in this directory as a shared library
include $(BUILD_SHARED_LIBRARY)

# Compile the c++ dependencies required for ReactAndroid
#
# How does the import-module function work?
#   For each $(call import-module,<module-dir>), you search the directories in
#   NDK_MODULE_PATH. (This variable is defined in Application.mk). If you find a
#   <module-dir>/Android.mk you in a directory <dir>, you run:
#   include <dir>/<module-dir>/Android.mk
#
# What does it mean to include an Android.mk file?
#   Whenever you encounter an include <dir>/<module-dir>/Android.mk, you
#   tell andorid-ndk to compile the module in <dir>/<module-dir> according
#   to the specification inside <dir>/<module-dir>/Android.mk.
$(call import-module,folly)
$(call import-module,fb)
# $(call import-module,fbjni)
# $(call import-module,jsc)
$(call import-module,fbgloginit)
# MRN60 luojiani yoga 拆库
# $(call import-module,yogajni)
$(call import-module,cxxreact)
$(call import-module,mscjsi)
$(call import-module,jsiexecutor)
# $(call import-module,mrnjsiexecutor)
# TODO chdc 通过变量配置编译哪个JS引擎
# $(call import-module,hermes)

# TODO(ramanpreet):
#   Why doesn't this import-module call generate a jscexecutor.so file?
# $(call import-module,mscexecutor)
# $(call import-module,mrnjscexecutor)

# TODO chdc 通过变量配置编译哪个JS引擎
include $(REACT_SRC_DIR)/jscexecutor/Android.mk
# include $(REACT_SRC_DIR)/mrnjscexecutor/Android.mk
# include $(REACT_SRC_DIR)/../hermes/reactexecutor/Android.mk
# include $(REACT_SRC_DIR)/../hermes/instrumentation/Android.mk
# include $(REACT_SRC_DIR)/modules/blob/jni/Android.mk
