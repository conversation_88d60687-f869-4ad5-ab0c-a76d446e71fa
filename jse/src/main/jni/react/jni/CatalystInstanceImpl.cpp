/*
 * Copyright (c) Facebook, Inc. and its affiliates.
 *
 * This source code is licensed under the MIT license found in the
 * LICENSE file in the root directory of this source tree.
 */

#include "CatalystInstanceImpl.h"

#include <condition_variable>
#include <memory>
#include <mutex>
#include <sstream>
#include <vector>
#include <fstream>

#include <cxxreact/Instance.h>
#include <cxxreact/JSBigString.h>
#include <cxxreact/DioReader.h>
#include <cxxreact/RecoverableError.h>
#include <fb/log.h>
// MRN63 chendacai 将 fbjni 和 fb 合并
#include <fb/fbjni/ByteBuffer.h>
#include <folly/dynamic.h>
#include <ReadableNativeArray.h>
#include <glog/logging.h>

#include "JNativeRunnable.h"
#include "JavaScriptExecutorHolder.h"
#include "NativeArray.h"
#include <folly/json.h>
#include <jsiexecutor/jsireact/JSIExecutor.h>
#include <cxxreact/JSIDynamic.h>
#include <glog/logging.h>

using namespace facebook::jni;

namespace facebook {
namespace react {

class CatalystInstanceImpl::MSCModuleProxy : public msc::jsi::HostObject {
 public:
  MSCModuleProxy(alias_ref<ReactCallback::javaobject> jobj, std::string name):
    _jobj(jobj),
    _name(std::move(name)){}

  std::string invokeCallback(jstring functionName, ReadableNativeArray::javaobject params) {
    static auto method = ReactCallback::javaClassStatic()->getMethod<jstring(jstring, jstring, ReadableNativeArray::javaobject)>("invokeMSCCallback");
    return method(_jobj, facebook::jni::make_jstring(_name).get(), functionName, params)->toStdString();
  }

  msc::jsi::Value get( msc::jsi::Runtime &runtime_, const msc::jsi::PropNameID &name) override {
    auto functionName = name.utf8(runtime_);
    return msc::jsi::Function::createFromHostFunction(
                runtime_,
                msc::jsi::PropNameID::forAscii(runtime_, functionName),
                1,
                [this, functionName](
                    msc::jsi::Runtime &runtime,
                    const msc::jsi::Value &thisValue,
                    const msc::jsi::Value *arguments,
                    size_t count) -> msc::jsi::Value {
                        folly::dynamic result = folly::dynamic::array();
                        for (unsigned int argIndex = 0; argIndex < count; argIndex += 1) {
                            const msc::jsi::Value *arg = &arguments[argIndex];
                            if(arg->isNumber()){
                                result.push_back(arg->getNumber());
                            } else if(arg->isBool()){
                                result.push_back(arg->getBool());
                            } else if(arg->isString()){
                                result.push_back(arg->getString(runtime).utf8(runtime));
                            } else if(arg->isObject()){
                                folly::dynamic dynamicFromValue = msc::jsi::dynamicFromValue(runtime, *arg);
                                result.push_back(dynamicFromValue);
                            }
                        }
                        auto jParams = ReadableNativeArray::newObjectCxxArgs(std::move(result));
                        auto resultValue = invokeCallback(facebook::jni::make_jstring(functionName).get(), jParams.get());
                        if (resultValue.empty()){
                            return msc::jsi::Value::null();
                        }
                        return msc::jsi::String::createFromAscii(runtime, resultValue);
                     });
  }

  private:
     alias_ref<ReactCallback::javaobject> _jobj;
     std::string _name;
};

namespace {

class Exception : public jni::JavaClass<Exception> {
 public:
};

class JInstanceCallback : public InstanceCallback {
 public:
  explicit JInstanceCallback(
      alias_ref<ReactCallback::javaobject> jobj)
      : jobj_(make_global(jobj)) {}

  void onBatchComplete() override {
      jni::ThreadScope guard;
      static auto method = ReactCallback::javaClassStatic()->getMethod<void()>(
          "onBatchComplete");
      method(jobj_);
  }

  void incrementPendingJSCalls() override {
    // For C++ modules, this can be called from an arbitrary thread
    // managed by the module, via callJSCallback or callJSFunction.  So,
    // we ensure that it is registered with the JVM.
    jni::ThreadScope guard;
    static auto method = ReactCallback::javaClassStatic()->getMethod<void()>(
        "incrementPendingJSCalls");
    method(jobj_);
  }

  void decrementPendingJSCalls() override {
    jni::ThreadScope guard;
    static auto method = ReactCallback::javaClassStatic()->getMethod<void()>(
        "decrementPendingJSCalls");
    method(jobj_);
  }

  void callNativeModules(std::string &&queue) override {
    jni::ThreadScope guard;
    //auto queueParams = ReadableNativeArray::newObjectCxxArgs(std::move(queue));
    static auto method = ReactCallback::javaClassStatic()->getMethod<void(jstring)>(
        "callNativeModules");
    method(jobj_, facebook::jni::make_jstring(queue).get());
  }

  folly::dynamic callSerializableNativeHook(std::string &&moduleName, std::string &&methodName, std::string &&params) override {
    jni::ThreadScope guard;
    //auto queueParams = ReadableNativeArray::newObjectCxxArgs(std::move(params));
    static auto method = ReactCallback::javaClassStatic()->getMethod<NativeArray::javaobject(jstring, jstring, jstring)>(
        "callSerializableNativeHook");
    auto result = method(jobj_, facebook::jni::make_jstring(moduleName).get(), facebook::jni::make_jstring(methodName).get(), facebook::jni::make_jstring(params).get());
   // jni::local_ref<NativeArray::javaobject> result = method(jobj_, std::move(moduleName), std::move(methodName), queueParams.get());
    //if(!result){
    //    return folly::none;
    //}
    return result->cthis()->consume();
  }

  folly::Optional<ModuleConfig> getConfig(const std::string &name) override {
      jni::ThreadScope guard;
      static auto method = ReactCallback::javaClassStatic()->getMethod<NativeArray::javaobject(jstring)>("getModuleConfig");
      jni::local_ref<NativeArray::javaobject> configNativeArray = method(jobj_, facebook::jni::make_jstring(name).get());
      if (!configNativeArray) {
         return folly::none;
      }
      auto config = configNativeArray->cthis()->consume();
      return ModuleConfig{std::move(name), folly::dynamic{config}};
  }

 private:
  global_ref<ReactCallback::javaobject> jobj_;
  std::shared_ptr<JMessageQueueThread> messageQueueThread_;
};

} // namespace

jni::local_ref<CatalystInstanceImpl::jhybriddata>
CatalystInstanceImpl::initHybrid(jni::alias_ref<jclass>) {
  return makeCxxInstance();
}

CatalystInstanceImpl::CatalystInstanceImpl()
    : instance_(std::make_unique<Instance>()) {}

CatalystInstanceImpl::~CatalystInstanceImpl() {
  if (moduleMessageQueue_ != NULL) {
    moduleMessageQueue_->quitSynchronous();
  }
}

void CatalystInstanceImpl::registerNatives() {
  registerHybrid({
      makeNativeMethod("initHybrid", CatalystInstanceImpl::initHybrid),
      makeNativeMethod("jniGetJSRuntimePtr", CatalystInstanceImpl::jniGetJSRuntimePtr),
      makeNativeMethod(
          "initializeBridge", CatalystInstanceImpl::initializeBridge),
      makeNativeMethod(
          "jniSetSourceURL", CatalystInstanceImpl::jniSetSourceURL),
      makeNativeMethod(
          "jniLoadScriptFromString",
          CatalystInstanceImpl::jniLoadScriptFromString),
      makeNativeMethod(
          "jniEvaluateJavaScript", 
          CatalystInstanceImpl::jniEvaluateJavaScript),
      makeNativeMethod(
          "jniSetGlobalVariableSync", 
          CatalystInstanceImpl::jniSetGlobalVariableSync),
      makeNativeMethod(
          "jniChangeV8InspectorName",
          CatalystInstanceImpl::jniChangeV8InspectorName),
      makeNativeMethod(
          "jniLoadScriptFromFile", CatalystInstanceImpl::jniLoadScriptFromFile),
      // [MRN60: chendacai] DIO 适配，详见：https://km.sankuai.com/page/349814196
      makeNativeMethod("jniLoadScriptFromDioFile", CatalystInstanceImpl::jniLoadScriptFromDioFile),
      makeNativeMethod(
          "jniCallJSFunction", CatalystInstanceImpl::jniCallJSFunction),
      makeNativeMethod(
          "jniCallJSCallback", CatalystInstanceImpl::jniCallJSCallback),
      makeNativeMethod(
          "jniCallJSCallbackWithDynamic", CatalystInstanceImpl::jniCallJSCallbackWithDynamic),
      makeNativeMethod(
          "setGlobalVariable", CatalystInstanceImpl::setGlobalVariable),
      makeNativeMethod(
          "setGlobalVariableString", CatalystInstanceImpl::setGlobalVariableString),
      makeNativeMethod(
          "getJavaScriptContext", CatalystInstanceImpl::getJavaScriptContext),
      makeNativeMethod(
          "jniHandleMemoryPressure",
          CatalystInstanceImpl::handleMemoryPressure),
      makeNativeMethod("jniJSIGarbageCollect", CatalystInstanceImpl::jniJSIGarbageCollect),
      makeNativeMethod("jniGetJSIMemoryUsage", CatalystInstanceImpl::jniGetJSIMemoryUsage),
      makeNativeMethod("jniJSIStartCPUProfiling", CatalystInstanceImpl::jniJSIStartCPUProfiling),
      makeNativeMethod("jniJSIStopCPUProfiling", CatalystInstanceImpl::jniJSIStopCPUProfiling),
      makeNativeMethod("registerMethod", CatalystInstanceImpl::registerMethod),
      makeNativeMethod("registerJSObject", CatalystInstanceImpl::registerJSObject),
      makeNativeMethod("jniExecuteJSFunction", CatalystInstanceImpl::jniExecuteJSFunction),
      makeNativeMethod("jniExecuteJSModule", CatalystInstanceImpl::jniExecuteJSModule),
      makeNativeMethod("jniExecuteListFunction", CatalystInstanceImpl::jniExecuteListFunction),

      makeNativeMethod("jniStartTimeTracer", CatalystInstanceImpl::jniStartTimeTracer),
      makeNativeMethod("jniStopTimeTracer", CatalystInstanceImpl::jniStopTimeTracer),
      makeNativeMethod("jniGetSpendTimeFromTimeTracer", CatalystInstanceImpl::jniGetSpendTimeFromTimeTracer),
      makeNativeMethod("jniClearTimeTracer", CatalystInstanceImpl::jniClearTimeTracer),
    });

  JNativeRunnable::registerNatives();
}

void CatalystInstanceImpl::initializeBridge(
    jni::alias_ref<ReactCallback::javaobject> callback,
    // This executor is actually a factory holder.
    JavaScriptExecutorHolder *jseh,
    jni::alias_ref<JavaMessageQueueThread::javaobject> jsQueue,
    std::string name,
    bool useQuickJS) {
   callbackobj_ = make_global(callback);
  // TODO mhorowitz: how to assert here?
  // Assertions.assertCondition(mBridge == null, "initializeBridge should be
  // called once");

  // This used to be:
  //
  // Java CatalystInstanceImpl -> C++ CatalystInstanceImpl -> Bridge ->
  // Bridge::Callback
  // --weak--> ReactCallback -> Java CatalystInstanceImpl
  //
  // Now the weak ref is a global ref.  So breaking the loop depends on
  // CatalystInstanceImpl#destroy() calling mHybridData.resetNative(), which
  // should cause all the C++ pointers to be cleaned up (except C++
  // CatalystInstanceImpl might be kept alive for a short time by running
  // callbacks). This also means that all native calls need to be pre-checked
  // to avoid NPE.

  // See the comment in callJSFunction.  Once js calls switch to strings, we
  // don't need jsModuleDescriptions any more, all the way up and down the
  // stack.

  instance_->initializeBridge(
      std::make_unique<JInstanceCallback>(callback),
      jseh->getExecutorFactory(),
      std::make_unique<JMessageQueueThread>(jsQueue),
      name,
      useQuickJS);
}

jlong CatalystInstanceImpl::jniGetJSRuntimePtr() {
    msc::jsi::Runtime* jsiRuntime = (msc::jsi::Runtime*)instance_->getJavaScriptContext();
    if (jsiRuntime != nullptr) {
        // 将指针转换为 jlong，并确保在 64 位系统下安全
        jlong result = reinterpret_cast<jlong>(jsiRuntime);
        return result;
    }

    // 返回 0 表示错误或无效的运行时指针
    return -1;
}

void CatalystInstanceImpl::jniSetSourceURL(const std::string &sourceURL) {
  instance_->setSourceURL(sourceURL);
}

// [MRN63：leipengchao] MRN精简版引擎，详见：https://km.sankuai.com/page/516768147
void CatalystInstanceImpl::jniLoadScriptFromString(const std::string &scriptString, const std::string &sourceURL, bool loadSynchronously) {
    std::unique_ptr<const JSBigStdString> script(new JSBigStdString(scriptString, true));
    instance_->loadScriptFromString(
        std::move(script), sourceURL, loadSynchronously);
}
// [MRN63: leipengchao] MRN无引擎渲染表达式，由于没有异步队列，所以同步设置变量，详见：
void CatalystInstanceImpl::jniSetGlobalVariableSync(
    const std::string propName,
    const std::string &&jsonValue) {
  instance_->setGlobalVariableSync(
      std::move(propName),
      std::make_unique<JSBigStdString>(std::move(jsonValue)));
}

void CatalystInstanceImpl::jniChangeV8InspectorName(
    const std::string name) {
   msc::jsi::Runtime* jsiRuntime = ( msc::jsi::Runtime*)instance_->getJavaScriptContext();
     if (jsiRuntime != nullptr) {
       jsiRuntime->changeV8InspectorName(name);
     }
}

// [MRN63: leipengchao] MRN无引擎渲染表达式支持，通过V8直接计算表达式，返回string
std::string CatalystInstanceImpl::jniEvaluateJavaScript(
    const std::string &scriptString,
    const std::string &sourceURL,
    const std::string& jsCodeCachePath,
    jni::alias_ref<LoadJSCodeCacheCallbackJNIWrapper::javaobject> loadJSCodeCacheCallback) {

  // TODO CodeCache: 删除全局对象的引用
  msc::jsi::LoadJSCodeCacheCallback callback = loadJSCodeCacheCallback ? [loadJSCodeCacheCallback=jni::make_global(loadJSCodeCacheCallback)]
          (const std::string& sourceURL, const std::string& jsCodeCachePath, int status) {
      if (loadJSCodeCacheCallback) {
          static auto method = LoadJSCodeCacheCallbackJNIWrapper::javaClassStatic()->getMethod<void(jstring, jstring, jint)>("onLoad");
          method(loadJSCodeCacheCallback, facebook::jni::make_jstring(sourceURL).get(), facebook::jni::make_jstring(jsCodeCachePath).get(), status);
      }
  } : msc::jsi::LoadJSCodeCacheCallback();

  msc::jsi::Runtime *jsiRuntime = ( msc::jsi::Runtime *)instance_->getJavaScriptContext();
  std::unique_ptr<const JSBigStdString> script(
      new JSBigStdString(scriptString, true));
  folly::dynamic result = folly::dynamic::object;

  if (jsiRuntime != nullptr) {
    // std::string src = "function __tickleJs() { return 1+2; }";
    auto rawResult = jsiRuntime->evaluateJavaScriptWithCodeCache(
        std::make_unique<BigStringBuffer>(std::move(script)), sourceURL, jsCodeCachePath, callback);

    if (rawResult.isString()) {
      auto s = rawResult.getString(*jsiRuntime);
      result["result"] = s.utf8(*jsiRuntime);
      result["type"] = "string";
    } else if (rawResult.isBool()) {
      auto b = rawResult.getBool();
      result["result"] = b;
      result["type"] = "boolean";
    } else if (rawResult.isObject()) {
      auto dynamicResult = msc::jsi::dynamicFromValue(*jsiRuntime, rawResult);
      result["result"] = folly::toJson(dynamicResult);
      result["type"] = "object";
    } else if (rawResult.isNull()) {
      result["result"] = NULL;
      result["type"] = "null";
    } else if (rawResult.isUndefined()){ 
      result["result"] = NULL;
      result["type"] = "undefined";
    } else if (rawResult.isNumber()) {
      auto bb = rawResult.getNumber();
      //判断是否是NaN类型
      if (bb != bb) {
       result["result"] = NULL;
      } else {
        result["result"] = bb;
      }
      result["type"] = "double";
    }
    // result["result"] = folly::toJson(dynamicResult);
    // result["type"] = dynamicResult.typeName();
    return folly::toJson(result);
  }
  result["result"] = "evaluate js error: jsiRuntime is nullptr";
  result["type"] = "error";
  return folly::toJson(result);
}

std::string CatalystInstanceImpl::jniExecuteJSFunction(const std::string &name, const std::string &params){
    msc::jsi::Runtime* runtime_ = ( msc::jsi::Runtime*)instance_->getJavaScriptContext();
    msc::jsi::Value paramValue = runtime_->global().getPropertyAsObject(*runtime_, "JSON")
        .getPropertyAsFunction(*runtime_, "parse").call(*runtime_, params);
    msc::jsi::Value excValue = runtime_->global()
            .getPropertyAsFunction(*runtime_, name.c_str())
            .call(*runtime_, paramValue);
    if (excValue.isString()) {
        auto s = excValue.getString(*runtime_);
        return s.utf8(*runtime_);
    }
    return "";
}

std::string CatalystInstanceImpl::jniExecuteJSModule(const std::string &moduleName, const std::string &methodName, const std::string &params){
    msc::jsi::Runtime* runtime_ = ( msc::jsi::Runtime*)instance_->getJavaScriptContext();
    msc::jsi::Value paramValue = runtime_->global().getPropertyAsObject(*runtime_, "JSON")
        .getPropertyAsFunction(*runtime_, "parse").call(*runtime_, params);
    msc::jsi::Object obj = paramValue.getObject(*runtime_);
    if(obj.isArray(*runtime_)){
        msc::jsi::Array array = obj.getArray(*runtime_);
        msc::jsi::Value excValue = runtime_->global()
            .getPropertyAsObject(*runtime_, moduleName.c_str())
            .getPropertyAsFunction(*runtime_, methodName.c_str())
            .call(*runtime_, array.getValueAtIndex(*runtime_, 0),  array.getValueAtIndex(*runtime_, 1), array.getValueAtIndex(*runtime_, 2));
        if (excValue.isString()) {
            auto s = excValue.getString(*runtime_);
            return s.utf8(*runtime_);
        }
    }
    return "";
}

std::string CatalystInstanceImpl::jniExecuteListFunction(const std::string &moduleName, const std::string &methodName, const std::string &jsModuleName, const std::string &jsMethodName, const std::string &params){
    msc::jsi::Runtime* runtime_ = ( msc::jsi::Runtime*)instance_->getJavaScriptContext();
    msc::jsi::Value excValue = runtime_->global()
        .getPropertyAsObject(*runtime_, moduleName.c_str())
        .getPropertyAsFunction(*runtime_, methodName.c_str())
        .call(*runtime_, jsModuleName.c_str(), jsMethodName.c_str(), params.c_str());
    if (excValue.isString()) {
        auto s = excValue.getString(*runtime_);
        return s.utf8(*runtime_);
    }
    return "";
}


void CatalystInstanceImpl::jniLoadScriptFromFile(
    const std::string &fileName,
    const std::string &sourceURL,
    bool loadSynchronously) {
    std::unique_ptr<const JSBigFileString> script;
    RecoverableError::runRethrowingAsRecoverable<std::system_error>(
        [&fileName, &script]() {
          script = JSBigFileString::fromPath(fileName);
        });
    instance_->loadScriptFromString(
        std::move(script), sourceURL, loadSynchronously);
}

// [MRN60: chendacai] DIO 适配，详见：https://km.sankuai.com/page/349814196
void CatalystInstanceImpl::jniLoadScriptFromDioFile(const std::string& dioFilePath,
                                                 const std::string& entryFilePath,
                                                 const std::string& sourceURL,
                                                 bool loadSynchronously) {
    auto bundle = std::make_unique<dio::DioReader>(dioFilePath);
    dio::DioReader::DioFileData data = bundle -> getChildFileData(entryFilePath);
    std::string ss((char*)data.data.get(), data.size);
    std::unique_ptr<const JSBigStdString> script(new JSBigStdString(ss, true));
    instance_->loadScriptFromString(std::move(script), sourceURL, loadSynchronously);
}

void CatalystInstanceImpl::jniCallJSFunction(
    std::string module,
    std::string method,
    std::string &&arguments) {
    // LOG(ERROR) << "[MSC_LOG]CatalystInstanceImpl.cpp jniCallJSFunction:"
    //               << module << "." << method;
  // We want to share the C++ code, and on iOS, modules pass module/method
  // names as strings all the way through to JS, and there's no way to do
  // string -> id mapping on the objc side.  So on Android, we convert the
  // number to a string, here which gets passed as-is to JS.  There, they they
  // used as ids if isFinite(), which handles this case, and looked up as
  // strings otherwise.  Eventually, we'll probably want to modify the stack
  // from the JS proxy through here to use strings, too.
  instance_->callJSFunction(
      std::move(module), std::move(method), std::move(arguments));
}

// [MRN60: chendacai] 主动GC，详见：https://km.sankuai.com/page/504934851
void CatalystInstanceImpl::jniJSIGarbageCollect() {
  msc::jsi::Runtime* jsiRuntime = ( msc::jsi::Runtime*)instance_->getJavaScriptContext();
  if (jsiRuntime != nullptr) {
    jsiRuntime->garbageCollect();
  }
}

// [MRN60: chendacai] 主动GC，详见：https://km.sankuai.com/page/504934851
jlong CatalystInstanceImpl::jniGetJSIMemoryUsage() {
  msc::jsi::Runtime* jsiRuntime = ( msc::jsi::Runtime*)instance_->getJavaScriptContext();
  if (jsiRuntime != nullptr) {
    return (jlong)jsiRuntime->getMemoryUsage();
  }
  return -1;
}

void CatalystInstanceImpl::jniJSIStartCPUProfiling(const std::string &profilerName, int interval) {
    // LOG(ERROR) << "[MSC_LOG]CatalystInstanceImpl.cpp jniJSIStartCPUProfiling begin, profilerName:"
    //               << profilerName.c_str();
  msc::jsi::Runtime* jsiRuntime = ( msc::jsi::Runtime*)instance_->getJavaScriptContext();
  if (jsiRuntime != nullptr) {
    jsiRuntime->startCPUProfiling(profilerName, interval);
  }
  // LOG(ERROR) << "[MSC_LOG]CatalystInstanceImpl.cpp jniJSIStartCPUProfiling end, profilerName:"
    //               << profilerName.c_str();
}

void CatalystInstanceImpl::jniJSIStopCPUProfiling(const std::string &profilerName, const std::string &traceFilePath) {
    // LOG(ERROR) << "[MSC_LOG]CatalystInstanceImpl.cpp jniJSIStopCPUProfiling begin, profilerName:"
    //               << profilerName.c_str() << ", traceFilePath:" << traceFilePath.c_str() ;
  msc::jsi::Runtime* jsiRuntime = ( msc::jsi::Runtime*)instance_->getJavaScriptContext();
  if (jsiRuntime != nullptr) {
    std::ofstream ofs;
    ofs.open(traceFilePath.c_str());
    jsiRuntime->stopCPUProfiling(profilerName, ofs);
    ofs.close();
  }
  // LOG(ERROR) << "[MSC_LOG]CatalystInstanceImpl.cpp jniJSIStopCPUProfiling end, profilerName:"
    //               << profilerName.c_str() << ", traceFilePath:" << traceFilePath.c_str() ;
}

void CatalystInstanceImpl::registerMethod(std::string functionName){
   msc::jsi::Runtime* runtime_ = ( msc::jsi::Runtime*)instance_->getJavaScriptContext();
   msc::jsi::Function fn = msc::jsi::Function::createFromHostFunction(
                            *runtime_,
                            msc::jsi::PropNameID::forAscii(*runtime_, functionName),
                            1,
                            [this, functionName](
                                msc::jsi::Runtime &runtime,
                                const msc::jsi::Value &thisValue,
                                const msc::jsi::Value *arguments,
                                size_t count) -> msc::jsi::Value {
                                     folly::dynamic result = folly::dynamic::array();
                                     for (unsigned int argIndex = 0; argIndex < count; argIndex += 1) {
                                         const msc::jsi::Value *arg = &arguments[argIndex];
                                         if(arg->isNumber()){
                                             result.push_back(arg->getNumber());
                                         } else if(arg->isBool()){
                                             result.push_back(arg->getBool());
                                         } else if(arg->isString()){
                                             result.push_back(arg->getString(runtime).utf8(runtime));
                                         } else if(arg->isObject()){
                                            folly::dynamic dynamicFromValue = msc::jsi::dynamicFromValue(runtime, *arg);
                                            result.push_back(dynamicFromValue);
                                         }
                                     }
                                auto jParams = ReadableNativeArray::newObjectCxxArgs(std::move(result));
                                auto resultValue = invokeCallback(facebook::jni::make_jstring(functionName).get(), jParams.get());
                                if (resultValue.empty()){
                                    return msc::jsi::Value::null();
                                }
                                return msc::jsi::String::createFromAscii(runtime, resultValue);
                           });
            runtime_->global().setProperty(
                       *runtime_,
                       functionName.c_str(),
                       fn);
}

void CatalystInstanceImpl::registerJSObject(std::string name){
   msc::jsi::Runtime* runtime_ = ( msc::jsi::Runtime*)instance_->getJavaScriptContext();
   auto module = std::make_shared<MSCModuleProxy>(callbackobj_, name);
   auto object = msc::jsi::Object::createFromHostObject(*runtime_, module);
   runtime_->global().setProperty(*runtime_, name.c_str(), std::move(object));
}

std::string CatalystInstanceImpl::invokeCallback(jstring functionName, ReadableNativeArray::javaobject params) {
    static auto method = ReactCallback::javaClassStatic()->getMethod<jstring(jstring, ReadableNativeArray::javaobject)>("invokeMSCCallback");
    return method(callbackobj_, functionName, params)->toStdString();
}

void CatalystInstanceImpl::jniCallJSCallback(
    jint callbackId,
    std::string &&arguments) {
    instance_->callJSCallback(callbackId, std::move(arguments));
}

void CatalystInstanceImpl::jniCallJSCallbackWithDynamic(
    jint callbackId,
    NativeArray *arguments) {
    instance_->callJSCallbackWithDynamic(callbackId, arguments->consume());
}

void CatalystInstanceImpl::setGlobalVariableString(
    std::string propName,
    std::string &&jsonValue) {
  // This is only ever called from Java with short strings, and only
  // for testing, so no need to try hard for zero-copy here.
  msc::jsi::Runtime *runtime_ = ( msc::jsi::Runtime *)instance_->getJavaScriptContext();
  runtime_->global().setProperty(
        *runtime_,
        propName.c_str(),
        jsonValue.c_str());
}

void CatalystInstanceImpl::setGlobalVariable(
    std::string propName,
    std::string &&jsonValue) {
  // This is only ever called from Java with short strings, and only
  // for testing, so no need to try hard for zero-copy here.
  instance_->setGlobalVariable(
      std::move(propName),
      std::make_unique<JSBigStdString>(std::move(jsonValue)));
}

jlong CatalystInstanceImpl::getJavaScriptContext() {
  return (jlong)(intptr_t)instance_->getJavaScriptContext();
}

void CatalystInstanceImpl::handleMemoryPressure(int pressureLevel) {
  instance_->handleMemoryPressure(pressureLevel);
}

void CatalystInstanceImpl::jniStartTimeTracer() {
    msc::jsi::Runtime* jsiRuntime = (msc::jsi::Runtime*)instance_->getJavaScriptContext();
    if (jsiRuntime != nullptr) {
        jsiRuntime->StartTimeTracer();
    }
}

void CatalystInstanceImpl::jniStopTimeTracer() {
    msc::jsi::Runtime* jsiRuntime = (msc::jsi::Runtime*)instance_->getJavaScriptContext();
    if (jsiRuntime != nullptr) {
        jsiRuntime->StopTimeTracer();
    }
}

std::string CatalystInstanceImpl::jniGetSpendTimeFromTimeTracer(int64_t startTimeInUnixTime, int64_t endTimeInUnixTime) {
    msc::jsi::Runtime* jsiRuntime = (msc::jsi::Runtime*)instance_->getJavaScriptContext();
    if (jsiRuntime != nullptr) {
        return jsiRuntime->GetSpendTimeFromTimeTracer(startTimeInUnixTime, endTimeInUnixTime);
    } else {
        return "";
    }
}

void CatalystInstanceImpl::jniClearTimeTracer() {
    msc::jsi::Runtime* jsiRuntime = (msc::jsi::Runtime*)instance_->getJavaScriptContext();
    if (jsiRuntime != nullptr) {
        jsiRuntime->ClearTimeTracer();
    }
}


} // namespace react
} // namespace facebook
