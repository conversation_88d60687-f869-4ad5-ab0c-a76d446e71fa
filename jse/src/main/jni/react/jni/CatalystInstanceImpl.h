/*
 * Copyright (c) Facebook, Inc. and its affiliates.
 *
 * This source code is licensed under the MIT license found in the
 * LICENSE file in the root directory of this source tree.
 */

#include <memory>
#include <string>

// MRN63 chendacai 将 fbjni 和 fb 合并
#include <fb/fbjni.h>

#include "JMessageQueueThread.h"
#include <ReadableNativeArray.h>

namespace facebook {
namespace react {

class Instance;
class JavaScriptExecutorHolder;
class NativeArray;

struct ReactCallback : public jni::JavaClass<ReactCallback> {
  static constexpr auto kJavaDescriptor =
      "Lcom/meituan/msc/jse/bridge/ReactCallback;";
};

struct LoadJSCodeCacheCallbackJNIWrapper : public jni::JavaClass<LoadJSCodeCacheCallbackJNIWrapper> {
    static constexpr auto kJavaDescriptor = "Lcom/meituan/msc/jse/bridge/LoadJSCodeCacheCallbackJNIWrapper;";
};

class CatalystInstanceImpl : public jni::HybridClass<CatalystInstanceImpl> {
 public:
  static constexpr auto kJavaDescriptor =
      "Lcom/meituan/msc/jse/bridge/CatalystInstanceImpl;";

  static jni::local_ref<jhybriddata> initHybrid(jni::alias_ref<jclass>);
  ~CatalystInstanceImpl() override;

  static void registerNatives();

  std::shared_ptr<Instance> getInstance() {
    return instance_;
  }
  std::string invokeCallback(jstring functionName, ReadableNativeArray::javaobject params);

 private:
  friend HybridBase;

  class MSCModuleProxy;

  global_ref<ReactCallback::javaobject> callbackobj_;

  CatalystInstanceImpl();

  void initializeBridge(
      jni::alias_ref<ReactCallback::javaobject> callback,
      // This executor is actually a factory holder.
      JavaScriptExecutorHolder *jseh,
      jni::alias_ref<JavaMessageQueueThread::javaobject> jsQueue,
      std::string name,
      bool useQuickJS);

  jlong jniGetJSRuntimePtr();

  /**
   * Sets the source URL of the underlying bridge without loading any JS code.
   */
  void jniSetSourceURL(const std::string &sourceURL);

// [MRN63：leipengchao] MRN精简版引擎，详见：https://km.sankuai.com/page/516768147
  void jniLoadScriptFromString(
        const std::string &scriptString,
        const std::string &assetURL,
        bool loadSynchronously);
  // [MRN63: leipengchao] 无引擎渲染表达式支持，计算表达式，返回结果     
    std::string jniEvaluateJavaScript(
        const std::string &scriptString,
        const std::string &sourceURL,
        const std::string& jsCodeCachePath,
        jni::alias_ref<LoadJSCodeCacheCallbackJNIWrapper::javaobject> loadJSCodeCacheCallback);
  // [MRN63: leipengchao] 无引擎渲染表达式支持，同步设置global变量     
  void jniSetGlobalVariableSync(
      const std::string propName,
      const std::string &&jsonValue);
  void jniLoadScriptFromFile(
      const std::string &fileName,
      const std::string &sourceURL,
      bool loadSynchronously);
  // [MRN60: chendacai] DIO 适配，详见：https://km.sankuai.com/page/349814196
  void jniLoadScriptFromDioFile(const std::string& dioFilePath, const std::string& entryFilePath, const std::string& sourceURL, bool loadSynchronously);

  // [MRN60: chendacai] 主动GC，详见：https://km.sankuai.com/page/504934851
  void jniJSIGarbageCollect();
  jlong jniGetJSIMemoryUsage();

  void jniJSIStartCPUProfiling(const std::string &profilerName, int interval);
  void jniJSIStopCPUProfiling(const std::string &profilerName, const std::string &traceFilePath);

 void jniChangeV8InspectorName(const std::string name);


  void jniCallJSFunction(
      std::string module,
      std::string method,
      std::string &&arguments);
  void jniCallJSCallback(jint callbackId, std::string &&arguments);
  void jniCallJSCallbackWithDynamic(jint callbackId, NativeArray *arguments);
  void setGlobalVariable(std::string propName, std::string &&jsonValue);
  void setGlobalVariableString(std::string propName, std::string &&jsonValue);
  void registerMethod(std::string functionName);
  void registerJSObject(std::string name);
  jlong getJavaScriptContext();
  void handleMemoryPressure(int pressureLevel);

  // This should be the only long-lived strong reference, but every C++ class
  // will have a weak reference.
  std::shared_ptr<Instance> instance_;
  std::shared_ptr<JMessageQueueThread> moduleMessageQueue_;
  std::string jniExecuteJSFunction(const std::string &name, const std::string &params);
  std::string jniExecuteJSModule(const std::string &moduleName, const std::string &methodName, const std::string &params);
  std::string jniExecuteListFunction(const std::string &moduleName, const std::string &methodName, const std::string &jsModuleName, const std::string &jsMethodName, const std::string &params);

  void jniStartTimeTracer();
  void jniStopTimeTracer();
  std::string jniGetSpendTimeFromTimeTracer(int64_t startTimeInUnixTime, int64_t endTimeInUnixTime);
  void jniClearTimeTracer();
};

} // namespace react
} // namespace facebook
