# Copyright (c) Meituan, Inc. and its affiliates.
#
# This source code is licensed under the MIT license found in the
# LICENSE file in the root directory of this source tree.

LOCAL_PATH := $(call my-dir)

# libmsc_jsi.so - 预编译的共享库
include $(CLEAR_VARS)
LOCAL_MODULE := mscjsi
LOCAL_SRC_FILES := $(THIRD_PARTY_NDK_DIR)/mscjsi/jni/$(TARGET_ARCH_ABI)/libmsc_jsi.so
LOCAL_EXPORT_C_INCLUDES := $(THIRD_PARTY_NDK_DIR)/mscjsi/headers
include $(PREBUILT_SHARED_LIBRARY)

# libmtquickjsi.so - 预编译的共享库
include $(CLEAR_VARS)
LOCAL_MODULE := mtquickjsi
LOCAL_SRC_FILES := $(THIRD_PARTY_NDK_DIR)/mscjsi/jni/$(TARGET_ARCH_ABI)/libmtquickjsi.so
LOCAL_EXPORT_C_INCLUDES := $(THIRD_PARTY_NDK_DIR)/mscjsi/headers
include $(PREBUILT_SHARED_LIBRARY)

# libmtv8jsi.so - 预编译的共享库
include $(CLEAR_VARS)
LOCAL_MODULE := mtv8jsi
LOCAL_SRC_FILES :=$(THIRD_PARTY_NDK_DIR)/mscjsi/jni/$(TARGET_ARCH_ABI)/libmtv8jsi.so
LOCAL_EXPORT_C_INCLUDES := $(THIRD_PARTY_NDK_DIR)/mscjsi/headers
include $(PREBUILT_SHARED_LIBRARY)