//
// Created by 苏世睿 on 2025/2/6.
//

#ifndef MTV8_JS_TIME_TRACER_H
#define MTV8_JS_TIME_TRACER_H

#include <stdint.h>
#include <vector>
#include <algorithm>
#include <stack>

enum CategoryFlag {
    kReadCodeCacheFlag,
    kLoadCodeCacheFlag,
    kCompileFlag,
    kExecuteFlag,
    kJSFunctionCallFlag,
    kNativeFunctionCallFlag,
    kCreateCachedDataFlag,
    kCategoryFlagCount
};

struct TimeSegment {
    int64_t start_time;
    int64_t end_time;
    CategoryFlag category_flag;
};

class JsTimeTracer {
public:
/**
 * 开始收集性能埋点
 */
    void Start();
/**
 * 停止收集性能埋点，但不清理埋点数据
 */
    void Stop();

/**
 * 获取指定时间段内指定埋点类型的耗时，可能会在stop后调用多次以获取不同类型的埋点；
 * @param category 指定想获取耗时的埋点类型，它是调用start时传入参数的子集，如果该值指定的类型没有在start时收集，则返回-1
 * @param startTimeInUnixTime 开始时间，单位为ms
 * @param endTimeInUnixTime 结束时间，单位为ms
 * @return 指定时间段内指定埋点类型的耗时，单位为ms
 */
    std::string GetSpendTime(int64_t startTimeInUnixTime, int64_t endTimeInUnixTime);

/**
 * 清除所有埋点数据
 */
    void Clear();

private:
    std::vector<std::shared_ptr<TimeSegment>> time_segments_;
    bool is_running_ = false;
friend class TimeTracerScope;
};

class TimeTracerScope{
public:
    TimeTracerScope(std::shared_ptr<JsTimeTracer> time_tracer, CategoryFlag category_flag);
    ~TimeTracerScope();

private:
    std::shared_ptr<JsTimeTracer> time_tracer_;
    CategoryFlag category_flag_;
    std::shared_ptr<TimeSegment> time_segment_;
};

#endif //MTV8_JS_TIME_TRACER_H
