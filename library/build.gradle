
boolean local_project = false

if (local_project) {
    apply plugin: 'com.android.library'
} else {
    apply from: '../gradle_msc_common.gradle'
}

android {
    compileSdkVersion project.compileSdkVersion
    buildToolsVersion project.buildToolsVersion


    defaultConfig {
        minSdkVersion project.minSdkVersion
        targetSdkVersion project.targetSdkVersion

        versionCode 1
        versionName "1.0"

        // Specifies the ABI configurations of your native
        // libraries Grad<PERSON> should build and package with your APK.
        ndk {
            abiFilters 'x86', 'x86_64', 'armeabi-v7a', 'arm64-v8a'
        }

        buildConfigField "String", "AAR_VERSION", "\"" + VERSION_NAME + "\""
        buildConfigField "String", "MSC_SDK_VERSION", "\"" + MSC_SDK_VERSION + "\""
    }

    packagingOptions {
        doNotStrip '**mips*/*.so'
        pickFirst '**/**.so'
        exclude 'META-INF/DEPENDENCIES'
    }

    buildTypes {
        release {
//            minifyEnabled true
//            proguardFiles getDefaultProguardFile('proguard-android.txt'), 'proguard-rules.pro'
            consumerProguardFile 'proguard-rules-with-attr.pro'
        }
    }

    lintOptions {
        abortOnError false
    }
    compileOptions {
        sourceCompatibility JavaVersion.VERSION_1_8
        targetCompatibility JavaVersion.VERSION_1_8
    }
}

configurations {
    all*.exclude group: 'com.meituan.android.mrn', module: 'react-native'
    all*.exclude group: 'com.meituan.android.mrn', module: 'imageloader'
    all*.exclude group: 'com.meituan.android.mrn', module: 'mrn'
    all*.exclude group: 'com.meituan.metrics', module: 'metrics'
    all*.exclude group: 'com.meituan.android.sniffer'
    all*.exclude group: 'com.meituan.android.common', module: 'babel'
    all*.exclude group: 'com.android.support', module: 'coordinatorlayout'
    all*.exclude group: 'com.squareup.okhttp', module: 'okhttp'
    all*.exclude group: 'net.sf.kxml', module: 'kxml2'
    all*.exclude group: 'com.sankuai.meituan.kernel', module: 'image'
    // 下线netsingleton & netmodule & net-impl
    all*.exclude group: 'com.sankuai.meituan.pylon', module: 'netsingleton'
    all*.exclude group: 'com.sankuai.meituan.pylon', module: 'netmodule'
    /*解决Cannot find a version of 'com.sankuai.meituan.pylon:model' that satisfies the version constraints:*/
    all*.exclude group: 'com.sankuai.meituan.pylon', module: 'model'
    all*.exclude group: 'com.meituan.android.favorite', module: 'library'
    all*.exclude group: 'com.sankuai.meituan.kernel', module: 'net-impl'
    all*.exclude group: 'com.sankuai.android.jarvis', module: 'core'
    all*.exclude group: 'com.android.support', module: 'design'
    all*.exclude group: 'com.meituan.android.homepage', module: 'interface'
    all*.exclude group: 'com.meituan.itc', module: 'mnnlibrary'
}

dependencies {
    if (local_project) {
        String local_base_version = project.ext.get("MSC_LIBRARY_VERSION")
        implementation("com.meituan.msc:jse:${local_base_version}")
        implementation("com.meituan.android.msc:interface:${local_base_version}")
        implementation("com.meituan.android.msc:common-interface:${local_base_version}")
        implementation("com.meituan.android.msc:msc-util:${local_base_version}")
        implementation("com.meituan.android.msc:tech-stack-interface:${local_base_version}")
        implementation "com.meituan.android.cipstoragemetrics:library:1.12.31"
    } else {
        api project(':interface')
        api project(':common-interface')
        api project(':tech-stack-interface')
        implementation project(path: ':jse')
//    implementation project(path: ':render')
        compile project(path: ':msc-util')
    }
    api("com.meituan.android.msi:library:${project.msiVersion}"){
        exclude group: 'com.sankuai.meituan.serviceloader', module: 'annotation'
        exclude group: 'com.meituan.android.knb', module: 'titans-adapter-base'
        exclude group: 'com.sankuai.titans.submodule', module: 'step-core'
        exclude group: 'com.sankuai.meituan.skyeye', module: 'library'
        exclude group: 'com.meituan.android.edfu', module: 'mbar'
    }
    // compile project(':msi-library')

//    implementation project(path: ':imageloader')

    implementation('com.sankuai.meituan.multiprocess:library:0.0.28')

//    implementation project(path: ':debug')

    //noinspection GradleCompatible
//    compile 'com.android.support:design:26.0.2'
    //noinspection GradleDependency
//    compile 'com.android.support:appcompat-v7:26.0.2'

    api 'android.arch.lifecycle:common:1.1.1'
    api 'android.arch.lifecycle:runtime:1.1.1'

    //noinspection GradleDependency
    implementation 'com.squareup.picasso:picasso:10000.**********'
    //noinspection GradleDependency
    implementation 'com.google.code.gson:gson:2.8.2'

    // 路由
    implementation 'com.sankuai.meituan.arbiter:arbiter:********'

    // 网络
    api('com.sankuai.meituan.kernel:net-msi:0.1.27')
    api 'com.sankuai.meituan.kernel:net:3.0.5'
    api 'com.sankuai.meituan.retrofit2:retrofit-mt:1.10.20'
    api 'com.sankuai.meituan.retrofit2:mock:********'
    api 'com.sankuai.meituan.retrofit2:callfactory-okhttp3:1.10.2'
    api 'com.meituan.android.httpdns:httpdns-okhttp3:1.3.37'
    api 'com.meituan.android.msi:msi-network:0.0.18'
    api('com.meituan.android.risk:mtretrofit:1.0.0.40') {
        exclude group: '*'
    }
    // ddd 预置包获取. 1.3.53 dio下载染色
    api("com.meituan.met.mercury:load:1.3.62") {
        force = true
    }

    // dio
    api("com.meituan.dio:dio:0.1.7")
    implementation("com.meituan.dio:picassohelper:0.1.2")

    // 包下载
    compileOnly('com.meituan.android.downloadmanager:library:********')
    // 灵犀
    api('com.meituan.android.common.analyse:library:********')
    // logan
    api 'com.dianping.android.sdk:networklog:*******.3'
    // logan private api
    api('com.dianping.android.sdk:networklog-mmp:1.0.2')

    api 'com.meituan.android.common:shortcut:0.0.6'

    // https://km.sankuai.com/page/997366289
    api('com.meituan.android.common:utils:3.0.15')

    // 存储
    api 'com.meituan.android.cipstorage:library:*********-embed'

    // SnackBar 用于替换toast
    api 'com.sankuai.meituan.pylon:snackbar:0.0.28'

    // mtWebVieww
    implementation "com.meituan.android.mtwebkit:library:$MT_WEBVIEW_VERSION"
    implementation 'com.sankuai.meituan.pylon:basemodule:3.0.39'

    api 'com.sankuai.meituan.serviceloader:serviceloader:2.2.33'
    api 'com.sankuai.meituan.serviceloader:annotation:2.2.30'
    annotationProcessor('com.sankuai.meituan.serviceloader:processor:2.2.27')
    implementation('com.meituan.android.common:horn:0.3.55')
    implementation('com.meituan.android.degrade:interface:***********') {
        exclude group: 'com.meituan.android.common', module: 'unionid'
        exclude group: 'com.meituan.android.aurora', module: 'core'
        exclude group: 'com.meituan.android.msi', module: 'library'
    }

    // ddd中间层 https://km.sankuai.com/page/1277485411
    implementation "com.meituan.met.mercury:msc-adaptor:${DD_MSC_ADAPTER_VERSION}"


    // 隐私组件接入文档 https://km.sankuai.com/page/1215866077
    implementation "com.meituan.android.privacy:interface:${project.privacySdkVersion}"

    implementation "com.meituan.android.common.metricx:babel:${project.babelVersion}"
    // crashreporter、滚动fps
    implementation "com.meituan.android.common.metricx:metricx:${project.metricxVersion}"

    api "com.meituan.android.msi:msi-api-metrics:1.0.609"

    // 秒开2.0接入
    implementation "com.meituan.android.common.ffp:interfaces:${FFP_VERSION}"
    // 秒开的WebView侧的指标上报桥接口定义组件，用于在秒开上报时携带MSC的自定义维度值
    implementation('com.meituan.android.msi.extends:metrics-base-adapter:0.0.4') {
        exclude group: '*'
    }
    implementation "com.meituan.android.common.metricx:interfaces:12.39.200"

    implementation "com.meituan.shared.resource:library:0.0.22"

    implementation "com.airbnb.android:lottie:2.6.1"

}
