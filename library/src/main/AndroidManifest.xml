<?xml version="1.0" encoding="utf-8"?>
<manifest xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:tools="http://schemas.android.com/tools"
    package="com.meituan.msc.lib">
    <uses-sdk tools:overrideLibrary="com.sankuai.meituan.mtlive,com.meituan.android.loader" />
    <!--允许获得精确的GPS定位-->
    <uses-permission android:name="android.permission.ACCESS_FINE_LOCATION" />
    <!--允许获得粗略的基站网络定位-->
    <uses-permission android:name="android.permission.ACCESS_COARSE_LOCATION" />
    <uses-permission android:name="android.permission.INTERNET" />
    <uses-permission android:name="android.permission.ACCESS_NETWORK_STATE" />
    <uses-permission android:name="android.permission.ACCESS_WIFI_STATE" />
    <uses-permission android:name="android.permission.CHANGE_WIFI_STATE" />
    <uses-permission android:name="android.permission.READ_EXTERNAL_STORAGE" />
    <uses-permission android:name="android.permission.WRITE_EXTERNAL_STORAGE" />
    <uses-permission android:name="android.permission.RECORD_AUDIO" />
    <uses-permission android:name="android.permission.BLUETOOTH" />
    <uses-permission android:name="android.permission.BLUETOOTH_ADMIN" />
    <uses-permission android:name="android.permission.CAMERA" />
    <uses-permission android:name="android.permission.VIBRATE" />

    <uses-feature android:name="android.hardware.bluetooth_le" />

    <uses-permission android:name="android.permission.READ_PHONE_STATE" />
    <!--    直播悬浮窗-->
    <uses-permission android:name="android.permission.SYSTEM_ALERT_WINDOW" />

    <application
        android:supportsRtl="true">
<!-- todo target升级到30后放开 -->
        <!--        <queries>-->
        <!--            <package android:name="com.google.android.webview" />-->
        <!--            <package android:name="com.android.settings" />-->
        <!--            <package android:name="com.miui.securitycenter" />-->
        <!--            <package android:name="com.oppo.safe" />-->
        <!--            <package android:name="com.iqoo.secure" />-->
        <!--            <package android:name="com.color.safecenter" />-->
        <!--            <package android:name="com.coloros.safecenter" />-->
        <!--            <package android:name="com.smartisanos.security" />-->
        <!--        </queries>-->

        <service
            android:name="com.meituan.msc.common.remote.RemoteService"
            android:exported="false" />
        <!--        主进程默认MSC     -->
        <activity
            android:name="com.meituan.msc.modules.container.MSCActivity"
            android:configChanges="orientation|screenSize|smallestScreenSize|screenLayout"
            android:hardwareAccelerated="true"
            android:maxAspectRatio="2.4"
            android:screenOrientation="portrait"
            android:theme="@style/MSCTheme"
            android:windowSoftInputMode="adjustNothing">

            <!--
        TODO 目前未对屏幕尺寸变化及旋转作充分支持，可以通过Activity重建来临时解决，但因为视频全屏时会导致横屏，此方法不可行
        微信有此类接口 https://developers.weixin.qq.com/miniprogram/dev/framework/view/resizable.html
            -->

            <intent-filter>
                <action android:name="android.intent.action.VIEW" />

                <category android:name="android.intent.category.DEFAULT" />
                <category android:name="android.intent.category.BROWSABLE" />

                <data
                    android:host="www.meituan.com"
                    android:path="/msc/"
                    android:scheme="msc" />
                <data
                    android:host="www.meituan.com"
                    android:path="/msc"
                    android:scheme="msc" />
            </intent-filter>
        <!-- TODO 目前未对屏幕尺寸变化及旋转作充分支持，可以通过Activity重建来临时解决，但因为视频全屏时会导致横屏，此方法不可行
        微信有此类接口 https://developers.weixin.qq.com/miniprogram/dev/framework/view/resizable.html -->
        </activity>

        <!--        独立进程默认MSC 容器-->
        <activity
            android:name="com.meituan.msc.modules.container.MSCTransparentActivity"
            android:configChanges="orientation|screenSize|smallestScreenSize|screenLayout"
            android:hardwareAccelerated="true"
            android:maxAspectRatio="2.4"
            android:screenOrientation="portrait"
            android:theme="@style/MSCTransparentTheme"
            android:windowSoftInputMode="adjustNothing" />

        <activity
            android:name="com.meituan.msc.common.process.MSCActivity0"
            android:configChanges="orientation|screenSize|smallestScreenSize|screenLayout"
            android:exported="false"
            android:hardwareAccelerated="true"
            android:launchMode="standard"
            android:maxAspectRatio="2.4"
            android:process=":mscMiniApp0"
            android:screenOrientation="portrait"
            android:theme="@style/MSCTheme"
            android:windowSoftInputMode="adjustNothing" />

        <provider
            android:name="com.meituan.msc.common.process.ipc.provider.MSCCoreProvider"
            android:authorities="${applicationId}.mscMiniApp"
            android:enabled="true"
            android:exported="false" />

        <provider
            android:name="com.meituan.msc.common.process.ipc.provider.MSC0Provider"
            android:authorities="${applicationId}.mscMiniApp0"
            android:enabled="true"
            android:exported="false"
            android:process=":mscMiniApp0" />
    </application>

</manifest>