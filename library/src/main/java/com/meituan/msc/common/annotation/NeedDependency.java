package com.meituan.msc.common.annotation;

import android.support.annotation.Keep;

import java.lang.annotation.ElementType;
import java.lang.annotation.Retention;
import java.lang.annotation.RetentionPolicy;
import java.lang.annotation.Target;

/**
 * 通过此注解描述本类所依赖的其他类
 * 有时hera中会提供一些默认实现，但并不强制引入其所需的依赖，因此在使用这些默认实现前需要选任意依赖中的类进行检查，以确定依赖是否被宿主引入
 * 注意这些默认实现类本身在被引用甚至初始化时并不会抛异常，而是在执行到使用依赖时发生异常，不事先检查会难以控制
 */
@Keep
@Target(ElementType.TYPE)
@Retention(RetentionPolicy.RUNTIME)
public @interface NeedDependency {
    /** @param 任意需要被检查的类 */
    Class<?>[] value();
}
