package com.meituan.msc.common.ensure;

import android.support.annotation.NonNull;
import android.text.TextUtils;

import com.meituan.msc.modules.update.pkg.MSCLoadPackageScene;
import com.meituan.msc.modules.engine.MSCHornRollbackConfig;
import com.meituan.msc.modules.engine.ResultCallback;
import com.meituan.msc.modules.page.render.webview.WebViewFileFilter;
import com.meituan.msc.modules.reporter.MSCLog;
import com.meituan.msc.modules.update.MSCAppModule;
import com.meituan.msc.modules.update.PackageDownloadCallback;
import com.meituan.msc.modules.update.bean.PackageInfoWrapper;

import java.io.File;
import java.util.List;
import java.util.concurrent.CountDownLatch;
import java.util.concurrent.atomic.AtomicInteger;

/**
 * dio文件保证。
 */
public class ResFilesEnsure {
    private static final String TAG = "ResFilesEnsure";
    private final MSCAppModule mAppModule;

    public ResFilesEnsure(@NonNull MSCAppModule module) {
        mAppModule = module;
    }

    /**
     * 保证文件都存在。page层。
     *
     * @param prefix
     * @param fileUris
     * @throws WebViewFileFilter.IllegalPathAccessException
     */
    public void ensure(boolean enableAsyncSubPkg, String prefix, String[] fileUris) throws WebViewFileFilter.IllegalPathAccessException {
        if (MSCHornRollbackConfig.readConfig().rollbackInjectAdvanceBuildConfig) {
            MSCLog.i(TAG,"injectMetaInfoConfig rollback");
            if (!MSCHornRollbackConfig.isEnableEnsureDioFile()) {
                return;
            }
        } else {
            if (!enableAsyncSubPkg) {
                return;
            }
        }
        InputOptions input = new InputOptions();
        final boolean needPrefix = !TextUtils.isEmpty(prefix);
        // 过滤下路径。同步WebViewFileFilter里对路径的处理。
        if (needPrefix) {
            String[] ret = new String[fileUris.length];
            int index = 0;
            for (String file : fileUris) {
                file = file.trim();
                if (file.startsWith("/")) {
                    file = file.substring(1);
                }
                ret[index++] = prefix + file;
            }
            input.fileUris = ret;
        } else {
            input.fileUris = fileUris;
        }
        OutputResult result = new OutputResult(null);
        ensure(true, input, result);
        if (!TextUtils.isEmpty(result.loadResult)) {
            MSCLog.e(TAG, "ensure result fail. => " + result.loadResult);
            throw new WebViewFileFilter.IllegalPathAccessException(result.loadResult);
        }
    }

    /**
     * 保证文件都存在。service层。
     *
     * @param inputOptions
     * @param result
     */
    public void ensure(boolean enableAsyncSubPkg, @NonNull InputOptions inputOptions, @NonNull OutputResult result) {
        if (MSCHornRollbackConfig.readConfig().rollbackInjectAdvanceBuildConfig) {
            MSCLog.i(TAG,"injectMetaInfoConfig rollback");
            if (!MSCHornRollbackConfig.isEnableEnsureDioFile()) {
                return;
            }
        } else {
            if (!enableAsyncSubPkg) {
                return;
            }
        }
        final String[] fileUris = inputOptions.fileUris;
        // 这里没有直接使用文件数量的count，方便随时返回。
        final CountDownLatch launch = new CountDownLatch(1);
        final AtomicInteger count = new AtomicInteger(fileUris.length);
        for (final String fileUri : fileUris) {
            // 对齐iOS，空文件过滤掉，不报错。
            if (TextUtils.isEmpty(fileUri)) {
                count.decrementAndGet();
                checkNotify(launch, count);
                continue;
            }
            // 读取dio文件信息
            PackageInfoWrapper pkgInfo = mAppModule.getPackageInfoByUrl(fileUri, false);
            if (pkgInfo == null) {
                // 路径非法访问。
                result.loadResult = "path is not illegal.";
                count.set(-1);
                checkNotify(launch, count);
                return;
            }
            final String localPath = pkgInfo.getLocalPath();
            if (TextUtils.isEmpty(localPath) || !new File(localPath).exists()) {
                // 文件不存在。需要下载。
                requestDioFile(pkgInfo, new ResultCallback() {
                    @Override
                    public void onReceiveValue(String value) {
                        count.decrementAndGet();
                        checkNotify(launch, count);
                    }

                    @Override
                    public void onReceiveFailValue(Exception e) {
                        result.loadResult = e.getMessage();
                        count.set(-1);
                        checkNotify(launch, count);
                    }
                });
            } else {
                // 文件已存在
                count.decrementAndGet();
                checkNotify(launch, count);
            }
            MSCLog.d("AppService", "importScripts: ", fileUri, " -> ", pkgInfo);
        }
        try {
            launch.await();
        } catch (InterruptedException e) {
            // 忽略。
            MSCLog.i(TAG, e.getMessage());
        }
    }

    private void checkNotify(@NonNull CountDownLatch latch, @NonNull AtomicInteger count) {
        if (count.get() <= 0 && latch.getCount() > 0) {
            // 数据都返回 && 还在await。
            latch.countDown();
        }
    }

    private void requestDioFile(@NonNull PackageInfoWrapper infoWrapper, @NonNull ResultCallback callback) {
        if (null == mAppModule.getRuntime() || null == mAppModule.getRuntime().getPageLoadModule()) {
            callback.onReceiveFailValue(new Exception("runtime is not suitable."));
            return;
        }
        mAppModule.getRuntime().getPageLoadModule().downloadSubPackage(infoWrapper, MSCLoadPackageScene.LOAD_PACKAGE_TYPE_LAUNCH, new PackageDownloadCallback() {
            @Override
            public void onPackageLoaded(PackageInfoWrapper packageInfo) {
                // 加载成功。
                callback.onReceiveValue(null);
            }

            @Override
            public void onAllPackageLoaded(List<PackageInfoWrapper> packageList) {

            }

            @Override
            public void onPackageLoadFailed(String msg, Exception e) {
                // 加载失败。
                callback.onReceiveFailValue(e);
            }

            @Override
            public void onPackageLoadCanceled() {
                // 加载取消。
                callback.onReceiveFailValue(new Exception("package load canceled."));
            }
        });
    }
}
