package com.meituan.msc.common.framework;

import android.support.annotation.NonNull;

import com.meituan.msc.common.process.MSCProcess;
import com.meituan.msc.common.process.ipc.IPCInvoke;
import com.meituan.msc.common.utils.CollectionUtil;
import com.meituan.msc.modules.apploader.IAppLoader;

import java.util.ArrayList;
import java.util.HashMap;
import java.util.List;

/**
 * Created by letty on 2019/7/30.
 * <p>
 * 分为本进程及跨进程全局两种记录
 **/
public class MSCRunningManager {

    // appId, List<engineId>
    private static final HashMap<String, List<Integer>> runningApps = new HashMap<>();
    private static final HashMap<String, List<Integer>> runningAppsGlobal = new HashMap<>();

    // 指最后一个使用的小程序，可能已退出
    private static String currentForegroundAppId;
    private static String currentForegroundAppIdGlobal;

    @NonNull
    private static final IIPCTask ipcTask;

    static {
        if (MSCProcess.isInMainProcess()) {
            ipcTask = new IPCTask();
        } else {
            ipcTask = IPCInvoke.getInvokeProxy(IPCTask.class, MSCProcess.MAIN);
        }
    }

    public static void startApp(String appId, IAppLoader engine) {
        startApp(appId, engine.getLoaderId());
        ipcTask.startAppGlobal(appId, engine.getLoaderId());
    }

    public static void startApp(String appId, int engineId) {
        synchronized (MSCRunningManager.class) {
            List<Integer> list = runningApps.get(appId);
            if (list == null) {
                list = new ArrayList<>();
            }
            list.add(engineId);
            runningApps.put(appId, list);
        }
    }

    private static void startAppGlobal(String appId, int engineId) {
        synchronized (MSCRunningManager.class) {
            List<Integer> list = runningAppsGlobal.get(appId);
            if (list == null) {
                list = new ArrayList<>();
            }
            list.add(engineId);
            runningAppsGlobal.put(appId, list);
        }
    }

    public static void finishApp(String appId, IAppLoader appLoader) {
        finishApp(appId, appLoader.getLoaderId());
        ipcTask.finishAppGlobal(appId, appLoader.getLoaderId());
    }

    private static void finishApp(String appId, int engineId) {
        synchronized (MSCRunningManager.class) {
            List<Integer> list = runningApps.get(appId);
            if (list != null) {
                list.remove((Integer) engineId);
            }
            if (CollectionUtil.isEmpty(list)) {
                runningApps.remove(appId);
            }

            if (runningApps.isEmpty()) {
                currentForegroundAppId = "";
            }
        }
    }

    private static void finishAppGlobal(String appId, int engineId) {
        synchronized (MSCRunningManager.class) {
            List<Integer> list = runningAppsGlobal.get(appId);
            if (list != null) {
                list.remove((Integer) engineId); //注意不能去掉转换，否则会被当做int型的index，而非数组`元素
            }
            if (CollectionUtil.isEmpty(list)) {
                runningAppsGlobal.remove(appId);
            }

            if (runningAppsGlobal.isEmpty()) {
                currentForegroundAppIdGlobal = "";
            }
        }
    }

    public static void resumeApp(String appId) {
        synchronized (MSCRunningManager.class) {
            currentForegroundAppId = appId;
        }
        ipcTask.resumeAppGlobal(appId);
    }

    private static void resumeAppGlobal(String appId) {
        synchronized (MSCRunningManager.class) {
            currentForegroundAppIdGlobal = appId;
        }
    }

    public static boolean isMultiRunningApp(String appId) {
        synchronized (MSCRunningManager.class) {
            List<Integer> list = runningAppsGlobal.get(appId);
            return !CollectionUtil.isEmpty(list) && list.size() > 1;
        }
    }

    /**
     * 指定小程序是否在运行中（已预热 or 启动中）
     *
     * @param appId appId
     * @return 指定小程序是否在运行中
     */
    public static boolean isAppRunningGlobal(String appId) {
        synchronized (MSCRunningManager.class) {
            List<Integer> list = runningAppsGlobal.get(appId);
            return !CollectionUtil.isEmpty(list);
        }
    }

    /**
     * 本进程
     */
    public static String getCurrentForegroundAppIdInThisProcess() {
        return currentForegroundAppId;
    }

    /**
     * 全局
     */
    public static String getCurrentForegroundAppIdGlobal() {
        return currentForegroundAppIdGlobal;
    }

    interface IIPCTask {
        void startAppGlobal(String appId, Integer engineId);

        void finishAppGlobal(String appId, Integer engineId);

        void resumeAppGlobal(String appId);
    }

    static class IPCTask implements IIPCTask {

        @Override
        public void startAppGlobal(String appId, Integer engineId) {
            MSCRunningManager.startAppGlobal(appId, engineId);
        }

        @Override
        public void finishAppGlobal(String appId, Integer engineId) {
            MSCRunningManager.finishAppGlobal(appId, engineId);
        }

        @Override
        public void resumeAppGlobal(String appId) {
            MSCRunningManager.resumeAppGlobal(appId);
        }
    }
}
