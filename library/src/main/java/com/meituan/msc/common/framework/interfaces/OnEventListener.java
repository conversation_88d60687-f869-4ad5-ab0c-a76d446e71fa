package com.meituan.msc.common.framework.interfaces;

import org.json.JSONObject;


/**
 * 负责Native、Service、Page层之间的通信
 * 实现中不只有一种Listener实例，所以不是任意方都能随时与任意方通信的
 * 基类OnEventListener包含共通的事件，注意不要把其实不通用、无法抽象的事件添加至此处
 *
 * 该类无效！！！！ 相关API 请做功能时自行删除
 */
@Deprecated
public interface OnEventListener {
//
//    /**
//     * Page层/Native触发，通知Service层的订阅处理器处理
//     *
//     * @param event  事件名称
//     * @param params 参数
//     * @param viewId 视图id
//     */
//    void notifyServiceSubscribeHandler(String event, String params, int viewId);

    /**
     * Native层触发，通知Service层的UI Event 订阅处理器处理
     *
     * 该方法无效！！！！ 相关API 请做功能时自行删除
     *
     * @param event  事件名称
     * @param params 参数
     * @param viewId 视图id
     */
    @Deprecated
    void notifyServiceSubscribeUIEventHandler(String event, JSONObject params, int viewId);

}
