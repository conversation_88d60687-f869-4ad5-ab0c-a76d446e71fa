package com.meituan.msc.common.framework.interfaces;

import com.meituan.msc.modules.container.OpenParams;

import java.util.HashMap;

/**
 * 由Page层触发的事件
 * 固定的接收者为Page对应的HeraActivity，无需分发
 */
public interface PageEventListener extends OnEventListener {

    /**
     * Page层发给Native，表示首个Page已经完成了渲染
     */
    void onPageFirstRender(String path, HashMap<String, Object> paramMap, String openType);


    /**
     * Page层Native跳转逻辑执行后触发，当产生页面事件(创建、销毁、跳转等)的时候
     */
    void onAppRoute(OpenParams openParams, int viewId, int reloadViewId, String cache);
}
