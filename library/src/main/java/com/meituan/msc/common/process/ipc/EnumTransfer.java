package com.meituan.msc.common.process.ipc;

import android.os.Parcel;

import com.android.meituan.multiprocess.transfer.IBaseTransfer;
import com.meituan.msc.modules.reporter.MSCLog;

public class EnumTransfer implements IBaseTransfer {
    @Override
    public boolean canTransfer(Object o) {
        return o != null && o.getClass().isEnum();
    }

    @Override
    public void writeToParcel(Object o, Parcel dest) {
        dest.writeString(((Enum<?>)o).getDeclaringClass().getName());
        dest.writeString(((Enum<?>)o).name());
    }

    @SuppressWarnings("rawtypes")
    @Override
    public Enum<?> readFromParcel(Parcel in) {
        try {
            //noinspection unchecked,rawtypes
            return Enum.valueOf(((Class<? extends Enum>) Class.forName(in.readString())), in.readString());
        } catch (Throwable tr) {
            MSCLog.e(tr);
            return null;
        }
    }
}
