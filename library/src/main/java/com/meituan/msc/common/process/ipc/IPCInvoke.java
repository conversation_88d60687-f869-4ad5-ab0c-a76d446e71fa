package com.meituan.msc.common.process.ipc;

import android.os.Parcel;
import android.os.Parcelable;
import android.support.annotation.NonNull;

import com.android.meituan.multiprocess.IPCManager;
import com.android.meituan.multiprocess.exception.TypeTransferExecption;
import com.android.meituan.multiprocess.invoker.IAsyncTaskInvoker;
import com.android.meituan.multiprocess.invoker.IIPCInvokeCallback;
import com.android.meituan.multiprocess.transfer.IBaseTransfer;
import com.android.meituan.multiprocess.transfer.ObjectTypeTransfer;
import com.meituan.msc.common.process.MSCProcess;
import com.meituan.msc.common.process.ProcessMonitor;
import com.meituan.msc.modules.reporter.MSCLog;
import com.meituan.msc.extern.MSCEnvHelper;

import java.lang.reflect.Constructor;
import java.lang.reflect.InvocationHandler;
import java.lang.reflect.InvocationTargetException;
import java.lang.reflect.Method;
import java.lang.reflect.Proxy;
import java.util.ArrayList;
import java.util.Arrays;
import java.util.List;

public class IPCInvoke {

    private static final String TAG = "IPCInvoke";
    public static boolean enableIpcLog = true;
    public static boolean enableIpcTrace = false;

    /**
     * https://km.sankuai.com/page/583087012
     *
     * 通过动态代理，以自然的方法调用方式使用IPC
     * 由于动态代理机制的限制，不能直接代理class，被代理的方法必须由某个interface定义
     *
     * 方法参数分为两种
     *   普通参数：携带数据，可在远端实例化，除基本类型/数组/List/Map外，需实现Parcelable
     *   回调：在远端创建代理对象，同样由于上述动态代理的限制，必须声明为interface形式，另外只能放在参数的最外层（而非其他数据结构内）
     *
     * 对返回值类型不为void的方法，将阻塞等待其返回值（同步调用），返回值类型为void则立即返回（异步调用）
     * 如有同步调用且返回void类型的需要可后续再增加控制方式
     *
     * 对返回的代理对象，强转为{@link IPCInvokeControl}可使用其中的控制功能，包括异常监听
     *
     * @param targetClass 被调用的类，会在远端自动实例化，并且由于实例化导致的风险，不能是非静态的内部类
     * @return 将自动代理targetClass实现的全部interface，因未指定具体返回类型，需要自行转换为其中一种来使用
     */
    @OnLocalProcess
    public static <T> T getInvokeProxy(@NonNull Class<?> targetClass,
                                       @NonNull MSCProcess runOnProcess) {
        InvocationHandler invocationHandler = new BaseIPCInvokeHandler(targetClass) {

            IPCExceptionListener exceptionListener;

            @Override
            public Object invoke(Object proxy, Method method, Object[] callArgs) throws Throwable {
                // 处理非IPC的调用
                if (isCommonMethod(method)) {
                    return super.invoke(proxy, method, callArgs);
                }
                if (method.equals(IPCInvokeControl.GET_TARGET_PROCESS_METHOD)) {
                    // 查询本proxy的目标进程
                    return runOnProcess;
                }
                if (method.equals(IPCInvokeControl.SET_IPC_EXCEPTION_LISTENER)) {
                    // 注册IPC异常监听
                    exceptionListener = (IPCExceptionListener) callArgs[0];
                    return null;
                }
                if (callArgs == null) {
                    callArgs = new Object[0];
                }

                if (enableIpcLog) {
                    MSCLog.v(TAG, "ipc invoke send: ", targetClass.getSimpleName(), ".", method.getName());
                }
                IPCReporter.recordInvoke();

                IPCMethodCall methodCall = new IPCMethodCall();
                methodCall.targetClass = targetClass;
                methodCall.method = method;
                methodCall.args = Arrays.copyOf(callArgs, callArgs.length);
                // 对不能作为数据跨进程传输的参数，作为回调处理，将在远端创建其代理
                for (int i = 0; i < callArgs.length; i++) {
                    if (ObjectTypeTransfer.getTypeTransfer(callArgs[i]) == null) {
                        RemoteCallbackProxyData proxyData = new RemoteCallbackProxyData();
                        proxyData.targetClass = callArgs[i].getClass();
                        proxyData.callbackId = i;
                        methodCall.args[i] = proxyData;
                    }
                }

                final SimpleFutureTask<Object> resultFuture;
                if (method.getReturnType() != void.class) {
                    resultFuture = new SimpleFutureTask<>();
                } else {
                    resultFuture = null;
                }


                Object[] finalCallArgs = callArgs;
                IPCManager.invokeAsync(MSCIPCInit.IPC_PROCESS_NAME_PREFIX + runOnProcess.getProcessName(), methodCall,
                        ProxiedTaskInvoker.class, new IIPCInvokeCallback<IPCMethodCall>() {
                            private boolean returned;

                            @Override
                            public void onCallback(IPCMethodCall data) throws TypeTransferExecption {
                                if (!returned) {
                                    IPCReporter.recordReturn();
                                    returned = true;
                                }

                                if (data.callbackId == IPCMethodCall.CALLBACK_ID_RETURN_VALUE) {
                                    // 返回值
                                    if (enableIpcLog) {
                                        MSCLog.v(TAG, "ipc invoke return result: "
                                                + targetClass.getSimpleName() + "." + method.getName());
                                    }
                                    if (resultFuture != null) { //TODO 判断用于监测回调丢失，后续可去除
                                        resultFuture.set(data.args[0]);
                                    }
                                } else if (data.callbackId == IPCMethodCall.CALLBACK_ID_EXCEPTION) {
                                    // 异常
                                    IPCRemoteException exception = new IPCRemoteException(
                                            runOnProcess, targetClass, method, "remote exception", (Exception) data.args[0]);
                                    if (resultFuture != null) {
                                        // 只有同步调用时有机会向调用方抛出
                                        resultFuture.setException(exception);
                                    }
                                    if (exceptionListener != null) {
                                        exceptionListener.onIPCException(exception);
                                    }
                                } else {
                                    // 回调
                                    if (enableIpcLog) {
                                        MSCLog.v(TAG, "ipc invoke callback: "
                                                + finalCallArgs[data.callbackId].getClass().getName() + "." + data.method.getName());
                                    }
                                    try {
                                        data.method.invoke(finalCallArgs[data.callbackId], data.args);
                                    } catch (IllegalAccessException | InvocationTargetException e) {
                                        MSCLog.e(TAG, e);
                                    }
                                }
                            }
                        });

                if (resultFuture == null) {
                    return null;
                } else {
                    try {
                        return resultFuture.get();
                    } finally {
                        // do nothing
                    }
                }
            }
        };

        //noinspection unchecked
        return (T) Proxy.newProxyInstance(targetClass.getClassLoader(),
                getDeclaredInterfaces(targetClass), invocationHandler);
    }

    /**
     * 在远端进程承接调用
     */
    @OnRemoteProcess
    static class ProxiedTaskInvoker implements IAsyncTaskInvoker<IPCMethodCall, IPCMethodCall> {

        @Override
        public final void invoke(IPCMethodCall call, IIPCInvokeCallback<IPCMethodCall> callback) {
            if (enableIpcLog) {
                MSCLog.v(TAG, "ipc invoke received: ", call.targetClass.getSimpleName(), ".", call.method.getName());
            }

            MSCEnvHelper.ensureFullInited();

            Object[] proxiedArgs = Arrays.copyOf(call.args, call.args.length);
            for (int i = 0; i < proxiedArgs.length; i++) {
                if (proxiedArgs[i] instanceof RemoteCallbackProxyData) {
                    RemoteCallbackProxyData proxyData = (RemoteCallbackProxyData) proxiedArgs[i];
                    proxiedArgs[i] = getCallbackProxy(callback, proxyData);
                }
            }

            try {
                Constructor<?> constructor = call.targetClass.getDeclaredConstructor();
                constructor.setAccessible(true);
                Object obj = constructor.newInstance();
                Object result = call.method.invoke(obj, proxiedArgs);   // 实际调用远端方法

//                if (call.method.getReturnType() != void.class) {
//                    // 返回值不为空，返回结果
                    IPCMethodCall resultCall = new IPCMethodCall();
                    resultCall.callbackId = IPCMethodCall.CALLBACK_ID_RETURN_VALUE;
                    resultCall.args = new Object[]{result};
                    callback.onCallback(resultCall);
//                } //TODO 为了在灰度阶段检查IPC丢失的比例，对返回值为空的情况也一律回调，全量上线时可去除
            } catch (Exception e) {
                if (e instanceof IllegalArgumentException) {
                    MSCLog.e(TAG, "check invoke arguments, must use a interface as callback");
                }
                MSCLog.e(TAG, e, "exception in ipc invoke");
                IPCReporter.onException(e.toString(), null);

                // 异常本身为parcelable，但有时原异常持有的参数无法序列化，会抛异常，只取出类型确定的部分进行传输
                IPCRemoteException remoteException = new IPCRemoteException(
                        MSCProcess.getCurrentProcess(), call.targetClass, call.method, e.toString(), e.getStackTrace());

                IPCMethodCall resultCall = new IPCMethodCall();
                resultCall.callbackId = IPCMethodCall.CALLBACK_ID_EXCEPTION;
                resultCall.args = new Object[]{remoteException};
                try {
                    // 返回异常，调用方可能还在同步等待
                    callback.onCallback(resultCall);
                } catch (Exception e2) {
                    MSCLog.e(TAG, e2, "exception when send remote exception, caused by " + e.toString());
                }
            }
        }
    }

    /**
     * 将回调由跨进程传输用的纯数据描述转换为动态代理
     */
    @OnRemoteProcess
    static <T> T getCallbackProxy(IIPCInvokeCallback<IPCMethodCall> callbackObj,
                                  RemoteCallbackProxyData data) {
        InvocationHandler invocationHandler = new BaseIPCInvokeHandler(data.targetClass) {
            @Override
            public Object invoke(Object proxy, Method method, Object[] args) throws Throwable {
                if (isCommonMethod(method)) {
                    return super.invoke(proxy, method, args);
                }
                if (args == null) {
                    args = new Object[0];
                }

                IPCMethodCall methodCall = new IPCMethodCall();
                methodCall.targetClass = Object.class;   // 回调时不会用到，赋值以防止序列化问题
                methodCall.method = method;
                methodCall.args = args;
                methodCall.callbackId = data.callbackId;

                callbackObj.onCallback(methodCall);
                return null;
            }
        };

        //noinspection unchecked
        return (T) Proxy.newProxyInstance(callbackObj.getClass().getClassLoader(),
                getDeclaredInterfaces(data.targetClass), invocationHandler);
    }

    /**
     * Class只提供了获取本类实现的interface的getInterfaces()方法，此处实现对父类实现的方法的获取
     */
    private static Class<?>[] getDeclaredInterfaces(Class<?> target) {
        List<Class<?>> result = new ArrayList<>();
        Class<?> curr = target;
        while (curr != null) {
            result.addAll(Arrays.asList(curr.getInterfaces()));
            curr = curr.getSuperclass();
        }
        result.add(IPCInvokeControl.class); // 特别加料，包含一些控制用功能
        return result.toArray(new Class<?>[0]);
    }

    /**
     * 描述一个跨进程的方法调用
     */
    static class IPCMethodCall implements Parcelable {

        static final int CALLBACK_ID_DEFAULT = -1;
        // 用于返回方法的返回值时，此时targetClass、method = null，args仅使用[0]
        static final int CALLBACK_ID_RETURN_VALUE = -2;
        static final int CALLBACK_ID_EXCEPTION = -3;

        int callbackId = CALLBACK_ID_DEFAULT;    //仅在回调时使用，用于区分回调参数

        Class<?> targetClass;
        Method method;
        Object[] args;  // 注意从动态代理来的参数可能是null，需要特别处理，目前是在赋值至此之前用空数组代替

        public IPCMethodCall() {
        }

        protected IPCMethodCall(Parcel in) {
            targetClass = readFromParcelWithType(in);
            method = readFromParcelWithType(in);
            args = readFromParcelWithType(in);
            callbackId = in.readInt();
        }

        public static final Creator<IPCMethodCall> CREATOR = new Creator<IPCMethodCall>() {
            @Override
            public IPCMethodCall createFromParcel(Parcel in) {
                MSCEnvHelper.ensureFullInited();
                return new IPCMethodCall(in);
            }

            @Override
            public IPCMethodCall[] newArray(int size) {
                return new IPCMethodCall[size];
            }
        };

        @Override
        public int describeContents() {
            return 0;
        }

        @Override
        public void writeToParcel(Parcel dest, int flags) {
            try {
                writeToParcelWithType(targetClass, dest);
                writeToParcelWithType(method, dest);
                writeToParcelWithType(args, dest);
                dest.writeInt(callbackId);
            } catch (Exception e) {
                MSCLog.e(e); //不在MMP管理的线程内调用，异常打不出来，在此专门打一下
                IPCReporter.onException(e.toString(), null);
                throw e;
            }
        }
    }

    public static void writeToParcelWithType(Object object, Parcel dest) {
        IBaseTransfer transfer = ObjectTypeTransfer.getTypeTransfer(object);
        if (transfer != null) {
            dest.writeString(transfer.getClass().getName());
            transfer.writeToParcel(object, dest);
        } else {
            throw new IllegalArgumentException("TypeTransfer not found for " + object.getClass().getName());
        }
    }

    public static <T> T readFromParcelWithType(Parcel in) {
        String transferClass = in.readString();
        //noinspection unchecked
        return (T) ObjectTypeTransfer.readFromParcel(transferClass, in);
    }

    /**
     * 描述一个位于远端的回调对象，此对象本身无法通过IPC传输，需要在被调方进程生成一个代理，转发收到的调用
     */
    static class RemoteCallbackProxyData implements Parcelable {

        Class<?> targetClass;
        int callbackId;

        public RemoteCallbackProxyData() {
        }

        protected RemoteCallbackProxyData(Parcel in) {
            try {
                targetClass = Class.forName(in.readString());
                callbackId = in.readInt();
            } catch (ClassNotFoundException e) {
                MSCLog.e(e);
                IPCReporter.onException(e.toString(), null);
            }
        }

        @Override
        public void writeToParcel(Parcel dest, int flags) {
            dest.writeString(targetClass.getName());
            dest.writeInt(callbackId);
        }

        @Override
        public int describeContents() {
            return 0;
        }

        public static final Creator<RemoteCallbackProxyData> CREATOR = new Creator<RemoteCallbackProxyData>() {
            @Override
            public RemoteCallbackProxyData createFromParcel(Parcel in) {
                return new RemoteCallbackProxyData(in);
            }

            @Override
            public RemoteCallbackProxyData[] newArray(int size) {
                return new RemoteCallbackProxyData[size];
            }
        };
    }

    /**
     * 已知此异常需要跨进程传输，且无法正常序列化，预先处理以避免抛异常的成本
     */
    public static RuntimeException toIpcSafeException(Throwable t) {
        RuntimeException e = new RuntimeException(t.toString());
        e.setStackTrace(t.getStackTrace());
        return e;
    }

    interface ExecuteOnRemoteInterface {
        void executeOnRemote(Class<?> arrayComponentType, Object[] convertedParams, IPCAsyncTask.AsyncTaskCallback callback);
    }

    public static void registerProcessDieListener(Object proxy, ProcessMonitor.ProcessDieListener listener) {
        if (!Proxy.isProxyClass(proxy.getClass())) {
            MSCLog.i("HeraTrace: [" + proxy + "] is not a proxy");
            return;
        }
        ProcessMonitor.registerOneTimeWeakProcessListener(((IPCInvokeControl) proxy).getTargetProcess(), listener);
    }
}
