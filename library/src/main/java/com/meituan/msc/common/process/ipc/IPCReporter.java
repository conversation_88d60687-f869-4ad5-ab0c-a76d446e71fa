package com.meituan.msc.common.process.ipc;

import android.text.TextUtils;

import com.meituan.msc.common.process.MSCProcess;
import com.meituan.msc.modules.api.report.MetricsModule;
import com.meituan.msc.common.utils.HashMapHelper;

import java.util.HashMap;
import java.util.Map;

public class IPCReporter {

    /**
     * 记录本段运行过程中的IPC调用次数，目的是对比检查是否存在调用未返回的情况
     */
    private static class InvokeRecord {
        private int invokeCount = 0;
        private int returnCount = 0;
    }

    private static InvokeRecord invokeRecord = new InvokeRecord();

    public static void recordInvoke() {
        invokeRecord.invokeCount ++;
    }

    public static void recordReturn() {
        invokeRecord.returnCount ++;
    }

    public static void reportInvokeCount(String appId) {
        // InvokeRecord result = invokeRecord;
        // invokeRecord = new InvokeRecord();

        // MSCProcess currentProcess = MSCProcess.getCurrentProcess();
        // Map<String, Object> params = HashMapHelper.of(
        //         "process", currentProcess == null ? "" : currentProcess.getLogName(),
        //         "appId", appId,
        //         "invokeCount", result.invokeCount,
        //         // TODO 大部分调用无返回，因此无法启用上报，目前临时增加了回调用于统计IPC调用丢失，后续可去除
        //         "returnCount", result.returnCount,
        //         "countDiff", result.invokeCount - result.returnCount
        // );
        // MetricsModule.reportMetrics("msc.ipc.invoke.count", params);
    }

    static void onLog(String type, Map<String, String> values) {
        if (values != null && values.containsKey("extra")) {
            String extra = values.get("extra");
            if (!TextUtils.isEmpty(extra)) {
                onException(null, values);
            }
        }
    }

    static void onException(String message, Map<String, String> values) {
        // Map<String, Object> params = new HashMap<>();
        // if (values != null) {
        //     params.putAll(values);
        // }
        // if (!TextUtils.isEmpty(message)) {
        //     params.put("message", message);
        // }
        // MSCProcess currentProcess = MSCProcess.getCurrentProcess();
        // params.put("process", currentProcess == null ? "" : currentProcess.getLogName());

        // MetricsModule.reportMetrics("msc.ipc.exception", params);
    }
}
