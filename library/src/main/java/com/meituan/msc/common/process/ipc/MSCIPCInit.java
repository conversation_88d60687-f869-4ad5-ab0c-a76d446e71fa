package com.meituan.msc.common.process.ipc;

import android.content.Context;

import com.android.meituan.multiprocess.ILog;
import com.android.meituan.multiprocess.IPCInitializer;
import com.android.meituan.multiprocess.IPCManager;
import com.android.meituan.multiprocess.init.IIPCInitDelegate;
import com.android.meituan.multiprocess.init.TypeTransferInitializer;
import com.meituan.msc.common.process.MSCProcess;
import com.meituan.msc.lib.BuildConfig;
import com.meituan.msc.modules.reporter.MSCLog;

import java.util.Map;

public class MSCIPCInit {

    public static final String IPC_CHANNEL_MSC = "msc";
    public static final String IPC_PROCESS_NAME_PREFIX = "msc_";

    private static volatile boolean isInited;

    public static synchronized void init(Context context) {
        if (isInited) {
            return;
        }
        isInited = true;

        MSCProcess.init(context);
        if (MSCProcess.getCurrentProcess() == null) {
            MSCLog.i("HeraTrace-run in unexpected process, stop init");
            return;
        }


        IPCManager.debug(BuildConfig.DEBUG);
        IPCManager.init(context, new IIPCInitDelegate() {
            @Override
            public void addService(IPCInitializer initializer) {
                String pkgName = context.getPackageName();
                // 此处第一个参数设计为进程名，在发起IPC调用时作为查找目标ContentProvider的key使用
                // 因主进程存在多个可接收调用的ContentProvider，以进程名作为key存在冲突，MSC统一添加前缀解决
                initializer.addIPCService(IPC_PROCESS_NAME_PREFIX + pkgName, String.format("content://%s.mscMiniApp", pkgName));//main process
                initializer.addIPCService(IPC_PROCESS_NAME_PREFIX + pkgName + ":mscMiniApp0", String.format("content://%s.mscMiniApp0", pkgName));//proc 1
//                initializer.addIPCService(IPC_PROCESS_NAME_PREFIX + pkgName + ":miniApp1", String.format("content://%s.miniApp1", pkgName));//proc 2
//                initializer.addIPCService(IPC_PROCESS_NAME_PREFIX + pkgName + ":miniApp2", String.format("content://%s.miniApp2", pkgName));//proc2
//                initializer.addIPCService(IPC_PROCESS_NAME_PREFIX + pkgName + ":miniApp3", String.format("content://%s.miniApp3", pkgName));//proc2
            }

            @Override
            public void addServiceManager(IPCInitializer initializer) {
            }

            @Override
            public void onAddTypeTransfer(TypeTransferInitializer initializer) {
                initializer.addTypeTransfer(new EnumTransfer());
            }

            @Override
            public void setLog(IPCInitializer initializer) {
                initializer.setLog(new ILog() {
                    @Override
                    public void onLog(String type, Map<String, String> values) {
                        IPCReporter.onLog(type, values);
                    }
                });

            }
        }, IPC_CHANNEL_MSC);
    }
}
