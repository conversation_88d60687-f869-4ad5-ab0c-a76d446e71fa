package com.meituan.msc.common.process.ipc.provider;

import com.android.meituan.multiprocess.IPCBaseContentProvider;
import com.meituan.msc.common.process.ipc.MSCIPCInit;
import com.meituan.msc.modules.reporter.MSCLog;
import com.meituan.msc.extern.MSCEnvHelper;
import com.sankuai.common.utils.ProcessUtils;

public abstract class MSCIPCProvider extends IPCBaseContentProvider {

    private boolean inited;

    @Override
    public String getProcessName() {
        return ProcessUtils.getCurrentProcessName();
    }

    @Override
    public String getChannel() {
        return MSCIPCInit.IPC_CHANNEL_MSC;
    }

    @Override
    public boolean startAfterInit() {
        return true;
    }

    @Override
    public int getWaitInitTimeOut() {
        return -1;
    }

    @Override
    public void prepareForCall() {
        if (inited) {
            return;
        }
        inited = true;
        MSCLog.i("MSCIPCProvider", "prepareForCall");
        //  在实际执行IPC调用前需要执行MMPIPCInit.init(getContext())以初始化IPC框架的typeTransfer，如MMP整体未初始化将由此处触发
        MSCEnvHelper.startHostInit(getContext());
        MSCEnvHelper.ensureFullInited();
    }
}
