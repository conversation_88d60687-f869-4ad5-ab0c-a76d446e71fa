package com.meituan.msc.common.remote;

import android.app.Service;
import android.content.ComponentName;
import android.content.Context;
import android.content.Intent;
import android.content.ServiceConnection;
import android.net.Uri;
import android.os.Binder;
import android.os.IBinder;
import android.support.annotation.Nullable;

import com.meituan.msc.common.executor.MSCExecutors;
import com.meituan.msc.common.process.GlobalEngineMonitor;
import com.meituan.msc.common.process.MSCProcess;
import com.meituan.msc.common.process.ProcessMonitor;
import com.meituan.msc.common.process.ipc.IPCAsyncTask;
import com.meituan.msc.modules.reporter.MSCLog;
import com.meituan.msc.extern.MSCEnvHelper;

import java.util.Collections;
import java.util.List;
import java.util.Set;
import java.util.concurrent.ConcurrentHashMap;

/**
 * 用于监视存活的进程，在进程绑定/死亡时提供回调
 * 由子进程绑定至主进程
 */
public class RemoteService extends Service {

    private static final String TAG = "RemoteService";

    private static final String KEY_CLIENT_PROCESS = "msc_clientProcess";

    /**
     * 仅供主进程用，监视各子进程存活情况
     * 因主进程可能被杀后重建，只有在调用过requestSubProcessBindToMainProcess()且子进程bind后此处的信息才可靠
     */
    private static final Set<MSCProcess> boundSubProcesses = Collections.newSetFromMap(new ConcurrentHashMap<>());
    private static final Set<MSCProcess> pendingSubProcesses = Collections.newSetFromMap(new ConcurrentHashMap<>());

    @Nullable
    @Override
    public IBinder onBind(Intent intent) {
        MSCProcess bindingProcess = MSCProcess.getProcessByName(intent.getStringExtra(KEY_CLIENT_PROCESS));
        if (bindingProcess == null) {
            MSCLog.e(TAG, "onBind, target process not found: " + intent);
        } else {
            MSCLog.i(TAG, "onBind, process ", bindingProcess, " bound to main process");
            pendingSubProcesses.remove(bindingProcess);
            boundSubProcesses.add(bindingProcess);
        }
        return new Binder();
    }

    // 不能保证可靠调用
    @Override
    public boolean onUnbind(Intent intent) {
        // 主进程发现子进程被杀
        MSCProcess diedProcess = MSCProcess.getProcessByName(intent.getStringExtra(KEY_CLIENT_PROCESS));
        if (diedProcess == null) {
            MSCLog.e(TAG, "onUnbind, target process not found: " + intent);
        } else {
            MSCLog.i(TAG, "onUnbind, process ", diedProcess, " died");
            boundSubProcesses.remove(diedProcess);
            ProcessMonitor.notifyProcessDie(diedProcess);
        }
        return super.onUnbind(intent);
    }

    public static void init(Context context) {
        if (MSCProcess.isInMainProcess()) {
            //若存在主进程主动调用子进程的情况，需增加处理
        } else {
            bindToMainProcess(context);
        }
    }

    /**
     * @param context 有Activity的话传Activity，以便主进程Service提升到与调用方进程一致的进程优先级
     */
    public static void bindToMainProcess(Context context) {
        if (serviceConnection != null) {
            return;
        }
        MonitorServiceConnection newServiceConnection = new MonitorServiceConnection();

        MSCProcess currentProcess = MSCProcess.getCurrentProcess();
        if (currentProcess == null || currentProcess == MSCProcess.MAIN) {
            return;
        }
        Intent intent = new Intent(context, RemoteService.class);
        intent.setData(Uri.parse("imeituan://msc/" + MSCProcess.getCurrentProcess().name()));   // 为了防止intent相等后多次bind只收到一次onBind
        intent.putExtra(KEY_CLIENT_PROCESS, MSCProcess.getCurrentProcess().getProcessName());

        //TODO 绑定activity
        MSCLog.i(TAG, "bindToMainProcess");
        if (MSCEnvHelper.getContext().bindService(intent, newServiceConnection,
                Context.BIND_AUTO_CREATE | Context.BIND_ADJUST_WITH_ACTIVITY)) {
            serviceConnection = newServiceConnection;
        }
    }

    public static void unbindToMainProcess() {  //TODO 希望跟Activity关联以提供前台优先级，但多个Activity交错时可能造成被释放，还未解决
        if (serviceConnection == null) {
            return;
        }
//        try {
//            HeraTrace.w(TAG, "unbindService");
//            MMPEnvHelper.getContext().unbindService(serviceConnection);
//        } catch (Exception e) {
//            HeraTrace.e(e); //TODO 必然异常，待处理，另外绑定Activity的话unbind要去重
//        }
//        serviceConnection = null;
    }

    /**
     * 在主进程重启后用于要求存活的子进程再次绑定service，以供监听
     */
    public static void requestSubProcessBindToMainProcess() {
        if (!MSCProcess.isInMainProcess()) {
            return;
        }
        List<MSCProcess> subProcesses = ProcessMonitor.findAllLivingProcesses();
        subProcesses.remove(MSCProcess.MAIN);

        MSCLog.i(TAG, "requestSubProcessBindToMainProcess: ", subProcesses);
        for (MSCProcess process : subProcesses) {
            new SubBindToMainProcessTask().execute(process);
            pendingSubProcesses.add(process);
        }
    }

    static class SubBindToMainProcessTask extends IPCAsyncTask<Void, Void> {

        @Override
        public Void doOnRemote(Void... voids) throws Exception {
            MSCEnvHelper.ensureFullInited();    // 可能在进程刚创建时就调过来，等待至能拿到context
            bindToMainProcess(MSCEnvHelper.getContext());
            GlobalEngineMonitor.getInstance().syncToMainProcess();
            return null;
        }
    }

    private static MonitorServiceConnection serviceConnection;

    private static class MonitorServiceConnection implements ServiceConnection {
        @Override
        public void onServiceConnected(ComponentName name, IBinder service) {
        }

        @Override
        public void onServiceDisconnected(ComponentName name) {
            serviceConnection = null;
            // 子进程发现主进程被杀
            MSCLog.w(TAG, "notify main process die");
            ProcessMonitor.notifyProcessDie(MSCProcess.MAIN);
        }
    }

    /**
     * 本类的监听有时不准，可能收不到进程被杀消息，在有别处获取存活进程信息时借机进行同步，以保证最终一致
     */
    public static void syncProcessLiveState(List<MSCProcess> realLiveProcesses) {
        for (MSCProcess process : MSCProcess.values()) {
            if (process == MSCProcess.MAIN) {
                continue;
            }
            if (!realLiveProcesses.contains(process)) {
                // 当前不存在，但之前记录中存在，需要更新记录
                if (boundSubProcesses.remove(process) || pendingSubProcesses.remove(process)) {
                    MSCExecutors.runOnUiThread(new Runnable() {
                        @Override
                        public void run() {
                            MSCLog.w(TAG, "process " + process + " died without unbind, notifyProcessDie");
                            ProcessMonitor.notifyProcessDie(process);
                        }
                    });
                }
            }
        }
    }
}
