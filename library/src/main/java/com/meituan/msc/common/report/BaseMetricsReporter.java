package com.meituan.msc.common.report;

import com.meituan.msc.common.utils.MPConcurrentHashMap;

import java.util.Collections;
import java.util.HashSet;
import java.util.List;
import java.util.Map;
import java.util.Set;

public abstract class BaseMetricsReporter {
    private final Map<String, Long> markPointMap = new MPConcurrentHashMap<>();
    private final Map<String, Object> commonTags = new MPConcurrentHashMap<>();
    private final Set<String> sentUnrepeatableMetrics = new HashSet<>();

    /**
     * 设置公共维度
     *
     * @param key
     * @param value
     * @return
     */
    public BaseMetricsReporter commonTag(String key, Object value) {
        if (key != null) {
            this.commonTags.put(key, value);
        }
        return this;
    }

    /**
     * 设置公共维度
     * @param tags
     * @return
     */
    public BaseMetricsReporter commonTags(Map<String, Object> tags) {
        this.commonTags.putAll(tags);
        return this;
    }

    /**
     * 开始记录，用它返回的Entry发送的指标在 Reporter 生命周期内只能发送一次
     *
     * @return
     */
    public MetricsEntry once(String key) {
        return buildEntry(key, false);
    }

    /**
     * 开始记录，用它返回的Entry发送的指标在 Reporter 生命周期内可以发送多次
     *
     * @return
     */
    public MetricsEntry record(String key) {
        return buildEntry(key, true);
    }

    protected MetricsEntry buildEntry(String key, boolean repeatable) {
        return new MetricsEntry(BaseMetricsReporter.this, repeatable, key).tags(commonTags);
    }

    /**
     * 标记时间点，时间为当前时间
     * @param mark
     */
    public void markPoint(String mark) {
        markPointMap.put(mark, getCurrentTime());
    }

    public void markDurationStart(String durationName) {
        markPoint(durationName);
    }

    public Map<String, Long> getMarkPointMap() {
        return Collections.unmodifiableMap(markPointMap);
    }

    /**
     * 获取指定的标记点的时间
     * @param mark
     * @return
     */
    public Long getMarkPointTime(String mark) {
        return markPointMap.get(mark);
    }

    /**
     * 获取当前时间，单位为 ms
     * @return
     */
    protected long getCurrentTime() {
        return System.currentTimeMillis();
    }

    protected void sendEntry(MetricsEntry entry, boolean realTime) {
        // 如果指定的指标不能重复发送，且已经发送过了，则直接退出
        if (!entry.isRepeatable() && sentUnrepeatableMetrics.contains(entry.getKey())) {
            return;
        }
        realSendEntry(entry, realTime);
        if (!entry.isRepeatable()) {
            sentUnrepeatableMetrics.add(entry.getKey());
        }
    }

    protected abstract void realSendEntry(MetricsEntry entry, boolean realTime);

    protected abstract void realSendBatchEntry(List<MetricsEntry> entries, boolean realTime);

    /**
     * 延迟发送，一般用于性能指标
     * 不建议直接调用该方法，请使用 {@link MetricsEntry#sendDelay()}
     */
    public void sendDelay(MetricsEntry entry) {
        realSendEntry(entry, false);
    }

    public Map<String, Object> getCommonTags() {
        return Collections.unmodifiableMap(commonTags);
    }

    /**
     * 立即发送，一般用于质量指标
     * 不建议直接调用该方法，请使用 {@link MetricsEntry#sendRealTime()}
     */
    public void sendRealTime(MetricsEntry entry) {
        realSendEntry(entry, true);
    }

    /**
     * 延迟发送，一般用于性能指标
     */
    public void sendBatchDelay(List<MetricsEntry> entries) {
        if (entries == null || entries.isEmpty()) {
            return;
        }
        realSendBatchEntry(entries, false);
    }

    /**
     * 立即发送，一般用于质量指标
     */
    public void sendBatchRealTime(List<MetricsEntry> entries) {
        realSendBatchEntry(entries, true);
    }
}
