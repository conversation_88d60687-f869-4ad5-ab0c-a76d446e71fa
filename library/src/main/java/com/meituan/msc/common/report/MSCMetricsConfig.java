package com.meituan.msc.common.report;

import android.support.annotation.Keep;
import android.text.TextUtils;

import com.google.gson.annotations.SerializedName;
import com.meituan.msc.lib.interfaces.BaseRemoteConfig;
import com.meituan.msc.modules.container.fusion.MSCFusionInstrumentation;

import java.util.ArrayList;
import java.util.HashMap;
import java.util.List;
import java.util.Map;


public class MSCMetricsConfig extends BaseRemoteConfig<MSCMetricsConfig.Config> {
    public static final String HORN_CONFIG_FILE_KEY = "msc_feature_metrics";
    public static final String TYPE_PATH_FILTER = "pathFilter";


    private MSCMetricsConfig(String hornConfigFileKey, Class<? extends Config> configClass) {
        super(hornConfigFileKey, configClass);
    }

    private static MSCMetricsConfig sInstance;

    public static MSCMetricsConfig get() {
        if (sInstance == null) {
            synchronized (MSCMetricsConfig.class) {
                if (sInstance == null) {
                    sInstance = new MSCMetricsConfig(HORN_CONFIG_FILE_KEY, Config.class);
                }
            }
        }
        return sInstance;
    }

    @Override
    protected void onRemoteConfigChanged(String rawConfigString) {
        super.onRemoteConfigChanged(rawConfigString);
        if (TextUtils.isEmpty(rawConfigString)) {
            return;
        }
        config = parseRemoteConfig(rawConfigString);
    }

    @Keep
    public static class Config {
        public Config() {
        }

        @SerializedName("metricsBlackList")
        Map<String, Map<String, Map<String, PathFilter>>> metricsBlackList = new HashMap<>();

        @Override
        public String toString() {
            StringBuilder sb = new StringBuilder();
            sb.append("Config{ metricsBlackList= {");
            for (Map.Entry<String, Map<String, Map<String, PathFilter>>> entry : metricsBlackList.entrySet()) {
                sb.append(entry.getKey()).append("= {");
                for (Map.Entry<String, Map<String, PathFilter>> innerEntry : entry.getValue().entrySet()) {
                    sb.append(innerEntry.getKey()).append("= ");
                    for (Map.Entry<String, PathFilter> innerInnerEntry : innerEntry.getValue().entrySet()) {
                        sb.append(innerInnerEntry.getKey()).append("= ");
                        sb.append(innerInnerEntry.getValue().toString()).append(", ");
                    }
                }
                sb.append("}");
            }
            sb.append("}");
            return sb.toString();
        }
    }

    @Keep
    public static class PathFilter {
        public PathFilter() {
        }
        @SerializedName("purePath")
        List<String> purePath = new ArrayList<>();

        @Override
        public String toString() {
            return "PathFilter{" +
                    "purePath=" + purePath +
                    '}';
        }
    }
}
