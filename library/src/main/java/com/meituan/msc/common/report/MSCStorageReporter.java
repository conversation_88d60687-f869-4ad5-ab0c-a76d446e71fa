package com.meituan.msc.common.report;

import com.meituan.msc.lib.BuildConfig;
import com.meituan.msc.modules.reporter.CommonTags;
import com.meituan.msc.modules.reporter.MSCReporter;
import com.meituan.msc.modules.reporter.ReporterFields;
import com.meituan.msc.modules.storage.StorageCleanRecord;

import java.util.Map;
import java.util.Set;

public class MSCStorageReporter extends MSCReporter {

    public static MSCStorageReporter create() {
        return new MSCStorageReporter();
    }

    private MSCStorageReporter() {
        super();
        commonTag(CommonTags.SDK_VERSION, BuildConfig.AAR_VERSION);
    }

    public void reportCleanRecord(String appId, String cleanScene, Map<String, StorageCleanRecord> cleanResults) {
        Set<Map.Entry<String, StorageCleanRecord>> entries = cleanResults.entrySet();
        for (Map.Entry<String, StorageCleanRecord> entry : entries) {
            StorageCleanRecord cleanRecord = entry.getValue();
            record(ReporterFields.REPORT_TEMP_FILE_CLEAN_COUNT)
                    .tag("mscAppId", appId)
                    .tag("cleanScene", cleanScene)
                    .tag("dirName", entry.getKey())
                    .tag("totalSizeBeforeClean", cleanRecord.totalSizeBeforeClean)
                    .tag("totalSizeAfterClean", cleanRecord.totalSizeAfterClean)
                    .tag("cleanedSize", cleanRecord.totalSizeBeforeClean - cleanRecord.totalSizeAfterClean)
                    .sendDelay();
        }
    }
}
