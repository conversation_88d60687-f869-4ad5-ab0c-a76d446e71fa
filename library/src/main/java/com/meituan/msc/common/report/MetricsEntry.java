package com.meituan.msc.common.report;

import com.meituan.msc.common.utils.MPConcurrentHashMap;
import com.meituan.msc.common.utils.SampleUtil;
import com.meituan.msc.modules.engine.MSCHornRollbackConfig;
import com.meituan.msc.modules.reporter.CommonTags;
import com.meituan.msc.modules.reporter.MSCLog;
import com.sankuai.android.jarvis.Jarvis;
import com.sankuai.android.jarvis.JarvisThreadPriority;

import java.util.List;
import java.util.Map;
import java.util.concurrent.CopyOnWriteArrayList;
import java.util.concurrent.ExecutorService;

/**
 * 按照职责来看 MetricsEntry 不应该自己"发送"自己，这里为了上层使用方便，所以加了这样的设计
 */
public class MetricsEntry {
    public static final double DEFAULT_VALUE = -1;

    private final BaseMetricsReporter reporter;
    private final boolean repeatable;
    private final Map<String, Object> tags;
    private volatile List<LazyAction> lazyActionList;
    private final String key;
    private double value = DEFAULT_VALUE;
    private String extra;
    private boolean isSent = false;

    private static final ExecutorService sExecutor;

    static {
        sExecutor = Jarvis.newSingleThreadExecutor("msc-metrics", JarvisThreadPriority.PRIORITY_LOW);
    }

    public MetricsEntry(BaseMetricsReporter reporter, boolean repeatable, String key) {
        this.reporter = reporter;
        this.repeatable = repeatable;
        this.key = key;
        this.tags = new MPConcurrentHashMap<>();
    }

    private Object safeConvert(Object value) {
        if (value == null) {
            return null;
        }
        // 由于Babel是使用 new JSONObject(tags) 的方式将维度值转为json字符串上报的，但是new JSONObject不支持枚举值的toString，所以需要处理下
        if (value.getClass().isEnum()) {
            return value.toString();
        }
        return value;
    }

    /**
     * 增加维度
     *
     * @param key
     * @param value
     * @return
     */
    public MetricsEntry tag(String key, Object value) {
        if (key != null) {
            this.tags.put(key, safeConvert(value));
        }
        return this;
    }

    /**
     * 增加懒获取维度
     * 有的维度值的获取比较耗时，可以在发送的时候再获取；如果发送的时候是在异步线程发送，那么就会在异步线程获取维度值
     * 注意线程安全问题
     * 注意：懒获取意味着获取到的值不一定是最新的，对于对准确度要求高的维度，不要使用
     * @param key
     * @param valueGetter
     * @return
     */
    public MetricsEntry lazyTag(String key, IValueGetter valueGetter) {
        if (key != null) {
            return lazy(new LazyAction() {
                @Override
                public void doAction(MetricsEntry metricsEntry) {
                    Object value = valueGetter.getValue();
                    if (value != null) {
                        metricsEntry.tag(key, valueGetter.getValue());
                    }
                }
            });
        } else {
            return this;
        }
    }

    /**
     * 增加懒操作，该操作会在上报之前被执行
     * 有的维度值的获取比较耗时，可以在发送的时候再获取；如果发送的时候是在异步线程发送，那么就会在异步线程获取维度值
     * 注意线程安全问题
     * 注意：懒获取意味着获取到的值不一定是最新的，对于对准确度要求高的维度，不要使用
     * @param action
     * @return
     */
    public MetricsEntry lazy(LazyAction action) {
        if (lazyActionList == null) {
            lazyActionList = new CopyOnWriteArrayList<>();
        }
        lazyActionList.add(action);
        return this;
    }

    public void performLazyActions() {
        if (lazyActionList == null || lazyActionList.isEmpty()) {
            return;
        }
        for (LazyAction lazyAction : lazyActionList) {
            lazyAction.doAction(this);
        }
        lazyActionList.clear();
    }

    /**
     * 增加一堆维度
     *
     * @param tags
     * @return
     */
    public MetricsEntry tags(Map<String, Object> tags) {
        if (tags != null) {
            for (Map.Entry<String, Object> e : tags.entrySet()) {
                this.tags.put(e.getKey(), safeConvert(e.getValue()));
            }
        }
        return this;
    }

    /**
     * 设置值
     *
     * @param value
     * @return
     */
    public MetricsEntry value(double value) {
        this.value = value;
        return this;
    }

    /**
     * 增加扩展信息
     *
     * @param extra
     * @return
     */
    public MetricsEntry extra(String extra) {
        this.extra = extra;
        return this;
    }

    /**
     * 过程计时结束，使用 当前时间 减去 以key为标记的时间点 得到过程耗时
     * 如果没有开始点，则耗时为 -1
     *
     * @return
     */
    public MetricsEntry durationEnd() {
        Long start = reporter.getMarkPointTime(key);
        value = start == null || start < 0 ? -1 : reporter.getCurrentTime() - start;
        return this;
    }

    /**
     * 过程计时结束，使用 当前时间 减去 指定的标记点 得到过程耗时
     * 如果没有开始点，则耗时为 -1
     *
     * @return
     */
    public MetricsEntry durationEnd(String startMarkPoint) {
        Long start = reporter.getMarkPointTime(startMarkPoint);
        value = start == null || start < 0 ? -1 : reporter.getCurrentTime() - start;
        return this;
    }

    /**
     * 过程计时结束，使用 当前时间 减去入参的时间点，得到过程耗时
     *
     * @param startTimeMillis
     * @return
     */
    public MetricsEntry durationEnd(long startTimeMillis) {
        value = startTimeMillis < 0 ? -1 : reporter.getCurrentTime() - startTimeMillis;
        return this;
    }

    public Map<String, Object> getTags() {
        return tags;
    }

    public double getValue() {
        return value;
    }

    public String getExtra() {
        return extra;
    }

    public String getKey() {
        return key;
    }

    protected void reset() {
        tags.clear();
        value = -1;
        extra = null;
        isSent = false;
    }

    protected boolean isRepeatable() {
        return repeatable;
    }

    /**
     * 延迟发送，一般用于性能指标
     */
    public void sendDelay() {
        if (isSent) {
            return;
        }
        isSent = true;
        sExecutor.execute(new Runnable() {
            @Override
            public void run() {
                sendEntryWithSampling(false);
            }
        });
    }

    /**
     * 立即发送，一般用于质量指标
     */
    public void sendRealTime() {
        if (isSent) {
            return;
        }
        isSent = true;
        sExecutor.execute(new Runnable() {
            @Override
            public void run() {
                sendEntryWithSampling(true);
            }
        });
    }

    private void sendEntryWithSampling(boolean isRealTime) {
        try {
            if (MSCMetricsConfig.get().getConfig().metricsBlackList.containsKey(key)) {
                String mscAppId = (String) tags.get(CommonTags.TAG_MSC_APP_ID);
                Map<String, Map<String, MSCMetricsConfig.PathFilter>> currMap =
                        MSCMetricsConfig.get().getConfig().metricsBlackList.get(key);
                if (currMap.containsKey(mscAppId)) {
                    MSCMetricsConfig.PathFilter filter = currMap.get(mscAppId).get(MSCMetricsConfig.TYPE_PATH_FILTER);
                    List<String> pathList = filter.purePath;
                    String currPurePath = (String) tags.get(CommonTags.TAG_PURE_PAGE_PATH);
                    if (pathList.contains(currPurePath)) {
                        MSCLog.i("MetricsEntry", "黑名单过滤 key:", key, ", mscAppId:", mscAppId, ", currPurePath:", currPurePath);
                        return;
                    }
                }
            }
            if (tags.containsKey("$sr") && MSCHornRollbackConfig.isSamplingIndicators(key)) {
                Object sampleRateObj = tags.get("$sr");
                if (sampleRateObj instanceof Number) {
                    double sampleRate = ((Number) sampleRateObj).doubleValue();
                    if (!SampleUtil.isSampled(sampleRate)) {
                        return;
                    }
                }
            }
        } catch (Exception e) {
            String msg = isRealTime ? "sendRealTime " + key : "sendDelay " + key;
            MSCLog.e("[MetricsEntry@sendEntryWithSampling]", e, msg);
        }
        reporter.sendEntry(MetricsEntry.this, isRealTime);
    }

    @Override
    public String toString() {
        return "MetricsEntry{" +
                "key='" + key + '\'' +
                ", value=" + value +
                ", tags=" + getTags() +
                ", extra='" + extra + '\'' +
                '}';
    }
}
