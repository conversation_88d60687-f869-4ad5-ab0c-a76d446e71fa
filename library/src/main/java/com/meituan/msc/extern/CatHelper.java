package com.meituan.msc.extern;

import java.util.Collections;
import java.util.List;
import java.util.Map;

public class CatHelper {
    public void send(String key) {
    }

    public void send(String key, Map<String, Object> options) {
    }

    public void sendKV(String key, float value) {
        sendKVs(key, Collections.singletonList(value));
    }

    public void sendKVs(String key, List<Float> values) {
        sendKVs(key, values, null);
    }

    public void sendKV(String key, float value, Map<String, Object> options) {
        sendKVs(key, Collections.singletonList(value), options);
    }

    public void sendKVs(String key, List<Float> values, Map<String, Object> options) {
    }
}