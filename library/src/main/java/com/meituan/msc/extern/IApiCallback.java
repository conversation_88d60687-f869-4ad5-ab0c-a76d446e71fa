package com.meituan.msc.extern;

import android.support.annotation.Keep;

import org.json.JSONObject;

/**
 * Api回调接口
 */
@Keep
public interface IApiCallback {

    /**
     * Api调用成功
     *
     * @param result Api调用返回的结果
     */
    void onSuccess(JSONObject result);

    /**
     * Api调用失败
     */
    @Deprecated
    void onFail();

    /**
     * Api调用失败，带有参数的 如 errCode
     */
    void onFail(JSONObject result);

    /**
     * Api调用取消
     */
    void onCancel();

}
