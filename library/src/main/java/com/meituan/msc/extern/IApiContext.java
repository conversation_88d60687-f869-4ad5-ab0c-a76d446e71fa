package com.meituan.msc.extern;

import android.app.Activity;
import android.content.Intent;
import android.support.annotation.Nullable;

import com.meituan.msc.modules.devtools.IDevTools;
import com.meituan.msc.modules.page.PageManager;
import com.meituan.msc.modules.update.MSCAppModule;

public interface IApiContext {
    void startActivityForResult(Intent intent, int requestCode, @Nullable IApiCallback callback);

    MSCAppModule getMSCAppModule();

    PageManager getPageManager();

    Activity getContainer();

    IDevTools getDevTools();
}
