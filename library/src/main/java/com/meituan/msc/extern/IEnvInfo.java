package com.meituan.msc.extern;

import android.content.Context;
import android.support.annotation.Keep;
import android.support.annotation.MainThread;


/**
 * 小程序环境信息接口
 */
@Keep
public abstract class IEnvInfo {

    /**
     * @api
     * 组件标准化注释_标准API
     * 获得应用全局对象
     * @return 应用全局对象
     */
    public abstract Context getApplicationContext();

    /**
     * @api
     * 组件标准化注释_标准API
     * 设备唯一标识 请使用灵犀提供的unionid；主要用于请求小程序更新&提供给小程序使用
     * @return 返回设备唯一id
     */
    public abstract String getUUID();

    /**
     * @api
     * 组件标准化注释_标准API
     * 用户唯一标识userid，当用户未登陆小程序的时候为空
     * @return 返回用户id
     */
    public abstract String getUserID();

    /**
     * @api
     * 组件标准化注释_标准API
     * 应用的渠道号，用于请求小程序更新，如果没有 忽略
     * @return 返回渠道号
     */
    public abstract String getChannel();

    /**
     * @api
     * 组件标准化注释_标准API
     * 宿主唯一标识，提供给小程序判断运行环境，接入开发者后台提供
     * 对应开发者后台的appCode字段（https://km.sankuai.com/page/366947792）
     * @return 返回宿主标识
     */
    public abstract String getAppCode();

    /**
     * @api
     * 组件标准化注释_标准API
     * DD后台注册的APP名，用于请求小程序更新
     * 对应开发者后台的DD-Android字段（https://km.sankuai.com/page/366947792）
     * @return 返回应用名称
     */
    public abstract String getAppName();

    /**
     * @api
     * 组件标准化注释_标准API
     * 宿主唯一标识,提供给小程序判断运行环境,可以选择性提供来自 https://app.sankuai.com/app 的 App ID
     * @return 返回宿主id
     */
    public abstract String getAppID();

    /**
     * @api
     * 组件标准化注释_标准API
     * 应用的版本名称，用来标记宿主版本信息，用于UA，及请求小程序更新
     * 例如"11.1.20"，此版本号要求为三段数字，之间用点分隔，每段不大于4位
     * 此版本号不必须与应用真实的VersionName一致，但要与后台配置小程序发布目标时填写的版本号相对应
     * 如格式无法自行处理满足要求，请询客服
     * @return 返回应用版本名称
     */
    public abstract String getAppVersionName();

    /**
     * @api
     * 组件标准化注释_标准API
     * 应用的版本号，用于检测版本变化
     * @return 返回应用版本号
     */
    public abstract int getAppVersionCode();

    /**
     * @api
     * 组件标准化注释_标准API
     * 是否为生产环境
     * 线下版本支持访问测试小程序；线上线下环境数据区分
     * @return 是否为生成环境值
     */
    public abstract boolean isProdEnv();

    /**
     * @api
     * 组件标准化注释_标准API
     * 延迟初始化，将保证在MMP主要逻辑开始执行之前被执行
     */
    @Deprecated
    @MainThread
    public void doDelayedInit() {
    }

    /**
     *
     * @api
     * 组件标准化注释_标准API
     * 参考Pigeon接入文档 https://km.sankuai.com/page/221843893 的appName入参
     * @return 返回宿主名称
     */
    public String getAliasAppName() {
        return getAppCode();
    }

    /**
     * @api
     * 组件标准化注释_标准API
     * 移动之家app id，Cat上报需要
     * @return cat id
     */
    public abstract int getMobileAppId();


    /**
     * @api
     * 组件标准化注释_标准API
     * 微信AppId, 用于初始化微信WXApi时使用。
     * @return 返回宿主程序的WXAppId
     */
    public abstract String getWXAppId();

    /**
     * build号，提供给MRNEnvironment做环境常量使用，如果不需要可以传空字符串
     * @return the build number
     */
    public abstract String getBuildNumber(); // build号，提供给MRNEnvironment做环境常量使用，如果不需要可以传空字符串

    /**
     * @api
     * 组件标准化注释_标准API
     * KNB容器启动协议，用于启动失败降级到H5页面 或 启动提示升级宿主App版本H5页面
     * 示例：imeituan://www.meituan.com/web
     * @return KNB容器启动协议
     */
    public abstract String getKNBHostScheme();

    /**
     * @api
     * 内置元信息功能开关，默认关闭
     * @return 内置元信息功能是否开启
     */
    public boolean enableInnerMetaInfo() {
        return false;
    }
}
