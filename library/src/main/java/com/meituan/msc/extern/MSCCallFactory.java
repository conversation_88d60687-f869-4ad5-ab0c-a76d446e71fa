package com.meituan.msc.extern;

import com.meituan.msc.common.utils.Interceptors;
import com.meituan.msc.common.utils.OkHttpFactory;
import com.sankuai.meituan.retrofit2.Interceptor;
import com.sankuai.meituan.retrofit2.callfactory.okhttp3.OkHttp3CallFactory;
import com.sankuai.meituan.retrofit2.raw.RawCall;

import java.util.ArrayList;
import java.util.List;

/**
 * Created by letty on 2020-06-22.
 *
 * 本项目网络部分原本采用OkHttp形式，因Shark的接口形式为Retrofit，与Shark相关的一部分代码改用Retrofit形式，目前存在不统一
 * 目前Retrofit形式使用范围：Shark -> Candy -> ApiRequest -> FrameworkRequest
 * OkHttp使用范围：Socket、Upload、Download，及遗留的Interceptor
 *
 * 本类对外为Retrofit形式，并通过OkHttp3CallFactory对OkHttp形式进行包装
 **/
public class MSCCallFactory {

    private static RawCall.Factory pureSharkFactory = null;

    private static volatile RawCall.Factory sApiOk3CallFactory;
    private static volatile RawCall.Factory sFrameworkOk3CallFactory;

    private static Boolean apiForceShark;


    /**
     * 使API请求忽略API中参数的控制，强制走/不走Shark
     * 注意如果未提供Shark的实现，依然会回落至OkHttp
     *
     * @param newForceShark null表示默认，不强制
     */
    public static void setForceUseShark(Boolean newForceShark) {
        apiForceShark = newForceShark;
    }

    public static RawCall.Factory getFrameworkCallFactory() {
        if (pureSharkFactory != null) {
            return pureSharkFactory;
        }
        return getFrameworkOk3CallFactory();
    }

    private static RawCall.Factory getFrameworkOk3CallFactory() {
        if (sFrameworkOk3CallFactory == null) {
            synchronized (MSCCallFactory.class) {
                if (sFrameworkOk3CallFactory == null) {
                    sFrameworkOk3CallFactory = OkHttp3CallFactory.create(OkHttpFactory.getInstance().getFrameworkClient());
                }
            }
        }
        return sFrameworkOk3CallFactory;
    }

    public static RawCall.Factory getApiCallFactory(boolean enableShark) {
        boolean actualEnableShark = apiForceShark != null ? apiForceShark : enableShark;
        if (actualEnableShark && pureSharkFactory != null) {
            return pureSharkFactory;
        } else {
            return getApiOk3CallFactory();
        }
    }

    public static RawCall.Factory getMsiApiCallFactory() {
        return pureSharkFactory;
    }

    private static RawCall.Factory getApiOk3CallFactory() {
        if (sApiOk3CallFactory == null) {
            synchronized (MSCCallFactory.class) {
                if (sApiOk3CallFactory == null) {
                    sApiOk3CallFactory = OkHttp3CallFactory.create(OkHttpFactory.getInstance().getRequestClient());
                }
            }
        }
        return sApiOk3CallFactory;
    }

    /**
     * 小程序内部request API使用的
     * 支持Shark/其他网络通道，如不传入将降级使用OkHttp
     *
     * @param externalFactory
     */
    public static void setPureSharkFactory(RawCall.Factory externalFactory) {
        MSCCallFactory.pureSharkFactory = externalFactory;
    }

    private static List<Interceptor> apiInterceptors = new ArrayList<>();

    public static void addApiInterceptor(Interceptor interceptor) {
        apiInterceptors.add(interceptor);
    }

    public static List<Interceptor> getApiInterceptors(boolean needMock) {
        List<Interceptor> result = Interceptors.RetrofitMt.getCommonInterceptors(needMock);
        result.addAll(apiInterceptors);
        return result;
    }


    /**
     * MtGuard验签拦截器、siua拦截器
     * MtGuard验签拦、siua拦截器需保证在所有拦截器最后
     * @return
     */
    public static List<Interceptor> getMtGuardInterceptor() {
        List<Interceptor> mtGuardInterceptors = new ArrayList<>();
        mtGuardInterceptors.add(new Interceptors.SignInterceptor(MSCEnvHelper.getContext()));
        mtGuardInterceptors.add(new Interceptors.SiuaInterceptor());
        return mtGuardInterceptors;
    }


    /**
     * MtGuard验签拦截器、siua拦截器
     * 宿主可以通过Interceptors.setNeededMtGuardSignAndSiua关闭验签
     * @return
     */
    public static List<Interceptor> getMtGuardInterceptors(boolean mtSecuritySign, boolean mtSecuritySiua) {
        List<Interceptor> interceptors = new ArrayList<>();
        //内部小程序且宿主此处需要验签  https://km.sankuai.com/page/545602564
        if (Interceptors.getIsNeededMtGuardSignAndSiua()) {
            if (mtSecuritySign) {
                interceptors.add(new Interceptors.SignInterceptor(MSCEnvHelper.getContext()));
            }
            if (mtSecuritySiua) {
                interceptors.add(new Interceptors.SiuaInterceptor());
            }
        }
        return interceptors;
    }

}
