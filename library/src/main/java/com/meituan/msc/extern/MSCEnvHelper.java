package com.meituan.msc.extern;

import android.app.Activity;
import android.content.Context;
import android.content.SharedPreferences;
import android.os.Build;
import android.support.annotation.Keep;
import android.support.annotation.NonNull;
import android.support.annotation.Nullable;
import android.text.TextUtils;
import android.util.Pair;
import android.webkit.DownloadListener;

import com.meituan.android.common.horn.Horn;
import com.meituan.android.common.weaver.interfaces.feedbackblock.IPageLoadFailedListener;
import com.meituan.android.mercury.msc.adaptor.callback.MSCMetaLoadRulerListener;
import com.meituan.android.mercury.msc.adaptor.callback.OfflineAppIdListener;
import com.meituan.android.mercury.msc.adaptor.core.DDLoadMSCAdaptor;
import com.meituan.android.mercury.msc.adaptor.core.DDMSCAdaptorConfig;
import com.meituan.android.picassohelper.DioPicassoHelper;
import com.meituan.android.singleton.CityControllerSingleton;
import com.meituan.crashreporter.CrashInfoProvider;
import com.meituan.crashreporter.CrashReporter;
import com.meituan.met.mercury.load.bean.DDResourceCleanData;
import com.meituan.met.mercury.load.bean.ExtraParamsBean;
import com.meituan.met.mercury.load.core.DDLoaderManager;
import com.meituan.met.mercury.load.core.LoaderEnvironment;
import com.meituan.met.mercury.load.core.OfflineResourcePushListener;
import com.meituan.met.mercury.load.core.ResourceCleanupCallback;
import com.meituan.metrics.util.DeviceUtil;
import com.meituan.msc.common.config.MSCConfig;
import com.meituan.msc.common.config.MSCHornCacheConfig;
import com.meituan.msc.common.executor.MSCExecutors;
import com.meituan.msc.common.framework.Callback;
import com.meituan.msc.common.framework.MPListenerManager;
import com.meituan.msc.common.interfaces.ICallback;
import com.meituan.msc.common.process.MSCProcess;
import com.meituan.msc.common.process.ipc.MSCIPCInit;
import com.meituan.msc.common.remote.RemoteService;
import com.meituan.msc.common.report.MSCStorageReporter;
import com.meituan.msc.common.utils.JsonUtil;
import com.meituan.msc.common.utils.MSCSharedPreferences;
import com.meituan.msc.common.utils.MSCStorageCleanScene;
import com.meituan.msc.common.utils.ReflectUtil;
import com.meituan.msc.common.utils.ToastUtils;
import com.meituan.msc.common.utils.VersionUtil;
import com.meituan.msc.jse.bridge.ReactBridge;
import com.meituan.msc.lib.interfaces.BaseRemoteConfig;
import com.meituan.msc.modules.IMSCLibraryInterfaceHelper;
import com.meituan.msc.modules.api.location.MSCLocationLoaderCreator;
import com.meituan.msc.modules.api.map.ILocation;
import com.meituan.msc.modules.api.map.ILocationLoader;
import com.meituan.msc.modules.api.map.ILocationLoaderProvider;
import com.meituan.msc.modules.api.web.WebViewUtil;
import com.meituan.msc.modules.apploader.LaunchInfo;
import com.meituan.msc.modules.container.ApplicationLifecycleMonitor;
import com.meituan.msc.modules.container.fusion.FusionPageManager;
import com.meituan.msc.modules.container.fusion.IFusionPageManager;
import com.meituan.msc.modules.engine.MSCHornRollbackConfig;
import com.meituan.msc.modules.engine.MSCLocalAppStackHelper;
import com.meituan.msc.modules.engine.RuntimeManager;
import com.meituan.msc.modules.engine.dataprefetch.MSCHornDynamicPrefetchConfig;
import com.meituan.msc.modules.manager.IMSCLibraryInterface;
import com.meituan.msc.modules.page.render.rn.MSCFpsHornConfig;
import com.meituan.msc.modules.page.render.webview.PreloadWebViewManager;
import com.meituan.msc.modules.page.render.webview.RenderCacheHelper;
import com.meituan.msc.modules.preload.MSCHornPreloadConfig;
import com.meituan.msc.modules.preload.PreloadManager;
import com.meituan.msc.modules.reporter.CrashReporterHelper;
import com.meituan.msc.modules.reporter.MSCLog;
import com.meituan.msc.modules.reporter.MSCLogEnv;
import com.meituan.msc.modules.reporter.MSCPackageNotHitCacheHelper;
import com.meituan.msc.modules.reporter.MSCReporter;
import com.meituan.msc.modules.reporter.ReporterFields;
import com.meituan.msc.modules.reporter.memory.MemoryManager;
import com.meituan.msc.modules.router.MMPRouterManager;
import com.meituan.msc.modules.service.IServiceEngine;
import com.meituan.msc.modules.service.ServiceInstance;
import com.meituan.msc.modules.service.codecache.CodeCacheManager;
import com.meituan.msc.modules.storage.StorageCleanRecord;
import com.meituan.msc.modules.storage.StorageManageUtil;
import com.meituan.msc.modules.update.DDLoaderDebugHelper;
import com.meituan.msc.modules.update.MetaInfoMinVersionManager;
import com.meituan.msc.modules.update.metainfo.AppCheckUpdateManager;
import com.meituan.msc.modules.update.metainfo.MetaFetchRulerManager;
import com.meituan.msc.modules.update.metainfo.PackageExpirationTimeManager;
import com.meituan.msc.modules.update.packageattachment.PackageAttachmentManager;
import com.meituan.msc.modules.update.pkg.PackageLoadManager;
import com.meituan.msc.util.perf.PerfTrace;
import com.meituan.msi.ApiPortalGlobalEnv;
import com.meituan.msi.provider.LocationLoaderConfig;
import com.sankuai.common.utils.ProcessUtils;
import com.sankuai.meituan.serviceloader.ServiceLoader;

import java.util.HashMap;
import java.util.HashSet;
import java.util.List;
import java.util.Map;
import java.util.Set;
import java.util.concurrent.CountDownLatch;

/**
 * MSC环境帮助类
 */
@Keep
public final class MSCEnvHelper {
    public static final String TAG = "MSCEnvHelper";
    private static final String MSC_HOST_APP_VERSION = "msc_host_app_version";

    public static final Object INIT_LOCK = new Object();    //在获取envInfo前会获取此对象锁，可由宿主用于保护初始化过程
    private static volatile IEnvInfo envInfo;
    private static Pair<String, String> customUA;
    //    private static final HashMap<String, String> buildInPackage = new HashMap<>();
    private static String defaultAppID;
    private static boolean hideNavigationBarMenu;
    private static boolean hideNavigationBarBackImage;
    private static IFusionPageManager fusionPageManager;
    private static boolean hideFirstPageNavigationBarBackImage;

    private static Class<? extends IServiceEngine> serviceEngineClazz;
    private static DownloadListener webViewDownloadListener;
    private static boolean enableCleanMMPBizResource;
    private static boolean isFirstStartAppAfterInstall;
    private static final String MMP_ROUTER_CENTER_ACTIVITY = "com.meituan.mmp.lib.RouterCenterActivity";
    private static final boolean MMPOffline = !ReflectUtil.isClassExist(MMP_ROUTER_CENTER_ACTIVITY);
    /**
     * 宿主通用上报指标，如T2
     */
    private static Map<String, Object> commonTags = new HashMap<>();

    public static void init(IEnvInfo info) {
        if (info == null || info.getApplicationContext() == null
                || (!ProcessUtils.isMainProcess(info.getApplicationContext()) && !ProcessUtils.getCurrentProcessName().contains(MSCProcess.PROCESS_SUFFIX_NAME))) {
            String message = "only main process or msc sub process trigger sdk init";
            PreloadManager.getInstance().preloadBaseErrorMsg = message;
            MSCLog.i(TAG, message);
            return;
        }

        envInfo = info;
        MSCLog.setLogEnv(new MSCLogEnv());

        MSCProcess.init(info.getApplicationContext());
        ApiPortalGlobalEnv.setContext(info.getApplicationContext());


        MSCIPCInit.init(getContext());
        MSCConfig.init(false);

        ServiceInstance.staticInit();

        // ddd 初始化
        initDDD(getContext());
        initMSCDDDAdapter();

        PackageAttachmentManager.createInstance(getContext());

        PreloadWebViewManager.getInstance().registerLifecycleListener(getContext());

        // 新增异常上报
        initExecutorReport();

        MSCExecutors.Serialized.submit(new Runnable() {
            @Override
            public void run() {
                ensureFullInited();

                // JS引擎预加载，so文件
                preloadJSESoFiles();

                // 在异步线程初始化预加载器
                PreloadManager.getInstance().init();

                // 提前获取小程序使用栈
                if (MSCHornRollbackConfig.enableLocalAppStackPriority()) {
                    MSCLocalAppStackHelper.getReadOnlyStackListFromPreferences();
                }
            }
        });
    }

    private static void preloadJSESoFiles() {
        if (!MSCHornRollbackConfig.readConfig().enablePreloadJSE) {
            return;
        }
        ReactBridge.staticInit();
        String appName = getContext().getPackageName();
        String deviceName = ServiceInstance.getFriendlyDeviceName();
        ServiceInstance.getDefaultJSExecutorFactory(appName, deviceName, getContext());
    }

    private static void initExecutorReport() {
        MSCExecutors.setSubmitCallback(new ICallback() {

            private final MSCReporter metricsReporter = new MSCReporter();

            @Override
            public void onSuccess(Object result) {
                // ignored.
            }

            @Override
            public void onFailure(int errorCode, String errorMsg, Throwable ex) {
                // on failure.
                report(false, errorMsg);
            }

            private void report(boolean success, String errorMsg) {
                metricsReporter
                        .record(ReporterFields.MSC_EXE_POOL_ERROR_RATE)
                        .tag("errorMsg", String.valueOf(errorMsg))
                        .tag("$sr", MSCHornRollbackConfig.getExeSubmitUploadRate())
                        .value(success ? MSCReporter.ReportValue.SUCCESS : MSCReporter.ReportValue.FAILED)
                        .sendDelay();
            }
        });
    }

    private static void delayedInit() {

        DioPicassoHelper.registerDioImageModelLoader(MSCEnvHelper.getContext());
        MetaFetchRulerManager.getInstance().savaPkgExtraParamsPersistAfterInit();

        if (!ServiceLoader.isInited()) {
            ServiceLoader.setContext(getContext());
        }

//        MMPHornAppBrandConfig.loadPreference();
//        registerApis();
        if (fusionPageManager == null) {
            MSCEnvHelper.setFusionPageManager(new FusionPageManager());
        }

        if (MSCProcess.isInMainProcess()) {
            StorageManageUtil.registerOneTouchCleanHandler();
        }
//         } else {
        //     MSCProcess currentProcess = MSCProcess.getCurrentProcess();
        //     MetricsModule.reportMetrics("msc.launch.process.create",
        //             HashMapHelper.of("process", currentProcess == null ? "" : currentProcess.getLogName()));
        // }

        onMSCEnvHelperInit();
    }

    private static void onMSCEnvHelperInit() {
        IMSCLibraryInterface libraryInterface = IMSCLibraryInterfaceHelper.getIMSCLibraryInterface();
        if (libraryInterface != null) {
            libraryInterface.onMSCEnvHelperInit(getContext());
        }
    }

    private static void asyncInit() {
        MSCLog.i(TAG, "aysncInit");

        appendMSCBaseDataToCrashInfo();
        // TODO: 2022/2/16 tianbin V8内存数据上报与MMP Key冲突，一期不上线
//        JSMemoryHelper.registerCrashInfoProviders();

        CodeCacheManager.getInstance();

        MemoryManager.init(getContext());

        RemoteService.init(getContext());
        RemoteService.requestSubProcessBindToMainProcess();

        MSCHornCacheConfig.registerHorn();
//        MMPHornAppBrandConfig.registerHorn();
        MSCConfig.registerHorn();
        LaunchInfo.registerHorn();
        MSCHornPreloadConfig.get().registerHornConfig();
        MSCHornDynamicPrefetchConfig.get().registerHornConfig();
        IMSCLibraryInterface libraryInterface = IMSCLibraryInterfaceHelper.getIMSCLibraryInterface();
        if (libraryInterface != null) {
            libraryInterface.onHornInit(getContext(), getMSCRenderHornQuery());
        }

        // 读取最低版本号配置到内存中
        MetaInfoMinVersionManager.getInstance().init();

        // App启动时/进入前台 进行批量更新
        initBatchCheckUpdate();

        // SDK 初始化的时候异步清理一波过期的CodeCache产物
        PackageAttachmentManager.getInstance().cleanAbandonedAttachmentAsync();

        Horn.preload(MSCFpsHornConfig.HORN_CONFIG_FILE_KEY);

        // 防止envInfo为空
        if (MSCEnvHelper.isInited()) {
            int oldAppVersion = getDefaultSharedPreferences().getInt(MSC_HOST_APP_VERSION, 0);
            if (oldAppVersion != MSCEnvHelper.getEnvInfo().getAppVersionCode()) {
                getDefaultSharedPreferences().edit().putInt(MSC_HOST_APP_VERSION, MSCEnvHelper.getEnvInfo().getAppVersionCode()).apply();
                isFirstStartAppAfterInstall = true;
            }
        }
        // 渲染缓存进行老版本数据清理
        RenderCacheHelper.init();
    }

    /**
     * 为了控制影响，目前只提供给了渲染侧，其他模块按需使用
     * Note: 如果要使用这个，参考一下MSCRenderConfig的实现，要调用{@link BaseRemoteConfig}带有isRegisterConfigImmediately的构造方法，并设置为false
     * 否则，请求参数会默认通过当前子类的getHornQuery获取
     *
     * @return
     */
    public static Map<String, Object> getMSCRenderHornQuery() {
        Map<String, Object> extra = new HashMap<>();
        extra.put("cityId", CityControllerSingleton.getInstance().getCityId());
        extra.put("chromeVersion", WebViewUtil.getChromeWebViewVersion(getContext()));
        extra.put("deviceLevel", DeviceUtil.getDeviceLevel(getContext()).getValue());
        String manufacturer = Build.MANUFACTURER;
        if (!TextUtils.isEmpty(manufacturer)) {
            extra.put("manufacturer", manufacturer);
        }
        extra.put("osVersion", Build.VERSION.RELEASE);
        return extra;
    }

    private static void initBatchCheckUpdate() {
        if (!MSCConfig.enableBatchCheckUpdate()) {
            MSCLog.d(TAG, "enableBatchCheckUpdate is false");
            return;
        }

        AppCheckUpdateManager.getInstance().batchCheckUpdate();
        ApplicationLifecycleMonitor.register(MSCEnvHelper.getContext());
        ApplicationLifecycleMonitor.ALL.addReEnterForegroundListener(new Runnable() {
            @Override
            public void run() {
                // 后台切换到前台时，触发批量检查更细，10min窗口期
                AppCheckUpdateManager.getInstance().batchCheckUpdate();

                // 后台切换到前台时，触发基础库检查更新，10min窗口期
                PackageLoadManager.getInstance().checkUpdateLatestBasePackage();
            }
        });
    }

    private static void initMSCDDDAdapter() {
        MSCLog.i(TAG, "initMSCDDDAdapter");
        DDLoadMSCAdaptor.initWithConfig(new DDMSCAdaptorConfig() {

            @Override
            protected String getApp() {
                return MSCEnvHelper.getEnvInfo().getAppCode();
            }

            @Override
            protected String getMSCVersion() {
                return VersionUtil.getVersionWithoutChar();
            }

            @Override
            public int getStorageMode() {
                // 0 存储在cache目录下（可能被清除）; 1 存储在file目录下
                return 1;
            }

            @Override
            public boolean isTestEnv() {
                if (getEnvInfo().isProdEnv()) {
                    return false;
                }

                // 用于控制拉取业务包的环境切换，基础包的环境切换由传参控制
                return DDLoaderDebugHelper.shouldCheckUpdateFromTestEnv();
            }

            @Override
            public boolean isMetaPreset() {
                return MSCEnvHelper.getEnvInfo().enableInnerMetaInfo();
            }
        });
        //业务下线指定版本小程序
        DDLoadMSCAdaptor.setOfflineAppIdListener(new OfflineAppIdListener() {
            @Override
            public void offlineAppId(String appId, String publishId) {
                MSCLog.iSync(TAG, "DDD offline, appId:", appId, "publishId:", publishId);
                if (MSCHornRollbackConfig.get().getConfig().rollbackOfflineBizPackageChange) {
                    MSCLog.iSync(TAG, "DDD offline, appId:", appId, "publishId:", publishId, "rollback feature");
                    return;
                }
                RuntimeManager.markOrDestroyRuntimes(appId, publishId);
            }
        });

        DDLoadMSCAdaptor.setMscMetaLoadRulerListener(new MSCMetaLoadRulerListener() {
            @Override
            public List<ExtraParamsBean> getExtraParams(String appId) {
                return MetaFetchRulerManager.getInstance().getExtraParamsList(appId, AppCheckUpdateManager.getInstance().getPkgExtraParams());
            }
        });
    }

    private static void appendMSCBaseDataToCrashInfo() {
        CrashReporter.getInstance().registerCrashInfoProvider(new CrashInfoProvider() {
            @Override
            public Map<String, Object> getCrashInfo(String s, boolean b) {
                HashMap<String, Object> map = new HashMap<>();
                // 上报栈顶页面业务版本号
                map.put("mscAppVersion", CrashReporterHelper.getCurrentMSCForegroundAppVersion(false));
                // 上报栈顶页面小程序id，会覆盖宿主的设置逻辑
                String appId = CrashReporterHelper.getCurrentMSCForegroundAppId(false);
                map.put("mscAppId", appId);
                // 上报栈顶页面业务页面路径
                map.put("mscPagePath", CrashReporterHelper.getCurrentMSCForegroundPagePath(false));
                // 上报栈顶页面小程序业务维度数据
                appendBizTags(map, appId);

                // 上报栈顶Widget页面数据
                appendCrashInfoOfWidget(map);
                return map;
            }
        });
    }

    private static void appendCrashInfoOfWidget(HashMap<String, Object> map) {

        String appIdOfWidget = CrashReporterHelper.getCurrentMSCForegroundAppId(true);
        if (!TextUtils.isEmpty(appIdOfWidget)) {
            map.put("mscAppIdOfWidget", appIdOfWidget);
        }
        String appVersionOfWidget = CrashReporterHelper.getCurrentMSCForegroundAppVersion(true);
        if (!TextUtils.isEmpty(appVersionOfWidget)) {
            map.put("mscAppVersionOfWidget", appVersionOfWidget);
        }
        String pagePathOfWidget = CrashReporterHelper.getCurrentMSCForegroundPagePath(true);
        if (!TextUtils.isEmpty(pagePathOfWidget)) {
            map.put("mscPagePathOfWidget", pagePathOfWidget);
        }
        appendBizTagsOfWidget(map, appIdOfWidget);
    }

    private static void appendBizTags(Map<String, Object> map, String appId) {
        if (isRollbackAppendBizTags(appId)) {
            return;
        }
        // 已与质量组确认，最大上报大小不能超过1kb
        Map<String, String> bizTagsForTopPage = CrashReporterHelper.getCurrentMSCForegroundPageBizTags(false);
        if (bizTagsForTopPage != null) {
            map.put("mscBizTagsForPage", JsonUtil.parseToJson(bizTagsForTopPage));
        }
        Map<String, String> bizTagsForAppId = CrashReporterHelper.getCurrentMSCForegroundBizTagsForAppId(appId);
        if (bizTagsForAppId != null) {
            map.put("mscBizTagsForAppId", JsonUtil.parseToJson(bizTagsForAppId));
        }
    }

    private static void appendBizTagsOfWidget(Map<String, Object> map, String appIdOfWidget) {
        Map<String, String> bizTagsForTopPageCoverWidget = CrashReporterHelper.getCurrentMSCForegroundPageBizTags(true);
        if (bizTagsForTopPageCoverWidget != null) {
            map.put("mscBizTagsForPageOfWidget", JsonUtil.parseToJson(bizTagsForTopPageCoverWidget));
        }
        Map<String, String> bizTagsForAppIdOfWidget = CrashReporterHelper.getCurrentMSCForegroundBizTagsForAppId(appIdOfWidget);
        if (bizTagsForAppIdOfWidget != null) {
            map.put("mscBizTagsForAppIdOfWidget", JsonUtil.parseToJson(bizTagsForAppIdOfWidget));
        }
    }

    private static boolean isRollbackAppendBizTags(String appId) {
        String[] appIds = MSCHornRollbackConfig.get().getConfig().rollbackAppendBizTagsAppIds;
        if (appIds == null) {
            return false;
        }
        for (String id : appIds) {
            if (TextUtils.equals(id, appId)) {
                return true;
            }
        }
        return false;
    }

    private static void initDDD(Context context) {
        DDLoaderManager.init(context, new LoaderEnvironment() {
            @Override
            protected String getUuid() {
                return MSCEnvHelper.getEnvInfo().getUUID();
            }

            @Override
            protected String getUserId() {
                return MSCEnvHelper.getEnvInfo().getUserID();
            }

            @Override
            protected String getChannel() {
                return MSCEnvHelper.getEnvInfo().getChannel();
            }

            @Override
            protected int getMobileAppId() {
                return MSCEnvHelper.getEnvInfo().getMobileAppId();
            }

            @Override
            public boolean enableDebug() {
                return DDLoaderDebugHelper.enableDDLoaderDebug();
            }
        });

        // MSC 基础库下线支持Pike https://km.sankuai.com/collabpage/1756700310
        DDLoaderManager.getLoader("mscsdk").setResourceOfflineListener(new OfflineResourcePushListener() {
            @Override
            public void offlineResource(String business, String name, String version) {
                if (MSCHornRollbackConfig.get().getConfig().isRollBackPikeOfflineBaseSDKStrategy) {
                    MSCLog.iSync(TAG, "isRollBackPikeOfflineBaseSDKStrategy true", business, name, version);
                } else {
                    MSCLog.iSync(TAG, "offlineResource", business, name, version);
                    // 1.基础库预热/业务预热 禁止复用
                    // 2.使用中运行时 禁止保活
                    RuntimeManager.destroyRuntimesWhenBasePackageOffline(name, version);
                }
            }
        });
        // DDD清理基础包缓存回调
        DDLoaderManager.getLoader("mscsdk").registerCleanupCallback(new ResourceCleanupCallback() {
            @Override
            public void onResourceDelete(List<DDResourceCleanData> delResources) {
                if (delResources == null || delResources.isEmpty()) {
                    return;
                }
                MSCPackageNotHitCacheHelper.recordCleanResourceStrategy(delResources);
            }
        });
        // DDD清理业务包缓存回调
        DDLoaderManager.getLoader("msc").registerCleanupCallback(new ResourceCleanupCallback() {
            @Override
            public void onResourceDelete(List<DDResourceCleanData> delResources) {
                if (delResources == null || delResources.isEmpty()) {
                    return;
                }
                MSCPackageNotHitCacheHelper.recordCleanResourceStrategy(delResources);
            }
        });
    }

    /**
     * @return 返回小程序运行时环境信息
     * @api 组件标准化注释_标准API
     * 获取小程序运行时信息
     */
    public static IEnvInfo getEnvInfo() {
        if (envInfo == null) {
            synchronized (INIT_LOCK) {
                // 为防初始化还在进行中，在synchronized内再次检查
                // 不负责保证初始化开始在getEnvInfo之前，这点需要由初始化时机提供保证
                if (envInfo == null) {
                    throw new RuntimeException("need init first");
                }
            }
        }
        return envInfo;
    }

    public static boolean isInited() {
        return envInfo != null;
    }

    public static Context getContext() {
        return getEnvInfo().getApplicationContext();
    }

    public static <T> T getSystemService(@NonNull String name) {
        //noinspection unchecked
        return (T) getEnvInfo().getApplicationContext().getSystemService(name);
    }

    public static void setWebViewDownloadListener(DownloadListener listener) {
        webViewDownloadListener = listener;
    }

    @Nullable
    public static DownloadListener getWebViewDownloadListener() {
        return webViewDownloadListener;
    }

    private static MSCCityController cityController;

    public static void setCityController(MSCCityController aCityController) {
        cityController = aCityController;
    }

    public static @Nullable
    MSCCityController getCityController() {
        return cityController;
    }

    private static IMSCUserCenter mscUserCenter;

    // 保留此接口，如果业务需要自行设置userCenter
    public static void setMSCUserCenter(IMSCUserCenter aMSCUserCenter) {
        mscUserCenter = aMSCUserCenter;
    }


    public static IMSCUserCenter getMSCUserCenter() {
        // 加载默认的userCenter, 在mmp-plugin-user module中
//        if (mscUserCenter == null) {
//            List<IMSCUserCenter> loadedProviders = ServiceLoader.load(IMSCUserCenter.class, "msc_user_center");
//            mscUserCenter = (loadedProviders != null && loadedProviders.size() > 0) ? loadedProviders.get(0) : null;
//        }
        return mscUserCenter;
    }

    private static CatHelper catHelper = new CatHelper();

    public static void setCatHelper(CatHelper aCatHelper) {
        catHelper = aCatHelper;
    }

    public static CatHelper getCatHelper() {
        return catHelper;
    }

    public static SharedPreferences getSharedPreferences(String name) {
        return getSharedPreferences(getContext(), name);
    }

    public static SharedPreferences getSharedPreferences(Context context, String name) {
        return new MSCSharedPreferences(context, name);
    }

    public static SharedPreferences getDefaultSharedPreferences() {
        return getSharedPreferences(getContext(), "msc");
    }

    public static SharedPreferences getDefaultSharedPreferences(Context context) {
        return getSharedPreferences(context, "msc");
    }

    public static void setCustomUserAgentSuffix(String uaKey, String uaValue) {
        if (TextUtils.isEmpty(uaKey) || uaKey.contains(" ") || uaKey.contains("/")) {
            throw new IllegalArgumentException("uaKey 不能包含空格和/");
        }

        if (TextUtils.isEmpty(uaValue) || (uaValue.contains(" ") || uaValue.contains("/"))) {
            throw new IllegalArgumentException("uaValue 不能包含空格和/");
        }

        customUA = new Pair<>(uaKey, uaValue);
    }

    public static Pair<String, String> getCustomUA() {
        return customUA;
    }

    public static String getDefaultAppID() {
        return defaultAppID;
    }

    public static void setDefaultAppID(String defaultAppID) {
        MSCEnvHelper.defaultAppID = defaultAppID;
    }

    public static void onMSCContainerCreate(Context context) {
        startHostInit(context);
        ApplicationLifecycleMonitor.register(context);
    }

    /**
     * 供业务感知 MMP 切 MSC 开关状态
     *
     * @param mmpAppId mmpAppId
     * @return true: 需要切换到MSC
     */
    public static boolean isMMPNeedRouteToMSC(String mmpAppId) {
        return MMPRouterManager.isMMPNeedRouteToMSC(mmpAppId);
    }

    public static boolean isMMPNeedRouteToMSC(Context context, String mmpAppId) {
        return MMPRouterManager.isMMPNeedRouteToMSC(context, mmpAppId);
    }

    /**
     * 还未初始化时触发宿主对MSC的初始化，预期宿主将调用至MSCEnvHelper.init()
     */
    public static void startHostInit(Context context) {
        if (!isInited()) {
            Context applicationContext = context.getApplicationContext();
            if (!ServiceLoader.isInited()) {
                MSCLog.i("HeraTrace-MSCEnvHelper: init service loader by MSC startHostInit");
                ServiceLoader.init(applicationContext, null);
            }
            List<MSCHostInitializer> initializerList = ServiceLoader.load(MSCHostInitializer.class, null);
            if (initializerList.isEmpty()) {
                MSCLog.i("HeraTrace-MSCEnvHelper: HostInitializer not found");
            }
            for (MSCHostInitializer initializer : initializerList) {
                MSCLog.i("HeraTrace-MSCEnvHelper: run hostInit");
                initializer.init(applicationContext);
            }
        }
    }

    private static final CountDownLatch fullInitLatch = new CountDownLatch(1);

    private static boolean isFullInited() {
        return fullInitLatch.getCount() == 0;
    }

    /**
     * 完成全部初始化，此后才能不受限的使用各种功能，小程序启动需要保证此状态
     */
    public static void ensureFullInited() {
        if (isFullInited()) {
            return;
        }
        synchronized (fullInitLatch) {
            if (isFullInited()) {
                return;
            }

            // 还未开始初始化，执行
            String threadName = Thread.currentThread().getName();
            MSCLog.i("HeraTrace-MSCEnvHelper: start full init in thread " + threadName);
            PerfTrace.begin("MSCEnvHelper.doFullInit");

            doFullInit();

            PerfTrace.end("MSCEnvHelper.doFullInit");
            MSCLog.i("HeraTrace-MSCEnvHelper: end full init");
            fullInitLatch.countDown();
        }
    }

    /**
     * 执行完整初始化，包括执行延后和异步任务
     */
    private static void doFullInit() {
        if (!isInited()) {
            MSCLog.i("HeraTrace-MSCEnvHelper: base init not called before full init");
        }

        delayedInit();
        if (HostInit.hostDelayedInitRunnable != null) {
            HostInit.hostDelayedInitRunnable.run();
        }

        MSCExecutors.submit(new Runnable() {
            @Override
            public void run() {
                asyncInit();
            }
        });
    }

    public static boolean needHideNavigationBarMenu() {
        return hideNavigationBarMenu;
    }

    /**
     * 宿主设置隐藏导航栏胶囊按钮
     */
    public static void setHideNavigationBarMenu(boolean hideNavigationBarMenu) {
        MSCEnvHelper.hideNavigationBarMenu = hideNavigationBarMenu;
    }

    public static IFusionPageManager getFusionPageManager() {
        return fusionPageManager;
    }

    public static void setFusionPageManager(IFusionPageManager fusionPageManager) {
        MSCEnvHelper.fusionPageManager = fusionPageManager;
    }

    public static boolean enableCleanMMPBizResource() {
        return enableCleanMMPBizResource;
    }

    public static void setEnableCleanMMPBizResource(boolean enable) {
        enableCleanMMPBizResource = enable;
    }

    /**
     * 是否为首次启动App（首次安装、覆盖安装场景）
     */
    public static boolean isFirstStartAppAfterInstall() {
        return isFirstStartAppAfterInstall;
    }

    public static class HostInit {
        private static volatile Runnable hostDelayedInitRunnable;

        /**
         * 可在启动MSC前较早的必经路径上设置，用于将宿主方面的初始化延后至真正需要启动MSC时/空闲时再执行
         * 应在MSCEnvHelper.init()之前设置，之后可能错过执行时机
         */
        public static void setHostDelayedInitRunnable(Runnable runnable) {
            hostDelayedInitRunnable = runnable;
        }
    }

    /**
     * 隐藏导航栏返回按钮
     */
    public static boolean needHideNavigationBarBackImage() {
        return hideNavigationBarBackImage;
    }

    /**
     * 宿主设置隐藏导航栏返回按钮
     */
    public static void setHideNavigationBarBackImage(boolean hideNavigationBarBackImage) {
        MSCEnvHelper.hideNavigationBarBackImage = hideNavigationBarBackImage;
    }

    /**
     * 隐藏首页导航栏返回按钮
     */
    public static boolean needHideFirstPageNavigationBarBackImage() {
        return hideFirstPageNavigationBarBackImage;
    }

    /**
     * 宿主设置隐藏首页导航栏返回按钮
     */
    public static void setHideFirstPageNavigationBarBackImage(boolean hideFirstPageNavigationBarBackImage) {
        MSCEnvHelper.hideFirstPageNavigationBarBackImage = hideFirstPageNavigationBarBackImage;
    }


    public static Set<String> getMSCFeatureHitList() {
        return mscFeatureHitList;
    }

    public static void setMSCFeatureHitList(Set<String> mscFeatureHitList) {
        MSCEnvHelper.mscFeatureHitList = mscFeatureHitList;
    }

    /**
     * 宿主设置能力黑名单
     */
    private static Set<String> mscFeatureHitList = new HashSet<>();

    /**
     * 定位加载器
     */
    @Nullable
    private static ILocationLoaderProvider sLocationLoaderProvider;

    @NonNull
    public static ILocationLoaderProvider getILocationLoaderProvider() {
        if (sLocationLoaderProvider == null) {
            sLocationLoaderProvider = new ILocationLoaderProvider() {
                @NonNull
                @Override
                public ILocationLoader create(@NonNull Activity activity, @NonNull LocationLoaderConfig locationLoaderConfig) {
                    List<MSCLocationLoaderCreator> locationLoaderCreators =
                            ServiceLoader.load(MSCLocationLoaderCreator.class, "msc_location_loader_creator");
                    if (locationLoaderCreators != null && locationLoaderCreators.size() > 0) {
                        MSCLocationLoaderCreator loaderCreator = locationLoaderCreators.get(0);
                        return loaderCreator.create(activity, locationLoaderConfig);
                    } else {
                        ToastUtils.toastIfDebug("need dependence msc-plugin-locate or call setLocationLoaderProvider");
                        return new ILocationLoader() {
                            @Override
                            public void startLocation(ILocation mapLocation, String type) {
                            }

                            @Override
                            public void stopLocation() {
                            }
                        };
                    }
                }
            };
            return sLocationLoaderProvider;
        }
        return sLocationLoaderProvider;
    }

    /**
     * 如果宿主有自定义实现LocationLoader的需求，可以调用该方法覆盖MSC的默认实现
     *
     * @param locationLoaderProvider locationLoader实例创建接口
     */
    public static void setLocationLoaderProvider(@NonNull ILocationLoaderProvider locationLoaderProvider) {
        sLocationLoaderProvider = locationLoaderProvider;
    }

    /**
     * 宿主主动清理临时文件接口，https://km.sankuai.com/collabpage/**********
     *
     * @param context                context
     * @param appId                  appId
     * @param cleanScene             清理时机
     * @param tempDirSizeLimitMB     temp文件夹缓存大小限制，当参数<= 0时，使用默认值
     * @param usrTmpFilesSizeLimitMB usr文件夹tmp前缀缓存文件大小限制
     */
    public static void cleanTempFiles(Context context, String appId, @MSCStorageCleanScene String cleanScene,
                                      final long tempDirSizeLimitMB, final long usrTmpFilesSizeLimitMB,
                                      Callback<Void> callback) {
        if (MSCHornRollbackConfig.readConfig().rollbackTempFileCleanChange) {
            MSCLog.i(TAG, "rollback cleanTempFiles");
            return;
        }
        MSCExecutors.submit(new Runnable() {
            @Override
            public void run() {
                try {
                    Map<String, StorageCleanRecord> cleanResults =
                            StorageManageUtil.cleanTempDirAndUsrTmpFiles(context, appId, cleanScene, tempDirSizeLimitMB, usrTmpFilesSizeLimitMB);

                    // report clean result
                    MSCStorageReporter.create().reportCleanRecord(appId, cleanScene, cleanResults);

                    if (callback != null) {
                        callback.onSuccess(null);
                    }
                } catch (Exception e) {
                    MSCLog.e(TAG, e, "cleanTempFiles error");
                    if (callback != null) {
                        callback.onFail(e != null ? e.toString() : "clean temp files error", e);
                    }
                }
            }
        });
    }

    /**
     * 是否回滚半屏弹窗
     *
     * @return 是否回滚半屏弹窗，会降级回全屏
     */
    public static boolean rollbackHalfDialogToPage() {
        return MSCHornRollbackConfig.readConfig().rollbackHalfDialog;
    }

    @Nullable
    public static Object getTag(String key) {
        return commonTags.get(key);
    }

    public static void setTag(String key, Object value) {
        commonTags.put(key, value);
    }

    public static boolean isMMPOffline() {
        return MMPOffline;
    }

    public static void registerPageLoadFailedListener(IPageLoadFailedListener loadFailedListener) {
        MPListenerManager.getInstance().registerPageLoadFailedListener(loadFailedListener);
    }

    public static void unRegisterPageLoadFailedListener(IPageLoadFailedListener loadFailedListener) {
        MPListenerManager.getInstance().unRegisterPageLoadFailedListener(loadFailedListener);
    }

    public static void setPackageExpirationTime(String appId, int seconds) {
        PackageExpirationTimeManager.getInstance().setPackageExpirationTimeFromNative(appId, seconds);
    }
}