package com.meituan.msc.extern;

import android.content.Intent;

import com.meituan.msc.common.lib.MiniProgramLifecycleManager;
import com.meituan.msc.common.process.MSCProcess;
import com.meituan.msc.common.process.ipc.IPCInvoke;
import com.meituan.msc.modules.reporter.MSCLog;

public class MSCLifecycleCallback extends MiniProgramLifecycleManager.AbsMSCLifecycleCallback {

    private static final String TAG = "MSCLifecycleCallback";

    private static volatile MSCLifecycleCallback sInstance;

    public static MSCLifecycleCallback getInstance() {
        if (sInstance == null) {
            synchronized (MSCLifecycleCallback.class) {
                if (sInstance == null) {
                    sInstance = new MSCLifecycleCallback();
                }
            }
        }
        return sInstance;
    }

    @Override
    public void onBackPressedByUser(String appId, Intent intent) {
        MSCLog.i(TAG, MSCProcess.getCurrentProcessName(), "onMPBackPressedByUser", appId);
        if (!MSCProcess.isInMainProcess()) {
            ((IIPCTask) IPCInvoke.getInvokeProxy(IPCTask.class, MSCProcess.MAIN)).onMPBackPressedByUser(appId, intent);
            return;
        }
        super.onBackPressedByUser(appId, intent);
    }

    private static class IPCTask implements IIPCTask {

        @Override
        public void onMPBackPressedByUser(String appId, Intent intent) {
            MSCLifecycleCallback.getInstance().onBackPressedByUser(appId, intent);
        }
    }

    private interface IIPCTask {
        void onMPBackPressedByUser(String appId, Intent intent);
    }
}
