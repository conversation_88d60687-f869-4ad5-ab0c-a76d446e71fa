package com.meituan.msc.modules;

import com.meituan.msc.modules.manager.IMSCLibraryInterface;
import com.sankuai.meituan.serviceloader.ServiceLoader;

import java.util.List;

public class IMSCLibraryInterfaceHelper {

    private static volatile IMSCLibraryInterface imscLibraryInterface;

    public static IMSCLibraryInterface getIMSCLibraryInterface() {
        if (imscLibraryInterface == null) {
            synchronized (IMSCLibraryInterface.class) {
                if (imscLibraryInterface == null) {
                    List<IMSCLibraryInterface> libraryInterfaces =
                            ServiceLoader.load(IMSCLibraryInterface.class, null);
                    if (libraryInterfaces != null && !libraryInterfaces.isEmpty()) {
                        imscLibraryInterface = libraryInterfaces.get(0);
                    }
                }
            }
        }
        return imscLibraryInterface;
    }
}
