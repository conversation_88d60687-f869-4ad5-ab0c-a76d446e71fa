package com.meituan.msc.modules.api;

import android.support.annotation.Keep;
import android.support.annotation.NonNull;

import com.meituan.msc.modules.reporter.MSCLog;

import org.json.JSONException;
import org.json.JSONObject;

@Keep
public abstract class AbsApi {

    public static final String ERR_MSG = "errMsg";

    public static final String ERR_CODE = "errCode";

    public @NonNull static JSONObject codeJson(int code, String msg) {
        try {
            JSONObject jsonObject = new JSONObject();
            jsonObject.put(ERR_CODE, code);
            if (null != msg) {
                jsonObject.put(ERR_MSG, msg);
            }
            return jsonObject;
        } catch (JSONException ignore) {
            return new JSONObject();
        }
    }

    public static String assembleResult(JSONObject data, String event, String status) {
        if (data == null) {
            data = new JSONObject();
        }
        try {
            String mmpStatus = status;
            if ("ok".equals(status)) {
                mmpStatus = "success";
            } else if ("fail".equals(status)) {
                mmpStatus = "failure";
            }
            data.put("msc.status", mmpStatus);
            if (data.has(ERR_MSG) && null != data.get(ERR_MSG)) {
                data.put(ERR_MSG, String.format("%s:%s %s", event, status, data.getString(ERR_MSG)));
            } else {
                data.put(ERR_MSG, String.format("%s:%s", event, status));
            }
        } catch (JSONException e) {
            MSCLog.e("Api", "assemble result exception!");
        }
        String resultString = data.toString();
        if ("fail".equals(status)) {
            MSCLog.w("BaseApiCallback", "api call failed: " + resultString);
        }
        return resultString;
    }
}
