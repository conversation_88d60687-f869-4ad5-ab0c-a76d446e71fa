package com.meituan.msc.modules.api;

/**
 * 用于表示在API调用过程中已知的、需要交由小程序处理的异常，可用于参数校验等
 * 将统一捕获，并引起API调用失败回调，不应导致客户端崩溃
 * 应在message中向小程序开发者说明错误原因，并且不要暴露过多实现细节
 */
public class ApiException extends Exception {
    private int errorCode;

    public ApiException(String message) {
        super(message);
    }

    public ApiException(int errorCode, String message) {
        super(message);
        this.errorCode = errorCode;
    }

    public int getErrorCode() {
        return this.errorCode;
    }
}
