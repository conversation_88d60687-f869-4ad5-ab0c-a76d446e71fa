package com.meituan.msc.modules.api;

import android.content.SharedPreferences;

import com.meituan.msc.extern.MSCEnvHelper;

/**
 * Created by letty on 2021/7/1.
 **/
public class LocationRequestIntervalLimit {
    private static final String KEY_LOCATION_REQUEST_TIME = "last_request_time";

    private static boolean enableTimeInterval;

    public static boolean timeInterval(String appId) {
        if (enableTimeInterval && appId != null) {
            return checkPermissionPer48h(appId);
        }
        return true;
    }

    public static void setEnableTimeInterval(boolean enable) {
        enableTimeInterval = enable;
    }

    /**
     * 48h 内只显示一次定位权限；工信部对app首页、频道要求； https://km.sankuai.com/page/836772856
     *
     * @param appId
     */
    public static boolean checkPermissionPer48h(String appId) {
        SharedPreferences sp = MSCEnvHelper.getSharedPreferences("mmp_location_interval");
        String lastRequestKey = KEY_LOCATION_REQUEST_TIME + appId;

        long lastShowTime = sp.getLong(lastRequestKey, 0);
        long currentTime = System.currentTimeMillis();

        if (lastShowTime == 0 || currentTime - lastShowTime > 48 * 60 * 60 * 1000) {
            sp.edit().putLong(lastRequestKey, currentTime).apply();
            return true;
        }

        return false;
    }

}
