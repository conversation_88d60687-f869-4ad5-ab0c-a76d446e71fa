package com.meituan.msc.modules.api.MenuButton;

import android.content.Context;
import android.graphics.Rect;

import com.google.gson.JsonObject;
import com.meituan.msc.common.utils.DisplayUtil;
import com.meituan.msc.lib.R;
import com.meituan.msc.modules.api.msi.MSCApi;
import com.meituan.msc.modules.api.msi.MSCErrorCode;
import com.meituan.msc.modules.engine.MSCHornRollbackConfig;
import com.meituan.msc.modules.page.IPageModule;
import com.meituan.msc.modules.page.IPageNavigationBarMethods;
import com.meituan.msc.modules.page.view.CustomNavigationBar;
import com.meituan.msc.modules.reporter.MSCLog;
import com.meituan.msi.annotations.MsiApiEnv;
import com.meituan.msi.annotations.MsiApiMethod;
import com.meituan.msi.api.IMsiApi;
import com.meituan.msi.api.MSIError;
import com.meituan.msi.bean.ContainerInfo;
import com.meituan.msi.bean.MsiContext;
import com.sankuai.meituan.serviceloader.annotation.ServiceLoaderInterface;


@ServiceLoaderInterface(key = "msc_menu_button", interfaceClass = IMsiApi.class)
@MsiApiEnv(name = ContainerInfo.ENV_MSC)
public class MenuButtonApi extends MSCApi {
    private static final String TAG = "MenuButtonApi";

    @MsiApiMethod(name = "getMenuButtonBoundingClientRect")
    public void getMenuButtonBoundingClientRect(MsiContext context) {
        IPageModule pageModule = getPageModule(context);

        // 优选Tab Widget场景跳转搜索页时，会出现前端pageId获取错误的问题
        // 短期客户端对其iOS获取栈顶页面，长期前端确认pageId获取策略是否合理，如保持现状需要限制业务使用场景
        if (!MSCHornRollbackConfig.readConfig().rollbackMenuButtonPageIdFix
                && pageModule != null && !pageModule.isPageShow()) {
            pageModule = getRuntime().getContainerManagerModule().getTopPage();
        }

        Context activity = context.getActivity();
        if (pageModule == null) {
            MSCLog.e(TAG, "pageModule is null!");
            context.onSuccess(new JsonObject());
            return;
        }
        if (pageModule.isWidget()) {
            MSCLog.e(TAG, "pageModule is widget!");
            context.onSuccess(new JsonObject());
            return;
        }
        if (activity == null && !MSCHornRollbackConfig.isRollbackGetMenuButtonBoundingClientRect()) {
            MSCLog.e(TAG, "activity is null!");
            context.onSuccess(new JsonObject());
            return;
        }
        IPageNavigationBarMethods pageNavigationBarMethods = pageModule
                .getPageNavigationBarMethods();
        if (pageNavigationBarMethods == null) {
            MSCLog.e(TAG, "pageNavigationBarMethods is null!");
            context.onSuccess(new JsonObject());
            return;
        }
        int width, height, top, bottom, left, right;
        if (pageNavigationBarMethods.isMenuButtonShown()) {
            Rect rect = pageNavigationBarMethods.getMenuRect();
            if (rect != null) {
                width = rect.width();
                height = rect.height();
                top = rect.top;
                bottom = rect.bottom;
                left = rect.left;
                right = rect.right;
            } else {
                context.onError("getMenuRect is null", MSIError.getIgnoreError(MSCErrorCode.ERROR_CODE_API_COMMON_MEET_EXPECTATIONS));
                return;
            }
        } else {
            //https://km.sankuai.com/page/351792133
            width = 0;
            height = (int) activity.getResources().getDimension(R.dimen.msc_capsule_height);      //胶囊按钮高度
            int space = (CustomNavigationBar.getFixedHeight() - height) / 2;                       //NavigationBar高度-胶囊按钮高度 = 空白区域
            top = DisplayUtil.getStatusBarHeight() + space;
            bottom = top + height;
            left = right = DisplayUtil.getScreenWidth(activity) - DisplayUtil.fromDPToPix(15);
        }
        MenuButtonResponse response = new MenuButtonResponse();
        response.width = DisplayUtil.toWebValueCeil(width);
        response.height = DisplayUtil.toWebValueCeil(height);
        response.top = DisplayUtil.toWebValueCeil(top);
        response.bottom = DisplayUtil.toWebValueCeil(bottom);
        response.left = DisplayUtil.toWebValueCeil(left);
        response.right = DisplayUtil.toWebValueCeil(right);
        context.onSuccess(response);
    }
}
