package com.meituan.msc.modules.api;

import com.meituan.msc.common.executor.MSCExecutors;
import com.meituan.msc.modules.container.IContainerManager;
import com.meituan.msc.modules.engine.MSCHornRollbackConfig;
import com.meituan.msc.modules.manager.MSCMethod;
import com.meituan.msc.modules.manager.MSCModule;
import com.meituan.msc.modules.manager.ModuleName;
import com.meituan.msc.modules.page.IPageManagerModule;
import com.meituan.msc.modules.page.IPageModule;
import com.meituan.msc.modules.page.RouteReporter;
import com.meituan.msc.modules.page.render.AppPageReporter;
import com.meituan.msc.modules.page.render.BaseRenderer;
import com.meituan.msc.modules.page.render.RendererType;
import com.meituan.msc.modules.reporter.MSCLog;
import com.meituan.msi.provider.IContainerStageProvider;

import org.json.JSONObject;

@ModuleName(name = "PageApi")
public class PageApi extends MSCModule {

    public static final String MSC_REPORT_ROUTE_SUCCESS = "reportRouteSuccess";
    private static final String TAG = "PageApi";

    /**
     * https://km.sankuai.com/collabpage/**********
     *
     * @param params
     * @param viewId
     */
    @MSCMethod(isSync = false)
    public void customReport(JSONObject params, int viewId) {
        // 上报路由成功埋点
        // TODO: 2024/2/4 tianbin 埋点适配 setRouteMapping
        reportRouteSucceed(params, viewId);
    }

    private void reportRouteSucceed(JSONObject params, int viewId) {
        MSCLog.i(TAG, "reportRouteSucceed", params, getRuntime());
        IPageModule pageModule = getPageById(viewId);
        AppPageReporter reporter;
        if (params == null || pageModule == null || (reporter = pageModule.getReporter()) == null) {
            RouteReporter.create(getRuntime()).reportRouteException(params, viewId);
            return;
        }

        JSONObject reportRouteSuccess = params.optJSONObject(MSC_REPORT_ROUTE_SUCCESS);
        reporter.reportRouteSucceed(reportRouteSuccess);
    }


    //https://km.sankuai.com/page/372116277
    @MSCMethod(isSync = true)
    public void pageNotFoundCallback(String path, int viewId) {
        MSCExecutors.runOnUiThread(new Runnable() {
            @Override
            public void run() {
                IContainerManager containerManager = getRuntime().getContainerManagerModule();
                if (containerManager == null) {
                    return;
                }
                IPageManagerModule pageManagerModule = containerManager.getPageManagerByPageId(viewId);
                if (pageManagerModule != null) {
                    pageManagerModule.pageNotFoundCallback();
                }
            }
        });
    }

    @MSCMethod(isSync = true)
    public void updateContainerStage(JSONObject params, int viewId) {
        if (MSCHornRollbackConfig.enableReportAPIPerformanceStatisticData()) {
            String stage = params.optString("stage");
            MSCLog.i(TAG, "updateContainerStage", stage, viewId);
            IPageModule pageModule = getPageById(viewId);
            if (pageModule != null) {
                pageModule.getRenderer().updateContainerStage(stage);
            }
        }
    }
}
