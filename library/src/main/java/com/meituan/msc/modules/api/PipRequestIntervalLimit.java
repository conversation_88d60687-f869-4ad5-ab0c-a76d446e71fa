package com.meituan.msc.modules.api;

import android.content.SharedPreferences;

import com.meituan.msc.extern.MSCEnvHelper;

public class PipRequestIntervalLimit {
    private static final String KEY_PIP_REQUEST_TIME = "last_request_time";

    private static boolean enableTimeInterval = true;

    public static boolean isMeetTimeInterval(String appId) {
        if (enableTimeInterval && appId != null) {
            return checkPermissionPer48h(appId);
        }
        return true;
    }

    public static void setEnableTimeInterval(boolean enable) {
        enableTimeInterval = enable;
    }

    public static boolean checkPermissionPer48h(String appId) {
        SharedPreferences sp = MSCEnvHelper.getSharedPreferences("mmp_pip_interval");
        String lastRequestKey = KEY_PIP_REQUEST_TIME + appId;

        long lastShowTime = sp.getLong(lastRequestKey, 0);
        long currentTime = System.currentTimeMillis();

        if (lastShowTime == 0 || currentTime - lastShowTime > 48 * 60 * 60 * 1000) {
            sp.edit().putLong(lastRequestKey, currentTime).apply();
            return true;
        }

        return false;
    }
}
