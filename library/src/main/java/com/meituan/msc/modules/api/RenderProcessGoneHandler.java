package com.meituan.msc.modules.api;

import android.annotation.TargetApi;
import android.arch.lifecycle.Lifecycle;
import android.content.Context;
import android.os.Build;
import android.support.annotation.Nullable;
import android.view.View;
import android.webkit.RenderProcessGoneDetail;
import android.webkit.WebView;

import com.meituan.msc.common.utils.ToastUtils;
import com.meituan.msc.modules.container.ApplicationLifecycleMonitor;
import com.meituan.msc.modules.container.LifecycleActivity;
import com.meituan.msc.modules.engine.IRendererManager;
import com.meituan.msc.modules.engine.MSCHornRollbackConfig;
import com.meituan.msc.modules.engine.MSCRuntime;
import com.meituan.msc.modules.page.render.webview.OnReloadListener;
import com.meituan.msc.modules.page.render.webview.WebViewCacheManager;
import com.meituan.msc.modules.reporter.CommonTags;
import com.meituan.msc.modules.reporter.MSCLog;
import com.meituan.msc.modules.reporter.MSCReporter;
import com.meituan.msc.modules.reporter.ReporterFields;
import com.meituan.msc.modules.update.MSCAppModule;
import com.meituan.mtwebkit.MTWebView;
import com.meituan.mtwebkit.internal.MTWebViewConstants;

import java.util.HashMap;

public class RenderProcessGoneHandler {

    public static final int RENDERER_PRIORITY_IMPORTANT = 2;

    @TargetApi(Build.VERSION_CODES.O)
    public static void handleRenderProcessGone(View view, RenderProcessGoneDetail detail, String source, MSCRuntime runtime, OnReloadListener listener) {
        handleRenderProcessGone(view, detail.didCrash(), detail.rendererPriorityAtExit(), source, runtime, listener);
    }

    public static void handleRenderProcessGone(View view, boolean didCrash, int rendererPriorityAtExit, String source,
                                               @Nullable MSCRuntime runtime, @Nullable OnReloadListener listener) {
        Context context = view.getContext();

        ToastUtils.toastIfDebug("页面出现问题，重新加载");

        MSCReporter reporter;
        if (runtime != null) {
            reporter = runtime.getRuntimeReporter();
        } else {
            reporter = new MSCReporter();
        }

        HashMap<String, Object> map = new HashMap<>();
        map.put("loadedUrl", source);
        map.put("didCrash", didCrash);
        map.put("rendererPriorityAtExit", rendererPriorityAtExit);
        map.put("appForeground", ApplicationLifecycleMonitor.ALL.isForeground());
        map.put("appState", ApplicationLifecycleMonitor.ALL.getState().name());
//        map.put("totalMemory", DeviceUtil.getTotalMemory(MSCEnvHelper.getContext()));
//        map.put("availMemory", DeviceUtil.getAvailableMemory(MSCEnvHelper.getContext()));

        map.put(ReporterFields.WEB_VIEW_TYPE, CommonTags.getWebViewType(getWebViewTypeByWebView(view)));

        Boolean activityForeground = null;
        if (context instanceof LifecycleActivity) {
            activityForeground = ((LifecycleActivity) context).getLifecycle().getCurrentState().isAtLeast(Lifecycle.State.STARTED);
            map.put("activityForeground", activityForeground);
            map.put("activityState", ((LifecycleActivity) context).getLifecycle().getCurrentState().name());
        }
        map.put("needFallbackToSystemWebView", WebViewCacheManager.isNeedFallbackToSystemWebView());
        reporter.record(ReporterFields.REPORT_RENDER_PROCESS_GONE)
                .tags(map)
                .sendDelay();
//
//        MSCEnvHelper.getSniffer().smell("WebView_Error", "onRenderProcessGone " + activityForeground,
//                String.format("current loaded appId %s Url %s", (app != null ? app.appId : "null"), source),
//                "RenderProcessGoneDetail didCrash " + didCrash + " rendererPriorityAtExit: " + rendererPriorityAtExit);

        MSCLog.w("RenderProcessGone", view +
                String.format("current loaded Url %s", source) +
                ", didCrash: " + didCrash + ", rendererPriorityAtExit: " + rendererPriorityAtExit
                + "，listener=" + listener);

        // release cached webView
        if (runtime != null) {
            //删除缓存的webview
            runtime.webViewCacheManager.releaseWebView(view);
            //清除复用池中的webview
            runtime.getModule(IRendererManager.class).releaseWebView(view);
        }

        if (listener != null) {
            listener.onReload(map);
        }
    }

    /**
     * 根据传入的WebView类型获取WebViewType, 若不是指定WebView, 返回全局变量 MSCAppModule.WEB_VIEW_TYPE
     */
    private static WebViewCacheManager.WebViewType getWebViewTypeByWebView(View view) {
        if (view instanceof MTWebView) {
            MTWebView mtWebView = (MTWebView) view;
            return MTWebViewConstants.TYPE_MTWEBVIEW_MT.equals(mtWebView.getMTWebViewType())
                ? WebViewCacheManager.WebViewType.MT_WEB_VIEW : WebViewCacheManager.WebViewType.MT_WEB_VIEW_SYSTEM;
        } else if (view instanceof WebView) {
            return WebViewCacheManager.WebViewType.CHROME;
        }
        return MSCAppModule.WEB_VIEW_TYPE;
    }
}
