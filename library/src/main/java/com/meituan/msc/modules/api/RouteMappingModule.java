package com.meituan.msc.modules.api;

import android.content.SharedPreferences;
import android.support.annotation.Nullable;
import android.text.TextUtils;

import com.google.gson.Gson;
import com.google.gson.reflect.TypeToken;
import com.meituan.msc.extern.MSCEnvHelper;
import com.meituan.msc.modules.api.msi.MSCApi;
import com.meituan.msc.modules.api.msi.MSCErrorCode;
import com.meituan.msc.modules.engine.MSCHornRollbackConfig;
import com.meituan.msc.modules.engine.MSCRuntime;
import com.meituan.msc.modules.reporter.MSCLog;
import com.meituan.msc.modules.update.AppConfigModule;
import com.meituan.msi.annotations.MsiApiEnv;
import com.meituan.msi.annotations.MsiApiMethod;
import com.meituan.msi.annotations.MsiParamChecker;
import com.meituan.msi.annotations.MsiSupport;
import com.meituan.msi.api.IMsiApi;
import com.meituan.msi.api.MSIError;
import com.meituan.msi.bean.ContainerInfo;
import com.meituan.msi.bean.EmptyResponse;
import com.meituan.msi.bean.MsiContext;
import com.sankuai.meituan.serviceloader.annotation.ServiceLoaderInterface;

import java.util.HashMap;
import java.util.Map;

@ServiceLoaderInterface(key = "mscRouteMapping", interfaceClass = IMsiApi.class)
@MsiApiEnv(name = ContainerInfo.ENV_MSC)
public class RouteMappingModule extends MSCApi {
    private static final String KEY = "mscRouteMapping";
    private static final String TAG = "RouteMappingModule";
    private static final String PERSIST_KEY = "mscRouteMappingPersist";
    private static final String ROUTE_MAPPING_PERSIST_BUILD_ID = "mscRouteMappingPersistBuildId";

    public static SharedPreferences getPersistSP() {
        return MSCEnvHelper.getSharedPreferences(PERSIST_KEY);
    }

    public static Mappings getRouteMappingPersist(String appId) {
        return new Gson().fromJson(getPersistSP().getString(appId, null), new TypeToken<Mappings>() {
        }.getType());
    }

    private static SharedPreferences getSP() {
        return MSCEnvHelper.getSharedPreferences(KEY);
    }

    public static Map<String, RouteParams> getRouteMap(Mappings mappings, boolean isPersist) {
        if (mappings == null || mappings.mappings == null) {
            return new HashMap<>();
        }
        Map<String, RouteParams> map = new HashMap<>();
        for (Mappings.Mapping mapping : mappings.mappings) {
            if (mapping == null || mapping.origin == null || mapping.target == null) {
                MSCLog.i(TAG, "getRouteMap", "mapping is illegal");
                continue;
            }
            boolean isTabDerived = mapping.isTabDerived == null ? !isPersist : mapping.isTabDerived;
            boolean onlyExternalRouter = Boolean.TRUE.equals(mapping.onlyExternalRouter);
            mapping.isTabDerived = isTabDerived;
            mapping.onlyExternalRouter = onlyExternalRouter;
            map.put(mapping.origin, new RouteParams(mapping.target, isTabDerived, onlyExternalRouter));
        }
        return map;
    }

    // 清除对应小程序缓存
    public static void clearCache(String appId) {
        getSP().edit().remove(appId).apply();
    }

    public static void clearCachePersist(String appId) {
        getPersistSP().edit().remove(appId).apply();
    }

    // TODO: 2024/2/4 tianbin 性能浪费
    @Nullable
    public static Mappings getRouteMapping(String appId, String publishId) {
        Mappings mappings = new Gson().fromJson(getSP().getString(appId, null), new TypeToken<Mappings>() {
        }.getType());
        if (mappings != null && mappings.checkIsValidVersionAndDeleteInvalidMappings(publishId, appId)) {
            return mappings;
        } else {
            return null;
        }
    }

    @Deprecated
    public static long getRouteMappingPersistBuildId() {
        return getPersistSP().getLong(ROUTE_MAPPING_PERSIST_BUILD_ID, 0);
    }

    @Deprecated
    public static void setRouteMappingPersistBuildId(long buildId) {
        getPersistSP().edit().putLong(ROUTE_MAPPING_PERSIST_BUILD_ID, buildId).apply();
    }

    @MsiApiMethod(name = "setRouteMapping", env = {"msc"}, scope = "msc", request = Mappings.class)
    public void setRouteMapping(Mappings param, MsiContext context) {
        realSetRouteMapping(param, context);
    }

    @MsiApiMethod(name = "setRouteMappingSync", env = {"msc"}, scope = "msc", request = Mappings.class)
    public EmptyResponse setRouteMappingSync(Mappings param, MsiContext context) {
        realSetRouteMapping(param, context);
        return EmptyResponse.INSTANCE;
    }

    private static void innerSetRouteMappingPersist(MSCRuntime runtime, Mappings param) {
        AppConfigModule appConfigModule = runtime.getAppConfigModule();
        appConfigModule.setRouteMappingsPersist(getRouteMap(param, true));
        getPersistSP().edit().putString(runtime.getAppId(), new Gson().toJson(param)).apply();
    }

    public static void innerClearSetRouteMappingPersist(MSCRuntime runtime) {
        if (MSCHornRollbackConfig.enableFixRouteMappingValidVersion()) {
            runtime.getAppConfigModule().setRouteMappingsPersist(new HashMap<>());
            RouteMappingModule.clearCachePersist(runtime.getAppId());
        } else {
            innerSetRouteMappingPersist(runtime, Mappings.getInstance());
        }
    }

    @MsiApiMethod(name = "getRouteMapping", env = {"msc"}, scope = "msc", response = Mappings.class)
    public void getRouteMapping(MsiContext context) {
        realGetRouteMapping(context);
    }

    @MsiApiMethod(name = "getRouteMappingSync", env = {"msc"}, scope = "msc", response = Mappings.class)
    public Mappings getRouteMappingSync(MsiContext context) {
        return realGetRouteMapping(context);
    }

    private Mappings realGetRouteMapping(MsiContext context) {
        if (MSCHornRollbackConfig.rollbackSetRouteMapping()) {
            MSCLog.i(TAG, "rollbackSetRouteMapping");
            return null;
        }
        Mappings mappings = getRouteMapping(getAppId(), getRuntime().getMSCAppModule().getPublishId());
        Mappings result = mappings != null ? mappings : Mappings.getInstance();
        context.onSuccess(result);
        return result;
    }

    private void realSetRouteMapping(Mappings param, MsiContext context) {
        if (MSCHornRollbackConfig.rollbackSetRouteMapping()) {
            MSCLog.i(TAG, "realSetRouteMapping");
            context.onError("set_route_mapping horn not open", MSIError.getIgnoreError(MSCErrorCode.ERROR_CODE_API_COMMON_CLIENT_CLOSE));
            return;
        }
        if (!checkRouteMapping(param, context)) {
            return;
        }
        AppConfigModule appConfigModule = getRuntime().getAppConfigModule();
        param.publishId = getRuntime().getMSCAppModule().getPublishId();
        appConfigModule.setRouteMapping(getRouteMap(param, false));
        getSP().edit().putString(getAppId(), new Gson().toJson(param)).apply();
        context.onSuccess(null);
    }

    @MsiApiMethod(name = "setRouteMappingPersist", env = {"msc"}, scope = "msc", request = Mappings.class)
    public void setRouteMappingPersist(Mappings param, MsiContext context) {
        if (MSCHornRollbackConfig.isRollbackSetRouteMappingPersist()) {
            context.onError("rollbackRouteMappingPersist",
                    MSIError.getIgnoreError(MSCErrorCode.ERROR_CODE_API_COMMON_CLIENT_CLOSE));
            return;
        }
        realSetRouteMappingPersist(param, context);
    }

    @MsiApiMethod(name = "setRouteMappingPersistSync", env = {"msc"}, scope = "msc", request = Mappings.class)
    public EmptyResponse setRouteMappingPersistSync(Mappings param, MsiContext context) {
        if (MSCHornRollbackConfig.isRollbackSetRouteMappingPersist()) {
            context.onError("rollbackRouteMappingPersist",
                    MSIError.getIgnoreError(MSCErrorCode.ERROR_CODE_API_COMMON_CLIENT_CLOSE));
            return EmptyResponse.INSTANCE;
        }
        realSetRouteMappingPersist(param, context);
        return EmptyResponse.INSTANCE;
    }

    private void realSetRouteMappingPersist(Mappings param, MsiContext context) {
        if (!checkRouteMapping(param, context)) {
            return;
        }
        if (MSCHornRollbackConfig.enableFixRouteMappingValidVersion()) {
            param.buildId = getRuntime().getMSCAppModule().getBuildId();
        }
        innerSetRouteMappingPersist(getRuntime(), param);
        context.onSuccess(null);
    }

    private void realClearRouteMappingPersist(MsiContext context) {
        innerClearSetRouteMappingPersist(getRuntime());
        context.onSuccess(null);
    }

    private boolean checkRouteMapping(Mappings param, MsiContext context) {
        AppConfigModule appConfigModule = getRuntime().getAppConfigModule();
        for (Mappings.Mapping mapping : param.mappings) {
            if (mapping == null || TextUtils.isEmpty(mapping.origin) || TextUtils.isEmpty(mapping.target)) {
                context.onError("setRouteMapping config error, there is mapping/origin/target null in appConfig",
                        MSIError.getGeneralError(MSCErrorCode.ERROR_CODE_API_COMMON_INVALID_PARAM));
                return false;
            }
            if (!appConfigModule.hasPage(mapping.origin)) {
                context.onError("setRouteMapping config error, there is no originPath " + mapping.origin + " in appConfig",
                        MSIError.getGeneralError(MSCErrorCode.ERROR_CODE_API_COMMON_PATH_NOT_EXIST));
                return false;
            }
            if (!appConfigModule.hasPage(mapping.target)) {
                context.onError("setRouteMapping config error, there is no targetPath " + mapping.target + " in appConfig",
                        MSIError.getGeneralError(MSCErrorCode.ERROR_CODE_API_COMMON_PATH_NOT_EXIST));
                return false;
            }
        }
        return true;
    }

    @MsiApiMethod(name = "getRouteMappingPersist", env = {"msc"}, scope = "msc", response = Mappings.class)
    public void getRouteMappingPersist(MsiContext context) {
        if (MSCHornRollbackConfig.isRollbackSetRouteMappingPersist()) {
            context.onError("rollbackRouteMappingPersist", MSIError.getIgnoreError(MSCErrorCode.ERROR_CODE_API_COMMON_CLIENT_CLOSE));
            return;
        }
        context.onSuccess(realGetRouteMappingPersist(context));
    }

    @MsiApiMethod(name = "getRouteMappingPersistSync", env = {"msc"}, scope = "msc", response = Mappings.class)
    public Mappings getRouteMappingPersistSync(MsiContext context) {
        if (MSCHornRollbackConfig.isRollbackSetRouteMappingPersist()) {
            context.onError("rollbackRouteMappingPersist", MSIError.getIgnoreError(MSCErrorCode.ERROR_CODE_API_COMMON_CLIENT_CLOSE));
            return Mappings.getInstance();
        }
        return realGetRouteMappingPersist(context);
    }

    private Mappings realGetRouteMappingPersist(MsiContext context) {
        Mappings mappings = getRouteMappingPersist(getAppId());
        if (MSCHornRollbackConfig.enableFixRouteMappingValidVersion()) {
            return mappings != null && mappings.isValidVersionPersist(getRuntime().getMSCAppModule().getBuildId()) ? mappings : Mappings.getInstance();
        } else {
            return mappings != null ? mappings : Mappings.getInstance();
        }
    }

    @MsiApiMethod(name = "clearRouteMappingPersist", env = {"msc"}, scope = "msc")
    public void clearRouteMappingPersist(MsiContext context) {
        if (MSCHornRollbackConfig.isRollbackSetRouteMappingPersist()) {
            context.onError("rollbackRouteMappingPersist", MSIError.getIgnoreError(MSCErrorCode.ERROR_CODE_API_COMMON_CLIENT_CLOSE));
            return;
        }
        realClearRouteMappingPersist(context);
    }

    @MsiApiMethod(name = "clearRouteMappingPersistSync", env = {"msc"}, scope = "msc")
    public EmptyResponse clearRouteMappingPersistSync(MsiContext context) {
        if (MSCHornRollbackConfig.isRollbackSetRouteMappingPersist()) {
            context.onError("rollbackRouteMappingPersist", MSIError.getIgnoreError(MSCErrorCode.ERROR_CODE_API_COMMON_CLIENT_CLOSE));
            return EmptyResponse.INSTANCE;
        }
        realClearRouteMappingPersist(context);
        return EmptyResponse.INSTANCE;
    }

    @MsiSupport
    public static class Mappings {
        @MsiParamChecker(required = true)
        public Mapping[] mappings;
        String publishId; // 内部字段，客户端用于校验数据版本
        String buildId;

        Mappings(Mapping[] mappings) {
            this.mappings = mappings;
        }

        public static Mappings getInstance() {
            return MappingsHolder.INSTANCE;
        }

        public boolean isValidVersion(String publishId) {
            return TextUtils.equals(this.publishId, publishId);
        }

        // TODO: 2024/2/4 tianbin 优化命名
        public boolean checkIsValidVersionAndDeleteInvalidMappings(String publishId, String appId) {
            boolean isValid = isValidVersion(publishId);
            if (!isValid) {
                clearCache(appId);
            }
            return isValid;
        }

        public boolean isValidVersionPersist(String buildId) {
            // buildId的生效范围已由全局调整为各个小程序独立，对存储位置进行了更改。如果buildId为空，因为业务未使用路由映射，或者使用了旧版的buildId（目前仅优选在使用，且已确认其在业务包降级时能够生效），默认返回true。
            // 为确保与优选场景的兼容性，若无法读取到buildId，将默认其为生效状态。
            if (this.buildId == null) {
                MSCLog.i(TAG, "valid exBuildId is null");
                return true;
            }
            if (buildId == null) {
                MSCLog.e(TAG, "invalid curBuildId is null");
                return false;
            }
            long curBuildId = Long.parseLong(buildId);
            long exBuildId = Long.parseLong(this.buildId);
            return curBuildId >= exBuildId;
        }

        @MsiSupport
        public static class Mapping {
            @MsiParamChecker(required = true)
            public String origin;
            // 必须是页面配置中存在的页面
            @MsiParamChecker(required = true)
            public String target;
            // 是否继承原页面的tab属性
            public Boolean isTabDerived;
            // 是否仅支持外部路由
            public Boolean onlyExternalRouter;
        }

        private static class MappingsHolder {
            private static final Mappings INSTANCE = new Mappings(new Mapping[]{});
        }

    }

    public static class RouteParams {
        //必须是页面配置中存在的页面
        private final String target;
        private final boolean isTabDerived;
        private final boolean onlyExternalRouter;

        public RouteParams(String target, boolean isTabDerived, boolean onlyExternalRouter) {
            this.target = target;
            this.isTabDerived = isTabDerived;
            this.onlyExternalRouter = onlyExternalRouter;
        }

        public String getTarget() {
            return target;
        }

        public boolean isTabDerived() {
            return isTabDerived;
        }

        public boolean isOnlyExternalRouter() {
            return onlyExternalRouter;
        }
    }
}
