package com.meituan.msc.modules.api.appLifecycle;

import android.os.Parcel;
import android.os.Parcelable;

/**
 * 生命周期枚举类 仅关注page，不关注widget
 */
public enum MSCAppLifecycle implements Parcelable {
	//即将首次进入小程序
	MSC_WILL_ENTER_APP_LIFECYCLE,
	//首次真正进入小程序
	MSC_DID_ENTER_APP_LIFECYCLE,
	//即将退出小程序，此时页面栈为空
	MSC_WILL_LEAVE_APP_LIFECYCLE,
	//首次进入小程序失败，异常退出
	MSC_LAUNCH_FAIL_APP_LIFECYCLE;

	@Override
	public int describeContents() {
		return 0;
	}

	@Override
	public void writeToParcel(Parcel dest, int flags) {
		dest.writeInt(ordinal());
	}

	public static final Creator<MSCAppLifecycle> CREATOR = new Creator<MSCAppLifecycle>() {
		@Override
		public MSCAppLifecycle createFromParcel(Parcel in) {
			return  MSCAppLifecycle.values()[in.readInt()];
		}

		@Override
		public MSCAppLifecycle[] newArray(int size) {
			return new MSCAppLifecycle[size];
		}
	};
}
