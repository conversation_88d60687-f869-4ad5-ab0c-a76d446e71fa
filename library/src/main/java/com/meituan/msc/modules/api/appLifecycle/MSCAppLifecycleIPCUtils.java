package com.meituan.msc.modules.api.appLifecycle;

import android.content.Context;
import com.android.meituan.multiprocess.exception.TypeTransferExecption;
import com.meituan.msc.common.process.ipc.MSCIPCInit;
import com.meituan.msc.modules.reporter.MSCLog;
import com.sankuai.common.utils.ProcessUtils;

public class MSCAppLifecycleIPCUtils {
	private static final String TAG = "MSCAppLifecycleIPCUtils";

	public static void dispatchLifecycle(String targetPath, String enterUri, String leaveAppInfo, MSCAppLifecycle mscAppLifecycle, String appId, Context context) {
		try {
			MSCAppLifecycleParams params = new MSCAppLifecycleParams(targetPath, enterUri, leaveAppInfo);
			MSCAppLifecycleManager.getInstance().notifyObserver(appId, mscAppLifecycle, params);
			if (ProcessUtils.getCurrentProcessName().contains("mscMiniApp0")) {
				MSCAppLifecycleEvent event = new MSCAppLifecycleEvent();
				MSCAppLifecycleParcel parcel = new MSCAppLifecycleParcel(mscAppLifecycle, appId, params);
				event.dispatch(MSCIPCInit.IPC_PROCESS_NAME_PREFIX + context.getPackageName(), parcel);
			}
		} catch (TypeTransferExecption typeTransferExecption) {
			typeTransferExecption.printStackTrace();
			MSCLog.e(TAG, typeTransferExecption);
		}
	}
}
