package com.meituan.msc.modules.api.appLifecycle;

import android.text.TextUtils;
import com.android.meituan.multiprocess.event.IPCObservable;
import com.android.meituan.multiprocess.event.IPCObserver;
import com.meituan.msc.common.report.MetricsEntry;
import com.meituan.msc.modules.reporter.MSCLog;
import com.meituan.msc.modules.reporter.MSCReporter;
import com.meituan.msc.modules.reporter.ReporterFields;
import com.sankuai.meituan.serviceloader.ServiceLoader;
import java.util.ArrayList;
import java.util.List;

/**
 * 生命周期感知管理类，统一管理所有监听者的生命周期回调
 * 仅针对page，不关注widget
 * https://km.sankuai.com/collabpage/1843778778
 */
public class MSCAppLifecycleManager {

	private List<MSCAppLifecycleObserver> lifecycleObserverList;
	private final IPCObservable<MSCAppLifecycleParcel> ipcObservable;
	private final IPCObserver<MSCAppLifecycleParcel> ipcObserver;
	private final MSCReporter mscReporter;
	private static volatile MSCAppLifecycleManager instance;
	private static final String TAG = "MSCAppLifecycleManager";

	private MSCAppLifecycleManager() {
		lifecycleObserverList = new ArrayList<>(ServiceLoader.load(MSCAppLifecycleObserver.class, null));
		ipcObservable = new IPCObservable<MSCAppLifecycleParcel>(MSCAppLifecycleEvent.class);
		ipcObserver = new IPCObserver<MSCAppLifecycleParcel>() {
			@Override
			public void onEvent(String from, MSCAppLifecycleParcel data) {
				notifyObserver(data.appId, data.mscAppLifecycle, data.mscAppLifecycleParams);
			}
		};
		mscReporter = new MSCReporter();
		registerIpcObservable();
	}

	public static MSCAppLifecycleManager getInstance() {
		if (instance == null) {
			synchronized (MSCAppLifecycleManager.class) {
				if (instance == null) {
					instance = new MSCAppLifecycleManager();
				}
			}
		}
		return instance;
	}

	/**
	 * 业务API，添加observer
	 * @param observer
	 * @return 添加observer是否成功
	 */
	public boolean addObserver(MSCAppLifecycleObserver observer) {
		if (getObserver(observer.getAppId()) != null) {
			MSCLog.e(TAG,"addObserver fail, observer is exist!");
			return false;
		}
		return lifecycleObserverList.add(observer);
	}

	/**
	 * 业务API，移除observer
	 * @param observer
	 * @return 移除observer是否成功
	 */
	public boolean removeObserver(MSCAppLifecycleObserver observer) {
		return lifecycleObserverList.remove(observer);
	}

	/**
	 * MSC内部注册跨进程监听
	 * 业务不可调用
	 */
	public void registerIpcObservable() {
		ipcObservable.registerIPCObserver(ipcObserver);
	}

	/**
	 * MSC内部反注册跨进程监听
	 * 业务不可调用
	 */
	public void unregisterIpcObservable() {
		ipcObservable.unregisterIPCObserver(ipcObserver);
	}

	private MSCAppLifecycleObserver getObserver(String appId) {
		for (MSCAppLifecycleObserver observer : lifecycleObserverList) {
			if (TextUtils.equals(observer.getAppId(), appId)) {
				return observer;
			}
		}
		return null;
	}

	/**
	 * MSC内部通知监听者
	 * 业务不可调用
	 */
	public void notifyObserver(String appId, MSCAppLifecycle action,
		MSCAppLifecycleParams params) {
		MSCAppLifecycleObserver observer = getObserver(appId);
		if (observer == null || !isLifecycleValid(appId, action)) {
			return;
		}

		long startTime = System.currentTimeMillis();
		try {
			observer.onEvent(action, params);
		} finally {
			mscReporter.record(ReporterFields.LIFECYCLE_COST_TIME)
					.tag("event", action)
					.tag("appId", appId)
					.durationEnd(startTime)
					.sendDelay();
		}
	}

	public List<MSCAppLifecycle> findValidLifecycleList(String appId) {
		for (MSCAppLifecycleObserver observer : lifecycleObserverList) {
			if (TextUtils.equals(observer.getAppId(), appId)) {
				return observer.getValidLifecycleList();
			}
		}
		return null;
	}

	private boolean isLifecycleValid(String appId, MSCAppLifecycle action) {
		List<MSCAppLifecycle> validLifecycleList = findValidLifecycleList(appId);
		for (MSCAppLifecycle validLifecycle : validLifecycleList) {
			if (validLifecycle == action) {
				return true;
			}
		}
		return false;
	}
}
