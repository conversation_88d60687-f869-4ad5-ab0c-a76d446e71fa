package com.meituan.msc.modules.api.appLifecycle;

import java.util.List;

/**
 * 生命周期感知监听者
 * 仅针对page，不关注widget
 * https://km.sankuai.com/collabpage/1843778778
 */

public interface MSCAppLifecycleObserver {

	/**
	 * 生命周期感知回调接口
	 * @param mscAppLifecycleName 生命周期名称
	 * @param params 生命周期回调参数
	 */
	void onEvent(MSCAppLifecycle mscAppLifecycleName, MSCAppLifecycleParams params);

	/**
	 * 业务提供appId
	 * @return 小程序id
	 */
	String getAppId();

	/**
	 * 业务提供需要感知的生命周期列表
	 * @return 需要感知的生命周期列表
	 */
	List<MSCAppLifecycle> getValidLifecycleList();
}
