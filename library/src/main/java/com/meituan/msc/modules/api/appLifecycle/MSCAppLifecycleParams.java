package com.meituan.msc.modules.api.appLifecycle;

import android.os.Parcel;
import android.os.Parcelable;
import javax.annotation.Nullable;

/**
 * 生命周期回调参数
 */
public class MSCAppLifecycleParams implements Parcelable {

	/**
	 * 目标路径
	 * 对应生命周期：MSC_WILL_ENTER_APP_LIFECYCLE,
	 * MSC_DID_ENTER_APP_LIFECYCLE,
	 * MSC_WILL_LEAVE_APP_LIFECYCLE,
	 * MSC_LAUNCH_FAIL_APP_LIFECYCLE
	 */
	@Nullable
	public String targetPath;

	/**
	 * imeituan协议跳链
	 * 对应生命周期：MSC_WILL_ENTER_APP_LIFECYCLE,
	 * MSC_DID_ENTER_APP_LIFECYCLE,
	 * MSC_WILL_LEAVE_APP_LIFECYCLE,
	 * MSC_LAUNCH_FAIL_APP_LIFECYCLE
	 */
	@Nullable
	public String enterUri;

	/**
	 * 退出app信息 对应生命周期：MSC_WILL_LEAVE_APP_LIFECYCLE
	 * 当业务调用NavigateBackNative时，携带的参数
	 */
	@Nullable
	public String leaveAppInfo;


	public static final Creator<MSCAppLifecycleParams> CREATOR = new Creator<MSCAppLifecycleParams>() {
		@Override
		public MSCAppLifecycleParams createFromParcel(Parcel source) {
			return new MSCAppLifecycleParams(source);
		}

		@Override
		public MSCAppLifecycleParams[] newArray(int size) {
			return new MSCAppLifecycleParams[size];
		}
	};

	private MSCAppLifecycleParams(Parcel in) {
		this.targetPath = in.readString();
	}

	public MSCAppLifecycleParams(@Nullable String targetPath, @Nullable String enterUri,
		@Nullable String leaveAppInfo) {
		this.targetPath = targetPath;
		this.enterUri = enterUri;
		this.leaveAppInfo = leaveAppInfo;
	}

	public MSCAppLifecycleParams() {
	}

	@Override
	public int describeContents() {
		return 0;
	}

	@Override
	public void writeToParcel(Parcel dest, int flags) {
		dest.writeString(targetPath);
	}

}
