package com.meituan.msc.modules.api.appLifecycle;

import android.os.Parcel;
import android.os.Parcelable;

public class MSCAppLifecycleParcel implements Parcelable {

	MSCAppLifecycle mscAppLifecycle;
	String appId;
	MSCAppLifecycleParams mscAppLifecycleParams;

	public static final Creator<MSCAppLifecycleParcel> CREATOR = new Creator<MSCAppLifecycleParcel>() {
		@Override
		public MSCAppLifecycleParcel createFromParcel(Parcel source) {
			return new MSCAppLifecycleParcel(source);
		}

		@Override
		public MSCAppLifecycleParcel[] newArray(int size) {
			return new MSCAppLifecycleParcel[size];
		}
	};

	private MSCAppLifecycleParcel(Parcel in) {
		this.mscAppLifecycle = in.readParcelable(MSCAppLifecycle.class.getClassLoader());
		this.appId = in.readString();
		this.mscAppLifecycleParams = in
			.readParcelable(MSCAppLifecycleParams.class.getClassLoader());
	}

	public MSCAppLifecycleParcel(MSCAppLifecycle mscAppLifecycle, String appId,
		MSCAppLifecycleParams mscAppLifecycleParams) {
		this.mscAppLifecycle = mscAppLifecycle;
		this.appId = appId;
		this.mscAppLifecycleParams = mscAppLifecycleParams;
	}

	@Override
	public int describeContents() {
		return 0;
	}

	@Override
	public void writeToParcel(Parcel dest, int flags) {
		dest.writeParcelable(mscAppLifecycle, 0);
		dest.writeString(appId);
		dest.writeParcelable(mscAppLifecycleParams, 0);
	}
}
