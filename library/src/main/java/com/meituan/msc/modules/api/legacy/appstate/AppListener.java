package com.meituan.msc.modules.api.legacy.appstate;

import com.meituan.msc.jse.bridge.JavaScriptModule;

/**
 * https://km.sankuai.com/page/1291879235
 * Created by letty on 2022/3/15.
 **/
public interface AppListener extends JavaScriptModule {
    /**
     * 页面路由
     */
    void onAppRoute(String params, int viewId);

    /**
     * 容器进入前台
     */
    void onAppEnterForeground(String params, int viewId);

    /**
     * 容器进入后台
     */
    void onAppEnterBackground(String params, int viewId);

    /**
     * APP进入前台
     *
     * RN 使用
     */
    void onNativeAppEnterForeground();

    /**
     * APP进入后台
     *
     * RN 使用
     */
    void onNativeAppEnterBackground();

    /**
     * 页面焦点变化
     * RN 中使用，用于支持分屏等
     */
    void onFocusChange(boolean hasFocus, int viewId);

    /**
     * 内存警告
     */
    void onMemoryWarning(int level);

    void onPerformanceDataChange(String param);

    /**
     * 逻辑层预初始化
     */
    void onPagePreload(String params, int viewId);
    /**
     * 逻辑层预初始化，pageId为-1时，不向前端传pageId
     */
    void onPagePreload(String params);

    /**
     * 发送时机：冷启、基础库预热场景首页基础包注入完成且获取页面路径，触发页面依赖bundle提前注入 TODO onAppRoute提前后可删除
     * @param params JsonObject类型参数，含页面路径
     */
    void onResourceReady(String params);
}
