package com.meituan.msc.modules.api.legacy.appstate;

/*
 * Copyright (c) Facebook, Inc. and its affiliates.
 *
 * This source code is licensed under the MIT license found in the
 * LICENSE file in the root directory of this source tree.
 */

import android.app.Activity;

import com.meituan.android.common.metricx.helpers.AppBus;
import com.meituan.msc.modules.engine.MSCRuntime;
import com.meituan.msc.modules.manager.IMSCCompletableCallback;
import com.meituan.msc.modules.manager.MSCMethod;
import com.meituan.msc.modules.manager.MSCModule;
import com.meituan.msc.modules.manager.ModuleName;
import com.meituan.msc.modules.page.IPageModule;
import com.meituan.msc.modules.page.RouteReporter;

import org.json.JSONException;
import org.json.JSONObject;

import javax.annotation.Nullable;

@ModuleName(name = "AppState")
public class AppStateModule extends MSCModule {
    public static final String TAG = AppStateModule.class.getSimpleName();
    public static final String APP_STATE_ACTIVE = "active";
    public static final String APP_STATE_BACKGROUND = "background";

    private static final String INITIAL_STATE = "initialAppState";

    private volatile String mAppState;

    private final AppBus.OnForegroundListener foregroundListener = new AppBus.OnForegroundListener() {
        @Override
        public void onForeground() {
            mAppState = APP_STATE_ACTIVE;
            getAppListener().onNativeAppEnterForeground();
        }
    };

    private final AppBus.OnBackgroundListener backgroundListener = new AppBus.OnBackgroundListener() {
        @Override
        public void onBackground() {
            mAppState = APP_STATE_BACKGROUND;
            getAppListener().onNativeAppEnterBackground();
        }
    };

    @Override
    public void onRuntimeAttached(MSCRuntime runtime) {
        super.onRuntimeAttached(runtime);
        /**
         * 这两个回调 预热场景有较大概率在Service未准备好时触发调用
         */
        AppBus.getInstance().register(foregroundListener, true);
        AppBus.getInstance().register(backgroundListener, true);
//        ApplicationLifecycleMonitor.ALL.addReEnterForegroundListener(foregroundListener);
//        ApplicationLifecycleMonitor.ALL.addEnterBackgroundListener(backgroundListener);
//        mAppState = ApplicationLifecycleMonitor.ALL.isForeground() ? APP_STATE_ACTIVE : APP_STATE_BACKGROUND;
    }

    @Override
    public void onDestroy() {
        AppBus.getInstance().unregister(backgroundListener);
        AppBus.getInstance().unregister(foregroundListener);
        super.onDestroy();
    }

    public JSONObject getTypedExportedConstants() {
        JSONObject constants = new JSONObject();
        try {
            constants.put(INITIAL_STATE, mAppState);
        } catch (JSONException e) {
            e.printStackTrace();
        }
        return constants;
    }

    @MSCMethod
    public void getCurrentAppState(IMSCCompletableCallback<JSONObject> callback) {
        callback.onComplete(createStateChangeParams());
    }

    @MSCMethod(isSync = true)
    public JSONObject getCurrentAppStateSync() {
        return createStateChangeParams();
    }

    private JSONObject createStateChangeParams() {
        JSONObject state = new JSONObject();
        try {
//            state.put("app_state",
//                    AppBus.getInstance().isForeground() ? APP_STATE_ACTIVE : APP_STATE_BACKGROUND);
            state.put("app_state", mAppState);
        } catch (JSONException e) {
            e.printStackTrace();
        }
        try {
            state.put("msc_state",
                    getRuntime().getContainerManagerModule().isAppForeground() ? "foreground" : "background");
        } catch (JSONException e) {
            e.printStackTrace();
        }
        return state;
    }

    @MSCMethod
    public void addListener(String eventName) {
    }

    @MSCMethod
    public void removeListeners(double count) {
    }

    @Override
    public final @Nullable
    JSONObject getConstants() {
        return getTypedExportedConstants();
    }

    public void onAppEnterForeground(final String params, final int viewId, final boolean isActivity) {
        if (isActivity) {
            getAppListener().onAppEnterForeground(params, viewId);
        } else {
            getRuntime().getJSModuleDelegate(WidgetListener.class).onWidgetEnterForeground(params, viewId);
        }
    }

    public void onAppEnterBackground(final String params, final int viewId, final boolean isActivity) {
        if (isActivity) {
            getAppListener().onAppEnterBackground(params, viewId);
        } else {
            getRuntime().getJSModuleDelegate(WidgetListener.class).onWidgetEnterBackground(params, viewId);
        }
    }

    public void reportNavigateBackRouteStart(Activity activity, boolean isWidget) {
        int pageId = getTopPageId();
        RouteReporter.create(getRuntime()).reportRouteStart(activity, "navigateBack", pageId, "", getTopPagePath(), isWidget);
    }

    private String getTopPagePath() {
        IPageModule topPage = getRuntime().getContainerManagerModule().getTopPage();
        return topPage == null ? "" : topPage.getPagePath();
    }

    private int getTopPageId() {
        IPageModule topPage = getRuntime().getContainerManagerModule().getTopPage();
        return topPage == null ? -1 : topPage.getId();
    }

    public void onAppRoute(final String params, final int viewId) {
        getAppListener().onAppRoute(params, viewId);
    }

    public void onMemoryWarning(final int level) {
        getAppListener().onMemoryWarning(level);
    }

    public void onWindowFocusChange(final boolean hasFocus, final int viewId) {
        getAppListener().onFocusChange(hasFocus, viewId);
    }

    public void onPagePreload(final String params, final int viewId) {
        if (viewId == -1) {
            getAppListener().onPagePreload(params);
        } else {
            getAppListener().onPagePreload(params, viewId);
        }
    }

    public void onResourceReady(final String params) {
        getAppListener().onResourceReady(params);
    }

    AppListener getAppListener() {
        return getRuntime().getJSModuleDelegate(AppListener.class, null);
    }
}
