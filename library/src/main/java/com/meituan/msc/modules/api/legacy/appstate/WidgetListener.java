package com.meituan.msc.modules.api.legacy.appstate;

import com.meituan.msc.jse.bridge.JavaScriptModule;
import com.meituan.msc.jse.bridge.LogMethodInvokeModule;

/**
 * Created by letty on 2022/3/18.
 **/
public interface WidgetListener extends JavaScriptModule, LogMethodInvokeModule {

    void onWidgetEnterForeground(String params, int viewId);

    void onWidgetEnterBackground(String params, int viewId);

    void onWidgetSizeChanged(String params, int viewId);

    void onWidgetDataChange(String params, int viewId);

}
