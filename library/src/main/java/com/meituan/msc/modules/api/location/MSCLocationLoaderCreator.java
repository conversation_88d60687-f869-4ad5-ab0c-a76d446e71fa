package com.meituan.msc.modules.api.location;

import android.app.Activity;
import android.support.annotation.NonNull;

import com.meituan.msc.modules.api.map.ILocationLoader;
import com.meituan.msi.provider.LocationLoaderConfig;

/**
 * <AUTHOR>
 * @date 2021/12/3.
 */
public interface MSCLocationLoaderCreator {

    @NonNull
    ILocationLoader create(@NonNull Activity activity, @NonNull LocationLoaderConfig config);
}
