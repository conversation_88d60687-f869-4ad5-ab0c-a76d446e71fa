package com.meituan.msc.modules.api.map;

import com.meituan.msi.api.location.MsiLocation;

public interface ILocation {
    /**
     * @param error    STATUS_SUCCESS = 0; //定位成功，即能确定当前定位点／坐标；
     *                 STATUS_SINGLE_WIFI_WITHOUT_CELL = 1;//单wifi且无基站
     *                 STATUS_INVALID_PARAMETERS = 2;//获取的定位参数无效，可能是获取过程中异常；
     *                 STATUS_NETWORK_ERROR = 3;//联网异常，可能是网络不通或超时等；
     *                 STATUS_JSON_ERROR = 4;//返回的JSON格式错误，解析异常；
     *                 STATUS_SERVER_ERROR = 5;//无法定位，服务器异常或数据缺失等；
     *                 STATUS_AUTH_FAILED = 6; //鉴权不通过
     *                 STATUS_CLIENT_EXCEPTION = 7;//客户端其他异常
     *                 STATUS_INIT_FAILED = 8;//客户端初始化失败
     *                 STATUS_PERMISSONS_ERROR = 9;//缺少权限
     *                 STATUS_HTTP_HIJACK_RESPONSE = 10;//网络未连接
     * @param location 位置对象
     * @param errMsg   错误消息  默认为空，如果没有权限返回"auth denied"
     */
    void onLocation(int error, MsiLocation location, String errMsg);
}
