package com.meituan.msc.modules.api.map;

/**
 * 地图定位加载器 接入自己实现
 * startLocation后会自动调用，调用完成，mmp在合适的时机会关闭定位请求
 */
public interface ILocationLoader {
    /**
     * GPS 坐标
     */
    String TYPE_WGS84 = "wgs84";
    /**
     * 国测局坐标
     */
    String TYPE_GCJ02 = "gcj02";
    int STATUS_SUCCESS = 0; //定位成功，即能确定当前定位点／坐标；
    int STATUS_SINGLE_WIFI_WITHOUT_CELL = 1;//单wifi且无基站
    int STATUS_INVALID_PARAMETERS = 2;//获取的定位参数无效，可能是获取过程中异常；
    int STATUS_NETWORK_ERROR = 3;//联网异常，可能是网络不通或超时等；
    int STATUS_JSON_ERROR = 4;//返回的JSON格式错误，解析异常；
    int STATUS_SERVER_ERROR = 5;//无法定位，服务器异常或数据缺失等；
    int STATUS_AUTH_FAILED = 6; //鉴权不通过
    int STATUS_CLIENT_EXCEPTION = 7;//客户端其他异常
    int STATUS_INIT_FAILED = 8;//客户端初始化失败
    int STATUS_PERMISSONS_ERROR = 9;//缺少权限
    int STATUS_HTTP_HIJACK_RESPONSE = 10;//网络未连接

    /**
     * 地图view请求加载，接入需要实现定位请求和分发逻辑，startLocation为持续定位，在非地图页面使用，获取到位置后mmp会自动调用关闭定位，
     * 在地图中使用时，地图销毁时会自动停止
     *
     * @param mapLocation
     * @param type        类型 gcj02 wgs84 非mt app需要自己转换坐标系，详情参阅SampleLocationLoader
     */
    void startLocation(ILocation mapLocation, String type);
//
//    /**
//     * 只有bearing参数正确是才会旋转，自定义的定位需要注意
//     * 获取位置成功，地图view位置回调，接入只关心传入即可
//     * @param error 状态码，正确返回0
//     * @param location
//     */
//    void onMapLocationDispatch(int error, Location location, String errMsg);

    /**
     * 地图请求关闭定位，此处可能由多个地图，实现需要注意IMapLocation是否被销毁
     */
    void stopLocation();

}
