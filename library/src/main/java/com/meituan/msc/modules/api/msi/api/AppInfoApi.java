package com.meituan.msc.modules.api.msi.api;

import com.meituan.msc.modules.api.msi.MSCApi;
import com.meituan.msc.modules.engine.MSCRuntime;
import com.meituan.msc.modules.update.MSCAppModule;
import com.meituan.msi.annotations.MsiApiEnv;
import com.meituan.msi.annotations.MsiApiMethod;
import com.meituan.msi.annotations.MsiSupport;
import com.meituan.msi.api.IMsiApi;
import com.meituan.msi.bean.ContainerInfo;
import com.meituan.msi.bean.MsiContext;
import com.sankuai.meituan.serviceloader.annotation.ServiceLoaderInterface;

/**
 * Created by letty on 2022/2/11.
 **/
@ServiceLoaderInterface(key = "msc_appInfo", interfaceClass = IMsiApi.class)
@MsiApiEnv(name = ContainerInfo.ENV_MSC)
public class AppInfoApi extends MSCApi {

    /**
     * MMP-6282255 feature getAccountInfoSync()
     * https://km.sankuai.com/page/*********
     * 小程序配置信息
     */
    @MsiApiMethod(name = "getAccountInfoSync", response = AccountInfo.class)
    public AccountInfo getAccountInfoSync(MsiContext context) {
        AccountInfo accountInfo = new AccountInfo();
        MSCRuntime mscRuntime = getRuntime();
        MSCAppModule mscAppModule = mscRuntime.getMSCAppModule();
        if (mscAppModule.hasMetaInfo()) {
            AccountInfo.MiniProgram miniProgram = accountInfo.miniProgram;

            miniProgram.appName = mscAppModule.getAppName();
            miniProgram.appId = mscRuntime.getAppId();

            String version = mscAppModule.getMSCAppVersion();
            if (version == null) {
                version = "";
            }
            //开发者配置版本号
            miniProgram.version = version;
            //开发者后台发布编号
            miniProgram.release = mscAppModule.getPublishId();
            miniProgram.icon = mscAppModule.getIconUrl();
            miniProgram.envVersion = mscAppModule.isDebug() ? "develop" : "release";
            miniProgram.buildId = mscAppModule.getBuildId();
        }
        return accountInfo;
    }

    @MsiSupport
    public static class AccountInfo {
        MiniProgram miniProgram = new MiniProgram();

        @MsiSupport
        static class MiniProgram {
            String appId;
            String appName;
            String version;
            String release;
            String icon;
            String envVersion;
            String buildId;
        }
    }
}
