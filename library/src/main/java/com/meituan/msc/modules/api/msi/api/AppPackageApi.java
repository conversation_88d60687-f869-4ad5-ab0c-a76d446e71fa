package com.meituan.msc.modules.api.msi.api;

import static com.meituan.msc.modules.api.msi.MSCErrorCode.ERROR_CODE_API_COMMON_INVALID_PARAM;
import android.text.TextUtils;
import com.meituan.msc.modules.api.msi.MSCApi;
import com.meituan.msc.modules.reporter.MSCLog;
import com.meituan.msc.modules.update.metainfo.MetaFetchRulerManager;
import com.meituan.msi.annotations.MsiApiEnv;
import com.meituan.msi.annotations.MsiApiMethod;
import com.meituan.msi.annotations.MsiParamChecker;
import com.meituan.msi.annotations.MsiSupport;
import com.meituan.msi.api.IMsiApi;
import com.meituan.msi.api.MSIError;
import com.meituan.msi.bean.ContainerInfo;
import com.meituan.msi.bean.MsiContext;
import com.sankuai.meituan.serviceloader.annotation.ServiceLoaderInterface;

import java.util.HashMap;
import java.util.Map;

@ServiceLoaderInterface(key = "mscAppPackageApi", interfaceClass = IMsiApi.class)
@MsiApiEnv(name = ContainerInfo.ENV_MSC)
public class AppPackageApi extends MSCApi {
    private static final String TAG = "AppPackageApi";
    @MsiApiMethod(name = "mtRemovePkgExtraParamPersist", env = {"msc"}, scope = "msc", request = PkgExtraParam.class)
    public void removePkgExtraParamPersist(PkgExtraParam param, MsiContext context) {
        if (TextUtils.isEmpty(param.key)) {
            context.onError("key is empty", MSIError.getGeneralError(ERROR_CODE_API_COMMON_INVALID_PARAM));
            return;
        }
        MetaFetchRulerManager.getInstance().removePkgExtraParamPersist(getAppId(), param.key);
        context.onSuccess(null);
    }

    @MsiApiMethod(name = "mtAddPkgExtraParamPersist", env = {"msc"}, scope = "msc", request = ApiPkgExtraParams.class)
    public void addPkgExtraParamPersist(ApiPkgExtraParams param, MsiContext context) {
        if (param == null || param.pkgExtraParams == null || param.pkgExtraParams.isEmpty()) {
            context.onError("pkgExtraParams is empty", MSIError.getGeneralError(ERROR_CODE_API_COMMON_INVALID_PARAM));
            return;
        }

        for (Map.Entry<String, String> entry : param.pkgExtraParams.entrySet()) {
            if (entry == null) {
                continue;
            }
            String key = entry.getKey();
            String value = entry.getValue();
            if (!TextUtils.isEmpty(key) && !TextUtils.isEmpty(value)) {
                MSCLog.i(TAG, "addPkgExtraParamPersist key:" + key + " value:" + value);
                MetaFetchRulerManager.getInstance().addPkgExtraParamPersist(getAppId(), key, value);
            }
        }

        context.onSuccess(null);
    }

    @MsiApiMethod(name = "mtClearPkgExtraParamsPersist", env = {"msc"}, scope = "msc")
    public void clearPkgExtraParamsPersist(MsiContext context) {
        MetaFetchRulerManager.getInstance().clearPkgExtraParamsPersist(getAppId());
        context.onSuccess(null);
    }

    @MsiApiMethod(name = "mtGetPkgExtraParamsPersist", env = {"msc"}, scope = "msc", response = ApiPkgExtraParams.class)
    public void getPkgExtraParamsPersist(MsiContext context) {
        Map<String, String> pkgExtraParams = MetaFetchRulerManager.getInstance().getPkgExtraParamsPersist(getAppId());
        if (pkgExtraParams == null) {
            pkgExtraParams = new HashMap<>();
        }
        MSCLog.i(TAG, "getPkgExtraParamsPersist: " + pkgExtraParams.toString());
        ApiPkgExtraParams response = new ApiPkgExtraParams();
        response.pkgExtraParams = pkgExtraParams;
        context.onSuccess(response);
    }

    @MsiSupport
    public static class PkgExtraParam {
        @MsiParamChecker(required = true)
        public String key;

        public String value;
    }

    @MsiSupport
    public static class ApiPkgExtraParams {
        public Map<String, String> pkgExtraParams;
    }
}
