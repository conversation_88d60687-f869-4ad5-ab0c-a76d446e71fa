package com.meituan.msc.modules.api.msi.api;

import android.text.TextUtils;

import com.meituan.msc.common.utils.ColorUtil;
import com.meituan.msc.modules.api.msi.MSCApi;
import com.meituan.msc.modules.api.msi.MSCErrorCode;
import com.meituan.msc.modules.engine.MSCHornRollbackConfig;
import com.meituan.msc.modules.page.IPageModule;
import com.meituan.msi.annotations.MsiApiEnv;
import com.meituan.msi.annotations.MsiApiMethod;
import com.meituan.msi.annotations.MsiParamChecker;
import com.meituan.msi.annotations.MsiSupport;
import com.meituan.msi.api.IMsiApi;
import com.meituan.msi.api.MSIError;
import com.meituan.msi.bean.ContainerInfo;
import com.meituan.msi.bean.MsiContext;
import com.sankuai.meituan.serviceloader.annotation.ServiceLoaderInterface;

@ServiceLoaderInterface(key = "msc_background", interfaceClass = IMsiApi.class)
@MsiApiEnv(name = ContainerInfo.ENV_MSC)
public class BackgroundApi extends MSCApi {

    @MsiSupport
    public static class BackgroundColorParams {
        String backgroundColor;
    }

    @MsiApiMethod(name = "setBackgroundColor", request = BackgroundColorParams.class, onUiThread = true)
    public void setBackgroundColor(BackgroundColorParams params, MsiContext context) {
        int pageId = getPageId(context);
        IPageModule pageModule = getPageById(pageId);
        if (pageModule == null) {
            pageNotFoundCallback(context, pageId, MSCErrorCode.ERROR_CODE_API_COMMON_MEET_EXPECTATIONS);
            return;
        }
        try {
            int color = ColorUtil.parseRGBAColor(params.backgroundColor);
            pageModule.setBackgroundColor(color);
            succeedCallback(context);
        } catch (Exception e) {
            context.onError("illegal argument name: color", MSIError.getGeneralError(MSCErrorCode.ERROR_CODE_API_COMMON_INVALID_PARAM));
        }
    }

    @MsiSupport
    public static class BackgroundTextStyleParams {
        @MsiParamChecker(required = true)
        public String textStyle;
    }

    @MsiApiMethod(name = "setBackgroundTextStyle", request = BackgroundTextStyleParams.class, onUiThread = true)
    public void setBackgroundTextStyle(BackgroundTextStyleParams params, MsiContext context) {
        int pageId = getPageId(context);
        IPageModule pageModule = getPageById(pageId);
        if (pageModule == null) {
            pageNotFoundCallback(context, pageId, MSCErrorCode.ERROR_CODE_API_COMMON_MEET_EXPECTATIONS);
            return;
        }
        pageModule.setBackgroundTextStyle(TextUtils.equals(params.textStyle, "dark"));
        succeedCallback(context);
    }
}
