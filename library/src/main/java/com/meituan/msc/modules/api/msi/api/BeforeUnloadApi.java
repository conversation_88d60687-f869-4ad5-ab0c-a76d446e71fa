package com.meituan.msc.modules.api.msi.api;

import com.meituan.msc.modules.api.msi.MSCApi;
import com.meituan.msc.modules.api.msi.MSCErrorCode;
import com.meituan.msc.modules.engine.MSCHornRollbackConfig;
import com.meituan.msc.modules.page.IPageModule;
import com.meituan.msi.annotations.MsiApiEnv;
import com.meituan.msi.annotations.MsiApiMethod;
import com.meituan.msi.api.IMsiApi;
import com.meituan.msi.api.MSIError;
import com.meituan.msi.bean.ContainerInfo;
import com.meituan.msi.bean.MsiContext;
import com.sankuai.meituan.serviceloader.annotation.ServiceLoaderInterface;

@ServiceLoaderInterface(key = "msc_pageApi", interfaceClass = IMsiApi.class)
@MsiApiEnv(name = ContainerInfo.ENV_MSC)
public class BeforeUnloadApi extends MSCApi {

    @MsiApiMethod(name = "enableBeforeUnload", onUiThread = true)
    public void enableBeforeUnload(MsiContext context) {
        int pageId = getPageId(context);
        IPageModule pageModule = getPageById(getPageId(context));
        if (pageModule == null) {
            context.onError("no page available", MSIError.getIgnoreError(MSCErrorCode.ERROR_CODE_API_COMMON_MEET_EXPECTATIONS));
            return;
        }
        pageModule.setEnableBackActionIntercept(true);
        context.onSuccess("enableBeforeUnload pageId:" + pageId);
    }

    @MsiApiMethod(name = "disableBeforeUnload", onUiThread = true)
    public void disableBeforeUnload(MsiContext context) {
        int pageId = getPageId(context);
        IPageModule pageModule = getPageById(pageId);
        if (pageModule == null) {
            context.onError("no page available", MSIError.getIgnoreError(MSCErrorCode.ERROR_CODE_API_COMMON_MEET_EXPECTATIONS));
            return;
        }
        pageModule.setEnableBackActionIntercept(false);
        context.onSuccess("disableBeforeUnload pageId:" + pageId);
    }

    @MsiApiMethod(name = "onPageBeforeUnload", response = PageBeforeUnloadParam.class, isCallback = true)
    public void onPageBeforeUnload(){

    }
}
