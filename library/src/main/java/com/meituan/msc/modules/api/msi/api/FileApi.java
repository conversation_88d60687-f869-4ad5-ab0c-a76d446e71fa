package com.meituan.msc.modules.api.msi.api;

import com.meituan.msc.lib.interfaces.IFileModule;
import com.meituan.msc.modules.api.msi.MSCApi;
import com.meituan.msc.modules.api.msi.MSCErrorCode;
import com.meituan.msc.modules.engine.MSCHornRollbackConfig;
import com.meituan.msc.modules.engine.MSCRuntime;
import com.meituan.msi.annotations.MsiApiEnv;
import com.meituan.msi.annotations.MsiApiMethod;
import com.meituan.msi.annotations.MsiSupport;
import com.meituan.msi.api.IMsiApi;
import com.meituan.msi.api.MSIError;
import com.meituan.msi.bean.ContainerInfo;
import com.meituan.msi.bean.MsiContext;
import com.sankuai.meituan.serviceloader.annotation.ServiceLoaderInterface;

/**
 * Created by letty on 2023/4/3.
 **/
@ServiceLoaderInterface(key = "msc_FileProtocolApi", interfaceClass = IMsiApi.class)
@MsiApiEnv(name = ContainerInfo.ENV_MSC)
public class FileApi extends MSCApi {

    @MsiApiMethod(name = "getFileProtocol", response = FileProtocolResponse.class)
    public FileProtocolResponse getFileProtocol(MsiContext context) {
        MSCRuntime runtime = getRuntime();
        if (runtime == null) {
            context.onError("runtime is null", MSIError.getIgnoreError(MSCErrorCode.ERROR_CODE_API_COMMON_MEET_EXPECTATIONS));
            return null;
        }
        FileProtocolResponse fileProtocolResponse = new FileProtocolResponse();
        fileProtocolResponse.fileProtocol =  getModule(IFileModule.class).getContainerFileScheme();
        context.onSuccess(fileProtocolResponse);
        return fileProtocolResponse;
    }

    @MsiSupport
    public static class FileProtocolResponse {
        public String fileProtocol;
    }
}

