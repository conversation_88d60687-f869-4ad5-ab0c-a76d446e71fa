package com.meituan.msc.modules.api.msi.api;

import com.google.gson.JsonElement;
import com.meituan.msc.modules.api.msi.MSCApi;
import com.meituan.msc.modules.api.msi.MSCErrorCode;
import com.meituan.msc.modules.engine.MSCHornRollbackConfig;
import com.meituan.msc.modules.page.render.webview.RenderCacheHelper;
import com.meituan.msc.modules.update.AppConfigModule;
import com.meituan.msi.annotations.MsiApiEnv;
import com.meituan.msi.annotations.MsiApiMethod;
import com.meituan.msi.annotations.MsiSupport;
import com.meituan.msi.api.IMsiApi;
import com.meituan.msi.api.MSIError;
import com.meituan.msi.bean.ContainerInfo;
import com.meituan.msi.bean.MsiContext;
import com.sankuai.meituan.serviceloader.annotation.ServiceLoaderInterface;

/**
 * Created by letty on 2022/3/16.
 **/
@ServiceLoaderInterface(key = "msc_initialCache", interfaceClass = IMsiApi.class)
@MsiApiEnv(name = ContainerInfo.ENV_MSC)
public class InitialCacheApi extends MSCApi {
    //非Dynamic状态，不允许设置
    private static final int ERROR_CODE_SET_INITIAL_RENDERING_CACHE_NOT_DYNAMIC = 800000200;

    @MsiSupport
    public static class InitialCacheParams {
        public String pageName;
        // msi 解析不支持使用JSONObject对象，需要使用gson中的JsonObject对象；为了能够正确处理null，此处使用JsonElement
        public JsonElement cache;
        public String cacheTemplate;
    }

    @MsiApiMethod(name = "setInitialRenderingCache", request = InitialCacheParams.class)
    public void setInitialRenderingCache(InitialCacheParams params, MsiContext context) {
        // 获取前端pageName参数
        final String pageName = params.pageName;
        // 如果非Dynamic状态 不允许设置。
        if (getRuntime().getMSCAppModule().obtainInitialRenderingCacheState(pageName) != AppConfigModule.InitialRenderingCacheState.DYNAMIC) {
            if (MSCHornRollbackConfig.enableUseNewFormatMsiApiErrno()) {
                context.onError("current renderingCacheState is not dynamic", MSIError.getGeneralError(ERROR_CODE_SET_INITIAL_RENDERING_CACHE_NOT_DYNAMIC));
            } else {
                context.onError("current renderingCacheState is not dynamic", MSIError.getGeneralError(MSCErrorCode.ERROR_SET_INITIAL_RENDERING_CACHE_NOT_DYNAMIC));
            }
            return;
        }
        RenderCacheHelper.saveDynamicInitialRenderCache(params, getRuntime());
        context.onSuccess(null);

    }

}
