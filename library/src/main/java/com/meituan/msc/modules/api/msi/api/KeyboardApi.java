package com.meituan.msc.modules.api.msi.api;

import android.app.Activity;

import com.meituan.msc.common.utils.InputMethodUtil;
import com.meituan.msc.modules.api.msi.MSCApi;
import com.meituan.msc.modules.api.msi.MSCErrorCode;
import com.meituan.msc.modules.container.IContainerDelegate;
import com.meituan.msc.modules.engine.MSCHornRollbackConfig;
import com.meituan.msc.modules.engine.MSCRuntime;
import com.meituan.msc.modules.reporter.MSCLog;
import com.meituan.msi.annotations.MsiApiEnv;
import com.meituan.msi.annotations.MsiApiMethod;
import com.meituan.msi.annotations.MsiParamChecker;
import com.meituan.msi.annotations.MsiSupport;
import com.meituan.msi.api.IMsiApi;
import com.meituan.msi.api.MSIError;
import com.meituan.msi.bean.ContainerInfo;
import com.meituan.msi.bean.MsiContext;
import com.sankuai.meituan.serviceloader.annotation.ServiceLoaderInterface;

@ServiceLoaderInterface(key = "msc_keyboardApi", interfaceClass = IMsiApi.class)
@MsiApiEnv(name = ContainerInfo.ENV_MSC)
public class KeyboardApi extends MSCApi {

    private static final String TAG = "KeyboardApi";

    @MsiApiMethod(name = "hideKeyboard", onUiThread = false, scope="msc")
    public void hideKeyboard(MsiContext context) {

        MSCRuntime runtime = getRuntime();
        if (runtime == null) {
            if (MSCHornRollbackConfig.enableUseNewFormatMsiApiErrno()) {
                context.onError("runtime is null", MSIError.getIgnoreError(MSCErrorCode.ERROR_CODE_API_COMMON_MEET_EXPECTATIONS));
            } else {
                context.onError("runtime is null", MSIError.getGeneralError(MSCErrorCode.ERROR_HIDE_KEYBOARD_RUNTIME_IS_NULL));
            }
            return;
        }

        IContainerDelegate containerDelegate = getContainerDelegate(context);
        if (containerDelegate == null) {
            MSCLog.i(TAG, "containerDelegate null, appId = ", getAppId());
            if (MSCHornRollbackConfig.enableUseNewFormatMsiApiErrno()) {
                context.onError("containerDelegate is null", MSIError.getIgnoreError(MSCErrorCode.ERROR_CODE_API_COMMON_MEET_EXPECTATIONS));
            } else {
                context.onError("containerDelegate is null", MSIError.getGeneralError(MSCErrorCode.ERROR_HIDE_KEYBOARD_CONTAINER_DELEGATE_IS_NULL));
            }
            return;
        }

        Activity activity = containerDelegate.getActivity();
        if (activity == null) {
            MSCLog.i(TAG, "activity null, appId = ", getAppId());
            if (MSCHornRollbackConfig.enableUseNewFormatMsiApiErrno()) {
                context.onError("activity is null", MSIError.getIgnoreError(MSCErrorCode.ERROR_CODE_API_COMMON_MEET_EXPECTATIONS));
            } else {
                context.onError("activity is null", MSIError.getGeneralError(MSCErrorCode.ERROR_HIDE_KEYBOARD_ACTIVITY_IS_NULL));
            }
            return;
        }
        InputMethodUtil.hideSoftKeyboard(activity);
        context.onSuccess(null);
    }

    @MsiApiMethod(name = "onKeyboardHeightChange", response = OnKeyboardHeightChangeParams.class, isCallback = true)
    public void onKeyboardHeightChange(){

    }

    @MsiSupport
    public static class OnKeyboardHeightChangeParams {
        @MsiParamChecker(required = true)
        public int height;
    }

}
