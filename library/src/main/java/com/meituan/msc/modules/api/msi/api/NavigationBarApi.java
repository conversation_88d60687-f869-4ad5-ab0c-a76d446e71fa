package com.meituan.msc.modules.api.msi.api;


import com.meituan.msc.common.utils.ColorUtil;
import com.meituan.msc.modules.api.msi.MSCApi;
import com.meituan.msc.modules.api.msi.MSCErrorCode;
import com.meituan.msc.modules.engine.MSCHornRollbackConfig;
import com.meituan.msc.modules.page.IPageModule;
import com.meituan.msc.modules.page.IPageNavigationBarMethods;
import com.meituan.msi.annotations.MsiApiEnv;
import com.meituan.msi.annotations.MsiApiMethod;
import com.meituan.msi.annotations.MsiParamChecker;
import com.meituan.msi.annotations.MsiSupport;
import com.meituan.msi.api.IMsiApi;
import com.meituan.msi.api.MSIError;
import com.meituan.msi.bean.ContainerInfo;
import com.meituan.msi.bean.MsiContext;
import com.sankuai.meituan.serviceloader.annotation.ServiceLoaderInterface;

@ServiceLoaderInterface(key = "msc_navigation_bar", interfaceClass = IMsiApi.class)
@MsiApiEnv(name = ContainerInfo.ENV_MSC)
public class NavigationBarApi extends MSCApi {

    @MsiSupport
    public static class NavigationBarColorParams {
        @MsiParamChecker(required = true)
        public String frontColor;
        @MsiParamChecker(required = true)
        public String backgroundColor;
    }

    @MsiApiMethod(name = "setNavigationBarColor", request = NavigationBarColorParams.class, onUiThread = true)
    public void setNavigationBarColor(NavigationBarColorParams params, MsiContext context) {
        int pageId = getPageId(context);
        IPageNavigationBarMethods methods = getPageNavigationBarMethods(pageId);
        if (methods == null) {
            if (MSCHornRollbackConfig.enableUseNewFormatMsiApiErrno()) {
                pageNotFoundCallback(context, pageId, MSCErrorCode.ERROR_CODE_API_COMMON_MEET_EXPECTATIONS);
            } else {
                pageNotFoundCallback(context, pageId, MSCErrorCode.ERROR_SET_NAVIGATION_BAR_COLOR_CAN_NOT_FIND_PAGE);
            }
            return;
        }
        try {
            int frontColor = ColorUtil.parseRGBAColor(params.frontColor);
            int backgroundColor = ColorUtil.parseRGBAColor(params.backgroundColor);
            methods.setNavigationBarColor(frontColor, backgroundColor);
            succeedCallback(context);
        } catch (Exception e) {
            context.onError("illegal argument name: frontColor or backgroundColor", MSIError.getGeneralError(MSCErrorCode.ERROR_CODE_API_COMMON_INVALID_PARAM));
        }
    }

    @MsiSupport
    public static class NavigationBarTitleParams {
        public String title;
    }

    @MsiApiMethod(name = "setNavigationBarTitle", request = NavigationBarTitleParams.class, onUiThread = true, version = "1.0.1")
    public void setNavigationBarTitle(NavigationBarTitleParams params, MsiContext context) {
        int pageId = getPageId(context);
        IPageNavigationBarMethods methods = getPageNavigationBarMethods(pageId);
        if (methods == null) {
            if (MSCHornRollbackConfig.enableUseNewFormatMsiApiErrno()) {
                pageNotFoundCallback(context, pageId, MSCErrorCode.ERROR_CODE_API_COMMON_MEET_EXPECTATIONS);
            } else {
                pageNotFoundCallback(context, pageId, MSCErrorCode.ERROR_SET_NAVIGATION_BAR_TITLE_CAN_NOT_FIND_PAGE);
            }
            return;
        }
        methods.setNavigationBarTitle(params.title);
        succeedCallback(context);
    }

    @MsiApiMethod(name = "showNavigationBarLoading", onUiThread = true)
    public void showNavigationBarLoading(MsiContext context) {
        int pageId = getPageId(context);
        IPageNavigationBarMethods methods = getPageNavigationBarMethods(pageId);
        if (methods == null) {
            if (MSCHornRollbackConfig.enableUseNewFormatMsiApiErrno()) {
                pageNotFoundCallback(context, pageId, MSCErrorCode.ERROR_CODE_API_COMMON_MEET_EXPECTATIONS);
            } else {
                pageNotFoundCallback(context, pageId, MSCErrorCode.ERROR_SHOW_NAVIGATION_BAR_LOADING_CAN_NOT_FIND_PAGE);
            }
            return;
        }
        methods.showNavigationBarLoading();
        succeedCallback(context);
    }

    @MsiApiMethod(name = "hideNavigationBarLoading", onUiThread = true)
    public void hideNavigationBarLoading(MsiContext context) {
        int pageId = getPageId(context);
        IPageNavigationBarMethods methods = getPageNavigationBarMethods(pageId);
        if (methods == null) {
            if (MSCHornRollbackConfig.enableUseNewFormatMsiApiErrno()) {
                pageNotFoundCallback(context, pageId, MSCErrorCode.ERROR_CODE_API_COMMON_MEET_EXPECTATIONS);
            } else {
                pageNotFoundCallback(context, pageId, MSCErrorCode.ERROR_HIDE_NAVIGATION_BAR_LOADING_CAN_NOT_FIND_PAGE);
            }
            return;
        }
        methods.hideNavigationBarLoading();
        succeedCallback(context);
    }

    private IPageNavigationBarMethods getPageNavigationBarMethods(int pageId) {
        IPageModule pageModule = getPageById(pageId);
        if (pageModule == null) {
            return null;
        }
        return pageModule.getPageNavigationBarMethods();
    }
}
