package com.meituan.msc.modules.api.msi.api;

import com.meituan.msc.modules.container.ContainerController;
import com.meituan.msc.modules.engine.MSCRuntime;
import com.meituan.msc.modules.page.IPageManagerModule;
import com.meituan.msc.modules.page.IPageModule;
import com.meituan.msi.annotations.MsiSupport;

@MsiSupport
public class PageBeforeUnloadParam {

    public static final String NAVIGATION_TYPE_EXITMINIPROGRAM =  "exitMiniProgram";

    public static final String NAVIGATION_TYPE_NAVIGATEBACK =  "navigateBack";

    public static final String NAVIGATION_ONPAGE_BEFORE_UNLOAD = "onPageBeforeUnload";

    int pageId;

    String navigationType;

    public PageBeforeUnloadParam(int pageId, String navigationType) {
        this.pageId = pageId;
        this.navigationType = navigationType;
    }

    public static void sendOnPageBeforeUnload(IPageModule page,
                                              ContainerController.BackOperator opt,
                                              IPageManagerModule pageManagerModule,
                                              MSCRuntime runtime){
        String navigationType = PageBeforeUnloadParam.NAVIGATION_TYPE_NAVIGATEBACK;
        //点击退出小程序｜当前小程序只有一个页面
        if (isCurMiniAppHasOnlyOnePage(pageManagerModule, runtime) || opt == ContainerController.BackOperator.CLOSE) {
            navigationType = PageBeforeUnloadParam.NAVIGATION_TYPE_EXITMINIPROGRAM;
        }
        runtime.apisManager.dispatchEvent(NAVIGATION_ONPAGE_BEFORE_UNLOAD, new PageBeforeUnloadParam(page.getId(), navigationType));
    }

    /**
     * 当前小程序是否只有一个页面
     *
     * @return
     */
    public static boolean isCurMiniAppHasOnlyOnePage(IPageManagerModule pageManagerModule, MSCRuntime runtime) {
        return pageManagerModule.getPageCount() <= 1 && runtime.hasOnlyOneContainer();
    }
}
