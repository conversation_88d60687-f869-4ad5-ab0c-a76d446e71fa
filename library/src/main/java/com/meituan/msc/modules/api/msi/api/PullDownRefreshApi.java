package com.meituan.msc.modules.api.msi.api;

import com.meituan.msc.modules.api.msi.MSCApi;
import com.meituan.msc.modules.api.msi.MSCErrorCode;
import com.meituan.msc.modules.engine.MSCHornRollbackConfig;
import com.meituan.msc.modules.page.IPageModule;
import com.meituan.msi.annotations.MsiApiEnv;
import com.meituan.msi.annotations.MsiApiMethod;
import com.meituan.msi.api.IMsiApi;
import com.meituan.msi.bean.ContainerInfo;
import com.meituan.msi.bean.MsiContext;
import com.sankuai.meituan.serviceloader.annotation.ServiceLoaderInterface;

@ServiceLoaderInterface(key = "msc_pullDownRefresh", interfaceClass = IMsiApi.class)
@MsiApiEnv(name = ContainerInfo.ENV_MSC)
public class PullDownRefreshApi extends MSCApi {

    public static final String MSI_PULL_DOWN_REFRESH_EVENT = "onPullDownRefresh";

    @MsiApiMethod(name = "startPullDownRefresh", onUiThread = true)
    public void startPullDownRefresh(MsiContext context) {
        int pageId = getPageId(context);
        IPageModule pageModule = getPageById(pageId);
        if (pageModule == null) {
            pageNotFoundCallback(context, pageId, MSCErrorCode.ERROR_CODE_API_COMMON_MEET_EXPECTATIONS);
            return;
        }
        pageModule.startPullDownRefresh();
        succeedCallback(context);
    }

    @MsiApiMethod(name = "stopPullDownRefresh", onUiThread = true)
    public void stopPullDownRefresh(MsiContext context) {
        int pageId = getPageId(context);
        IPageModule pageModule = getPageById(pageId);
        if (pageModule == null) {
            pageNotFoundCallback(context, pageId, MSCErrorCode.ERROR_CODE_API_COMMON_MEET_EXPECTATIONS);
            return;
        }
        pageModule.stopPullDownRefresh();
        succeedCallback(context);
    }

    @MsiApiMethod(name = "onPullDownRefresh", response = PullDownRefreshParam.class, isCallback = true)
    public void onPullDownRefresh(){

    }
}
