package com.meituan.msc.modules.api.msi.api;

import android.text.TextUtils;

import com.meituan.msc.modules.api.msi.MSCApi;
import com.meituan.msc.modules.api.msi.MSCErrorCode;
import com.meituan.msc.modules.api.report.MSCReportBizTagsManager;
import com.meituan.msc.modules.engine.MSCHornRollbackConfig;
import com.meituan.msi.annotations.MsiApiEnv;
import com.meituan.msi.annotations.MsiApiMethod;
import com.meituan.msi.annotations.MsiSupport;
import com.meituan.msi.api.IMsiApi;
import com.meituan.msi.api.MSIError;
import com.meituan.msi.bean.ContainerInfo;
import com.meituan.msi.bean.MsiContext;
import com.sankuai.meituan.serviceloader.annotation.ServiceLoaderInterface;

import java.util.Map;

/**
 * MSC容器指标上报业务维度方案
 * https://km.sankuai.com/collabpage/1573692797
 */
@ServiceLoaderInterface(key = "msc_reportBizTagsApi", interfaceClass = IMsiApi.class)
@MsiApiEnv(name = ContainerInfo.ENV_MSC)
public class ReportBizTagsApi extends MSCApi {
    private static final int ERROR_CODE_MT_BIZ_TAGS_COMMON = 800000200;

    @MsiSupport
    public static class ReportBizTagsParams {
        public String targetPath;
        public String bizTagKey;
        public Map<String, String> mtBizTagsMap;
    }

    @MsiSupport
    public static class RemoveBizTagsResult {
        public String bizTagValue;
    }

    @MsiApiMethod(name = "mtAddBizTags", request = ReportBizTagsParams.class)
    public void mtAddBizTags(ReportBizTagsParams reportBizTagsParams, MsiContext context) {
        boolean result = MSCReportBizTagsManager.getInstance()
                .addBizTags(getAppId(), reportBizTagsParams.targetPath, reportBizTagsParams.mtBizTagsMap);
        if (result) {
            context.onSuccess(null);
        } else {
            if (MSCHornRollbackConfig.enableUseNewFormatMsiApiErrno()) {
                context.onError(501, "bizTags out limit", MSIError.getGeneralError(ERROR_CODE_MT_BIZ_TAGS_COMMON));
            } else {
                context.onError(501, "bizTags out limit", MSIError.getGeneralError(MSCErrorCode.ERROR_MT_ADD_BIZ_TAGS_OUT_LIMIT));
            }
        }
    }

    @MsiApiMethod(name = "mtRemoveBizTag", request = ReportBizTagsParams.class, response = RemoveBizTagsResult.class)
    public void mtRemoveBizTag(ReportBizTagsParams reportBizTagsParams, MsiContext context) {
        String bizTagValue = MSCReportBizTagsManager.getInstance()
                .removeBizTags(getAppId(), reportBizTagsParams.targetPath, reportBizTagsParams.bizTagKey);
        if (TextUtils.isEmpty(bizTagValue)) {
            if (MSCHornRollbackConfig.enableUseNewFormatMsiApiErrno()) {
                context.onError(501, "key is not exist", MSIError.getGeneralError(ERROR_CODE_MT_BIZ_TAGS_COMMON));
            } else {
                context.onError(501, "key is not exist", MSIError.getGeneralError(MSCErrorCode.ERROR_MT_REMOVE_BIZ_TAGS_NO_KEY));
            }
        } else {
            context.onSuccess(bizTagValue);
        }
    }

    @MsiApiMethod(name = "mtClearBizTags", request = ReportBizTagsParams.class)
    public void mtClearBizTags(ReportBizTagsParams reportBizTagsParams, MsiContext context) {
        MSCReportBizTagsManager.getInstance().clearBizTags(getAppId(), reportBizTagsParams.targetPath);
        context.onSuccess(null);
    }

    @MsiApiMethod(name = "mtClearAllBizTags")
    public void mtClearAllBizTags(MsiContext context) {
        MSCReportBizTagsManager.getInstance().clearAllBizTags(getAppId());
        context.onSuccess(null);
    }

    @MsiApiMethod(name = "mtGetBizTags", request = ReportBizTagsParams.class, response = MSCReportBizTagsManager.BizTagsData.class)
    public void mtGetBizTags(ReportBizTagsParams reportBizTagsParams, MsiContext context) {
        MSCReportBizTagsManager.BizTagsData bizTagsData =
                MSCReportBizTagsManager.getInstance().getBizTags(getAppId(), reportBizTagsParams.targetPath);
        context.onSuccess(bizTagsData);
    }

    @MsiApiMethod(name = "mtGetAllBizTags", request = ReportBizTagsParams.class, response = MSCReportBizTagsManager.BizTagsData.class)
    public void mtGetAllBizTags(ReportBizTagsParams reportBizTagsParams, MsiContext context) {
        MSCReportBizTagsManager.BizTagsData bizTagsData =
                MSCReportBizTagsManager.getInstance().getAllBizTags(getAppId());
        context.onSuccess(bizTagsData);
    }

}
