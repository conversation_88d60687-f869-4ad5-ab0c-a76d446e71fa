package com.meituan.msc.modules.api.msi.api;

import com.google.gson.JsonElement;
import com.google.gson.JsonObject;
import com.meituan.msc.modules.api.msi.MSCApi;
import com.meituan.msc.modules.api.msi.MSCErrorCode;
import com.meituan.msc.modules.page.IPageModule;
import com.meituan.msi.annotations.MsiApiEnv;
import com.meituan.msi.annotations.MsiApiMethod;
import com.meituan.msi.annotations.MsiSupport;
import com.meituan.msi.api.IMsiApi;
import com.meituan.msi.api.MSIError;
import com.meituan.msi.bean.ContainerInfo;
import com.meituan.msi.bean.MsiContext;
import com.sankuai.meituan.serviceloader.annotation.ServiceLoaderInterface;

/**
 * Created by letty on 2022/2/23.
 **/
@ServiceLoaderInterface(key = "msc_sharemenu", interfaceClass = IMsiApi.class)
@MsiApiEnv(name = ContainerInfo.ENV_MSC)
public class ShareMenuApi extends MSCApi {
    //共享按钮在 appconfig 中设置不支持
    private final static int ERROR_CODE_MENU_NO_CONFIG = 800000200;

    // 控制share button显示隐藏时，需要判断时业务调用还是框架自身初始化调用
    private final static String SHOW_SHAREMENU_FROM_BUSINESS = "business";
    private final static String SHOW_SHAREMENU_FROM_FRAMEWORK = "framework";

    @MsiSupport
    static class HideShareMenuParams {
    }

    @MsiSupport
    static class ShowShareMenuParams {
    }

    /**
     * https://km.sankuai.com/page/288014184
     * @param hideShareMenuParams
     * @param context
     */
    @MsiApiMethod(name = "hideShareMenu", onUiThread = true, request = HideShareMenuParams.class)
    public void hideShareMenu(HideShareMenuParams hideShareMenuParams, MsiContext context) {
        // 判断是从框架触发还是从业务代码中显示调用showShareMenu
        String eventFrom = getEventFromFromInnerArgs(context);
        // 如果不是框架调用，则不受AppConfig控制, 直接hideMoreMenu, 否则要根据AppConfig判断
        if ((!SHOW_SHAREMENU_FROM_FRAMEWORK.equals(eventFrom)) || getRuntime().getMSCAppModule().isShareSupported()) {
            int id = getPageId(context);
            IPageModule pageModule = getRuntime().getContainerManagerModule().getPageByPageId(id);
            if (pageModule != null) {
                pageModule.getPageNavigationBarMethods().hideShareMenu(SHOW_SHAREMENU_FROM_BUSINESS.equals(eventFrom));
                context.onSuccess(null);
            } else {
                context.onError("can't find page by pageId:" + id, MSIError.getIgnoreError(MSCErrorCode.ERROR_CODE_API_COMMON_MEET_EXPECTATIONS));
            }
        } else {
            context.onError("invocation from framework is blocked, share button is disabled in " +
                    "appconfig", MSIError.getGeneralError(ERROR_CODE_MENU_NO_CONFIG));
        }
    }

    @MsiApiMethod(name = "showShareMenu", onUiThread = true, request = ShowShareMenuParams.class)
    public void showShareMenu(ShowShareMenuParams showShareMenuParams, MsiContext context) {
        // 判断是从框架触发还是从业务代码中显示调用showShareMenu
        String eventFrom = getEventFromFromInnerArgs(context);;
        // 如果不是框架调用，则不受AppConfig控制
        if ((!SHOW_SHAREMENU_FROM_FRAMEWORK.equals(eventFrom)) || getRuntime().getMSCAppModule().isShareSupported()) {
            int id = getPageId(context);
            IPageModule pageModule = getRuntime().getContainerManagerModule().getPageByPageId(id);
            if (pageModule != null) {
                pageModule.getPageNavigationBarMethods().showShareMenu(SHOW_SHAREMENU_FROM_BUSINESS.equals(eventFrom));
                context.onSuccess(null);
            } else {
                context.onError("can't find page by pageId:" + id, MSIError.getIgnoreError(MSCErrorCode.ERROR_CODE_API_COMMON_MEET_EXPECTATIONS));
            }
        } else {
                context.onError("invocation from framework is blocked, share button is disabled in " + "appconfig",
                        MSIError.getGeneralError(ERROR_CODE_MENU_NO_CONFIG));
        }
    }

    private String getEventFromFromInnerArgs(MsiContext context) {
        if (context == null) {
            return SHOW_SHAREMENU_FROM_BUSINESS;
        }
        JsonObject innerArgs = context.getInnerArgs();
        if (innerArgs != null) {
            JsonElement element = innerArgs.get("eventFrom");
            if (element != null) {
                return element.getAsString();
            }
        }
        return SHOW_SHAREMENU_FROM_BUSINESS;
    }
}
