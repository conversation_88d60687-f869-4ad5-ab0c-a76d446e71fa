package com.meituan.msc.modules.api.msi.api;

import com.meituan.msc.common.utils.ScreenUtil;
import com.meituan.msc.modules.api.msi.MSCApi;
import com.meituan.msc.modules.api.msi.MSCErrorCode;
import com.meituan.msc.modules.container.IContainerManager;
import com.meituan.msc.modules.engine.MSCHornRollbackConfig;
import com.meituan.msc.modules.page.IPageModule;
import com.meituan.msc.modules.page.transition.PageTransitionConfig;
import com.meituan.msc.modules.reporter.MSCLog;
import com.meituan.msi.annotations.MsiApiEnv;
import com.meituan.msi.annotations.MsiApiMethod;
import com.meituan.msi.annotations.MsiParamChecker;
import com.meituan.msi.annotations.MsiSupport;
import com.meituan.msi.api.Error;
import com.meituan.msi.api.IError;
import com.meituan.msi.api.IMsiApi;
import com.meituan.msi.api.MSIError;
import com.meituan.msi.bean.ContainerInfo;
import com.meituan.msi.bean.MsiContext;
import com.sankuai.meituan.serviceloader.annotation.ServiceLoaderInterface;

@ServiceLoaderInterface(key = "msc_transition", interfaceClass = IMsiApi.class)
@MsiApiEnv(name = ContainerInfo.ENV_MSC)
public class TransitionApi extends MSCApi {
    private static final String TAG = "TransitionApi";
    private final int GENERAL_PAGE_NOT_FOUND = 1;
    private final int GENERAL_PIVOTX_ILLEGAL = 2;
    private final int GENERAL_PIVOTY_ILLEGAL = 3;
    private final int GENERAL_NOT_PAGE = 4;
    private final int IGNORABLE_PAGE_IS_DESTROYED = 1;

    /**
     * 设置页面退出动画
     * https://km.sankuai.com/collabpage/2119366469
     *
     * @param param
     * @param context
     */
    @MsiApiMethod(name = "setPagePopTransitionStyle", request = PageTransitionStyleParam.class, env = {"msc"}, scope = "msc")
    public void setPagePopTransitionStyle(PageTransitionStyleParam param, MsiContext context) {
        int[] widthHeight = ScreenUtil.getScreenWidthAndHeight(context.getActivity(), null);
        int width = widthHeight[0];
        int height = widthHeight[1];
        if (param.pivotX > width) {
            String errMsg = "pivotX illegal";
            if (MSCHornRollbackConfig.enableSetPagePopTransitionStyleErrno()) {
                context.onError(errMsg, MSIError.getGeneralError(MSCErrorCode.ERROR_CODE_API_COMMON_INVALID_PARAM));
            } else {
                context.onError(errMsg, new Error(IError.ErrLevel.GENERAL, GENERAL_PIVOTX_ILLEGAL));
            }
            MSCLog.e(TAG, errMsg);
            return;
        }
        if (param.pivotY > height) {
            String errMsg = "pivotY illegal";
            if (MSCHornRollbackConfig.enableSetPagePopTransitionStyleErrno()) {
                context.onError(errMsg, MSIError.getGeneralError(MSCErrorCode.ERROR_CODE_API_COMMON_INVALID_PARAM));
            } else {
                context.onError(errMsg, new Error(IError.ErrLevel.GENERAL, GENERAL_PIVOTY_ILLEGAL));
            }
            MSCLog.e(TAG, errMsg);
            return;
        }
        IPageModule pageModule;
        Integer pageId = param.pageId;
        IContainerManager containerManager = getModule(IContainerManager.class);
        if (pageId == null) {
            pageModule = containerManager.getTopPage();
        } else {
            pageModule = containerManager.getPageByPageId(pageId);
        }
        if (pageModule == null) {
            String errMsg = "page not found";
            if (MSCHornRollbackConfig.enableSetPagePopTransitionStyleErrno()) {
                context.onError(errMsg, MSIError.getIgnoreError(MSCErrorCode.ERROR_CODE_API_COMMON_MEET_EXPECTATIONS));
            } else {
                context.onError(errMsg, new Error(IError.ErrLevel.GENERAL, GENERAL_PAGE_NOT_FOUND));
            }
            MSCLog.e(TAG, errMsg);
            return;
        }
        if (pageModule.isDestroyed()) {
            String errMsg = "page is destroyed";
            if (MSCHornRollbackConfig.enableSetPagePopTransitionStyleErrno()) {
                context.onError(errMsg, MSIError.getIgnoreError(MSCErrorCode.ERROR_CODE_API_COMMON_MEET_EXPECTATIONS));
            } else {
                context.onError(errMsg, new Error(IError.ErrLevel.IGNORABLE, IGNORABLE_PAGE_IS_DESTROYED));
            }
            MSCLog.e(TAG, errMsg);
            return;
        }
        if (pageModule.isWidget()) {
            String errMsg = "not page";
            if (MSCHornRollbackConfig.enableSetPagePopTransitionStyleErrno()) {
                context.onError(errMsg, MSIError.getGeneralError(MSCErrorCode.ERROR_CODE_API_COMMON_NOT_MATCH_SCENE));
            } else {
                context.onError(errMsg, new Error(IError.ErrLevel.GENERAL, GENERAL_NOT_PAGE));
            }
            MSCLog.e(TAG, errMsg);
            return;
        }
        PageTransitionConfig pageTransitionConfig = pageModule.getPageTransitionConfig();
        if (pageTransitionConfig == null) {
            MSCLog.i(TAG, "new pageTransitionConfig");
            pageTransitionConfig = new PageTransitionConfig();
        }
        pageTransitionConfig.popStyle = param.style;
        pageTransitionConfig.overrideContainerPop = param.overrideContainerPop;
        pageTransitionConfig.pivotX = param.pivotX;
        pageTransitionConfig.pivotY = param.pivotY;
        pageModule.setPageTransitionConfig(pageTransitionConfig);
        context.onSuccess(null);
    }

    @MsiSupport
    public static class PageTransitionStyleParam {
        Integer pageId;
        @MsiParamChecker(required = true, min = -1, max = 3)
        int style;
        boolean overrideContainerPop;
        @MsiParamChecker(min = 0)
        float pivotX = 0;
        @MsiParamChecker(min = 0)
        float pivotY = 0;
    }
}
