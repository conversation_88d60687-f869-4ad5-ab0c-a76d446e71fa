package com.meituan.msc.modules.api.msi.api;

import com.meituan.msc.modules.api.msi.MSCApi;
import com.meituan.msc.modules.api.msi.MSCErrorCode;
import com.meituan.msc.modules.engine.MSCHornRollbackConfig;
import com.meituan.msc.modules.engine.MSCRuntime;
import com.meituan.msc.modules.manager.UpdateManager;
import com.meituan.msc.modules.reporter.MSCLog;
import com.meituan.msi.annotations.MsiApiEnv;
import com.meituan.msi.annotations.MsiApiMethod;
import com.meituan.msi.api.IMsiApi;
import com.meituan.msi.api.MSIError;
import com.meituan.msi.bean.ContainerInfo;
import com.meituan.msi.bean.MsiContext;
import com.sankuai.meituan.serviceloader.annotation.ServiceLoaderInterface;

@ServiceLoaderInterface(key = "msc_updateManagerApi", interfaceClass = IMsiApi.class)
@MsiApiEnv(name = ContainerInfo.ENV_MSC)
public class UpdateManagerApi extends MSCApi {

    private static final String TAG = "UpdateManagerApi";

    @MsiApiMethod(name = "applyUpdate", onUiThread = false)
    public void applyUpdate(MsiContext context) {

        MSCRuntime runtime = getRuntime();
        if (runtime == null) {
            context.onError("runtime is null", MSIError.getIgnoreError(MSCErrorCode.ERROR_CODE_API_COMMON_MEET_EXPECTATIONS));
            return;
        }

        MSCLog.i(TAG, "applyUpdate, appId = ", getAppId());

        /**
         * 具体逻辑，调用ApplyUpdateManager实现
         */
        UpdateManager updateManager = runtime.getModule(UpdateManager.class);
        updateManager.applyUpdate(context);
    }
}
