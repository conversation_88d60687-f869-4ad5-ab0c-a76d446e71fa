package com.meituan.msc.modules.api.msi.api.ui;

import com.meituan.msc.common.utils.DisplayUtil;
import com.meituan.msc.modules.api.msi.MSCApi;
import com.meituan.msc.modules.api.msi.MSCErrorCode;
import com.meituan.msc.modules.page.IPageModule;
import com.meituan.msc.modules.reporter.MSCLog;
import com.meituan.msi.annotations.MsiApiEnv;
import com.meituan.msi.annotations.MsiApiMethod;
import com.meituan.msi.api.IMsiApi;
import com.meituan.msi.api.MSIError;
import com.meituan.msi.bean.ContainerInfo;
import com.meituan.msi.bean.MsiContext;
import com.sankuai.meituan.serviceloader.annotation.ServiceLoaderInterface;

@ServiceLoaderInterface(key = "msc_pageScrollTo", interfaceClass = IMsiApi.class)
@MsiApiEnv(name = ContainerInfo.ENV_MSC)
public class PageScrollApi extends MSCApi {
    public static final String TAG = "PageScrollApi";

    //    scrollTop: 0,
    //    duration: 300
    @MsiApiMethod(name = "pageScrollTo", request = PageScrollParam.class,
            onUiThread = true, onSerializedThread = false)
    public void pageScrollTo(PageScrollParam pageScrollParam, MsiContext context) {
        IPageModule pageModule = getPageModule(context);
        if (pageModule == null) {
            context.onError("pageModule is null!", MSIError.getIgnoreError(MSCErrorCode.ERROR_CODE_API_COMMON_MEET_EXPECTATIONS));
            return;
        }
        int targetY = DisplayUtil.toDeviceValue(pageScrollParam.scrollTop); //px
        int duration = pageScrollParam.duration;                            //ms
        //校验参数
        if (duration < 0) {
            MSCLog.w("PageScrollApi", "duration " + duration + " < 0, limit to 0");
            duration = 0;
        }
        //此处不做targetY超出页面长度的检查，有机型上页面刚创建时获取的页面长度不正确，目前仅由前端限制
        if (targetY < 0) {
            MSCLog.w("PageScrollApi", "scrollTop " + targetY + " < 0, limit to 0");
            targetY = 0;
        }
        pageModule.startScroll(targetY, duration, context);
    }
}
