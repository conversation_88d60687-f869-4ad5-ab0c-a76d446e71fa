package com.meituan.msc.modules.api.msi.components;

import android.view.View;
import android.view.ViewGroup;

import com.google.gson.JsonObject;
import com.meituan.msc.common.utils.UIUtil;
import com.meituan.msc.modules.api.msi.MSCNativeViewApi;
import com.meituan.msc.modules.api.msi.components.coverview.CoverViewUpdateUtil;
import com.meituan.msc.modules.api.msi.components.coverview.MSCCoverImageView;
import com.meituan.msc.modules.api.msi.components.coverview.params.MSCCoverImageViewParams;
import com.meituan.msc.modules.reporter.MSCLog;
import com.meituan.msi.annotations.MsiApiEnv;
import com.meituan.msi.annotations.MsiApiMethod;
import com.meituan.msi.api.IMsiApi;
import com.meituan.msi.bean.ContainerInfo;
import com.meituan.msi.bean.MsiContext;
import com.sankuai.meituan.serviceloader.annotation.ServiceLoaderInterface;

/**
 * cover-image
 * https://km.sankuai.com/page/1469968214
 *
 * Created by letty on 2022/12/6.
 **/
@MsiApiEnv(name = ContainerInfo.ENV_MSC)
@ServiceLoaderInterface(key = "msc_component_cover_image_view", interfaceClass = IMsiApi.class)
public class CoverImageApi extends MSCNativeViewApi<MSCCoverImageView, MSCCoverImageViewParams> {
    private static final String TAG = "CoverImageApi";

    @MsiApiMethod(name = "coverImageView", request = MSCCoverImageViewParams.class, onUiThread = true)
    public void beforeOperation(MSCCoverImageViewParams param, MsiContext msiContext) {
        handleViewOperation(msiContext, param);
    }

    @Override
    protected MSCCoverImageView doCreateCoverView(MsiContext msiContext, JsonObject uiParams,
                                                MSCCoverImageViewParams mscImageViewParams) {
        MSCLog.d(TAG, "iconPath:", mscImageViewParams.iconPath, "doCreateCoverView");
        MSCCoverImageView view = new MSCCoverImageView(msiContext.getActivity());
        setUpViewContext(view, msiContext, uiParams);
        view.updateCoverImageParam(mscImageViewParams, uiParams);
        CoverViewUpdateUtil.notifyLayerChanged(view);
        return view;
    }

    @Override
    protected boolean updateCoverView(MsiContext msiContext, MSCCoverImageView view,
                                      int pageId, int viewId, JsonObject uiParams,
                                      MSCCoverImageViewParams mscImageViewParams) {
        MSCLog.d(TAG, "iconPath:", mscImageViewParams.iconPath, "updateCoverView");
        view.updateCoverImageParam(mscImageViewParams, uiParams);
        CoverViewUpdateUtil.notifyLayerChanged(view);
        return true;
    }

    @Override
    protected void removeCoverView(MsiContext msiContext, View view,
                                   int pageId, JsonObject uiParams, MSCCoverImageViewParams componentParam) {
        MSCLog.d(TAG, "iconPath:", componentParam.iconPath, "removeCoverView");
        super.removeCoverView(msiContext, view, pageId, uiParams, componentParam);
        CoverViewUpdateUtil.notifyLayerChanged(view);
    }

}
