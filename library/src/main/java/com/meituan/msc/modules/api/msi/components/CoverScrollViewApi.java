package com.meituan.msc.modules.api.msi.components;

import com.google.gson.JsonObject;
import com.meituan.msc.modules.api.msi.MSCNativeViewApi;
import com.meituan.msc.modules.api.msi.components.coverview.CoverViewUpdateUtil;
import com.meituan.msc.modules.api.msi.components.coverview.MSCCoverScrollView;
import com.meituan.msc.modules.api.msi.components.coverview.params.MSCCoverScrollParams;
import com.meituan.msi.annotations.MsiApiEnv;
import com.meituan.msi.annotations.MsiApiMethod;
import com.meituan.msi.api.IMsiApi;
import com.meituan.msi.bean.ContainerInfo;
import com.meituan.msi.bean.MsiContext;
import com.sankuai.meituan.serviceloader.annotation.ServiceLoaderInterface;


/**
 * cover-view 的 scroll 模式
 * https://km.sankuai.com/page/1470041999
 *
 * Created by letty on 2022/12/6.
 **/
@MsiApiEnv(name = ContainerInfo.ENV_MSC)
@ServiceLoaderInterface(key = "msc_component_cover_scroll_view", interfaceClass = IMsiApi.class)
public class CoverScrollViewApi extends MSCNativeViewApi<MSCCoverScrollView, MSCCoverScrollParams> {

    @MsiApiMethod(name = "coverScrollView", request = MSCCoverScrollParams.class, onUiThread = true)
    public void beforeOperation(MSCCoverScrollParams param, MsiContext msiContext) {
        handleViewOperation(msiContext, param);
    }

    @Override
    protected MSCCoverScrollView doCreateCoverView(MsiContext msiContext, JsonObject uiParams,
                                                 MSCCoverScrollParams mscCoverScrollParams) {
        MSCCoverScrollView view = new MSCCoverScrollView(msiContext.getActivity());
        setUpViewContext(view, msiContext, uiParams);
        view.setUpScroll(mscCoverScrollParams);
        CoverViewUpdateUtil.notifyLayerChanged(view);
        return view;
    }

    @Override
    protected boolean updateCoverView(MsiContext msiContext, MSCCoverScrollView view,
                                      int pageId, int viewId, JsonObject uiParams,
                                      MSCCoverScrollParams mscCoverScrollParams) {
        view.setUpScroll(mscCoverScrollParams);
        CoverViewUpdateUtil.notifyLayerChanged(view);
        return true;
    }


}
