package com.meituan.msc.modules.api.msi.components;

import android.view.View;

import com.google.gson.JsonObject;
import com.meituan.msc.modules.api.msi.MSCNativeViewApi;
import com.meituan.msc.modules.api.msi.components.coverview.CoverViewAnimateUtil;
import com.meituan.msc.modules.api.msi.components.coverview.CoverViewUpdateUtil;
import com.meituan.msc.modules.api.msi.components.coverview.MSCCoverTextView;
import com.meituan.msc.modules.api.msi.components.coverview.params.MSCCoverTextViewParams;
import com.meituan.msi.annotations.MsiApiEnv;
import com.meituan.msi.annotations.MsiApiMethod;
import com.meituan.msi.api.IMsiApi;
import com.meituan.msi.bean.ContainerInfo;
import com.meituan.msi.bean.MsiContext;
import com.sankuai.meituan.serviceloader.annotation.ServiceLoaderInterface;

/**
 * cover-view
 * https://km.sankuai.com/page/1470041999
 *
 * Created by letty on 2022/12/6.
 **/
@MsiApiEnv(name = ContainerInfo.ENV_MSC)
@ServiceLoaderInterface(key = "msc_component_cover_view", interfaceClass = IMsiApi.class)
public class CoverTextViewApi extends MSCNativeViewApi<MSCCoverTextView, MSCCoverTextViewParams> {

    @MsiApiMethod(name = "coverTextView", request = MSCCoverTextViewParams.class, onUiThread = true)
    public void beforeOperation(MSCCoverTextViewParams param, MsiContext msiContext) {
        handleViewOperation(msiContext, param);
    }

    @Override
    protected MSCCoverTextView doCreateCoverView(MsiContext msiContext, JsonObject uiParams,
                                               MSCCoverTextViewParams mscTextViewParams) {
        MSCCoverTextView textView = new MSCCoverTextView(msiContext.getActivity());
        setUpViewContext(textView, msiContext, uiParams);
        textView.updateTextareaParams(mscTextViewParams);
        CoverViewUpdateUtil.notifyLayerChanged(textView);
        return textView;
    }

    @Override
    protected boolean updateCoverView(MsiContext msiContext, MSCCoverTextView textView,
                                      int pageId, int viewId, JsonObject uiParams,
                                      MSCCoverTextViewParams mscTextViewParams) {
        textView.updateTextareaParams(mscTextViewParams);
        CoverViewUpdateUtil.notifyLayerChanged(textView);
        return true;
    }

    @Override
    protected void removeCoverView(MsiContext msiContext, View view,
                                   int pageId, JsonObject uiParams, MSCCoverTextViewParams componentParam) {
        super.removeCoverView(msiContext, view, pageId, uiParams, componentParam);
        CoverViewUpdateUtil.notifyLayerChanged(view);
    }

    /**
     * 12.31.200 迭代正式支持该功能：https://km.sankuai.com/collabpage/2705318319
     * @param params
     * @param context
     */
    @MsiApiMethod(name = "animateCoverView", onUiThread = true, isForeground = true,
            request = CoverViewAnimateUtil.MSCAnimatedParams.class)
    public void animateCoverView(CoverViewAnimateUtil.MSCAnimatedParams params, MsiContext context) {
        CoverViewAnimateUtil.animateCoverView(params, context, context.findView());
    }

    // -- 事件声明 均为内部事件 不使用MSI声明
}
