package com.meituan.msc.modules.api.msi.components;

import android.graphics.Color;
import android.graphics.Paint;
import android.graphics.Shader;
import android.graphics.Typeface;

import com.meituan.msc.common.utils.ReflectUtil;


/**
 * Created by bunnyblue on 4/24/18.
 */
public class MSCPaint extends Paint {
    public PaintPadding padding = PaintPadding.NORMAL;
    private float alpha = (((float) getAlpha()) / 255.0f);
    private String familyName;
    private int style;
    private int defaultColor = getColor();

    public enum PaintPadding {
        NORMAL,//normal
        TOP,//top
        BOTTOM,//bottom
        MIDDLE;//middle
    }


    public final MSCPaint clonePaint() {
        MSCPaint MSCPaint = new MSCPaint();
        MSCPaint.setColor(getColor());
        MSCPaint.setFlags(getFlags());
        MSCPaint.setDither(isDither());
        Shader shader = getShader();
        if (shader != null) {
            Object cloneShader = ReflectUtil.invoke(Shader.class, "copy", shader, new Class[0], new Object[0]);
            if (cloneShader != null && (cloneShader instanceof Shader)) {
                shader = (Shader) cloneShader;
            }
            MSCPaint.setShader(shader);
        }
        MSCPaint.setStrokeJoin(getStrokeJoin());
        MSCPaint.setStrokeMiter(getStrokeMiter());
        MSCPaint.setStrokeWidth(getStrokeWidth());
        MSCPaint.setStrokeCap(getStrokeCap());
        MSCPaint.setStyle(getStyle());
        MSCPaint.setTextSize(getTextSize());
        MSCPaint.setTextAlign(getTextAlign());
        MSCPaint.setTypeface(getTypeface());
        MSCPaint.padding = this.padding;
        return MSCPaint;
    }

    public final void setAlpha(float newAlpha) {
        this.alpha = newAlpha;
        setColor(this.defaultColor);
    }

    public final void setColor(int color) {
        this.defaultColor = color;
        super.setColor(((((int) (((float) Color.alpha(color)) * this.alpha)) & 255) << 24) | (16777215 & color));
    }

    public final void reset() {
        this.padding = PaintPadding.NORMAL;
        super.reset();
    }

    /**
     * @param familyName May be null. The name of the font family.
     */
    public final void setTypeface(String familyName) {
        this.familyName = familyName;
        setTypeface(Typeface.create(familyName, this.style));
    }

    /**
     * @param style The style (normal, bold, italic) of the typeface.
     *              e.g. NORMAL, BOLD, ITALIC, BOLD_ITALIC
     **/
    public final void setTypeface(int style) {
        this.style = style;
        setTypeface(Typeface.create(this.familyName, style));
    }
}
