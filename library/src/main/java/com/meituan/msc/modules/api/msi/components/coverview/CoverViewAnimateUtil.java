package com.meituan.msc.modules.api.msi.components.coverview;

import android.animation.Animator;
import android.animation.AnimatorListenerAdapter;
import android.animation.AnimatorSet;
import android.animation.ObjectAnimator;
import android.animation.TimeInterpolator;
import android.animation.ValueAnimator;
import android.view.View;
import android.view.ViewGroup;
import android.view.animation.AccelerateDecelerateInterpolator;
import android.view.animation.AccelerateInterpolator;
import android.view.animation.DecelerateInterpolator;
import android.view.animation.LinearInterpolator;
import android.view.animation.PathInterpolator;
import android.widget.AbsoluteLayout;

import com.meituan.msc.common.utils.DisplayUtil;
import com.meituan.msc.modules.api.msi.MSCErrorCode;
import com.meituan.msc.modules.api.msi.MSCNativeViewApi;
import com.meituan.msc.modules.page.view.CoverViewWrapper;
import com.meituan.msi.annotations.MsiParamChecker;
import com.meituan.msi.annotations.MsiSupport;
import com.meituan.msi.api.MSIError;
import com.meituan.msi.bean.MsiContext;

/**
 * CoverView动画
 */
public class CoverViewAnimateUtil {
    private static final int ERROR_CODE_COVER_VIEW_IS_NOT_COVER_VIEW = 800000200;

    @MsiSupport
    public static class MSCAnimatedParams {

        @MsiParamChecker(required = true)
        public TransitionStyle finalStyle;
        int duration = 300;

        static final String EASING_LINEAR = "linear";
        static final String EASING_EASE = "ease";
        static final String EASING_EASE_IN = "ease-in";
        static final String EASING_EASE_OUT = "ease-out";
        static final String EASING_EASE_IN_OUT = "ease-in-out";
        @MsiParamChecker(in = {EASING_LINEAR, EASING_EASE, EASING_EASE_IN, EASING_EASE_OUT, EASING_EASE_IN_OUT})
        String easing = EASING_LINEAR;

        @MsiSupport
        static class TransitionStyle {
            public Double left;
            public Double top;
            public Double opacity;
            public Double rotate;
            public Double scaleX;
            public Double scaleY;
        }
    }

    public static void animateCoverView(MSCAnimatedParams param, MsiContext context, View view) {
        if (view == null) {
            context.onError(MSCNativeViewApi.VIEW_NOT_FOUND, MSIError.getIgnoreError(MSCErrorCode.ERROR_CODE_API_COMMON_MEET_EXPECTATIONS));
            return;
        }

        if (!(view.getParent() instanceof CoverViewWrapper)) {
            context.onError(MSCNativeViewApi.VIEW_NOT_FOUND, MSIError.getGeneralError(ERROR_CODE_COVER_VIEW_IS_NOT_COVER_VIEW));
            return;
        }
        //只允许操作coverview
        if (!(view instanceof MSCTextView) && !(view instanceof MSCImageView) && !(view instanceof MSCScrollView)) {
            context.onError(MSCNativeViewApi.VIEW_NOT_FOUND, MSIError.getGeneralError(ERROR_CODE_COVER_VIEW_IS_NOT_COVER_VIEW));
            return;
        }
//        MSCLog.i("CoverTextViewApi", view, view.getClass().getSimpleName(), "animateCoverView: ", param);
        CoverViewWrapper coverViewWrapper = (CoverViewWrapper) view.getParent();
        MSCAnimatedParams.TransitionStyle finalStyle = param.finalStyle;
        AnimatorSet animatorSet = new AnimatorSet();
        if (finalStyle.left != null) {
            final int left = DisplayUtil.roundComputeValue(finalStyle.left.floatValue());
            ValueAnimator animatorX;
            if (coverViewWrapper.getLayoutParams() instanceof AbsoluteLayout.LayoutParams) {
                AbsoluteLayout.LayoutParams lp = (AbsoluteLayout.LayoutParams) coverViewWrapper.getLayoutParams();
                animatorX = ValueAnimator.ofInt(lp.x, left);
                animatorX.addUpdateListener(new ValueAnimator.AnimatorUpdateListener() {
                    @Override
                    public void onAnimationUpdate(ValueAnimator animation) {
                        lp.x = (int) animation.getAnimatedValue();
                        coverViewWrapper.setLayoutParams(lp);
                    }
                });
            } else {
                ViewGroup.MarginLayoutParams lp = (ViewGroup.MarginLayoutParams) coverViewWrapper.getLayoutParams();
                animatorX = ValueAnimator.ofInt(lp.leftMargin, left);
                animatorX.addUpdateListener(new ValueAnimator.AnimatorUpdateListener() {
                    @Override
                    public void onAnimationUpdate(ValueAnimator animation) {
                        lp.leftMargin = (int) animation.getAnimatedValue();
                        coverViewWrapper.setLayoutParams(lp);
                    }
                });
            }
            animatorSet.playTogether(animatorX);
        }
        if (finalStyle.top != null) {
            final int top = DisplayUtil.roundComputeValue(finalStyle.top.floatValue());
            ValueAnimator animatorY;
            if (coverViewWrapper.getLayoutParams() instanceof AbsoluteLayout.LayoutParams) {
                AbsoluteLayout.LayoutParams lp = (AbsoluteLayout.LayoutParams) coverViewWrapper.getLayoutParams();
                animatorY = ValueAnimator.ofInt(lp.y, top);
                animatorY.addUpdateListener(new ValueAnimator.AnimatorUpdateListener() {
                    @Override
                    public void onAnimationUpdate(ValueAnimator animation) {
                        lp.y = (int) animation.getAnimatedValue();
                        coverViewWrapper.setLayoutParams(lp);
                    }
                });
            } else {
                ViewGroup.MarginLayoutParams lp = (ViewGroup.MarginLayoutParams) coverViewWrapper.getLayoutParams();
                animatorY = ValueAnimator.ofInt(lp.topMargin, top);
                animatorY.addUpdateListener(new ValueAnimator.AnimatorUpdateListener() {
                    @Override
                    public void onAnimationUpdate(ValueAnimator animation) {
                        lp.topMargin = (int) animation.getAnimatedValue();
                        coverViewWrapper.setLayoutParams(lp);
                    }
                });
            }
            animatorSet.playTogether(animatorY);
        }

        if (finalStyle.opacity != null) {
            final float opacity = finalStyle.opacity.floatValue();
            ObjectAnimator animatorAlpha = ObjectAnimator.ofFloat(coverViewWrapper, "alpha", coverViewWrapper.getAlpha(), opacity);
            animatorSet.playTogether(animatorAlpha);
        }
        if (finalStyle.rotate != null) {
            final float rotate = finalStyle.rotate.floatValue();
            ObjectAnimator animatorRotation = ObjectAnimator.ofFloat(coverViewWrapper, "rotation", coverViewWrapper.getRotation(), rotate);
            animatorSet.playTogether(animatorRotation);
        }

        if (finalStyle.scaleX != null) {
            final float scaleX = finalStyle.scaleX.floatValue();
            ObjectAnimator animatorSX = ObjectAnimator.ofFloat(coverViewWrapper, "scaleX", coverViewWrapper.getScaleX(), scaleX);
            animatorSet.playTogether(animatorSX);
        }
        if (finalStyle.scaleY != null) {
            final float scaleY = finalStyle.scaleY.floatValue();
            ObjectAnimator animatorSY = ObjectAnimator.ofFloat(coverViewWrapper, "scaleY", coverViewWrapper.getScaleY(), scaleY);
            animatorSet.playTogether(animatorSY);
        }
        // 没有动画，直接返回
        if (animatorSet.getChildAnimations().isEmpty()) {
            context.onSuccess(null);
            return;
        }
        animatorSet.setDuration(param.duration);
        animatorSet.setInterpolator(getTimeInterpolator(param.easing));
        animatorSet.addListener(new AnimatorListenerAdapter() {
            public void onAnimationEnd(Animator animator) {
                context.onSuccess(null);
                // FIXME, 地图气泡marker效果是否需要动画过程中实时更新。
                CoverViewUpdateUtil.notifyLayerChanged(view);
            }
        });
        animatorSet.start();
    }

    private static TimeInterpolator getTimeInterpolator(String easing) {
        // 前端 ease_in_out、ease 类似于Android AccelerateDecelerateInterpolator
        if (MSCAnimatedParams.EASING_EASE_IN.equals(easing)) {
            return new AccelerateInterpolator();
//            return new PathInterpolator(0.42F,0F, 1F, 1F);
        } else if (MSCAnimatedParams.EASING_EASE_OUT.equals(easing)) {
//            return new PathInterpolator(0F,0F, 0.58F, 1F);
            return new DecelerateInterpolator();
        } else if (MSCAnimatedParams.EASING_EASE.equals(easing)) {
            return new PathInterpolator(0.25F,0.1F, 0.25F, 1F);
        } else if (MSCAnimatedParams.EASING_EASE_IN_OUT.equals(easing)) {
//            return new PathInterpolator(0.42F,0F, 0.58F, 1F);
            return new AccelerateDecelerateInterpolator();
        }
//        return new PathInterpolator(0F,0F, 1F, 1F);
        return new LinearInterpolator();
    }
}
