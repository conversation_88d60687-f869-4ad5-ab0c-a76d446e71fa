package com.meituan.msc.modules.api.msi.components.coverview;

import android.view.View;

/**
 * Created by letty on 2022/12/14.
 **/
public class CoverViewUpdateUtil {

    /**
     * FIXME letty notifyInfoWindowChange in more elegant way
     * @param view
     */
    public static void notifyLayerChanged(View view) {
        if (view instanceof ICoverViewUpdateRegistry) {
            CoverUpdateObserver updateListener = ((ICoverViewUpdateRegistry) view).getCoverUpdateObserver();
            if (updateListener != null) {
                updateListener.onChange();
            }
        }
    }
}
