package com.meituan.msc.modules.api.msi.components.coverview;

/**
 * coverView 更新注册器
 */
public interface ICoverViewUpdateRegistry {

    /**
     * 获取coverView视图更新观察者
     * @return
     */
    CoverUpdateObserver getCoverUpdateObserver();

    /**
     * 添加coverView视图更新观察者
     * @param coverUpdateObserver
     */
    void addUpdateCoverViewObserver(CoverUpdateObserver coverUpdateObserver);

    /**
     * 自定义气泡cover-view
     * @param isCustomCallOut
     */
    void setIsCustomCallOutView(boolean isCustomCallOut);

    /**
     * 自定义气泡情况，允许内部cover-view响应手势事件
     * @return
     */
    boolean enableCoverViewEvent();
}
