package com.meituan.msc.modules.api.msi.components.coverview;

import android.content.Context;
import android.graphics.drawable.Drawable;
import android.support.annotation.Nullable;
import android.view.View;
import android.widget.ImageView;

import com.google.gson.JsonObject;
import com.meituan.msc.common.utils.DisplayUtil;
import com.meituan.msc.common.utils.FileUtil;
import com.meituan.msc.common.utils.HashMapHelper;
import com.meituan.msc.modules.api.msi.IMSCView;
import com.meituan.msc.modules.api.msi.MSCViewContext;
import com.meituan.msc.modules.api.msi.components.coverview.params.MSCCoverImageViewParams;
import com.meituan.msc.modules.reporter.MSCLog;
import com.squareup.picasso.Callback;
import com.squareup.picasso.RequestCreator;

/**
 * Created by letty on 2022/12/6.
 **/
public class MSCCoverImageView extends MSCImageView implements IMSCView {
    private static final String TAG = "MSCCoverImageView";

    public MSCCoverImageView(Context context) {
        super(context);
        setScaleType(ImageView.ScaleType.FIT_XY);
    }

    MSCViewContext mViewContext;
    private String imageUri;

    @Override
    public void setViewContext(MSCViewContext viewContext) {
        mViewContext = viewContext;
    }

    @Override
    public MSCViewContext getViewContext() {
        return mViewContext;
    }

    public void updateCoverImageParam(final MSCCoverImageViewParams params, JsonObject uiParams) {
        //调整顺序，padding在style
        boolean clickable = params.clickable != null && params.clickable;
        boolean gesture = params.gesture != null && params.gesture;

        this.imageUri = params.iconPath;

        if (params.enableCoverViewEvent != null) {
            enableCoverViewEvent = params.enableCoverViewEvent;
        }

        // 处理 gesture 手势
        MSCCoverViewTouchHelper.setupGestureTouchListener(this, getViewContext(), params.gesture);

        if (params.clickable != null) {
            // 如果开启手势，手势优先
            if (!gesture && clickable) {
                setOnClickListener(new View.OnClickListener() {
                    @Override
                    public void onClick(View v) {
                        JsonObject jsonObject = new JsonObject();
                        jsonObject.add("data", params.data);
                        mViewContext.dispatchPageEvent("onImageViewClick", jsonObject);
                    }
                });
            } else {
                setOnClickListener(null);
            }
        }

        RequestCreator requestCreator = FileUtil.getPicassoRequestCreator(getContext(), params.iconPath,
                mViewContext.getRuntime().getFileModule());
        if (requestCreator != null) {
            int width = (int) DisplayUtil.computeValue(uiParams.has("width") ? uiParams.get("width").getAsInt() : 0);
            int height = (int) DisplayUtil.computeValue(uiParams.has("height") ? uiParams.get("height").getAsInt() : 0);

            //height width
            if (width > 0 && height > 0) {
                //fix 当加载同一张图片，而宽高发生变化时，Picasso因为缓存获取到的图片宽高还未生效
                //导致图片加载完成在图片在view宽高调整完成加载完图片，导致图片模糊
                requestCreator.resize(width, height);
            } else {
                requestCreator.fit();
            }
            MSCLog.d(TAG, imageUri, "load image start");
            requestCreator.centerInside().into(this, new Callback() {
                @Override
                public void onSuccess() {
                    MSCLog.d(TAG, imageUri, "load image end");
                    mViewContext.dispatchPageEvent("imageLoadEvent", HashMapHelper.of("status", "success"));
                }

                @Override
                public void onError() {
                    mViewContext.dispatchPageEvent("imageLoadEvent", HashMapHelper.of("status", "error"));
                }
            });
        } else {
            mViewContext.dispatchPageEvent("imageLoadEvent", HashMapHelper.of("status", "error"));
        }
        return;
    }

    @Override
    protected void onLayout(boolean changed, int left, int top, int right, int bottom) {
        super.onLayout(changed, left, top, right, bottom);
        MSCLog.d(TAG, imageUri, "onLayout, right:", right, ", bottom:", bottom);
        // FIXME: 临时修复同层渲染模式下CoverImage中图片不展示的问题详细文档: https://km.sankuai.com/collabpage/2127117928
        if (!isAttachedToWindow()) {
            // 没有挂到window中时，布局完主动触发 ViewTreeObserver 的回调，主动通知图片库调用onSizeReady
            getViewTreeObserver().dispatchOnPreDraw();
        }
    }

    @Override
    public void setImageDrawable(@Nullable Drawable drawable) {
        boolean needToUpdate = getDrawable() != drawable;
        super.setImageDrawable(drawable);
        if (needToUpdate && !isAttachedToWindow()) {
            MSCLog.d(TAG, imageUri, "setImageDrawable");
            // FIXME: 临时修复同层渲染模式下CoverImage中图片不展示的问题，详细文档: https://km.sankuai.com/collabpage/2127117928
            CoverViewUpdateUtil.notifyLayerChanged(this);
        }
    }
}
