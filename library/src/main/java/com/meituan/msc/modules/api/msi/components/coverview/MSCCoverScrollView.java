package com.meituan.msc.modules.api.msi.components.coverview;

import android.content.Context;
import android.view.View;

import com.meituan.msc.common.utils.DisplayUtil;
import com.meituan.msc.modules.api.msi.IMSCView;
import com.meituan.msc.modules.api.msi.MSCViewContext;
import com.meituan.msc.modules.api.msi.components.coverview.params.MSCCoverScrollParams;

import java.util.HashMap;

/**
 * Created by letty on 2022/12/6.
 **/
public class MSCCoverScrollView extends MSCScrollView implements IMSCView {
    public MSCCoverScrollView(Context context) {
        super(context);
    }

    MSCViewContext mViewContext;

    @Override
    public void setViewContext(MSCViewContext viewContext) {
        mViewContext = viewContext;
    }

    @Override
    public MSCViewContext getViewContext() {
        return mViewContext;
    }

    public void setUpScroll(final MSCCoverScrollParams param) {
        if (param.needScrollEvent != null) {
            if (param.needScrollEvent) {
                this.scrollChange = new IScrollChange() {
                    @Override
                    public void onCoverScrollChange(View rootView, int left, int top) {
                        HashMap<String, Object> map =new HashMap<>();
                        map.put("scrollLeft", (int) DisplayUtil.toWebValue(left));
                        map.put("scrollTop", (int) DisplayUtil.toWebValue(top));
                        map.put("scrollWidth", (int) DisplayUtil.toWebValue(rootView.getWidth()));
                        map.put("scrollHeight", (int) DisplayUtil.toWebValue(rootView.getHeight()));
                        mViewContext.dispatchPageEvent("onScrollViewScroll", map);
                    }
                };
            } else {
                this.scrollChange = null;
            }
        }

        if (param.scrollX != null) {
            setScrollHorizontal(param.scrollX);
        }
        if (param.scrollY != null) {
            setScrollVertical(param.scrollY);
        }
        if (param.scrollTop != null) {
            final int scrollTop = DisplayUtil.toDeviceValue(param.scrollTop);
            postDelayed(new Runnable() {
                public final void run() {
                    scrollTo(getScrollX(), scrollTop);

                }
            }, 100);
        }
    }

    // 此处MMP之前是在scrollView中来更新样式，msc中统一放在了update 外层来做；暂时没发现明显问题，不确定是否可能有问题，后续再删除
//    private void updateViewStyle(JsonObject jsonObject) {
//        if (jsonObject == null) {
//            return;
//        }
//        if (jsonObject.getAsJsonObject("style") == null) {
//            return;
//        }
//        JsonObject style = jsonObject.getAsJsonObject("style");
//        boolean invalidate;
//
//        String bgColor = style.has("bgColor") ? style.get("bgColor").toString() : null;
//        if (!TextUtils.isEmpty(bgColor)) {
//            setBgColor(ColorUtil.parseRGBAColor(bgColor));
//        }
//        String borderColor = style.has("borderColor") ? style.get("borderColor").toString() : null;
//        if (!TextUtils.isEmpty(borderColor)) {
//            setColor(ColorUtil.parseRGBAColor(borderColor));
//        }
//
//        setBorderRadius(DisplayUtil.toDeviceValue(style.has("borderRadius") ?
//                style.get("borderRadius").getAsFloat() : 0.0f));
//        setBorderWidth(DisplayUtil.toDeviceValue(style.has("borderWidth") ?
//                style.get("borderWidth").getAsFloat() : 0.0f));
//
//        // FIXME : 这里的invalid比较奇怪 后续如果放开注释可以确认一下
//        invalidate = true;
//        float opacity = style.has("opacity") ? style.get("opacity").getAsFloat() : -1f;
//        if (opacity >= 0.0f && opacity <= 1.0f) {
//            setAlpha(opacity);
//            invalidate = true;
//        }
//        JsonArray optJSONArray = jsonObject.has("padding")?  style.get("padding").getAsJsonArray() : null;
//        if (optJSONArray != null && optJSONArray.size() == 4) {
//            setPadding((int) DisplayUtil.getDeviceValueInArray(optJSONArray, 0), (int) DisplayUtil.getDeviceValueInArray(optJSONArray, 1),
//                    (int) DisplayUtil.getDeviceValueInArray(optJSONArray, 2), (int) DisplayUtil.getDeviceValueInArray(optJSONArray, 3));
//        }
//
//        float rotate = JsonUtil.getFloatValue(jsonObject,"rotate",0);
//        float scaleX = JsonUtil.getFloatValue(jsonObject,"scaleX",0);;
//        float scaleY = JsonUtil.getFloatValue(jsonObject,"scaleY",0);
//        if (jsonObject.has("rotate")) {
//            setRotation(rotate);
//            invalidate = true;
//        }
//        if (jSONObject.has("scaleX")) {
//            setScaleX(scaleX);
//            invalidate = true;
//        }
//        if (jSONObject.has("scaleY")) {
//            setScaleY(scaleY);
//            invalidate = true;
//        }
//
//        if (invalidate) {
//            invalidate();
//        }
//
//    }
}
