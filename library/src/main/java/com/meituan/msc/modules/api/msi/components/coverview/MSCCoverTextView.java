package com.meituan.msc.modules.api.msi.components.coverview;

import android.content.Context;
import android.content.res.AssetManager;
import android.graphics.Typeface;
import android.text.TextUtils;
import android.util.TypedValue;
import android.view.Gravity;
import android.view.View;

import com.google.gson.JsonObject;
import com.meituan.msc.common.utils.ColorUtil;
import com.meituan.msc.common.utils.DisplayUtil;
import com.meituan.msc.common.utils.ViewUtils;
import com.meituan.msc.lib.interfaces.IFontfaceModule;
import com.meituan.msc.modules.api.msi.IMSCView;
import com.meituan.msc.modules.api.msi.MSCViewContext;
import com.meituan.msc.modules.api.msi.components.coverview.params.MSCCoverTextViewParams;
import com.meituan.msc.modules.engine.MSCRuntime;

/**
 * Created by letty on 2022/12/6.
 **/
public class MSCCoverTextView extends MSCTextView implements IMSCView {
    public MSCCoverTextView(Context context) {
        super(context);
    }

    MSCViewContext mViewContext;

    @Override
    public void setViewContext(MSCViewContext viewContext) {
        mViewContext = viewContext;
    }

    @Override
    public MSCViewContext getViewContext() {
        return mViewContext;
    }

    public void updateTextareaParams(MSCCoverTextViewParams textViewParams) {
        //调整顺序，padding在style
        updateTextViewStyle(textViewParams.label);
        boolean clickable = textViewParams.clickable != null && textViewParams.clickable;
        boolean gesture = textViewParams.gesture != null && textViewParams.gesture;
        if (textViewParams.enableCoverViewEvent != null) {
            enableCoverViewEvent = textViewParams.enableCoverViewEvent;
        }
        // 处理 gesture 手势
        MSCCoverViewTouchHelper.setupGestureTouchListener(this, getViewContext(), textViewParams.gesture);

        if (textViewParams.clickable != null) {
            // 如果开启手势，手势优先
            if (!gesture && clickable) {
                setOnClickListener(new View.OnClickListener() {
                    @Override
                    public void onClick(View v) {
                        JsonObject jsonObject = new JsonObject();
                        jsonObject.add("data", textViewParams.data);
                        mViewContext.dispatchPageEvent("onTextViewClick", jsonObject);
                    }
                });
            } else {
                setOnClickListener(null);
            }
        }
    }

    private void updateTextViewStyle(MSCCoverTextViewParams.Label label) {
        if (label != null) {
            String string;

            String color = label.color;
            if (!TextUtils.isEmpty(color)) {
                setTextColor(ColorUtil.parseRGBAColor(color));
            }

            if (label.fontSize != null) {
                setTextSize(TypedValue.COMPLEX_UNIT_DIP, label.fontSize.floatValue());
            }

            string = label.textAlign;
            if ("left".equals(string)) {
                setGravity(Gravity.LEFT);
            } else if ("center".equals(string)) {
                setGravity(Gravity.CENTER);
            } else if ("right".equals(string)) {
                setGravity(Gravity.RIGHT);
            }

            string = label.fontWeight;
            if ("bold".equals(string)) {
                setFakeBoldText(true);
            } else if ("normal".equals(string)) {
                setFakeBoldText(false);
            }

            string = label.lineBreak;
            if ("ellipsis".equals(string)) {
                setEllipsize(TextUtils.TruncateAt.END);
                setSingleLine(true);
            } else if ("clip".equals(string)) {
                setSingleLine(true);
            } else if ("break-word".equals(string)) {
                setSingleLine(false);
            } else if ("break-all".equals(string)) {
                setSingleLine(false);
            }
            setLineHeightWithPadding(this, label);//must invoke before setText
            // label.fontFamily默认为空，typeface将返回null，向下兼容。
            Typeface typeface = ViewUtils.getTypeFace(label.fontFamily, getContext(), mViewContext.getRuntime());
            if (typeface != null) {
                setTypeface(typeface);
            }
            setText(label.content);
        }

    }

    /**
     * spaced为lineHeight-fontHeight padding 上下要/2
     */
    private void setLineHeightWithPadding(MSCTextView textView, MSCCoverTextViewParams.Label label) {
        if (label.lineHeight != null) {
            textView.setLineHeightEx(DisplayUtil.roundWithDevice(label.lineHeight.floatValue()));
        } else {
            textView.setLineHeightEx(Math.round(textView.getTextSize() * 1.2f));
        }
    }

}
