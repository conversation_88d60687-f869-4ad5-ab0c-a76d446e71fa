package com.meituan.msc.modules.api.msi.components.coverview;

import android.support.annotation.Keep;
import android.view.MotionEvent;
import android.view.View;
import android.view.ViewConfiguration;

import com.meituan.msc.common.utils.DisplayUtil;
import com.meituan.msc.common.utils.HashMapHelper;
import com.meituan.msc.modules.api.msi.MSCViewContext;

public final class MSCCoverViewTouchHelper {
    private final int mTouchSlop;

    private int mLastMotionY = 0;
    private int mLastMotionX = 0;
    private int mActivePointerId = -1;
    boolean mIsBeingDragged = false;

    public MSCCoverViewTouchHelper(int scaledTouchSlop) {
        mTouchSlop = scaledTouchSlop;
    }

    public static void setupGestureTouchListener(View view, MSCViewContext viewContext, Boolean enableGesture) {
        //判断是否有手势，如果没有保持上次状态
        if (enableGesture == null || view == null) {
            return;
        }
        if (view.getTag("msc_touch".hashCode()) != null) {//如果有touch 直接更新状态
            MSCCoverViewTouchListener coverViewTouchListener = (MSCCoverViewTouchListener) view.getTag("msc_touch".hashCode());
            if (enableGesture != coverViewTouchListener.isEnable()) {
                coverViewTouchListener.setEnable(enableGesture);
            }
        } else {
            if (enableGesture) {//如果之前没有启用手势，创新监听器
                MSCCoverViewTouchListener coverViewTouchListener = new MSCCoverViewTouchListener(viewContext, ViewConfiguration.get(view.getContext()).getScaledTouchSlop());
                view.setTag("msc_touch".hashCode(), coverViewTouchListener);
                view.setOnTouchListener(coverViewTouchListener);
            }
        }
    }

    public void processMotionEvent(MotionEvent motionEvent, MSCViewContext viewContext) {
        String event = null;
        switch (motionEvent.getAction() & MotionEvent.ACTION_MASK) {
            case MotionEvent.ACTION_DOWN:
                mLastMotionY = (int) motionEvent.getY();
                mLastMotionX = (int) motionEvent.getX();
                mActivePointerId = motionEvent.getPointerId(0);
                event = "onTouchStart";
                break;
            case MotionEvent.ACTION_UP:
                mActivePointerId = -1;
                event = "onTouchEnd";
                break;
            case MotionEvent.ACTION_MOVE:
                final int activePointerId = mActivePointerId;
                if (activePointerId == -1) {
                    // If we don't have a valid id, the touch down wasn't on content.
                    break;
                }

                final int pointerIndex = motionEvent.findPointerIndex(activePointerId);
                if (pointerIndex == -1) {
                    break;
                }


                final int yDiff = (int) Math.abs(motionEvent.getY(pointerIndex) - mLastMotionY);
                final int xDiff = (int) Math.abs(motionEvent.getX(pointerIndex) - mLastMotionX);
                if (yDiff > mTouchSlop || xDiff > mTouchSlop) {
                    mLastMotionY = (int) motionEvent.getY(pointerIndex);
                    mLastMotionX = (int) motionEvent.getX(pointerIndex);
                    mIsBeingDragged = true;
                } else {
                    return;
                }
                event = "onTouchMove";
                break;
            case MotionEvent.ACTION_CANCEL:
                event = "onTouchCancel";
                break;
            default:
                break;
        }

        if (event != null) {
            viewContext.dispatchPageEvent(event, HashMapHelper.of("touches",covertMotionEvent(motionEvent)));
        }
    }

    private static TouchInfo[] covertMotionEvent(MotionEvent motionEvent) {
        TouchInfo[] infos = new TouchInfo[motionEvent.getPointerCount()];
        for (int i = 0; i < motionEvent.getPointerCount(); i++) {
            infos[i] = new TouchInfo(motionEvent.getPointerId(i),
                    (double) DisplayUtil.toWebValue(motionEvent.getX(i)),
                    (double) DisplayUtil.toWebValue(motionEvent.getY(i)));
        }
        return infos;
    }

    @Keep
    private static class TouchInfo {
        public int id;
        public double x;
        public double y;

        public TouchInfo(int id, double x, double y) {
            this.id = id;
            this.x = x;
            this.y = y;
        }
    }
}
