package com.meituan.msc.modules.api.msi.components.coverview;

import android.view.MotionEvent;
import android.view.View;
import android.view.ViewConfiguration;

import com.meituan.msc.modules.api.msi.MSCViewContext;


public class MSCCoverViewTouchListener implements View.OnTouchListener {
    private final MSCViewContext viewContext;
    private final MSCCoverViewTouchHelper coverViewTouchHelper;
    private boolean enable = true;

    public void setEnable(boolean enable) {
        this.enable = enable;
    }

    public boolean isEnable() {
        return enable;
    }

    public MSCCoverViewTouchListener(MSCViewContext viewContext, int scaledTouchSlop) {
        this.viewContext = viewContext;
        coverViewTouchHelper = new MSCCoverViewTouchHelper(scaledTouchSlop);
    }

    private Runnable longPress = new Runnable() {
        @Override
        public void run() {
            if (isLongPressed) {
                viewContext.dispatchPageEvent("onLongPress", null);
            }

        }
    };
    private volatile boolean isLongPressed = false;

    @Override
    public boolean onTouch(View v, MotionEvent event) {
        if (!enable) {
            return false;
        }

        coverViewTouchHelper.processMotionEvent(event, viewContext);
        switch (event.getAction()) {
            case MotionEvent.ACTION_DOWN:

                isLongPressed = true;
                v.postDelayed(longPress, ViewConfiguration.getLongPressTimeout());

                break;
            case MotionEvent.ACTION_MOVE:
                //  isLongPressed = true;
                break;
            case MotionEvent.ACTION_UP:
                isLongPressed = false;
                v.removeCallbacks(longPress);

                break;
        }
        return true;

    }
}
