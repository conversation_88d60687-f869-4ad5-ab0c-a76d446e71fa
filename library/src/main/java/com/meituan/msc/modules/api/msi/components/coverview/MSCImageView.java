package com.meituan.msc.modules.api.msi.components.coverview;

import android.annotation.SuppressLint;
import android.content.Context;
import android.widget.ImageView;


@SuppressLint("AppCompatCustomView")
public class MSCImageView extends ImageView implements ICoverViewUpdateRegistry{
    public MSCImageView(Context context) {
        super(context);
    }
    CoverUpdateObserver coverUpdateObserver;
    boolean enableCoverViewEvent;
    boolean isCustomCallOutView;


    @Override
    protected void onDetachedFromWindow() {
        super.onDetachedFromWindow();
        //自定义气泡情况，不移除监听Listener，用于响应点击事件
        if (!isCustomCallOutView) {
            setOnClickListener(null);
            setOnTouchListener(null);
        }
    }

    @Override
    public CoverUpdateObserver getCoverUpdateObserver() {
        return coverUpdateObserver;
    }

    @Override
    public void addUpdateCoverViewObserver(CoverUpdateObserver coverUpdateObserver) {
        this.coverUpdateObserver = coverUpdateObserver;
    }

    @Override
    public boolean enableCoverViewEvent() {
        return enableCoverViewEvent;
    }

    @Override
    public void setIsCustomCallOutView(boolean isCustomCallOut) {
        isCustomCallOutView = isCustomCallOut;
    }
}
