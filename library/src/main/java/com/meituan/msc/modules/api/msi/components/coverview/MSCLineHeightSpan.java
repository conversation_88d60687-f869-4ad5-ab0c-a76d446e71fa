package com.meituan.msc.modules.api.msi.components.coverview;

import android.graphics.Paint;
import android.text.style.LineHeightSpan;

public class MSCLineHeightSpan implements LineHeightSpan {
    private final int gravity = 16;
    public int height;

    public final boolean invalidate(float height) {
        return this.height != Math.round(height);
    }

    public MSCLineHeightSpan(float f) {
        this.height = Math.round(f);
    }
    /**
     * Classes that implement this should define how the height is being calculated.
     *
     * @param text       the text
     * @param start      the start of the line
     * @param end        the end of the line
     * @param spanstartv the start of the span
     * @param lineHeight the line height
     * @param fm         font metrics of the paint, in integers
     */
    @Override
    public final void chooseHeight(CharSequence text, int start, int end, int spanstartv, int lineHeight, Paint.FontMetricsInt fm) {
        int offset;
        if ((-fm.ascent) > this.height) {
            offset = -this.height;
            fm.ascent = offset;
            fm.top = offset;
            fm.descent = 0;
            fm.bottom = 0;
        } else if ((-fm.ascent) + fm.descent > this.height) {
            fm.descent = fm.bottom;
            offset = fm.descent - this.height;
            fm.ascent = offset;
            fm.top = offset;
        } else if ((-fm.ascent) + fm.bottom > this.height) {
            fm.top = fm.ascent;
            fm.bottom = fm.ascent + this.height;
        } else if ((-fm.top) + fm.bottom > this.height) {
            fm.top = fm.bottom - this.height;
        } else {
            offset = this.height - ((-fm.ascent) + fm.descent);
            offset = Math.round(((float) offset) / 2.0f);
            fm.top -= offset;
            fm.ascent -= offset;
            fm.bottom += offset;
            fm.descent = offset + fm.descent;
        }
    }
}
