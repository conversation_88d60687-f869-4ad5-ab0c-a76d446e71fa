package com.meituan.msc.modules.api.msi.components.coverview;

import android.content.Context;
import android.graphics.Canvas;
import android.graphics.Paint;
import android.graphics.Path;
import android.graphics.RectF;
import android.util.AttributeSet;
import android.view.MotionEvent;
import android.view.View;
import android.widget.FrameLayout;
import android.widget.ScrollView;

public class MSCScrollView extends FrameLayout implements IViewStyle {
    private float borderWidth;
    private float borderRadius;
    private int color;
    private int bgColor;
    private Paint mPaint = new Paint();
    FrameLayout rootFrame;
    private ScrollView scrollView;
    IScrollChange scrollChange;
    Path canvasPath = new Path();
    RectF canvasRectF = new RectF(0, 0, 0, 0);
    boolean scrollVertical = true;
    boolean scrollHorizontal = true;

    public MSCScrollView(Context context) {
        super(context);
        init();
    }

    public MSCScrollView(Context context, AttributeSet attributeSet) {
        super(context, attributeSet);
        init();
    }


    private void init() {
        this.scrollView = new ScrollView(getContext()) {
            @Override
            protected void onScrollChanged(int l, int t, int oldl, int oldt) {
                super.onScrollChanged(l, t, oldl, oldt);
                if (scrollChange != null)
                    scrollChange.onCoverScrollChange(rootFrame, l, t);
            }

            @Override
            public boolean onTouchEvent(MotionEvent ev) {
                switch (ev.getAction()) {
                    case MotionEvent.ACTION_MOVE:
                        if (!scrollVertical) {
                            return false;
                        }
                        break;
                }
                return super.onTouchEvent(ev);
            }
        };


        this.rootFrame = new FrameLayout(getContext());
        super.addView(this.scrollView, 0, new LayoutParams(LayoutParams.MATCH_PARENT, (LayoutParams.MATCH_PARENT)));
        this.scrollView.addView(this.rootFrame, 0, new LayoutParams(LayoutParams.MATCH_PARENT, LayoutParams.WRAP_CONTENT));

        this.mPaint.setStyle(Paint.Style.STROKE);
        this.mPaint.setAntiAlias(true);
        setWillNotDraw(false);

    }

    @Override
    public void draw(Canvas canvas) {
        boolean store;

        if (this.borderRadius > 0.0f) {
            canvas.save();

            canvasPath.reset();
            canvasRectF.set(0.0f, 0.0f, (float) getWidth(), (float) getHeight());
            canvasPath.addRoundRect(canvasRectF, this.borderRadius, this.borderRadius, Path.Direction.CW);
            canvas.clipPath(canvasPath);
        }
        if (this.bgColor != 0) {
            canvas.drawColor(this.bgColor);
        }
        if (this.borderWidth > 0.0f) {
            float halfBw = this.borderWidth / 2.0f;
            canvasRectF.set(halfBw, halfBw, ((float) getWidth()) - halfBw, ((float) getHeight()) - halfBw);
            canvas.drawRoundRect(canvasRectF, this.borderRadius, this.borderRadius, this.mPaint);
            if (this.borderRadius > 0.0f) {
                canvas.restore();
            }
            canvas.save();
            canvasPath.reset();
            float offset = 0.0f;
            if (this.borderRadius > 0.0f && this.borderRadius - this.borderWidth > 0.0f) {
                offset = this.borderRadius - this.borderWidth;
            }
            canvasRectF.set(this.borderWidth, this.borderWidth, ((float) getWidth()) - this.borderWidth,
                    ((float) getHeight()) - this.borderWidth);
            canvasPath.addRoundRect(canvasRectF, offset, offset, Path.Direction.CW);
            canvas.clipPath(canvasPath);
            store = true;
        } else {
            store = this.borderRadius > 0.0f;
        }
        int save = canvas.save();
        super.draw(canvas);
        canvas.restoreToCount(save);
        if (store) {
            canvas.restore();
        }
    }

    public final int getChildViewCount() {
        return this.rootFrame.getChildCount();
    }

    public void addView(View view, int i) {
        this.rootFrame.addView(view, i);
//        bringToFront();
    }

    public void addView(View view, int i, LayoutParams layoutParams) {
        this.rootFrame.addView(view, i, layoutParams);
    }

    public void removeView(View view) {
        this.rootFrame.removeView(view);
    }

    /**
     * borderRadius
     *
     * @param borderRadius
     */
    public final void setBorderRadius(float borderRadius) {
        this.borderRadius = borderRadius;
    }

    /**
     * borderColor
     *
     * @param color
     */
    public final void setColor(int color) {
        this.color = color;
        this.mPaint.setColor(color);
    }

    /**
     * borderWidth
     *
     * @param borderWidth
     */

    public final void setBorderWidth(float borderWidth) {
        this.borderWidth = borderWidth;
        this.mPaint.setStrokeWidth(borderWidth);
    }

    /**
     * bgColor
     */

    public final void setBgColor(int bgColor) {
        this.bgColor = bgColor;
    }

    @Override
    public boolean dispatchTouchEvent(MotionEvent motionEvent) {
        if (motionEvent.getActionMasked() == MotionEvent.ACTION_DOWN) {
            boolean result = true;
            float x = motionEvent.getX();
            float y = motionEvent.getY();
            if (this.borderRadius > 0.0f) {
                double pow = Math.pow((double) this.borderRadius, 2.0d);
                float width = (float) getWidth();
                float height = (float) getHeight();
                if (x < this.borderRadius) {
                    if (y < this.borderRadius) {
                        if (Math.pow((double) (this.borderRadius - y), 2.0d) + Math.pow((double) (this.borderRadius - x), 2.0d) > pow) {

                            result = false;

                        }
                    } else if (y > height - this.borderRadius) {
                        if (Math.pow((double) ((this.borderRadius + y) - height), 2.0d) + Math.pow((double) (this.borderRadius - x), 2.0d) > pow) {
                            result = false;

                        }
                    }
                } else if (x > width - this.borderRadius) {
                    if (y < this.borderRadius) {
                        if (Math.pow((double) (this.borderRadius - y), 2.0d) + Math.pow((double) ((x + this.borderRadius) - width), 2.0d) > pow) {
                            result = false;

//                            }
                        }
                    } else if (y > height - this.borderRadius) {
                        if (Math.pow((double) ((this.borderRadius + y) - height), 2.0d) + Math.pow((double) ((x + this.borderRadius) - width), 2.0d) > pow) {
                            result = false;

                        }
                    }
                }
                return result;
            }


        }
        return super.dispatchTouchEvent(motionEvent);
    }

    public void setScrollVertical(boolean scrollVertical) {
        this.scrollVertical = scrollVertical;
    }

    public void setScrollHorizontal(boolean scrollHorizontal) {
        this.scrollHorizontal = scrollHorizontal;
    }

    @Override
    public void scrollTo(int i, int i2) {
        this.scrollView.scrollTo(i, i2);
        invalidate();
    }

    @Override
    protected void onDetachedFromWindow() {
        super.onDetachedFromWindow();
        scrollChange = null;
    }

    public final FrameLayout getRootFrame() {
        return rootFrame;
    }
}