package com.meituan.msc.modules.api.msi.components.coverview;

import android.annotation.SuppressLint;
import android.content.Context;
import android.os.Build;
import android.support.annotation.Nullable;
import android.text.Spannable;
import android.text.TextUtils;
import android.util.AttributeSet;
import android.widget.TextView;

import com.meituan.msc.common.utils.FontMonkeyUtil;

@SuppressLint("AppCompatCustomView")
public class MSCTextView extends TextView implements ICoverViewUpdateRegistry {
    private MSCLineHeightSpan lineHeightSpan;
    CoverUpdateObserver coverUpdateObserver;
    boolean enableCoverViewEvent;
    boolean isCustomCallOutView;

    public MSCTextView(Context context) {
        super(context);
        init();
    }

    public MSCTextView(Context context, @Nullable AttributeSet attrs) {
        super(context, attrs);
        init();
    }

    public MSCTextView(Context context, @Nullable AttributeSet attrs, int defStyleAttr) {
        super(context, attrs, defStyleAttr);
        init();
    }

    private void init() {
        if (Build.BRAND.equalsIgnoreCase("xiaomi")) {
            if (FontMonkeyUtil.getFontMonkey() != null)
                setTypeface(FontMonkeyUtil.getFontMonkey());
        }
        super.setIncludeFontPadding(false);
        super.setLineSpacing(0.0f, 1.0f);
        super.setSpannableFactory(new Spannable.Factory() {
            public final Spannable newSpannable(CharSequence charSequence) {
                Spannable newSpannable = super.newSpannable(charSequence);
                if (!(lineHeightSpan == null || TextUtils.isEmpty(newSpannable))) {
                    newSpannable.setSpan(lineHeightSpan, 0, newSpannable.length(), 18);
                }
                return newSpannable;
            }
        });
    }

    @Override
    protected void onDetachedFromWindow() {
        super.onDetachedFromWindow();
        //自定义气泡情况，不移除监听Listener，用于响应点击事件
        if (!isCustomCallOutView) {
            setOnClickListener(null);
            setOnTouchListener(null);
        }
        lineHeightSpan = null;
    }

    public void setLineHeightEx(int lineHeight) {
        if (this.lineHeightSpan == null) {
            this.lineHeightSpan = new MSCLineHeightSpan((float) lineHeight);
        }
        if (this.lineHeightSpan.invalidate((float) lineHeight)) {
            invalidate();
        }
    }

    @Override
    public void setText(CharSequence text, BufferType type) {
        if (type == BufferType.NORMAL) {
            type = BufferType.SPANNABLE;
        }
        super.setText(text, type);
    }

    @Override
    public CoverUpdateObserver getCoverUpdateObserver() {
        return coverUpdateObserver;
    }

    @Override
    public void addUpdateCoverViewObserver(CoverUpdateObserver coverUpdateObserver){
        this.coverUpdateObserver = coverUpdateObserver;
    }

    @Override
    public void setIsCustomCallOutView(boolean isCustomCallOut) {
        isCustomCallOutView = isCustomCallOut;
    }

    @Override
    public boolean enableCoverViewEvent() {
        return enableCoverViewEvent;
    }

    public void setFakeBoldText(boolean fakeBoldText) {
        getPaint().setFakeBoldText(fakeBoldText);
    }
}