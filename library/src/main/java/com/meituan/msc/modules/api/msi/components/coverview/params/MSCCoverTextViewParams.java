package com.meituan.msc.modules.api.msi.components.coverview.params;

import com.google.gson.JsonElement;
import com.meituan.msi.annotations.MsiSupport;

/**
 * Created by letty on 2022/12/6.
 **/
@MsiSupport
public class MSCCoverTextViewParams {

    public Label label;

    public Boolean clickable;
    public Boolean gesture;
    public JsonElement data;
    public Boolean enableCoverViewEvent;

    @MsiSupport
    public static class Label {
        public String color;
        public Double fontSize;
        public String textAlign;//left、center、right
        public String fontWeight;//    bold、normal
        public String lineBreak;//ellipsis、clip、break-word、break-all
        public Double lineHeight;
        public String content;
        // 12.32.200 CoverText新增字体字段，可供业务自定义字体：https://km.sankuai.com/collabpage/2503649514
        public String fontFamily;
    }
}
