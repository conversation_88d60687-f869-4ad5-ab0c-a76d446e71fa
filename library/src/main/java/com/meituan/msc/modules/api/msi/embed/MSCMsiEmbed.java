package com.meituan.msc.modules.api.msi.embed;

import android.support.annotation.Nullable;

import com.meituan.msc.common.framework.Callback;
import com.meituan.msc.modules.container.IContainerManager;
import com.meituan.msc.modules.engine.MSCRuntime;
import com.meituan.msc.modules.page.IPageModule;
import com.meituan.msc.modules.page.embeddedwidget.EmbeddedManager;
import com.meituan.msc.modules.page.embeddedwidget.IMPInfo;
import com.meituan.msc.modules.page.embeddedwidget.MPWidget;
import com.meituan.msc.modules.page.embeddedwidget.MPWidgetClientManager;
import com.meituan.msc.modules.page.render.AppPageReporter;
import com.meituan.msc.modules.page.render.BaseRenderer;
import com.meituan.msc.modules.page.render.webview.IWebView;
import com.meituan.msc.modules.page.render.webview.MSCWebViewRenderer;
import com.meituan.msc.modules.page.view.PageViewWrapper;
import com.meituan.msc.modules.reporter.MSCLog;
import com.meituan.msi.view.IMsiEmbed;
import com.meituan.msi.view.IMsiEmbedCallback;
import com.meituan.msi.view.IMsiEmbedView;
import com.meituan.msi.view.MsiViewInfo;

public class MSCMsiEmbed implements IMsiEmbed {
    public static final String TAG = "MSCMsiEmbed";

    private MSCRuntime mRuntime;

    public MSCMsiEmbed(MSCRuntime runtime) {
        mRuntime = runtime;
    }

    @Override
    public void insertEmbedView(IMsiEmbedView view, MsiViewInfo viewInfo, IMsiEmbedCallback callback) {
        if (view == null || viewInfo == null) {
            if (callback != null) {
                callback.onFail("view or viewInfo is null", null);
            }
            return;
        }

        MPWidget mpWidget = new MPWidget();
        int pageId = viewInfo.pageId;
        MSCLog.i(TAG, "insertEmbedView viewInfo viewName:", viewInfo.viewName,
                ",appId:", viewInfo.appId,
                ",pageId:", viewInfo.pageId,
                ",viewContainerId:", viewInfo.viewContainerId);
        mpWidget.setMpInfo(new IMPInfo() {
            @Override
            public String getMPViewName() {
                return viewInfo.viewName;
            }

            @Override
            public String getMPAppId() {
                return viewInfo.appId;
            }

            @Override
            public int getMPPageId() {
                return pageId;
            }

            @Override
            public String getMPContainerId() {
                return viewInfo.viewContainerId;
            }
        });

        MPWidgetClientManager.getMPWidgetWithClientWithRetry(mpWidget, new Callback<Void>() {
            @Override
            public void onSuccess(Void data) {
                MSCLog.i(TAG, "getMPWidgetWithClientWithRetry onSuccess");
                mpWidget.setEmbedView(new MSCMsiEmbedViewWrapper(view));
                if (callback != null) {
                    callback.onSuccess();
                }
            }

            @Override
            public void onFail(String errMsg, Exception error) {
                MSCLog.i(TAG, "getMPWidgetWithClientWithRetry onFail errMsg:", errMsg);
                if (callback != null) {
                    callback.onFail(errMsg, error);
                }
            }

            @Override
            public void onCancel() {
                MSCLog.i(TAG, "getMPWidgetWithClientWithRetry onCancel");
                if (callback != null) {
                    callback.onFail("cancel", null);
                }
            }
        }, getReporter(pageId));

    }

    @Override
    public boolean isWidgetClientReady(IMsiEmbedView view) {
        if (view == null) {
            return false;
        }

        if (view.getSlWidget() instanceof MPWidget) {
            return ((MPWidget) view.getSlWidget()).isWidgetClientReady();
        }

        return false;
    }

    @Override
    public void reBindEmbedView(IMsiEmbedView view, IMsiEmbedCallback callback) {
        if (view == null) {
            if (callback != null) {
                callback.onFail("view is null ", null);
            }
            return;
        }
        Object slWidget = view.getSlWidget();
        if (!(slWidget instanceof MPWidget)) {
            if (callback != null) {
                callback.onFail("MPWidget is not match", null);
            }
            return;
        }

        MPWidget mpWidget = (MPWidget) slWidget;

        int pageId = mpWidget.getMPPageId();
        MPWidgetClientManager.getMPWidgetWithClientWithRetry(mpWidget, new Callback<Void>() {

            @Override
            public void onSuccess(Void data) {
                MSCLog.i(TAG, "updateCoverView rebind client success");
                if (callback != null) {
                    callback.onSuccess();
                }
            }

            @Override
            public void onFail(String errMsg, Exception error) {
                MSCLog.i(TAG, "updateCoverView rebind client failed");
                if (callback != null) {
                    callback.onFail(errMsg, error);
                }
            }

            @Override
            public void onCancel() {
                if (callback != null) {
                    callback.onFail("cancel", null);
                }
            }
        }, getReporter(pageId));
    }

    @Override
    public boolean isSupportEmbed(String viewName, int pageId) {
        IPageModule pageModule = getPageModule(pageId);
        if (pageModule != null) {
            PageViewWrapper wrapper = (PageViewWrapper) pageModule.asView();
            if (wrapper != null) {
                BaseRenderer renderer = wrapper.getRenderer();
                if (renderer instanceof MSCWebViewRenderer) {
                    MSCWebViewRenderer webViewRenderer = (MSCWebViewRenderer) renderer;
                    IWebView iWebView = webViewRenderer.getIWebView();
                    if (iWebView != null) {
                        return EmbeddedManager.supportEmbed(iWebView.getWebView());
                    }
                }
            }
        }
        return false;
    }

    @Nullable
    private IPageModule getPageModule(int pageId) {
        IPageModule pageModule = null;
        IContainerManager containerManager = mRuntime.getContainerManagerModule();
        if (containerManager != null) {
            pageModule = containerManager.getPageByPageId(pageId);
        }
        return pageModule;
    }


    public AppPageReporter getReporter(int pageId) {
        IPageModule pageModule = getPageModule(pageId);
        return pageModule == null ? null : pageModule.getReporter();
    }
}
