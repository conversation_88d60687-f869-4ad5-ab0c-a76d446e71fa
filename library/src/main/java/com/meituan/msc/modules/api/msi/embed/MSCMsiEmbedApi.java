package com.meituan.msc.modules.api.msi.embed;

import android.support.annotation.StringDef;

import com.meituan.msc.modules.api.msi.MSCApi;
import com.meituan.msc.modules.page.IPageModule;
import com.meituan.msc.modules.page.embeddedwidget.EmbedProvider;
import com.meituan.msc.modules.page.embeddedwidget.EmbeddedManager;
import com.meituan.msc.modules.page.render.BaseRenderer;
import com.meituan.msc.modules.page.render.webview.IWebView;
import com.meituan.msc.modules.page.render.webview.MSCWebViewRenderer;
import com.meituan.msc.modules.page.render.webview.WebViewCacheManager;
import com.meituan.msc.modules.page.view.PageViewWrapper;
import com.meituan.msi.annotations.MsiApiEnv;
import com.meituan.msi.annotations.MsiApiMethod;
import com.meituan.msi.annotations.MsiSupport;
import com.meituan.msi.api.IMsiApi;
import com.meituan.msi.bean.ContainerInfo;
import com.meituan.msi.bean.MsiContext;
import com.meituan.mtwebkit.internal.MTWebViewManager;
import com.sankuai.meituan.serviceloader.annotation.ServiceLoaderInterface;

import java.lang.annotation.Retention;
import java.lang.annotation.RetentionPolicy;

/**
 * https://km.sankuai.com/collabpage/**********
 * Created by letty on 2023/3/15.
 **/

@ServiceLoaderInterface(key = "mscEmbed", interfaceClass = IMsiApi.class)
@MsiApiEnv(name = ContainerInfo.ENV_MSC)
public class MSCMsiEmbedApi extends MSCApi {
    @Retention(RetentionPolicy.SOURCE)
    @StringDef({REASON_DEFAULT, REASON_DISABLE_MT_WEB_VIEW, REASON_NOT_SUPPORT_SAME_LAYER})
    public @interface WebViewReadyReason {}

    public static final String REASON_DEFAULT = "default";
    //MtWebView云控关闭
    public static final String REASON_DISABLE_MT_WEB_VIEW = "disable use MtWebView!";
    //MtWebView不支持同层渲染
    public static final String REASON_NOT_SUPPORT_SAME_LAYER = "not support sameLayer!";

    @MsiApiMethod(name = "isMtWebViewReady", env = {ContainerInfo.ENV_MSC}, response = WebViewReadyResponse.class, scope = "msc")
    public void isMtWebViewReady(MsiContext context) {
        WebViewReadyResponse response = new WebViewReadyResponse();
        boolean enableMtWebView = WebViewCacheManager.getInstance().useMtWebViewByAppId(getAppId());
        boolean possibleSupportSameLayer = MTWebViewManager.checkPossibleSupportSameLayer();
        if (!enableMtWebView) {
            response.reason = REASON_DISABLE_MT_WEB_VIEW;
        } else if (!possibleSupportSameLayer) {
            response.reason = REASON_NOT_SUPPORT_SAME_LAYER;
        }
        response.result = enableMtWebView && possibleSupportSameLayer;
        context.onSuccess(response);
    }

    @MsiSupport
    public static class WebViewReadyResponse {
        public boolean result;
        @MSCMsiEmbedApi.WebViewReadyReason
        public String reason = MSCMsiEmbedApi.REASON_DEFAULT;
    }

    @MsiSupport
    public static class EmbedComponent {
        public String componentName;
    }

    @MsiApiMethod(name = "isEmbedSupportSync", env = {ContainerInfo.ENV_MSC}, request = EmbedComponent.class, response = boolean.class, scope = "msc")
    public boolean isEmbedSupportSync(EmbedComponent param, MsiContext context) {
        if (!EmbedProvider.supportWidgetsSet.contains(param.componentName)) {
            return false;
        }
        IPageModule pageModule = getPageById(context.getPageId());
        if (pageModule != null) {
            PageViewWrapper wrapper = (PageViewWrapper) pageModule.asView();
            if (wrapper != null) {
                BaseRenderer renderer = wrapper.getRenderer();
                if (renderer instanceof MSCWebViewRenderer) {
                    MSCWebViewRenderer webViewRenderer = (MSCWebViewRenderer) renderer;
                    IWebView iWebView = webViewRenderer.getIWebView();
                    if (iWebView != null) {
                        return EmbeddedManager.supportEmbed(iWebView.getWebView());
                    }
                }
            }
        }
        return true;
    }

}
