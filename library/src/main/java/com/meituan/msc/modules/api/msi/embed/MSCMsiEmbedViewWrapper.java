package com.meituan.msc.modules.api.msi.embed;

import android.view.MotionEvent;
import android.view.Surface;

import com.meituan.msc.modules.page.embeddedwidget.IEmbedView;
import com.meituan.msc.modules.page.embeddedwidget.MPWidget;
import com.meituan.msi.view.IMsiEmbedView;

public class MSCMsiEmbedViewWrapper implements IEmbedView {

    private IMsiEmbedView msiEmbedView;

    public MSCMsiEmbedViewWrapper(IMsiEmbedView msiEmbedView) {
        this.msiEmbedView = msiEmbedView;
    }

    @Override
    public void setSurface(Surface surface) {
        if (msiEmbedView != null) {
            msiEmbedView.setSurface(surface);
        }
    }

    @Override
    public void onSizeChanged(Surface surface, int width, int height) {
        if (msiEmbedView != null) {
            msiEmbedView.onSizeChanged(surface, width, height);
        }
    }

    @Override
    public boolean dispatchTouchEvent(MotionEvent ev) {
        if (msiEmbedView != null) {
            msiEmbedView.dispatchTouchEvent(ev);
        }
        return false;
    }

    @Override
    public void onVisibilityChanged(boolean visibility) {
        if (msiEmbedView != null) {
            msiEmbedView.onVisibilityChanged(visibility);
        }
    }

    @Override
    public void onDestroySurface() {
        if (msiEmbedView != null) {
            msiEmbedView.onDestroySurface();
        }
    }

    @Override
    public void setMPWidget(MPWidget mpWidget) {
        if (msiEmbedView != null) {
            msiEmbedView.setSLWidget(mpWidget);
        }
    }

    @Override
    public MPWidget getMPWidget() {
        if (msiEmbedView != null) {
            if (msiEmbedView.getSlWidget() instanceof MPWidget) {
                return (MPWidget) msiEmbedView.getSlWidget();
            }
        }
        return null;
    }

    @Override
    public void onCreateView(boolean isEmbed) {

    }

    @Override
    public boolean isEmbed() {
        return false;
    }

    @Override
    public boolean isWidgetClientReady() {
        return false;
    }
}
