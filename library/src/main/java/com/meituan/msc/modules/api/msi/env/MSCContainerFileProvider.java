package com.meituan.msc.modules.api.msi.env;

import com.meituan.msc.lib.interfaces.IFileModule;
import com.meituan.msc.modules.engine.MSCRuntime;
import com.meituan.msi.provider.ContainerFileProvider;

import java.io.File;


/**
 * create by <PERSON><PERSON><PERSON><PERSON> on 2021-09-13.
 */
public class MSCContainerFileProvider implements ContainerFileProvider {
    public MSCRuntime mRuntime;

    public MSCContainerFileProvider(MSCRuntime runtime) {
        mRuntime = runtime;
    }

    //fixme msc 这里是mmp迁移过来的 这么粗暴的判断？
    @Override
    public boolean canRead(String path) {
        return false;
    }

    @Override
    public boolean canWrite(String path) {
        return false;
    }

    @Override
    public String read(String path, String encoding) {
        return null;
    }

    @Override
    public void write(String path, String data) {

    }

    @Override
    public String getRealPath(String path) {
        return mRuntime.getModule(IFileModule.class).getAbsolutePath(path);
    }

    @Override
    public File getMiniAppDir() {
        return mRuntime.getModule(IFileModule.class).getMiniAppDir();
    }

    @Override
    public String getTmpDir() {
        return mRuntime.getModule(IFileModule.class).getMiniAppTempPath();
    }

    @Override
    public String getContainerTmpFile(String fileName) {
        return mRuntime.getModule(IFileModule.class).getContainerFileScheme() + fileName;
    }

    @Override
    public String getTempFileName(String fileName, int type) {
        if (type == 0) { //tmp
            return "tmp_" + fileName;
        }
        return fileName;
    }

    @Override
    public String getUsrDir() {
        return mRuntime.getModule(IFileModule.class).getMiniAppUserDataPath();
    }
}
