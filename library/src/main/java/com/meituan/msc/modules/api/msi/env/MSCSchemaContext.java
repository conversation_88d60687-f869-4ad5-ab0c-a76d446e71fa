package com.meituan.msc.modules.api.msi.env;

import android.content.Intent;
import android.text.TextUtils;

import com.meituan.msc.lib.interfaces.container.MSCParams;
import com.meituan.msc.modules.container.ContainerController;
import com.meituan.msc.modules.container.MSCActivity;
import com.meituan.msc.modules.engine.MSCRuntime;
import com.meituan.msi.bean.ApiException;
import com.meituan.msi.context.ISchemaContext;

import org.json.JSONException;
import org.json.JSONObject;

public class MSCSchemaContext implements ISchemaContext {
    private MSCRuntime mRuntime;

    public MSCSchemaContext(MSCRuntime runtime){
        this.mRuntime = runtime;
    }

    @Override
    public boolean support(String apiName, String schema) {
        return true;
    }

    @Override
    public Intent extendIntent(String apiName, Intent intent, String extra) throws ApiException {
        if (TextUtils.equals(apiName, "openLink") && !TextUtils.isEmpty(extra)) {
            try {
                String name = intent.getStringExtra("name");
                Class<?> activityClass = Class.forName(name);
                if (MSCActivity.class.isAssignableFrom(activityClass)) {
                    intent.putExtra(ContainerController.START_FROM_MIN_PROGRAM, true);
                    intent.putExtra(MSCParams.SRC_APP_ID, mRuntime.getAppId());
                    intent.putExtra(MSCParams.EXTRA_DATA, new JSONObject().put(MSCParams.EXTRA_DATA, extra).toString());
                }
                return intent;
            } catch (ClassNotFoundException | JSONException e) {
                throw new ApiException("class not found or JSONException");
            }
        }
        return intent;
    }
}
