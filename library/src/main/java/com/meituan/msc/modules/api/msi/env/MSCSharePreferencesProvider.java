package com.meituan.msc.modules.api.msi.env;

import android.content.SharedPreferences;

import com.meituan.msc.common.utils.MSCSharedPreferences;
import com.meituan.msc.extern.MSCEnvHelper;
import com.meituan.msc.lib.interfaces.IFileModule;
import com.meituan.msc.modules.engine.MSCRuntime;
import com.meituan.msi.provider.SharedPreferencesProvider;
import com.meituan.msc.modules.engine.MSCHornRollbackConfig;

import java.io.File;

import static com.meituan.msc.extern.MSCEnvHelper.getContext;

public class MSCSharePreferencesProvider implements SharedPreferencesProvider {
    private static final long BYTE_LIMIT = 10 * 1024 * 1024;
    private static final long EXTERNAL_APP_BYTE_LIMIT = 5 * 1024 * 1024;
    private MSCRuntime mRuntime = null;

    public MSCSharePreferencesProvider(MSCRuntime runtime) {
        mRuntime = runtime;
    }

    @Override
    public long getSize(String fileName) {
        long res = 0;

        File spFile = getPreferenceFile(fileName);

        if (spFile.exists()) {
            res = spFile.length();
        } else {
            return MSCSharedPreferences.getSize(getContext(), fileName);
        }

        return res;
    }

    @Override
    public SharedPreferences getSharedPreferences(String spPrefer) {
        return MSCEnvHelper.getSharedPreferences(getContext(), spPrefer);
    }

    @Override
    public SharedPreferences getSharedPreferences() {
        String spPrefer = getSharePreferencesPath();
        return MSCEnvHelper.getSharedPreferences(getContext(), spPrefer);
    }

    private File getPreferenceFile(String fileName) {
        return new File(getContext().getApplicationInfo().dataDir + "/shared_prefs/" + fileName + ".xml");
    }

    /**
     * 获取 sp路径，优先获取用户维度的存储空间名称
     *
     * @return
     */
    @Override
    public String getSharePreferencesPath() {
        if (mRuntime == null) {
            return IFileModule.PREFIX_MSC;
        }
        String fileName = mRuntime.getFileModule().getSharePreferencesPath();
        if (fileName == null) {
            return IFileModule.PREFIX_MSC;
        }
        return fileName;
    }

    @Override
    public long limitSize() {
        if (MSCHornRollbackConfig.enableExternalAppStorageLimit()) {
            if (mRuntime != null) {
                boolean isExternalApp = mRuntime.getMSCAppModule().getExternalApp();
                return isExternalApp ? EXTERNAL_APP_BYTE_LIMIT : BYTE_LIMIT;
            } else {
                return BYTE_LIMIT;
            }
        } else {
            return BYTE_LIMIT;
        }
    }
}
