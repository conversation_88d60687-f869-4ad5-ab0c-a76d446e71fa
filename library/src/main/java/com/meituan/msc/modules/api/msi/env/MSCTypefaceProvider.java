package com.meituan.msc.modules.api.msi.env;

import android.app.Activity;
import android.content.res.AssetManager;
import android.graphics.Typeface;

import com.meituan.msc.common.utils.ViewUtils;
import com.meituan.msc.lib.interfaces.IFontfaceModule;
import com.meituan.msc.modules.apploader.LaunchInfo;
import com.meituan.msc.modules.container.IContainerManager;
import com.meituan.msc.modules.engine.MSCRuntime;
import com.meituan.msc.modules.msi.MSIManagerModule;
import com.meituan.msc.modules.reporter.MSCLog;
import com.meituan.msi.provider.TypefaceProvider;

import java.lang.ref.WeakReference;

public class MSCTypefaceProvider implements TypefaceProvider {

    private final WeakReference<MSCRuntime> runtimeRef;
    private static final String TAG = "MSCTypefaceProvider";


    public MSCTypefaceProvider(MSCRuntime runtime) {
        this.runtimeRef = new WeakReference<>(runtime);
    }

    @Override
    public Typeface getTypeface(String s) {
        MSCRuntime runtime = runtimeRef.get();
        if (runtime == null) {
            return null;
        }
        IContainerManager managerModule = runtime.getContainerManagerModule();
        if (managerModule == null) {
            MSCLog.i(TAG, "getActivity,msc app exit");
            return null;
        }
        Activity topActivity = managerModule.getTopActivity();
        if (topActivity == null || topActivity.getAssets() == null) {
            return null;
        }
        AssetManager assetManager =  topActivity.getAssets();
        IFontfaceModule module = runtime.getModule(IFontfaceModule.class);
        if (module == null) {
            return null;
        }
        return ViewUtils.getTypefaceBase(runtime.getModule(LaunchInfo.class), module, s, 0, assetManager);
    }
}
