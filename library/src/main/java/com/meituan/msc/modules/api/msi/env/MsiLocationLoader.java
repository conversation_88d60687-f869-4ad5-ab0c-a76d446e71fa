package com.meituan.msc.modules.api.msi.env;

import android.support.annotation.NonNull;

import com.meituan.msc.modules.api.map.ILocation;
import com.meituan.msc.modules.api.map.ILocationLoader;
import com.meituan.msi.api.location.MsiLocation;
import com.meituan.msi.location.IMsiLocation;
import com.meituan.msi.location.IMsiLocationLoader;

/**
 * <AUTHOR>
 * @date 2021/9/26.
 */
public class MsiLocationLoader implements IMsiLocationLoader {

    private final ILocationLoader locationLoader;

    public MsiLocationLoader(ILocationLoader locationLoader) {
        this.locationLoader = locationLoader;
    }

    @Override
    public void startLocation(@NonNull IMsiLocation mapLocation, @NonNull String type) {
        locationLoader.startLocation(new ILocation() {
            @Override
            public void onLocation(int error, MsiLocation location, String errMsg) {
                mapLocation.onLocation(error, location, errMsg);
            }
        }, type);
    }

    @Override
    public void stopLocation() {
        locationLoader.stopLocation();
    }
}