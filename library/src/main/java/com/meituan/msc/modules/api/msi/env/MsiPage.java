package com.meituan.msc.modules.api.msi.env;

import android.graphics.Point;
import android.graphics.Rect;
import android.os.Handler;
import android.os.Looper;
import android.support.annotation.NonNull;
import android.support.annotation.Size;
import android.text.TextUtils;
import android.view.MotionEvent;
import android.view.View;
import android.view.ViewConfiguration;
import android.view.ViewGroup;
import android.widget.TextView;

import com.meituan.msc.modules.page.IMSCViewGroup;
import com.meituan.msc.modules.page.IPageModule;
import com.meituan.msi.api.component.input.MSIBaseInputEvent;
import com.meituan.msi.api.component.textaera.MSITextAreaOriginPositionManager;
import com.meituan.msi.api.component.textaera.TextArea;
import com.meituan.msi.dispather.IMsiDispatcher;
import com.meituan.msi.page.ComponentAdjustType;
import com.meituan.msi.page.ComponentParam;
import com.meituan.msi.page.ComponentPositionManager;
import com.meituan.msi.page.IKeyBoard;
import com.meituan.msi.page.IKeyBoardHeightChangeObserver;
import com.meituan.msi.page.IPage;
import com.meituan.msi.page.IViewGroup;
import com.meituan.msi.page.IViewListener;
import com.meituan.msi.util.DisplayUtil;


public class MsiPage implements IPage {
    IPageModule mPageModule;
    int webViewHeight;

    public MsiPage(@NonNull IPageModule pageModule) {
        mPageModule = pageModule;
    }

    @Override
    public IViewGroup getViewGroup() {
        return mPageModule != null ? mPageModule.getIViewGroupImpl() : null;
    }

    @Override
    public String getPagePath() {
        if (mPageModule != null) {
            return mPageModule.getPagePath();
        }
        return null;
    }

    @Override
    public IKeyBoard getKeyBoard() {
        return iKeyBoard;
    }

    @Override
    public void showView(int type, View view, ViewParam viewParam) {
        if (type == TYPE_TOAST) {
            if (mPageModule != null) {
                mPageModule.showToast(view, viewParam);
            }
        }
    }

    @Override
    public View findView(int type, ViewParam viewParam) {
        if (mPageModule != null && type == TYPE_TOAST) {
            return mPageModule.getToast();
        }
        return null;
    }

    @Override
    public void hideView(int type, View view, ViewParam viewParam) {
        if (type == TYPE_TOAST) {
            if (mPageModule != null) {
                mPageModule.hideToast();
            }
        }
    }

    @Override
    public View getAndCreateView(int type, String viewId, IViewListener listener) {
        if (mPageModule != null && type == TYPE_INFO_WINDOW) {
            IMSCViewGroup viewGroup = mPageModule.getIViewGroupImpl();
            if (viewGroup != null) {
                return viewGroup.addLayerUpdateListener(viewId, listener);
            }
        }

        return null;
    }

    private IKeyBoard iKeyBoard = new IKeyBoard() {
        @Override
        public int getKeyBoardHeight() {
            if (mPageModule != null) {
                return mPageModule.getKeyboardHeight();
            }
            return 0;
        }

        @Override
        public void registerKeyBoardHeightChanged(IKeyBoardHeightChangeObserver listener) {
            if (mPageModule != null) {
                mPageModule.registerKeyboardChange(listener);
            }
        }

        @Override
        public void unregisterKeyBoardHeightChanged(IKeyBoardHeightChangeObserver listener) {
            if (mPageModule != null) {
                mPageModule.unregisterKeyboardChange(listener);
            }
        }

        @Override
        public void adjustPosition(int offset, int keyboardHeight, boolean scroll) {
            if (mPageModule != null) {
                mPageModule.adjustPosition(offset, keyboardHeight, scroll);
            }
        }

        @Override
        public void adjustPosition(View view, String adjustKeyboardTo, int cursorSpacing, int bottomInsetHeight, int delayDur) {
            if (mPageModule != null) {
                mPageModule.adjustPosition(view, adjustKeyboardTo, cursorSpacing, bottomInsetHeight, delayDur);
            }
        }

        @Override
        public void getLocationInWindow(@Size(2) int[] outLocation) {
            if (mPageModule != null) {
                mPageModule.getLocationInWindow(outLocation);
            } else {
                outLocation[0] = outLocation[1] = 0;
            }
        }

        @Override
        public int getContentHeight() {
            if (mPageModule != null) {
                return mPageModule.getContentHeight();
            }
            return 0;
        }

        //todo 调整textarea位置，后续进行优化
        @Override
        public boolean onViewEvent(ComponentAdjustType componentAdjustType, View view, ComponentPositionManager manager, IMsiDispatcher eventDispatcher, ComponentParam param) {
            if (componentAdjustType == ComponentAdjustType.onLayout && mPageModule != null) {
                return onLayout(view, manager, eventDispatcher, param);
            }

            if (componentAdjustType == ComponentAdjustType.onSizeChanged) {
                return onSizeChanged(view, manager, eventDispatcher, param);
            }

            if (componentAdjustType == ComponentAdjustType.tryAdjustPosition) {
                return tryAdjustPosition(view, manager, eventDispatcher, param);
            }

            if (componentAdjustType == ComponentAdjustType.onTouchEvent) {
                return onTouchEvent(view, manager, eventDispatcher, param);
            }
            return false;
        }

        private Handler mHandler = new Handler(Looper.getMainLooper());
        boolean longPress = false;
        Runnable mLongPressed = new Runnable() {
            public void run() {
                //Log.i("Long-press", "Long press!");
                longPress = true;
            }
        };

        private boolean onLayout(View view, ComponentPositionManager manager, IMsiDispatcher eventDispatcher, ComponentParam param) {
            int[] locationOnScreen = new int[2];
            view.getLocationOnScreen(locationOnScreen);
            int topOnScreen = locationOnScreen[1];
            final int webViewTop = mPageModule.getNavigationBarHeight() + param.statusBarHeight;
            int bottomToWebViewTop = topOnScreen + param.height - webViewTop; // 本View底边距离WebView顶部的距离

            int bottomToWebViewContentTop = bottomToWebViewTop + mPageModule.getWebScrollY();
            int originTopOnScreen = topOnScreen + mPageModule.getWebScrollY();
            //TODO top算法与bottom不一致，top的坐标是onScreen的，但又表示的是WebView内容不滚动时的值，含义不明确
            if (manager instanceof MSITextAreaOriginPositionManager) {
                ((MSITextAreaOriginPositionManager) manager).itemsOriginBottom.put(param.viewId, bottomToWebViewContentTop);
                ((MSITextAreaOriginPositionManager) manager).itemsOriginTop.put(param.viewId, originTopOnScreen);
            }
            return true;
        }

        private boolean onSizeChanged(View view, ComponentPositionManager manager, IMsiDispatcher eventDispatcher, ComponentParam param) {
            if (param.oldh != 0 && manager != null) {
                if (view instanceof TextView && ((TextView) view).isCursorVisible()) {
                    if (TextUtils.isEmpty(param.viewId)) {
                        return false;
                    }
                    if (manager instanceof MSITextAreaOriginPositionManager) {
                        try {
                            Integer rawBottom = ((MSITextAreaOriginPositionManager) manager).itemsOriginBottom.get(param.viewId);
                            Integer rawTop =  ((MSITextAreaOriginPositionManager) manager).itemsOriginTop.get(param.viewId);
                            rawTop = rawTop == null ? 0 : rawTop;
                            rawBottom = rawBottom == null ? 0 : rawBottom;
                            ((MSITextAreaOriginPositionManager) manager).itemsOriginBottom.put(param.viewId, rawBottom + param.h - param.oldh);
                            manager.updatePosition(rawTop, param.h - param.oldh);
                        } catch (Exception e) {
                            return false;
                        }
                    }
                } else {
                    if (TextUtils.isEmpty(param.viewId)) {
                        return false;
                    }
                    try {
                        Integer originBottom = ((MSITextAreaOriginPositionManager) manager).itemsOriginBottom.get(param.viewId);
                        originBottom = originBottom == null ? 0 : originBottom;
                        Integer originTop = ((MSITextAreaOriginPositionManager) manager).itemsOriginTop.get(param.viewId);
                        originTop = originTop == null ? 0 : originTop;
                        int changedBottom = originBottom + param.h - param.oldh;
                        int changedTop = originTop + param.h - param.oldh;
                        ((MSITextAreaOriginPositionManager) manager).itemsOriginBottom.put(param.viewId, changedBottom);
                        ((MSITextAreaOriginPositionManager) manager).itemsOriginTop.put(param.viewId, changedTop);
                    } catch (Exception e) {
                        return false;
                    }
                }
            }
            if (!param.autoHeight) {
                return false;
            }

            int currentHC = param.h - param.oldh;
            try {
                boolean deleteEventDispatch = false;
                // com.meituan.msi.page.IKeyBoard.onViewEvent 目前只有 TextArea 在使用，使用同一个回滚开关
                if (view instanceof TextArea) {
                    TextArea textArea = (TextArea) view;
                    deleteEventDispatch = textArea.fixLineCountEvent();
                }
                if (!deleteEventDispatch) {
                    // 功能稳定后会删除此段代码
                    MSIBaseInputEvent event = new MSIBaseInputEvent();
                    event.lineCount = param.lineCount;
                    event.height = Math.round(DisplayUtil.toWebValue(param.h));
                    event.viewId = param.viewId;
                    if (null != eventDispatcher) {
                        eventDispatcher.dispatchEvent("onTextAreaHeightChange", event);
                    }
                }
                if (param.keyboardHeight == 0) {
                    return false;
                }
                if (param.isKeyboardShow) {
                    tryAdjustPositionOnSizeChanged(view, ((MSITextAreaOriginPositionManager) manager), currentHC, param.viewId, param.keyboardHeight);
                }
            } catch (Exception e) {
                e.printStackTrace();
            }
            return false;
        }

        private boolean tryAdjustPosition(View view, ComponentPositionManager manager, IMsiDispatcher eventDispatcher, ComponentParam param) {
            Rect rect = new Rect();
            view.getGlobalVisibleRect(rect); //可见区域，如果被部分遮挡，遮挡区域不算
            int[] location = new int[2];
            getLocationInWindow(location);  //view左上角位置，被遮挡不影响
            int textTop = Math.max(location[1], rect.top);

            int confirmBarHeight = 0;
            if (param.showConfirmBar && param.mSoftKeyboardTopPopupWindow != null) {
                confirmBarHeight = param.mSoftKeyboardTopPopupWindow.getContentView().getMeasuredHeight();
            }
            int viewHeight = view.getHeight();
            int textBottom = textTop + viewHeight + param.cursorSpacing;   // 需可见部分底部距屏幕顶部距离，可能超出屏幕底部
            int screenHeight = DisplayUtil.getScreenHeightReal();

            int keyTop = screenHeight - param.keyboardHeight - confirmBarHeight; //键盘高度
            int webOffset = textBottom - keyTop;   // 为使需可见部分可见而需要上提WebView的高度
            boolean shouldAdjust = false;
            if (webOffset > 0) {
                // 通常为输入框被键盘遮挡（textBottom > keyTop），需要上推
                shouldAdjust = true;
            } else if (webOffset < 0) {
                // 但也存在短暂获取键盘高度错误，导致键盘顶部留空的情况，需要往回调整
                // 保守起见，目前仅在调整范围能被pan吸收的情况下调整，不进行scroll
                if (mPageModule.getPan() > 0 && mPageModule.getPan() >= -webOffset) {
                    shouldAdjust = true;
                }
            }

            if (!shouldAdjust) {
                return false;
            }

            mPageModule.getLocationInWindow(location);
            int hostBottom = location[1] + mPageModule.getHeight();
            int tabBarHeight = 0;
            //todo tabbar处理
//            if (hostPage.getTabBar() != null) {
//                tabBarHeight = hostPage.getTabBar().getHeight();
//            }
            // 当Page离屏幕底部有距离时（widget），因pan只能在Page内不含tab的区域生效，需要减去无法操作的部分
            int maxPan = param.keyboardHeight + confirmBarHeight - (screenHeight - hostBottom - tabBarHeight);
            int panOffset = Math.min(webOffset, maxPan);
            int scrollOffset = webOffset - panOffset;
            //通过添加空白上移hostPage到合适的位置
            mPageModule.adjustPan(panOffset);

            //todo, 滑动WebView页面内容到合适的位置
//            if (scrollOffset > 0) {
//                int maxScroll = hostPage.getCurrentWebViewPageHeight() - hostPage.getCurrentWebViewHeight() - hostPage.getWebScrollY();
//                if (maxScroll < 0) {
//                    maxScroll = 0;
//                }
//                if (scrollOffset > maxScroll) {
//                    scrollOffset = maxScroll;
//                }
//
//                int currScroll = hostPage.getWebScrollY();
//                hostPage.scrollYEx(scrollOffset);
//            } else {
//                hostPage.scrollYEx(0);
//            }
            return true;
        }

        private boolean onTouchEvent(View view, ComponentPositionManager manager, IMsiDispatcher eventDispatcher, ComponentParam param) {
            float motionDownY = 0;
            float lastTouchRawY = 0;
            final int webViewTop = mPageModule.getNavigationBarHeight() + param.statusBarHeight;
            if (manager instanceof MSITextAreaOriginPositionManager && ((MSITextAreaOriginPositionManager) manager).itemsOriginTop.get(param.viewId) == null) {
                return false;
            }

            Rect hostPageRect = new Rect(); //父级位置
            ((ViewGroup) view.getParent().getParent()).getGlobalVisibleRect(hostPageRect);

            if (param.event == null) {
                return false;
            }
            if (param.event.getAction() == MotionEvent.ACTION_DOWN) {
                webViewHeight = mPageModule.getContentHeight();
                motionDownY = param.event.getY();
                //Log.d("keyboard-msg-down",mRawViewId);

                //todo
                if (!param.keyboardShow && !param.focus) {
                    //Log.d("ta-ac-down","keyboardshow-no");
                    longPress = false;
                    view.setEnabled(false);
                    view.clearFocus();
                    lastTouchRawY = param.event.getRawY();
                    mHandler.postDelayed(mLongPressed, ViewConfiguration.getLongPressTimeout());
                } else {
                    //Log.d("ta-ac-down","keyboardshow-yes");
                }

            }

            Rect currRect = new Rect(); //自身位置
            Point point = new Point();
            view.getGlobalVisibleRect(currRect, point);

            int[] locationOnScreen = new int[2];
            view.getLocationOnScreen(locationOnScreen);    //TODO locationOnScreen[1]与currRect.top似乎总是相等的，合并？

            if (param.event.getAction() == MotionEvent.ACTION_MOVE && !param.keyboardShow) {
                //Log.d("moving", Float.toString(event.getRawY()));

                float currTouchRawY = param.event.getRawY();
                float moveOffset = lastTouchRawY - param.event.getRawY();   // 注意，此处已经过反转，moveOffset正值为上划
                int originTop = 0;
                if (manager instanceof MSITextAreaOriginPositionManager) {
                    originTop = ((MSITextAreaOriginPositionManager) manager).itemsOriginTop.get(param.viewId);
                }
                if (currRect.top - moveOffset > originTop) {
                    // 限制下划？
                    if (currRect.top > originTop) {
                        moveOffset = 0;
                    } else {
                        moveOffset = (float) (currRect.top - originTop);
                    }
                } else {
                }

                if (moveOffset < 0.0f) {
                    if (currRect.top <= originTop && currRect.top - moveOffset <= originTop) {
                        moveOffset += 1;
                        //todo
                        //this.hostPage.scrollYEx((int) moveOffset);  //滚动WebView
                    }

                    if (((float) currRect.top) - moveOffset <= ((float) originTop)) {
                        lastTouchRawY = currTouchRawY; //TODO 是否应按实际采纳的moveOffset来设置lastTouchRawY？
                    }
                } else if (moveOffset > 0.0f) {
                    //当前页面高度减去 当前TextArea可见区域底部相当于当前TextArea距离底部距离，这个距离要小于原始的距离。
                    int bottomOnScreen = locationOnScreen[1] + view.getHeight();
                    int bottomToWebViewTop = bottomOnScreen - webViewTop;
                    int bottomToWebViewPageBottom = mPageModule.getContentHeight()
                            - ((MSITextAreaOriginPositionManager) manager).itemsOriginBottom.get(param.viewId); //本View底边离WebView内容底边的距离，不随WebView滚动变化

                    if (hostPageRect.height() - bottomToWebViewTop + moveOffset >= bottomToWebViewPageBottom) {
                        moveOffset = bottomToWebViewPageBottom - (hostPageRect.height() - bottomToWebViewTop);
                    }
                    //todo
                    //this.hostPage.scrollYEx((int) moveOffset);
                    lastTouchRawY = currTouchRawY;
                }
            }

            float offset = motionDownY - param.event.getY();
            if (!longPress && param.event.getAction() == MotionEvent.ACTION_UP) {
                if (Math.abs(offset) < 1) {
                    this.mHandler.removeCallbacks(mLongPressed);
                    view.setEnabled(true);
                    view.requestFocus();
                } else {
                    return false;
                }
            }

            return safeCallSuperOnTouchEvent(view, param.event);
        }

        private void tryAdjustPositionOnSizeChanged(View view, MSITextAreaOriginPositionManager manager, final int changedHeight, String viewId,
                                                    int keyboardHeight) {
            int originTop = manager.itemsOriginTop.get(viewId);
            if (originTop <= view.getHeight()) {
                return;
            }

            if (changedHeight < 0) {
                Rect rect = new Rect();
                view.getGlobalVisibleRect(rect);
                if (rect.top + changedHeight < originTop) {
                    return;
                }
            }
            int currentWebViewHeight = mPageModule.getContentHeight();

            //键盘输入的时候，auto-height会让组件高度增加，这样就可能被键盘遮挡，为了解决这种问题 需要让页面发生滚动
            Rect rect = new Rect();
            view.getGlobalVisibleRect(rect);
            int confirmBarHeight = DisplayUtil.fromDPToPix(42);
            //键盘输入的时候，auto-height会让组件高度增加，这样就可能被键盘遮挡，为了解决这种问题 需要让页面发生滚动，当
            //网页高度没有发生变化并且组件被遮挡的时候不敢滚动页面而是调整页面的padding
            if (rect.bottom + keyboardHeight + confirmBarHeight > DisplayUtil.getScreenHeightReal()
                    && currentWebViewHeight == webViewHeight) {
                mPageModule.adjustPan(changedHeight);
                return;
            }

            if (currentWebViewHeight > webViewHeight) {
                mPageModule.scrollYEx(changedHeight);
                webViewHeight = currentWebViewHeight;
            }

        }
    };

    private boolean safeCallSuperOnTouchEvent(View view, MotionEvent event) {
        boolean touchEvent = false;
        try {
            touchEvent = view.onTouchEvent(event);
        } catch (Exception e) {
        }
        return touchEvent;
    }

}
