package com.meituan.msc.modules.api.msi.hook;

import android.view.View;

import com.meituan.android.common.weaver.interfaces.ffp.FFPTags;
import com.meituan.msc.modules.api.msi.IMSCApi;
import com.meituan.msc.modules.api.msi.MSCAPIHook;
import com.meituan.msc.modules.page.IPageModule;
import com.meituan.msi.api.ApiRequest;
import com.meituan.msi.metrics.base.FspRecordParam;

import java.util.HashMap;
import java.util.Map;

/**
 * 对秒开在WebView上的秒开结束事件上报桥做hook，在上报数据中添加MSC的自定义维度值
 * 该实现和MSC Native渲染的ffpTags实现不同。MSC Native渲染由秒开组件提供支持。但是MSC WebView场景，秒开组件拿不到MSC的页面对象，所以使用hook桥的实现。
 *
 * 为什么没有让fspRecord桥继承{@link IMSCApi}，并在秒开组件中实现呢？
 * 因为该方案有一个问题：该桥的实现在com.meituan.android.common.ffp:impl-msi组件中，而不是在com.meituan.android.common.ffp:impl-msc组件中，这么做会往优选独立App中引入MSC组件。
 */
public class FFPApiHook extends MSCAPIHook<Object> {
    @Override
    public String getHookAPIName() {
        return "fspRecord";
    }

    @Override
    public void beforeInvoke(ApiRequest<?> apiRequest) {
        fillFFPTags(apiRequest);
    }

    private void fillFFPTags(ApiRequest<?> apiRequest) {
        Object originalParams = apiRequest.bodyData().data;
        if (originalParams instanceof FspRecordParam) {
            Map<String, Object> ffpTags = getFFPTags(apiRequest);
            if (ffpTags != null) {
                FspRecordParam params = (FspRecordParam) originalParams;
                if (params.tags == null) {
                    params.tags = new HashMap<>();
                }
                if (params.tags instanceof Map) {
                    ((Map) params.tags).putAll(ffpTags);
                }
            }
        }
    }

    private Map<String, Object> getFFPTags(ApiRequest<?> apiRequest) {
        IPageModule pageModule = getPage(apiRequest);
        if (pageModule != null) {
            View pageView = pageModule.asView();
            if (pageView instanceof FFPTags) {
                return ((FFPTags) pageView).ffpTags();
            }
        }
        return null;
    }

    @Override
    public Object afterInvoke(ApiRequest<?> apiRequest, Object data) {
        return data;
    }
}
