package com.meituan.msc.modules.api.msi.hook;

import com.meituan.msc.common.config.MSCConfig;
import com.meituan.msc.common.utils.HeaderUtil;
import com.meituan.msc.common.utils.OkHttpUtil;
import com.meituan.msc.extern.MSCEnvHelper;
import com.meituan.msc.modules.api.msi.MSCAPIHook;
import com.meituan.msc.modules.devtools.IDevTools;
import com.meituan.msc.modules.update.MSCAppModule;
import com.meituan.msi.api.ApiRequest;
import com.meituan.msi.api.IMsiApiInit;
import com.meituan.network.request.RequestInitParam;
import com.sankuai.meituan.retrofit2.Interceptor;

import java.util.ArrayList;
import java.util.List;

/**
 * Created by letty on 2022/2/28.
 **/
public class RequestApiHook extends MSCAPIHook<Object> {

    @Override
    public String getHookAPIName() {
        return "request";
    }

    @Override
    public void beforeInvoke(ApiRequest apiRequest) {
        initRequest(apiRequest.getApiImpl());
    }

    @Override
    public Object afterInvoke(ApiRequest<?> apiRequest, Object requestApiResponse) {
        return requestApiResponse;
    }

    private void initRequest(Object requestObject) {
        // 网络库迁移到网络组后的API叫做【新API】。MSI原有保留的API叫【旧API】。
        // 上线后会使用Horn逐步放量，这里命中的可能是新API，也可能是旧API。
        // 所以这里做了新旧API初始化逻辑，后面会完全使用新API，到时删除这里的旧API初始化。
        IMsiApiInit<RequestInitParam> oldApi = null;
        com.sankuai.meituan.kernel.net.msi.RequestApi newApi = null;
        if (requestObject instanceof IMsiApiInit) {
            // 初始化旧网络API
            oldApi = (IMsiApiInit) requestObject;
            if (oldApi == null || oldApi.isInit()) {
                return;
            }
        } else if (requestObject instanceof com.sankuai.meituan.kernel.net.msi.RequestApi) {
            newApi = (com.sankuai.meituan.kernel.net.msi.RequestApi) requestObject;
            if (newApi == null || newApi.isInit()) {
                return;
            }
        } else {
            return;
        }

        boolean signatureVerificationEnable = false, enableSharkInContainer = false;
//        if (getRuntime().getMSCAppModule().isInnerApp()) 内部小程序可以使用验签能力
        signatureVerificationEnable = true;
        if (MSCConfig.isEnableShark()) {
            enableSharkInContainer = true;
        }
//        }
        IDevTools devTools = getRuntime().getModuleWithoutDelegate(IDevTools.class);
        List<Interceptor> interceptors = new ArrayList<>();
        if (devTools != null) {
            Interceptor devInterceptor = devTools.getDevInterceptor(MSCEnvHelper.getContext());
            if (devInterceptor != null) {
                interceptors.add(devInterceptor);
            }
        }

        if (oldApi != null) {
            RequestInitParam param = new RequestInitParam();
            param.userAgent = getUserAgent();
            param.referer = getRequestRefer(getRuntime().getMSCAppModule());
            param.signatureVerificationEnable = signatureVerificationEnable;
            param.enableSharkInContainer = enableSharkInContainer;
            param.interceptors = interceptors;
            oldApi.init(param);
        } else if (newApi != null) {
            newApi.init(getUserAgent(), getRequestRefer(getRuntime().getMSCAppModule()), signatureVerificationEnable, enableSharkInContainer, interceptors);
        }
    }


    public static String getRequestRefer(MSCAppModule mscAppModule) {
        if (mscAppModule.hasMetaInfo()) {
            return String.format(OkHttpUtil.REFERER, mscAppModule.getAppId(), mscAppModule.getPublishId());
        } else {
            return OkHttpUtil.REFERER;
        }
    }

    public static String getUserAgent() {
        return HeaderUtil.stripHeaderValue(RequestUserAgent.getInstance().getCustomUserAgent());
    }
}
