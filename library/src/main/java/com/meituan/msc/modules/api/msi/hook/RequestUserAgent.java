package com.meituan.msc.modules.api.msi.hook;

import android.text.TextUtils;
import android.util.Pair;

import com.meituan.msc.extern.IEnvInfo;
import com.meituan.msc.extern.MSCEnvHelper;
import com.meituan.msc.lib.BuildConfig;
import com.meituan.msc.modules.page.render.RendererType;
import com.meituan.msc.modules.update.MSCAppModule;

import java.util.Map;
import java.util.concurrent.ConcurrentHashMap;

/**
 * https://km.sankuai.com/collabpage/1705549638
 */
public class RequestUserAgent {
    private Map<RendererType, String> rendererUAs;
    private static final String systemUa = java.lang.System.getProperty("http.agent");

    private static volatile RequestUserAgent sInstance;
    private RequestUserAgent() {
        rendererUAs = new ConcurrentHashMap<>();
    }

    public static RequestUserAgent getInstance() {
        if (sInstance == null) {
            synchronized (RequestUserAgent.class) {
                if (sInstance == null) {
                    sInstance = new RequestUserAgent();
                }
            }
        }
        return sInstance;
    }

    public void recordRendererUAWhenInit(RendererType rendererType, String rendererUA) {
        rendererUAs.put(rendererType, rendererUA);
    }

    /**
     * @return usergent 格式为：[WebView UA(可选)][Native UA(可选)][系统UA][小程序标识][MSC容器标识][宿主标识]
     */
    public String getCustomUserAgent() {
        StringBuilder customUserAgent = new StringBuilder();
        for (RendererType type : RendererType.values()) {
            if (rendererUAs.containsKey(type) && !TextUtils.isEmpty(rendererUAs.get(type))) {
                customUserAgent.append(rendererUAs.get(type));
                customUserAgent.append(" ");
            }
        }
        customUserAgent.append(systemUa)
                .append(" ")
                .append(MSCAppModule.WEIXING_UA)
                .append(" MMP/")
                .append(BuildConfig.MSC_SDK_VERSION).append(".").append(BuildConfig.AAR_VERSION)
                .append(" MSC/")
                .append(BuildConfig.AAR_VERSION);
        IEnvInfo envInfo = MSCEnvHelper.getEnvInfo();
        customUserAgent.append(' ').append(envInfo.getAppCode()).append('/').append(envInfo.getAppVersionName());
        Pair<String, String> customUA = MSCEnvHelper.getCustomUA();
        if (customUA != null) {
            customUserAgent.append(" ").append(customUA.first).append("/").append(customUA.second);
        }
        return customUserAgent.toString();
    }
}
