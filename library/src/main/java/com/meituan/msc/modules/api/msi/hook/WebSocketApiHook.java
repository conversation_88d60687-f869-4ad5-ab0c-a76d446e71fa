package com.meituan.msc.modules.api.msi.hook;

import com.meituan.msc.modules.api.msi.MSCAPIHook;
import com.meituan.msi.api.ApiRequest;
import com.meituan.msi.api.IMsiApiInit;
import com.meituan.network.websocket.SocketInitParam;

/**
 * Created by letty on 2022/3/1.
 **/
public class WebSocketApiHook extends MSCAPIHook<Object> {
    @Override
    public String getHookAPIName() {
        return "connectSocket";
    }
    @Override
    public Object afterInvoke(ApiRequest apiRequest, Object data) {
        return data;
    }

    @Override
    public void beforeInvoke(ApiRequest apiRequest) {
        // 参考RequestApiHook里面对应的注释
        IMsiApiInit<SocketInitParam> oldApi = null;
        com.sankuai.meituan.kernel.net.msi.WebSocketApi newApi = null;
        Object webSocketApiObject = apiRequest.getApiImpl();
        if (webSocketApiObject instanceof IMsiApiInit) {
            oldApi = (IMsiApiInit) webSocketApiObject;
            if (oldApi == null || oldApi.isInit()) {
                return;
            }
        } else if (webSocketApiObject instanceof com.sankuai.meituan.kernel.net.msi.WebSocketApi) {
            newApi = (com.sankuai.meituan.kernel.net.msi.WebSocketApi) webSocketApiObject;
            if (newApi == null || newApi.isInit()) {
                return;
            }
        } else {
            return;
        }

        if (oldApi != null) {
            SocketInitParam param = new SocketInitParam();
            param.userAgent = RequestApiHook.getUserAgent();
            param.referer = RequestApiHook.getRequestRefer(getRuntime().getMSCAppModule());
            oldApi.init(param);
        } else if (newApi != null) {
            newApi.init(RequestApiHook.getUserAgent(), RequestApiHook.getRequestRefer(getRuntime().getMSCAppModule()));
        }
    }
}
