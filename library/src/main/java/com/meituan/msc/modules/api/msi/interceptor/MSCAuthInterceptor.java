//package com.meituan.msc.modules.api.msi.interceptor;
//
//import android.support.annotation.NonNull;
//import android.text.TextUtils;
//
//import com.google.gson.JsonObject;
//import com.meituan.msc.modules.api.auth.AuthManager;
//import com.meituan.msc.modules.engine.MSCRuntime;
//import com.meituan.msc.modules.reporter.MSCLog;
//import com.meituan.msi.api.ApiRequest;
//import com.meituan.msi.api.ApiResponse;
//import com.meituan.msi.bean.ApiException;
//import com.meituan.msi.interceptor.ApiInterceptor;
//import com.meituan.msi.interceptor.InterceptorPriority;
//
//public class MSCAuthInterceptor implements ApiInterceptor {
//    private static final String TAG = "MSCAuthInterceptor";
//    private Chain pendingChain = null;
//
//    private MSCRuntime runtime;
//
//    public MSCAuthInterceptor(@NonNull MSCRuntime runtime) {
//        this.runtime = runtime;
//    }
//
//    @Override
//    public ApiResponse<?> intercept(Chain chain) throws ApiException {
//        ApiRequest apiRequest = chain.request();
//
//
//        //检查api scope.userLocation权限、scope.userLocationBackground权限在小程序中字段的配置
//        String scope = AuthManager.checkApiIfNeedAuth(apiRequest.getName());
//        //内部小程序，apiName没有对应scope时，继续执行
//        if ((runtime.getMSCAppModule() != null && runtime.getMSCAppModule().isInnerApp())
//                || TextUtils.isEmpty(scope)) {
//            return chain.proceed(apiRequest);
//        }
//
//        //需要验证scope
//        MSCLog.d("MSCMsi", "MSCAuthInterceptor need auth check");
//        if (!AuthManager.checkConfiguration(runtime.getMSCAppModule(), scope)) {
//            ApiResponse<?> error = authFail(apiRequest, "api call failed, auth denied, need to configure the necessary fields in app.json!");
//            return error;
//        }
//
//        if (apiRequest.callback() == null) {
//            ApiResponse<?> error = authFail(apiRequest, "api call failed, need auth check, you need use api by asynInvoke");
//            return error;
//        }
//
//        //checkIfNeedAuth 授权后继续执行
//        pendingChain = chain;
//
//        JsonObject innerArgs = apiRequest.getInnerArgs();
//        String from = null;
//        if (innerArgs != null){
//            if (innerArgs.has("from")){
//                from = innerArgs.get("from").getAsString();
//            }
//        }
//        checkIfNeedAuth(apiRequest, scope, from);
//
//        return null;
//    }
//
//    /**
//     * 检查小程序的授权
//     */
//    private String checkIfNeedAuth(final ApiRequest apiRequest, String scope, String from) {
//        if (runtime == null || runtime.getContainerManagerModule().getTopContainer() == null) {    // 防止onDestroy之后调用
//            ApiResponse<?> error = ApiResponse.negativeResponse(apiRequest, ApiResponse.NO_PERMISSION, "api call failed, auth denied, need to configure the necessary fields in app.json!", ApiResponse.InvokeType.callbackValue);
//            ApiResponse.notifyNegativeResult(apiRequest.callback(), error);
//            return null;
//        }
//
//        AuthManager.auth(runtime.getContainerManagerModule().getTopContainer().getActivity() , runtime.getMSCAppModule(), scope, from, new AuthManager.OnCheckAuthListener() {
//            @Override
//            public void onResult(int result) {
//                switch (result) {
//                    case AuthManager.OnCheckAuthListener.RESULT_ACCEPT:
//                        if (pendingChain != null) {
//                            try {
//                                MSCLog.d("MSCMsi", "MSCAuthInterceptor go on after auth");
//                                pendingChain.proceed(apiRequest);
//                            } catch (ApiException e) {
//                                e.printStackTrace();
//                                ApiResponse.notifyNegativeResult(apiRequest.callback(),
//                                        ApiResponse.negativeResponse(apiRequest, e, ApiResponse.InvokeType.callbackValue));
//                            }
//                        }
//                        break;
//                    case AuthManager.OnCheckAuthListener.RESULT_REFUSE:
//                        authFail(apiRequest, "api call failed, auth denied");
//                        break;
//                    case AuthManager.OnCheckAuthListener.RESULT_CANCEL:
//                        authFail(apiRequest, "api call failed, auth cancel");
//                        break;
//                }
//            }
//        });
//        return null;
//    }
//
//    private ApiResponse authFail(ApiRequest apiRequest, String errMsg) {
//        ApiResponse<?> error = ApiResponse.negativeResponse(apiRequest, ApiResponse.NO_PERMISSION, errMsg, ApiResponse.InvokeType.callbackValue);
//        ApiResponse.notifyNegativeResult(apiRequest.callback(), error);
//        return error;
//    }
//
//    @Override
//    public int priority() {
//        return InterceptorPriority.BEFORE_INNER_INTERCEPTOR;
//
//    }
//}
