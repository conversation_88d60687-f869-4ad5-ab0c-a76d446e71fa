package com.meituan.msc.modules.api.msi.interceptor;

import com.meituan.msc.modules.api.msi.IMSCApi;
import com.meituan.msc.modules.api.msi.MSCApi;
import com.meituan.msc.modules.engine.MSCRuntime;
import com.meituan.msi.api.ApiRequest;
import com.meituan.msi.api.ApiResponse;
import com.meituan.msi.bean.ApiException;
import com.meituan.msi.interceptor.ApiInterceptor;
import com.meituan.msi.interceptor.InterceptorPriority;

/**
 * MSC 容器特有参数注入，搭配 IMSCApi 使用
 * Created by letty on 2022/1/14.
 **/
public class MSCParamInterceptor implements ApiInterceptor {

    private MSCRuntime mRuntime;

    public MSCParamInterceptor(MSCRuntime runtime) {
        this.mRuntime = runtime;
    }

    @Override
    public ApiResponse<?> intercept(ApiInterceptor.Chain chain) throws ApiException {
        ApiRequest apiRequest = chain.request();
        if (apiRequest.getApiImpl() instanceof IMSCApi) {
            ((IMSCApi) apiRequest.getApiImpl()).setRuntime(mRuntime);
        }
        return chain.proceed(apiRequest);
    }

    @Override
    public int priority() {
        return InterceptorPriority.AFTER_INNER_INTERCEPTOR;
    }
}
