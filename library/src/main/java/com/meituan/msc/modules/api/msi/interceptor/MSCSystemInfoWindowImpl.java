package com.meituan.msc.modules.api.msi.interceptor;

import android.app.Activity;
import android.graphics.Rect;
import android.support.annotation.NonNull;
import android.support.annotation.Nullable;
import android.util.DisplayMetrics;
import android.view.View;

import com.meituan.msc.common.utils.DisplayUtil;
import com.meituan.msc.common.utils.ScreenUtil;
import com.meituan.msc.extern.MSCEnvHelper;
import com.meituan.msc.modules.container.IContainerDelegate;
import com.meituan.msc.modules.container.IMSCContainer;
import com.meituan.msc.modules.engine.MSCHornRollbackConfig;
import com.meituan.msc.modules.engine.MSCRuntime;
import com.meituan.msc.modules.page.IPageManagerModule;
import com.meituan.msc.modules.page.IPageModule;
import com.meituan.msc.modules.page.view.CustomNavigationBar;
import com.meituan.msc.modules.reporter.MSCLog;
import com.meituan.msc.modules.update.MSCAppModule;
import com.meituan.msi.api.systeminfo.SystemInfoApi;
import com.meituan.msi.util.MsiBugFixConfig;

public class MSCSystemInfoWindowImpl implements SystemInfoApi.ISystemInfoWindow {
    private static final String TAG = "MSCSystemInfoWindowImpl";
    private DisplayMetrics displayMetrics;
    private final MSCRuntime mRuntime;
    private static int sScreenWidth;
    private static int sScreenHeight;

    public MSCSystemInfoWindowImpl(@NonNull MSCRuntime runtime) {
        this.mRuntime = runtime;
    }

    @Override
    public int getWindowWidth() {
        IContainerDelegate controller = mRuntime.getContainerManagerModule().getTopContainer();
        return getContainerWidth(controller);
    }


    @Override
    public int getWindowHeight() {
        IContainerDelegate controller = mRuntime.getContainerManagerModule().getTopContainer();
        return getContainerHeight(controller);
    }

    @Override
    public int getWindowWidthById(int i) {
        return getContainerWidth(mRuntime.getContainerManagerModule().getContainerDelegateByPageIdOrTopPage(i));
    }

    @Override
    public int getWindowHeightById(int i) {
        return getContainerHeight(mRuntime.getContainerManagerModule().getContainerDelegateByPageIdOrTopPage(i));
    }

    private int getContainerWidth(IContainerDelegate controller) {
        int width = ScreenUtil.getScreenWidth(null, null);
        if (controller == null) {
            MSCLog.d("SystemInfoModule", "[MSI] use default screen width as window width size when activity not attached ", width);
            return width;
        }

        int screenWidth;
        View view;
        if (controller.getMSCContainer() != null && controller.isWidget() && ((view = controller.getMSCContainer().getRootView()) != null)) {
            screenWidth = view.getWidth();
        } else {
            Activity activity = controller.getActivity();
            String appId = null;
            IMSCContainer mscContainer = controller.getMSCContainer();
            if (mscContainer != null) {
                appId = mscContainer.getMPAppId();
            }
            screenWidth = ScreenUtil.getScreenWidth(activity, appId);
        }
        if (screenWidth != 0) {
            width = screenWidth;
        }
        if (width == 0) {
            MSCLog.e(TAG, "getContainerWidth screenWidth is 0");
        }
        return width;
    }

    /**
     * https://km.sankuai.com/collabpage/1758879542
     * TODO: 2023/7/18 逻辑暂时对齐MMP，后续重新制定方案对齐微信小程序
     */
    private int getContainerHeight(@Nullable IContainerDelegate controller) {
        int height = ScreenUtil.getScreenHeight(null, null);
        if (controller == null || controller.getMSCContainer() == null) {
            MSCLog.e("SystemInfoModule", "[MSI] use default screen height as window height size when activity not attached " + height);
            return height;
        }

        // widget
        if (controller.isWidget()) {
            View view = controller.getMSCContainer().getRootView();
            return view != null ? view.getHeight() : height;
        }

        //activity
        // have topPage
        IPageManagerModule pageManager = controller.getPageMangerModule();
        IPageModule topPage = pageManager != null ? pageManager.getTopPage() : null;
        int[] size = topPage != null ? topPage.getWindowSize() : null;
        if (size != null) {
            height = size[1];
            if (!topPage.isTabPage()) {
                // 这里的逻辑是错误的，为了暂时对齐MMP，正确的做法：在客户端定义导航栏的情况下减去状态栏和导航栏高度，业务自定义情况下不减
                MSCAppModule mscAppModule = mRuntime.getMSCAppModule();
                if (mscAppModule != null && mscAppModule.isCustomNavigationStyle(topPage.getPagePath())) {
                    height -= DisplayUtil.getStatusBarHeight();
                    height -= CustomNavigationBar.getFixedHeight();
                }
            }
            return height;
        }
        // have not topPage
        Activity activity = controller.getActivity();
        if (activity != null) {
            IMSCContainer imscContainer = controller.getMSCContainer();
            String appId = null;
            if (imscContainer != null) {
                appId = imscContainer.getMPAppId();
            }
            height = ScreenUtil.getScreenHeight(activity, appId);
        }

        return height;
    }
}
