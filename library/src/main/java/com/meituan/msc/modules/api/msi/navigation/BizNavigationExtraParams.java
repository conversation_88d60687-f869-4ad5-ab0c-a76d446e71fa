package com.meituan.msc.modules.api.msi.navigation;

public class BizNavigationExtraParams {
    public boolean disablePrefetch;

    public BizNavigationExtraParams(Builder builder) {
        this.disablePrefetch = builder.disablePrefetch;
    }

    // builder
    public static class Builder {
        private boolean disablePrefetch;
        public Builder setDisablePrefetch(boolean disablePrefetch) {
            this.disablePrefetch = disablePrefetch;
            return this;
        }
        public BizNavigationExtraParams build() {
            return new BizNavigationExtraParams(this);
        }
    }
}
