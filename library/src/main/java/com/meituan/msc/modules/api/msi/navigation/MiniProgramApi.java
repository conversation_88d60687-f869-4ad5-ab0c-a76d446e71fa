package com.meituan.msc.modules.api.msi.navigation;

import android.app.Activity;
import android.content.Intent;
import android.content.pm.PackageManager;
import android.content.pm.ResolveInfo;
import android.net.Uri;
import android.text.TextUtils;

import com.google.gson.JsonElement;
import com.meituan.msc.common.utils.Constants;
import com.meituan.msc.common.utils.DependencyUtils;
import com.meituan.msc.common.utils.IntentUtil;
import com.meituan.msc.common.utils.JsonUtil;
import com.meituan.msc.extern.MSCEnvHelper;
import com.meituan.msc.lib.interfaces.container.MSCParams;
import com.meituan.msc.modules.api.ApiException;
import com.meituan.msc.modules.api.msi.MSCApi;
import com.meituan.msc.modules.api.msi.MSCErrorCode;
import com.meituan.msc.modules.container.ContainerController;
import com.meituan.msc.modules.container.IContainerDelegate;
import com.meituan.msc.modules.container.IStartActivityModule;
import com.meituan.msc.modules.container.MSCActivity;
import com.meituan.msc.modules.engine.MSCHornRollbackConfig;
import com.meituan.msc.modules.engine.MSCRuntime;
import com.meituan.msc.modules.engine.RuntimeManager;
import com.meituan.msc.modules.page.IPageManagerModule;
import com.meituan.msc.modules.page.IPageModule;
import com.meituan.msc.modules.page.PageManager;
import com.meituan.msc.modules.reporter.MSCLog;
import com.meituan.msc.modules.reporter.MSCReporter;
import com.meituan.msc.modules.reporter.ReporterFields;
import com.meituan.msc.modules.router.MMPRouterManager;
import com.meituan.msc.modules.router.MSCInstrumentation;
import com.meituan.msi.annotations.MsiApiEnv;
import com.meituan.msi.annotations.MsiApiMethod;
import com.meituan.msi.annotations.MsiParamChecker;
import com.meituan.msi.annotations.MsiSupport;
import com.meituan.msi.api.IMsiApi;
import com.meituan.msi.api.MSIError;
import com.meituan.msi.bean.ContainerInfo;
import com.meituan.msi.bean.MsiContext;
import com.sankuai.meituan.serviceloader.annotation.ServiceLoaderInterface;
import com.tencent.mm.opensdk.modelbiz.WXLaunchMiniProgram;
import com.tencent.mm.opensdk.openapi.IWXAPI;
import com.tencent.mm.opensdk.openapi.WXAPIFactory;

import okhttp3.HttpUrl;

/**
 * Created by letty on 2022/1/26.
 **/
@ServiceLoaderInterface(key = "msc_miniprogram", interfaceClass = IMsiApi.class)
@MsiApiEnv(name = ContainerInfo.ENV_MSC)
public class MiniProgramApi extends MSCApi {
    private static final String TAG = "MiniProgramApi";
    //启动小程序失败
    static final int ERROR_CODE_NAVIGATE_TO_MINI_PROGRAM_LAUNCH_ERROR = 800000203;

    // 跳转MMP的兜底目标页
    private static final String MMP_ROUTER_CENTER_ACTIVITY = "com.meituan.mmp.lib.RouterCenterActivity";

    @MsiApiMethod(name = "exitMiniProgram", onUiThread = true, isForeground = true)
    public void exitMiniProgram(MsiContext context) {
        exitCurrentApp(context);
        context.onSuccess(null);
    }

    private void exitCurrentApp(MsiContext context) {
        IContainerDelegate controller = getContainerDelegate(context);
        if (controller == null) {
            return;
        }
        MSCLog.d(TAG, "MiniProgramApi exitCurrentApp");
        PageManager pageManager = controller.getPageManager();
        try {
            if (pageManager == null || pageManager.getTopPage() == null) {
                MSCLog.d(TAG, "MiniProgramApi exitCurrentApp", pageManager);
            } else {
                String url = pageManager.getTopPage().getPagePath();
                Intent intent = controller.getIntent();
                intent.putExtra(MSCActivity.FINISH_BY_EXIT_MINIPROGRAM, true);
                if (pageManager.reLaunchActionInFusion(url, intent)) {
                    return;
                }
            }
        } catch (ApiException e) {
            MSCLog.e(TAG, e, "exitCurrentApp");
        }
        controller.handleCloseContainer("exitMiniProgram");
    }

    @MsiSupport
    public static class NavigateMiniProgramParams {
        @MsiParamChecker(required = true)
        public String appId;

        String path;
        String platform;
        String envVersion = ENV_VERSION_RELEASE; // 目前仅用于跳转微信
        JsonElement extraData;
        String checkUpdateUrl;
        boolean disablePrefetch;
        Boolean reload;

        final static String TARGET_MP_PLATFORM_WX = "wx";
        final static String TARGET_MP_PLATFORM_MT = "mt";

        final static String ENV_VERSION_RELEASE = "release";
        final static String ENV_VERSION_DEVELOP = "develop";
        final static String ENV_VERSION_TRIAL = "trial";

    }

    /**
     * 跳转到小程序
     * 添加 <a href="https://km.sankuai.com/collabpage/2704593151">MSC navigateToMiniProgram API 调用埋点</a>
     */
    @MsiApiMethod(name = "navigateToMiniProgram", request = NavigateMiniProgramParams.class, onUiThread = true)
    public void navigateToMiniProgram(NavigateMiniProgramParams params, MsiContext context) {
        if (NavigateMiniProgramParams.TARGET_MP_PLATFORM_WX.equals(params.platform)) {
            navigateToWXMiniProgram(params, context);
            reportApiMethodCalled(params, context, "msc_wx");
            return;
        }
        // 如果命中存量业务的MMP的appId白名单，则路由到MMP容器页。
        if (MSCHornRollbackConfig.canNavigateToMMPFromMSC(params.appId) || MMPRouterManager.isInMSCNavigateToMMPWhiteList(params.appId)) {
            // 如果路由消费失败（return false：目前是intent跳转异常，例如目标页不存在等，说明MMP模块不存在，可能是已下线或未接入，则兜底路由到跳转MSC）
            if (navigateToMMPMiniProgram(params, context)) {
                return;
            }
        } else {
            reportApiMethodCalled(params, context, "msc_msc");
        }
        // 注意不可在new Intent()中直接传入class，也不可以在注释中提到那样的代码 -> hpx：检测到有显式Intent跳转，请及时修改！
        // 检测的原因为Multi-Dex防止主包过大
        Intent intent = new Intent();
        // FIXME: 2022/9/2 当前仅支持融合模式，之后多任务栈/多进程需使用其他的方式进行判定启动目标页面； MSCActivity.class.getName()
        intent.setClassName(context.getActivity(), MSCActivity.class.getName());
        intent.putExtra(MSCParams.DISABLE_PREFETCH, params.disablePrefetch);
        intent.putExtra(MSCParams.APP_ID, params.appId);
        intent.putExtra(MSCParams.SRC_APP_ID, getRuntime().getAppId());
        intent.putExtra(ContainerController.START_FROM_MIN_PROGRAM, true);

        if (MSCHornRollbackConfig.enableExternalAppPageDepthLimit()) {
            MSCRuntime existRuntime = RuntimeManager.getRuntimeWithAppId(params.appId);
            if (existRuntime != null) {
                boolean isExternalApp = existRuntime.getMSCAppModule().getExternalApp();
                if (isExternalApp) {
                    intent.putExtra(MSCParams.MSC_EXTERNAL_APP_NAVIGATE_TO_EXIST_MINI_PROGRAM_ANIMATION, true);
                    intent.putExtra(MSCActivity.RELAUNCH, true);
                }
            }
        }

        String path = params.path;
        if (!TextUtils.isEmpty(path)) {
            path = path.startsWith("/") ? path : "/" + path;
            intent.putExtra(MSCParams.TARGET_PATH, path);
        }

        // FIXME: 2022/9/2 msc extraData 流程复杂简化之
        if (params.extraData != null && !params.extraData.isJsonNull()) {
            intent.putExtra(MSCParams.EXTRA_DATA,
                    JsonUtil.toJsonString(MSCParams.EXTRA_DATA,
                            params.extraData.toString()));
        }

        if (!TextUtils.isEmpty(params.checkUpdateUrl)) {//for debug
            if (HttpUrl.parse(params.checkUpdateUrl) != null) {
                intent.putExtra(MSCParams.CHECK_UPDATE_URL, params.checkUpdateUrl);
            } else {
                context.onError("invalid checkUpdateUrl.", MSIError.getGeneralError(MSCErrorCode.ERROR_CODE_API_COMMON_INVALID_PARAM));
                return;
            }
        }

        if (params.reload != null) {
            intent.putExtra(MSCParams.RELOAD, params.reload);
        }

        try {
            IStartActivityModule startActivityModule = getRuntime().getModule(IStartActivityModule.class);
            if (startActivityModule == null) {
                context.onError("startActivityForResult,msc app exit", MSIError.getIgnoreError(MSCErrorCode.ERROR_CODE_API_COMMON_MEET_EXPECTATIONS));
                MSCLog.i(TAG, "startActivityForResult,msc app exit");
                return;
            }

            startActivityModule.startActivityForResult(intent, Constants.REQUEST_CODE_MINI_PROGRAM, getNavActivityInfo(context), null);
            context.onSuccess(null);
        } catch (Exception e) {
            context.onError("start activity error. " + e.getMessage(), MSIError.getGeneralError(ERROR_CODE_NAVIGATE_TO_MINI_PROGRAM_LAUNCH_ERROR));
        }
    }


    /**
     * 跳转到MMP小程序
     */
    private boolean navigateToMMPMiniProgram(NavigateMiniProgramParams params, MsiContext context) {
        String path = params.path;
        Intent intent = new Intent();
        // 设置了scheme 优先按照scheme启动，此时可根据
        Uri mmpUri = MSCInstrumentation.getMmpUri();
        String scheme = mmpUri != null ? mmpUri.toString() : "";
        boolean fixNavigateToMiniProgramWhenMMPOffline = MSCHornRollbackConfig.fixNavigateToMiniProgramWhenMMPOffline();
        if (!fixNavigateToMiniProgramWhenMMPOffline || !MSCEnvHelper.isMMPOffline()) {
            // 存在MMP页面，才有拉起MMP可能性
            if (TextUtils.isEmpty(scheme)) {
                intent.setClassName(context.getActivity(), MMP_ROUTER_CENTER_ACTIVITY);
                intent.putExtra(MSCParams.APP_ID, params.appId);
                reportApiMethodCalled(params, context, "msc_mmp_none_scheme");
            } else {
                intent.setData(Uri.parse(scheme).buildUpon().appendQueryParameter(MSCParams.APP_ID, params.appId).build());
                reportApiMethodCalled(params, context, "msc_mmp_scheme");
            }
        } else {
            // 不存在MMP页面，说明是MMP组件未接入或已下线，只能跳转MSC页面。
            if (TextUtils.isEmpty(scheme)) {
                // 不存在MMP Scheme，无法直接通过路由映射拦截协议，所以需要提前转换为MSC AppId。
                String mscAppId = MMPRouterManager.getMSCAppIdRouteByMMP(params.appId);
                if (!TextUtils.isEmpty(mscAppId)) {
                    params.appId = mscAppId;
                    reportApiMethodCalled(params, context, "msc_mmp_offline_route_msc");
                } else {
                    reportApiMethodCalled(params, context, "msc_mmp_offline_direct_msc");
                }
                // 此处return会继续拉起MSC页面。
                return false;
            } else {
                // 存在MMP Scheme，可直接通过路由映射拦截协议，需要直接按Scheme协议拉起即可。
                intent.setData(Uri.parse(scheme).buildUpon().appendQueryParameter(MSCParams.APP_ID, params.appId).build());
                reportApiMethodCalled(params, context, "msc_mmp_offline_scheme");
            }
        }

        intent.putExtra(MSCParams.SRC_APP_ID, getAppId());
        intent.putExtra(ContainerController.START_FROM_MIN_PROGRAM, true);

        if (!TextUtils.isEmpty(path)) {
            path = path.startsWith("/") ? path : "/" + path;
            intent.putExtra(MSCParams.TARGET_PATH, path);
        }

        if (params.extraData != null && !params.extraData.isJsonNull()) {
            intent.putExtra(MSCParams.EXTRA_DATA,
                    JsonUtil.toJsonString(MSCParams.EXTRA_DATA,
                            params.extraData.toString()));
        }

        if (!TextUtils.isEmpty(params.checkUpdateUrl)) { //for debug
            if (HttpUrl.parse(params.checkUpdateUrl) != null) {
                intent.putExtra(MSCParams.CHECK_UPDATE_URL, params.checkUpdateUrl);
            } else {
                context.onError("invalid checkUpdateUrl.", MSIError.getGeneralError(MSCErrorCode.ERROR_CODE_API_COMMON_INVALID_PARAM));
                return true;
            }
        }

        if (params.reload != null) {
            intent.putExtra(MSCParams.RELOAD, params.reload);
        }

        try {
            IStartActivityModule startActivityModule = getRuntime().getModule(IStartActivityModule.class);
            if (startActivityModule == null) {
                MSCLog.i(TAG, "startActivityForResult,msc app exit");
                context.onError("startActivityForResult,msc app exit", MSIError.getIgnoreError(MSCErrorCode.ERROR_CODE_API_COMMON_MEET_EXPECTATIONS));
                return true;
            }
            // 先判断Intent是否有页面消费
            ResolveInfo activityInfo = context.getActivity().getPackageManager().resolveActivity(intent, PackageManager.MATCH_DEFAULT_ONLY);
            if (activityInfo == null || activityInfo.activityInfo == null) {
                activityInfo = context.getActivity().getPackageManager().resolveActivity(intent, 0);
            }
            // 有MMP页面消费则进行跳转
            if (activityInfo != null && activityInfo.activityInfo != null) {
                startActivityModule.startActivityForResult(intent, Constants.REQUEST_CODE_MINI_PROGRAM, getNavActivityInfo(context), null);
            } else {
                // 如果没有消费MMP Intent的页面，说明没有引入MMP或者已下线MMP，则兜底跳转MSC。
                return false;
            }
        } catch (Exception e) {
            return false;
        }
        context.onSuccess(null);
        return true;
    }

    private void reportApiMethodCalled(NavigateMiniProgramParams params, MsiContext context, String navigateType) {
        IContainerDelegate containerDelegate = getContainerDelegate(context);
        if (containerDelegate == null) {
            MSCLog.i(TAG, "reportNavigateToMiniProgramApiCalled containerDelegate is null");
            return;
        }
        ContainerController controller = (ContainerController) containerDelegate;
        IPageManagerModule pageManager = controller.getPageMangerModule();
        IPageModule topPage = pageManager != null ? pageManager.getTopPage() : null;
        MSCReporter reporter;
        if (topPage == null || (reporter = topPage.getReporter()) == null) {
            reporter = controller.getContainerReporter();
        }
        if (reporter == null) {
            MSCLog.i(TAG, "reportNavigateToMiniProgramApiCalled reporter is null");
            return;
        }
        reporter.record(ReporterFields.REPORT_API_METHOD_COUNT)
                .tag("apiName", "navigateToMiniProgram")
                .tag("targetAppId", params.appId)
                .tag("srcAppId", getRuntime().getAppId())
                .tag("navigateType", navigateType)
                .tag("path", params.path)
                .tag("platform", params.platform)
                .tag("envVersion", params.envVersion)
                .sendRealTime();
    }

    /**
     * 跳转到微信小程序 https://km.sankuai.com/page/788368654
     */
    private void navigateToWXMiniProgram(NavigateMiniProgramParams params, MsiContext context) {
        if (!DependencyUtils.isAvailable(() -> WXAPIFactory.class)) {
            context.onError("wx opensdk not available", MSIError.getGeneralError(MSCErrorCode.ERROR_CODE_API_COMMON_MODULE_NOT_EXIST));
            return;
        }
        if (context.getActivity() != null && !TextUtils.isEmpty(MSCEnvHelper.getEnvInfo().getWXAppId())) {
            IWXAPI api = WXAPIFactory.createWXAPI(context.getActivity(), MSCEnvHelper.getEnvInfo().getWXAppId());
            if (!api.isWXAppInstalled()) {
                MSCLog.d(TAG, "wx is not installed");
                context.onError("wx is not installed", MSIError.getGeneralError(MSCErrorCode.ERROR_CODE_API_COMMON_MODULE_NOT_EXIST));
                return;
            }
            WXLaunchMiniProgram.Req req = new WXLaunchMiniProgram.Req();

            req.userName = params.appId; // 填小程序原始id
            req.path = params.path; // 小程序页面path
            if (TextUtils.isEmpty(req.userName)) {
                context.onError("invalid params, appId is required", MSIError.getGeneralError(MSCErrorCode.ERROR_CODE_API_COMMON_INVALID_APP_ID));
                return;
            }
            switch (params.envVersion) {
                case NavigateMiniProgramParams.ENV_VERSION_RELEASE:
                    req.miniprogramType = WXLaunchMiniProgram.Req.MINIPTOGRAM_TYPE_RELEASE;// 可选打开 开发版，体验版和正式版
                    break;
                case NavigateMiniProgramParams.ENV_VERSION_DEVELOP:
                    req.miniprogramType = WXLaunchMiniProgram.Req.MINIPROGRAM_TYPE_TEST;
                    break;
                case NavigateMiniProgramParams.ENV_VERSION_TRIAL:
                    req.miniprogramType = WXLaunchMiniProgram.Req.MINIPROGRAM_TYPE_PREVIEW;
                    break;
                default:
                    req.miniprogramType = WXLaunchMiniProgram.Req.MINIPTOGRAM_TYPE_RELEASE;
            }
            boolean launchResult = api.sendReq(req);
            if (launchResult) {
                context.onSuccess(null);
            } else {
                context.onError("failed to launch wx miniprogram", MSIError.getGeneralError(ERROR_CODE_NAVIGATE_TO_MINI_PROGRAM_LAUNCH_ERROR));
            }
        } else {
            context.onError("Current Activity is null or WXAppID is empty", MSIError.getIgnoreError(MSCErrorCode.ERROR_CODE_API_COMMON_MEET_EXPECTATIONS));
        }
    }


    @MsiSupport
    public static class NavigateBackParams {
        JsonElement extraData;
    }

    /**
     * 返回小程序
     */
    @MsiApiMethod(name = "navigateBackMiniProgram", request = NavigateBackParams.class, onUiThread = true)
    public void navigateBackMiniProgram(NavigateBackParams param, MsiContext context) {
        MSCLog.d(TAG, "MiniProgramApi navigateBackMiniProgram");

        IContainerDelegate containerDelegate = getContainerDelegate(context);
        if (containerDelegate == null) {
            context.onError("not containerDelegate alive", MSIError.getGeneralError(MSCErrorCode.ERROR_CODE_API_COMMON_MEET_EXPECTATIONS));
            return;
        }
        if (param.extraData != null && !param.extraData.isJsonNull()) {
            Intent intent = new Intent();
            intent.putExtra(MSCParams.EXTRA_DATA, param.extraData.toString());
            intent.putExtra(MSCParams.SRC_APP_ID, getAppId());
            containerDelegate.setResult(Activity.RESULT_OK, intent);
        }
        /**
         * MSC 暂不支持多任务栈 & 多进程，因此暂时无需相关逻辑
         */
//        // 原先在非保活activity之间（处于同一task）使用上面的setExtraData（Activity.setResult），
//        // 保活activity之间因跨task，setResult无效，使用下面的HeraActivity.onNavigateBackFromMiniProgram向实例直接设置
//        // 后增加了非保活->保活的混合情况，因此目前HeraActivity.onNavigateBackFromMiniProgram的目标也涵盖了非保活activity，功能有重叠
//        // 考虑到未来task结构可能有改动，暂不去除
//
//        String srcApp = controller.getSrcAppId();
//        if (srcApp == null) {
//            context.onError("current mini program is not launched by a mini program");
//            return;
//        }
//
//        // 向目标activity传递navigateBackMiniProgram携带的数据
//        new NavigateBackDataIPCTask().execute(MMPProcess.MAIN, srcApp, getAppId(), controller.getResultExtraData());
//
//        //TODO 此处存在问题，在当前小程序保活，且曾切出切入时，navigateBackMiniProgram会回到桌面，并不会返回至srcApp
//        // 微信及iOS的行为是，小程序一定会与启动它的小程序放在同一个栈里，因此无此问题，从小程序打开已保活小程序时，会把已有的实例杀掉再在自己的栈里新建
//        // 因改动较大，暂时不改
        containerDelegate.handleCloseContainer("navigateBackMiniProgram");
        context.onSuccess(null);
    }

    /**
     * 返回Native界面，并可以携带信息
     * <p>
     * https://km.sankuai.com/page/512877731
     */
    @MsiApiMethod(name = "navigateBackNative", request = NavigateBackParams.class, onUiThread = true)
    public void navigateBackNative(NavigateBackParams param, MsiContext context) {
        IContainerDelegate containerDelegate = getContainerDelegate(context);
        if (containerDelegate == null) {
            context.onError("not containerDelegate alive", MSIError.getGeneralError(MSCErrorCode.ERROR_CODE_API_COMMON_MEET_EXPECTATIONS));
            return;
        }
        Intent intent = new Intent();
        if (param.extraData != null && !param.extraData.isJsonNull()) {
            String resultData = param.extraData.toString();
            //生命周期感知:MSC_WILL_LEAVE_APP_LIFECYCLE 回调时带上 LeaveAppInfo
            containerDelegate.setLeaveAppInfo(resultData);
            intent.putExtra(MSCParams.EXTRA_DATA, resultData);
            intent.putExtra("resultData", resultData);    // 此参数不同于mmp返回结果时标准的extraData，需要注意
            // FIXME: 2022/9/5 setNavigateBackNativeData 广播？？、https://km.sankuai.com/page/1214211706 暂不支持 com/meituan/msc/common/utils/MiniProgramUtil.java:69
//            if (controller != null) {
//                controller.setNavigateBackNativeData(extraData.toString());
//            }
        }
        intent.putExtra(MSCParams.APP_ID, getAppId());
        containerDelegate.setResult(Activity.RESULT_OK, intent);

        String broadcastName = IntentUtil.getStringExtra(containerDelegate.getIntent(), "navigateBackBroadCastAction");
        if (TextUtils.isEmpty(broadcastName)) {
            MSCLog.i(TAG, "Broadcast name is null in navigateBackNative, please use setResult onActivityResult!");
        } else {
            //独立任务栈场景需要使用广播，setResult方式失效
            intent.setAction(broadcastName);
            containerDelegate.getActivity().sendBroadcast(intent);
        }
        if (containerDelegate.isWidget()) {
            MSCLog.d(TAG, "widget navigateBackNative");
            containerDelegate.getActivity().finish();
        } else {
            MSCLog.d(TAG, "page navigateBackNative");
            containerDelegate.handleCloseContainer("navigateBackNative");
        }
        context.onSuccess(null);
    }

}
