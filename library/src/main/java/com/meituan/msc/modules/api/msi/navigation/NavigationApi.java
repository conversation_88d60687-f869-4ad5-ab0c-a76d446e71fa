package com.meituan.msc.modules.api.msi.navigation;

import android.support.annotation.VisibleForTesting;
import com.meituan.msc.modules.api.ApiException;
import com.meituan.msc.modules.api.msi.MSCApi;
import com.meituan.msc.modules.api.msi.MSCErrorCode;
import com.meituan.msc.modules.container.IContainerManager;
import com.meituan.msc.modules.engine.MSCHornRollbackConfig;
import com.meituan.msc.modules.navigation.INavigationModule;
import com.meituan.msc.modules.page.IPageManagerModule;
import com.meituan.msc.modules.page.IPageModule;
import com.meituan.msc.modules.reporter.MSCLog;
import com.meituan.msi.annotations.MsiApiEnv;
import com.meituan.msi.annotations.MsiApiMethod;
import com.meituan.msi.api.IMsiApi;
import com.meituan.msi.api.MSIError;
import com.meituan.msi.bean.ContainerInfo;
import com.meituan.msi.bean.LifecycleData;
import com.meituan.msi.bean.MsiContext;
import com.sankuai.meituan.serviceloader.annotation.ServiceLoaderInterface;

import static com.meituan.msc.modules.api.msi.MSCErrorCode.ERROR_CODE_API_COMMON_INVALID_PARAM;
import static com.meituan.msc.modules.api.msi.MSCErrorCode.ERROR_CODE_API_COMMON_MEET_EXPECTATIONS;
import static com.meituan.msc.modules.api.msi.MSCErrorCode.ERROR_CODE_API_COMMON_NOT_MATCH_PATH;
import static com.meituan.msc.modules.api.msi.MSCErrorCode.ERROR_CODE_API_COMMON_PATH_NOT_EXIST;

/**
 * widget 兼容行为： https://km.sankuai.com/page/644599884
 * Created by letty on 2022/1/14.
 **/

@ServiceLoaderInterface(key = "msc_navigation", interfaceClass = IMsiApi.class)
@MsiApiEnv(name = ContainerInfo.ENV_MSC)
public class NavigationApi extends MSCApi {
    private static final int ERROR_CODE_ROUTE_COMMON = 800000200;

    private final String TAG = "NavigationApi@" + Integer.toHexString(hashCode());

    private INavigationModule getNavigationModule() {
        return getModule(INavigationModule.class);
    }

    private boolean handleWidgetNavigation(NavigationParam param, MsiContext context, long routeTime) {
        if (isWidgetForeground(context)) {
            try {
                BizNavigationExtraParams bizNavigationExtraParams = new BizNavigationExtraParams.Builder().setDisablePrefetch(param.disablePrefetch).build();
                getNavigationModule().launchApp(param.url, param.openSeq, routeTime, bizNavigationExtraParams);
            } catch (ApiException e) {
                MSCLog.e(TAG, e, "handleWidgetNavigation", param.url);
                context.onError(e.getMessage(), MSIError.getGeneralError(ERROR_CODE_ROUTE_COMMON));
                return true;
            }
            context.onSuccess("widget navigate to new app");
            return true;
        }
        return false;
    }

    @MsiApiMethod(name = "navigateTo", request = NavigationParam.class, onUiThread = true)
    public void navigateTo(NavigationParam param, MsiContext context) {
        long routeTime = System.currentTimeMillis();
        if (handleWidgetNavigation(param, context, routeTime)) {
            return;
        }
        try {
            BizNavigationExtraParams bizNavigationExtraParams = new BizNavigationExtraParams.Builder()
                    .setDisablePrefetch(param.disablePrefetch)
                    .build();
            getTopManagerModule(getPageId(context)).navigateTo(param.url, param.openSeq, routeTime, bizNavigationExtraParams);
        } catch (ApiException e) {
            MSCLog.e(TAG, e, "navigateTo", param.url);
            context.onError(e.getMessage(), getMSIError(e));
            return;
        }
        context.onSuccess(null);
    }

    @MsiApiMethod(name = "switchTab", request = NavigationParam.class, onUiThread = true)
    public void switchTab(NavigationParam param, MsiContext context) {
        long routeTime = System.currentTimeMillis();
        if (handleWidgetNavigation(param, context, routeTime)) {
            return;
        }
        try {
            BizNavigationExtraParams bizNavigationExtraParams = new BizNavigationExtraParams.Builder()
                    .setDisablePrefetch(param.disablePrefetch)
                    .build();
            getTopManagerModule(getPageId(context)).switchTab(param.url, routeTime, bizNavigationExtraParams);
        } catch (ApiException e) {
            MSCLog.e(TAG, e, "switchTab", param.url);
            context.onError(e.getMessage(), getMSIError(e));
            return;
        }
        context.onSuccess(null);
    }

    @MsiApiMethod(name = "reLaunch", request = NavigationParam.class, onUiThread = true)
    public void reLaunch(NavigationParam param, MsiContext context) {
        long routeTime = System.currentTimeMillis();
        if (handleWidgetNavigation(param, context, routeTime)) {
            return;
        }
        try {
            BizNavigationExtraParams bizNavigationExtraParams = new BizNavigationExtraParams.Builder()
                    .setDisablePrefetch(param.disablePrefetch)
                    .build();
            getTopManagerModule(getPageId(context)).reLaunch(param.url, routeTime, bizNavigationExtraParams);
        } catch (ApiException e) {
            MSCLog.e(TAG, e, "reLaunch", param.url);
            context.onError(e.getMessage(), getMSIError(e));
            return;
        }
        context.onSuccess(null);
    }

    @MsiApiMethod(name = "redirectTo", request = NavigationParam.class, onUiThread = true)
    public void redirectTo(NavigationParam param, MsiContext context) {
        long routeTime = System.currentTimeMillis();
        if (handleWidgetNavigation(param, context, routeTime)) {
            return;
        }
        try {
            BizNavigationExtraParams bizNavigationExtraParams = new BizNavigationExtraParams.Builder()
                    .setDisablePrefetch(param.disablePrefetch)
                    .build();
            getTopManagerModule(getPageId(context)).redirectTo(param.url, routeTime, bizNavigationExtraParams);
        } catch (ApiException e) {
            MSCLog.e(TAG, e, "redirectTo", param.url);
            context.onError(e.getMessage(), getMSIError(e));
            return;
        }
        context.onSuccess(null);
    }

    @MsiApiMethod(name = "navigateBack", request = NavigateBackParam.class, onUiThread = true)
    public void navigateBack(NavigateBackParam param, MsiContext context) {
        long routeTime = System.currentTimeMillis();
        if (isWidgetForeground(context)) {
            context.onError("cannot navigate back in widget", MSIError.getGeneralError(MSCErrorCode.ERROR_CODE_API_COMMON_NOT_MATCH_SCENE));
            return;
        }
        try {
            //Native渲染时,如果有page-container等容器组件需要拦截弹窗,此时即便是栈顶,也不应关闭容器,而是先响应弹窗关闭逻辑
            if (MSCHornRollbackConfig.enablePageContainerLifecycleIntercept()) {
                IPageManagerModule pageManagerModule = getTopManagerModule(getPageId(context));
                IPageModule curPage = pageManagerModule.getTopPage();
                if (curPage != null && curPage.getRenderer() != null && curPage.getRenderer().isNativeRender() && curPage.getRenderer().getPageLifecycleCallback() != null) {
                    if (curPage.getRenderer().getPageLifecycleCallback().onBackPressed(curPage.getId(), new LifecycleData())) {
                        MSCLog.i(TAG, "PageManager navigateBackPage NativeRender onBackPressed");
                        context.onError("navigateBack intercepted", MSIError.getGeneralError(MSCErrorCode.ERROR_CODE_API_COMMON_NOT_MATCH_SCENE));
                        return;
                    }
                }
            }
            getTopManagerModule(getPageId(context)).navigateBack(param.delta, param.__mtAllowCloseContainer, routeTime);
        } catch (ApiException e) {
            MSCLog.e(TAG, e, "navigateBack");
            context.onError(e.getMessage(), getMSIError(e));
            return;
        }
        context.onSuccess(null);
    }

    @VisibleForTesting
    public IPageManagerModule getTopManagerModule(int pageId) throws ApiException {
        IPageManagerModule pageManagerModule = getModule(IContainerManager.class).getTopPageManager();
        if (pageManagerModule == null) {
            throw new ApiException(ERROR_CODE_API_COMMON_MEET_EXPECTATIONS, "operation not available for not pageStack");
        }
        // FIXME: 2022/12/14 评估路由是否需要通过 pageId 来获取页面栈进行操作；
        // 本期先不做逻辑修改，先添加校验栈顶页面栈是否与前端栈顶pageId一致
        IPageManagerModule pageStackByPageId = getModule(IContainerManager.class).getPageManagerByPageId(pageId);
        if (pageStackByPageId != pageManagerModule) {
            MSCLog.w(TAG, "pageStack misMatched", pageManagerModule, pageStackByPageId);
            getRuntime().getRuntimeReporter().once("msc.navigation.page.stack.mismatched").sendDelay();
        }

        return pageManagerModule;
    }

    private MSIError getMSIError(ApiException apiException){
        MSIError msiError = MSCErrorCode.getCommonMSIError(apiException);
        if (msiError == null) {
            msiError = MSIError.getGeneralError(ERROR_CODE_ROUTE_COMMON);
        }
        return msiError;
    }
}
