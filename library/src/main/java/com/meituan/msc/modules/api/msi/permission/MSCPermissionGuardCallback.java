package com.meituan.msc.modules.api.msi.permission;

import android.support.annotation.NonNull;

import com.meituan.msc.common.config.MSCConfig;
import com.meituan.msc.modules.reporter.MSCLog;
import com.meituan.msc.common.utils.LocationUtils;
import com.meituan.msi.privacy.permission.MsiPermissionGuard;
import com.meituan.msi.privacy.permission.PermissionGuardCallback;

import java.util.List;
import java.util.concurrent.ConcurrentHashMap;

/**
 * 权限检查兜底逻辑 https://km.sankuai.com/page/1237298609
 *
 * <AUTHOR>
 * @date 2021/12/17.
 */
public class MSCPermissionGuardCallback implements PermissionGuardCallback {

    private static final String TAG = "MMPPermissionGuardCallback";
    public final ConcurrentHashMap<String, PermissionRequestLimitData> permissionRequestLimitDataMap = new ConcurrentHashMap<>();

    @Override
    public boolean allowRequestPermission(String[] permissions, String token) {
        if (permissions == null || permissions.length == 0) {
            MSCLog.e(TAG, "permission param invalid");
            return false;
        }

        // 定位限制策略保持原有逻辑，MMPPermissionStrategy
        if (LocationUtils.isLocationPermission(permissions)) {
            MSCLog.d(TAG, "isLocationPermission not check");
            return true;
        }

        MSCLog.d(TAG, "enable:", MSCConfig.enableRequestPermissionLimit());
        if (!MSCConfig.enableRequestPermissionLimit()) {
            return true;
        }

        // 权限组在白名单，不受限制，允许请求权限
        if (isPermissionInWhiteList(permissions)) {
            MSCLog.d(TAG, "permissions in whitelist:", createPermissionStr(permissions));
            return true;
        }

        // 不符合限制条件，允许请求权限
        boolean allowRequestPermission = true;
        for (String permission : permissions) {
            PermissionRequestLimitData limit = permissionRequestLimitDataMap.get(permission);
            if (limit == null) {
                continue;
            }

            // 时间间隔的限制 优先级 高于 请求权限的次数限制

            // 未超出时间间隔，不允许再次请求
            if (!isTimeIntervalOutOfLimit(permission, limit)) {
                allowRequestPermission = false;
            } else {
                // 超出请求次数上限，不允许再次请求
                if (isRequestCountOutOfLimit(permission, limit)) {
                    allowRequestPermission = false;
                }
            }
        }
        MSCLog.d(TAG, "permissions:", createPermissionStr(permissions)
                + ",token:" + token + ",allowRequestPermission:" + allowRequestPermission);
        return allowRequestPermission;
    }

    @Override
    public void onResult(String token, String[] permissions, int[] retCode) {
        boolean grand = MsiPermissionGuard.isGrand(retCode);
        MSCLog.d(TAG, "permissions:", createPermissionStr(permissions)
                + ",token:" + token + ",onResult:" + grand);
        if (!grand) {
            // 更新权限请求限制数据
            for (String permission : permissions) {
                PermissionRequestLimitData limit = permissionRequestLimitDataMap.get(permission);
                if (limit == null) {
                    permissionRequestLimitDataMap.put(permission, createPermissionCheckLimit());
                } else {
                    limit.requestTimes++;
                    limit.lastRequestTimeMillis = System.currentTimeMillis();
                }
            }
        }
    }

    private String createPermissionStr(String[] permissions) {
        if (permissions == null) {
            return "permissions is empty";
        }
        StringBuilder builder = new StringBuilder();
        for (String permission : permissions) {
            builder.append(permission);
            builder.append(",");
        }
        return builder.toString();
    }

    private boolean isPermissionInWhiteList(String[] permissions) {
        List<String> whiteList = MSCConfig.getRequestPermissionWhiteList();
        if (whiteList == null || whiteList.isEmpty()) {
            return false;
        }

        boolean inWhiteList = true;
        for (String permission : permissions) {
            if (!whiteList.contains(permission)) {
                inWhiteList = false;
                break;
            }
        }
        return inWhiteList;
    }

    private boolean isRequestCountOutOfLimit(String permission, @NonNull PermissionRequestLimitData limit) {
        int maxRequestTimes = MSCConfig.getMaxRequestPermissionTimes();
        MSCLog.d(TAG, "isRequestCountOutOfLimit permission:", permission, ",count:", limit.requestTimes, ",max:", maxRequestTimes);
        // 默认不开启次数限制
        if (maxRequestTimes == -1) {
            return false;
        }
        return limit.requestTimes >= maxRequestTimes;
    }

    private PermissionRequestLimitData createPermissionCheckLimit() {
        PermissionRequestLimitData checkLimit = new PermissionRequestLimitData();
        checkLimit.requestTimes++;
        checkLimit.lastRequestTimeMillis = System.currentTimeMillis();
        return checkLimit;
    }

    private boolean isTimeIntervalOutOfLimit(String permission, @NonNull PermissionRequestLimitData limit) {
        long intervalMillis = MSCConfig.getRequestPermissionTimeIntervalMillis();
        MSCLog.d(TAG, "isTimeIntervalOutOfLimit permission:", permission
               , ",current:", System.currentTimeMillis()
               , ",last:", limit.lastRequestTimeMillis
               , ",interval:", intervalMillis);
        return System.currentTimeMillis() - limit.lastRequestTimeMillis > intervalMillis;
    }

    public static class PermissionRequestLimitData {
        public int requestTimes;
        public long lastRequestTimeMillis;
    }
}
