package com.meituan.msc.modules.api.msi.permission;

import com.meituan.msc.modules.container.IContainerDelegate;
import com.meituan.msc.modules.engine.MSCRuntime;
import com.meituan.msi.provider.PermissionStrategy;


public class MSCPermissionStrategy implements PermissionStrategy {
    private PermissionModule mPermissionModule;
    private MSCRuntime runtime;

    public MSCPermissionStrategy(MSCRuntime runtime, PermissionModule permissionModule) {
        this.runtime = runtime;
        this.mPermissionModule = permissionModule;
    }

    /**
     * 容器检验权限
     * @param permissions
     * @return true 允许申请权限
     *         false 不允许申请权限
     */
    @Override
    public boolean handlePermission(String[] permissions, String token, StringBuilder errorBuilder) {
        if (permissions != null && permissions.length > 0 && mPermissionModule != null) {
            //定位相关请求系统权限3次之后不再请求 https://ones.sankuai.com/ones/product/6246/workItem/requirement/detail/7879378
            int handleRes = mPermissionModule.handleLocationRequestPermission(permissions);
            if (handleRes == PermissionModule.LOCATION_PERMISSION_RES_ERROR_LIMIT_COUNT ||
                    handleRes == PermissionModule.LOCATION_PERMISSION_RES_ERROR_LIMIT_TIME_INTERVAL) {
                    if (errorBuilder != null){
                        errorBuilder.append(mPermissionModule.getRequestLocationPermissionErrorDes(handleRes));
                    }
                return true;
            }

            //宿主自定义权限管理
            IContainerDelegate container = runtime.getContainerManagerModule().getTopContainer();
            if (container != null) {
                return mPermissionModule.handleRequestPermissions(container.getActivity(), permissions, token);
            }
        }

        return false;
    }
}
