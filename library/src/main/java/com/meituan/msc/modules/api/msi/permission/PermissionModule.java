package com.meituan.msc.modules.api.msi.permission;

import android.app.Activity;
import android.support.annotation.NonNull;

import com.meituan.msc.common.utils.LocationUtils;
import com.meituan.msc.modules.api.LocationRequestIntervalLimit;
import com.meituan.msc.modules.engine.MSCRuntime;
import com.meituan.msc.modules.manager.MSCModule;
import com.meituan.msc.modules.manager.ModuleName;
import com.meituan.msi.privacy.permission.MsiPermissionGuard;
import com.meituan.msi.privacy.permission.PermissionGuardCallback;

/**
 * Created by letty on 2022/1/19.
 **/
@ModuleName(name = "PermissionModule")
public class PermissionModule extends MSCModule {
    MSCPermissionGuardCallback mPermissionGuardCallback = new MSCPermissionGuardCallback();
    MSCPermissionStrategy mMSCPermissionStrategy;

    private static int maxRequestLocationPermissionCount = 3;
    private int maxRequestLocationPermissionCountPerApp = maxRequestLocationPermissionCount;
    private int mRequestLocationPermissionCount;

    public static final int LOCATION_PERMISSION_RES_OK = 0;
    public static final int LOCATION_PERMISSION_RES_ERROR_LIMIT_COUNT = 1;
    public static final int LOCATION_PERMISSION_RES_ERROR_LIMIT_TIME_INTERVAL = 2;

    public PermissionModule(MSCRuntime runtime) {
        mMSCPermissionStrategy = new MSCPermissionStrategy(runtime, this);
    }

    public MSCPermissionStrategy getMMPPermissionStrategy() {
        return mMSCPermissionStrategy;
    }

    public int handleLocationRequestPermission(String[] permissions) {
        if (LocationUtils.isLocationPermission(permissions)) {
            if (++mRequestLocationPermissionCount > maxRequestLocationPermissionCountPerApp) {
                return LOCATION_PERMISSION_RES_ERROR_LIMIT_COUNT;
            }

            if (getRuntime().getApp() != null && !LocationRequestIntervalLimit.timeInterval(getRuntime().getAppId())) {
                return LOCATION_PERMISSION_RES_ERROR_LIMIT_TIME_INTERVAL;
            }
        }

        return LOCATION_PERMISSION_RES_OK;
    }

    public String getRequestLocationPermissionErrorDes(int error) {
        String msg = "auth denied ";
        if (error == LOCATION_PERMISSION_RES_ERROR_LIMIT_COUNT) {
            msg += "system permission has been denied more than " + maxRequestLocationPermissionCountPerApp + " times";
        } else if (error == LOCATION_PERMISSION_RES_ERROR_LIMIT_TIME_INTERVAL) {
            msg += "checkPermissionPer48h";
        }
        return msg;
    }

    /**
     * @param activity
     * @param permissions
     * @param token
     * @return true 进行拦截
     * false 不进行拦截
     */
    public boolean handleRequestPermissions(Activity activity, String[] permissions, String token) {
        if (mRequestPermissionListener != null) {
            return mRequestPermissionListener.onRequestPermissions(activity, permissions, token);
        }

        return false;
    }

    public PermissionGuardCallback getPermissionGuardCallback() {
        return mPermissionGuardCallback;
    }

    public static void setMaxRequestLocationPermissionCount(int count) {
        maxRequestLocationPermissionCount = count;
    }

    private static RequestPermissionListener mRequestPermissionListener;

    public static void addRequestPermissionListener(RequestPermissionListener permissionCompatDelegate) {
        mRequestPermissionListener = permissionCompatDelegate;
    }

    public static RequestPermissionListener getRequestPermissionListener() {
        return mRequestPermissionListener;
    }

    public interface RequestPermissionListener {
        /**
         * 是否block后续权限申请
         *
         * @param activity
         * @param permissions
         * @param token
         * @return true 进行拦截
         * false 不进行拦截
         */
        boolean onRequestPermissions(@NonNull Activity activity, @NonNull String[] permissions, String token);
    }

}
