package com.meituan.msc.modules.api.msi.preload;

import com.google.gson.JsonObject;
import com.meituan.msc.common.framework.Callback;
import com.meituan.msc.modules.api.ApiException;
import com.meituan.msc.modules.api.msi.MSCErrorCode;
import com.meituan.msc.modules.preload.PreloadManager;
import com.meituan.msc.modules.preload.PreloadResultData;
import com.meituan.msi.api.IError;
import com.meituan.msi.api.preload.IContainerPreloadBizProvider;
import com.meituan.msi.api.preload.PreloadBizParam;
import com.meituan.msi.api.preload.PreloadBizResp;
import com.meituan.msi.api.preload.PreloadCallback;
import com.meituan.msi.bean.ContainerInfo;
import com.sankuai.meituan.serviceloader.annotation.ServiceLoaderInterface;

/**
 * <AUTHOR>
 * @Date 2025/4/1 23:00
 * @Description:
 */
@ServiceLoaderInterface(key = ContainerInfo.ENV_MSC, interfaceClass = IContainerPreloadBizProvider.class)
public class MscPreloadProvider implements IContainerPreloadBizProvider {

    @Override
    public void preloadBiz(PreloadBizParam preloadParam, PreloadCallback callback) {
        boolean preloadWebView = false;
        JsonObject extra = preloadParam.extra;
        if (extra != null && extra.get("preloadWebView") != null) {
            preloadWebView = extra.get("preloadWebView").getAsBoolean();
        }
        PreloadManager.getInstance()
                .preloadBiz(preloadParam.bundleName, preloadParam.pageName, preloadWebView,
                        new Callback<PreloadResultData>() {
                            @Override
                            public void onSuccess(PreloadResultData data) {
                                PreloadBizResp resp = new PreloadBizResp();
                                resp.preloadResp = new JsonObject();
                                resp.preloadResp.addProperty("mscPreload", "success");
                                resp.preloadResp.addProperty("appId", data.getAppId());
                                resp.preloadResp.addProperty("targetPath", data.getTargetPath());
                                resp.preloadResp.addProperty("preloadWebView", data.isPreloadWebView());
                                callback.onSuccess(resp);
                            }

                            @Override
                            public void onFail(String errMsg, Exception error) {
                                int level = IError.ErrLevel.GENERAL;
                                if (error instanceof ApiException) {
                                    int code = ((ApiException) error).getErrorCode();
                                    if (code == MSCErrorCode.ERROR_CODE_API_COMMON_MEET_EXPECTATIONS || code == MSCErrorCode.ERROR_CODE_API_COMMON_CLIENT_CLOSE) {
                                        level = IError.ErrLevel.IGNORABLE;
                                    }
                                }
                                callback.onError(errMsg, level);
                            }

                            @Override
                            public void onCancel() {
                                callback.onError("msc runtime exist, preload cancel!", IError.ErrLevel.IGNORABLE);
                            }
                        });
    }
}