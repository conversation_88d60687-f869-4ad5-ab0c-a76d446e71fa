package com.meituan.msc.modules.api.msi.preload;

import android.util.Log;

import com.meituan.msc.common.framework.Callback;
import com.meituan.msc.modules.api.ApiException;
import com.meituan.msc.modules.api.msi.MSCApi;
import com.meituan.msc.modules.api.msi.MSCErrorCode;
import com.meituan.msc.modules.preload.PreloadManager;
import com.meituan.msc.modules.preload.PreloadResultData;
import com.meituan.msi.annotations.MsiApiEnv;
import com.meituan.msi.annotations.MsiApiMethod;
import com.meituan.msi.api.IMsiApi;
import com.meituan.msi.api.MSIError;
import com.meituan.msi.bean.ContainerInfo;
import com.meituan.msi.bean.MsiContext;
import com.sankuai.meituan.serviceloader.annotation.ServiceLoaderInterface;

/**
 * 支持MSI预热MSC能力
 * https://km.sankuai.com/collabpage/1853090611
 */
@ServiceLoaderInterface(key = "msc_preload", interfaceClass = IMsiApi.class)
public class PreloadApi implements IMsiApi {
	private static final int ERROR_CODE_PRELOAD_FAIL = 800000200;

	@MsiApiMethod(name = "preloadMSCBiz", request = PreloadParam.class)
	public void preloadMSCBiz(PreloadParam preloadParam, MsiContext context) {
		PreloadManager.getInstance()
				.preloadBiz(preloadParam.appId, preloadParam.targetPath, preloadParam.preloadWebView,
						new Callback<PreloadResultData>() {
							@Override
							public void onSuccess(PreloadResultData data) {
								context.onSuccess(data);
							}

							@Override
							public void onFail(String errMsg, Exception error) {
								context.onError(errMsg, getMsiError(error));
							}

							@Override
							public void onCancel() {
								context.onError("msc runtime exist, preload cancel!", MSIError.getIgnoreError(MSCErrorCode.ERROR_CODE_API_COMMON_MEET_EXPECTATIONS));
							}
						});
	}

	private MSIError getMsiError(Exception error) {
		MSIError msiError = null;
		if (error instanceof ApiException) {
			msiError = MSCErrorCode.getCommonMSIError((ApiException) error);
		}

		if (msiError == null) {
			msiError = MSIError.getGeneralError(ERROR_CODE_PRELOAD_FAIL);
		}

		return msiError;
	}
}
