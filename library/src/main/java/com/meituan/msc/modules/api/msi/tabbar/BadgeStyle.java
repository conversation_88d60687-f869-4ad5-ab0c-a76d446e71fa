package com.meituan.msc.modules.api.msi.tabbar;

import android.support.annotation.Keep;
import android.widget.RelativeLayout;

/**
 * <AUTHOR>
 * @Date 6/28/24 8:11 PM
 * @Description:
 */
@Keep
public class BadgeStyle {
    public static final int DEFAULT_BORDER_SIZE = 0;
    public static final String DEFAULT_BORDER_COLOR = "#ffffff";
    public static final int DEFAULT_NOT_SET_BORDER_RADIUS = -1;
    public static final String DEFAULT_BACKGROUND_COLOR = "#ff0000";
    public static final int DEFAULT_FONT_SIZE = 10;
    public static final String DEFAULT_FONT_WEIGHT = "normal";
    public static final String DEFAULT_COLOR = "#ffffff";
    public static final int DEFAULT_HEIGHT = 12;
    public static final int DEFAULT_WIDTH = RelativeLayout.LayoutParams.WRAP_CONTENT;
    // 角标边框粗细，单位 dp，默认为 0
    public int borderSize = DEFAULT_BORDER_SIZE;
    // 角标边框颜色
    public String borderColor = DEFAULT_BORDER_COLOR;
    // 角标圆角大小,单位 dp,默认为-1表示不设置圆角
    public int borderRadius = DEFAULT_NOT_SET_BORDER_RADIUS;
    // 角标背景颜色
    public String backgroundColor = DEFAULT_BACKGROUND_COLOR;
    // 角标字体大小
    public int fontSize = DEFAULT_FONT_SIZE;
    // 角标字体字重
    public String fontWeight = DEFAULT_FONT_WEIGHT;
    // 角标字体颜色
    public String color = DEFAULT_COLOR;
    // 角标高度，单位 dp，小于 fontSize 则取 fontSize 的值，大于 fontSize 时保持文字上下居中
    public int height = DEFAULT_HEIGHT;
    // 角标宽度, 单位dp
    public int width = DEFAULT_WIDTH;
    // 相对tabBar中心的位置,单位 dp ，若不设置默认右上角
    public Offset offset;
    public static class Offset {
        public int x = 0;
        public int y = 0;
    }
}
