package com.meituan.msc.modules.api.msi.tabbar;


import android.text.TextUtils;
import android.view.View;

import com.meituan.msc.common.utils.ColorUtil;
import com.meituan.msc.modules.api.msi.MSCApi;
import com.meituan.msc.modules.api.msi.MSCErrorCode;
import com.meituan.msc.modules.page.IPageModule;
import com.meituan.msc.modules.page.ITabPage;
import com.meituan.msc.modules.page.view.tab.TabBar;
import com.meituan.msc.modules.page.view.tab.TabItemView;
import com.meituan.msi.annotations.MsiApiEnv;
import com.meituan.msi.annotations.MsiApiMethod;
import com.meituan.msi.annotations.MsiSupport;
import com.meituan.msi.api.IMsiApi;
import com.meituan.msi.api.MSIError;
import com.meituan.msi.bean.ContainerInfo;
import com.meituan.msi.bean.MsiContext;
import com.sankuai.meituan.serviceloader.annotation.ServiceLoaderInterface;

@ServiceLoaderInterface(key = "msc_tabbar", interfaceClass = IMsiApi.class)
@MsiApiEnv(name = ContainerInfo.ENV_MSC)
public class TabBarApi extends MSCApi {
    public final static String WHITE = "#f5f5f5";
    public final static String BLACK = "#e5e5e5";

    // 非 tab 页调用tabbar api
    private final static int ERROR_CODE_TAB_BAR_NOT_TAB_PAGE = 800000201;
    //在自定义 tab 页中调用tabbar api
    private final static int ERROR_CODE_TAB_BAR_IS_CUSTOM = 800000202;
    //tabBar 为空时调用
    private final static int ERROR_CODE_TAB_BAR_NOT_EXIST = 800000203;
    //index越界
    private final static int ERROR_CODE_TAB_BAR_INDEX_OUT = 800000204;

    private TabBar checkTabPage(MsiContext context) {
        IPageModule pageModule = getPageModule(context);
        if (pageModule == null) {
            context.onError("no available page",
                    MSIError.getIgnoreError(MSCErrorCode.ERROR_CODE_API_COMMON_MEET_EXPECTATIONS));
            return null;
        }
        ITabPage tabPage = pageModule.getTabPage();
        if (tabPage == null) {
            context.onError("can not operate tab api while not in tab page",
                    MSIError.getGeneralError(ERROR_CODE_TAB_BAR_NOT_TAB_PAGE));
            return null;
        }
        if (tabPage.isCustomTabPage()) {
            context.onError("can not operate tab api in CustomTab Page",
                    MSIError.getGeneralError(ERROR_CODE_TAB_BAR_IS_CUSTOM));
            return null;
        }
        TabBar tabBar = tabPage.getTabBar();
        if (tabBar == null) {
            context.onError("can not operate tab api while tabBar is null",
                    MSIError.getGeneralError(ERROR_CODE_TAB_BAR_NOT_EXIST));
            return null;
        }
        return tabBar;
    }

    @MsiApiMethod(name = "showTabBar", env = {"msc"}, onUiThread = true)
    public void showTabBar(MsiContext context) {
        TabBar tabBar = checkTabPage(context);
        if (tabBar == null) {
            return;
        }

        tabBar.setVisibility(View.VISIBLE);
        context.onSuccess("");
    }

    @MsiApiMethod(name = "hideTabBar", env = {"msc"}, onUiThread = true)
    public void hideTabBar(MsiContext context) {
        TabBar tabBar = checkTabPage(context);
        if (tabBar == null) {
            return;
        }

        tabBar.setVisibility(View.GONE);
        context.onSuccess("");
    }

    @MsiApiMethod(name = "setTabBarBadge", request = TabBarApiParam.class, env = {"msc"}, onUiThread = true)
    public void setTabBarBadge(TabBarApiParam param, MsiContext context) {
        TabBar tabBar = checkTabPage(context);
        if (tabBar == null) {
            return;
        }
        if (param.style == null) {
            param.style = new BadgeStyle();
        }
        if (param.style.fontSize <= 0) {
            param.style.fontSize = BadgeStyle.DEFAULT_FONT_SIZE;
        }
        TabItemView tabItem = getTabBarItemWithValidation(param, tabBar, context, ERROR_CODE_TAB_BAR_INDEX_OUT);
        if (tabItem != null) {
            tabItem.setTabBarBadge(param.text, param.style);
            context.onSuccess("");
        }

    }

    @MsiApiMethod(name = "removeTabBarBadge", request = TabBarApiParam.class, env = {"msc"}, onUiThread = true)
    public void removeTabBarBadge(TabBarApiParam param, MsiContext context) {
        TabBar tabBar = checkTabPage(context);
        if (tabBar == null) {
            return;
        }

        TabItemView tabItem = getTabBarItemWithValidation(param, tabBar, context, ERROR_CODE_TAB_BAR_INDEX_OUT);
        if (tabItem != null) {
            tabItem.removeTabBarBadge();
            context.onSuccess("");
        }

    }

    @MsiApiMethod(name = "showTabBarRedDot", request = TabBarApiParam.class, env = {"msc"}, onUiThread = true)
    public void showTabBarRedDot(TabBarApiParam param, MsiContext context) {
        TabBar tabBar = checkTabPage(context);
        if (tabBar == null) {
            return;
        }

        TabItemView tabItem = getTabBarItemWithValidation(param, tabBar, context, ERROR_CODE_TAB_BAR_INDEX_OUT);
        if (tabItem != null) {
            tabItem.showTabBarRedDot();
            context.onSuccess("");
        }

    }

    @MsiApiMethod(name = "hideTabBarRedDot", request = TabBarApiParam.class, env = {"msc"}, onUiThread = true)
    public void hideTabBarRedDot(TabBarApiParam param, MsiContext context) {
        TabBar tabBar = checkTabPage(context);
        if (tabBar == null) {
            return;
        }

        TabItemView tabItem = getTabBarItemWithValidation(param, tabBar, context, ERROR_CODE_TAB_BAR_INDEX_OUT);
        if (tabItem != null) {
            tabItem.hideTabBarRedDot();
            context.onSuccess("");
        }
    }

    @MsiSupport
    public static class TabBarStyle {

        public String color;

        public String selectedColor;

        public String backgroundColor;

        public String borderColor;

        public String borderStyle;
    }
    @MsiApiMethod(name = "setTabBarStyle", request = TabBarStyle.class, env = {"msc"}, onUiThread = true)
    public void setTabBarStyle(TabBarStyle param, MsiContext context) {
        TabBar tabBar = checkTabPage(context);
        if (tabBar == null) {
            return;
        }

        if (!TextUtils.isEmpty(param.backgroundColor)) {
            tabBar.setBackgroundColor(ColorUtil.parseColor(param.backgroundColor));
        }
        // 优先使用borderColor字段，borderColor为空时使用borderStyle字段
        String borderColor = param.borderColor;
        if (TextUtils.isEmpty(borderColor) && !TextUtils.isEmpty(param.borderStyle)) {
            borderColor = "white".equals(param.borderStyle) ? WHITE : BLACK;
        }
        if (!TextUtils.isEmpty(borderColor)) {
            tabBar.setBorderColor(ColorUtil.parseColor(borderColor));
        }

        int count = tabBar.getTabItemCount();
        for (int i = 0; i < count; i++) {
            TabItemView tabItem = tabBar.getTabItem(i);
            if (tabItem != null) {
                tabItem.setTabBarItemStyle(param.color, param.selectedColor);
                tabItem.setSelected(tabItem.isSelected());
            }
        }
        context.onSuccess("");
    }

    @MsiApiMethod(name = "setTabBarItem", request = TabBarApiParam.class, env = {"msc"}, onUiThread = true)
    public void setTabBarItem(TabBarApiParam param, MsiContext context) {
        TabBar tabBar = checkTabPage(context);
        if (tabBar == null) {
            return;
        }

        TabItemView tabItem = getTabBarItemWithValidation(param, tabBar, context, ERROR_CODE_TAB_BAR_INDEX_OUT);
        if (tabItem != null) {
            tabItem.setTabBarItem(param.text, param.iconPath, param.selectedIconPath, param.isLargerIcon);
            tabItem.setSelected(tabItem.isSelected());
            context.onSuccess("");
        }
    }

    private TabItemView getTabBarItemWithValidation(TabBarApiParam param, TabBar tabBar, MsiContext context, int errorCode) {
        if (param.index == null || param.index < 0 || param.index > tabBar.getTabItemCount() - 1) {
            context.onError("index越界", MSIError.getGeneralError(errorCode));
            return null;
        }
        return tabBar.getTabItem(param.index);
    }

}

