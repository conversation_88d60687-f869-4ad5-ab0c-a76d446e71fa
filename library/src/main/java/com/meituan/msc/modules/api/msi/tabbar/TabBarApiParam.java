package com.meituan.msc.modules.api.msi.tabbar;

import com.meituan.msi.annotations.MsiParamChecker;
import com.meituan.msi.annotations.MsiSupport;

@MsiSupport
public class TabBarApiParam {
    public String text;

    public String iconPath;

    public String selectedIconPath;

    @MsiParamChecker(min = 0, required = true)
    public Integer index;

    //如果 isLargerIcon 为 true，则该 tab 项不展示文字，且放大图标。否则展示文字，图标大小正常。默认为 false。
    public boolean isLargerIcon = false;
    public BadgeStyle style = new BadgeStyle();
}
