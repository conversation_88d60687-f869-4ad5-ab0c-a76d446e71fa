package com.meituan.msc.modules.api.msi.webview;

import android.content.Context;
import android.content.Intent;
import android.content.pm.PackageManager;
import android.content.pm.ResolveInfo;
import android.net.Uri;
import android.os.Build;
import android.support.annotation.Nullable;
import android.text.TextUtils;
import android.view.View;
import android.webkit.ValueCallback;
import android.webkit.WebView;

import com.google.gson.JsonObject;
import com.meituan.msc.common.config.MSCConfig;
import com.meituan.msc.common.utils.FileUtil;
import com.meituan.msc.common.utils.ToastUtils;
import com.meituan.msc.modules.api.msi.hook.RequestUserAgent;
import com.meituan.msc.modules.engine.MSCHornRollbackConfig;
import com.meituan.msc.modules.engine.MSCRuntime;
import com.meituan.msc.modules.page.IWebViewComponentInfo;
import com.meituan.msc.modules.reporter.MSCLog;
import com.meituan.msc.modules.update.MSCAppModule;
import com.meituan.msi.api.ApiCallback;
import com.meituan.msi.bean.MsiContext;
import com.meituan.msi.context.IActivityResultCallBack;
import com.meituan.msi.view.INativeLifecycleInterceptor;
import com.meituan.mtwebkit.MTWebView;

import org.json.JSONException;
import org.json.JSONObject;

/**
 * 管理webview视图，同时向前端传递事件
 */
public abstract class BaseWebViewComponentManager {

    public static final String TAG = "BaseWebViewComponentManager";
    //viewId
    protected String mHtmlId;
    protected int mPageId;
    public static String JAVASCRIPT_ENVIROMENT = "javascript:window.__wxjs_environment = 'miniprogram';";
    private IWebNativeToJsBridge mWebNative2JsBridge;
    MSCRuntime mRuntime;
    Context context;
    INativeLifecycleInterceptor mNativeLifecycleInterceptor;
    MsiContext mMsiContext;
    ValueCallback<Uri[]> mFilePathCallback;

    WebProgressChangedListener mWebProgressChangedListener;

    IWebViewComponentInfo mWebViewComponent;

    boolean mPageFinished;

    abstract protected View createWebView(MsiContext msiContext, JsonObject uiParams, WebViewComponentParam params, IWebViewComponentInfo webViewComponent);

    abstract protected INativeLifecycleInterceptor getNativeLifecycleInterceptor();

    abstract protected void onActivityResult(int resultCode, Intent data);

    abstract protected long getWebViewComponentInitializationDuration();

    void setWebProgressChangedListener(WebProgressChangedListener webProgressChangedListener) {
        this.mWebProgressChangedListener = webProgressChangedListener;
    }

    public BaseWebViewComponentManager(Context context, MSCRuntime runtime, IWebViewComponentInfo webViewComponent) {
        this.mRuntime = runtime;
        this.context = context;
        this.mWebViewComponent = webViewComponent;
    }

    public void setWebNative2JsBridge(IWebNativeToJsBridge webNative2JsBrigde) {
        this.mWebNative2JsBridge = webNative2JsBrigde;
    }


    protected View createView(MsiContext msiContext, JsonObject uiParams, WebViewComponentParam params) {
        mPageId = msiContext.getPageId();
        mHtmlId = params.htmlId;
        this.mMsiContext = msiContext;
        return createWebView(msiContext, uiParams, params, mWebViewComponent);
    }

    /**
     * web-view组件 Fe调用Native api能力
     */
    public class WebViewApiInvokeListener {
        public String invoke(String param, ApiCallback apiCallback) {
            return invokeApiByMSI(param, apiCallback);
        }
    }

    /**
     * web-view组件通过msi调用Api
     * @param param
     * @param apiCallback
     * @return
     */
    public String invokeApiByMSI(String param, ApiCallback apiCallback){
        return mRuntime.apisManager.msiAsyncInvoke(param, apiCallback);
    }

    abstract protected boolean loadUrl(String url);


    public void setNavigationBarTitle(String title) {
        JSONObject data = new JSONObject();
        JSONObject uiArgs = new JSONObject();
        JSONObject metrics = new JSONObject();
        JSONObject args = new JSONObject();
        try {
            uiArgs.put("pageId", mPageId);
            metrics.put("startTime", System.currentTimeMillis());
            args.put("title", title);
            data.put("uiArgs", uiArgs);
            data.put("scope", "default");
            data.put("name", "setNavigationBarTitle");
            data.put("args", args);
            invokeApiByMSI(data.toString(), new ApiCallback() {
                @Override
                public void onSuccess(Object data) {
                    MSCLog.i(TAG, "setNavigationBarTitle success!");
                }

                @Override
                public void onFail(Object error) {
                    MSCLog.i(TAG, "setNavigationBarTitle onFail!");
                }
            });
        } catch (JSONException e) {
            e.printStackTrace();
        }
    }

    protected String getUserAgent(String sExtend) {
        String ua = RequestUserAgent.getInstance().getCustomUserAgent();
        if (sExtend != null && ua != null) {
            ua = ua.replace(MSCAppModule.WEIXING_UA, sExtend);
        }

        return ua;
    }

    protected void notifyWebviewStartLoad(String url) {
        if (mWebNative2JsBridge == null) {
            return;
        }
        OnWebViewStartLoadEvent event = new OnWebViewStartLoadEvent();
        event.pageId = mPageId;
        event.htmlId = mHtmlId;
        event.src = url;
        mWebNative2JsBridge.dispatchEvent(WebViewComponentApi.MSC_WEB_VIEW_ON_START_LOAD, event, mPageId, mHtmlId);
    }

    protected void notifyOnWebviewFinishLoad(String url) {
        if (mWebNative2JsBridge == null) {
            return;
        }
        OnWebViewFinishLoadEvent event = new OnWebViewFinishLoadEvent();
        event.pageId = mPageId;
        event.htmlId = mHtmlId;
        event.src = url;
        mWebNative2JsBridge.dispatchEvent(WebViewComponentApi.MSC_WEB_VIEW_ON_FINISH_LOAD, event, mPageId, mHtmlId);
    }

    protected void notifyOnWebviewError(int errorCode, String description, String failingUrl) {
        if (mWebNative2JsBridge == null) {
            return;
        }
        OnWebViewErrorEvent event = new OnWebViewErrorEvent();
        event.pageId = mPageId;
        event.htmlId = mHtmlId;
        event.src = failingUrl;
        event.description = description;
        event.errorCode = errorCode;
        mWebNative2JsBridge.dispatchEvent(WebViewComponentApi.MSC_WEB_VIEW_ON_ERROR, event, mPageId, mHtmlId);
    }

    protected boolean shouldOverrideUrlLoading(String s) {
        // 所有页面跳转协议均上报
        mRuntime.getRuntimeReporter().reportWebViewUrl(getWebViewUrl(), s);

        if (!s.startsWith("http")) {
            Intent intent = new Intent(Intent.ACTION_VIEW, Uri.parse(s));
            PackageManager packageManager = context.getPackageManager();
            if (packageManager != null) {
                Intent hostIntent = new Intent(intent);
                hostIntent.setPackage(context.getPackageName());
                ResolveInfo resolveActivity = null;
                try {
                    resolveActivity = packageManager.resolveActivity(hostIntent, PackageManager.MATCH_DEFAULT_ONLY);
                } catch (RuntimeException ignore) {
                }
                if (resolveActivity == null || resolveActivity.activityInfo == null) {
                    try {
                        resolveActivity = packageManager.resolveActivity(hostIntent, 0);
                    } catch (RuntimeException ignore) {
                    }
                }
                if (resolveActivity != null && resolveActivity.activityInfo != null) {
                    //todo确认这个是否可以
                    mMsiContext.startActivityForResult(hostIntent, new IActivityResultCallBack() {
                        @Override
                        public void onActivityResult(int resultCode, Intent data) {
                            BaseWebViewComponentManager.this.onActivityResult(resultCode, data);
                        }

                        @Override
                        public void onFail(int errorCode, String errorMsg) {

                        }
                    });

                    return true;
                }
            }

            if (MSCConfig.nonsupportSchema(s)) {
                return false;
            }

            if (validSchema(s)) {
                if (context != null && intent.resolveActivity(context.getPackageManager()) != null) {
                    mMsiContext.startActivityForResult(intent, new IActivityResultCallBack() {
                        @Override
                        public void onActivityResult(int resultCode, Intent data) {
                            BaseWebViewComponentManager.this.onActivityResult(resultCode, data);
                        }

                        @Override
                        public void onFail(int errorCode, String errorMsg) {

                        }
                    });
                } else {
                    ToastUtils.toastIfDebug("no app support:" + s);
                }
                return true;
            }
        }

        return false;
    }

    @Nullable
    protected abstract String getWebViewUrl();

    /**
     * schema是否合法，hard code + horn配置扩展
     *
     * @param schema
     * @return
     */
    private boolean validSchema(String schema) {
        if (TextUtils.isEmpty(schema)) {
            return false;
        }

        if (MSCHornRollbackConfig.enableExternalAppStorageLimit()) {
            boolean isExternalApp = mRuntime.getMSCAppModule().getExternalApp();
            if (isExternalApp) {
                return schema.startsWith("tel:") || schema.startsWith("sms:");
            }
        }

        if (schema.startsWith("weixin://") || schema.startsWith("tel:") ||
                schema.startsWith("mailto:") || schema.startsWith("sms:") ||
                schema.startsWith("geo:") || schema.startsWith("alipays:")) {
            return true;
        }

        boolean res = false;
        try {
            res = MSCConfig.isSupportSchema(schema);
        } catch (Exception e) {
            //horn更新配置和查询是否支持存在存在多线程问题，捕获异常
        }
        return res;
    }

    /**
     * 美团webview
     * 注入wxjs.js。wxjs.js定义webview前端与客户端的通信协议及对外可使用的api
     * mscwxjs.js 文件在MSC Android、iOS逻辑稍有不同，原因是Android MSC MMP各有一份此文件，iOS共用（需要做环境判断）
     * @param mtWebView
     * @return
     */
    final boolean loadAssetsJSFileContent(MTWebView mtWebView) {
        String convertStreamToString;
        try {
            convertStreamToString = FileUtil.readAssetsFileContent(context, "mscwxjs.js");
        } catch (Throwable e) {
            convertStreamToString = null;
        }
        if (convertStreamToString == null) {
            return false;
        } else if (mtWebView == null) {
            return false;
        } else {
            try {
                if (Build.VERSION.SDK_INT >= Build.VERSION_CODES.KITKAT) {
                    mtWebView.evaluateJavascript("javascript:" + convertStreamToString, null);
                } else {
                    mtWebView.loadUrl("javascript:" + convertStreamToString);
                }
            } catch (Throwable t) { //前端可能执行失败，获取异常
                return false;
            }

            return true;
        }
    }

    /**
     * 注入wxjs.js wxjs.js定义webview前端与客户端的通信协议及对外可使用的api
     *
     * @param webView
     * @return
     */
    final boolean loadAssetsJSFileContent(WebView webView) {
        String convertStreamToString;
        try {
            convertStreamToString = FileUtil.readAssetsFileContent(context, "mscwxjs.js");
        } catch (Throwable e) {
            convertStreamToString = null;
        }
        if (convertStreamToString == null) {
            return false;
        } else if (webView == null) {
            return false;
        } else {
            try {
                if (Build.VERSION.SDK_INT >= Build.VERSION_CODES.KITKAT) {
                    webView.evaluateJavascript("javascript:" + convertStreamToString, null);
                } else {
                    webView.loadUrl("javascript:" + convertStreamToString);
                }
            } catch (Throwable t) {
                return false;
            }

            return true;
        }
    }

    protected void dispatcherPageState() {
        if (mWebViewComponent != null) {
            boolean show = mWebViewComponent.isPageShow();
            dispatcherPageEvent(show, mWebNative2JsBridge);
        }
    }

    protected static void dispatcherPageEvent(boolean active, IWebNativeToJsBridge native2JsBridge) {
        if (native2JsBridge != null) {
            JSONObject data = new JSONObject();
            try {
                data.put("active", active);
            } catch (JSONException e) {
                e.printStackTrace();
            }
            //onPageStateChange 在JS文件中，现在使用不了msi。
            WebJSBridge.dispatchEvent("onPageStateChange", data.toString(), native2JsBridge);
        }
    }

    protected String getJSBrigeName() {
        return "__wx";
    }
}
