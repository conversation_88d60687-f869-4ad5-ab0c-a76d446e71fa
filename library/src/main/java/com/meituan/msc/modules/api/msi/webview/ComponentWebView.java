package com.meituan.msc.modules.api.msi.webview;

import android.annotation.SuppressLint;
import android.app.Activity;
import android.content.Context;
import android.os.Build;
import android.support.annotation.NonNull;
import android.webkit.WebView;

import com.meituan.msi.bean.BroadcastEvent;
import com.meituan.msi.dispather.IMsiDispatcher;
import com.meituan.msi.view.INativeLifecycleInterceptor;

import java.util.HashMap;
import java.util.Map;

@SuppressLint("ViewConstructor")
public class ComponentWebView extends WebView
        implements INativeLifecycleInterceptor, IWebNativeToJsBridge, IComponentWebView {
    private final String INTERFACE = "__msc__plugin_webview";
    private MSCWebViewInterface mWebViewInterface;
    IMsiDispatcher mMsiDispatcher;

    public ComponentWebView(Context context, @NonNull IWebFocusDispatcher webFocusDispatcher, IMsiDispatcher dispatcher) {
        super(context);
        mWebViewInterface = new MSCWebViewInterface((Activity) context, webFocusDispatcher);
        mMsiDispatcher = dispatcher;
    }

    @Override
    protected void onAttachedToWindow() {
        super.onAttachedToWindow();
        this.addJavascriptInterface(mWebViewInterface, INTERFACE);
    }

    @Override
    protected void onDetachedFromWindow() {
        super.onDetachedFromWindow();
        removeJavascriptInterface(INTERFACE);
    }


    @Override
    public boolean onSystemDialogClose(String cause) {
        return false;
    }

    @Override
    public boolean onBackPressed() {
        return false;
    }

    @Override
    public void onPagePaused(int cause) {
        this.onPause();
        BaseWebViewComponentManager.dispatcherPageEvent(false, this);
    }

    @Override
    public void onPageResume() {
        this.onResume();
        BaseWebViewComponentManager.dispatcherPageEvent(true, this);
    }

    @Override
    public boolean isPipMode() {
        return false;
    }

    @Override
    public void evaluateJavascript(String script) {
        if (Build.VERSION.SDK_INT >= Build.VERSION_CODES.KITKAT) {
            evaluateJavascript(script, null);
        } else {
            loadUrl(script);
        }
    }

    @Override
    public void dispatchEvent(String name, Object msg, int pageId, String viewId) {
        //通过msi方式通知
        BroadcastEvent broadcastEvent = new BroadcastEvent(name, msg);
        Map<String, String> uiMap = new HashMap<>();
        uiMap.put("pageId", String.valueOf(pageId));
        uiMap.put("viewId", String.valueOf(viewId));
        broadcastEvent.setUiData(uiMap);
        mMsiDispatcher.dispatchEvent(broadcastEvent);
    }

    @Override
    public boolean tryGoBack() {
        if (canGoBack()) {
            goBack();
            return true;
        }
        return false;
    }
}
