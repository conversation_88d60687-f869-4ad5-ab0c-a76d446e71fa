package com.meituan.msc.modules.api.msi.webview;

import android.app.Activity;
import android.webkit.JavascriptInterface;

/**
 * webview模块回调接口，因为沉浸导致adjustSize失效，需要在键盘弹出的时候计算输入框的位置
 */
public class MSCWebViewInterface {
    IWebFocusDispatcher webFocusDispatcher;
    Activity activity;

    public MSCWebViewInterface(Activity context, IWebFocusDispatcher webFocusDispatcher) {
        this.webFocusDispatcher = webFocusDispatcher;
        activity = context;
    }

    @JavascriptInterface
    /**
     *
     * @param code 1 或者-1 ，1说明找到焦点，如果非1 后面的参数无效
     * @param bottom 输入框底部到顶部的距离
     * @param top 输入框顶部到顶部的距离
     */
    public void onInputFocusDispatcher(final int code, final float bottom, final float top) {
        activity.runOnUiThread(new Runnable() {
            @Override
            public void run() {
                if (webFocusDispatcher != null) {
                    webFocusDispatcher.onElementFocused(code, bottom, top);
                }
            }
        });

    }
}
