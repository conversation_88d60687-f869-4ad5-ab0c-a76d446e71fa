package com.meituan.msc.modules.api.msi.webview;

import android.annotation.TargetApi;
import android.app.Activity;
import android.content.Context;
import android.content.Intent;
import android.graphics.Bitmap;
import android.net.Uri;
import android.os.Build;
import android.os.SystemClock;
import android.support.annotation.Keep;
import android.text.TextUtils;
import android.util.Log;
import android.view.View;
import android.webkit.URLUtil;
import android.webkit.WebViewClient;

import com.google.gson.JsonObject;
import com.meituan.android.privacy.interfaces.PermissionGuard;
import com.meituan.android.privacy.interfaces.Privacy;
import com.meituan.msc.common.resource.MTWebResponseBuild;
import com.meituan.msc.common.utils.CIPStorageFileUtil;
import com.meituan.msc.common.utils.ViewUtils;
import com.meituan.msc.modules.api.RenderProcessGoneHandler;
import com.meituan.msc.modules.api.web.WebViewUtil;
import com.meituan.msc.modules.engine.MSCHornRollbackConfig;
import com.meituan.msc.modules.engine.MSCRuntime;
import com.meituan.msc.modules.page.IWebViewComponentInfo;
import com.meituan.msc.modules.page.render.webview.WebViewFileFilter;
import com.meituan.msc.modules.reporter.MSCLog;
import com.meituan.msi.bean.MsiContext;
import com.meituan.msi.context.IActivityResultCallBack;
import com.meituan.msi.view.INativeLifecycleInterceptor;
import com.meituan.mtwebkit.MTConsoleMessage;
import com.meituan.mtwebkit.MTPermissionRequest;
import com.meituan.mtwebkit.MTRenderProcessGoneDetail;
import com.meituan.mtwebkit.MTValueCallback;
import com.meituan.mtwebkit.MTWebChromeClient;
import com.meituan.mtwebkit.MTWebResourceRequest;
import com.meituan.mtwebkit.MTWebResourceResponse;
import com.meituan.mtwebkit.MTWebSettings;
import com.meituan.mtwebkit.MTWebView;
import com.meituan.mtwebkit.MTWebViewClient;

import java.util.ArrayList;
import java.util.HashMap;
import java.util.List;
import java.util.Map;

/**
 * Created by liting06 on 2018/5/10.
 */

//@RequiresApi(api = Build.VERSION_CODES.LOLLIPOP)
//@SuppressLint("NewApi")
public class MTWebViewComponentManager extends BaseWebViewComponentManager {
    public static final String TAG = "MTWebViewComponentManager";

    MTValueCallback<Uri[]> mFilePathCallback;

    private RenderProcessGoneHandler renderProcessGoneHandler;
    MtComponentWebView mWebView;

    private long mMTWebViewComponentInitializationDuration = 0;

    public MTWebViewComponentManager(Context context, MSCRuntime runtime, IWebViewComponentInfo webViewComponent) {
        super(context, runtime, webViewComponent);
    }


    @Override
    @SuppressWarnings("AddJavascriptInterface")
    protected View createWebView(MsiContext msiContext, JsonObject uiParams, WebViewComponentParam params, IWebViewComponentInfo webViewComponent) {
        long start = SystemClock.elapsedRealtime();
        mWebView = new MtComponentWebView(mRuntime.getMSCAppModule().getMTWebViewAppTag(),
                msiContext.getActivity(), webViewComponent.getWebFocusDispatcher(), msiContext.getEventDispatcher());
        mMTWebViewComponentInitializationDuration = SystemClock.elapsedRealtime() - start;
        mNativeLifecycleInterceptor = mWebView;
        setWebNative2JsBridge(mWebView);

        MTWebSettings webSettings = mWebView.getSettings();

        webSettings.setAllowFileAccess(true);
        if (MSCHornRollbackConfig.readConfig().enableFixFileAccessSecurity) {
            webSettings.setAllowFileAccessFromFileURLs(false);
            webSettings.setAllowUniversalAccessFromFileURLs(false);
            webSettings.setJavaScriptEnabled(TextUtils.isEmpty(params.src) || !params.src.startsWith("file://"));
        } else {
            webSettings.setJavaScriptEnabled(true);
        }
        //  webSettings.setDefaultFontSize(1);
        webSettings.setJavaScriptCanOpenWindowsAutomatically(true);

        // ========================
        webSettings.setDomStorageEnabled(true);
        webSettings.setBuiltInZoomControls(false);
        webSettings.setUseWideViewPort(true);
        webSettings.setLoadWithOverviewMode(true);
        webSettings.setSaveFormData(false);
        webSettings.setGeolocationEnabled(true);
        webSettings.setJavaScriptCanOpenWindowsAutomatically(true);
        if (Build.VERSION.SDK_INT >= Build.VERSION_CODES.LOLLIPOP) {
            webSettings.setMixedContentMode(0);
        }
        webSettings.setDatabaseEnabled(true);
        webSettings.setDatabasePath(CIPStorageFileUtil.getFilesDir(msiContext.getActivity(), "databases").getAbsolutePath());
        webSettings.setAppCacheMaxSize(10 * 1024 * 1024);
        webSettings.setAppCachePath(CIPStorageFileUtil.getFilesDir(msiContext.getActivity(), "webviewcache").getAbsolutePath());

        if (Build.VERSION.SDK_INT > Build.VERSION_CODES.ICE_CREAM_SANDWICH) {
            webSettings.setTextZoom(100);
        }

        try {
            webSettings.setMediaPlaybackRequiresUserGesture(false);
        } catch (Exception e) {
            //ignore
        }

        try {
            webSettings.setMediaPlaybackRequiresUserGesture(false);
        } catch (Exception e) {
            //ignore
        }
        String msc_ua_append = params.msc_ua_append;
        String ua = getUserAgent(msc_ua_append);
        webSettings.setUserAgentString(ua);

        mWebView.addJavascriptInterface(
                new WebJSBridge(new WebViewApiInvokeListener(), mWebView, params.htmlId, msiContext.getPageId()),
                getJSBrigeName());
        mWebView.setWebChromeClient(new MTWebChromeClient() {

            // WebView video组件默认图兜底（问题修复：https://stackoverflow.com/questions/76700882/nullpointer-exception-on-bitmap-getwidth-at-chromium-trichromewebviewgoogle-aa）
            @Override
            public Bitmap getDefaultVideoPoster() {
                return ViewUtils.getDefaultVideoPoster(super.getDefaultVideoPoster());
            }

            @Override
            public boolean onConsoleMessage(MTConsoleMessage consoleMessage) {
                if (consoleMessage.messageLevel() == MTConsoleMessage.MessageLevel.ERROR) {
                    Log.e("web-view",
                            "[error] " + consoleMessage.message());
                    Log.e("web-view",
                            "[error] sourceId = " + consoleMessage.sourceId());
                    Log.e("web-view",
                            "[error] lineNumber = " + consoleMessage.lineNumber());
                } else {
                    Log.e("web-view",
                            consoleMessage.message());
                }
                return super.onConsoleMessage(consoleMessage);
            }

            @Override
            public void onReceivedTitle(MTWebView view, String title) {
                super.onReceivedTitle(view, title);
                if (view.isAttachedToWindow() && !TextUtils.isEmpty(title) && !TextUtils.isEmpty(view.getUrl()) && !view.getUrl().contains(title)) {
                    setNavigationBarTitle(title);
                }
            }

            @Override
            public boolean onShowFileChooser(MTWebView webView, MTValueCallback<Uri[]> filePathCallback, FileChooserParams fileChooserParams) {
                if (android.os.Build.VERSION.SDK_INT >= android.os.Build.VERSION_CODES.LOLLIPOP) {
                    Intent intent = fileChooserParams.createIntent();
                    mFilePathCallback = filePathCallback;
                    msiContext.startActivityForResult(intent, new IActivityResultCallBack() {
                        @Override
                        public void onActivityResult(int resultCode, Intent data) {
                            MSCLog.i(TAG, "startActivityForResult onActivityResult requestCode:", resultCode);
                            MTWebViewComponentManager.this.onActivityResult(resultCode, data);
                        }

                        @Override
                        public void onFail(int errorCode, String errorMsg) {
                            MSCLog.i(TAG, "startActivityForResult onFail errorCode:", errorCode, "errorMsg:", errorMsg);
                        }
                    }); // need permission ?
                    return true;
                }
                return super.onShowFileChooser(webView, filePathCallback, fileChooserParams);
            }

            // For Android  >= 3.0 // not working on 4.4  :(
            @Keep
            public void openFileChooser(final MTValueCallback<Uri> valueCallback, String acceptType) {
                Intent i = new Intent(Intent.ACTION_GET_CONTENT);
                i.addCategory(Intent.CATEGORY_OPENABLE);
                if (TextUtils.isEmpty(acceptType)) {
                    i.setType("*/*");
                } else {
                    i.setType(acceptType);
                }
                mFilePathCallback = new MTValueCallback<Uri[]>() {
                    @Override
                    public void onReceiveValue(Uri[] value) {
                        if (value != null && value.length > 0) {
                            valueCallback.onReceiveValue(value[0]);
                        }
                    }
                };
                msiContext.startActivityForResult(i, new IActivityResultCallBack() {
                    @Override
                    public void onActivityResult(int resultCode, Intent data) {
                        MTWebViewComponentManager.this.onActivityResult(resultCode, data);
                    }

                    @Override
                    public void onFail(int errorCode, String errorMsg) {

                    }
                });
            }

            @Override
            public void onProgressChanged(MTWebView view, int newProgress) {
                if (mPageFinished) {
                    mWebProgressChangedListener.onProgressChanged(100);
                    return;
                }
                mWebProgressChangedListener.onProgressChanged(newProgress);
                super.onProgressChanged(view, newProgress);
            }

            @Override
            public void onPermissionRequest(MTPermissionRequest request) {
                List<String> grantedPermissions = new ArrayList<>();
                Map<String, String> permissionMap = new HashMap<>();
                permissionMap.put(MTPermissionRequest.RESOURCE_VIDEO_CAPTURE, PermissionGuard.PERMISSION_CAMERA);
                permissionMap.put(MTPermissionRequest.RESOURCE_AUDIO_CAPTURE, PermissionGuard.PERMISSION_MICROPHONE);
                for (String permission : request.getResources()) {
                    // 系统权限检查说明 https://km.sankuai.com/page/1221102175
                    if (permissionMap.containsKey(permission) && Privacy.createPermissionGuard().checkPermission(context, permissionMap.get(permission), "__checkOnly") == PermissionGuard.CODE_DENIED_CHECK_ONLY_GRANT) {
                        grantedPermissions.add(permission);
                    }
                }
                if (grantedPermissions.isEmpty()) {
                    request.deny();
                } else {
                    request.grant(grantedPermissions.toArray(new String[0]));
                }
            }
        });

        mWebView.setWebViewClient(new MTWebViewClient() {
            private MTWebResponseBuild webResponseBuild = new MTWebResponseBuild();
            private boolean error = false;

            @Override
            public boolean onRenderProcessGone(MTWebView view, MTRenderProcessGoneDetail detail) {
                RenderProcessGoneHandler.handleRenderProcessGone(
                        view, detail.didCrash(), detail.rendererPriorityAtExit(), TAG + view.getUrl(), mRuntime, null);
                return true;
            }

            @Override
            public void onPageStarted(MTWebView webView, String url, Bitmap bitmap) {
                error = false;
                if (Build.VERSION.SDK_INT >= Build.VERSION_CODES.KITKAT) {
                    webView.evaluateJavascript(JAVASCRIPT_ENVIROMENT, null);
                } else {
                    webView.loadUrl(JAVASCRIPT_ENVIROMENT);
                }

                super.onPageStarted(webView, url, bitmap);
                notifyWebviewStartLoad(url);
            }

            @Override
            public void onPageFinished(MTWebView webView, String url) {
                super.onPageFinished(webView, url);

                if (Build.VERSION.SDK_INT >= Build.VERSION_CODES.KITKAT) {
                    webView.evaluateJavascript(JAVASCRIPT_ENVIROMENT, null);
                } else {
                    webView.loadUrl(JAVASCRIPT_ENVIROMENT);
                }
                loadAssetsJSFileContent(webView);
                if (!error) {
                    notifyOnWebviewFinishLoad(url);
                }

                dispatcherPageState();
            }

            @Override
            public void onReceivedError(MTWebView view, int errorCode, String description, String failingUrl) {
                super.onReceivedError(view, errorCode, description, failingUrl);
                error = true;
                notifyOnWebviewError(errorCode, description, failingUrl);
            }

            @Override
            public boolean shouldOverrideUrlLoading(MTWebView webView, String s) {
                if (MTWebViewComponentManager.this.shouldOverrideUrlLoading(s)) {
                    return true;
                }

                return super.shouldOverrideUrlLoading(webView, s);
            }

            @TargetApi(Build.VERSION_CODES.LOLLIPOP)
            @Override
            public MTWebResourceResponse shouldInterceptRequest(MTWebView view, MTWebResourceRequest request) {
                final String url = request.getUrl().toString();
                MTWebResourceResponse resource = (MTWebResourceResponse) WebViewFileFilter.interceptResource(view.getContext(), mRuntime.getFileModule(), url, webResponseBuild, null, mRuntime.getMSCAppModule().enableAsyncSubPkg());
                return resource != null ? resource : super.shouldInterceptRequest(view, request);
            }

            @Override
            public MTWebResourceResponse shouldInterceptRequest(MTWebView view, String url) {
                MTWebResourceResponse resource = (MTWebResourceResponse) WebViewFileFilter.interceptResource(view.getContext(), mRuntime.getFileModule(), url, webResponseBuild, null, mRuntime.getMSCAppModule().enableAsyncSubPkg());
                return resource != null ? resource : super.shouldInterceptRequest(view, url);
            }

        });
        mWebView.setOnLongClickListener(new View.OnLongClickListener() {
            @Override
            public boolean onLongClick(View v) {
                return handleWebViewLongClickEvent(v);
            }
        });
        return mWebView;
    }

    /**
     * 处理 WebView 的长按事件
     * 单独拿出来是为了简化单测逻辑
     *
     * @param v The view that was clicked and held.
     * @return true if the callback consumed the long click, false otherwise.
     */
    private boolean handleWebViewLongClickEvent(View v) {
        MTWebView.HitTestResult result = ((MTWebView) v).getHitTestResult();
        if (result != null) {
            return WebViewUtil.dealLongClickEvent((Activity) context, mRuntime, result.getType(), result.getExtra());
        }
        return false;
    }

    @Override
    protected boolean loadUrl(String url) {
        if (mWebView == null) {
            return false;
        }

        if (MSCHornRollbackConfig.enableExternalAppStorageLimit() && mRuntime.getMSCAppModule().getExternalApp()) {
            if (!URLUtil.isNetworkUrl(url)) {
                notifyOnWebviewError(WebViewClient.ERROR_BAD_URL, "Unsupported URL For ExternalApp:", url);
                return false;
            }
        }

        mWebView.loadUrl(url);
        return true;
    }

    @Override
    protected String getWebViewUrl() {
        if (mWebView != null) {
            return mWebView.getUrl();
        }
        return null;
    }

    @Override
    public void onActivityResult(int resultCode, Intent data) {
        if (Build.VERSION.SDK_INT >= Build.VERSION_CODES.LOLLIPOP && mFilePathCallback != null) {
            mFilePathCallback.onReceiveValue(MTWebChromeClient.FileChooserParams.parseResult(resultCode, data));
        }
    }


    @Override
    protected INativeLifecycleInterceptor getNativeLifecycleInterceptor() {
        return mNativeLifecycleInterceptor;
    }

    @Override
    public long getWebViewComponentInitializationDuration() {
        return mMTWebViewComponentInitializationDuration;
    }
}