package com.meituan.msc.modules.api.msi.webview;

import static com.meituan.msc.modules.api.AbsApi.codeJson;

import android.os.Handler;
import android.os.Looper;
import android.support.annotation.Keep;
import android.text.TextUtils;
import android.webkit.JavascriptInterface;

import com.google.gson.Gson;
import com.google.gson.JsonSyntaxException;
import com.google.gson.reflect.TypeToken;
import com.meituan.msc.modules.api.AbsApi;
import com.meituan.msc.modules.engine.MSCHornRollbackConfig;
import com.meituan.msc.modules.reporter.MSCLog;
import com.meituan.msi.api.ApiCallback;

import org.json.JSONException;
import org.json.JSONObject;

import java.util.ArrayList;

@Keep
public
class WebJSBridge {
    private final BaseWebViewComponentManager.WebViewApiInvokeListener mApiInvokeListener;
    private final IWebNativeToJsBridge mWebNativeToJsBridge;
//    private static final String[] mWebViewComponentSupportApi = new String[]{
//            "navigateTo", "navigateBack", "switchTab", "reLaunch", "redirectTo"};
    String mHtmlId;
    int mPageId;
    public static final String MSC_WEB_JS_BRIDGE = "WebJSBridge";

    public WebJSBridge(BaseWebViewComponentManager.WebViewApiInvokeListener mApiInvokeListener,
                       IWebNativeToJsBridge webNative2JsBridge,
                       String htmlId,
                       int pageId) {
        this.mApiInvokeListener = mApiInvokeListener;
        this.mWebNativeToJsBridge = webNative2JsBridge;
        this.mHtmlId = htmlId;
        this.mPageId = pageId;
    }

    Handler handler = new Handler(Looper.getMainLooper());

    @JavascriptInterface
    public void _sendMessage(final String msg) {
        handler.post(new Runnable() {
            @Override
            public void run() {
                _sendMessage_main(msg);
            }
        });
    }

    public void _sendMessage_main(String msg) {
        ArrayList<String> arrayList = null;
        try {
            arrayList = new Gson().fromJson(msg, new TypeToken<ArrayList<String>>() {
            }.getType());
        } catch (JsonSyntaxException e) {
            MSCLog.i(MSC_WEB_JS_BRIDGE, e.toString());
            return;
        }
        if (arrayList == null) {
            MSCLog.i(MSC_WEB_JS_BRIDGE, "arrayList is null");
            return;
        }

        for (int i = 0; i < arrayList.size(); i++) {
            String eventStr = arrayList.get(i);
            if (TextUtils.isEmpty(eventStr)) {
                MSCLog.i(MSC_WEB_JS_BRIDGE, "eventStr is null!");
                continue;
            }
            WebJSMessageEvent messageEvent = new Gson().fromJson(eventStr, WebJSMessageEvent.class);
            if (messageEvent == null) {
                MSCLog.i(MSC_WEB_JS_BRIDGE, "messageEvent is null!");
                continue;
            }

            String func = messageEvent.func;
            String callbackId = messageEvent.callbackId;
            String name = messageEvent.name;
            if ("invokeMiniProgramAPI".equals(func)) {
                if ("postMessage".equals(name)) {
                    if (mWebNativeToJsBridge != null) {
                        OnWebViewPostMessageEvent event = new OnWebViewPostMessageEvent();
                        Object args = messageEvent.args;
                        if (args == null) {
                            event.data = "";
                        } else {
                            event.data = args;
                        }
                        event.pageId = mPageId;
                        event.id = mHtmlId;
                        mWebNativeToJsBridge.dispatchEvent(WebViewComponentApi.MSC_WEB_VIEW_ON_MESSAGE, event, mPageId, mHtmlId);
                    } else {
                        MSCLog.i(MSC_WEB_JS_BRIDGE, "mWebNativeToJsBridge is null!");
                    }
                    return;
                }

                mApiInvokeListener.invoke(eventStr, new ApiCallback<String>() {
                    @Override
                    public void onSuccess(String data) {
                        callbackForWx(callbackId, data);
                    }

                    @Override
                    public void onFail(String error) {
                        callbackForWx(callbackId, error);
                    }
                });
            } else {
                //给出失败回调
                callbackForWx(callbackId,
                        AbsApi.assembleResult(
                                codeJson(-1, "not supported"),
                                TextUtils.isEmpty(name) ? func : name,
                                "fail"));
            }
        }

    }

//    /**
//     * 校验api是否支持
//     * @return
//     */
//    private boolean validApi(String name) {
//        //service api 白名单的才支持， activity api都可以支持
//        if (mWebViewComponentSupportApi != null && mWebViewComponentSupportApi.length > 0) {
//            for (String api : mWebViewComponentSupportApi) {
//                if (api.equals(name)) {
//                    return true;
//                }
//            }
//        }
//        return false;
//    }

    /**
     * Todo 暂不支持回调结果，后续支持
     * @param callbackId
     * @param result
     */
    private void callbackForWx(String callbackId, String result) {
        try {
            JSONObject jsonObject = new JSONObject();
            jsonObject.put("__msg_type", "callback");
            jsonObject.put("__callback_id", callbackId);
            jsonObject.put("__params", new JSONObject(result));

            if (MSCHornRollbackConfig.enableWebViewMainThread()) {
                handler.post(new Runnable() {
                    @Override
                    public void run() {
                        if (mWebNativeToJsBridge != null) {
                            mWebNativeToJsBridge.evaluateJavascript("javascript:WeixinJSBridge._handleMessageFromWeixin(" + jsonObject.toString() + ")");
                        }
                    }
                });
            } else if (mWebNativeToJsBridge != null) {
                mWebNativeToJsBridge.evaluateJavascript("javascript:WeixinJSBridge._handleMessageFromWeixin(" + jsonObject.toString() + ")");
            }
        } catch (JSONException e) {
            MSCLog.e("WebJSBridge callbackForWx", e);
        }
    }

    /**
     * 同web-view内部注入本地js文件逻辑通信
     * @param eventId
     * @param params
     * @param native2JsBridge
     */
    public static void dispatchEvent(String eventId, String params, IWebNativeToJsBridge native2JsBridge) {
        try {
            JSONObject jsonObject = new JSONObject();
            jsonObject.put("__msg_type", "event");
            jsonObject.put("__event_id", eventId);
            jsonObject.put("__params", new JSONObject(params));

            if (native2JsBridge != null) {
                native2JsBridge.evaluateJavascript("javascript:WeixinJSBridge._handleMessageFromWeixin(" + jsonObject.toString() + ")");
            }

        } catch (JSONException e) {
            MSCLog.e("WebJSBridge dispatcherEvent", e);
        }
    }
}
