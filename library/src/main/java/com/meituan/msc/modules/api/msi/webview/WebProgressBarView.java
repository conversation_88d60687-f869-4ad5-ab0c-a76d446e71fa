package com.meituan.msc.modules.api.msi.webview;

import android.animation.Animator;
import android.animation.ValueAnimator;
import android.content.Context;
import android.content.res.TypedArray;
import android.graphics.Canvas;
import android.graphics.Paint;
import android.view.View;
import android.view.animation.AlphaAnimation;
import android.view.animation.Animation;
import android.view.animation.AnimationSet;

import com.meituan.msc.common.utils.ScreenUtil;
import com.meituan.msc.lib.R;

public class WebProgressBarView extends View {

    private Context mContext;
    private int mCurProgress;
    private int mWidth;
    private int mHeight;
    private Paint mPaint;
    private int mColor;

    public WebProgressBarView(Context context) {
        super(context);
        mContext = context;

        TypedArray array = context.obtainStyledAttributes(R.styleable.webViewProgressBar);
        mCurProgress = array.getInt(R.styleable.webViewProgressBar_WebViewProgress, 0);
        mHeight = array.getInt(R.styleable.webViewProgressBar_WebViewProgressHeight, ScreenUtil.dp2px(2));
        mColor = array.getColor(R.styleable.webViewProgressBar_WebViewProgressColor, getResources().getColor(R.color.msc_web_progress));
        array.recycle();

        mPaint = new Paint();
        mPaint.setColor(mColor);

        setVisibility(GONE);
    }


    @Override
    protected void onMeasure(int widthMeasureSpec, int heightMeasureSpec) {
        super.onMeasure(widthMeasureSpec, heightMeasureSpec);
        mWidth = MeasureSpec.getSize(widthMeasureSpec);
    }


    @Override
    protected void onDraw(Canvas canvas) {
        super.onDraw(canvas);

        float result = mWidth * ((float) mCurProgress / (float) 100);
        canvas.drawRect(0, 0, result, mHeight, mPaint);
    }

    public void setCurProgress(int newProgress, final EventEndListener endListener) {
        ValueAnimator animator = ValueAnimator.ofInt(mCurProgress, newProgress);
        animator.setDuration(10 * (newProgress - mCurProgress)); // 整个进度条用1s加载完成，任意一段按照比例来划分
        animator.addUpdateListener(new ValueAnimator.AnimatorUpdateListener() {
            @Override
            public void onAnimationUpdate(ValueAnimator animation) {
                setNormalProgress((Integer) animation.getAnimatedValue());
            }
        });
        animator.addListener(new Animator.AnimatorListener() {
            @Override
            public void onAnimationStart(Animator animation) {

            }

            @Override
            public void onAnimationEnd(Animator animation) {
                if (endListener != null) {
                    endListener.onEndEvent();
                }
            }

            @Override
            public void onAnimationCancel(Animator animation) {

            }

            @Override
            public void onAnimationRepeat(Animator animation) {

            }
        });

        animator.start();

    }

    public void setNormalProgress(int mCurProgress) {
        this.mCurProgress = mCurProgress;
        postInvalidate();
    }

    public void hideProgress() {
        AnimationSet animation = getDismissAnim();
        animation.setAnimationListener(new Animation.AnimationListener() {
            @Override
            public void onAnimationStart(Animation animation) {
            }

            @Override
            public void onAnimationEnd(Animation animation) {
                mCurProgress = 0;
                setVisibility(View.GONE);
            }

            @Override
            public void onAnimationRepeat(Animation animation) {
            }
        });
        this.startAnimation(animation);
    }

    private AnimationSet getDismissAnim() {
        AnimationSet dismiss = new AnimationSet(mContext, null);
        AlphaAnimation alpha = new AlphaAnimation(1.0f, 0.0f);
        alpha.setDuration(1000); // 用一秒时间渐变消失，目测和微信比较像
        dismiss.addAnimation(alpha);
        return dismiss;
    }

    public int getCurProgress() {
        return mCurProgress;
    }

    public interface EventEndListener {
        void onEndEvent();
    }

}
