package com.meituan.msc.modules.api.msi.webview;

import android.view.View;
import android.webkit.WebView;

public class WebProgressChangedListener {

    protected WebProgressBarView mProgressBar;


    public WebProgressChangedListener setProgressBar(WebProgressBarView mProgressBar) {
        this.mProgressBar = mProgressBar;
        return this;
    }

    public void onProgressChanged(int newProgress) {
        if (mProgressBar == null || newProgress <= mProgressBar.getCurProgress()) { // WebView中会出现两轮页面加载，微信进度条只和第一次加载有关
            return;
        }
        if (View.GONE == mProgressBar.getVisibility()) {
            mProgressBar.setVisibility(View.VISIBLE);
        }
        mProgressBar.setCurProgress(newProgress, new WebProgressBarView.EventEndListener() {
            @Override
            public void onEndEvent() {
                if (mProgressBar.getVisibility() == View.VISIBLE && mProgressBar.getCurProgress() == 100) {
                    mProgressBar.hideProgress();
                }
            }
        });
    }
}
