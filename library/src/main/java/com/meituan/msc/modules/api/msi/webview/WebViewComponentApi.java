package com.meituan.msc.modules.api.msi.webview;

import android.view.ViewGroup;

import com.google.gson.JsonObject;
import com.meituan.msc.modules.api.msi.MSCErrorCode;
import com.meituan.msc.modules.api.msi.MSCNativeViewApi;
import com.meituan.msc.modules.page.IPageModule;
import com.meituan.msc.modules.page.IWebViewComponentInfo;
import com.meituan.msc.modules.reporter.MSCLog;
import com.meituan.msi.annotations.MsiApiEnv;
import com.meituan.msi.annotations.MsiApiMethod;
import com.meituan.msi.api.IMsiApi;
import com.meituan.msi.api.MSIError;
import com.meituan.msi.bean.ContainerInfo;
import com.meituan.msi.bean.EventType;
import com.meituan.msi.bean.MsiContext;
import com.sankuai.meituan.serviceloader.annotation.ServiceLoaderInterface;


/**
 * web-view 组件Api能力
 * https://km.sankuai.com/collabpage/1437033005
 * <AUTHOR>
 */
@MsiApiEnv(name = ContainerInfo.ENV_MSC)
@ServiceLoaderInterface(key = "msc_component_web_view", interfaceClass = IMsiApi.class)
public class WebViewComponentApi extends MSCNativeViewApi<WebViewComponentWrapper, WebViewComponentParam> {
    //不允许在WebView组件中继续插入WebView组件
    private static final int ERROR_CODE_WEBVIEW_CANNOT_EMBED_WEBVIEW = 800000200;

    public static final String MSC_WEB_VIEW_ON_MESSAGE = "onWebViewPostMessage";
    public static final String MSC_WEB_VIEW_ON_ERROR = "onWebviewError";
    public static final String MSC_WEB_VIEW_ON_START_LOAD = "onWebviewStartLoad";
    public static final String MSC_WEB_VIEW_ON_FINISH_LOAD = "onWebviewFinishLoad";


    @MsiApiMethod(name = "webView", request = WebViewComponentParam.class, onUiThread = true)
    public void beforeOperation(WebViewComponentParam param, MsiContext msiContext) {
        handleViewOperation(msiContext, param);
    }

    @Override
    protected WebViewComponentWrapper doCreateCoverView(MsiContext context, JsonObject uiParams, WebViewComponentParam componentParam) {
        //判断当前是WebView，不能添加coverView
        int pageId = context.getPageId();
        IPageModule pageModule = getPageById(pageId);
        if (pageModule == null) {
            String errMsg = "can't insert web-view pageModule is null";
            MSCLog.i(errMsg);
            context.onError(errMsg, MSIError.getIgnoreError(MSCErrorCode.ERROR_CODE_API_COMMON_MEET_EXPECTATIONS));
            return null;
        }
        IWebViewComponentInfo webViewComponent = pageModule.getWebViewComponent();
        if (webViewComponent.hasInnerWebViewComponent()) {
            //不允许在WebView组件中继续插入WebView组件
            String errMsg = "can't insert web-view in web-view";
            MSCLog.i(errMsg);
            context.onError(errMsg, MSIError.getGeneralError(ERROR_CODE_WEBVIEW_CANNOT_EMBED_WEBVIEW));
            return null;
        }
        //web-view组件全屏, 宽高设置为匹配父布局
        JsonObject position = uiParams.getAsJsonObject("position");
        position.addProperty("height", ViewGroup.LayoutParams.MATCH_PARENT);
        position.addProperty("width", ViewGroup.LayoutParams.MATCH_PARENT);
        uiParams.add("position", position);
        return new WebViewComponentWrapper(getRuntime(), context, uiParams, componentParam, webViewComponent);
    }

    @Override
    protected boolean updateCoverView(MsiContext msiContext, WebViewComponentWrapper view, int pageId, int viewId, JsonObject uiParams, WebViewComponentParam componentParam) {
        if (view != null) {
            getRuntime().getRuntimeReporter().reportWebViewUrl("", componentParam.src);
            return view.loadUrl(componentParam.src);
        }
        return false;
    }

    @MsiApiMethod(name = "onWebViewPostMessage", isCallback = true, eventType = EventType.VIEW_EVENT)
    public void onWebViewPostMessage(MsiContext context) {

    }


    @MsiApiMethod(name = "onWebviewError", isCallback = true, eventType = EventType.VIEW_EVENT)
    public void onWebviewError(MsiContext context) {

    }

    @MsiApiMethod(name = "onWebviewStartLoad", isCallback = true, eventType = EventType.VIEW_EVENT)
    public void onWebviewStartLoad(MsiContext context) {

    }

    @MsiApiMethod(name = "onWebviewFinishLoad", isCallback = true, eventType = EventType.VIEW_EVENT)
    public void onWebviewFinishLoad(MsiContext context) {

    }
}
