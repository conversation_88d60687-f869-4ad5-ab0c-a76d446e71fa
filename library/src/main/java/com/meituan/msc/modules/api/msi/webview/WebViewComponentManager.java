package com.meituan.msc.modules.api.msi.webview;

import android.annotation.TargetApi;
import android.app.Activity;
import android.content.Context;
import android.content.Intent;
import android.graphics.Bitmap;
import android.net.Uri;
import android.os.Build;
import android.os.SystemClock;
import android.support.annotation.Keep;
import android.support.annotation.Nullable;
import android.text.TextUtils;
import android.util.Log;
import android.view.View;
import android.webkit.ConsoleMessage;
import android.webkit.PermissionRequest;
import android.webkit.RenderProcessGoneDetail;
import android.webkit.ValueCallback;
import android.webkit.WebChromeClient;
import android.webkit.WebResourceRequest;
import android.webkit.WebResourceResponse;
import android.webkit.WebSettings;
import android.webkit.WebView;
import android.webkit.WebViewClient;

import com.google.gson.JsonObject;
import com.meituan.android.privacy.interfaces.PermissionGuard;
import com.meituan.android.privacy.interfaces.Privacy;
import com.meituan.msc.common.resource.DefaultWebResponseBuild;
import com.meituan.msc.common.utils.CIPStorageFileUtil;
import com.meituan.msc.common.utils.ViewUtils;
import com.meituan.msc.extern.MSCEnvHelper;
import com.meituan.msc.modules.api.RenderProcessGoneHandler;
import com.meituan.msc.modules.api.web.WebViewUtil;
import com.meituan.msc.modules.engine.MSCHornRollbackConfig;
import com.meituan.msc.modules.engine.MSCRuntime;
import com.meituan.msc.modules.page.IWebViewComponentInfo;
import com.meituan.msc.modules.page.render.webview.WebViewFileFilter;
import com.meituan.msc.modules.reporter.MSCLog;
import com.meituan.msi.bean.MsiContext;
import com.meituan.msi.context.IActivityResultCallBack;
import com.meituan.msi.view.INativeLifecycleInterceptor;

import java.util.ArrayList;
import java.util.HashMap;
import java.util.List;
import java.util.Map;

/**
 * 系统webview 组件module
 */
public class WebViewComponentManager extends BaseWebViewComponentManager {

    public static final String TAG = "WebViewComponentManager";

    ComponentWebView mWebView;
    private long mWebViewComponentInitializationDuration = 0;

    public WebViewComponentManager(Context context, MSCRuntime runtime, IWebViewComponentInfo webViewComponent) {
        super(context, runtime, webViewComponent);
    }

    @Override
    @SuppressWarnings("AddJavascriptInterface")
    protected View createWebView(MsiContext msiContext, JsonObject uiParams, WebViewComponentParam params, IWebViewComponentInfo webViewComponent) {
        long start = SystemClock.elapsedRealtime();
        mWebView = new ComponentWebView(msiContext.getActivity(), webViewComponent.getWebFocusDispatcher(), msiContext.getEventDispatcher());
        mWebViewComponentInitializationDuration = SystemClock.elapsedRealtime() - start;
        mNativeLifecycleInterceptor = mWebView;
        setWebNative2JsBridge(mWebView);

        WebSettings webSettings = mWebView.getSettings();

        webSettings.setAllowFileAccess(true);
        if (MSCHornRollbackConfig.readConfig().enableFixFileAccessSecurity) {
            webSettings.setAllowFileAccessFromFileURLs(false);
            webSettings.setAllowUniversalAccessFromFileURLs(false);
            webSettings.setJavaScriptEnabled(TextUtils.isEmpty(params.src) || !params.src.startsWith("file://"));
        } else {
            webSettings.setJavaScriptEnabled(true);
        }
        //  webSettings.setDefaultFontSize(1);
        webSettings.setJavaScriptCanOpenWindowsAutomatically(true);

        // ========================
        webSettings.setDomStorageEnabled(true);
        webSettings.setBuiltInZoomControls(false);
        webSettings.setUseWideViewPort(true);
        webSettings.setLoadWithOverviewMode(true);
        webSettings.setSaveFormData(false);
        webSettings.setGeolocationEnabled(true);
        webSettings.setJavaScriptCanOpenWindowsAutomatically(true);
        if (Build.VERSION.SDK_INT >= Build.VERSION_CODES.LOLLIPOP) {
            webSettings.setMixedContentMode(0);
        }
        webSettings.setDatabaseEnabled(true);
        webSettings.setDatabasePath(CIPStorageFileUtil.getFilesDir(msiContext.getActivity(), "databases").getAbsolutePath());
        webSettings.setAppCacheMaxSize(10 * 1024 * 1024);
        webSettings.setAppCachePath(CIPStorageFileUtil.getFilesDir(msiContext.getActivity(), "webviewcache").getAbsolutePath());

        if (Build.VERSION.SDK_INT > Build.VERSION_CODES.ICE_CREAM_SANDWICH) {
            webSettings.setTextZoom(100);
        }

        try {
            webSettings.setMediaPlaybackRequiresUserGesture(false);
        } catch (Exception e) {
            //ignore
        }
        String msc_ua_append = params.msc_ua_append;
        String ua = getUserAgent(msc_ua_append);
        webSettings.setUserAgentString(ua);
        try {
            webSettings.setMediaPlaybackRequiresUserGesture(false);
        } catch (Exception e) {
            //ignore
        }


        mWebView.addJavascriptInterface(
                new WebJSBridge(new WebViewApiInvokeListener(), mWebView, params.htmlId, msiContext.getPageId()),
                getJSBrigeName());
        mWebView.evaluateJavascript("javascript:window.__webviewEnv = 'msc';", null);

        mWebView.setWebChromeClient(new WebChromeClient() {

            // WebView video组件默认图兜底（问题修复：https://stackoverflow.com/questions/76700882/nullpointer-exception-on-bitmap-getwidth-at-chromium-trichromewebviewgoogle-aa）
            @Override
            public Bitmap getDefaultVideoPoster() {
                return ViewUtils.getDefaultVideoPoster(super.getDefaultVideoPoster());
            }

            @Override
            public boolean onConsoleMessage(ConsoleMessage consoleMessage) {
                if (consoleMessage.messageLevel() == ConsoleMessage.MessageLevel.ERROR) {
                    Log.e("web-view",
                            "[error] " + consoleMessage.message());
                    Log.e("web-view",
                            "[error] sourceId = " + consoleMessage.sourceId());
                    Log.e("web-view",
                            "[error] lineNumber = " + consoleMessage.lineNumber());
                } else {
                    Log.e("web-view",
                            consoleMessage.message());
                }
                return super.onConsoleMessage(consoleMessage);
            }

            @Override
            public void onReceivedTitle(WebView view, String title) {
                super.onReceivedTitle(view, title);
                if (view.isAttachedToWindow() && !TextUtils.isEmpty(title) && !TextUtils.isEmpty(view.getUrl()) && !view.getUrl().contains(title)) {
                    setNavigationBarTitle(title);
                }
            }

            @Override
            public boolean onShowFileChooser(WebView webView, ValueCallback<Uri[]> filePathCallback, FileChooserParams fileChooserParams) {
                Intent intent = fileChooserParams.createIntent();
                mFilePathCallback = filePathCallback;
                msiContext.startActivityForResult(intent, new IActivityResultCallBack() {
                    @Override
                    public void onActivityResult(int resultCode, Intent data) {
                        MSCLog.i(TAG, "startActivityForResult onActivityResult requestCode:", resultCode);
                        WebViewComponentManager.this.onActivityResult(resultCode, data);
                    }

                    @Override
                    public void onFail(int errorCode, String errorMsg) {
                        MSCLog.i(TAG, "startActivityForResult onFail errorCode:", errorCode, "errorMsg:", errorMsg);
                    }
                }); // need permission ?
                return true;
            }

            @Override
            public void onProgressChanged(WebView view, int newProgress) {
                if (mPageFinished) {
                    mWebProgressChangedListener.onProgressChanged(100);
                    return;
                }
                mWebProgressChangedListener.onProgressChanged(newProgress);
                super.onProgressChanged(view, newProgress);
            }

            @Override
            public void onPermissionRequest(PermissionRequest request) {
                List<String> grantedPermissions = new ArrayList<>();
                Map<String, String> permissionMap = new HashMap<>();
                permissionMap.put(PermissionRequest.RESOURCE_VIDEO_CAPTURE, PermissionGuard.PERMISSION_CAMERA);
                permissionMap.put(PermissionRequest.RESOURCE_AUDIO_CAPTURE, PermissionGuard.PERMISSION_MICROPHONE);
                for (String permission : request.getResources()) {
                    // 系统权限检查说明 https://km.sankuai.com/page/1221102175
                    if (permissionMap.containsKey(permission) && Privacy.createPermissionGuard().checkPermission(context, permissionMap.get(permission), "__checkOnly") == PermissionGuard.CODE_DENIED_CHECK_ONLY_GRANT) {
                        grantedPermissions.add(permission);
                    }
                }
                if (grantedPermissions.isEmpty()) {
                    request.deny();
                } else {
                    request.grant(grantedPermissions.toArray(new String[0]));
                }
            }
        });

        mWebView.setWebViewClient(new WebViewClient() {
            @Override
            public boolean onRenderProcessGone(WebView view, RenderProcessGoneDetail detail) {
                RenderProcessGoneHandler.handleRenderProcessGone(
                        view, detail, TAG + view.getUrl(), mRuntime, null);
                return true;
            }

            private DefaultWebResponseBuild webResponseBuild = new DefaultWebResponseBuild();
            private boolean error = false;

            @Override
            public void onPageStarted(WebView webView, String url, Bitmap bitmap) {
                error = false;
                if (Build.VERSION.SDK_INT >= Build.VERSION_CODES.KITKAT) {
                    webView.evaluateJavascript(JAVASCRIPT_ENVIROMENT, null);
                } else {
                    webView.loadUrl(JAVASCRIPT_ENVIROMENT);
                }
                super.onPageStarted(webView, url, bitmap);
                notifyWebviewStartLoad(url);
            }

            @Override
            public void onPageFinished(WebView webView, String url) {
                super.onPageFinished(webView, url);
                mPageFinished = true;

                if (Build.VERSION.SDK_INT >= Build.VERSION_CODES.KITKAT) {
                    webView.evaluateJavascript(JAVASCRIPT_ENVIROMENT, null);
                } else {
                    webView.loadUrl(JAVASCRIPT_ENVIROMENT);
                }
                loadAssetsJSFileContent(webView);
                if (!error) {
                    notifyOnWebviewFinishLoad(url);
                }
                dispatcherPageState();
            }

            @Override
            public void onReceivedError(WebView view, int errorCode, String description, String failingUrl) {
                super.onReceivedError(view, errorCode, description, failingUrl);
                error = true;
                notifyOnWebviewError(errorCode, description, failingUrl);
            }

            @Override
            public boolean shouldOverrideUrlLoading(WebView webView, String s) {
                if (WebViewComponentManager.this.shouldOverrideUrlLoading(s)) {
                    return true;
                }
                return super.shouldOverrideUrlLoading(webView, s);
            }

            @TargetApi(Build.VERSION_CODES.LOLLIPOP)
            @Override
            public WebResourceResponse shouldInterceptRequest(WebView view, WebResourceRequest request) {
                final String url = request.getUrl().toString();
                WebResourceResponse resource = (WebResourceResponse) WebViewFileFilter.interceptResource(view.getContext(), mRuntime.getFileModule(), url, webResponseBuild, null, mRuntime.getMSCAppModule().enableAsyncSubPkg());
                return resource != null ? resource : super.shouldInterceptRequest(view, request);
            }

            @Override
            public WebResourceResponse shouldInterceptRequest(WebView view, String url) {
                WebResourceResponse resource = (WebResourceResponse) WebViewFileFilter.interceptResource(view.getContext(), mRuntime.getFileModule(), url, webResponseBuild, null, mRuntime.getMSCAppModule().enableAsyncSubPkg());
                return resource != null ? resource : super.shouldInterceptRequest(view, url);
            }
        });

        mWebView.setDownloadListener((String url, String userAgent, String contentDisposition, String mimetype, long contentLength) -> {
            if (MSCEnvHelper.getWebViewDownloadListener() != null) {
                MSCEnvHelper.getWebViewDownloadListener().onDownloadStart(url, userAgent, contentDisposition, mimetype, contentLength);
            }
        });
        mWebView.setOnLongClickListener(new View.OnLongClickListener() {
            @Override
            public boolean onLongClick(View v) {

                WebView.HitTestResult result = ((WebView) v).getHitTestResult();
                if (result != null) {
                    return WebViewUtil.dealLongClickEvent((Activity) context, mRuntime, result.getType(), result.getExtra());
                }
                return false;
            }
        });
//        wrapper.setWebView(webView, webView);
        return mWebView;
    }


    @Override
    protected INativeLifecycleInterceptor getNativeLifecycleInterceptor() {
        return mNativeLifecycleInterceptor;
    }

    @Override
    protected boolean loadUrl(String url) {
        if (mWebView != null) {
            mWebView.loadUrl(url);
            return true;
        }
        return false;
    }

    @Nullable
    @Override
    protected String getWebViewUrl() {
        if (mWebView != null) {
            return mWebView.getUrl();
        }
        return null;
    }

    @Override
    public void onActivityResult(int resultCode, Intent data) {
        if (Build.VERSION.SDK_INT >= Build.VERSION_CODES.LOLLIPOP && mFilePathCallback != null) {
            mFilePathCallback.onReceiveValue(WebChromeClient.FileChooserParams.parseResult(resultCode, data));
        }
    }

    @Override
    public long getWebViewComponentInitializationDuration() {
        return mWebViewComponentInitializationDuration;
    }
}
