package com.meituan.msc.modules.api.msi.webview;

import android.os.SystemClock;
import android.view.View;
import android.view.ViewGroup;
import android.widget.FrameLayout;

import com.google.gson.JsonObject;
import com.meituan.msc.common.utils.ScreenUtil;
import com.meituan.msc.modules.engine.MSCRuntime;
import com.meituan.msc.modules.page.IWebViewComponentInfo;
import com.meituan.msc.modules.page.render.webview.MSCWebView;
import com.meituan.msc.modules.page.render.webview.WebViewCacheManager;
import com.meituan.msc.modules.reporter.CommonTags;
import com.meituan.msc.modules.reporter.WebViewInitReporter;
import com.meituan.msi.bean.MsiContext;
import com.meituan.msi.view.INativeLifecycleInterceptor;
import com.meituan.mtwebkit.MTWebView;
import com.meituan.mtwebkit.internal.MTWebViewConstants;

/**
 * web-view组件包装类
 */
public class WebViewComponentWrapper extends FrameLayout implements INativeLifecycleInterceptor {
    private View mWebView;
    protected WebProgressBarView mProgressBar;
    private INativeLifecycleInterceptor mNativeLifecycleInterceptor = null;

    BaseWebViewComponentManager mWebViewManager;

    public WebViewComponentWrapper(MSCRuntime runtime, MsiContext context, JsonObject uiParams,
                                   WebViewComponentParam componentParam, IWebViewComponentInfo webViewComponent) {
        super(context.getActivity());

        long start = SystemClock.elapsedRealtime();
        //web-view组件使用系统 WebView 还是 MTWebView
        boolean useMtWebView = runtime.webViewCacheManager.useMtWebViewByAppId(runtime.getAppId());
        if (useMtWebView) {
            mWebViewManager = new MTWebViewComponentManager(context.getActivity(), runtime, webViewComponent);
        } else {
            mWebViewManager = new WebViewComponentManager(context.getActivity(), runtime, webViewComponent);
        }
        View webView = mWebViewManager.createView(context, uiParams, componentParam);
        setWebView(webView, mWebViewManager.getNativeLifecycleInterceptor());

        reportWebViewInitTrack(runtime, useMtWebView, webView, start);
    }

    /**
     * 上报 webView 初始化埋点
     * @param useMtWebView  是否使用MTWebView
     * @param webView       WebView对象
     * @param start         开始时间戳
     */
    private void reportWebViewInitTrack(MSCRuntime runtime, boolean useMtWebView, View webView, long start) {
        WebViewCacheManager.WebViewType webViewType;
        if (useMtWebView) {
            if (MTWebViewConstants.TYPE_MTWEBVIEW_MT.equals(((MTWebView) webView).getMTWebViewType())) {
                webViewType = WebViewCacheManager.WebViewType.MT_WEB_VIEW;
            } else {
                webViewType = WebViewCacheManager.WebViewType.MT_WEB_VIEW_SYSTEM;
            }
        } else {
            webViewType = WebViewCacheManager.WebViewType.CHROME;
        }
        long duration = mWebViewManager == null ? -1 : mWebViewManager.getWebViewComponentInitializationDuration();
        new WebViewInitReporter(CommonTags.getAppId(runtime)).report(webViewType, duration, MSCWebView.SourceType.COMPONENT.toString(), start);
    }

    boolean loadUrl(String url) {
        return mWebViewManager.loadUrl(url);
    }

    protected void setWebView(View webView, INativeLifecycleInterceptor nativeLifecycleInterceptor) {
        mWebView = webView;
        LayoutParams params = new LayoutParams(ViewGroup.LayoutParams.MATCH_PARENT, ViewGroup.LayoutParams.MATCH_PARENT);
        addView(mWebView, params);
        mProgressBar = new WebProgressBarView(getContext());
        params = new LayoutParams(ViewGroup.LayoutParams.MATCH_PARENT, ScreenUtil.dp2px(4));
        addView(mProgressBar, params);
        mNativeLifecycleInterceptor = nativeLifecycleInterceptor;
        mWebViewManager.setWebProgressChangedListener(new WebProgressChangedListener().setProgressBar(mProgressBar));
    }

    public View getWebView() {
        return mWebView;
    }

    @Override
    public boolean onSystemDialogClose(String cause) {
        if (mNativeLifecycleInterceptor != null) {
            return mNativeLifecycleInterceptor.onSystemDialogClose(cause);
        }
        return false;
    }

    @Override
    public boolean onBackPressed() {
        if (mNativeLifecycleInterceptor != null) {
            return mNativeLifecycleInterceptor.onBackPressed();
        }
        return false;
    }

    @Override
    public void onPagePaused(int cause) {
        if (mNativeLifecycleInterceptor != null) {
            mNativeLifecycleInterceptor.onPagePaused(cause);
        }
    }

    @Override
    public void onPageResume() {
        if (mNativeLifecycleInterceptor != null) {
            mNativeLifecycleInterceptor.onPageResume();
        }
    }

    @Override
    public boolean isPipMode() {
        if (mNativeLifecycleInterceptor != null) {
            return mNativeLifecycleInterceptor.isPipMode();
        }
        return false;
    }


    public void destroy() {
        ((IComponentWebView) mWebView).destroy();
    }

    public void evaluateJavascript(String script) {
        ((IWebNativeToJsBridge) mWebView).evaluateJavascript(script);
    }

    @Override
    public void scrollBy(int x, int y) {
        mWebView.scrollBy(x, y);
    }


    public boolean tryGoBack() {
        return ((IComponentWebView) mWebView).tryGoBack();
    }


    public int getWebHeight() {
        return mWebView.getHeight();
    }


    public String getUrl() {
        return ((IComponentWebView) mWebView).getUrl();
    }

}
