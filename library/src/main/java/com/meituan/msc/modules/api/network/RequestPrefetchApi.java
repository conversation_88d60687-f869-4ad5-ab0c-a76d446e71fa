package com.meituan.msc.modules.api.network;

import com.meituan.msc.common.framework.Callback;
import com.meituan.msc.extern.MSCEnvHelper;
import com.meituan.msc.modules.api.msi.MSCApi;
import com.meituan.msc.modules.api.msi.MSCErrorCode;
import com.meituan.msc.modules.api.storage.StorageModule;
import com.meituan.msc.modules.engine.requestPrefetch.RequestPrefetchManager;
import com.meituan.msc.modules.update.MSCAppModule;
import com.meituan.msi.annotations.MsiApiEnv;
import com.meituan.msi.annotations.MsiApiMethod;
import com.meituan.msi.api.IMsiApi;
import com.meituan.msi.api.MSIError;
import com.meituan.msi.bean.ContainerInfo;
import com.meituan.msi.bean.MsiContext;
import com.sankuai.meituan.serviceloader.annotation.ServiceLoaderInterface;


@ServiceLoaderInterface(key = "msc_requestPrefetch", interfaceClass = IMsiApi.class)
@MsiApiEnv(name = ContainerInfo.ENV_MSC)
public class RequestPrefetchApi extends MSCApi {
    private static final int ERROR_CODE_BACKGROUND_FETCH_DATA_FAIL = 800000201;

    @MsiApiMethod(name = "setBackgroundFetchToken", request = FetchTokenParam.class)
    public void setBackgroundFetchToken(FetchTokenParam param, MsiContext context) {
        String spName = RequestPrefetchModule.getPrefetchSpName(getRuntime().getMSCAppModule().getAppId());
        MSCEnvHelper.getSharedPreferences(spName)
                .edit()
                .putString(RequestPrefetchModule.SP_KEY_REQUEST_PREFETCH_TOKEN, param.token)
                .apply();
        context.onSuccess(null);
    }


    //https://km.sankuai.com/page/1304067328#id-1%E3%80%81wx.getBackgroundFetchData
    //https://developers.weixin.qq.com/miniprogram/dev/api/storage/background-fetch/wx.getBackgroundFetchData.html
    @MsiApiMethod(name = "getBackgroundFetchData", request = FetchDataParam.class, response = FetchTokenResponse.class)
    public void getBackgroundFetchData(FetchDataParam fetchDataParam, MsiContext context) {
        if (RequestPrefetchModule.FETCH_DATA_TYPE_PRE.equals(fetchDataParam.fetchType)) {
            getRuntime().getRequestPrefetchManager().getData(new Callback<FetchTokenResponse>() {
                @Override
                public void onSuccess(FetchTokenResponse data) {
                    context.onSuccess(data);
                }

                @Override
                public void onFail(String errMsg, Exception error) {
                    context.onError(errMsg, MSIError.getGeneralError(ERROR_CODE_BACKGROUND_FETCH_DATA_FAIL));
                }

                @Override
                public void onCancel() {
                    context.onError(RequestPrefetchManager.REPORT_STATE_CANCEL, MSIError.getIgnoreError(MSCErrorCode.ERROR_CODE_API_COMMON_MEET_EXPECTATIONS));
                }
            });
        } else {
            context.onError(-1, "fetchType + " + fetchDataParam.fetchType + " not supported", MSIError.getGeneralError(MSCErrorCode.ERROR_CODE_API_COMMON_INVALID_PARAM));
        }
    }
}