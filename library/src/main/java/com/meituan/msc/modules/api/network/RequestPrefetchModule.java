package com.meituan.msc.modules.api.network;

import com.meituan.msc.common.framework.Callback;
import com.meituan.msc.extern.MSCEnvHelper;
import com.meituan.msc.jse.bridge.WritableNativeMap;
import com.meituan.msc.modules.api.storage.StorageModule;
import com.meituan.msc.modules.engine.dataprefetch.IDataPrefetchModule;
import com.meituan.msc.modules.engine.requestPrefetch.RequestPrefetchManager;
import com.meituan.msc.modules.manager.MSCMethod;
import com.meituan.msc.modules.manager.MSCModule;
import com.meituan.msc.modules.manager.MSCNativePromiseCallback;
import com.meituan.msc.modules.manager.ModuleName;

import org.json.JSONArray;
import org.json.JSONException;
import org.json.JSONObject;

/**
 * Created by letty on 2023/3/24.
 **/
@ModuleName(name = "RequestPrefetchModule")
public class RequestPrefetchModule extends MSCModule {
    public static final String SP_KEY_REQUEST_PREFETCH_TOKEN = "request_prefetch_token";
    //缓存数据类别：数据预拉取
    public static final String FETCH_DATA_TYPE_PRE = "pre";
    public static final String KEY_FETCH_TYPE = "fetchType";

    //https://km.sankuai.com/page/1304067328#id-1%E3%80%81wx.getBackgroundFetchData
    //https://developers.weixin.qq.com/miniprogram/dev/api/storage/background-fetch/wx.getBackgroundFetchData.html
    @MSCMethod
    public void getBackgroundFetchData(JSONObject fetchDataParam, MSCNativePromiseCallback callback) {
        if (FETCH_DATA_TYPE_PRE.equals(fetchDataParam.optString(KEY_FETCH_TYPE))) {
            getRuntime().getRequestPrefetchManager().getData(new Callback<FetchTokenResponse>() {
                @Override
                public void onSuccess(FetchTokenResponse data) {
                    callback.onSuccess(buildFetchTokenResponseMap(data));
                }

                @Override
                public void onFail(String errMsg, Exception error) {
                    callback.onError(errMsg, error);
                }

                @Override
                public void onCancel() {
                    callback.onError(RequestPrefetchManager.REPORT_STATE_CANCEL);
                }
            });
        } else {
            callback.onError("fetchType + " + fetchDataParam.optString("fetchType") + " not supported");
        }
    }

    private WritableNativeMap buildFetchTokenResponseMap(FetchTokenResponse data) {
        if (data == null) {
            return null;
        }
        WritableNativeMap writableMap = new WritableNativeMap();
        writableMap.putString("fetchedData", data.fetchedData);
        writableMap.putString("url", data.url);
        writableMap.putDouble("timeStamp", data.timeStamp);
        writableMap.putDouble("__mtFinishTimeStamp", data.__mtFinishTimeStamp);
        writableMap.putString("path", data.path);
        writableMap.putString("query", data.query);
        writableMap.putInt("scene", data.scene);
        return writableMap;
    }

    @MSCMethod(isSync = true)
    public WritableNativeMap getBackgroundFetchDataSync(JSONObject fetchDataParam) {
        if (FETCH_DATA_TYPE_PRE.equals(fetchDataParam.optString(KEY_FETCH_TYPE))) {
            FetchTokenResponse data = getRuntime().getRequestPrefetchManager().getDataSync();
            return buildFetchTokenResponseMap(data);
        } else {
            return null;
        }
    }

    public static String getPrefetchSpName(String appId) {
        return StorageModule.getMiniProgramStorageNameUserPrefer(appId) + "_prefetch";
    }

    public static String getBackgroundFetchToken(String appId) {
        return MSCEnvHelper.getSharedPreferences(getPrefetchSpName(appId)).getString(SP_KEY_REQUEST_PREFETCH_TOKEN, null);
    }

    @MSCMethod(isSync = true)
    public JSONObject getAppBackgroundFetchDataSync(JSONObject fetchDataParam) throws JSONException {
        if (FETCH_DATA_TYPE_PRE.equals(fetchDataParam.optString(KEY_FETCH_TYPE))) {
            JSONObject appPrefetchResult = getRuntime().getRequestPrefetchManager().getAppDataSync();
            if (appPrefetchResult != null) {
                appPrefetchResult.put("mmp.status", "success");
                return appPrefetchResult;
            }
        }
        return new JSONObject().put("mmp.status", "failure");
    }

    @MSCMethod(isSync = true)
    public JSONArray getPrefetchDataSync(String pageId, JSONArray urlArray){
        String[] urls = null;
        if (urlArray != null) {
            int urlLen = urlArray.length();
            if (urlLen > 0) {
                urls = new String[urlLen];
                for (int i = 0; i < urlLen; i++) {
                    urls[i] = urlArray.optString(i);
                }
            }
        }

        JSONArray jsonArray = null;
        IDataPrefetchModule dataPrefetchModule = getModule(IDataPrefetchModule.class);
        if (dataPrefetchModule != null) {
            jsonArray = dataPrefetchModule.getPrefetchDataSync(pageId, urls);
        }

        if (jsonArray == null) {
            jsonArray = new JSONArray();
        }

        return jsonArray;
    }
}
