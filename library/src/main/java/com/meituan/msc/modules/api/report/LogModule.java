package com.meituan.msc.modules.api.report;

import com.meituan.msc.modules.manager.MSCMethod;
import com.meituan.msc.modules.manager.MSCModule;
import com.meituan.msc.modules.manager.ModuleName;
import com.meituan.msc.modules.reporter.MSCLog;

/**
 * 提供给前端打日志到logan
 * Created by letty on 2022/3/9.
 **/
@ModuleName(name = "LogModule")
public class LogModule extends MSCModule {

    @MSCMethod
    public void reportLog(String params){
        MSCLog.i("LogModule", params);
    }
}
