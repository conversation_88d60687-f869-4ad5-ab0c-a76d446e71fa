//package com.meituan.msc.modules.api.report;
//
//import com.meituan.android.common.statistics.Statistics;
//import com.meituan.msc.modules.api.ApiFunction;
//import com.meituan.msc.extern.IApiCallback;
//
//import org.json.JSONObject;
//
//public class LxModule {
//    public static class LxLog extends ApiFunction<JSONObject, JSONObject> {
//        @Override
//        protected void onInvoke(String apiName, JSONObject params, IApiCallback callback) {
//            try {
//                callback.onSuccess(Statistics.mmpToNative(params));
//            } catch (Throwable t) {
//                callback.onFail(null);
//            }
//        }
//
//        @Override
//        protected boolean isActivityApi() {
//            return false;
//        }
//    }
//}
