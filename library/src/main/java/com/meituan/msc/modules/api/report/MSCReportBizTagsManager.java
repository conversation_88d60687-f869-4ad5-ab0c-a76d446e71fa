package com.meituan.msc.modules.api.report;

import android.support.annotation.Nullable;
import android.text.TextUtils;

import com.meituan.msc.common.config.MSCConfig;
import com.meituan.msc.common.utils.MPConcurrentHashMap;
import com.meituan.msc.common.utils.PathUtil;
import com.meituan.msc.modules.reporter.MSCLog;
import com.meituan.msi.annotations.MsiSupport;

import java.util.Collection;
import java.util.HashMap;
import java.util.HashSet;
import java.util.Map;
import java.util.Set;

public class MSCReportBizTagsManager {

    private static final String TAG = "MSCReportBizTagsManager";
    private static volatile MSCReportBizTagsManager instance;

    public static MSCReportBizTagsManager getInstance() {
        if (instance == null) {
            synchronized (MSCReportBizTagsManager.class) {
                if (instance == null) {
                    instance = new MSCReportBizTagsManager();
                }
            }
        }
        return instance;
    }

    /**
     * 业务公共维度数据（所有页面均上报）
     */
    private final Map<String, Map<String, String>> bizTagsForAppId = new MPConcurrentHashMap<>();
    /**
     * 业务指定页面维度数据（指定页面上报）
     */
    private final Map<String, Map<String, Map<String, String>>> bizTagsForPage = new MPConcurrentHashMap<>();

    /**
     * @param appId      appId
     * @param targetPath targetPath
     * @param bizTags    bizTags
     * @return 是否添加成功 如appId为空 或 超过维度个数限制，会返回false
     * @api 添加业务维度数据，key重复时会覆盖
     */
    public boolean addBizTags(String appId, String targetPath, Map<String, String> bizTags) {
        MSCLog.i(TAG, "putBizTags", appId, targetPath, bizTags);
        if (TextUtils.isEmpty(appId)) {
            MSCLog.e(TAG, "putBizTags appId null");
            return false;
        }
        if (isOutOfLimit(appId, targetPath, bizTags)) {
            return false;
        }

        String path = PathUtil.getPath(targetPath);
        // 缓存 业务公共维度数据（所有页面均上报）
        if (TextUtils.isEmpty(path)) {
            Map<String, String> originBizTagsMap = bizTagsForAppId.get(appId);
            if (originBizTagsMap == null) {
                originBizTagsMap = new MPConcurrentHashMap<>();
            }
            originBizTagsMap.putAll(bizTags);
            bizTagsForAppId.put(appId, originBizTagsMap);
        } else {
            // 缓存 业务指定页面维度数据（指定页面上报）
            Map<String, Map<String, String>> pageBizTags;
            if (!bizTagsForPage.containsKey(appId)) {
                pageBizTags = new MPConcurrentHashMap<>();
                bizTagsForPage.put(appId, pageBizTags);
            } else {
                pageBizTags = bizTagsForPage.get(appId);
            }
            Map<String, String> originBizTagsMap = pageBizTags.get(path);
            if (originBizTagsMap == null) {
                originBizTagsMap = new MPConcurrentHashMap<>();
                pageBizTags.put(path, originBizTagsMap);
            }
            originBizTagsMap.putAll(bizTags);
        }
        return true;
    }

    private boolean isOutOfLimit(String appId, String targetPath, Map<String, String> bizTags) {
        if (bizTags == null) {
            return false;
        }
        // 业务公共维度数据（所有页面均上报）
        if (TextUtils.isEmpty(targetPath)) {
            BizTagsData bizTagsData = getBizTags(appId, null);
            Map<String, String> originBizTags = bizTagsData == null ? null : bizTagsData.getBizTagsForAppId();
            if (isOutOfSizeLimit(originBizTags, bizTags, MSCConfig.getBizTagsForAppIdMaxCount())) {
                return true;
            }
        } else {
            BizTagsData bizTagsData = getBizTags(appId, targetPath);
            // 业务指定页面维度数据（指定页面上报）
            Map<String, String> originBizTags = bizTagsData == null ? null : bizTagsData.getBizTagsForPage(targetPath);
            if (isOutOfSizeLimit(originBizTags, bizTags, MSCConfig.getBizTagsForPageMaxCount())) {
                return true;
            }
        }

        // 维度数据长度限制
        Collection<String> tagContents = bizTags.values();
        for (String tag : tagContents) {
            if (tag == null) {
                continue;
            }
            if (tag.length() > MSCConfig.getBizTagsContentMaxLength()) {
                return true;
            }
        }
        return false;
    }

    private boolean isOutOfSizeLimit(Map<String, String> originBizTags, Map<String, String> newBizTags, int sizeLimit) {
        Set<String> keySet = new HashSet<>();
        if (originBizTags != null) {
            keySet.addAll(originBizTags.keySet());
        }
        keySet.addAll(newBizTags.keySet());
        return keySet.size() > sizeLimit;
    }

    /**
     * @param appId      appId
     * @param targetPath targetPath 如果为空，会清除业务公共维度数据
     * @param bizTagKey  bizTagKey
     * @return 是否成功移除，key不存在时返回false
     * @api 移除业务维度数据
     */
    public String removeBizTags(String appId, String targetPath, String bizTagKey) {
        if (TextUtils.isEmpty(appId)) {
            MSCLog.e(TAG, "removeBizTags appId null");
            return "";
        }
        MSCLog.i(TAG, "removeBizTags", appId, targetPath, bizTagKey);
        String path = PathUtil.getPath(targetPath);
        // 移除 业务公共维度数据（所有页面均上报）
        if (TextUtils.isEmpty(path)) {
            Map<String, String> originBizTagsMap = bizTagsForAppId.get(appId);
            return originBizTagsMap.remove(bizTagKey);
        } else {
            // 缓存 业务指定页面维度数据（指定页面上报）
            Map<String, Map<String, String>> bizTagsForAllPages = bizTagsForPage.get(appId);
            if (bizTagsForAllPages == null) {
                return "";
            }
            Map<String, String> bizTags = bizTagsForAllPages.get(path);
            if (bizTags == null) {
                return "";
            }
            return bizTags.remove(bizTagKey);
        }
    }

    /**
     * @param appId      appId
     * @param targetPath targetPath 如果为空，会清除业务公共维度数据
     * @api 清除业务维度数据
     */
    public void clearBizTags(String appId, String targetPath) {
        if (TextUtils.isEmpty(appId)) {
            MSCLog.e(TAG, "clearBizTags appId null");
            return;
        }
        MSCLog.i(TAG, "clearBizTags", appId, targetPath);
        String path = PathUtil.getPath(targetPath);
        // 移除 业务公共维度数据（所有页面均上报）
        if (TextUtils.isEmpty(path)) {
            Map<String, String> bizTags = bizTagsForAppId.get(appId);
            if (bizTags != null) {
                bizTags.clear();
            }
        } else {
            // 缓存 业务指定页面维度数据（指定页面上报）
            Map<String, Map<String, String>> bizTagsForAllPages = bizTagsForPage.get(appId);
            if (bizTagsForAllPages == null) {
                return;
            }
            Map<String, String> bizTags = bizTagsForAllPages.get(path);
            if (bizTags != null) {
                bizTags.clear();
            }
        }
    }

    /**
     * @param appId appId
     * @api 清除业务所有维度数据
     */
    public void clearAllBizTags(String appId) {
        if (TextUtils.isEmpty(appId)) {
            MSCLog.e(TAG, "clearAllBizTags appId null");
            return;
        }
        MSCLog.i(TAG, "clearAllBizTags", appId);
        Map<String, String> originBizTagsMap = bizTagsForAppId.get(appId);
        if (originBizTagsMap != null) {
            originBizTagsMap.clear();
        }

        Map<String, Map<String, String>> pageBizTags = bizTagsForPage.get(appId);
        if (pageBizTags != null) {
            pageBizTags.clear();
        }
    }

    /**
     * @param appId      appId
     * @param targetPath targetPath 如果为空，则仅返回业务公共维度数据（所有页面均上报）
     * @return 容器指标上报时聚合的业务维度数据，含容器API添加的数据
     * @api 获取业务维度数据
     */
    @Nullable
    public BizTagsData getBizTags(String appId, String targetPath) {
        if (TextUtils.isEmpty(appId)) {
            return null;
        }
        String path = PathUtil.getPath(targetPath);
        Map<String, String> originBizTagsMap = bizTagsForAppId.get(appId);
        // 获取 业务公共维度数据（所有页面均上报）
        if (TextUtils.isEmpty(path)) {
            return new BizTagsData(originBizTagsMap != null ? new HashMap<>(originBizTagsMap) : null, null);
        } else {
            // 获取 业务指定页面维度数据（指定页面上报）
            Map<String, Map<String, String>> bizTagsForAllPages = this.bizTagsForPage.get(appId);
            Map<String, Map<String, String>> bizTagsForPageResult = new HashMap<>();
            if (bizTagsForAllPages != null) {
                bizTagsForPageResult.put(path, bizTagsForAllPages.get(path));
            }
            return new BizTagsData(null, bizTagsForPageResult);
        }
    }

    /**
     * @param appId appId
     * @return 容器指标上报时聚合的业务维度数据，含容器API添加的数据
     * @api 获取业务维度数据
     */
    public BizTagsData getAllBizTags(String appId) {
        if (TextUtils.isEmpty(appId)) {
            return null;
        }
        MSCLog.i(TAG, "getAllBizTags", appId);
        Map<String, String> tagsForAppId = new HashMap<>();
        if (bizTagsForAppId.get(appId) != null) {
            tagsForAppId = new HashMap<>(bizTagsForAppId.get(appId));
        }
        Map<String, Map<String, String>> tagsForPage = new HashMap<>();
        if (bizTagsForPage.get(appId) != null) {
            tagsForPage = new HashMap<>(bizTagsForPage.get(appId));
        }
        return new BizTagsData(tagsForAppId, tagsForPage);
    }

    @MsiSupport
    public static class BizTagsData {
        private final Map<String, String> bizTagsForAppId;
        private final Map<String, Map<String, String>> bizTagsForPage;

        public BizTagsData(Map<String, String> bizTagsForAppId, Map<String, Map<String, String>> bizTagsForPage) {
            this.bizTagsForAppId = bizTagsForAppId;
            this.bizTagsForPage = bizTagsForPage;
        }

        /**
         * @return 业务公共维度数据
         * @api 业务公共维度数据
         */
        @Nullable
        public Map<String, String> getBizTagsForAppId() {
            return bizTagsForAppId;
        }

        /**
         * @return 业务所有页面及对应维度数据
         * @api 获取业务所有页面及对应维度数据
         */
        @Nullable
        public Map<String, Map<String, String>> getBizTagsForPage() {
            return bizTagsForPage;
        }

        /**
         * @param targetPath 页面路径
         * @return 业务指定页面维度数据（指定页面上报）
         * @api 获取业务指定页面维度数据
         */
        @Nullable
        public Map<String, String> getBizTagsForPage(String targetPath) {
            if (TextUtils.isEmpty(targetPath)) {
                return null;
            }
            MSCLog.i(TAG, "getBizTagsForPage", targetPath);
            return bizTagsForPage != null ? bizTagsForPage.get(PathUtil.getPath(targetPath)) : null;
        }
    }
}
