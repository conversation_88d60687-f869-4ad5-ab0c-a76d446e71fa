package com.meituan.msc.modules.api.report;

import static com.meituan.msc.common.utils.Constants.PAGE_ID;

import android.text.TextUtils;
import android.view.View;

import com.meituan.msc.common.constant.APPIDConstants;
import com.meituan.msc.common.report.MetricsEntry;
import com.meituan.msc.common.utils.JsonUtil;
import com.meituan.msc.extern.MSCEnvHelper;
import com.meituan.msc.jse.modules.core.JSDeviceEventEmitter;
import com.meituan.msc.modules.container.IContainerManager;
import com.meituan.msc.modules.devtools.IPerformanceManager;
import com.meituan.msc.modules.engine.MSCHornRollbackConfig;
import com.meituan.msc.modules.engine.MSCRuntime;
import com.meituan.msc.modules.engine.MSCRuntimeReporter;
import com.meituan.msc.modules.manager.IMSCCompletableCallback;
import com.meituan.msc.modules.manager.MSCMethod;
import com.meituan.msc.modules.manager.MSCModule;
import com.meituan.msc.modules.manager.ModuleName;
import com.meituan.msc.modules.page.IPageModule;
import com.meituan.msc.modules.page.render.AppPageReporter;
import com.meituan.msc.modules.page.render.BaseRenderer;
import com.meituan.msc.modules.page.render.RendererType;
import com.meituan.msc.modules.reporter.MSCLog;
import com.meituan.msc.util.perf.PerfEvent;
import com.meituan.msc.util.perf.PerfTrace;
import com.meituan.msi.bean.EventType;

import org.json.JSONArray;
import org.json.JSONException;
import org.json.JSONObject;

import java.util.ArrayList;
import java.util.HashSet;
import java.util.List;
import java.util.Map;
import java.util.concurrent.ConcurrentHashMap;

/**
 * 小程序框架性能指标上报
 * https://km.sankuai.com/page/539280488
 * 支持批量上报
 */
@ModuleName(name = "MetricsModule")
public class MetricsModule extends MSCModule {
    private static final String TAG = "MetricsModule";

    public static final String FE_REPORT_PAGE_SCROLL_FPS = "msc.fe.page.scroll.fps";

    @MSCMethod
    public void reportPerfEvent(String name, String phase, long timestamp, JSONObject extra) {
        PerfEvent event = new PerfEvent(name, phase, timestamp);
        event.extra = extra;
        PerfTrace.addJsPerfEvent(event);
    }

    private void reportTopPagePerfEvents() {
        reportTopPagePerfEventsByCount(getRuntime());
    }

    private static AppPageReporter getAppPageReporter(MSCRuntime runtime) {
        IPageModule topPage = runtime.getTopPageModule();
        if (topPage == null) {
            return null;
        }
        BaseRenderer renderer = topPage.getRenderer();
        if (renderer == null) {
            return null;
        }
        return renderer.pageData.appPageReporter;
    }

    private static void reportTopPagePerfEventsByCount(MSCRuntime runtime) {
        AppPageReporter reporter = getAppPageReporter(runtime);
        if (reporter == null) {
            return;
        }
        int count = reporter.getFlushCount();
        if (count == 2) {//认为第二次是FFP
            reporter.triggerFFPStagesReportIfNeed();
            reporter.clearFlushCount();
        } else if (count == 1) {//认为第一次是FP
            reporter.triggerFPStagesReportIfNeed();
        }
    }

    /**
     * 通知JS侧将JS侧的Trace缓存一次性刷过来，JS侧会调用reportPerfEvent桥将缓存的Trace刷过来
     */
    public void flushPerfEvents() {
        getRuntime().getJSModuleDelegate(JSDeviceEventEmitter.class)
                .emit("flushPerfEvents", null);
    }

    /**
     * 增加pageId参数用于视图层发送事件
     */
    public void sendFFPEndEvent(String pageUrl, long startTimeStamp, long endTimeStamp, String pageId) {
        String ffpEndEvent = "FFPEnd";
        JSONObject data = new JSONObject();
        try {
            data.put("pageUrl", pageUrl);
            data.put("value", (endTimeStamp - startTimeStamp));
            data.put("startTime", startTimeStamp);
            data.put("endTime", endTimeStamp);
        } catch (JSONException ignore) {
        }
        MSCLog.i(TAG, "sendFFPEndEvent");
        getRuntime().getJSModuleDelegate(JSDeviceEventEmitter.class)
                .emit(ffpEndEvent, data.toString());
        if (MSCHornRollbackConfig.enableSendFFPEndToMSI()) {
            getRuntime().apisManager.dispatchEvent(ffpEndEvent, new JSONObject());
            if (!TextUtils.isEmpty(pageId)) {
                getRuntime().apisManager.dispatchEventToWebView(ffpEndEvent, new JSONObject(), pageId);
            }
        }
    }

    public void sendFFPResetEvent(int pageId) {
        if (MSCHornRollbackConfig.enableSendFFPEndToMSI()) {
            String onFFPResetEvent = "onFFPReset";
            getRuntime().apisManager.dispatchEvent(onFFPResetEvent, new JSONObject());
            IContainerManager containerManager = getRuntime().getContainerManagerModule();
            if (containerManager != null) {
                IPageModule pageModule = containerManager.getPageByPageId(pageId);
                if (pageModule != null && pageModule.getRenderer() != null && pageId != View.NO_ID && pageModule.getRenderer().getType() == RendererType.WEBVIEW) {
                    getRuntime().apisManager.dispatchEventToWebView(onFFPResetEvent, new JSONObject(), String.valueOf(pageId));
                }
            }
        }
    }

    @MSCMethod
    public void reportPerfEvents(JSONArray events, JSONObject customData) {
        if (events == null || events.length() == 0) {
            reportTopPagePerfEvents();
            return;
        }
        try {
            for (int i = 0; i < events.length(); i++) {
                JSONObject event = events.getJSONObject(i);
                String name = event.getString("name");
                String phase = event.getString("phase");
                long timestamp = event.getLong("timestamp");
                JSONObject extra = event.optJSONObject("extra");
                PerfEvent perfEvent = new PerfEvent(name, phase, timestamp);
                if (extra != null) {
                    perfEvent.shouldReport(extra.optBoolean("report", false));
                }
                perfEvent.extra = extra;
                PerfTrace.addJsPerfEvent(perfEvent);
            }
        } catch (JSONException e) {
            MSCLog.e("batchReportPerfEvent", e);
        } finally {
            reportTopPagePerfEvents();
        }
    }

    @MSCMethod
    public void batchReportPerfEvent(JSONArray events) {
        reportPerfEvents(events, null);
    }

    @MSCMethod
    public void generateTraceFile(JSONObject args) {
        // AOP
    }

    @MSCMethod
    public void reportMetrics(String category, String type, long value, JSONObject tags) {
        if (TextUtils.isEmpty(type) || type.startsWith("mmp.")) {
            // [客户端兜底] 不上报mmp相关的指标
            return;
        }
        MSCRuntime runtime = getRuntime();
        Map<String, Object> tagMap = JsonUtil.toMap(tags);
        int pageId = tags.optInt(PAGE_ID, -1);
        if (pageId > 0) {
            tagMap.remove(PAGE_ID);
            // 前端打过来的 page 相关的指标使用PageReporter上报
            IPageModule pageModule = runtime.getContainerManagerModule().getPageByPageId(pageId);
            if (pageModule != null) {
                AppPageReporter pageReporter = pageModule.getReporter();
                if (pageReporter != null) {
                    pageReporter.reportFeMetrics(type, value, tagMap);
                    return;
                }
            }
        }
        runtime.getRuntimeReporter().record(type)
                .tags(tagMap)
                //https://km.sankuai.com/page/951566560
                .value(value)
                .sendRealTime();
        updateFrameData(type, value);
    }

    @MSCMethod
    public void reportBatchMetrics(JSONArray jsonArray) {
        MSCRuntimeReporter reporter = getRuntime().getRuntimeReporter();
        List<MetricsEntry> entries = new ArrayList<>();
        for (int i = 0; i < jsonArray.length(); i++) {
            JSONObject param = jsonArray.optJSONObject(i);
            if (param != null) {
                String type = param.optString("type");
                if (TextUtils.isEmpty(type) || type.startsWith("mmp.")) {
                    // [客户端兜底] 不上报mmp相关的指标
                    continue;
                }
                entries.add(reporter
                        .record(type)
                        .tags(JsonUtil.toMap(param.optJSONObject("tags")))
                        //https://km.sankuai.com/page/951566560
                        .value(param.optLong("value"))
                );
            }
        }
        reporter.sendBatchRealTime(entries);
    }

    // FIXME by chendacai: 2022/3/3 之前MMP上报指标太多了，先静默掉
    @Deprecated
    public static void reportMetrics(String type, long value, Map<String, Object> option) {
//        MSCEnvHelper.getLogger().log(type, null, option, value);
        MSCLog.d("Metrics", type, "\t", value, "\t", option);
    }

    // FIXME by chendacai: 2022/3/3 之前MMP上报指标太多了，先静默掉
    @Deprecated
    public static void reportMetrics(String type, Map<String, Object> option) {
//        MSCEnvHelper.getLogger().log(type, null, option);
        MSCLog.d("Metrics", type, "\t", "\t", option);
    }

    private void updateFrameData(String type, long value) {
        if (MSCEnvHelper.getEnvInfo().isProdEnv()) {
            return;
        }
        IPerformanceManager performanceManager = getRuntime().getModuleWithoutDelegate(IPerformanceManager.class);
        if (performanceManager != null && performanceManager.isPerformanceManagerOpened()) {
            if (TextUtils.equals(type, FE_REPORT_PAGE_SCROLL_FPS)) {
                performanceManager.updateFrameData(type, value);
            }
        }
    }

    @MSCMethod(isSync = false)
    public void getPerformanceData(IMSCCompletableCallback<JSONObject> callback) {
        JSONObject result = new JSONObject();
        try {
            // mmp文档埋点地址 https://km.sankuai.com/collabpage/1683064235
            MSCRuntimeReporter runtimeReporter = getRuntime().getRuntimeReporter();
            HashSet<String> hotSet = new HashSet<>();
            hotSet.add("JSThread_Create");
            hotSet.add("Pre_V8_Create_JS");
            hotSet.add("After_V8_Create_JS");
            hotSet.add("Pre_Meta_Read");
            hotSet.add("After_Meta_Read");
            hotSet.add("Pre_Package_Load");
            hotSet.add("After_Package_Load");
            hotSet.add("Pre_ServiceJS_Load");
            hotSet.add("After_SreviceJS_Load");
            hotSet.add("Pre_YXServJS_Load");
            hotSet.add("After_YXServJS_Load");
            long launcherTime = runtimeReporter.getLauncherTime();
            ConcurrentHashMap<String, Long> statisticsMap = runtimeReporter.getStatisticsMap();
            if (TextUtils.equals(getRuntime().getAppId(), APPIDConstants.YOU_XUAN)) {
                for (Map.Entry<String, Long> entry : statisticsMap.entrySet()) {
                    if (hotSet.contains(entry.getKey()) && (launcherTime - entry.getValue() > 0)) {
                        result.put(entry.getKey() + "_Hot", entry.getValue());
                    } else {
                        result.put(entry.getKey(), entry.getValue());
                    }
                }
                statisticsMap.clear();
            }
            if (TextUtils.equals(getRuntime().getAppId(), APPIDConstants.YOU_XUAN)) {
                for (Map.Entry<String, Long> entry : statisticsMap.entrySet()) {
                    if (hotSet.contains(entry.getKey()) && (launcherTime - entry.getValue() > 0)) {
                        result.put(entry.getKey() + "_Hot", entry.getValue());
                    } else {
                        result.put(entry.getKey(), entry.getValue());
                    }
                }
                runtimeReporter.getStatisticsMap().clear();
            }
        } catch (JSONException e) {
            MSCLog.e(TAG, e);
        }
        MSCLog.i(TAG, "getPerformanceData", result);
        callback.onComplete(result);
    }
}
