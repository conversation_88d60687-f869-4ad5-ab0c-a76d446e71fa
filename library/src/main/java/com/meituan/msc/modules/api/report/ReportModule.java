//package com.meituan.msc.modules.api.report;
//
//import android.text.TextUtils;
//
//import com.meituan.android.common.statistics.LxEnvironment;
//import com.meituan.android.common.statistics.Statistics;
//import com.meituan.android.common.statistics.channel.Channel;
//import com.meituan.msc.modules.api.ApiFunction;
//import com.meituan.msc.modules.api.Empty;
//import com.meituan.msc.common.utils.JsonUtil;
//import com.meituan.msc.common.model.GsonBean;
//import com.meituan.msc.extern.IApiCallback;
//import com.meituan.msc.common.annotation.Api;
//import com.meituan.msc.common.annotation.Required;
//
//import org.json.JSONException;
//import org.json.JSONObject;
//
//import java.util.HashMap;
//import java.util.Iterator;
//import java.util.Map;
//
//public class ReportModule {
//
//    public static class ReportAnalyticsParams implements GsonBean {
//        @Required
//        public String eventName;
//
//        @Required
//        public String data;
//    }
//
//    @Api("reportAnalytics")
//    public static class ReportAnalytics extends ApiFunction<ReportAnalyticsParams, Empty> {
//        @Override
//        protected boolean isActivityApi() {
//            return false;
//        }
//
//        @Override
//        protected void onInvoke(String apiName, ReportAnalyticsParams params, IApiCallback callback) {
//            if (!"lingxi".contentEquals(params.eventName)) {
//                callback.onFail(null);
//                return;
//            }
//
//            JSONObject dataJson = null;
//            try {
//                dataJson = new JSONObject(params.data);
//            } catch (JSONException ignore) {
//            }
//            if (dataJson == null) {
//                callback.onFail(null);
//                return;
//            }
//            String channel = dataJson.optString("channel");
//            String nm = dataJson.optString("nm");
//            String cid = dataJson.optString("val_cid");
//            String bid = dataJson.optString("val_bid");
//            JSONObject lab = dataJson.optJSONObject("val_lab");
//
//            if (TextUtils.isEmpty(channel) || TextUtils.isEmpty(nm)) {
//                callback.onFail(null);
//                return;
//            }
//
//            Map<String, Object> labMap = null;
//            if (lab != null) {
//                labMap = new HashMap<>();
//
//                Iterator<String> iterator = lab.keys();
//                while (iterator.hasNext()) {
//                    String key = iterator.next();
//                    String value = lab.optString(key);
//                    labMap.put(key, value);
//                }
//            }
//
//            String pageInfoKey = String.format("%s_%s", "MSCActivity", getAppId());
//            Channel reportChannel = Statistics.getChannel(channel);
//
//            if ("MV".contentEquals(nm)) {
//                reportChannel.writeModelView(pageInfoKey, bid, labMap, cid);
//            } else if ("MC".contentEquals(nm)) {
//                reportChannel.writeModelClick(pageInfoKey, bid, labMap, cid);
//            } else if ("ME".contentEquals(nm)) {
//                reportChannel.writeModelEdit(pageInfoKey, bid, labMap, cid);
//            } else if ("BO".contentEquals(nm)) {
//                reportChannel.writeBizOrder(pageInfoKey, bid, labMap, cid);
//            } else if ("BP".contentEquals(nm)) {
//                reportChannel.writeBizPay(pageInfoKey, bid, labMap, cid);
//            } else if ("SC".contentEquals(nm)) {
//                reportChannel.writeSystemCheck(pageInfoKey, bid, labMap, cid);
//            } else if ("PV".contentEquals(nm)) {
//                reportChannel.writePageView(pageInfoKey, cid, labMap);
//            } else if ("PD".contentEquals(nm)) {
//                reportChannel.writePageDisappear(pageInfoKey, cid, labMap);
//            } else {
//                callback.onFail(null);
//                return;
//            }
//            callback.onSuccess(null);
//        }
//    }
//
//    @Api("setLxTag")
//    public static class SetLxTag extends ApiFunction<JSONObject, Empty> {
//        @Override
//        protected boolean isActivityApi() {
//            return false;
//        }
//
//        @Override
//        protected void onInvoke(String apiName, JSONObject params, IApiCallback callback) {
//            JSONObject tags = params.optJSONObject("data");
//            if (tags == null || tags.length() == 0) {
//                callback.onFail(null);
//                return;
//            }
//            String key = tags.optString("key");
//            JSONObject value = tags.optJSONObject("value");
//            if (value == null) {
//                callback.onFail(null);
//                return;
//            }
//            Map<String, Object> map = null;
//            try {
//                map = JsonUtil.toMap(value);
//            } catch (JSONException ignore) {
//            }
//            if (map == null) {
//                callback.onFail(null);
//                return;
//            }
//
//            Statistics.getChannel().updateTag(key, map);
//            callback.onSuccess(null);
//        }
//    }
//
//    public static class GetLxEnvironmentResult implements GsonBean {
//        public Map<String, Object> tag = new HashMap<>();
//        public Env env = new Env();
//
//        public class Env {
//            public String union_id;
//            public String uuid;
//            public String dpid;
//            public String lch;
//            public String msid;
//        }
//    }
//
//    @Api("getLxEnvironment")
//    public static class GetLxEnvironment extends ApiFunction<Empty, GetLxEnvironmentResult> {
//
//
//        @Override
//        protected boolean isActivityApi() {
//            return false;
//        }
//
//        @Override
//        protected void onInvoke(String apiName, Empty params, IApiCallback callback) {
//            GetLxEnvironmentResult result = new GetLxEnvironmentResult();
//            LxEnvironment lxEnvironment = Statistics.getLxEnvironment();
//            if (lxEnvironment == null) {
//                returnFail(-1, "cannot get LxEnvironment", callback);
//                return;
//            }
//            result.env.union_id = lxEnvironment.getUnionid();
//            result.env.uuid = lxEnvironment.getUuid();
//            result.env.dpid = lxEnvironment.getDpid();
//            result.env.lch = lxEnvironment.getLch();
//            result.env.msid = lxEnvironment.getMsid();
//
//            Map<String, Object> tags = lxEnvironment.getTag();
//            if (tags != null && !tags.isEmpty()) {
//                result.tag.putAll(tags);
//            }
//
//            returnSuccess(result, callback);
//        }
//    }
//}
