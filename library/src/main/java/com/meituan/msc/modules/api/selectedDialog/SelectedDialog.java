package com.meituan.msc.modules.api.selectedDialog;

import android.app.Activity;

import android.graphics.Color;
import android.os.Bundle;

import android.util.TypedValue;
import android.view.Gravity;
import android.view.View;
import android.view.ViewGroup;
import android.view.Window;

import android.widget.LinearLayout;
import android.widget.RelativeLayout;
import android.widget.TextView;

import com.meituan.msc.lib.R;

import com.meituan.msc.common.utils.ScreenUtil;
import com.meituan.msc.modules.page.widget.BottomDialog;

import java.util.List;

public class SelectedDialog extends BottomDialog {
    private List<SelectedItem> mSelectedList;
    private static final String SELECTED_TYPE_CANCEL = "取消";

    public SelectedDialog(Activity activity, List<SelectedItem> selectedList) {
        super(activity);
        mSelectedList = selectedList;
    }

    @Override
    protected void onCreate(Bundle savedInstanceState) {
        LinearLayout linearLayout = new LinearLayout(getContext());
        linearLayout.setLayoutParams(new ViewGroup.LayoutParams(ViewGroup.LayoutParams.MATCH_PARENT, ViewGroup.LayoutParams.WRAP_CONTENT));
        linearLayout.setOrientation(LinearLayout.VERTICAL);
        setContentView(linearLayout);
        super.onCreate(savedInstanceState);
        Window window = getWindow();
        if (window != null) {
            window.setWindowAnimations(R.style.MSCDialogShowAnimation);
        }

        initSelectedView(linearLayout);
    }


    private void initSelectedView(ViewGroup viewGroup) {
        if (null != mSelectedList) {
            for (SelectedItem item : mSelectedList) {
                addSelectedItemView(viewGroup, item);
            }
        }
        //内部添加退出Dialog
        addSelectedItemView(viewGroup, new SelectedItem(null, SELECTED_TYPE_CANCEL));
    }

    private void addSelectedItemView(ViewGroup viewGroup, SelectedItem item) {
        TextView textView = new TextView(getContext());
        textView.setBackgroundResource(R.drawable.msc_button_selector);
        textView.setGravity(Gravity.CENTER);
        textView.setText(item.getContent());
        textView.setTextColor(Color.parseColor("#222222"));
        textView.setTextSize(TypedValue.COMPLEX_UNIT_DIP, 16);
        textView.setWidth(ViewGroup.LayoutParams.MATCH_PARENT);
        textView.setHeight(ScreenUtil.dp2px(55));
        textView.setOnClickListener(new View.OnClickListener() {
            @Override
            public void onClick(View v) {
                if (null != item.getOnClickListener()) {
                    item.getOnClickListener().onClick(v);
                }
                dismiss();
            }
        });

        RelativeLayout.LayoutParams params = new RelativeLayout.LayoutParams(ViewGroup.LayoutParams.MATCH_PARENT, ViewGroup.LayoutParams.WRAP_CONTENT);
        params.addRule(RelativeLayout.ALIGN_PARENT_BOTTOM);
        viewGroup.addView(textView, params);
    }

}
