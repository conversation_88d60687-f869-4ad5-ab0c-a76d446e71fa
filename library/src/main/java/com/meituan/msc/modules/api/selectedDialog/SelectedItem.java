package com.meituan.msc.modules.api.selectedDialog;

import android.view.View;

/**
 * 选择内容
 */
public class SelectedItem {
    private View.OnClickListener mOnClickListener;
    //文案
    private String mContent;

    public SelectedItem(View.OnClickListener onClickListener, String content) {
        this.mContent = content;
        this.mOnClickListener = onClickListener;
    }


    public void setSelectedListener(View.OnClickListener onClickListener) {
        this.mOnClickListener = onClickListener;
    }


    public void setContent(String content) {
        this.mContent = content;
    }

    public View.OnClickListener getOnClickListener() {
        return mOnClickListener;
    }

    public String getContent() {
        return mContent;
    }
}
