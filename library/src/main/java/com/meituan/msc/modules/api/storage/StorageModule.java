//
// Copyright (c) 2017, weidian.com
// All rights reserved.
//
// Redistribution and use in source and binary forms, with or without
// modification, are permitted provided that the following conditions are met:
//
// * Redistributions of source code must retain the above copyright notice, this
// list of conditions and the following disclaimer.
//
// * Redistributions in binary form must reproduce the above copyright notice,
// this list of conditions and the following disclaimer in the documentation
// and/or other materials provided with the distribution.
//
// THIS SOFTWARE IS PROVIDED BY THE COPYRIGHT HOLDERS AND CONTRIBUTORS "AS IS"
// AND ANY EXPRESS OR IMPLIED WARRANTIES, INCLUDING, BUT NOT LIMITED TO, THE
// IMPLIED WARRANTIES OF MERCHANTABILITY AND FITNESS FOR A PARTICULAR PURPOSE ARE
// DISCLAIMED. IN NO EVENT SHALL THE COPYRIGHT HOLDER OR CONTRIBUTORS BE LIABLE
// FOR ANY DIRECT, INDIRECT, INCIDENTAL, SPECIAL, EXEMPLARY, OR CONSEQUENTIAL
// DAMAGES (INCLUDING, BUT NOT LIMITED TO, PROCUREMENT OF SUBSTITUTE GOODS OR
// SERVICES; LOSS OF USE, DATA, OR PROFITS; OR BUSINESS INTERRUPTION) HOWEVER
// CAUSED AND ON ANY THEORY OF LIABILITY, WHETHER IN CONTRACT, STRICT LIABILITY,
// OR TORT (INCLUDING NEGLIGENCE OR OTHERWISE) ARISING IN ANY WAY OUT OF THE USE
// OF THIS SOFTWARE, EVEN IF ADVISED OF THE POSSIBILITY OF SUCH DAMAGE.
//


package com.meituan.msc.modules.api.storage;

import android.content.Context;
import android.content.SharedPreferences;
import android.text.TextUtils;

import com.meituan.msc.extern.MSCEnvHelper;
import com.meituan.msc.modules.api.ServiceApi;
import com.meituan.msc.modules.reporter.MSCLog;
import com.meituan.msc.modules.update.MSCAppModule;

import org.json.JSONArray;
import org.json.JSONException;
import org.json.JSONObject;

import java.util.ArrayList;
import java.util.Comparator;
import java.util.List;
import java.util.Map;
import java.util.Set;
import java.util.TreeSet;

public class StorageModule extends ServiceApi {
    private static final int TYPE_JSONOBJECT = 1;
    private static final int TYPE_JSONARRAY = 2;
    private static final int TYPE_STRING = 3;
    private static final int TYPE_BOOLEAN = 4;
    private static final int TYPE_NUMBER = 5;
    private static final String FIELD_DATA = "data";
    private static final String FIELD_TYPE = "type";

    public static String getMSCSharedPreferenceName(String name) {
        return "msc_" + name;
    }

    private static String getStorageType(Object data) {
        String dataType = "String";

        if (data instanceof JSONObject) {
            dataType = "Object";
        } else if (data instanceof JSONArray) {
            dataType = "Array";
        } else if (data instanceof Boolean) {
            dataType = "Boolean";
        } else if (data instanceof Number) {
            dataType = "Number";
        }
        return dataType;
    }

    /**
     * 添加存入参数的类型信息，现取出已经不需要判断类型。暂保留给后续逻辑使用。
     *
     * @param data
     */
    public static JSONObject getJSONObjectFromJs(Object data) {
        int dataType = 0;
        if (data instanceof JSONObject) {
            dataType = TYPE_JSONOBJECT;
        } else if (data instanceof JSONArray) {
            dataType = TYPE_JSONARRAY;
        } else if (data instanceof Boolean) {
            dataType = TYPE_BOOLEAN;
        } else if (data instanceof Number) {
            dataType = TYPE_NUMBER;
        } else {
            dataType = TYPE_STRING;
        }

        JSONObject jsonObject = new JSONObject();
        try {
            jsonObject.put(FIELD_DATA, data);
            jsonObject.put(FIELD_TYPE, dataType);
        } catch (JSONException e) {
        }
        return jsonObject;
    }

    /**
     * 根据SP中存入的值，解出存入的数据部分data，按原类型返回。
     *
     * @param result
     */
    private static Object getJsonObjectForJs(String result) {
        Object rt = result;
        try {
            rt = new JSONObject(result);
            JSONObject wrapper = (JSONObject) rt;
            if (wrapper.length() == 2 && wrapper.has(FIELD_DATA) && wrapper.has(FIELD_TYPE)) {
                // int type = wrapper.optInt(FIELD_TYPE, 0);
                Object data = wrapper.opt(FIELD_DATA);
                return data == null ? JSONObject.NULL : data;
            }
        } catch (JSONException e) {
            MSCLog.e("StorageModule", "Get JSONObject for Js Failed! " + e.toString());
        }
        // 如果将null存入JSONObject将会直接return，使用JSONObject.NUll，可以放入一个value=null的Object值
        return JSONObject.NULL;
    }

    /**
     * 获取Storage存储的名字,优先获取用户目录下的
     * <p>
     * 登录了的存（小程序id+用户id）目录下，没登录存小程序id名下的
     *
     * @return
     */
    public static String getMiniProgramStorageNameUserPrefer(MSCAppModule mscAppModule) {
        return getMiniProgramStorageNameUserPrefer(mscAppModule.getAppId());
    }

    /**
     * 获取Storage存储的名字,优先获取用户目录下的
     * <p>
     * 登录了的存（小程序id+用户id）目录下，没登录存小程序id名下的
     *
     * @return
     */
    public static String getMiniProgramStorageNameUserPrefer(String appId) {
        String name = getUserStorageName(appId);
        if (name == null) {
            return getMiniProgramStorageName(appId);
        } else {
            return name;
        }
    }

    /**
     * 用户维度的存储空间名称
     *
     * @return
     */
    public static String getUserStorageName(MSCAppModule mscAppModule) {
        return getUserStorageName(mscAppModule.getAppId());
    }

    /**
     * 用户维度的存储空间名称
     *
     * @return
     */
    public static String getUserStorageName(String appId) {
        String userId = MSCEnvHelper.getEnvInfo().getUserID();
        if (!TextUtils.isEmpty(userId)) {
            return String.format("%s%s", appId, userId);
        }
        return null;
    }

    /**
     * 小程序纬度的存储空间名称
     *
     * @return
     */
    public static String getMiniProgramStorageName(MSCAppModule mscAppModule) {
        return getMiniProgramStorageName(mscAppModule.getAppId());
    }

    /**
     * 小程序纬度的存储空间名称
     *
     * @return
     */
    public static String getMiniProgramStorageName(String appId) {
        return appId;
    }

    /**
     * 先从小程序目录取 再从用户目录取；因为用户目录优先级高
     *
     * @param context
     * @param mscAppModule
     * @return
     */
    public static ArrayList<List<Object>> getStorageEntries(Context context, MSCAppModule mscAppModule) {
        ArrayList<List<Object>> entries = new ArrayList<>();
        entries.addAll(getStorageEntries(StorageModule.getMiniProgramStorageName(mscAppModule), context));
        entries.addAll(getStorageEntries(StorageModule.getUserStorageName(mscAppModule), context));
        return entries;
    }

    private static ArrayList<List<Object>> getStorageEntries(String name, Context context) {
        ArrayList<List<Object>> entries = new ArrayList<>();
        SharedPreferences prefs = MSCEnvHelper.getSharedPreferences(context, StorageModule.getMSCSharedPreferenceName(name));
        for (Map.Entry<String, ?> prefsEntry : getSharedPreferenceEntriesSorted(prefs)) {
            ArrayList<Object> entry = new ArrayList<>(2);
            entry.add(prefsEntry.getKey());
            prefsEntry.getValue();
            Object obj = getJsonObjectForJs(prefsEntry.getValue().toString());
            entry.add(obj);
            entry.add(getStorageType(obj));
            entries.add(entry);
        }
        return entries;
    }

    private static Set<Map.Entry<String, ?>> getSharedPreferenceEntriesSorted(SharedPreferences preferences) {
        TreeSet<Map.Entry<String, ?>> entries = new TreeSet<>(new Comparator<Map.Entry<String, ?>>() {
            @Override
            public int compare(Map.Entry<String, ?> lhs, Map.Entry<String, ?> rhs) {
                return lhs.getKey().compareTo(rhs.getKey());
            }
        });
        entries.addAll(preferences.getAll().entrySet());
        return entries;
    }

}
