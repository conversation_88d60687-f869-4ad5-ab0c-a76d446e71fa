/*
 * Copyright (c) Facebook, Inc. and its affiliates.
 *
 * This source code is licensed under the MIT license found in the
 * LICENSE file in the root directory of this source tree.
 */

package com.meituan.msc.modules.api.timing;

import com.meituan.msc.jse.common.annotations.DoNotStrip;
import com.meituan.msc.jse.bridge.JavaScriptModule;
import com.meituan.msc.jse.bridge.WritableArray;

@DoNotStrip
public interface JSTimers extends JavaScriptModule {
  void callTimers(WritableArray timerIDs);

  void callIdleCallbacks(double frameTime);

  void emitTimeDriftWarning(String warningMessage);
}
