/*
 * Copyright (c) Facebook, Inc. and its affiliates.
 *
 * This source code is licensed under the MIT license found in the
 * LICENSE file in the root directory of this source tree.
 */

package com.meituan.msc.modules.api.timing;

import android.support.annotation.VisibleForTesting;

import com.meituan.msc.jse.bridge.WritableArray;
import com.meituan.msc.modules.container.IContainerManager;
import com.meituan.msc.modules.engine.AppService;
import com.meituan.msc.modules.engine.MSCHornRollbackConfig;
import com.meituan.msc.modules.engine.MSCRuntime;
import com.meituan.msc.modules.manager.IMSCPromiseCallback;
import com.meituan.msc.modules.manager.MSCEvent;
import com.meituan.msc.modules.manager.MSCMethod;
import com.meituan.msc.modules.manager.MSCModule;
import com.meituan.msc.modules.manager.MSCSubscriber;
import com.meituan.msc.modules.manager.ModuleName;
import com.meituan.msc.modules.reporter.MSCLog;
import com.meituan.msc.modules.service.EngineStatus;
import com.meituan.msc.modules.service.JSCServiceEngine;

/**
 * Native module for JS timer execution. Timers fire on frame boundaries.
 */

@ModuleName(name = "Timing")
public final class TimingModule extends MSCModule {

    // MRN60 chendacai 计时器在页面进入后台的时候也能运行
    private boolean mEnableBackgroundTimer = true;
    private volatile boolean mDestroyed = false;

    public class BridgeTimerManager implements JavaScriptTimerManager {
        @Override
        public void callTimers(WritableArray timerIDs) {
            if (mDestroyed) {
//                MSCLog.e("[BridgeTimerManager@callTimers]", "TimingModule destroyed");
                return;
            }
            JSTimers jsTimers = getJSTimer();
            if (jsTimers == null) {
                return;
            }
            jsTimers.callTimers(timerIDs);
        }

        @Override
        public void callIdleCallbacks(double frameTime) {
            if (mDestroyed) {
//                MSCLog.e("[BridgeTimerManager@callIdleCallbacks]", "TimingModule destroyed");
                return;
            }
            JSTimers jsTimers = getJSTimer();
            if (jsTimers == null) {
                return;
            }
            jsTimers.callIdleCallbacks(frameTime);
        }

        @Override
        public void emitTimeDriftWarning(String warningMessage) {
            if (mDestroyed) {
//                MSCLog.e("[BridgeTimerManager@emitTimeDriftWarning]", "TimingModule destroyed");
                return;
            }
            JSTimers jsTimers = getJSTimer();
            if (jsTimers == null) {
                return;
            }
            jsTimers.emitTimeDriftWarning(warningMessage);
        }
    }

    private final JavaTimerManager mJavaTimerManager;

    public TimingModule() {
        mJavaTimerManager = new JavaTimerManager(
                new BridgeTimerManager());
    }

    private JSTimers getJSTimer() {
        return getRuntime().getJSModuleDelegate(JSTimers.class);
    }

    @Override
    public void onRuntimeAttached(MSCRuntime runtime) {
        super.onRuntimeAttached(runtime);
        mJavaTimerManager.setServiceModule(getModule(AppService.class).getJsExecutor());
        mDestroyed = false;


        getRuntime().subscribe(JSCServiceEngine.MSC_EVENT_ENGINE_STATUS_CHANGED, new MSCSubscriber<EngineStatus>() {
            @Override
            public void onReceive(MSCEvent<EngineStatus> event) {
                if (event.getData() == EngineStatus.Released) {
                    onCatalystInstanceDestroy();
                }
            }
        });

        getRuntime().subscribe(IContainerManager.MSC_EVENT_CONTAINER_RESUMED, new MSCSubscriber() {
            @Override
            public void onReceive(MSCEvent event) {
                onHostResume();
            }
        });

        getRuntime().subscribe(IContainerManager.MSC_EVENT_CONTAINER_PAUSED, new MSCSubscriber() {
            @Override
            public void onReceive(MSCEvent event) {
                onHostPause();
            }
        });

        getRuntime().subscribe(IContainerManager.MSC_EVENT_CONTAINER_DESTROYED, new MSCSubscriber() {
            @Override
            public void onReceive(MSCEvent event) {
                onHostDestroy();
            }
        });
    }

    @Override
    public void onDestroy() {
        onHostDestroy();
        mDestroyed = true;
        MSCLog.i("[TimingModule@onDestroy]", "mDestroyed: ", mDestroyed);
        getRuntime().unsubscribe(JSCServiceEngine.MSC_EVENT_ENGINE_STATUS_CHANGED);
        getRuntime().unsubscribe(IContainerManager.MSC_EVENT_CONTAINER_RESUMED);
        getRuntime().unsubscribe(IContainerManager.MSC_EVENT_CONTAINER_PAUSED);
        getRuntime().unsubscribe(IContainerManager.MSC_EVENT_CONTAINER_DESTROYED);
        super.onDestroy();
    }

    @MSCMethod
    public void createTimer(
            final double callbackIDDouble,
            final double durationDouble,
            final double jsSchedulingTime,
            final boolean repeat) {
        final int callbackID = (int) callbackIDDouble;
        final int duration = (int) durationDouble;

        mJavaTimerManager.createAndMaybeCallTimer(callbackID, duration, jsSchedulingTime, repeat);
    }

    @MSCMethod
    public void deleteTimer(double timerIdDouble) {
        int timerId = (int) timerIdDouble;

        mJavaTimerManager.deleteTimer(timerId);
    }

    @MSCMethod
    public void setSendIdleEvents(final boolean sendIdleEvents) {
        mJavaTimerManager.setSendIdleEvents(sendIdleEvents);
    }

    public void onHostResume() {
        mJavaTimerManager.onHostResume();
    }

    public void onHostPause() {
        // MRN60 chendacai 计时器在页面进入后台的时候也能运行
        if (isEnableBackgroundTimer()) {
            return;
        }
        mJavaTimerManager.onHostPause();
    }

    public void onHostDestroy() {
        mJavaTimerManager.onHostDestroy();
    }

    public void onCatalystInstanceDestroy() {
        mJavaTimerManager.onInstanceDestroy();
    }

    @VisibleForTesting
    public boolean hasActiveTimersInRange(long rangeMs) {
        return mJavaTimerManager.hasActiveTimersInRange(rangeMs);
    }

    /**
     * 后台timer 禁用影响了msi灵犀埋点上报；具体暂时没时间排查原因和其他更合适的解决方案，先放开后台timer解决问题；
     * 同时添加回滚开关；
     * @return
     */
    public boolean isEnableBackgroundTimer() {
        return mEnableBackgroundTimer;
    }

    /**
     * MRN60 chendacai 计时器在页面进入后台的时候也能运行
     *
     * @param enable
     * @param promise
     */
    @MSCMethod
    public void enableBackgroundTimer(boolean enable, IMSCPromiseCallback<Boolean> promise) {
        mEnableBackgroundTimer = enable;
        promise.onSuccess(true);
    }
}
