package com.meituan.msc.modules.api.widget;

import android.view.View;

import com.meituan.msc.common.utils.DisplayUtil;
import com.meituan.msc.common.utils.HashMapHelper;
import com.meituan.msc.common.utils.JsonUtil;
import com.meituan.msc.modules.container.IContainerDelegate;
import com.meituan.msc.modules.container.IMSCContainer;
import com.meituan.msc.modules.container.WidgetEventListener;
import com.meituan.msc.modules.manager.MSCMethod;
import com.meituan.msc.modules.manager.MSCModule;
import com.meituan.msc.modules.manager.MSCNativeCompletableCallback;
import com.meituan.msc.modules.manager.ModuleName;
import com.meituan.msc.modules.reporter.MSCLog;

import org.json.JSONObject;

/**
 * MSC Widget 框架通信
 * https://km.sankuai.com/page/1318667905
 */
@ModuleName(name = "WidgetEventModule")
public class WidgetEventModule extends MSCModule {

    private static final String TAG = "WidgetEventModule";

    @MSCMethod
    public void triggerWidgetEvent(String eventName, JSONObject params, int pageId) {
        IContainerDelegate controller = getRuntime().getContainerManagerModule().getContainerDelegateByPageId(pageId);
        if (controller == null) {
            MSCLog.e(TAG, "triggerWidgetEvent but container not found");
            return;
        }
        if (!controller.isWidget()) {
            MSCLog.e(TAG, "triggerWidgetEvent but current container is not widget");
            return;
        }
        IMSCContainer container = controller.getMSCContainer();
        if (container instanceof WidgetEventListener) {
            ((WidgetEventListener) container).onWidgetEvent(eventName, JsonUtil.toMap(params));
        }
    }


    @MSCMethod
    public void getGlobalPosition(int pageId, MSCNativeCompletableCallback callback) {
        if (callback == null) return;
        IContainerDelegate controller = getRuntime().getContainerManagerModule().getContainerDelegateByPageId(pageId);
        if (controller == null) {
            MSCLog.e(TAG, "triggerWidgetEvent but container not found");
            callback.onComplete(JsonUtil.parseToJson(HashMapHelper.of("x", 0, "y", 0)));
            return;
        }
        IMSCContainer container = controller.getMSCContainer();
        View view = null;
        if (container != null && (view = container.getRootView()) != null && view.isAttachedToWindow()) {
            int[] outLocation = new int[2];
            view.getLocationInWindow(outLocation);
            callback.onComplete(JsonUtil.parseToJson(HashMapHelper.of("x", DisplayUtil.toWebValue(outLocation[0]),
                    "y", DisplayUtil.toWebValue(outLocation[1]))));
            return;
        }
        callback.onComplete(HashMapHelper.of("x", 0, "y", 0));
    }

}
