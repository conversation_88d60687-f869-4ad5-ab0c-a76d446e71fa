package com.meituan.msc.modules.apploader;

import android.support.annotation.NonNull;

import com.meituan.msc.common.aov_task.TaskManager;
import com.meituan.msc.common.aov_task.task.ITask;
import com.meituan.msc.common.support.java.util.concurrent.CompletableFuture;
import com.meituan.msc.modules.api.msi.navigation.BizNavigationExtraParams;
import com.meituan.msc.modules.engine.RuntimeStateBeforeLaunch;

import java.util.List;
import java.util.concurrent.ConcurrentHashMap;

public interface IAppLoader {
    String APP_PROP_UPDATED = "AppPropUpdated";
    String FRAMEWORK_PACKAGE_LOADED = "FrameworkPackageLoaded";
    String ALL_PACKAGES_READY_FOR_LAUNCH = "AllPackagesReadyForLaunch";
    String LOAD_FAILED = "LoadFailed";

    String NOTIFY_SHOW_YOUXUAN_REOPEN_DIALOG = "notify_show_youxuan_reopen_dialog";
    String NOTIFY_REOPEN_YOUXUAN_WIDGET = "notify_reopen_youxuan_widget";

    void launchPage(final String targetPath, ITask<?> startPageTask, boolean isLaunchFromRoute, boolean external,
                    int routeId, long routeTime, boolean isWidget, boolean isIgnoreRouteMapping, boolean isFirstLaunch, @NonNull BizNavigationExtraParams bizNavigationExtraParams);

    void launchInstrumentTask(final String targetPath, ITask<?> startPageTask, boolean external,
                    int routeId, long routeTime, boolean isWidget, boolean isIgnoreRouteMapping, @NonNull BizNavigationExtraParams bizNavigationExtraParams);

    void preload(String targetPath, int routeId, boolean isWidget, @NonNull BizNavigationExtraParams bizNavigationExtraParams);

    void preloadWebViewBasePackage();

    CompletableFuture preloadWebViewBizPackageOnly();

    boolean isUsed();

    boolean isFirstPageInLaunchStatus();

    boolean isLaunched();

    boolean isDestroyed();

    boolean isFailed();

    boolean isUsable();

    boolean isFrameworkReady();

    boolean isBizPreloadReady();

    void destroy();

    int getLoaderId();

    void setReload(boolean reload);

    boolean isReload();

    /**
     * 设置线下调试小程序元信息获取地址
     *
     * @param checkUpdateUrl url
     */
    void setCheckUpdateUrl(String checkUpdateUrl);

    String getCheckUpdateUrl();

    /**
     * 设置是否需要强制更新
     *
     * @param needForceUpdate needForceUpdate
     */
    void setNeedForceUpdate(boolean needForceUpdate);

    boolean needForceUpdate();

    /**
     * 设置线下调试小程序对应的基础包版本号
     *
     * @param mscVersionOfDebug version
     */
    void setBasePkgVersionOfDebug(String mscVersionOfDebug);

    String getBasePkgVersionOfDebug();

    void setStatusChangeListener(IEngineStatusChangeListener statusChangeListener);

    void setLaunched(boolean isLaunched);

    boolean isJSECreated();

    boolean isMetaInfoFetchSuccess();

    <TaskResult> List<ITask<TaskResult>> findLaunchTasksByClass(@NonNull Class<? extends ITask<TaskResult>> taskClass);

    RuntimeStateBeforeLaunch getRuntimeStateBeforeLaunch();

    void cleanTaskExecuteStateForReport();

    ConcurrentHashMap<String, String> getTaskExecuteStateForReport();

    void setBizPackageOffline();

    boolean isBizPreloadAndNoLaunched();

    List<String> getRunningTimeoutTasks(long timeout);

    CompletableFuture<Void> getFrameworkReadyFuture();

    TaskManager getTaskManager();
}
