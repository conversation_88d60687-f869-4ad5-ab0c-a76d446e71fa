package com.meituan.msc.modules.apploader;

import android.content.Intent;
import android.text.TextUtils;

import com.meituan.msc.common.utils.IntentUtil;
import com.meituan.msc.extern.MSCEnvHelper;
import com.meituan.msc.lib.interfaces.container.MSCParams;
import com.meituan.msc.modules.api.msi.navigation.BizNavigationExtraParams;
import com.meituan.msc.modules.apploader.launchtasks.InstrumentLaunchTask;
import com.meituan.msc.modules.container.ContainerController;
import com.meituan.msc.modules.container.ContainerDebugLaunchData;
import com.meituan.msc.modules.engine.EngineHelper;
import com.meituan.msc.modules.engine.MSCHornRollbackConfig;
import com.meituan.msc.modules.engine.MSCRuntime;
import com.meituan.msc.modules.engine.RuntimeDestroyReason;
import com.meituan.msc.modules.engine.RuntimeManager;
import com.meituan.msc.modules.page.Page;
import com.meituan.msc.modules.reporter.MSCLog;
import com.meituan.msc.modules.update.MSCHornBasePackageReloadConfig;
import com.meituan.msc.modules.update.metainfo.AppCheckUpdateManager;

import java.util.HashMap;
import java.util.Map;
import java.util.concurrent.ConcurrentHashMap;
import java.util.concurrent.atomic.AtomicInteger;

public class InstrumentLaunchManager {
    private static final String TAG = "InstrumentLaunchManager";

    private Map<String, PageLaunchInfo> launchInfoMap = new ConcurrentHashMap<>();
    private static InstrumentLaunchManager sInstance;

    private static AtomicInteger sRouteId = new AtomicInteger(1);

    public static synchronized InstrumentLaunchManager getInstance() {
        if (sInstance == null) {
            synchronized (InstrumentLaunchManager.class) {
                if (sInstance == null) {
                    sInstance = new InstrumentLaunchManager();
                }
            }
        }

        return sInstance;
    }

    public void startLaunch(Intent intent) {
        if (!MSCHornRollbackConfig.enableLaunchTaskOnRoute()) {
            return;
        }

        //生成routeId，并将routeId放到intent中
        String routeId = String.valueOf(sRouteId.getAndAdd(1));
        intent.putExtra(MSCParams.MSC_INNER_ROUTE_ID, routeId);
        long routeStartTime = System.currentTimeMillis();

        String appId = ContainerController.defaultGetMPAppId(intent);
        String targetPath = IntentUtil.getStringExtra(intent, MSCParams.TARGET_PATH);
        ContainerDebugLaunchData debugLaunchData = new ContainerDebugLaunchData(intent);
        boolean disableReuseAny = intent.getBooleanExtra(MSCParams.DISABLE_REUSE_ANY, false);
        //保障msc完成初始化
        MSCEnvHelper.ensureFullInited();

        MSCLog.i(TAG, "instrument routeId ", routeId);
        PageLaunchInfo launchInfo = new PageLaunchInfo(Integer.parseInt(routeId), true);
        launchInfo.setRouteStartTime(routeStartTime);
        //创建引擎
        boolean isUrlExternalApp = IntentUtil.getBooleanExtra(intent, "externalApp", false);
        MSCRuntime runtime = createRuntime(appId, targetPath, debugLaunchData, disableReuseAny, isUrlExternalApp);
        launchInfo.setRuntime(runtime);
        InstrumentLaunchTask task = new InstrumentLaunchTask();
        launchInfo.setInstrumentLaunchTask(task);
        //初始状态可使用，在Activity.onCreate时机执行launchPage时，如果选择引擎不是一个，canUseInstrumentTask设置为false
        launchInfo.canUseInstrumentRuntime = true;
        launchInfoMap.put(routeId, launchInfo);
        boolean disablePrefetch = IntentUtil.getBooleanExtra(intent, MSCParams.DISABLE_PREFETCH, false);
        BizNavigationExtraParams bizNavigationExtraParams = new BizNavigationExtraParams.Builder()
                .setDisablePrefetch(disablePrefetch)
                .build();
        //发起任务
        IAppLoader appLoader = runtime.getModule(IAppLoader.class);
        appLoader.launchInstrumentTask(targetPath, task,false, Integer.parseInt(routeId), System.currentTimeMillis(), false, false, bizNavigationExtraParams);
    }

    public void addLaunchInfo(String routeId, PageLaunchInfo launchInfo) {
        launchInfoMap.put(routeId, launchInfo);
    }

    MSCRuntime createRuntime(String appId, String targetPath, ContainerDebugLaunchData debugLaunchData, boolean disableReuseAny, boolean isUrlExternalApp) {
        AppCheckUpdateManager.getInstance().checkUpdateBeforeRuntimeInit(appId, debugLaunchData);
        MSCRuntime runtime = EngineHelper.setupRuntimeForLaunch(appId, targetPath, debugLaunchData, disableReuseAny, isUrlExternalApp);
        runtime.getMSCAppModule().setUrlExternalApp(isUrlExternalApp);
        return runtime;
    }

    public PageLaunchInfo getLaunchInfo(String routeId) {
        return launchInfoMap.get(routeId);
    }

    public void removeLaunchInfo(String routeId) {
        MSCLog.i(TAG, "removeLaunchInfo " + routeId);
        if (!TextUtils.isEmpty(routeId) && launchInfoMap != null && launchInfoMap.containsKey(routeId)) {
            launchInfoMap.remove(routeId);
        }
    }

    public void removeLaunchInfo(MSCRuntime runtime) {
        try {
            if (runtime == null) {
                return;
            }
            MSCLog.i(TAG, "removeLaunchInfo runtime " + runtime);
            for (Map.Entry<String, PageLaunchInfo> entry : launchInfoMap.entrySet()) {
                PageLaunchInfo pageLaunchInfo = entry.getValue();
                //路由是否启动任务标记保留。路由时机启动任务执行失败，引擎销毁
                if (pageLaunchInfo != null && pageLaunchInfo.getMscRuntime() == runtime) {
                    pageLaunchInfo.setInstrumentLaunchTask(null);
                    pageLaunchInfo.setStartPageTask(null);
                    pageLaunchInfo.setRuntime(null);
                }
            }
        } catch (Exception e) {
            MSCLog.e(TAG, "removeLaunchInfo exception", e);
        }
    }

    public MSCRuntime getRuntime(String routeId) {
        PageLaunchInfo launchInfo = launchInfoMap.get(routeId);
        if (launchInfo == null) {
            MSCLog.i(TAG, "return lanchInfo is null, routeId " + routeId);
            return null;
        }

        MSCRuntime runtime = launchInfo.getMscRuntime();

        if (runtime == null) {
            MSCLog.i(TAG, "launchInfoMap is not exist");
            return null;
        }

        Map<MSCRuntime, RuntimeDestroyReason> runtimeDestroyPendingMap = new HashMap<>();
        if (RuntimeManager.canUseRuntime(runtime, false, runtimeDestroyPendingMap)) {
            return runtime;
        }

        if (!runtimeDestroyPendingMap.isEmpty()) {
            RuntimeManager.delayAsyncDestroyRuntimes(runtimeDestroyPendingMap);
        }

        return null;
    }
}
