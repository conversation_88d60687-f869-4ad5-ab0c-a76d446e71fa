package com.meituan.msc.modules.apploader;

import android.app.Activity;
import android.os.Build;
import android.text.TextUtils;

import com.meituan.android.common.horn.Horn;
import com.meituan.android.common.horn.HornCallback;
import com.meituan.msc.common.utils.ScreenUtil;
import com.meituan.msc.extern.MSCEnvHelper;
import com.meituan.msc.lib.BuildConfig;
import com.meituan.msc.modules.engine.MSCApp;
import com.meituan.msc.modules.engine.MSCRuntime;
import com.meituan.msc.modules.manager.IMSCCompletableCallback;
import com.meituan.msc.modules.manager.MSCMethod;
import com.meituan.msc.modules.manager.MSCModule;
import com.meituan.msc.modules.manager.ModuleName;
import com.meituan.msc.modules.reporter.MSCLog;
import com.meituan.msi.util.DisplayUtil;
import com.meituan.msi.util.MsiBugFixConfig;

import org.json.JSONException;
import org.json.JSONObject;

@ModuleName(name = "LaunchInfo")
public class LaunchInfo extends MSCModule {

    public final String TAG = "LaunchInfo@" + Integer.toHexString(hashCode());
    private volatile JSONObject feHornConfig = null;
    private static final int RELEASE = 0;
    public static final String FROM_SERVICE = "service";
    public static final String FROM_WEBVIEW_INJECT = "webview_inject";
    public static final String FROM_WEBVIEW_ASYNC = "webview_async";

    @MSCMethod(isSync = true)
    public Object baseInfo() {
        JSONObject baseSystemInfo = new JSONObject();
        try {
            getBaseInfo(baseSystemInfo);
            MSCRuntime runtime = getRuntime();
            boolean isPreload = runtime != null && runtime.isPreload();
            baseSystemInfo.put("isPreload", isPreload);
            Activity activity = getActivity();
            String appId = null;
            if (runtime != null) {
                appId = runtime.getAppId();
            }
            int[] widthHeight = ScreenUtil.getScreenWidthAndHeight(activity, appId);
            if (!MsiBugFixConfig.getBugFixHornConfig().enableUseDisplayMetrics && !MsiBugFixConfig.getBugFixHornConfig().containsEnabledReferer(appId) && activity != null) {
                widthHeight[1] += DisplayUtil.getStatusBarHeight();
            }

            baseSystemInfo.put("screenWidth", ScreenUtil.divide(widthHeight[0], ScreenUtil.getDensity()));
            baseSystemInfo.put("screenHeight", ScreenUtil.divide(widthHeight[1], ScreenUtil.getDensity()));
            baseSystemInfo.put("pixelRatio", ScreenUtil.getDensity());
            baseSystemInfo.put("shareMiniProgramType", getShareMiniProgramType(runtime));
        } catch (JSONException e) {
            e.printStackTrace();
        }
        return baseSystemInfo;
    }

    private Activity getActivity() {
        return getRuntime().getContainerManagerModule() == null ?
                null : getRuntime().getContainerManagerModule().getTopActivity();
    }

    /**
     * 需求文档：https://km.sankuai.com/page/**********
     *
     * @return
     */
    @MSCMethod(isSync = true)
    public Object hornConfig() {
        MSCLog.i(TAG, "hornConfig 逻辑层同步调用");
        return getHornConfig(FROM_SERVICE);
    }

    /**
     * 12.26.400
     * <a href="https://km.sankuai.com/collabpage/**********">视图层注入horn&accountInfo及异步桥调用</a>
     */
    @MSCMethod(isSync = false)
    public void hornConfigAsync(IMSCCompletableCallback<JSONObject> callback) {
        MSCLog.i(TAG, "hornConfigAsync 视图层异步回调");
        callback.onComplete(getHornConfig(FROM_WEBVIEW_ASYNC));
    }

    public synchronized JSONObject getHornConfig(String from) {
        String appId = getRuntime().getMSCAppModule().getAppId();
        String logMessage = "getHornConfig, from:" + from + ", appId:" + appId + ", runtime:" + getRuntime().TAG;
        // 已获取过一次 hornConfig, 直接返回缓存
        if (feHornConfig != null) {
            MSCLog.i(TAG, logMessage, "使用缓存, horn length:", feHornConfig.toString().length());
            return feHornConfig;
        }
        // horn 配置尚未注册
        if (!feFileRegistered) {
            feHornConfig = new JSONObject();
            MSCLog.i(TAG, logMessage, "horn 配置尚未注册, 返回空JSON");
            return feHornConfig;
        }
        String result = Horn.accessCache("msc_fe_framework");
        if (TextUtils.isEmpty(result)) {
            feHornConfig = new JSONObject();
            MSCLog.i(TAG, logMessage, "horn 配置为空, 返回空JSON");
            return feHornConfig;
        }
        try {
            feHornConfig = new JSONObject(result);
        } catch (Exception e) {
            MSCLog.w(TAG, logMessage, "转换result报错, 返回空JSON, e:", e);
            feHornConfig = new JSONObject();
        }
        MSCLog.i(TAG, logMessage, "horn 配置获取成功, 返回JSON, length:", result.length());
        return feHornConfig;
    }

    @MSCMethod(isSync = false)
    public void accountInfoAsync(IMSCCompletableCallback<JSONObject> callback) {
        MSCLog.i(TAG, "accountInfoAsync 视图层异步回调");
        JSONObject accountInfo = getAccountInfo(FROM_WEBVIEW_ASYNC);
        callback.onComplete(accountInfo);
    }

    public synchronized JSONObject getAccountInfo(String from) {
        String logMessage = "getAccountInfo, from:" + from + ", runtime:" + getRuntime().TAG;
        JSONObject result = new JSONObject();
        String appId = getRuntime().getMSCAppModule().getAppId();
        String appName = getRuntime().getMSCAppModule().getAppName();
        try {
            result.put("appId", appId);
            result.put("appName", appName);
            result.put("version", getRuntime().getMSCAppModule().getMSCAppVersion());
            result.put("buildId", getRuntime().getMSCAppModule().getBuildId());
            result.put("publishId", getRuntime().getMSCAppModule().getPublishId());
        } catch (Exception e) {
            MSCLog.w(TAG, "getAccountInfo exception:", e);
        }
        MSCLog.i(TAG, logMessage, "accountInfo 获取成功, appId:", appId, ", accountInfo:", result.toString());
        return result;
    }

    @MSCMethod(isSync = true)
    public Object getMSCAppState() {
        JSONObject result = new JSONObject();
        try {
            MSCApp app = getApp();
            result.put("state", (app != null && app.isAppInForeground()) ? "foreground" : "background");
        } catch (JSONException e) {
            e.printStackTrace();
        }
        return result;
    }

    // 常量
    private static final String system = "Android " + Build.VERSION.RELEASE;//与微信保持一致
    private static final String platform = "android";
    private static final String SDKVersion = "2.2.3";
    // FIXME: 2022/2/17 mmpSDKVersion 取值从哪开始
    private static final String mscSDKVersion = BuildConfig.MSC_SDK_VERSION + "." + BuildConfig.AAR_VERSION;

    /**
     * 除用于getSystemInfo API外，也会在启动后第一时间注入至前端框架，提供环境信息
     * 获取的所有参数都是不可变的
     */
    public static void getBaseInfo(JSONObject result) throws JSONException {
        result.put("brand", Build.BRAND);
        result.put("model", Build.MODEL);
        result.put("system", system);
        result.put("platform", platform);
        result.put("SDKVersion", SDKVersion);
        result.put("mscSDKVersion", mscSDKVersion);
        result.put("appVersion", MSCEnvHelper.getEnvInfo().getAppVersionName());
        // 宿主 App 可以选择性提供 appId 信息,如果提供，该信息将会出现在 getSystemInfo、getSystemInfoSync 等 API 的返回结果中.
        if (!TextUtils.isEmpty(MSCEnvHelper.getEnvInfo().getAppCode())) {
            result.put("app", MSCEnvHelper.getEnvInfo().getAppCode());
        }
//        if (MSCEnvHelper.getCustomServiceEngineClazz() == JSCServiceEngine.class) {
        result.put("V8", 1);    //JS引擎版本，前端主要用于判断是否V8，目前JS引擎没有可获取的版本号，由客户端写死，有需要时人工修改
//        }
    }


    //记录配置是否注册过
    public static boolean feFileRegistered;

    /**
     * 未注册过的配置全部注册一遍
     */
    public static void registerHorn() {
        Horn.register("msc_fe_framework", new HornCallback() {
            @Override
            public void onChanged(boolean enable, String result) {
            }
        });
        feFileRegistered = true;
    }

    /**
     * 获取小程序是测试版/体验版/正式版 aop
     * https://km.sankuai.com/collabpage/1747350135
     */
    private int getShareMiniProgramType(MSCRuntime runtime) {
        return RELEASE;
    }
}
