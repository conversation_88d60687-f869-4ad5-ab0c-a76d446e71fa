package com.meituan.msc.modules.apploader;

import com.meituan.android.common.weaver.interfaces.Weaver;
import com.meituan.android.common.weaver.interfaces.diagnose.PageStepLayer;
import com.meituan.android.common.weaver.interfaces.diagnose.PageStepLevel;
import com.meituan.msc.common.aov_task.TaskManager;
import com.meituan.msc.common.aov_task.context.ITaskExecuteContext;
import com.meituan.msc.common.aov_task.task.ITask;
import com.meituan.msc.common.support.java.util.concurrent.CompletableFuture;
import com.meituan.msc.common.utils.MPConcurrentHashMap;
import com.meituan.msc.modules.engine.MSCHornRollbackConfig;
import com.meituan.msc.modules.reporter.MSCLog;
import com.meituan.msc.util.perf.PerfTrace;

public class LaunchTaskManager extends TaskManager {
    public final String TAG = "LaunchTaskManager" + "@" + Integer.toHexString(hashCode());

    public final MPConcurrentHashMap<String, String> taskExecuteStateMap = new MPConcurrentHashMap<>();

    private ITaskStatusListener taskStatusListener;

    @Override
    protected CompletableFuture<?> executeTask(ITask<?> task, ITaskExecuteContext executeContext) {
        String taskName = task.getName();
        MSCLog.i(TAG, "start to execute ", task.isAsyncTask() ? "async" : "sync",
                "task: ", task, ",source:", source);
        taskExecuteStateMap.put(taskName, "executing");
        PerfTrace.online().begin(taskName).eventId(task.hashCode()).report();
        Weaver.newPageStep(PageStepLayer.CONTAINER).level(PageStepLevel.ONLINE_METRICS).asyncBegin(taskName);
        return super.executeTask(task, executeContext);
    }

    @Override
    protected void handleExecuteSuccess(ITask<?> task, Object result) {
        String taskName = task.getName();
        PerfTrace.online().end(taskName).eventId(task.hashCode()).report();
        Weaver.newPageStep(PageStepLayer.CONTAINER).level(PageStepLevel.ONLINE_METRICS).asyncEnd(taskName);
        taskExecuteStateMap.put(taskName, "execute success");
        if (!MSCHornRollbackConfig.isRollbackPendingFrameWorkReady()) {
            taskStatusListener.onSuccess(taskName);
        }
        super.handleExecuteSuccess(task, result);
    }

    @Override
    protected void handleExecuteException(Throwable exception, ITask<?> task) {
        MSCLog.e(TAG, exception, "Failed to execute ", task.isAsyncTask() ? "async" : "sync",
                "task: ", task, "Exception: ", exception,
                ",source:", source);
        String taskName = task.getName();
        taskExecuteStateMap.put(taskName, "execute failed:" + exception);
        if (!MSCHornRollbackConfig.isRollbackPendingFrameWorkReady()) {
            taskStatusListener.onFail(taskName, exception);
        }
        super.handleExecuteException(exception, task);
    }

    @Override
    public String toString() {
        return TAG;
    }

    public void cleanTaskExecuteStateMap() {
        taskExecuteStateMap.clear();
    }

    public MPConcurrentHashMap<String, String> getTaskExecuteStateMap() {
        return taskExecuteStateMap;
    }

    public void setTaskStatusListener(ITaskStatusListener taskStatusListener) {
        this.taskStatusListener = taskStatusListener;
    }

    public interface ITaskName {
        String CREATE_JSE_TASK = "CreateJsEngine";
        String FETCH_BASE_PKG_TASK = "FetchBasePackage";
        String FETCH_BUZ_PKG_TASK = "FetchBuzPkgTask";
        String FETCH_META_INFO_TASK = "FetchMetaInfo";
        String INJECT_BASE_PKG_TASK = "InjectBasePackage";
        String INJECT_BUZ_PKG_TASK = "InjectBuzPkgTask";
        String PATH_CFG_TASK = "SetLaunchPath";
        String PRE_SEND_ON_PAGE_START = "PreSendOnPageStart";
        String PRE_INIT_RENDER_TASK = "PreInitWebViewTask";
        String SERVICE_PRE_INIT_TASK = "ServicePreInitTask";
        String START_PAGE_BY_ROUTE_TASK = "startPageByRoute";
        String START_PAGE_OF_LAUNCH_TASK = "StartPageTaskOfLaunch";
        String WEB_VIEW_PRELOAD_BASE_TASK = "WebViewPreloadBaseTask";
        String DATA_PREFETCH_TASK = "DataPrefetchTask";
        String PATH_CHECK_TASK = "PathCheckTask";
        String FETCH_SUB_BUZ_PKG_TASK = "FetchSubBuzPkgTask";
        String BUNDLE_LOAD_TASK = "BundleLoadTask";
        String PRE_PARSE_CSS_TASK = "PreParseCssTask";
        String FETCH_CONFIG_PKG_TASK = "FetchConfigPkgTask";
        String RENDER_PRELOAD_RESOURCE_TASK = "RenderPreloadResourceTask";
    }
}
