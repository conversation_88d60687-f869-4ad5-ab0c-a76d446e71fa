package com.meituan.msc.modules.apploader;


import android.content.Context;
import android.os.SystemClock;
import android.support.annotation.NonNull;
import android.text.TextUtils;

import com.meituan.msc.common.aov_task.ExceptionHandler;
import com.meituan.msc.common.aov_task.ExecuteStatus;
import com.meituan.msc.common.aov_task.TaskManager;
import com.meituan.msc.common.aov_task.exception.TaskNonexistentException;
import com.meituan.msc.common.aov_task.task.ITask;
import com.meituan.msc.common.config.MSCConfig;
import com.meituan.msc.common.framework.MPListenerManager;
import com.meituan.msc.common.framework.MSCRunningManager;
import com.meituan.msc.common.support.java.util.concurrent.CompletableFuture;
import com.meituan.msc.common.utils.Constants;
import com.meituan.msc.common.utils.ExceptionHelper;
import com.meituan.msc.common.utils.MPConcurrentHashMap;
import com.meituan.msc.extern.MSCEnvHelper;
import com.meituan.msc.modules.api.msi.navigation.BizNavigationExtraParams;
import com.meituan.msc.modules.apploader.events.AppLoadException;
import com.meituan.msc.modules.apploader.launchtasks.BundlePreloadTask;
import com.meituan.msc.modules.apploader.launchtasks.CreateJSETask;
import com.meituan.msc.modules.apploader.launchtasks.DataPrefetchTask;
import com.meituan.msc.modules.apploader.launchtasks.FetchBasePkgTask;
import com.meituan.msc.modules.apploader.launchtasks.FetchBuzPkgTask;
import com.meituan.msc.modules.apploader.launchtasks.FetchConfigPkgTask;
import com.meituan.msc.modules.apploader.launchtasks.FetchMetaInfoTask;
import com.meituan.msc.modules.apploader.launchtasks.FetchSubBuzPkgTask;
import com.meituan.msc.modules.apploader.launchtasks.InjectBasePkgTask;
import com.meituan.msc.modules.apploader.launchtasks.InjectBuzPkgTask;
import com.meituan.msc.modules.apploader.launchtasks.PathCfgTask;
import com.meituan.msc.modules.apploader.launchtasks.PathCheckTask;
import com.meituan.msc.modules.apploader.launchtasks.PreInitRenderTask;
import com.meituan.msc.modules.apploader.launchtasks.PreParseCssTask;
import com.meituan.msc.modules.apploader.launchtasks.PreSendOnPageStartTask;
import com.meituan.msc.modules.apploader.launchtasks.RenderPreloadResourceTask;
import com.meituan.msc.modules.apploader.launchtasks.SendOnAppRouteTask;
import com.meituan.msc.modules.apploader.launchtasks.ServicePreInitTask;
import com.meituan.msc.modules.apploader.launchtasks.StartPageFinishTask;
import com.meituan.msc.modules.apploader.launchtasks.WebViewPreloadBaseTask;
import com.meituan.msc.modules.container.ContainerController;
import com.meituan.msc.modules.engine.MSCApp;
import com.meituan.msc.modules.engine.MSCHornRollbackConfig;
import com.meituan.msc.modules.engine.MSCRuntime;
import com.meituan.msc.modules.engine.RuntimeSource;
import com.meituan.msc.modules.engine.RuntimeStateBeforeLaunch;
import com.meituan.msc.modules.engine.dataprefetch.MSCHornDynamicPrefetchConfig;
import com.meituan.msc.modules.manager.MSCEvent;
import com.meituan.msc.modules.manager.MSCMethod;
import com.meituan.msc.modules.manager.MSCModule;
import com.meituan.msc.modules.manager.ModuleName;
import com.meituan.msc.modules.page.render.ICssPreParseManager;
import com.meituan.msc.modules.page.render.IRendererCreator;
import com.meituan.msc.modules.page.render.webview.WebViewFirstPreloadStateManager;
import com.meituan.msc.modules.preload.MSCHornPreloadConfig;
import com.meituan.msc.modules.reporter.MSCLoadErrorConstants;
import com.meituan.msc.modules.reporter.MSCLog;
import com.meituan.msc.modules.reporter.ReporterFields;
import com.meituan.msc.modules.service.IServiceEngine;
import com.meituan.msc.modules.update.bean.AppMetaInfoWrapper;
import com.meituan.msc.modules.update.bean.PackageInfoWrapper;
import com.meituan.msc.modules.update.pkg.MSCLoadPackageScene;
import com.meituan.msc.util.perf.PerfTrace;
import com.sankuai.meituan.serviceloader.ServiceLoader;

import java.util.ArrayList;
import java.util.Collection;
import java.util.List;
import java.util.Map;
import java.util.Set;
import java.util.concurrent.ConcurrentHashMap;


@ModuleName(name = "AppLoader")
public class MSCAppLoader extends MSCModule implements IAppLoader {

    public final String TAG = "MSCAppLoader" + "@" + Integer.toHexString(hashCode());

    protected final Context mContext;

    protected volatile boolean mReload;
    private volatile String checkUpdateUrl;
    /**
     * 是否强制拉取 MetaInfo，默认走缓存过期失效策略
     */
    private volatile boolean needForceUpdate = false;
    private volatile String basePkgVersionOfDebug;

    private volatile boolean isLaunched;
    private volatile boolean isFailed;
    private volatile boolean isDestroyed;
    private volatile boolean isUsed;
    /**
     * 业务包被下线，禁止复用当前运行时启动
     */
    private boolean isBizPackageOffline;

    /**
     * 是否需要触发业务预热
     */
    private volatile boolean isBizPreloadNeeded = false;


    private IEngineStatusChangeListener statusChangeListener;

    private LaunchTaskManager taskManager;

    private List<IRendererCreator> rendererCreators;

    private CompletableFuture<Void> frameworkReadyFuture;

    /**
     * 不允许直接new 请用EngineManager创建
     */
    public MSCAppLoader(Context context) {
        mContext = context.getApplicationContext();
        initTaskManager();
    }

    @Override
    public void onAppStart(MSCApp app) {
        MSCLog.i(TAG, taskManager, "onAppStart", app.getAppId());

        if (isUsed) {
            MSCLog.e(TAG, "already used: " + getAppOrThrow().getAppId());
            return;
        }
        // TODO: 2022/5/24 tianbin 业务预热时也会执行onAppStart，此时运行时并未被使用
        isUsed = true;
        MSCRunningManager.startApp(app.getAppId(), this);
        addFetchMetaInfoTaskIfNotExist();
    }


    private void initTaskManager() {
        taskManager = new LaunchTaskManager();
        if (!MSCHornRollbackConfig.isRollbackPendingFrameWorkReady()) {
            frameworkReadyFuture = new CompletableFuture<>();
            taskManager.setTaskStatusListener(new ITaskStatusListener() {
                @Override
                public void onSuccess(String taskName) {
                    if (taskName.equals(LaunchTaskManager.ITaskName.INJECT_BASE_PKG_TASK)) {
                        frameworkReadyFuture.complete(null);
                    }
                }

                @Override
                public void onFail(String taskName, Throwable exception) {

                }
            });
        }
        taskManager.setExceptionHandler(new ExceptionHandler() {
            @Override
            public void handleException(Throwable exception, ITask<?> task, TaskManager taskManager) {
                MSCLog.i(TAG, "Task dependency graph:", taskManager.buildGravizoImageUrl());
                onFailed(ExceptionHelper.createAppLoadException(exception));
            }
        });
    }


    @Override
    public void onRuntimeAttached(MSCRuntime runtime) {
        super.onRuntimeAttached(runtime);
        MSCLog.i(TAG, "onRuntimeAttached", runtime);
        LaunchInfo launchInfoModule = new LaunchInfo();
        MSCLog.i(TAG, "[LaunchInfo] onRuntimeAttached runtime:", runtime.TAG, ", launchInfoModule:", launchInfoModule.TAG);
        getRuntime().registerModule(launchInfoModule);

        // 初始化运行时加载的启动任务
        CreateJSETask createJSETask = new CreateJSETask(getRuntime());
        ITask downloadBasePkgTask = addDownloadBaseTaskIfNotExist();
        InjectBasePkgTask injectBasePkgTask = new InjectBasePkgTask(getRuntime());
        taskManager.addTask(createJSETask);
        taskManager.addTask(injectBasePkgTask, createJSETask, downloadBasePkgTask);
    }

    private void onCreateJSETask(ITask<IServiceEngine> createJSETask) {
        addRendererTasks(createJSETask);
    }

    /**
     * 注入基础库包
     */
    public CompletableFuture<PackageInfoWrapper> injectBasePackage(String source, String basePkgVersionOfDebug) {
        MSCLog.i(TAG, "injectBasePackage", source, basePkgVersionOfDebug);
        MSCLog.d(source, "加载基础库包，依赖关系图:", taskManager.buildGravizoImageUrl());
        taskManager.setSource(source);

        // 线下调试流程 支持 预热指定版本基础包
        setBasePkgVersionForDebug(basePkgVersionOfDebug);

        // 基础库预热场景下，预创建native渲染器
        ITask<IServiceEngine> createJSETask = taskManager.findTaskByClassOrThrow(CreateJSETask.class);
        onCreateJSETask(createJSETask);
        // webview预热task
        if (MSCHornPreloadConfig.needPreloadWebView() && MSCHornPreloadConfig.needWebViewInjectBasePackage()) {
            WebViewFirstPreloadStateManager.PreloadState state = WebViewFirstPreloadStateManager.getInstance().getPreloadState();
            // 防止在APP冷启动的时候占用主线程时间。即：冷启动不从这里触发。
            if (state == WebViewFirstPreloadStateManager.PreloadState.WEBVIEW_PRECREATE
                    || state == WebViewFirstPreloadStateManager.PreloadState.WEBVIEW_PREINJECT) {
                addWebViewBaseTaskIfNotExist();
            }
        }


        ITask<PackageInfoWrapper> injectBasePkgTask = taskManager.findTaskByClassOrThrow(InjectBasePkgTask.class);
        return taskManager.executeAllTasks()
                .thenApply(taskManager -> taskManager.getTaskResult(injectBasePkgTask));
    }

    public CompletableFuture<Void> preloadAppPackage(String basePkgVersionOfDebug, String appId, String targetPath, boolean preloadWebViewPage) {
        MSCLog.i(TAG, "preloadAppPackage", appId, targetPath, basePkgVersionOfDebug);
        // 线下调试流程 支持 预热指定版本基础包
        setBasePkgVersionForDebug(basePkgVersionOfDebug);
        return executeDataTasks(Constants.PRELOAD_BIZ, targetPath, preloadWebViewPage || isPreloadWebViewAtBizPreloadByMSC(), 0, System.currentTimeMillis(), false, new BizNavigationExtraParams.Builder().build());
    }

    public CompletableFuture<Void> preloadTargetPathForExistingRuntime(String targetPath, boolean preloadWebViewPage) {
        MSCLog.i(TAG, "preloadTargetPathForExistingRuntime", getRuntime().getAppId(), targetPath);
        taskManager.setSource(Constants.PRELOAD_BIZ_PAGE);
        ITask<AppMetaInfoWrapper> fetchMetaInfoTask = taskManager.findTaskByClass(FetchMetaInfoTask.class);
        ITask<String> pathCfgTask;
        pathCfgTask = new PathCfgTask(getRuntime(), targetPath, true);
        taskManager.addTask(pathCfgTask, fetchMetaInfoTask);
        FetchBuzPkgTask fetchBuzPkgTask = new FetchBuzPkgTask(getRuntime(), MSCLoadPackageScene.LOAD_PACKAGE_TYPE_BIZ_PRE_LOAD);
        taskManager.addTask(fetchBuzPkgTask, pathCfgTask, fetchMetaInfoTask);
        PathCheckTask pathCheckTask = new PathCheckTask(targetPath, getRuntime().getAppConfigModule());
        FetchSubBuzPkgTask fetchSubBuzPkgTask = new FetchSubBuzPkgTask(getRuntime(), MSCLoadPackageScene.LOAD_PACKAGE_TYPE_BIZ_PRE_LOAD);
        if (MSCConfig.enableRouteMappingFix()) {
            taskManager.addTask(pathCheckTask, pathCfgTask, fetchBuzPkgTask);
            taskManager.addTask(fetchSubBuzPkgTask, pathCheckTask, fetchMetaInfoTask);
        }
        InjectBuzPkgTask injectBuzPkgTask = new InjectBuzPkgTask(getRuntime());
        if (MSCConfig.enableRouteMappingFix()) {
            taskManager.addTask(injectBuzPkgTask, fetchSubBuzPkgTask, taskManager.findTaskByClassOrThrow(CreateJSETask.class));
        } else {
            taskManager.addTask(injectBuzPkgTask, fetchBuzPkgTask, taskManager.findTaskByClassOrThrow(CreateJSETask.class));
        }
        if (preloadWebViewPage) {
            if (MSCConfig.enableRouteMappingFix()) {
                taskManager.addTask(new RenderPreloadResourceTask(getRuntime()), pathCheckTask, fetchSubBuzPkgTask);
            } else {
                taskManager.addTask(new RenderPreloadResourceTask(getRuntime()), pathCfgTask, fetchBuzPkgTask);
            }
        }
        MSCLog.i(TAG, "executeTargetPathPreloadTasks，依赖关系图:", taskManager.buildGravizoImageUrl());
        return taskManager.executeAllTasks()
                .thenApply(taskManager -> taskManager.getTaskResult(injectBuzPkgTask));
    }

    public boolean enablePreCssParse() {
        ICssPreParseManager cssPreParseManager = getRuntime().getCssPreParseManagerWithoutDelegate();
        return cssPreParseManager != null && cssPreParseManager.enablePreParseCssV2();
    }

    private CompletableFuture<Void> executeDataTasks(String source, String targetPath, boolean preloadWebViewPage, int routeId, long routeTime, boolean isWidget, @NonNull BizNavigationExtraParams bizNavigationExtraParams) {
        return executeDataTasks(source, targetPath, preloadWebViewPage, routeId, routeTime, isWidget, false, false, null, bizNavigationExtraParams);
    }

    private CompletableFuture<Void> executeDataTasks(String source, String targetPath, boolean preloadWebViewPage, int routeId, long routeTime,
                                                     boolean isWidget, boolean external, boolean isIgnoreRouteMapping, ITask<?> finishTask, @NonNull BizNavigationExtraParams bizNavigationExtraParams) {
        taskManager.setSource(source);
        List<ITask<?>> finishDependTasks = new ArrayList<>();
        ITask<String> pathCfgTask = null;
        String loadScene = MSCLoadPackageScene.LOAD_PACKAGE_TYPE_BIZ_PRE_LOAD;
        if (TextUtils.equals(source, Constants.LAUNCH) || TextUtils.equals(source, Constants.ROUTE_LAUNCH)) {
            loadScene = MSCLoadPackageScene.LOAD_PACKAGE_TYPE_LAUNCH;
        }
        FetchBuzPkgTask fetchBuzPkgTask = new FetchBuzPkgTask(getRuntime(), loadScene);
        ITask<AppMetaInfoWrapper> fetchMetaInfoTask = addFetchMetaInfoTaskIfNotExist();
        if (TextUtils.equals(source, Constants.ROUTE_LAUNCH)) {
            pathCfgTask = new PathCfgTask(getRuntime(), targetPath, external, isIgnoreRouteMapping, true);
        } else {
            pathCfgTask = new PathCfgTask(getRuntime(), targetPath, true);
        }
        taskManager.addTask(pathCfgTask, fetchMetaInfoTask);

        taskManager.addTask(fetchBuzPkgTask, pathCfgTask, fetchMetaInfoTask);
        PathCheckTask pathCheckTask = new PathCheckTask(targetPath, getRuntime().getAppConfigModule());
        FetchSubBuzPkgTask downgradeFetchSubBuzPkgTask = new FetchSubBuzPkgTask(getRuntime(), loadScene);
        PreParseCssTask preParseCssTask = new PreParseCssTask(getRuntime(), ICssPreParseManager.STAGE_PRELOAD);
        if (MSCConfig.enableRouteMappingFix()) {
            taskManager.addTask(pathCheckTask, pathCfgTask, fetchBuzPkgTask);
            taskManager.addTask(downgradeFetchSubBuzPkgTask, pathCheckTask, fetchMetaInfoTask);
            addCreateNativeRenderTasks(pathCheckTask, downgradeFetchSubBuzPkgTask);
            if (enablePreCssParse()) {
                taskManager.addTask(preParseCssTask, downgradeFetchSubBuzPkgTask, pathCheckTask, pathCfgTask, fetchMetaInfoTask);
                finishDependTasks.add(preParseCssTask);
            }
        } else {
            addCreateNativeRenderTasks(pathCfgTask, fetchBuzPkgTask);
            if (enablePreCssParse()) {
                taskManager.addTask(preParseCssTask, fetchBuzPkgTask, pathCfgTask, pathCfgTask, fetchMetaInfoTask);
                finishDependTasks.add(preParseCssTask);
            }
        }

        if (!Constants.PRELOAD_BIZ.equals(source) && !TextUtils.equals(source, Constants.ROUTE_LAUNCH)) {
            // 业务预热无需发送
            if (MSCConfig.enableRouteMappingFix()) {
                addBundlePreloadTask(pathCheckTask, taskManager.findTaskByClass(InjectBasePkgTask.class));
            } else {
                addBundlePreloadTask(pathCfgTask, taskManager.findTaskByClass(InjectBasePkgTask.class));
            }
        }

        if (MSCHornRollbackConfig.enablePrefetchOptimizer() && !taskManager.containsTask(FetchConfigPkgTask.class)) {
            FetchConfigPkgTask fetchConfigPkgTask = new FetchConfigPkgTask(getRuntime());
            taskManager.addTask(fetchConfigPkgTask, fetchMetaInfoTask);
        }
        if (routeId > 0 && MSCHornDynamicPrefetchConfig.enablePrefetch() && (!MSCHornRollbackConfig.enableControlPrefetchInSpecialScene() || !bizNavigationExtraParams.disablePrefetch)) {
            MSCLog.i(TAG, "add DataPrefetchTask ", routeId);
            DataPrefetchTask dataPrefetchTask = new DataPrefetchTask(getRuntime(), routeId, routeTime, isWidget);
            if (MSCConfig.enableRouteMappingFix()) {
                taskManager.addTask(dataPrefetchTask, pathCheckTask, fetchMetaInfoTask);
            } else {
                taskManager.addTask(dataPrefetchTask, pathCfgTask, fetchMetaInfoTask);
            }
            finishDependTasks.add(dataPrefetchTask);
        }

        InjectBuzPkgTask injectBuzPkgTask = new InjectBuzPkgTask(getRuntime());
        if (enablePreCssParse()) {
            if (MSCConfig.enableRouteMappingFix()) {
                taskManager.addTask(injectBuzPkgTask, downgradeFetchSubBuzPkgTask, taskManager.findTaskByClassOrThrow(CreateJSETask.class), preParseCssTask);
            } else {
                taskManager.addTask(injectBuzPkgTask, fetchBuzPkgTask, taskManager.findTaskByClassOrThrow(CreateJSETask.class), preParseCssTask);
            }
        } else {
            if (MSCConfig.enableRouteMappingFix()) {
                taskManager.addTask(injectBuzPkgTask, downgradeFetchSubBuzPkgTask, taskManager.findTaskByClassOrThrow(CreateJSETask.class));
            } else {
                taskManager.addTask(injectBuzPkgTask, fetchBuzPkgTask, taskManager.findTaskByClassOrThrow(CreateJSETask.class));
            }
        }
        finishDependTasks.add(injectBuzPkgTask);

        if (preloadWebViewPage || isBizPreloadNeeded || TextUtils.equals(source, Constants.ROUTE_LAUNCH)) {
            boolean isPreloadWebViewPage = !TextUtils.equals(source, Constants.ROUTE_LAUNCH);
            PreInitRenderTask preInitRenderTask = null;
            if (TextUtils.equals(source, Constants.ROUTE_LAUNCH) && getApp() != null && MSCHornRollbackConfig.isEnablePrePageStartOptimize(getApp().getAppId())) {
                preInitRenderTask = new PreInitRenderTask(getRuntime(), true);
                if (MSCConfig.enableRouteMappingFix()) {
                    taskManager.addTask(preInitRenderTask, pathCheckTask, downgradeFetchSubBuzPkgTask, taskManager.findTaskByClass(FetchBasePkgTask.class));
                } else {
                    taskManager.addTask(preInitRenderTask, pathCfgTask, fetchBuzPkgTask, taskManager.findTaskByClass(FetchBasePkgTask.class));
                }
            } else {
                if (MSCConfig.enableRouteMappingFix()) {
                    preInitRenderTask = addPreInitTaskIfNotExist(pathCheckTask, downgradeFetchSubBuzPkgTask, isPreloadWebViewPage);
                } else {
                    preInitRenderTask = addPreInitTaskIfNotExist(pathCfgTask, fetchBuzPkgTask, isPreloadWebViewPage);
                }
            }
            if (preInitRenderTask != null) {
                finishDependTasks.add(preInitRenderTask);
            }
        }

        // 由于目前业务预热场景不会创建PageModule，因此native页面和WebView页面都只进行逻辑层预初始化
        if (source.equals(Constants.PRELOAD_BIZ)) {
            MSCLog.i(TAG, "enableServicePreInit horn on");
            ServicePreInitTask servicePreInitTask = new ServicePreInitTask(getRuntime(), preloadWebViewPage);
            if (MSCConfig.enableRouteMappingFix()) {
                taskManager.addTask(servicePreInitTask, injectBuzPkgTask, pathCheckTask, fetchMetaInfoTask, taskManager.findTaskByClass(InjectBasePkgTask.class));
            } else {
                taskManager.addTask(servicePreInitTask, injectBuzPkgTask, pathCfgTask, fetchMetaInfoTask, taskManager.findTaskByClass(InjectBasePkgTask.class));
            }
        }

        MSCLog.d(TAG, "preloadAppPackage，依赖关系图:", taskManager.buildGravizoImageUrl());

        taskManager.setSource(source);
        //启动场景，按指定任务执行
        if (TextUtils.equals(source, Constants.ROUTE_LAUNCH)) {
            taskManager.addTask(finishTask, finishDependTasks.toArray(new ITask<?>[0]));
            return taskManager.executeTargetTask(finishTask).thenApply(taskManager -> taskManager.getTaskResult(injectBuzPkgTask));
        }

        //触发执行启动任务
        return taskManager.executeAllTasks()
                .thenApply(taskManager -> taskManager.getTaskResult(injectBuzPkgTask));
    }

    private boolean isPreloadWebViewAtBizPreloadByMSC() {
        return MSCHornPreloadConfig.isPreloadWebViewAtBizPreload(getRuntime().getAppId());
    }

    private void addCreateNativeRenderTasks(ITask<?> pathCheckTask, ITask<?> fetchBuzPkgTask) {
        // RN渲染器 & Native渲染器 在业务预热、启动场景，根据页面类型创建，减少多余预热的负向影响
        ITask<IServiceEngine> createJSETask = taskManager.findTaskByClassOrThrow(CreateJSETask.class);
        addRendererTasks(createJSETask, pathCheckTask, fetchBuzPkgTask);
    }

    private List<IRendererCreator> getRendererCreators() {
        if (rendererCreators == null) {
            synchronized (MSCAppLoader.class) {
                if (rendererCreators == null) {
                    rendererCreators = ServiceLoader.load(IRendererCreator.class, null);
                }
            }
        }
        return rendererCreators;
    }

    private void onLaunchPage(ITask<?> fetchBuzPkgTask, List<ITask<?>> dependList) {
        List<IRendererCreator> rendererCreators = getRendererCreators();
        if (rendererCreators != null) {
            for (IRendererCreator rendererCreator : rendererCreators) {
                rendererCreator.onAppLoaderLaunchPage(taskManager, getRuntime(), fetchBuzPkgTask, dependList);
            }
        }
    }

    private void addRendererTasks(ITask<?>... dependList) {
        List<IRendererCreator> rendererCreators = getRendererCreators();
        if (rendererCreators != null) {
            for (IRendererCreator rendererCreator : rendererCreators) {
                synchronized (this) {
                    rendererCreator.addRendererTasks(taskManager, getRuntime(), dependList);
                }
            }
        }
    }

    private boolean isPreloadTaskRunning() {
        List<IRendererCreator> rendererCreators = getRendererCreators();
        if (rendererCreators != null) {
            boolean isPreload = false;
            for (IRendererCreator rendererCreator : rendererCreators) {
                isPreload = isPreload || rendererCreator.isAppLoaderPreloadTaskRunning(taskManager);
            }
            return isPreload;
        } else {
            return false;
        }
    }

    private void setBasePkgVersionForDebug(String basePkgVersionOfDebug) {
        if (TextUtils.isEmpty(basePkgVersionOfDebug)) {
            return;
        }
        if (MSCEnvHelper.getEnvInfo().isProdEnv()) {
            return;
        }
        ITask<PackageInfoWrapper> task = taskManager.findTaskByClassOrThrow(FetchBasePkgTask.class);
        if (task instanceof FetchBasePkgTask) {
            ((FetchBasePkgTask) task).setBasePkgVersionOfDebug(basePkgVersionOfDebug);
        }
    }

    @Override
    public int getLoaderId() {
        return hashCode();
    }

    @Override
    public boolean isFirstPageInLaunchStatus() {
        if (isLaunched() && getApp() != null && !getApp().isPageLaunched) {
            ITask<?> task = taskManager.findTaskByClass(ContainerController.StartPageTaskOfLaunch.class);
            MSCLog.i("isFirstPageInLaunchStatus", "task found", task);
            if (task != null) {
                ExecuteStatus taskResult = taskManager.getExecuteStatus(task);
                ContainerController.StartPageTaskOfLaunch startPageTask = (ContainerController.StartPageTaskOfLaunch) task;
                if (!startPageTask.isWidget() && taskResult.isPending()) {
                    boolean isAdded1SecondsAgo = startPageTask.isAdded1SecondsAgo();
                    getApp().getRuntime().getRuntimeReporter().once(ReporterFields.MSC_LAUNCH_MULTI_SKIP).tag("isAdded1SecondsAgo", isAdded1SecondsAgo).sendDelay();
                    // 暂时只限制 1s 内添加的重复任务，避免触及了启动中的其他风险
                    if (!isAdded1SecondsAgo) {
                        return true;
                    } else {
                        return false;
                    }
                }
            }
        }
        return false;
    }

    @Override
    public boolean isLaunched() {
        return isLaunched;
    }

    @Override
    public boolean isDestroyed() {
        return isDestroyed;
    }

    @Override
    public boolean isFailed() {
        return isFailed;
    }

    @Override
    public boolean isUsable() {
        return !isFailed && !isDestroyed && !isBizPackageOffline;
    }

    @Override
    public boolean isFrameworkReady() {
        return taskManager.getExecuteStatus(InjectBasePkgTask.class).equals(ExecuteStatus.SUCCEED);
    }

    // FIXME: 2023/3/3 tianbin 存在子包的场景，通过Class无法准确获取状态
    @Override
    public boolean isBizPreloadReady() {
        return taskManager.getExecuteStatus(InjectBuzPkgTask.class).equals(ExecuteStatus.SUCCEED);
    }

    /**
     * 小程序实际开始前台运行
     * 目前每个AppEngine实例仅可调用此方法一次
     * 预热的小程序mLoadSuccessSubPackList一定为空，与无预热启动不一致，在启动的时候如果直接启动子包，会走页面跳转启动的逻辑
     */
    @Override
    public void launchPage(final String targetPath, ITask<?> startPageTask, boolean isLaunchFromRoute, boolean external, int routeId, long routeTime, boolean isWidget, boolean isIgnoreRouteMapping, boolean isFirstLaunch, @NonNull BizNavigationExtraParams bizNavigationExtraParams) {
        PerfTrace.begin("launchPage");
        MSCLog.i(TAG, "launchPage", taskManager.TAG, targetPath, startPageTask);
        getRuntime().getRuntimeReporter().resetReportDataWhenLaunchPage();
        if (MSCHornRollbackConfig.enableLaunchTaskOnRoute()) {
            long launchPageStartTime = System.currentTimeMillis();
            MSCLog.i(TAG, "  launchPage " + routeId);
            PageLaunchInfo pageLaunchInfo = InstrumentLaunchManager.getInstance().getLaunchInfo(String.valueOf(routeId));
            if (pageLaunchInfo != null) {
                if (pageLaunchInfo.getMscRuntime() != getRuntime()) {
                    pageLaunchInfo.canUseInstrumentRuntime = false;
                }
            } else { //二级页面跳转按终点任务查找个任务执行时间
                pageLaunchInfo = new PageLaunchInfo(routeId);
                pageLaunchInfo.setRouteStartTime(routeTime);
                InstrumentLaunchManager.getInstance().addLaunchInfo(String.valueOf(routeId), pageLaunchInfo);
            }
            pageLaunchInfo.setStartPageTask(startPageTask);
            pageLaunchInfo.setLaunchPageStartTime(launchPageStartTime);
        }

        ITask<AppMetaInfoWrapper> fetchMetaInfoTask = addFetchMetaInfoTaskIfNotExist();
        ITask<String> pathCfgTask;

        pathCfgTask = new PathCfgTask(getRuntime(), targetPath, external, isIgnoreRouteMapping, isLaunchFromRoute);
        taskManager.addTask(pathCfgTask, fetchMetaInfoTask);

        FetchBuzPkgTask fetchBuzPkgTask = new FetchBuzPkgTask(getRuntime(), MSCLoadPackageScene.LOAD_PACKAGE_TYPE_LAUNCH, routeId);
        // 启动场景fetchBuzPkgTask下载主包，内部路由场景fetchBuzPkgTask下载子包
        taskManager.addTask(fetchBuzPkgTask, pathCfgTask, fetchMetaInfoTask);
        PathCheckTask pathCheckTask = new PathCheckTask(targetPath, getRuntime().getAppConfigModule());
        ITask<?> lastFetchBuzPkgTask = fetchBuzPkgTask;
        PreParseCssTask preParseCssTask = new PreParseCssTask(getRuntime(), ICssPreParseManager.STAGE_LAUNCH_PAGE);
        if (MSCConfig.enableRouteMappingFix()) {
            taskManager.addTask(pathCheckTask, pathCfgTask, fetchBuzPkgTask);
            if (!isLaunchFromRoute) {
                // 启动场景FetchSubBuzPkgTask拉子包
                FetchSubBuzPkgTask downgradeFetchSubBuzPkgTask = new FetchSubBuzPkgTask(getRuntime(), MSCLoadPackageScene.LOAD_PACKAGE_TYPE_LAUNCH);
                taskManager.addTask(downgradeFetchSubBuzPkgTask, pathCheckTask, fetchMetaInfoTask);
                lastFetchBuzPkgTask = downgradeFetchSubBuzPkgTask;
            }
            addCreateNativeRenderTasks(pathCheckTask, lastFetchBuzPkgTask);
            if (enablePreCssParse()) {
                taskManager.addTask(preParseCssTask, pathCheckTask, lastFetchBuzPkgTask, pathCfgTask, fetchMetaInfoTask);
            }
        } else {
            addCreateNativeRenderTasks(pathCfgTask, fetchBuzPkgTask);
            if (enablePreCssParse()) {
                taskManager.addTask(preParseCssTask, pathCfgTask, fetchBuzPkgTask, fetchMetaInfoTask);
            }
        }
        String appId = getRuntime().getAppId();
        List<ITask<?>> startPageTaskDependList = new ArrayList<>();
        List<ITask<?>> appRouteTaskDependList = new ArrayList<>();
        List<ITask<?>> startPageFinishTaskDependList = new ArrayList<>();
        if (MSCHornRollbackConfig.enablePreSendOnPageStart()) {
            PreSendOnPageStartTask preSendOnPageStartTask = new PreSendOnPageStartTask(getRuntime());
            if (MSCConfig.enableRouteMappingFix()) {
                taskManager.addTask(preSendOnPageStartTask, lastFetchBuzPkgTask, pathCheckTask);
            } else {
                taskManager.addTask(preSendOnPageStartTask, lastFetchBuzPkgTask, pathCfgTask);
            }
            if (isAppRouteTaskEnabled(appId, isFirstLaunch)) {
                startPageFinishTaskDependList.add(preSendOnPageStartTask);
            } else {
                startPageTaskDependList.add(preSendOnPageStartTask);
            }
        }


        InjectBuzPkgTask injectBuzPkgTask = new InjectBuzPkgTask(getRuntime());
        InjectBasePkgTask injectBasePkgTask = (InjectBasePkgTask) taskManager.findTaskByClassOrThrow(InjectBasePkgTask.class);
        BundlePreloadTask bundlePreloadTask;
        if (MSCConfig.enableRouteMappingFix()) {
            bundlePreloadTask = addBundlePreloadTask(pathCheckTask, injectBasePkgTask);
        } else {
            bundlePreloadTask = addBundlePreloadTask(pathCfgTask, injectBasePkgTask);
        }
        if (enablePreCssParse()) {
            if (MSCConfig.enableRouteMappingFix()) {
                taskManager.addTask(injectBuzPkgTask, pathCheckTask, lastFetchBuzPkgTask, injectBasePkgTask, taskManager.findTaskByClassOrThrow(CreateJSETask.class), preParseCssTask);
            } else {
                taskManager.addTask(injectBuzPkgTask, pathCfgTask, fetchBuzPkgTask, injectBasePkgTask, taskManager.findTaskByClassOrThrow(CreateJSETask.class), preParseCssTask);
            }
        } else {
            if (MSCConfig.enableRouteMappingFix()) {
                taskManager.addTask(injectBuzPkgTask, pathCheckTask, lastFetchBuzPkgTask, injectBasePkgTask, taskManager.findTaskByClassOrThrow(CreateJSETask.class));
            } else {
                taskManager.addTask(injectBuzPkgTask, pathCfgTask, fetchBuzPkgTask, injectBasePkgTask, taskManager.findTaskByClassOrThrow(CreateJSETask.class));
            }
        }
        PreInitRenderTask preInitRenderTask;
        if (MSCHornRollbackConfig.isEnablePrePageStartOptimize(appId)) {
            preInitRenderTask = new PreInitRenderTask(getRuntime(), true);
            if (MSCConfig.enableRouteMappingFix()) {
                taskManager.addTask(preInitRenderTask, pathCheckTask, lastFetchBuzPkgTask, taskManager.findTaskByClass(FetchBasePkgTask.class));
            } else {
                taskManager.addTask(preInitRenderTask, pathCfgTask, fetchBuzPkgTask, taskManager.findTaskByClass(FetchBasePkgTask.class));
            }
        } else {
            if (MSCConfig.enableRouteMappingFix()) {
                preInitRenderTask = addPreInitTaskIfNotExist(pathCheckTask, lastFetchBuzPkgTask, false);
            } else {
                preInitRenderTask = addPreInitTaskIfNotExist(pathCfgTask, fetchBuzPkgTask, false);
            }
        }

        if (isAppRouteTaskEnabled(appId, isFirstLaunch)) {
            startPageTaskDependList.add(preInitRenderTask);
            onLaunchPage(lastFetchBuzPkgTask, startPageTaskDependList);
            startPageTaskDependList.add(lastFetchBuzPkgTask);
            taskManager.addTask(startPageTask, startPageTaskDependList.toArray(new ITask<?>[0]));

            appRouteTaskDependList.add(startPageTask);
            appRouteTaskDependList.add(injectBasePkgTask);

            startPageFinishTaskDependList.add(injectBuzPkgTask);
        } else {
            startPageTaskDependList.add(taskManager.findTaskByClassOrThrow(InjectBasePkgTask.class));
            startPageTaskDependList.add(injectBuzPkgTask);
            startPageTaskDependList.add(preInitRenderTask);
        }

        //数据预拉取
        if (MSCHornDynamicPrefetchConfig.enablePrefetch() && (!MSCHornRollbackConfig.enableControlPrefetchInSpecialScene() || !bizNavigationExtraParams.disablePrefetch)) {
            DataPrefetchTask dataPrefetchTask = new DataPrefetchTask(getRuntime(), routeId, routeTime, isWidget);
            if (MSCConfig.enableRouteMappingFix()) {
                taskManager.addTask(dataPrefetchTask, pathCheckTask, fetchMetaInfoTask);
            } else {
                taskManager.addTask(dataPrefetchTask, pathCfgTask, fetchMetaInfoTask);
            }
            if (isAppRouteTaskEnabled(appId, isFirstLaunch)) {
                startPageFinishTaskDependList.add(dataPrefetchTask);
            } else {
                startPageTaskDependList.add(dataPrefetchTask);
            }
        } else {
            MSCLog.i("MSCDynamicDataPrefetch", "DataPrefetchTask DynamicPrefetch disable isWidget ", isWidget);
        }
        if (bundlePreloadTask != null) {
            startPageTaskDependList.add(bundlePreloadTask);
        }
        StartPageFinishTask startPageFinishTask = new StartPageFinishTask();

        if (isAppRouteTaskEnabled(appId, isFirstLaunch)) {
            SendOnAppRouteTask sendOnAppRouteTask = new SendOnAppRouteTask(getRuntime());
            taskManager.addTask(sendOnAppRouteTask, appRouteTaskDependList.toArray(new ITask<?>[0]));

            startPageFinishTaskDependList.add(sendOnAppRouteTask);
            taskManager.addTask(startPageFinishTask, startPageFinishTaskDependList.toArray(new ITask<?>[0]));
        } else {
            onLaunchPage(lastFetchBuzPkgTask, startPageTaskDependList);
            taskManager.addTask(startPageTask, startPageTaskDependList.toArray(new ITask<?>[0]));
        }
        getRuntime().addLoadedTargetPath(targetPath, true);

        MSCLog.d(Constants.LAUNCH, "launchPage，依赖关系图:", taskManager.buildGravizoImageUrl());

        taskManager.setSource(Constants.LAUNCH);
        //触发执行启动任务
        if (!MSCHornRollbackConfig.isRollbackStartPageAdvanced(appId)) {
            if ((MSCHornRollbackConfig.isEnablePrePageStartOptimize(appId) && isLaunchFromRoute)
                    || getRuntime().getSource() == RuntimeSource.KEEP_ALIVE || getRuntime().getSource() == RuntimeSource.BIZ_PRELOAD) {
                // 保活场景、业务预热场景由于包已经注入完了，可以直接在当前线程执行startPage，不用再抛到下一次lopper中了
                // TODO chdc 优化子包场景
                // fixme: 1.executeAllTasks在启动异常场景下未能正常结束任务 2.executeTargetTask只能同时执行一个任务，执行多个任务存在互相覆盖的情况
                if (isAppRouteTaskEnabled(appId, isFirstLaunch)) {
                    taskManager.executeTargetTask(startPageFinishTask);
                } else {
                    taskManager.executeTargetTask(startPageTask);
                }
            } else {
                if (isAppRouteTaskEnabled(appId, isFirstLaunch)) {
                    taskManager.executeTargetTaskAsync(startPageFinishTask);
                } else {
                    taskManager.executeTargetTaskAsync(startPageTask);
                }
            }
        } else {
            // 回滚
            if (isAppRouteTaskEnabled(appId, isFirstLaunch)) {
                taskManager.executeTargetTaskAsync(startPageFinishTask);
            } else {
                taskManager.executeTargetTaskAsync(startPageTask);
            }
        }
        PerfTrace.end("launchPage");
    }

    @Override
    public void launchInstrumentTask(final String targetPath, ITask<?> startPageTask, boolean external,
                              int routeId, long routeTime, boolean isWidget, boolean isIgnoreRouteMapping, @NonNull BizNavigationExtraParams bizNavigationExtraParams) {
        PerfTrace.begin("launchInstrumentTask");
        MSCLog.i(TAG, "launchInstrumentTask", taskManager.TAG, targetPath);
        executeDataTasks(Constants.ROUTE_LAUNCH, targetPath, false, routeId, routeTime, isWidget, external, isIgnoreRouteMapping, startPageTask, bizNavigationExtraParams);

        PerfTrace.end("launchInstrumentTask");
    }

    // https://ones.sankuai.com/ones/product/9659/workItem/defect/detail/65189471?activeTabName=first
    // 修复launchPage先于onAppStart执行导致FetchMetaInfoTask找不到的问题
    private ITask<AppMetaInfoWrapper> addFetchMetaInfoTaskIfNotExist() {
        ITask<AppMetaInfoWrapper> fetchMetaInfoTask;
        synchronized (this) {
            fetchMetaInfoTask = taskManager.findTaskByClass(FetchMetaInfoTask.class);
            if (fetchMetaInfoTask == null) {
                MSCLog.i(TAG, "addFetchMetaInfoTaskIfNotExist fetchMetaInfoTask is null");
                fetchMetaInfoTask = new FetchMetaInfoTask(getRuntime());
                taskManager.addTask(fetchMetaInfoTask);
            }
        }
        return fetchMetaInfoTask;
    }

    private ITask<PackageInfoWrapper> addDownloadBaseTaskIfNotExist() {
        ITask<PackageInfoWrapper> downloadBasePkgTask;
        synchronized (this) {
            downloadBasePkgTask = taskManager.findTaskByClass(FetchBasePkgTask.class);
            if (downloadBasePkgTask == null) {
                MSCLog.i(TAG, "addDownloadBaseTaskIfNotExist createDownloadBasePkgTask is null");
                downloadBasePkgTask = new FetchBasePkgTask(getRuntime());
                taskManager.addTask(downloadBasePkgTask);
            }
        }
        return downloadBasePkgTask;
    }

    private ITask<PackageInfoWrapper> addWebViewBaseTaskIfNotExist() {
        ITask<PackageInfoWrapper> webViewBaseTask;
        synchronized (this) {
            webViewBaseTask = taskManager.findTaskByClass(WebViewPreloadBaseTask.class);
            if (webViewBaseTask == null) {
                MSCLog.i(TAG, "addWebViewBaseTaskIfNotExist webViewBaseTask is null");
                webViewBaseTask = new WebViewPreloadBaseTask(getRuntime());
                taskManager.addTask(webViewBaseTask, addDownloadBaseTaskIfNotExist());
            }
        }
        return webViewBaseTask;
    }

    private PreInitRenderTask addPreInitTaskIfNotExist(ITask pathCheckTask, ITask downloadBuzPkgTask) {
        return addPreInitTaskIfNotExist(pathCheckTask, downloadBuzPkgTask, true);
    }

    private PreInitRenderTask addPreInitTaskIfNotExist(ITask pathCheckTask, ITask downloadBuzPkgTask, boolean preloadWebViewPage) {
        PreInitRenderTask preInitRenderTask;
        synchronized (this) {
            preInitRenderTask = (PreInitRenderTask) taskManager.findTaskByClass(PreInitRenderTask.class);
            if (null == preInitRenderTask) {
                MSCLog.i(TAG, "addPreInitTaskIfNotExist, create one and add.");
                preInitRenderTask = new PreInitRenderTask(getRuntime(), preloadWebViewPage, false);
                taskManager.addTask(preInitRenderTask, pathCheckTask, downloadBuzPkgTask, taskManager.findTaskByClass(FetchBasePkgTask.class));
            } else {
                MSCLog.i(TAG, "addPreInitTaskIfNotExist, already exist.");
            }
        }
        return preInitRenderTask;
    }

    @Override
    public CompletableFuture preloadWebViewBizPackageOnly() {
        isBizPreloadNeeded = true;
        if (MSCConfig.enableRouteMappingFix()) {
            ITask pathCheck = taskManager.findTaskByClass(PathCheckTask.class);
            ITask downloadBuz = taskManager.findTaskByClass(FetchSubBuzPkgTask.class);
            if (downloadBuz == null) {
                downloadBuz = taskManager.findTaskByClass(FetchBuzPkgTask.class);
            }
            if (null != pathCheck && null != downloadBuz) {
                addPreInitTaskIfNotExist(pathCheck, downloadBuz);
                return taskManager.executeAllTasks();
            }
        } else {
            ITask pathCfg = taskManager.findTaskByClass(PathCfgTask.class);
            ITask downloadBuz = taskManager.findTaskByClass(FetchBuzPkgTask.class);
            if (null != pathCfg && null != downloadBuz) {
                addPreInitTaskIfNotExist(pathCfg, downloadBuz);
                return taskManager.executeAllTasks();
            }
        }
        return CompletableFuture.failedFuture(new RuntimeException("PathCheckTask and DownloadBuzPkgTask can't find."));
    }

    @Override
    public void preload(String targetPath, int routeId, boolean isWidget, @NonNull BizNavigationExtraParams bizNavigationExtraParams) {
        MSCLog.i(TAG, "preload", targetPath, "routeId", routeId);
        executeDataTasks(Constants.LAUNCH, targetPath, false, routeId, System.currentTimeMillis(), isWidget, bizNavigationExtraParams);
    }

    @Override
    public void preloadWebViewBasePackage() {
        MSCLog.i("webviewInjectBase", "preloadBasePackage step2 start");
        MSCLog.d(TAG, "preloadBasePackage，依赖关系图:", taskManager.buildGravizoImageUrl());
        addWebViewBaseTaskIfNotExist();
        //触发执行启动任务
        taskManager.executeAllTasks();
    }

    @Override
    public void destroy() {
        if (isDestroyed) {
            MSCLog.w(TAG, "already destroyed: " + getRuntime());
            return;
        }
        this.isDestroyed = true;
        MSCLog.i(TAG, "engine destroy: ", getRuntime());
    }


    @MSCMethod
    public void onRuntimeLaunch(String param, String webIds) {
        MPListenerManager.getInstance().launchListener.onServiceRuntimeLaunch(getRuntime().getAppId());
    }

    private void onFailed(AppLoadException exception) {
        if (exception == null) return;
        isFailed = true;
        getRuntime().getRuntimeReporter().reportLoadFail(exception);
        getRuntime().publish(new MSCEvent(MSCAppLoader.LOAD_FAILED, exception));
    }

    @Override
    public boolean isUsed() {
        return isUsed;
    }

    public void onEngineInitFailed(Exception e) {
        AppLoadException exception;
        // 引擎初始化失败 错误码用于提示启动失败，不会上报到msc.load.error.count
        if (e instanceof AppLoadException) {
            exception = (AppLoadException) e;
        } else {
            exception = new AppLoadException(MSCLoadErrorConstants.ERROR_CREATE_JS_ENGINE_ERROR, "创建引擎失败", e);
        }
        onFailed(exception);
    }

    @Override
    public String toString() {
        return "AppEngine{" + (getApp() != null ? getApp().getAppId() : null) + " @" + getLoaderId() + "}";
    }


    /**
     * forDebug 不使用缓存启动，注意设置的同时还需要不加入EngineManager管理
     */
    @Override
    public void setReload(boolean reload) {
        mReload = reload;
    }

    @Override
    public boolean isReload() {
        return mReload;
    }


    /**
     * forDebug 指定请求更新地址
     */
    @Override
    public void setCheckUpdateUrl(String checkUpdateUrl) {
        this.checkUpdateUrl = checkUpdateUrl;
    }

    @Override
    public String getCheckUpdateUrl() {
        if (MSCEnvHelper.getEnvInfo().isProdEnv()) {
            return null;
        }
        return checkUpdateUrl;
    }

    @Override
    public void setNeedForceUpdate(boolean needForceUpdate) {
        this.needForceUpdate = needForceUpdate;
    }

    @Override
    public boolean needForceUpdate() {
        return needForceUpdate;
    }

    @Override
    public void setBasePkgVersionOfDebug(String mscVersionOfDebug) {
        this.basePkgVersionOfDebug = mscVersionOfDebug;
    }

    @Override
    public String getBasePkgVersionOfDebug() {
        return basePkgVersionOfDebug;
    }

    @Override
    public void setStatusChangeListener(IEngineStatusChangeListener statusChangeListener) {
        this.statusChangeListener = statusChangeListener;
    }

    public IEngineStatusChangeListener getStatusChangeListener() {
        return statusChangeListener;
    }

    public void onKeepAlive() {
        if (statusChangeListener != null) {
            statusChangeListener.onKeepAlive(getRuntime());
        }
    }

    @Override
    public void setLaunched(boolean isLaunched) {
        if (this.isLaunched != isLaunched) {
            this.isLaunched = isLaunched;
            MSCLog.d(TAG, "setLaunched", getRuntime().getAppId());
            if (statusChangeListener != null) {
                // 这里引擎状态改为启动状态了
                statusChangeListener.onLaunchStatusChanged(getRuntime(), isLaunched);
            }
        }
    }

    @Override
    public boolean isJSECreated() {
        ITask<IServiceEngine> createJSETask = taskManager.findTaskByClass(CreateJSETask.class);
        if (createJSETask == null) {
            return false;
        }
        return taskManager.getExecuteStatus(createJSETask).equals(ExecuteStatus.SUCCEED);
    }

    @Override
    public boolean isMetaInfoFetchSuccess() {
        ITask<AppMetaInfoWrapper> fetchMetaInfoTask = taskManager.findTaskByClass(FetchMetaInfoTask.class);
        if (fetchMetaInfoTask == null) {
            MSCLog.w(this.toString(), "fetchMetaInfoTask is null");
            return false;
        }
        try {
            return taskManager.getExecuteStatus(fetchMetaInfoTask).equals(ExecuteStatus.SUCCEED);
        } catch (TaskNonexistentException e) {
            MSCLog.e(this.toString(), "fetchMetaInfoTask getExecuteStatus error");
            throw e;
        }
    }

    @Override
    public <TaskResult> List<ITask<TaskResult>> findLaunchTasksByClass(@NonNull Class<? extends ITask<TaskResult>> taskClass) {
        return taskManager.findTasksByClass(taskClass);
    }

    @Override
    public RuntimeStateBeforeLaunch getRuntimeStateBeforeLaunch() {
        RuntimeStateBeforeLaunch runtimeStateBeforeLaunch = getRuntime().getRuntimeStateBeforeLaunch();
        if (runtimeStateBeforeLaunch == RuntimeStateBeforeLaunch.BIZ_PRELOADING_FROM_NEW) {
            if (isBizPreloadTaskRunning()) {
                return RuntimeStateBeforeLaunch.BIZ_PRELOADING_FROM_NEW;
            } else {
                return RuntimeStateBeforeLaunch.BIZ_PRELOAD_FROM_NEW;
            }
        } else if (runtimeStateBeforeLaunch == RuntimeStateBeforeLaunch.BIZ_PRELOADING_FROM_BASE) {
            if (isBizPreloadTaskRunning()) {
                return RuntimeStateBeforeLaunch.BIZ_PRELOADING_FROM_BASE;
            } else {
                return RuntimeStateBeforeLaunch.BIZ_PRELOAD_FROM_BASE;
            }
        } else if (runtimeStateBeforeLaunch == RuntimeStateBeforeLaunch.BASE_PRELOADING) {
            if (isBasePreloadTaskRunning()) {
                return RuntimeStateBeforeLaunch.BASE_PRELOADING;
            } else {
                return RuntimeStateBeforeLaunch.BASE_PRELOAD;
            }
        }
        return runtimeStateBeforeLaunch;
    }

    private boolean isBizPreloadTaskRunning() {
        if (isBasePreloadTaskRunning()) {
            return true;
        }
        InjectBuzPkgTask injectBuzPkgTask = (InjectBuzPkgTask) taskManager.findTaskByClass(InjectBuzPkgTask.class);
        if (injectBuzPkgTask != null && taskManager.getExecuteStatus(injectBuzPkgTask) != ExecuteStatus.SUCCEED) {
            return true;
        }
        ServicePreInitTask servicePreInitTask = (ServicePreInitTask) taskManager.findTaskByClass(ServicePreInitTask.class);
        if (servicePreInitTask != null && taskManager.getExecuteStatus(servicePreInitTask) != ExecuteStatus.SUCCEED) {
            return true;
        }
        PreInitRenderTask preInitRenderTask = (PreInitRenderTask) taskManager.findTaskByClass(PreInitRenderTask.class);
        return preInitRenderTask != null && taskManager.getExecuteStatus(preInitRenderTask) != ExecuteStatus.SUCCEED;
    }

    /**
     * 获取正在执行超时的任务
     * @param timeout 超时时间，单位ms
     * @return 超时的任务列表
     */
    @Override
    public List<String> getRunningTimeoutTasks(long timeout) {
        if (taskManager == null) {
            return new ArrayList<>();
        }

        Collection<ITask<?>> tasks = taskManager.getAllTasks();
        List<String> timeoutTasks = new ArrayList<>();
        for (ITask<?> task : tasks) {
            ExecuteStatus executeStatus = taskManager.getExecuteStatus(task);
            if (executeStatus == ExecuteStatus.RUNNING) {
                long taskStartTime = taskManager.getExecuteStartTime(task);
                long runningDuration = SystemClock.elapsedRealtime() - taskStartTime;
                MSCLog.i(TAG, task.getName(), runningDuration);
                if (runningDuration > timeout) {
                    timeoutTasks.add(task.getName());
                }
            }
        }
        return timeoutTasks;
    }

    private boolean isBasePreloadTaskRunning() {
        InjectBasePkgTask injectBasePkgTask = (InjectBasePkgTask) taskManager.findTaskByClass(InjectBasePkgTask.class);
        if (injectBasePkgTask != null && taskManager.getExecuteStatus(injectBasePkgTask) != ExecuteStatus.SUCCEED) {
            return true;
        }
        return isPreloadTaskRunning();
    }

    private BundlePreloadTask addBundlePreloadTask(ITask<String> pathTask, ITask<PackageInfoWrapper> injectBasePkgTask) {
        BundlePreloadTask bundlePreloadTask = null;
        if (MSCHornRollbackConfig.enableSplitChunks() && !getRuntime().hasContainerAttached() && (getRuntime().getSource() == RuntimeSource.NEW || getRuntime().getSource() == RuntimeSource.BASE_PRELOAD)) {
            // 逻辑层bundle只需要第一个页面并且新建和基础库预热时触发提前注入
            bundlePreloadTask = new BundlePreloadTask(getRuntime());
            taskManager.addTask(bundlePreloadTask, pathTask, injectBasePkgTask);
        }
        return bundlePreloadTask;
    }

    public boolean isDependTaskExecutedCheckError() {
        return taskManager.isDependTaskExecutedCheckError();
    }

    @Override
    public void cleanTaskExecuteStateForReport() {
        taskManager.cleanTaskExecuteStateMap();
    }

    @Override
    public ConcurrentHashMap<String, String> getTaskExecuteStateForReport() {
        MPConcurrentHashMap<String, String> map = new MPConcurrentHashMap<>();
        Set<Map.Entry<String, String>> entries = taskManager.getTaskExecuteStateMap().entrySet();
        for (Map.Entry<String, String> entry : entries) {
            map.put(entry.getKey(), entry.getValue());
        }
        return map;
    }

    @Override
    public void setBizPackageOffline() {
        isBizPackageOffline = true;
    }

    @Override
    public boolean isBizPreloadAndNoLaunched() {
        return getRuntime().getSource() == RuntimeSource.BIZ_PRELOAD && isUsable() && !isLaunched();
    }

    @Override
    public CompletableFuture<Void> getFrameworkReadyFuture() {
        return frameworkReadyFuture;
    }

    @Override
    public TaskManager getTaskManager() {
        return taskManager;
    }

    private boolean isAppRouteTaskEnabled(String appId, boolean isFirstLaunch) {
        return MSCHornPreloadConfig.enableAppRouteTask(appId) && isFirstLaunch;
    }
}
