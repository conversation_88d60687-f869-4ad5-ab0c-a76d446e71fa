package com.meituan.msc.modules.apploader;

import com.meituan.msc.common.aov_task.task.ITask;
import com.meituan.msc.modules.apploader.launchtasks.InstrumentLaunchTask;
import com.meituan.msc.modules.engine.MSCRuntime;

public class PageLaunchInfo {
    private int routeId;
    private MSCRuntime mscRuntime;

    public long routeStartTime;

    public long launchPageStartTime;

    private InstrumentLaunchTask instrumentLaunchTask;

    private ITask<?> startPageTask;

    public boolean isLaunchWhenInstrument = false;

    public boolean canUseInstrumentRuntime = false;

    public PageLaunchInfo(int routeId, boolean isLaunchWhenInstrument) {
        this.routeId = routeId;
        this.isLaunchWhenInstrument = isLaunchWhenInstrument;
    }

    public PageLaunchInfo(int routeId) {
        this.routeId = routeId;
    }

    public void setRuntime(MSCRuntime runtime) {
        this.mscRuntime = runtime;
    }

    public void setInstrumentLaunchTask(InstrumentLaunchTask task) {
        this.instrumentLaunchTask = task;
    }

    public void setRouteStartTime(long startTime) {
        this.routeStartTime = startTime;
    }

    public void setLaunchPageStartTime(long startTime) {
        this.launchPageStartTime = startTime;
    }

    public void setStartPageTask(ITask<?> task) {
        this.startPageTask = task;
    }

    public ITask<?> getStartPageTask() {
        return startPageTask;
    }

    public int getRouteId() {
        return routeId;
    }
    public MSCRuntime getMscRuntime() {
        return mscRuntime;
    }

    public InstrumentLaunchTask getInstrumentLaunchTask() {
        return instrumentLaunchTask;
    }
}
