package com.meituan.msc.modules.apploader.events;

import com.meituan.android.mercury.msc.adaptor.core.MSCLoadExeption;
import com.meituan.met.mercury.load.bean.DDLoadPhaseData;

public class AppLoadException extends RuntimeException{

    int errorCode;

    public AppLoadException(int errorCode, String msg, Throwable error){
        super(msg, error);
        this.errorCode = errorCode;
    }

    public AppLoadException(int errorCode, Exception e) {
        super(e == null ? "" : e.toString());
        this.errorCode = errorCode;
    }

    public AppLoadException(int errorCode, String msg){
        super(msg);
        this.errorCode = errorCode;
    }

    public AppLoadException(Throwable error){
        super(error);
        if (error instanceof MSCLoadExeption) {
            this.errorCode = ((MSCLoadExeption) error).getErrCode();
        }
    }

    public int getErrorCode() {
        return errorCode;
    }

    public DDLoadPhaseData getDDPhaseData() {
        if (getCause() instanceof MSCLoadExeption) {
            MSCLoadExeption mscLoadExeption = (MSCLoadExeption) getCause();
            return mscLoadExeption.getLoadPhaseData();
        }

        return null;
    }
}
