package com.meituan.msc.modules.apploader.launchtasks;

import android.support.annotation.NonNull;

import com.meituan.dio.easy.DioFile;
import com.meituan.met.mercury.load.core.DDResource;
import com.meituan.msc.common.aov_task.context.ITaskExecuteContext;
import com.meituan.msc.common.aov_task.context.ITaskResetContext;
import com.meituan.msc.common.aov_task.task.AsyncTask;
import com.meituan.msc.common.support.java.util.concurrent.CompletableFuture;
import com.meituan.msc.common.utils.DioDataCache;
import com.meituan.msc.common.utils.OkHttpFactory;
import com.meituan.msc.modules.apploader.IAppLoader;
import com.meituan.msc.modules.apploader.MSCAppLoader;
import com.meituan.msc.modules.apploader.events.AppLoadException;
import com.meituan.msc.modules.engine.IRendererManager;
import com.meituan.msc.modules.engine.MSCHornRollbackConfig;
import com.meituan.msc.modules.engine.MSCRuntime;
import com.meituan.msc.modules.manager.MSCEvent;
import com.meituan.msc.modules.msi.IMSIManagerModule;
import com.meituan.msc.modules.preload.MSCHornPreloadConfig;
import com.meituan.msc.modules.reporter.MSCLoadErrorConstants;
import com.meituan.msc.modules.reporter.MSCLog;
import com.meituan.msc.modules.service.MSCFileUtils;
import com.meituan.msc.modules.update.IPageLoadModule;
import com.meituan.msc.modules.update.PackageDownloadCallback;
import com.meituan.msc.modules.update.PackageLoadReporter;
import com.meituan.msc.modules.update.bean.PackageInfoWrapper;

import java.util.List;

public class BaseFetchBuzPkgTask extends AsyncTask<List<PackageInfoWrapper>> {

    private final String TAG;
    private final MSCRuntime runtime;
    private boolean isNeedReFetch = false;
    private String loadScene;

    private long routeId;

    public BaseFetchBuzPkgTask(@NonNull MSCRuntime runtime, String name, String loadScene, long routeId) {
        super(name);
        this.runtime = runtime;
        this.loadScene = loadScene;
        this.routeId = routeId;
        this.TAG = name + "@" + Integer.toHexString(hashCode());
    }

    public BaseFetchBuzPkgTask(@NonNull MSCRuntime runtime, String name, String loadScene) {
       this(runtime, name, loadScene, -1);
    }

    @Override
    public CompletableFuture<List<PackageInfoWrapper>> executeTaskAsync(ITaskExecuteContext executeContext) {
        String targetPath = getTargetPath(executeContext);
        CompletableFuture<List<PackageInfoWrapper>> future = new CompletableFuture<>();

        runtime.getModule(IPageLoadModule.class).downLoadBizPackages(targetPath, loadScene, routeId,
                new PackageDownloadCallback() {
                    @Override
                    public void onPackageLoaded(PackageInfoWrapper packageInfo) {
                        if (runtime.getModule(IAppLoader.class).isLaunched() && packageInfo.isBasePackage()) {
                            // 基础包在Service层会有前端驱动的多次文件加载，此处提前完成Dio解密，减少在JSCore线程的耗时
                            DioDataCache.preloadAllBrotherEntries(packageInfo.getServiceFile());
                        } else if (packageInfo.isMainPackage()) {
                            // TODO: 2023/4/12 tianbin config.json拆分为单独任务
                            initMainPackageConfigJsonAndCallbackFailIfError();
                            // TODO: 2023/5/31 tianbin 暂不关注子包流程
                            checkPackageInvalidAndReport(packageInfo);
                        }
                    }

                    private void checkPackageInvalidAndReport(@NonNull PackageInfoWrapper mainPackageInfo) {
                        if (!MSCHornPreloadConfig.enablePreCheckDDResourceMd5()) {
                            return;
                        }
                        MSCLog.i(TAG, "checkPackageInvalidAndReport");
                        DDResource resource = mainPackageInfo.getDDResource();
                        if (resource == null) {
                            return;
                        }
                        boolean resourceExists = new DioFile(resource.getLocalPath()).exists();
                        boolean localCacheValid = false;
                        if (resourceExists) {
                            localCacheValid = resource.isLocalCacheValid();
                        }
                        mainPackageInfo.setPackagePreCheckResult(resourceExists, localCacheValid);
                        if (!resourceExists || !localCacheValid) {
                            PackageLoadReporter.CommonReporter reporter = PackageLoadReporter.CommonReporter.create();
                            reporter.reportDDResourceInvalidPreCheck(mainPackageInfo, resourceExists);
                        }
                    }

                    private boolean initMainPackageConfigJsonAndCallbackFailIfError() {
                        try {
                            runtime.getAppConfigModule().initConfig();
                            runtime.getModule(IMSIManagerModule.class).updateDefaultValue();
                        } catch (Exception e) {
                            MSCFileUtils.checkMd5AndDeleteIfNeed("initConfig", runtime.getMSCAppModule().getMainPackageWrapper());
                            String errorMessage = "initConfigError";
                            onPackageLoadFailed(errorMessage, new AppLoadException(MSCLoadErrorConstants.ERROR_PARSE_CONFIG_FILE_ERROR, errorMessage));
                            if (MSCHornRollbackConfig.readConfig().rollbackLoadErrorReportFix) {
                                runtime.getRuntimeReporter().reportMSCLoadError(MSCLoadErrorConstants.ERROR_PARSE_CONFIG_FILE_ERROR, e);
                            } else {
                                runtime.getRuntimeReporter().reportMSCLoadError(runtime.hasContainerAttached(),
                                        MSCLoadErrorConstants.ERROR_PARSE_CONFIG_FILE_ERROR, e);
                            }
                            MSCLog.e(TAG, e, errorMessage);
                            return false;
                        }
                        return true;
                    }

                    @Override
                    public void onAllPackageLoaded(List<PackageInfoWrapper> packageList) {
                        if (!isNeedReFetch) {
                            runtime.getModule(IRendererManager.class).setPackageReady(!isNeedReFetch);
                            runtime.publish(new MSCEvent(MSCAppLoader.ALL_PACKAGES_READY_FOR_LAUNCH, packageList));
                            OkHttpFactory.getInstance().initCustomClient(runtime.getMSCAppModule());
                        }
                        boolean isInitSuccess = initMainPackageConfigJsonAndCallbackFailIfError();
                        MSCLog.i(TAG, "onAllPackageLoaded", isInitSuccess);
                        if (isInitSuccess) {
                            future.complete(packageList);
                        }
                    }

                    @Override
                    public void onPackageLoadFailed(String msg, Exception ex) {
                        MSCLog.i(TAG, "onPackageLoadFailed");
                        //runtime.publish(new MSCEvent(MSCAppLoader.LOAD_FAILED, new AppLoaderExceptionEvent(msg, ex)));
                        future.completeExceptionally(ex);
                    }

                    @Override
                    public void onPackageLoadCanceled() {
                        MSCLog.i(TAG, "onPackageLoadCanceled");
                        future.completeExceptionally(new AppLoadException(MSCLoadErrorConstants.ERROR_DOWNLOAD_MAIN_PACKAGE_CANCEL, "download cancel", null));
                    }
                });
        return future;
    }

    protected String getTargetPath(ITaskExecuteContext executeContext) {
        return null;
    }

    @Override
    public void onReset(ITaskResetContext resetContext) {
    }
}
