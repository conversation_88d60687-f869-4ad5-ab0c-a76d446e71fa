package com.meituan.msc.modules.apploader.launchtasks;

import android.support.annotation.NonNull;
import android.text.TextUtils;

import com.meituan.msc.common.aov_task.context.ITaskExecuteContext;
import com.meituan.msc.common.aov_task.task.AsyncTask;
import com.meituan.msc.common.config.MSCConfig;
import com.meituan.msc.common.support.java.util.concurrent.CompletableFuture;
import com.meituan.msc.common.utils.JsonUtil;
import com.meituan.msc.modules.api.legacy.appstate.AppStateModule;
import com.meituan.msc.modules.apploader.LaunchTaskManager;
import com.meituan.msc.modules.engine.MSCRuntime;
import com.meituan.msc.modules.reporter.MSCLog;
import com.meituan.msc.util.perf.PerfTrace;

import java.util.HashMap;
import java.util.Map;

/**
 * <AUTHOR>
 * 发送onResourceReady，触发bundle提前注入
 */
public class BundlePreloadTask extends AsyncTask<Void> {
    private static final String TAG = "BundlePreloadTask";
    private final MSCRuntime mRuntime;

    public BundlePreloadTask(@NonNull MSCRuntime runtime) {
        super(LaunchTaskManager.ITaskName.BUNDLE_LOAD_TASK);
        this.mRuntime = runtime;
    }

    @Override
    public CompletableFuture<Void> executeTaskAsync(ITaskExecuteContext executeContext) {
        CompletableFuture<Void> future = new CompletableFuture<>();
        AppStateModule appStateModule = mRuntime.getModule(AppStateModule.class);
        Map<String, Object> params = new HashMap<>();
        String path;
        if (MSCConfig.enableRouteMappingFix()) {
            path = executeContext.getTaskResult(PathCheckTask.class);
        } else {
            path = executeContext.getTaskResult(PathCfgTask.class);
        }
        params.put("path", TextUtils.isEmpty(path) ? mRuntime.getAppConfigModule().getRootPath() : path);
        String jsonStr = JsonUtil.toJsonString(params);
        MSCLog.i(TAG, "service onResourceReady", jsonStr);
        PerfTrace.online().instant("serviceSendResourceReady").report();
        appStateModule.onResourceReady(jsonStr);
        future.complete(null);
        return future;
    }
}
