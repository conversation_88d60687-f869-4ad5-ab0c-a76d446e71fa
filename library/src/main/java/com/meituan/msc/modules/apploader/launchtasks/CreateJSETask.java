package com.meituan.msc.modules.apploader.launchtasks;

import android.os.SystemClock;
import android.support.annotation.NonNull;

import com.meituan.msc.common.abtest.MSCABTestManager;
import com.meituan.msc.common.aov_task.context.ITaskExecuteContext;
import com.meituan.msc.common.aov_task.task.AsyncTask;
import com.meituan.msc.common.constant.ABTestConstants;
import com.meituan.msc.common.perf.PerfEventConstant;
import com.meituan.msc.common.report.BaseMetricsReporter;
import com.meituan.msc.common.support.java.util.concurrent.CompletableFuture;
import com.meituan.msc.modules.apploader.LaunchTaskManager;
import com.meituan.msc.modules.apploader.events.AppLoadException;
import com.meituan.msc.modules.engine.AppService;
import com.meituan.msc.modules.engine.MSCHornRollbackConfig;
import com.meituan.msc.modules.engine.MSCRuntime;
import com.meituan.msc.modules.reporter.MSCLoadErrorConstants;
import com.meituan.msc.modules.reporter.MSCLog;
import com.meituan.msc.modules.reporter.ReporterFields;
import com.meituan.msc.modules.service.ILaunchJSEngineCallback;
import com.meituan.msc.modules.service.IServiceEngine;
import com.sankuai.android.jarvis.Jarvis;

public class CreateJSETask extends AsyncTask<IServiceEngine> {

    private static final String TAG = "CreateJsEngine";
    private final MSCRuntime runtime;

    public CreateJSETask(@NonNull MSCRuntime runtime) {
        super(LaunchTaskManager.ITaskName.CREATE_JSE_TASK);
        this.runtime = runtime;
    }

    @Override
    public CompletableFuture<IServiceEngine> executeTaskAsync(ITaskExecuteContext executeContext) {
        runtime.getPerfEventRecorder().beginDurableEvent(PerfEventConstant.INIT_JS_ENGINE);
        reportFirstAB(runtime.getRuntimeReporter());
        CompletableFuture<IServiceEngine> future = new CompletableFuture<>();
        long startTime = SystemClock.elapsedRealtime();
        // 避免占用调度线程，提高调度效率
        Jarvis.newSingleThreadExecutor("MSC-Launch-CreateJsEngine").submit(new Runnable() {
            @Override
            public void run() {
                try {
                    long switchThreadDuration = SystemClock.elapsedRealtime() - startTime;
                    MSCLog.i(TAG, "switchThreadDuration", switchThreadDuration);
                    runtime.getRuntimeReporter().addStatisticsToMap("Pre_V8_Create_M");
                    // TODO: 2024/9/9 tianbin AppService存在获取不到的情况，待分析
                    runtime.getModule(AppService.class).initJSEngine(new ILaunchJSEngineCallback() {
                        @Override
                        public void callBack(IServiceEngine engine) {
                            future.complete(engine);
                        }
                    });
                    runtime.getRuntimeReporter().addStatisticsToMap("After_V8_Create_M");
                } catch (Exception e) {
                    MSCLog.e(TAG, e, "initJSEngine");
                    future.completeExceptionally(new AppLoadException(MSCLoadErrorConstants.ERROR_CREATE_JS_ENGINE_ERROR, e));
                    if (MSCHornRollbackConfig.readConfig().rollbackLoadErrorReportFix) {
                        runtime.getRuntimeReporter().reportMSCLoadError(MSCLoadErrorConstants.ERROR_CREATE_JS_ENGINE_ERROR, e);
                    } else {
                        runtime.getRuntimeReporter().reportMSCLoadError(runtime.hasContainerAttached(),
                                MSCLoadErrorConstants.ERROR_CREATE_JS_ENGINE_ERROR, e);
                    }
                }
            }
        });
        return future;
    }

    private void reportFirstAB(BaseMetricsReporter reporter) {
        if (MSCHornRollbackConfig.enableReportFirstABTest()) {
            String firstAB = MSCABTestManager.getTimelyStrategy(ABTestConstants.ABTEST_KEY_MSC_FIRST_ABTEST);
            if (firstAB == null) {
                firstAB = "none";
            }
            reporter.record(ReporterFields.REPORT_FIRST_ABTEST)
                    .tag("strategyName", firstAB)
                    .sendDelay();
        }
    }
}
