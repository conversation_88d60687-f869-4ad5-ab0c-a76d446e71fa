package com.meituan.msc.modules.apploader.launchtasks;

import android.support.annotation.NonNull;
import android.text.TextUtils;

import com.meituan.msc.common.aov_task.context.ITaskExecuteContext;
import com.meituan.msc.common.aov_task.task.AsyncTask;
import com.meituan.msc.common.aov_task.task.ITask;
import com.meituan.msc.common.config.MSCConfig;
import com.meituan.msc.common.support.java.util.concurrent.CompletableFuture;
import com.meituan.msc.modules.apploader.LaunchTaskManager;
import com.meituan.msc.modules.engine.MSCRuntime;
import com.meituan.msc.modules.engine.dataprefetch.IDataPrefetchModule;
import com.meituan.msc.modules.reporter.MSCLog;
import com.meituan.msc.modules.update.bean.AppMetaInfoWrapper;

public class DataPrefetchTask extends AsyncTask<Void> {
    private MSCRuntime runtime;
    private int routeId = -1;
    private long routeTime;
    private boolean isWidget;

    public DataPrefetchTask(@NonNull MSCRuntime runtime, int routeId, long routeTime, boolean isWidget){
        super(LaunchTaskManager.ITaskName.DATA_PREFETCH_TASK);
        this.runtime = runtime;
        this.routeId = routeId;
        this.routeTime = routeTime;
        this.isWidget = isWidget;
    }

    @Override
    public CompletableFuture<Void> executeTaskAsync(ITaskExecuteContext executeContext) {
        AppMetaInfoWrapper appMetaInfoWrapper = null;
        ITask<?> fetchMetaInfoTask = executeContext.getDependTaskByClass(FetchMetaInfoTask.class);
        if (fetchMetaInfoTask != null) {
            appMetaInfoWrapper = executeContext.getTaskResult((FetchMetaInfoTask) fetchMetaInfoTask);
        }

        MSCLog.i("MSCDynamicDataPrefetch", "start run DataPrefetchTask");
        //配置了预拉取数据信息，发起预拉取处理
        if (appMetaInfoWrapper != null && appMetaInfoWrapper.getConfigPackage() != null) {
            IDataPrefetchModule dataPrefetchModule = runtime.getModule(IDataPrefetchModule.class);
            if (dataPrefetchModule != null) {
                //获取目标路径
                String targetPath = null;
                if (MSCConfig.enableRouteMappingFix()) {
                    ITask<?> pathCheckTask = executeContext.getDependTaskByClass(PathCheckTask.class);
                    if (pathCheckTask != null) {
                        targetPath = executeContext.getTaskResult((PathCheckTask) pathCheckTask);
                    }
                } else {
                    ITask<?> pathCfgTask = executeContext.getDependTaskByClass(PathCfgTask.class);
                    if (pathCfgTask != null) {
                        targetPath = executeContext.getTaskResult((PathCfgTask) pathCfgTask);
                    }
                }

                if (TextUtils.isEmpty(targetPath)) {
                    targetPath = appMetaInfoWrapper.getMainPath();
                }
                dataPrefetchModule.startDataPrefetch(targetPath, routeId, routeTime, isWidget);
            } else {
                MSCLog.i("MSCDynamicDataPrefetch", "DataPrefetchModule is null");
            }
        } else {
            MSCLog.i("MSCDynamicDataPrefetch", "appMetaInfoWrapper or getConfigPackage() is null");
            if (appMetaInfoWrapper != null) {
                MSCLog.i("MSCDynamicDataPrefetch", "getConfigPackage() is null");
            }
        }

        return CompletableFuture.completedFuture(null);
    }
}
