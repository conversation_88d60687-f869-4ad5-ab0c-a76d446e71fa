package com.meituan.msc.modules.apploader.launchtasks;

import android.support.annotation.NonNull;
import android.support.annotation.Nullable;
import android.text.TextUtils;

import com.meituan.android.mercury.msc.adaptor.core.MSCLoadExeption;
import com.meituan.dio.easy.DioFile;
import com.meituan.met.mercury.load.bean.DDLoadPhaseData;
import com.meituan.met.mercury.load.core.DDResource;
import com.meituan.msc.common.aov_task.context.ITaskExecuteContext;
import com.meituan.msc.common.aov_task.task.AsyncTask;
import com.meituan.msc.common.support.java.util.concurrent.CompletableFuture;
import com.meituan.msc.extern.MSCEnvHelper;
import com.meituan.msc.modules.apploader.IAppLoader;
import com.meituan.msc.modules.apploader.LaunchTaskManager;
import com.meituan.msc.modules.apploader.events.AppLoadException;
import com.meituan.msc.modules.engine.MSCHornRollbackConfig;
import com.meituan.msc.modules.engine.MSCRuntime;
import com.meituan.msc.modules.engine.ReportUtils;
import com.meituan.msc.modules.engine.RuntimeManager;
import com.meituan.msc.modules.preload.MSCHornPreloadConfig;
import com.meituan.msc.modules.preload.PackageDebugHelper;
import com.meituan.msc.modules.reporter.MSCLog;
import com.meituan.msc.modules.update.MSCHornBasePackageReloadConfig;
import com.meituan.msc.modules.update.PackageLoadReporter;
import com.meituan.msc.modules.update.PackageReportBean;
import com.meituan.msc.modules.update.bean.PackageInfoWrapper;
import com.meituan.msc.modules.update.pkg.PackageLoadCallback;
import com.meituan.msc.modules.update.pkg.PackageLoadManager;
import com.meituan.msc.util.perf.PerfEventRecorder;

public class FetchBasePkgTask extends AsyncTask<PackageInfoWrapper> {

    MSCRuntime runtime;
    private String basePkgVersionOfDebug;
    // TODO: 8/19/24 linyinong 上报维度未赋值
    String checkScene;

    public FetchBasePkgTask(@NonNull MSCRuntime runtime) {
        super(LaunchTaskManager.ITaskName.FETCH_BASE_PKG_TASK);
        this.runtime = runtime;
    }

    public FetchBasePkgTask(@NonNull MSCRuntime runtime, String checkScene) {
        super(LaunchTaskManager.ITaskName.FETCH_BASE_PKG_TASK);
        this.runtime = runtime;
        this.checkScene = checkScene;
    }

    @Override
    public CompletableFuture<PackageInfoWrapper> executeTaskAsync(ITaskExecuteContext executeContext) {
        PerfEventRecorder recorder = runtime.getPerfEventRecorder();
        runtime.checkUpdateBasePackageErrorMsg = PackageLoadManager.getInstance().checkUpdateBasePackageErrorMsg;

        CompletableFuture<PackageInfoWrapper> completableFuture = new CompletableFuture<>();
        IAppLoader appLoader = runtime.getModule(IAppLoader.class);
        final String mscVersionOfDebug = getMSCVersionOfDebug(appLoader);

        PackageLoadCallback<DDResource> callback = createCallback(completableFuture, mscVersionOfDebug);
        PackageLoadManager.getInstance().loadLatestBasePackage(recorder, runtime.getAppId(), mscVersionOfDebug, checkScene, callback);
        return completableFuture;
    }

    private PackageLoadCallback<DDResource> createCallback(CompletableFuture<PackageInfoWrapper> completableFuture,
                                                           String mscVersionOfDebug) {
        final long startTime = System.currentTimeMillis();
        return new PackageLoadCallback<DDResource>() {
            @Override
            public void onSuccess(@NonNull DDResource ddResource) {
                PackageInfoWrapper basePackageWrapper = runtime.getMSCAppModule().createBasePackageWrapper();
                basePackageWrapper.setDDResource(ddResource);
                basePackageWrapper.setDownloadTimeInMs(startTime, System.currentTimeMillis());
                runtime.getPerformanceManager().onDownloadPackage(basePackageWrapper);
                ReportUtils.addBasePkgInfoToMSIContainer(runtime, basePackageWrapper);
                PackageReportBean packageReportBean = new PackageReportBean.Builder()
                        .setLoadType(ddResource.isFromNet() ? PackageLoadReporter.LoadType.NETWORK : PackageLoadReporter.LoadType.LOCAL)
                        .setPkgName(runtime.getMSCAppModule().getBasePkgName())
                        .setDdLoadPhaseData(ddResource.getLoadPhaseData())
                        .setPkgType(basePackageWrapper.getPkgTypeString())
                        .setSourceFrom(RuntimeManager.getSourceFrom(runtime))
                        .build();
                PackageLoadReporter packageLoadReporter = PackageLoadReporter.create(runtime);
                // report duration
                // TODO loadLatestBasePackage包含重试逻辑，指标需要补充重试维度
                packageLoadReporter.reportLoadPackageSuccessDuration(packageReportBean, System.currentTimeMillis() - startTime);
                // report success
                packageLoadReporter.onLoadPackageSuccess(packageReportBean);

                checkBasePackageVersion(ddResource);
                checkPackageInvalidAndReport(basePackageWrapper);

                MSCLog.i(source, "loadLatestBasePackage success");
                completableFuture.complete(basePackageWrapper);
            }

            @Override
            public void onFail(String errMsg, AppLoadException error) {
                PackageInfoWrapper basePackageWrapper = runtime.getMSCAppModule().createBasePackageWrapper();
                PackageLoadReporter.create(runtime).onLoadPackageFailed(
                        new PackageReportBean.Builder()
                                .setPkgName(basePackageWrapper.getPackageName())
                                .setPkgType(basePackageWrapper.getPkgTypeString())
                                .setDdLoadPhaseData(error != null ? error.getDDPhaseData() : null)
                                .setSourceFrom(RuntimeManager.getSourceFrom(runtime))
                                .build(),
                        error);
                if (MSCHornRollbackConfig.readConfig().rollbackLoadErrorReportFix) {
                    runtime.getRuntimeReporter().reportMSCLoadError(error.getErrorCode(), error);
                } else {
                    runtime.getRuntimeReporter().reportMSCLoadError(runtime.hasContainerAttached(),
                            error.getErrorCode(), error);
                }
                MSCLog.i(source, "loadLatestBasePackage failed:", mscVersionOfDebug);
                completableFuture.completeExceptionally(error);
            }
        };
    }

    private void checkPackageInvalidAndReport(@NonNull PackageInfoWrapper basePackageWrapper) {
        if (!MSCHornPreloadConfig.enablePreCheckDDResourceMd5()) {
            return;
        }
        MSCLog.i("DownloadBasePkgTask", "checkPackageInvalidAndReport");
        DDResource resource = basePackageWrapper.getDDResource();
        if (resource == null) {
            return;
        }
        boolean resourceExists = new DioFile(resource.getLocalPath()).exists();
        boolean localCacheValid = false;
        if (resourceExists) {
            localCacheValid = resource.isLocalCacheValid();
        }
        basePackageWrapper.setPackagePreCheckResult(resourceExists, localCacheValid);
        if (!resourceExists || !localCacheValid) {
            PackageLoadReporter.CommonReporter reporter = PackageLoadReporter.CommonReporter.create();
            reporter.reportDDResourceInvalidPreCheck(basePackageWrapper, resourceExists);
        }
    }

    // 基础库版本非法，清除AAR_Version缓存下次获取时网络更新，上报版本号非法
    private void checkBasePackageVersion(DDResource ddResource) {
        String[] sdkReloadVersions = MSCHornBasePackageReloadConfig.get().getSDKReloadVersions();
        if (!MSCHornBasePackageReloadConfig.get().isInReloadVersions(ddResource.getVersion(), sdkReloadVersions)) {
            return;
        }
        MSCLog.i(source, "loadLatestBasePackage failed, version in reload list:" + ddResource.getVersion());
        // 当前基础库版本 在强制更新名单，需要重新拉取
        PackageLoadManager.getInstance().cleanMSCAARVersionCache();
        PackageLoadReporter.create(runtime).reportIllegalBaseVersion(sdkReloadVersions,
                ddResource.isFromNet() ? PackageLoadReporter.LoadType.NETWORK : PackageLoadReporter.LoadType.LOCAL);
    }

    @Nullable
    private String getMSCVersionOfDebug(IAppLoader appLoader) {
        if (MSCEnvHelper.getEnvInfo().isProdEnv()) {
            return null;
        }

        // 预热场景下的基础包版本
        if (!TextUtils.isEmpty(basePkgVersionOfDebug)) {
            return basePkgVersionOfDebug;
        }

        return PackageDebugHelper.instance.getBasePkgVersionOfDebug(appLoader.getBasePkgVersionOfDebug());
    }

    public void setBasePkgVersionOfDebug(String basePkgVersionOfDebug) {
        this.basePkgVersionOfDebug = basePkgVersionOfDebug;
    }
}
