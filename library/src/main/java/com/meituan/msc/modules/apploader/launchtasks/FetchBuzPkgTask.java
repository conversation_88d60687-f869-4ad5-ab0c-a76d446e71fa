package com.meituan.msc.modules.apploader.launchtasks;

import android.support.annotation.NonNull;

import com.meituan.msc.common.aov_task.context.ITaskExecuteContext;
import com.meituan.msc.common.aov_task.task.ITask;
import com.meituan.msc.modules.apploader.LaunchTaskManager;
import com.meituan.msc.modules.engine.MSCRuntime;

public class FetchBuzPkgTask extends BaseFetchBuzPkgTask {
    public FetchBuzPkgTask(@NonNull MSCRuntime runtime, String loadScene) {
        super(runtime, LaunchTaskManager.ITaskName.FETCH_BUZ_PKG_TASK, loadScene);
    }

    public FetchBuzPkgTask(@NonNull MSCRuntime runtime, String loadScene, long routeId) {
        super(runtime, LaunchTaskManager.ITaskName.FETCH_BUZ_PKG_TASK, loadScene, routeId);
    }

    @Override
    protected String getTargetPath(ITaskExecuteContext executeContext) {
        ITask<?> task = executeContext.getDependTaskByClass(PathCfgTask.class);
        if (task != null) {
            return executeContext.getTaskResult((PathCfgTask) task);
        }
        return null;
    }
}
