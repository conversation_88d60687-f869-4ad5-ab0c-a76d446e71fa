package com.meituan.msc.modules.apploader.launchtasks;
import android.support.annotation.NonNull;
import com.meituan.msc.common.aov_task.context.ITaskExecuteContext;
import com.meituan.msc.common.aov_task.task.AsyncTask;
import com.meituan.msc.common.aov_task.task.ITask;
import com.meituan.msc.common.executor.MSCExecutors;
import com.meituan.msc.common.support.java.util.concurrent.CompletableFuture;
import com.meituan.msc.modules.apploader.LaunchTaskManager;
import com.meituan.msc.modules.engine.MSCRuntime;
import com.meituan.msc.modules.engine.dataprefetch.IDataPrefetchModule;
import com.meituan.msc.modules.update.bean.AppMetaInfoWrapper;

/**
 * 拉取配置包任务，目前仅在业务预热场景添加该任务
 */
public class FetchConfigPkgTask extends AsyncTask<Void> {
    MSCRuntime runtime;
    public FetchConfigPkgTask(@NonNull MSCRuntime runtime) {
        super(LaunchTaskManager.ITaskName.FETCH_CONFIG_PKG_TASK);
        this.runtime = runtime;
    }
    /**
     * 做拉包操作
     * @param executeContext
     * @return
     */
    @Override
    public CompletableFuture<Void> executeTaskAsync(ITaskExecuteContext executeContext) {
        AppMetaInfoWrapper appMetaInfoWrapper = null;
        ITask<?> fetchMetaInfoTask = executeContext.getDependTaskByClass(FetchMetaInfoTask.class);
        if (fetchMetaInfoTask != null) {
            appMetaInfoWrapper = executeContext.getTaskResult((FetchMetaInfoTask) fetchMetaInfoTask);
        }
        if (appMetaInfoWrapper == null) {
            return CompletableFuture.completedFuture(null);
        }
        IDataPrefetchModule dataPrefetchModule = runtime.getModule(IDataPrefetchModule.class);
        if (dataPrefetchModule == null) {
            return CompletableFuture.completedFuture(null);
        }
        // 新开线程，保证不会延后业务预热场景结束时机
        MSCExecutors.submit(new Runnable() {
            @Override
            public void run() {
                dataPrefetchModule.getConfigPackage();
                dataPrefetchModule.loadDataPrefetchConfigFromDio();
            }
        });
        return CompletableFuture.completedFuture(null);
    }
}
