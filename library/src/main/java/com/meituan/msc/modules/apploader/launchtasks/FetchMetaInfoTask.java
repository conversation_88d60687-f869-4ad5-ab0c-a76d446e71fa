package com.meituan.msc.modules.apploader.launchtasks;

import android.support.annotation.NonNull;

import com.meituan.msc.common.aov_task.context.ITaskExecuteContext;
import com.meituan.msc.common.aov_task.task.AsyncTask;
import com.meituan.msc.common.support.java.util.concurrent.CompletableFuture;
import com.meituan.msc.modules.apploader.IAppLoader;
import com.meituan.msc.modules.apploader.LaunchTaskManager;
import com.meituan.msc.modules.apploader.MSCAppLoader;
import com.meituan.msc.modules.engine.MSCRuntime;
import com.meituan.msc.modules.manager.MSCEvent;
import com.meituan.msc.modules.update.IPageLoadModule;
import com.meituan.msc.modules.update.bean.AppMetaInfoWrapper;
import com.meituan.msc.modules.update.bean.LaunchPageParams;
import com.meituan.msc.common.perf.PerfEventConstant;
import com.meituan.msc.modules.update.metainfo.BackgroundCheckUpdateModule;
import com.meituan.msc.util.perf.PerfTrace;

public class FetchMetaInfoTask extends AsyncTask<AppMetaInfoWrapper> {

    MSCRuntime runtime;

    public FetchMetaInfoTask(@NonNull MSCRuntime runtime) {
        super(LaunchTaskManager.ITaskName.FETCH_META_INFO_TASK);
        this.runtime = runtime;
    }

    @Override
    public CompletableFuture<AppMetaInfoWrapper> executeTaskAsync(ITaskExecuteContext executeContext) {
        PerfTrace.online().begin(PerfEventConstant.FETCH_META_INFO).report();
        LaunchPageParams launchPageParams = new LaunchPageParams();
        launchPageParams.appId = runtime.getAppId();
        IAppLoader appLoader = runtime.getModule(IAppLoader.class);
        launchPageParams.checkUpdateUrl = appLoader.getCheckUpdateUrl();
        launchPageParams.needForceUpdate = appLoader.needForceUpdate();
        CompletableFuture<AppMetaInfoWrapper> future = runtime.getModule(IPageLoadModule.class).fetchAppMetaInfo(launchPageParams);
        return future.thenApply(metaInfoWrapper -> {
            runtime.publish(new MSCEvent<>(MSCAppLoader.APP_PROP_UPDATED));
            BackgroundCheckUpdateModule backgroundCheckUpdateModule = runtime.getModule(BackgroundCheckUpdateModule.class);
            if (backgroundCheckUpdateModule != null){
                if (metaInfoWrapper.isFromCache()) {
                    backgroundCheckUpdateModule.checkUpdate(runtime);
                } else {
                    // 记录最近一次检查更新的时间戳，用于后台检查更新窗口期计算
                    backgroundCheckUpdateModule.recordLastCheckUpdateTimestamp();

                    // 测试场景全都走了这个 todo find a property time
//                RenderCacheHelper.clearCache(launchPageParams.appId);
                }
            }

            PerfTrace.online().end(PerfEventConstant.FETCH_META_INFO).report();
            return metaInfoWrapper;
        });
    }
}
