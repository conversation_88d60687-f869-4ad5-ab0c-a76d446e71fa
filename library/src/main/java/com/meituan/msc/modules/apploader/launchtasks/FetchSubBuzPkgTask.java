package com.meituan.msc.modules.apploader.launchtasks;

import android.support.annotation.NonNull;

import com.meituan.msc.common.aov_task.context.ITaskExecuteContext;
import com.meituan.msc.common.aov_task.task.ITask;
import com.meituan.msc.modules.apploader.LaunchTaskManager;
import com.meituan.msc.modules.engine.MSCRuntime;

/**
 * 仅下载子包任务
 */
public class FetchSubBuzPkgTask extends BaseFetchBuzPkgTask {
    public FetchSubBuzPkgTask(@NonNull MSCRuntime runtime, String loadScene) {
        super(runtime, LaunchTaskManager.ITaskName.FETCH_SUB_BUZ_PKG_TASK, loadScene);
    }

    @Override
    protected String getTargetPath(ITaskExecuteContext executeContext) {
        ITask<?> task = executeContext.getDependTaskByClass(PathCheckTask.class);
        if (task != null) {
            return executeContext.getTaskResult((PathCheckTask) task);
        }
        return null;
    }
}
