package com.meituan.msc.modules.apploader.launchtasks;

import android.support.annotation.NonNull;

import com.meituan.msc.common.aov_task.context.ITaskExecuteContext;
import com.meituan.msc.common.aov_task.task.AsyncTask;
import com.meituan.msc.common.perf.PerfEventConstant;
import com.meituan.msc.common.support.java.util.concurrent.CompletableFuture;
import com.meituan.msc.modules.apploader.LaunchTaskManager;
import com.meituan.msc.modules.apploader.events.AppLoadException;
import com.meituan.msc.modules.engine.AppService;
import com.meituan.msc.modules.engine.MSCHornRollbackConfig;
import com.meituan.msc.modules.engine.MSCPackageLoadCallback;
import com.meituan.msc.modules.engine.MSCRuntime;
import com.meituan.msc.modules.reporter.MSCLog;
import com.meituan.msc.modules.update.bean.PackageInfoWrapper;

public class InjectBasePkgTask extends AsyncTask<PackageInfoWrapper> {

    MSCRuntime runtime;

    public InjectBasePkgTask(@NonNull MSCRuntime runtime) {
        super(LaunchTaskManager.ITaskName.INJECT_BASE_PKG_TASK);
        this.runtime = runtime;
    }

    @Override
    public CompletableFuture<PackageInfoWrapper> executeTaskAsync(ITaskExecuteContext executeContext) {
        MSCLog.i(getName(), "Start to InjectBasePackage");
        // MSCLog.i(getName(), "[MSC_LOG]Start to InjectBasePackage appId:", runtime.getAppId());
        PackageInfoWrapper packageInfo = executeContext.getTaskResult(FetchBasePkgTask.class);

        CompletableFuture<PackageInfoWrapper> completableFuture = new CompletableFuture<>();
        runtime.getModule(AppService.class).loadPackage(packageInfo, new MSCPackageLoadCallback() {
            @Override
            public void onPackageLoadSuccess(@NonNull PackageInfoWrapper packageInfo, boolean realLoaded) {
                MSCLog.i(source, "inject base package success");
                completableFuture.complete(packageInfo);
                runtime.getPerfEventRecorder().endDurableEvent(PerfEventConstant.PRELOAD);
                onInjectBasePkgSuccess();
            }

            @Override
            public void onPackageLoadFailed(@NonNull PackageInfoWrapper packageInfo, AppLoadException e) {
                MSCLog.i(source, "inject base package failed");
                completableFuture.completeExceptionally(e);
                if (MSCHornRollbackConfig.readConfig().rollbackLoadErrorReportFix) {
                    runtime.getRuntimeReporter().reportMSCLoadError(e.getErrorCode(), e);
                } else {
                    runtime.getRuntimeReporter().reportMSCLoadError(runtime.hasContainerAttached(), e.getErrorCode(), e);
                }
                runtime.getPerfEventRecorder().endDurableEvent(PerfEventConstant.PRELOAD);
            }
        });

        return completableFuture;
    }

    public MSCRuntime getRuntime() {
        return runtime;
    }

    // For IDE hook
    public void onInjectBasePkgSuccess() {
    }
}
