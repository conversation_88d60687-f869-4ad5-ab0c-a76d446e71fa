package com.meituan.msc.modules.apploader.launchtasks;

import android.support.annotation.NonNull;
import android.text.TextUtils;

import com.meituan.android.mercury.msc.adaptor.bean.MSCAppMetaInfo;
import com.meituan.msc.common.aov_task.context.ITaskExecuteContext;
import com.meituan.msc.common.aov_task.context.ITaskResetContext;
import com.meituan.msc.common.aov_task.task.AsyncTask;
import com.meituan.msc.common.config.MSCConfig;
import com.meituan.msc.common.support.java.util.concurrent.CompletableFuture;
import com.meituan.msc.jse.bridge.ConversionUtil;
import com.meituan.msc.modules.apploader.LaunchTaskManager;
import com.meituan.msc.modules.apploader.events.AppLoadException;
import com.meituan.msc.modules.engine.AppService;
import com.meituan.msc.modules.engine.MSCHornRollbackConfig;
import com.meituan.msc.modules.engine.MSCRuntime;
import com.meituan.msc.modules.engine.requestPrefetch.RequestPrefetchManager;
import com.meituan.msc.modules.page.render.webview.MSCWebViewRenderer;
import com.meituan.msc.modules.reporter.MSCLog;
import com.meituan.msc.modules.update.IPageLoadModule;
import com.meituan.msc.modules.update.PackageInjectCallback;
import com.meituan.msc.modules.update.bean.AppMetaInfoWrapper;
import com.meituan.msc.modules.update.bean.PackageInfoWrapper;

import org.json.JSONException;
import org.json.JSONObject;


/**
 * 拆分业务包下载及注入的原因为注入需要依赖逻辑层创建
 *
 * <AUTHOR>
 * @date 2022/9/5.
 */
public class InjectBuzPkgTask extends AsyncTask<Void> {

    private static final String TAG = LaunchTaskManager.ITaskName.INJECT_BUZ_PKG_TASK;
    private final MSCRuntime runtime;

    public InjectBuzPkgTask(@NonNull MSCRuntime runtime) {
        super(LaunchTaskManager.ITaskName.INJECT_BUZ_PKG_TASK);
        this.runtime = runtime;
    }

    @Override
    public CompletableFuture<Void> executeTaskAsync(ITaskExecuteContext executeContext) {
        CompletableFuture<Void> future = new CompletableFuture<>();
        if (RequestPrefetchManager.isAppLevelPrefetch(runtime.getMSCAppModule(), runtime.getAppId())) {
            // 业务包注入前 在逻辑层注入全局变量
            String backgroundFetchDataLevel = "app";
            JSONObject jsonObject = new JSONObject();
            try {
                jsonObject.put("backgroundFetchDataLevel", backgroundFetchDataLevel);
                String preloadStrategyStr = runtime.getPreloadStrategyStr();
                if (!TextUtils.isEmpty(preloadStrategyStr)) {
                    runtime.getModule(AppService.class).injectGlobalField("__mtAfterT3PreloadStrategy", "'" + preloadStrategyStr + "'");
                }
                runtime.getModule(AppService.class).injectGlobalField("__bizInfo", jsonObject.toString());
            } catch (JSONException e) {
                MSCLog.e(e);
            }
        }

        injectMetaInfoConfig();

        String targetPath;
        if (MSCConfig.enableRouteMappingFix()) {
            targetPath = executeContext.getTaskResult(PathCheckTask.class);
        } else {
            targetPath = executeContext.getTaskResult(PathCfgTask.class);
        }
        runtime.getModule(IPageLoadModule.class).injectPackages(targetPath,
                new PackageInjectCallback() {
                    @Override
                    public void onPackageInjectSuccess(PackageInfoWrapper packageInfo, boolean realLoaded) {
                    }

                    @Override
                    public void onPackageInjectFailed(PackageInfoWrapper packageInfo, String errorMsg, AppLoadException e) {
                        future.completeExceptionally(e);
                    }

                    @Override
                    public void onAllPackageInjected() {
                        future.complete(null);
                    }
                });
        return future;
    }

    private void injectMetaInfoConfig() {
        if (MSCHornRollbackConfig.readConfig().rollbackInjectAdvanceBuildConfig) {
            MSCLog.i(TAG,"injectMetaInfoConfig rollback");
            return;
        }

        AppMetaInfoWrapper metaInfo = runtime.getMSCAppModule().getMetaInfo();
        if (metaInfo == null) {
            MSCLog.i(TAG,"injectMetaInfoConfig metaInfo is null");
            return;
        }
        MSCAppMetaInfo.AdvanceBuildConfig advanceBuildConfig = metaInfo.getAdvanceBuildConfig();
        if (advanceBuildConfig == null) {
            MSCLog.i(TAG,"injectMetaInfoConfig advanceBuildConfig is null");
            return;
        }
        MSCWebViewRenderer.MetaInfoConfig metaInfoConfig = new MSCWebViewRenderer.MetaInfoConfig(advanceBuildConfig);
        String metaInfoConfigStr = ConversionUtil.toJsonString(metaInfoConfig);
        runtime.getModule(AppService.class).injectGlobalField("APP_METADATA", metaInfoConfigStr);
    }

    @Override
    public void onReset(ITaskResetContext resetContext) {
    }
}
