package com.meituan.msc.modules.apploader.launchtasks;

import com.meituan.msc.common.aov_task.context.ITaskExecuteContext;
import com.meituan.msc.common.aov_task.task.AsyncTask;
import com.meituan.msc.common.config.MSCConfig;
import com.meituan.msc.common.support.java.util.concurrent.CompletableFuture;
import com.meituan.msc.modules.api.appLifecycle.MSCAppLifecycle;
import com.meituan.msc.modules.apploader.IAppLoader;
import com.meituan.msc.modules.apploader.events.AppLoadException;
import com.meituan.msc.modules.engine.MSCApp;
import com.meituan.msc.modules.engine.MSCRuntime;
import com.meituan.msc.modules.manager.MSCEvent;
import com.meituan.msc.modules.manager.MSCSubscriber;
import com.meituan.msc.modules.reporter.MSCLog;

public class InstrumentLaunchTask extends AsyncTask<String> {
    private static final String TAG = "InstrumentLaunchTask";
    private CompletableFuture<String> taskFuture = new CompletableFuture<>();

    public InstrumentLaunchTask() {
        super("InstrumentLaunchTask");
    }

    @Override
    public CompletableFuture<String> executeTaskAsync(ITaskExecuteContext executeContext) {
        MSCLog.i(TAG, "execute InstrumentLaunchTask");
        taskFuture.complete("success");
        return taskFuture;
    }
}
