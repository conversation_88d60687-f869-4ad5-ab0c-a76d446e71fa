package com.meituan.msc.modules.apploader.launchtasks;

import com.meituan.msc.common.aov_task.context.ITaskExecuteContext;
import com.meituan.msc.common.aov_task.task.SyncTask;

public class ParamCfgTask<T> extends SyncTask<T> {
    T param;

    public ParamCfgTask(String name, T param){
        super(name);
        this.param = param;
    }

    @Override
    public T executeTaskSync(ITaskExecuteContext executeContext) {
        return this.param;
    }
}
