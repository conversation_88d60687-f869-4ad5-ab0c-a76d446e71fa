package com.meituan.msc.modules.apploader.launchtasks;

import android.text.TextUtils;

import com.meituan.msc.common.aov_task.context.ITaskExecuteContext;
import com.meituan.msc.common.aov_task.task.ITask;
import com.meituan.msc.common.config.MSCConfig;
import com.meituan.msc.modules.apploader.LaunchTaskManager;
import com.meituan.msc.modules.engine.MSCHornRollbackConfig;
import com.meituan.msc.modules.engine.MSCRuntime;
import com.meituan.msc.modules.update.AppConfigModule;
import com.meituan.msc.modules.update.bean.AppMetaInfoWrapper;

public class PathCfgTask extends ParamCfgTask<String> {

    private MSCRuntime runtime;
    //当前路由是不是外部路由
    private boolean external;
    private boolean isIgnoreRouteMapping;
    private boolean isLaunchFromRoute;

    public PathCfgTask(String path) {
        super(LaunchTaskManager.ITaskName.PATH_CFG_TASK, path);
    }

    public PathCfgTask(MSCRuntime runtime, String path) {
        super(LaunchTaskManager.ITaskName.PATH_CFG_TASK, path);
        this.runtime = runtime;
    }

    public PathCfgTask(MSCRuntime runtime, String path, boolean external) {
        this(runtime, path);
        this.external = external;
    }

    public PathCfgTask(MSCRuntime runtime, String path, boolean external, boolean isIgnoreRouteMapping, boolean isLaunchFromRoute) {
        this(runtime, path, external);
        this.isIgnoreRouteMapping = isIgnoreRouteMapping;
        this.isLaunchFromRoute = isLaunchFromRoute;
    }

    @Override
    public String executeTaskSync(ITaskExecuteContext executeContext) {
        // param为空且已经获取到元信息时，param的值更新为mainPath
        ITask<?> fetchMetaInfoTask = executeContext.getDependTaskByClass(FetchMetaInfoTask.class);
        if (param == null && fetchMetaInfoTask != null) {
            AppMetaInfoWrapper appMetaInfoWrapper = executeContext.getTaskResult((FetchMetaInfoTask) fetchMetaInfoTask);
            param = appMetaInfoWrapper.getMainPath();
        }
        if (MSCConfig.enableRouteMappingFix() && isLaunchFromRoute) {
            return param;
        }
        AppConfigModule appConfigModule = runtime.getAppConfigModule();
        String pathIfRoutedPersist = appConfigModule.getRoutePathPersistIfRouted(param);
        if (!TextUtils.isEmpty(pathIfRoutedPersist)) {
            boolean onlyExternal = appConfigModule.isOnlyExternalRouterPersist(param);
            if (MSCConfig.enableRouteMappingFix()) {
                // 是否是外部路由
                if (external) {
                    // 是否忽略路由映射（仅对外部路由映射）
                    if (!isIgnoreRouteMapping) {
                        return pathIfRoutedPersist;
                    }
                    //  判断是否仅外部路由
                } else if (!onlyExternal) {
                    return pathIfRoutedPersist;
                }
            } else {
                // 当前路由是外部路由则直接返回映射后页面，否则需要判断是否仅外部路由生效。如果仅外部路由生效，则跳过当前路由映射。如果非仅外部路由生效，则没有限制，直接返回映射后页面
                if (external || !onlyExternal) {
                    return pathIfRoutedPersist;
                }
            }
        }
        String pathIfRouted = appConfigModule.getRoutePathIfRouted(param);
        if (!TextUtils.isEmpty(pathIfRouted)) {
            boolean onlyExternal = appConfigModule.isOnlyExternalRouter(param);
            if (external || !onlyExternal) {
                return pathIfRouted;
            }
        }
        return param;
    }
}
