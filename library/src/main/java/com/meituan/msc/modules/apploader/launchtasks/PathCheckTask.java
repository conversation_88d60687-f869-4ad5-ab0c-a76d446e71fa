package com.meituan.msc.modules.apploader.launchtasks;

import com.meituan.msc.common.aov_task.context.ITaskExecuteContext;
import com.meituan.msc.common.aov_task.task.AsyncTask;
import com.meituan.msc.common.aov_task.task.ITask;
import com.meituan.msc.common.support.java.util.concurrent.CompletableFuture;
import com.meituan.msc.modules.apploader.LaunchTaskManager;
import com.meituan.msc.modules.update.AppConfigModule;

/**
 * 仅给Launch使用，Route无需使用
 * 检查路由映射后的页面是否存在，不存在需要降级
 */
public class PathCheckTask extends AsyncTask<String> {
    private final String originPath;
    private final AppConfigModule appConfigModule;

    public PathCheckTask(String originPath, AppConfigModule appConfigModule) {
        super(LaunchTaskManager.ITaskName.PATH_CHECK_TASK);
        this.originPath = originPath;
        this.appConfigModule = appConfigModule;
    }

    @Override
    public CompletableFuture<String> executeTaskAsync(ITaskExecuteContext executeContext) {
        String targetPath = null;
        ITask<?> task = executeContext.getDependTaskByClass(PathCfgTask.class);
        if (task != null) {
            targetPath = executeContext.getTaskResult((PathCfgTask) task);
        }
        if (appConfigModule.hasPage(targetPath)) {
            return CompletableFuture.completedFuture(targetPath);
        } else if (appConfigModule.hasPage(originPath)) {
            return CompletableFuture.completedFuture(originPath);
        } else {
            return CompletableFuture.completedFuture(appConfigModule.getRootPath());
        }
    }
}
