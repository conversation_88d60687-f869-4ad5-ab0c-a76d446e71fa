package com.meituan.msc.modules.apploader.launchtasks;

import android.support.annotation.NonNull;
import android.text.TextUtils;

import com.meituan.msc.common.aov_task.context.ITaskExecuteContext;
import com.meituan.msc.common.aov_task.task.ITask;
import com.meituan.msc.common.aov_task.task.SyncTask;
import com.meituan.msc.common.config.MSCConfig;
import com.meituan.msc.common.executor.MSCExecutors;
import com.meituan.msc.common.utils.DisplayUtil;
import com.meituan.msc.common.utils.PathUtil;
import com.meituan.msc.extern.MSCEnvHelper;
import com.meituan.msc.modules.apploader.LaunchTaskManager;
import com.meituan.msc.modules.engine.IRendererManager;
import com.meituan.msc.modules.engine.MSCHornRollbackConfig;
import com.meituan.msc.modules.engine.MSCRuntime;
import com.meituan.msc.modules.engine.ResultCallback;
import com.meituan.msc.modules.engine.RuntimeManager;
import com.meituan.msc.modules.page.render.RendererType;
import com.meituan.msc.modules.page.render.webview.TemplateHelper;
import com.meituan.msc.modules.preload.MSCHornPreloadConfig;
import com.meituan.msc.modules.preload.PreloadManager;
import com.meituan.msc.modules.reporter.MSCLog;
import com.meituan.msc.modules.update.MSCAppModule;

/**
 * 提前初始化渲染器，依赖基础库及业务包下载
 */
public class PreInitRenderTask extends SyncTask<Void> {
    private final String TAG = LaunchTaskManager.ITaskName.PRE_INIT_RENDER_TASK + "@" + Integer.toHexString(hashCode());
    private final MSCRuntime runtime;

    private boolean mPreloadWebViewPage = false;
    private final boolean isLaunchFromRoute;

    public PreInitRenderTask(@NonNull MSCRuntime runtime, boolean isLaunchFromRoute) {
        super(LaunchTaskManager.ITaskName.PRE_INIT_RENDER_TASK);
        this.runtime = runtime;
        this.isLaunchFromRoute = isLaunchFromRoute;
    }

    public PreInitRenderTask(@NonNull MSCRuntime runtime, boolean preloadWebViewPage, boolean isLaunchFromRoute) {
        this(runtime, isLaunchFromRoute);
        this.mPreloadWebViewPage = preloadWebViewPage;
    }

    @Override
    public Void executeTaskSync(ITaskExecuteContext executeContext) {
        String targetPath = null;
        if (MSCConfig.enableRouteMappingFix()) {
            ITask<?> task = executeContext.getDependTaskByClass(PathCheckTask.class);
            if (task != null) {
                targetPath = executeContext.getTaskResult((PathCheckTask) task);
            }
        } else {
            ITask<?> task = executeContext.getDependTaskByClass(PathCfgTask.class);
            if (task != null) {
                targetPath = executeContext.getTaskResult((PathCfgTask) task);
            }
        }
        final String path = PathUtil.getPath(targetPath);
        MSCLog.i(TAG, "path:", path);
        Runnable runnable = new Runnable() {
            @Override
            public void run() {
                if (RuntimeManager.isPageNotFound(runtime, path)) {
                    return;
                }
                String realPath = path;
                if (TextUtils.isEmpty(realPath)) {
                    realPath = runtime.getMSCAppModule().getRootPath();
                }
                RendererType type = runtime.getMSCAppModule().getRendererTypeForPage(realPath);
                if (type != RendererType.WEBVIEW) {
                    return;
                }

                IRendererManager rendererManager = runtime.getModule(IRendererManager.class);
                MSCLog.i(TAG, "preloadPage", path, mPreloadWebViewPage);
                if (mPreloadWebViewPage) {
                    preloadPage(rendererManager, realPath);
                } else {
                    rendererManager.preloadPage(MSCEnvHelper.getContext(), realPath, isLaunchFromRoute);
                }
            }
        };
        if (MSCHornRollbackConfig.isEnablePrePageStartOptimize(runtime.getAppId()) && isLaunchFromRoute) {
            // 优选二级页先执行preload，解决预热页面时主线程繁忙，导致预热出现反效果的问题，后续改为任务依赖
            runnable.run();
        } else {
            MSCExecutors.submit(runnable);
        }
        return null;
    }

    /**
     * 预加载webview
     * 预加载首页快照，预热快照+空白的webview，其余情况预热空白的webview（深度预加载+主包基础包bootstrap文件）
     * path为子包，在DownLoadBuzPkgTask已下载主包+子包
     * @param path   页面路径
     */
    public void preloadPage(IRendererManager rendererManager, String path) {
        MSCAppModule mscAppModule = runtime.getMSCAppModule();
        if (!DisplayUtil.isWebViewRender(runtime, mscAppModule.getRootPath())) {
            MSCLog.i(TAG, "root path is not webview render, don't prelod");
            return;
        }
        String appId = runtime.getAppId();
        PreloadManager.getInstance().setLastPagePreloadAppId(appId == null ? "" : appId);
        if (TextUtils.equals(mscAppModule.getRootPath(), path)) {
            //首页有快照，预热首页
            if (TemplateHelper.shouldUseSnapshotTemplate(mscAppModule, path)
                    && MSCHornPreloadConfig.get().allowPreloadPageToHome(runtime.getAppId())) {
                rendererManager.preloadHomePage(MSCEnvHelper.getContext(), isLaunchFromRoute);
            }
        }
        preloadDefaultPage(rendererManager);
    }

    /**
     * 预加载空白webview
     */
    public void preloadDefaultPage(IRendererManager rendererManager) {
        MSCLog.i(TAG, "preloadDefaultPage");
        rendererManager.preloadDefaultResources(MSCEnvHelper.getContext(), new ResultCallback() {
            @Override
            public void onReceiveFailValue(Exception e) {
                MSCLog.i(TAG, "preloadDefaultResources failed:", e != null ? e.toString() : "");
            }

            @Override
            public void onReceiveValue(String value) {
                MSCLog.i(TAG, "preloadDefaultResources sucessed:", value);
            }
        }, isLaunchFromRoute);
    }
}
