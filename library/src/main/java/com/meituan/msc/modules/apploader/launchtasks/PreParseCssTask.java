package com.meituan.msc.modules.apploader.launchtasks;

import android.support.annotation.NonNull;
import android.support.annotation.Nullable;
import android.text.TextUtils;
import android.util.Log;

import com.meituan.msc.common.aov_task.context.ITaskExecuteContext;
import com.meituan.msc.common.aov_task.task.AsyncTask;
import com.meituan.msc.common.aov_task.task.ITask;
import com.meituan.msc.common.support.java.util.concurrent.CompletableFuture;
import com.meituan.msc.common.utils.PathUtil;
import com.meituan.msc.modules.apploader.LaunchTaskManager;
import com.meituan.msc.modules.engine.MSCRuntime;
import com.meituan.msc.modules.page.render.ICssPreParseManager;
import com.meituan.msc.modules.update.bean.AppMetaInfoWrapper;

public class PreParseCssTask extends AsyncTask<Void> {

    private static final String TAG = LaunchTaskManager.ITaskName.PRE_PARSE_CSS_TASK;

    private @NonNull final MSCRuntime runtime;

    private final @ICssPreParseManager.Stage int stage;

    public PreParseCssTask(@NonNull MSCRuntime runtime, @ICssPreParseManager.Stage int stage) {
        super(TAG);
        this.runtime = runtime;
        this.stage = stage;
    }

    @Override
    public CompletableFuture<Void> executeTaskAsync(ITaskExecuteContext executeContext) {
        CompletableFuture<Void> future = new CompletableFuture<>();
        ICssPreParseManager cssPreParseManager = runtime.getCssPreParseManager();
        if (cssPreParseManager != null && cssPreParseManager.enablePreParseCssV2()) {
            String targetPath = getTargetPath(executeContext);
            boolean fixed = false;
            if (TextUtils.isEmpty(targetPath)) {
                ITask<?> fetchMetaInfoTask = executeContext.getDependTaskByClass(FetchMetaInfoTask.class);
                if (fetchMetaInfoTask != null) {
                    AppMetaInfoWrapper appMetaInfoWrapper = executeContext.getTaskResult((FetchMetaInfoTask) fetchMetaInfoTask);
                    if (appMetaInfoWrapper != null) {
                        targetPath = appMetaInfoWrapper.getMainPath();
                        fixed = true;
                    }
                }
            }
            Log.d(TAG, "executeTaskAsync() called with: targetPath = [" + targetPath + "], fixed = [" + fixed + "]");
            cssPreParseManager.preCssFileV2(PathUtil.getPath(targetPath), stage);
        }
        future.complete(null);
        return future;
    }

    public String getTargetPath(ITaskExecuteContext executeContext) {
        ITask<?> task = executeContext.getDependTaskByClass(PathCheckTask.class);
        if (task == null) {
            task = executeContext.getDependTaskByClass(PathCfgTask.class);
        }
        if (task == null) {
            return null;
        } else {
            return (String) executeContext.getTaskResult(task);
        }
    }
}
