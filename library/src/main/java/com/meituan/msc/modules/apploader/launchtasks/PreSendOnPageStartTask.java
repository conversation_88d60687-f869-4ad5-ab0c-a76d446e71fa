package com.meituan.msc.modules.apploader.launchtasks;

import android.text.TextUtils;

import com.meituan.msc.common.aov_task.context.ITaskExecuteContext;
import com.meituan.msc.common.aov_task.task.AsyncTask;
import com.meituan.msc.common.aov_task.task.ITask;
import com.meituan.msc.common.config.MSCConfig;
import com.meituan.msc.common.support.java.util.concurrent.CompletableFuture;
import com.meituan.msc.common.utils.PathUtil;
import com.meituan.msc.modules.apploader.LaunchTaskManager;
import com.meituan.msc.modules.engine.MSCRuntime;
import com.meituan.msc.modules.page.render.ReusableRenderer;
import com.meituan.msc.modules.page.render.webview.MSCWebViewRenderer;
import com.meituan.msc.modules.update.bean.AppMetaInfoWrapper;

/**
 * 在已预热webveiw、重复打开页面场景，可以在路由时机直接发送onPageStart事件，不依赖其他事件
 */
public class PreSendOnPageStartTask extends AsyncTask<Void> {
    private final MSCRuntime runtime;

    public PreSendOnPageStartTask(MSCRuntime runtime) {
        super(LaunchTaskManager.ITaskName.PRE_SEND_ON_PAGE_START);
        this.runtime = runtime;
    }

    @Override
    public CompletableFuture<Void> executeTaskAsync(ITaskExecuteContext executeContext) {
        ITask<?> fetchMetaInfoTask = executeContext.findTaskByClass(FetchMetaInfoTask.class, false);
        if (fetchMetaInfoTask == null) {
            return CompletableFuture.completedFuture(null);
        }
        AppMetaInfoWrapper appMetaInfoWrapper = executeContext.getTaskResult((FetchMetaInfoTask) fetchMetaInfoTask);
        String targetPath;
        if (MSCConfig.enableRouteMappingFix()) {
            ITask<?> pathCheckTask = executeContext.getDependTaskByClass(PathCheckTask.class);
            if (pathCheckTask == null) {
                return CompletableFuture.completedFuture(null);
            }
            targetPath = executeContext.getTaskResult((PathCheckTask) pathCheckTask);
        } else {
            ITask<?> pathCfgTask = executeContext.getDependTaskByClass(PathCfgTask.class);
            if (pathCfgTask == null) {
                return CompletableFuture.completedFuture(null);
            }
            targetPath = executeContext.getTaskResult((PathCfgTask) pathCfgTask);
        }
        if (TextUtils.isEmpty(targetPath) && appMetaInfoWrapper != null) {
            targetPath = appMetaInfoWrapper.getMainPath();
        }
        if (TextUtils.isEmpty(targetPath)) {
            return CompletableFuture.completedFuture(null);
        }
        String purePath = PathUtil.getPath(targetPath);
        ReusableRenderer renderer = runtime.getRendererManager().findRenderer(purePath);
        if (renderer instanceof MSCWebViewRenderer) {
            MSCWebViewRenderer webViewRenderer = (MSCWebViewRenderer) renderer;
            webViewRenderer.enablePreSendOnPageStart = true;
            if (webViewRenderer.isBasePackageLoaded) {
                webViewRenderer.onPageStart(targetPath);
            }
        }
        return CompletableFuture.completedFuture(null);
    }
}
