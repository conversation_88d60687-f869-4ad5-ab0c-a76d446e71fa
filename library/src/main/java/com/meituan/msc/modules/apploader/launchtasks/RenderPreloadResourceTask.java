package com.meituan.msc.modules.apploader.launchtasks;

import android.support.annotation.NonNull;
import android.text.TextUtils;

import com.meituan.msc.common.aov_task.context.ITaskExecuteContext;
import com.meituan.msc.common.aov_task.task.AsyncTask;
import com.meituan.msc.common.aov_task.task.ITask;
import com.meituan.msc.common.config.MSCConfig;
import com.meituan.msc.common.support.java.util.concurrent.CompletableFuture;
import com.meituan.msc.common.utils.DisplayUtil;
import com.meituan.msc.common.utils.PathUtil;
import com.meituan.msc.extern.MSCEnvHelper;
import com.meituan.msc.modules.apploader.LaunchTaskManager;
import com.meituan.msc.modules.engine.MSCRuntime;
import com.meituan.msc.modules.reporter.MSCLog;

import java.util.ArrayList;
import java.util.List;

/**
 * <AUTHOR>
 */
public class RenderPreloadResourceTask extends AsyncTask<Void> {
    private final String TAG = LaunchTaskManager.ITaskName.RENDER_PRELOAD_RESOURCE_TASK + "@" + Integer.toHexString(hashCode());
    private final MSCRuntime runtime;

    public RenderPreloadResourceTask(@NonNull MSCRuntime runtime) {
        super(LaunchTaskManager.ITaskName.PRE_INIT_RENDER_TASK);
        this.runtime = runtime;
    }

    @Override
    public CompletableFuture<Void> executeTaskAsync(ITaskExecuteContext executeContext) {
        String targetPath = null;
        if (MSCConfig.enableRouteMappingFix()) {
            ITask<?> task = executeContext.getDependTaskByClass(PathCheckTask.class);
            if (task != null) {
                targetPath = executeContext.getTaskResult((PathCheckTask) task);
            }
        } else {
            ITask<?> task = executeContext.getDependTaskByClass(PathCfgTask.class);
            if (task != null) {
                targetPath = executeContext.getTaskResult((PathCfgTask) task);
            }
        }

        final String path = PathUtil.getPath(targetPath);
        if (TextUtils.isEmpty(path) || !DisplayUtil.isWebViewRender(runtime, path)) {
            MSCLog.i(TAG, "path empty or isNativeRender");
        } else {
            List<String> preloadPages = new ArrayList<>();
            preloadPages.add(path);
            runtime.getRendererManager().preloadBizPagesResouces(MSCEnvHelper.getContext(), preloadPages, null, false);
        }
        CompletableFuture<Void> future = new CompletableFuture<>();
        future.complete(null);
        return future;
    }
}
