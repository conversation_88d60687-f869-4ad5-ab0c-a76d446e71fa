package com.meituan.msc.modules.apploader.launchtasks;

import static com.meituan.msc.common.utils.Constants.MAX_LOGCAT_MESSAGE_LENGTH;
import static com.meituan.msc.modules.IMSCLibraryInterfaceHelper.getIMSCLibraryInterface;

import com.meituan.msc.common.aov_task.context.ITaskExecuteContext;
import com.meituan.msc.common.aov_task.task.AsyncTask;
import com.meituan.msc.common.aov_task.task.ITask;
import com.meituan.msc.common.perf.PerfEventConstant;
import com.meituan.msc.common.support.java.util.concurrent.CompletableFuture;
import com.meituan.msc.common.utils.Constants;
import com.meituan.msc.modules.api.legacy.appstate.AppStateModule;
import com.meituan.msc.modules.api.report.MetricsModule;
import com.meituan.msc.modules.apploader.events.AppLoadException;
import com.meituan.msc.modules.container.ContainerController;
import com.meituan.msc.modules.container.IContainerDelegate;
import com.meituan.msc.modules.container.IContainerManager;
import com.meituan.msc.modules.container.OpenParams;
import com.meituan.msc.modules.engine.AppService;
import com.meituan.msc.modules.engine.MSCHornRollbackConfig;
import com.meituan.msc.modules.engine.MSCRuntime;
import com.meituan.msc.modules.page.IPageModule;
import com.meituan.msc.modules.page.PageManager;
import com.meituan.msc.modules.page.render.AppRouteParam;
import com.meituan.msc.modules.reporter.MSCLoadErrorConstants;
import com.meituan.msc.modules.reporter.MSCLog;
import com.meituan.msc.util.perf.PerfTrace;

public class SendOnAppRouteTask extends AsyncTask<Void> {
    public final static String TAG = "AppRouteTask";
    private final MSCRuntime runtime;

    public SendOnAppRouteTask(MSCRuntime runtime) {
        super("AppRouteTask");
        this.runtime = runtime;
    }

    @Override
    public CompletableFuture<Void> executeTaskAsync(ITaskExecuteContext executeContext) {
        ITask<?> launchTask = executeContext.getDependTaskByClass(ContainerController.StartPageTaskOfLaunch.class);
        ITask<?> routeTask = executeContext.getDependTaskByClass(PageManager.StartPageTaskOfRoute.class);
        AppRouteParam appRouteParam = null;
        if (launchTask != null) {
            MSCLog.i(TAG, "AppRouteTask from launch");
            appRouteParam = executeContext.getTaskResult((ContainerController.StartPageTaskOfLaunch) launchTask);
        } else if (routeTask != null) {
            MSCLog.i(TAG, "AppRouteTask from route");
            appRouteParam = executeContext.getTaskResult((PageManager.StartPageTaskOfRoute) routeTask);
        }
        CompletableFuture<Void> future = new CompletableFuture<>();
        if (appRouteParam != null) {
            injectGlobalField(appRouteParam.isFirstRender(), appRouteParam.getOpenType(), appRouteParam.getLaunchStartTimeCurrentTimeMillis());
            sendOnAppRouteReal(appRouteParam.getParam(), appRouteParam.getViewId(), appRouteParam.getContainerDelegate());
            future.complete(null);
        } else {
            // null场景只有可能页面被销毁，此处不能抛异常，会导致其他页面上报异常
            MSCLog.e(TAG, "appRouteParam is null");
            if (MSCHornRollbackConfig.enableAppRouteTaskGetParamsNoException()) {
                future.complete(null);
            } else {
                future.completeExceptionally(new AppLoadException(MSCLoadErrorConstants.ERROR_CODE_APP_ROUTE_FAILED, "appRouteParam is null", null));
            }
        }
        return future;
    }

    private void sendOnAppRouteReal(String paramsString, int viewId, IContainerDelegate containerDelegate) {
        AppStateModule appStateModule = runtime.getModule(AppStateModule.class);
        if (appStateModule != null) {
            MSCLog.i(TAG, "Real_Send_OnAppRoute", paramsString != null && paramsString.length() < MAX_LOGCAT_MESSAGE_LENGTH ? paramsString : "too long...", viewId);
            // MSCLog.i(TAG, "[MSC_LOG]Task Real_Send_OnAppRoute appStateModule.onAppRoute", ContainerController.TIME_FORMAT.format(new Date(System.currentTimeMillis())));
            appStateModule.onAppRoute(paramsString, viewId);

            IContainerManager containerManager = runtime.getContainerManagerModule();
            if (containerManager != null) {
                IPageModule pageModule = containerManager.getPageByPageId(viewId);
                if (pageModule != null) {
                    pageModule.getRenderer().updateContainerStage("on_app_route");
                }
            }
        } else {
            MSCLog.i(TAG, "Cancel_Send_OnAppRoute_When_AppStateModule_Is_Null", paramsString, viewId);
        }
        PerfTrace.online().instant(PerfEventConstant.SEND_APP_ROUTE).report();
        runtime.getRuntimeReporter().addStatisticsToMap(Constants.APP_ROUTER);

        MetricsModule metricsModule = runtime.getModule(MetricsModule.class);
        if (metricsModule != null) {
            metricsModule.sendFFPResetEvent(viewId);
        }

        if (containerDelegate != null) {
            containerDelegate.sendWidgetData();
        }
        if (getIMSCLibraryInterface() != null) {
            getIMSCLibraryInterface().onPendingAppRouteRun(runtime, paramsString, viewId);
        }
    }

    private void injectGlobalField(boolean firstRender, String openType, long launchStartTimeCurrentTimeMillis) {
        AppService appService = runtime.getModule(AppService.class);
        if (appService != null) {
            if (!firstRender) {
                appService.injectGlobalField(OpenParams.RE_LAUNCH.equals(openType) ? "__appReLaunchStartTime" : "__appLaunchStartTime", String.valueOf(launchStartTimeCurrentTimeMillis));
            }
        } else {
            MSCLog.i(TAG, "Cancel_injectGlobalField_When_AppService_Is_Null");
        }
    }
}
