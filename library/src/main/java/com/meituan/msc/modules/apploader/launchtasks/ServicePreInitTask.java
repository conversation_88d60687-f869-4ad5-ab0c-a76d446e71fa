package com.meituan.msc.modules.apploader.launchtasks;

import android.support.annotation.NonNull;
import android.text.TextUtils;
import android.view.View;

import com.meituan.msc.common.aov_task.context.ITaskExecuteContext;
import com.meituan.msc.common.aov_task.task.AsyncTask;
import com.meituan.msc.common.aov_task.task.ITask;
import com.meituan.msc.common.config.MSCConfig;
import com.meituan.msc.common.support.java.util.concurrent.CompletableFuture;
import com.meituan.msc.common.utils.JsonUtil;
import com.meituan.msc.modules.api.legacy.appstate.AppStateModule;
import com.meituan.msc.modules.apploader.LaunchTaskManager;
import com.meituan.msc.modules.container.OpenParams;
import com.meituan.msc.modules.engine.MSCHornRollbackConfig;
import com.meituan.msc.modules.engine.MSCRuntime;
import com.meituan.msc.modules.page.render.RendererType;
import com.meituan.msc.modules.reporter.MSCLog;
import com.meituan.msc.modules.update.bean.AppMetaInfoWrapper;
import com.meituan.msc.util.perf.PerfTrace;
import com.sankuai.android.jarvis.Jarvis;

import org.json.JSONObject;

import java.util.HashMap;
import java.util.Map;

/**
 * 逻辑层预初始化方案
 * https://km.sankuai.com/collabpage/2554931101
 */
public class ServicePreInitTask extends AsyncTask<Void> {
    private static final String TAG = LaunchTaskManager.ITaskName.SERVICE_PRE_INIT_TASK;

    MSCRuntime mRuntime;
    Boolean preloadWebViewPage;

    public ServicePreInitTask(@NonNull MSCRuntime runtime, Boolean preloadWebViewPage) {
        super(LaunchTaskManager.ITaskName.SERVICE_PRE_INIT_TASK);
        this.mRuntime = runtime;
        this.preloadWebViewPage = preloadWebViewPage;
    }

    @Override
    public CompletableFuture<Void> executeTaskAsync(ITaskExecuteContext executeContext) {
        CompletableFuture<Void> future = new CompletableFuture<>();
        Jarvis.newSingleThreadExecutor("MSC-Launch-ServicePreInit").submit(new Runnable() {
            @Override
            public void run() {
                // enableSplitChunks修改后onPagePreload发送不依赖业务配置，onPagePreload作为前端感知业务预热事件发送，由前端根据业务配置再做区分
                if (!MSCHornRollbackConfig.enableSplitChunks() && !mRuntime.getAppConfigModule().isEnableMSCAppPreload()) {
                    future.complete(null);
                    return;
                }
                PerfTrace.begin("ServicePreInitTask");
                String targetPath = "";
                int viewId = View.NO_ID;
                RendererType engineType = RendererType.WEBVIEW;

                // 获取pagePath
                if (MSCConfig.enableRouteMappingFix()) {
                    ITask<?> pathCheckTask = executeContext.getDependTaskByClass(PathCheckTask.class);
                    if (pathCheckTask != null) {
                        targetPath = executeContext.getTaskResult((PathCheckTask) pathCheckTask);
                    }
                } else {
                    ITask<?> pathCfgTask = executeContext.getDependTaskByClass(PathCfgTask.class);
                    if (pathCfgTask != null) {
                        targetPath = executeContext.getTaskResult((PathCfgTask) pathCfgTask);
                    }
                }
                if (TextUtils.isEmpty(targetPath)) {
                    targetPath = mRuntime.getMSCAppModule().getRootPath();
                }

                engineType = mRuntime.getMSCAppModule().getRendererTypeForPage(targetPath);

                // 获取appMetaInfo
                ITask<?> fetchMetaInfoTask = executeContext.getDependTaskByClass(FetchMetaInfoTask.class);
                if (fetchMetaInfoTask == null) {
                    MSCLog.i(TAG, "fetchMetaInfoTask is null");
                    future.complete(null);
                    return;
                }
                AppMetaInfoWrapper appMetaInfoWrapper = executeContext.getTaskResult((FetchMetaInfoTask) fetchMetaInfoTask);

                AppStateModule appStateModule = mRuntime.getModule(AppStateModule.class);
                Map<String, Object> params = new HashMap<>();
                params.put("path", targetPath);
                params.put("engineType", engineType == RendererType.WEBVIEW ? "webview" : "native");
                params.put("isTab", mRuntime.getAppConfigModule().isTabPage(targetPath));
                params.put("appId", appMetaInfoWrapper.getAppId());
                params.put("appName", appMetaInfoWrapper.getAppName());
                params.put("version", appMetaInfoWrapper.getVersion());
                params.put("buildId", appMetaInfoWrapper.getBuildId());
                params.put("publishId", appMetaInfoWrapper.getPublishId());
                params.put("openType", OpenParams.APP_LAUNCH);
                JSONObject jsonObject = JsonUtil.parseToJson(params);
                String paramsString = jsonObject.toString();
                appStateModule.onPagePreload(paramsString, viewId);
                mRuntime.setServicePreInit(true);
                MSCLog.i(TAG, "AppListener-onPagePreload sended");
                PerfTrace.end("ServicePreInitTask");
                future.complete(null);
            }
        });
        return future;
    }
}
