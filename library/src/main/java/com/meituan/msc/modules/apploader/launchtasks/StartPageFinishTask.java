package com.meituan.msc.modules.apploader.launchtasks;

import com.meituan.msc.common.aov_task.context.ITaskExecuteContext;
import com.meituan.msc.common.aov_task.task.AsyncTask;
import com.meituan.msc.common.support.java.util.concurrent.CompletableFuture;

/**
 * 启动结束任务
 * 由于目前无法存在多个终点任务，以此任务作为启动流程的终点
 */
public class StartPageFinishTask extends AsyncTask<Void> {
    public StartPageFinishTask() {
        super("StartPageFinishTask");
    }

    @Override
    public CompletableFuture<Void> executeTaskAsync(ITaskExecuteContext executeContext) {
        return CompletableFuture.completedFuture(null);
    }
}
