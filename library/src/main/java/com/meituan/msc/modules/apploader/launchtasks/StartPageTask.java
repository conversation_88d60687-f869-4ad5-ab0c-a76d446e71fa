package com.meituan.msc.modules.apploader.launchtasks;

import android.support.annotation.NonNull;

import com.meituan.msc.common.aov_task.context.ITaskExecuteContext;
import com.meituan.msc.common.aov_task.context.ITaskResetContext;
import com.meituan.msc.common.aov_task.task.AsyncTask;
import com.meituan.msc.common.support.java.util.concurrent.CompletableFuture;
import com.meituan.msc.modules.container.IContainerDelegate;
import com.meituan.msc.modules.page.render.AppRouteParam;

import java.lang.ref.WeakReference;

public class StartPageTask extends AsyncTask<AppRouteParam> {
    protected final WeakReference<IContainerDelegate> controllerRef;

    public StartPageTask(String name, IContainerDelegate containerController) {
        super(name);
        controllerRef = new WeakReference<>(containerController);
    }

    @Override
    public CompletableFuture<AppRouteParam> executeTaskAsync(ITaskExecuteContext executeContext) {
        IContainerDelegate controller;
        if (controllerRef == null) {
            return CompletableFuture.completedFuture(null);
        }
        controller = controllerRef.get();
        if (controller == null) {
            return CompletableFuture.completedFuture(null);
        }
        if (controller.getActivity().isFinishing() || controller.getActivity().isDestroyed()) {
            return CompletableFuture.completedFuture(null);
        }
        return doExecuteTaskAsync(controller, executeContext);
    }

    protected CompletableFuture<AppRouteParam> doExecuteTaskAsync(@NonNull IContainerDelegate controllerDelegate,
                                                         ITaskExecuteContext executeContext) {
        return null;
    }

    @Override
    public void onReset(ITaskResetContext resetContext) {
    }
}
