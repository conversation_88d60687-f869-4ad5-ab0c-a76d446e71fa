package com.meituan.msc.modules.apploader.launchtasks;

import android.support.annotation.NonNull;

import com.meituan.msc.common.aov_task.context.ITaskExecuteContext;
import com.meituan.msc.common.aov_task.task.AsyncTask;
import com.meituan.msc.common.support.java.util.concurrent.CompletableFuture;
import com.meituan.msc.extern.MSCEnvHelper;
import com.meituan.msc.modules.apploader.LaunchTaskManager;
import com.meituan.msc.modules.engine.MSCHornRollbackConfig;
import com.meituan.msc.modules.engine.MSCRuntime;
import com.meituan.msc.modules.engine.ResultCallback;
import com.meituan.msc.modules.page.render.webview.WebViewFirstPreloadStateManager;
import com.meituan.msc.modules.page.render.webview.WebViewPreloadReporter;
import com.meituan.msc.modules.preload.MSCHornPreloadConfig;
import com.meituan.msc.modules.reporter.MSCLoadErrorConstants;
import com.meituan.msc.modules.reporter.MSCLog;
import com.meituan.msc.modules.update.bean.PackageInfoWrapper;

/**
 * 基础库预注入到webview
 * 渲染层webview预热后加载基础库
 */
public class WebViewPreloadBaseTask extends AsyncTask<PackageInfoWrapper> {

    MSCRuntime runtime;

    public WebViewPreloadBaseTask(@NonNull MSCRuntime runtime) {
        super(LaunchTaskManager.ITaskName.WEB_VIEW_PRELOAD_BASE_TASK);
        this.runtime = runtime;
    }

    @Override
    public CompletableFuture<PackageInfoWrapper> executeTaskAsync(ITaskExecuteContext executeContext) {
        MSCLog.i("webviewInjectBase", "preloadBasePackage step3 start");
        PackageInfoWrapper packageInfo = executeContext.getTaskResult(FetchBasePkgTask.class);
        final CompletableFuture<PackageInfoWrapper> completableFuture = new CompletableFuture<>();
        needWebViewInjectBasePackage(packageInfo, completableFuture);
        return completableFuture;
    }

    public void needWebViewInjectBasePackage(final PackageInfoWrapper packageInfoWrapper, final CompletableFuture<PackageInfoWrapper> completableFuture) {
        if (MSCHornPreloadConfig.needPreloadWebView() && MSCHornPreloadConfig.needWebViewInjectBasePackage()) {
            injectToRender(packageInfoWrapper, completableFuture);
        } else {
            completableFuture.complete(packageInfoWrapper);
        }
    }

    public void injectToRender(@NonNull PackageInfoWrapper packageInfoWrapper, @NonNull CompletableFuture<PackageInfoWrapper> completableFuture) {
        MSCLog.i("webviewInjectBase", "preloadBasePackage step4 start");
        completableFuture.complete(packageInfoWrapper);
        this.runtime.getRendererManager().preloadWebViewBasePackage(MSCEnvHelper.getContext(), packageInfoWrapper, new ResultCallback() {
            @Override
            public void onReceiveFailValue(Exception e) {
                MSCLog.i("webviewInjectBase", "preloadBasePackage step4 exit");
                if (MSCHornRollbackConfig.readConfig().rollbackLoadErrorReportFix) {
                    runtime.getRuntimeReporter().reportMSCLoadError(MSCLoadErrorConstants.ERROR_INJECT_BASE_JS_WEBVIEW, e);
                } else {
                    runtime.getRuntimeReporter().reportMSCLoadError(runtime.hasContainerAttached(),
                            MSCLoadErrorConstants.ERROR_INJECT_BASE_JS_WEBVIEW, e);
                }
            }

            @Override
            public void onReceiveValue(String value) {
                MSCLog.i("webviewInjectBase", "preloadBasePackage step4 success");
                WebViewFirstPreloadStateManager.getInstance().updateStateAfterPreload();
                WebViewPreloadReporter.getInstance().reportWebViewPartThreeTime();
            }
        });
    }
}
