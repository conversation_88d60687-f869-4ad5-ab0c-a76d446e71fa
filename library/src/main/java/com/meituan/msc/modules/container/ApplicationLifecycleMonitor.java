package com.meituan.msc.modules.container;

import android.app.Activity;
import android.app.Application;
import android.arch.lifecycle.Lifecycle;
import android.content.Context;
import android.support.annotation.NonNull;
import android.support.annotation.Nullable;

import com.meituan.msc.common.model.MSCEnum;
import com.meituan.msc.common.process.MSCProcess;
import com.meituan.msc.common.process.ipc.IPCInvoke;
import com.meituan.msc.common.utils.ActivityLifecycleDispatcher;
import com.meituan.msc.modules.reporter.MSCLog;

import java.lang.ref.WeakReference;
import java.util.HashMap;
import java.util.Map;
import java.util.Queue;
import java.util.concurrent.ConcurrentLinkedDeque;

import static android.arch.lifecycle.Lifecycle.State.CREATED;
import static android.arch.lifecycle.Lifecycle.State.DESTROYED;
import static android.arch.lifecycle.Lifecycle.State.RESUMED;
import static android.arch.lifecycle.Lifecycle.State.STARTED;

/**
 * 监视当前app是否有Activity/HeraActivity处于前台
 * 需在第一个被监听的Activity的super.onCreate前调用，才能监听到其全部生命周期
 *
 * 注意：只在主进程监听了全部进程的activity，且跨进程时取不到activity引用
 */
public class ApplicationLifecycleMonitor {

    private static final String TAG = "ApplicationLifecycleMonitor";
    private static boolean isInited;
    private static final ActivityLifecycleDispatcher sDispatcher = new ActivityLifecycleDispatcher();

    /**
     * 注意调用时机，美团app要求首页T3后，为防不经首页还需要在首个MMP Activity create时再次执行
     */
    public static synchronized void register(Context context) {
        if (isInited) {
            return;
        }

        Context application = context.getApplicationContext();
        if (!(application instanceof Application)) {
            MSCLog.e(TAG, "cannot get Application from context to register lifecycle callbacks");
            return;
        }
        ((Application) application).registerActivityLifecycleCallbacks(sDispatcher);
        sDispatcher.addCallback(ALL.lifecycleCallbacks);
        sDispatcher.addCallback(MSC.lifecycleCallbacks);

        if (!MSCProcess.isInMainProcess()) {
            // 子进程将事件转发至主进程
            sDispatcher.addCallback(new Callback() {
                @Override
                public void onEvent(Lifecycle.Event event, Class<? extends Activity> activityClass, @Nullable Activity activityInstance) {
                    ipcTask.onEvent(event, activityClass, null);    //activity实例不能跨进程
                }
            });
        }

        isInited = true;
    }

    // 分为多种监听范围的Monitor实例，通过filter进行过滤
    public static Monitor ALL = new Monitor("application");  // 监视所在应用（进程）的全部activity，注意在美团app上由于初始化时间靠后，监听不到首页创建，将在再次进入首页时修正记录
    public static Monitor MSC = new Monitor("msc") {
        // 仅监视MSCActivity；注意各个monitor的更新不同步，如在回调中需要使用另外一个中的数据，需要自行post延迟执行

        @Override
        protected boolean filter(Class<? extends Activity> activityClass) {
            return MSCActivity.class.isAssignableFrom(activityClass);
        }
    };

    public static class Monitor {

        private WeakReference<Activity> lastActivityRef;
        private Lifecycle.State state = Lifecycle.State.INITIALIZED;
        private Lifecycle.State highestState = Lifecycle.State.INITIALIZED; // 达到过的最高状态

        // 使用计数方式进行统计，因需要考虑折叠屏平行视界这类两个activity同时resume/start的情况
        // 基本只有started有实际意义，因打开新activity时也会退出resume状态，过于频繁，destroy又不可控，与用户操作无关
        private int createdActivityCount = 0;
        private int startedActivityCount = 0;
        private int resumedActivityCount = 0;

        private final String monitorTag;

        private Monitor(String tag) {
            this.monitorTag = tag;
        }

        /**
         * 获取整个应用的状态，与Activity中状态最高者一致
         */
        public Lifecycle.State getState() {
            return state;
        }

        /**
         * 前后台判断
         *
         * @return
         */
        public boolean isForeground() {
            return state.isAtLeast(STARTED);
        }

        /**
         * 仅在本进程能拿到
         */
        @Nullable
        public Activity getLastActivity() {
            if (lastActivityRef != null) {
                return lastActivityRef.get();
            }
            return null;
        }

        public boolean isFirstActivityCreated() {
            return highestState.isAtLeast(CREATED);
        }

        /**
         * 如果检查不通过，说明输入的activity与本类监视的目标无关，返回false，此次行为将被忽略
         */
        protected boolean filter(Class<? extends Activity> activityClass) {
            return true;
        }

        private void recalculateAppState() {
            // 防止开始监听过晚，监听到的事件不完整，尽量修正
            if (resumedActivityCount > startedActivityCount) {
                startedActivityCount = resumedActivityCount;
            }
            if (startedActivityCount > createdActivityCount) {
                createdActivityCount = startedActivityCount;
            }
            if (resumedActivityCount < 0) {
                resumedActivityCount = 0;
            }
            if (startedActivityCount < 0) {
                startedActivityCount = 0;
            }
            if (createdActivityCount < 0) {
                createdActivityCount = 0;
            }
            Lifecycle.State oldState = state;
            if (resumedActivityCount > 0) {
                state = RESUMED;
            } else if (startedActivityCount > 0) {
                state = STARTED;
            } else if (createdActivityCount > 0) {
                state = CREATED;
            } else {
                state = DESTROYED;
            }

            if (!oldState.isAtLeast(STARTED) && state.isAtLeast(STARTED)) { //升至STARTED以上
                MSCLog.d(TAG, monitorTag, " enter foreground");
                if (highestState.isAtLeast(STARTED)) {  // 排除首次start
                    dispatchEvent(Event.RE_ENTER_FOREGROUND);
                }
            } else if (oldState.isAtLeast(STARTED) && !state.isAtLeast(STARTED)) { //降至STARTED以下
                MSCLog.d(TAG, monitorTag, " enter background");
                dispatchEvent(Event.ENTER_BACKGROUND);
            } else if (oldState!= DESTROYED && state == DESTROYED) { // 销毁
                MSCLog.d(TAG, monitorTag, " destroyed");
                dispatchEvent(Event.DESTROYED);
            }

            if (!highestState.isAtLeast(state)) {   // 最高状态上升
                if (!highestState.isAtLeast(CREATED) && state.isAtLeast(CREATED)) { //首次升至CREATED以上
                    dispatchEvent(Event.FIRST_CREATE);
                }
                highestState = state;
            }
        }

        Callback lifecycleCallbacks = new Callback() {
            @Override
            public void onEvent(Lifecycle.Event event, Class<? extends Activity> activityClass, @Nullable Activity activityInstance) {
                if (!filter(activityClass)) {
                    return;
                }

                // 由于整个生命周期框架注册在美团T3，导致第一个页面的生命周期监听不完整，以下为状态修正逻辑
                fixStateForFirstActivity(event);

                switch (event) {
                    case ON_CREATE:
                        createdActivityCount ++;
                        break;
                    case ON_START:
                        startedActivityCount ++;
                        break;
                    case ON_RESUME:
                        resumedActivityCount ++;
                        if (activityInstance != null) {
                            lastActivityRef = new WeakReference<>(activityInstance);
                        }
                        break;
                    case ON_PAUSE:
                        resumedActivityCount --;
                        break;
                    case ON_STOP:
                        startedActivityCount --;
                        break;
                    case ON_DESTROY:
                        createdActivityCount --;
                        if (getLastActivity() == activityInstance) {
                            lastActivityRef = null;
                        }
                        break;
                }
                recalculateAppState();
            }
        };

        /**
         * 生命周期在美团首页T3阶段注册，故需要将计数修正到的create、start、resume执行之后的状态
         * @param event event
         */
        private void fixStateForFirstActivity(Lifecycle.Event event) {
            if (event == Lifecycle.Event.ON_PAUSE) {
                if (createdActivityCount == 0 || startedActivityCount == 0 || resumedActivityCount == 0) {
                    createdActivityCount = 1;
                    startedActivityCount = 1;
                    resumedActivityCount = 1;
                    state = RESUMED;
                    highestState = RESUMED;
                }
            }
        }

        /**
         * 标识各种可监听的事件，内部用，可随需求新增
         */
        private static class Event extends MSCEnum {
            static final Event RE_ENTER_FOREGROUND = new Event("reEnterForeground");
            static final Event ENTER_BACKGROUND = new Event("enterBackground");
            static final Event FIRST_CREATE = new Event("firstCreate");
            static final Event DESTROYED = new Event("destroyed");

            private Event(@NonNull String name) {
                super(name);
            }
        }

        private final Map<Event, Queue<Runnable>> listeners = new HashMap<>();

        @NonNull
        private synchronized Queue<Runnable> getListenerQueue(Event event) {
            Queue<Runnable> result = listeners.get(event);
            if (result == null) {
                result = new ConcurrentLinkedDeque<>();
                listeners.put(event, result);
            }
            return result;
        }

        private void dispatchEvent(Event event) {
            for (Runnable runnable : getListenerQueue(event)) {
                runnable.run();
            }
        }

        public void addReEnterForegroundListener(@Nullable Runnable runnable) {
            if (runnable != null) {
                getListenerQueue(Event.RE_ENTER_FOREGROUND).add(runnable);
            }
        }

        public void removeReEnterForegroundListener(@Nullable Runnable runnable) {
            if (runnable != null) {
                getListenerQueue(Event.RE_ENTER_FOREGROUND).remove(runnable);
            }
        }

        public void addEnterBackgroundListener(@Nullable Runnable runnable) {
            if (runnable != null) {
                getListenerQueue(Event.ENTER_BACKGROUND).add(runnable);
            }
        }

        public void removeEnterBackgroundListener(@Nullable Runnable runnable) {
            if (runnable != null) {
                getListenerQueue(Event.ENTER_BACKGROUND).remove(runnable);
            }
        }

        public void addFirstCreateListener(@Nullable Runnable runnable) {
            if (runnable != null) {
                getListenerQueue(Event.FIRST_CREATE).add(runnable);
            }
        }
    }

    private static final Callback ipcTask = IPCInvoke.getInvokeProxy(IPCTask.class, MSCProcess.MAIN);

    private static class IPCTask implements Callback {

        @Override
        public void onEvent(Lifecycle.Event event, Class<? extends Activity> activityClass, @Nullable Activity activityInstance) {
            sDispatcher.onEvent(event, activityClass, activityInstance);
        }
    }

    public interface Callback {
        void onEvent(Lifecycle.Event event, Class<? extends Activity> activityClass, @Nullable Activity activityInstance);
    }
}
