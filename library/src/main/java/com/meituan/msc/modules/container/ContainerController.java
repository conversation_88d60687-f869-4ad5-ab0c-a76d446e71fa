package com.meituan.msc.modules.container;

import static android.content.ComponentCallbacks2.TRIM_MEMORY_RUNNING_CRITICAL;
import static android.content.ComponentCallbacks2.TRIM_MEMORY_RUNNING_LOW;
import static android.content.ComponentCallbacks2.TRIM_MEMORY_RUNNING_MODERATE;
import static com.meituan.msc.common.utils.Constants.MAX_LOGCAT_MESSAGE_LENGTH;
import static com.meituan.msc.modules.reporter.MSCCommonTagReporter.UNKNOWN_VALUE;

import android.app.Activity;
import android.app.Application.ActivityLifecycleCallbacks;
import android.content.ComponentName;
import android.content.Intent;
import android.content.res.Configuration;
import android.graphics.Color;
import android.net.Uri;
import android.os.Build;
import android.os.Bundle;
import android.os.Handler;
import android.os.Looper;
import android.os.SystemClock;
import android.support.annotation.LayoutRes;
import android.support.annotation.MainThread;
import android.support.annotation.NonNull;
import android.support.annotation.Nullable;
import android.support.annotation.VisibleForTesting;
import android.text.TextUtils;
import android.util.TypedValue;
import android.view.View;
import android.view.ViewGroup;
import android.view.ViewStub;
import android.widget.Button;
import android.widget.FrameLayout;
import android.widget.ImageView;
import android.widget.LinearLayout;
import android.widget.TextView;

import com.meituan.android.base.transformation.RoundedCornersTransformation;
import com.meituan.android.degrade.interfaces.resource.ResourceManager;
import com.meituan.android.mercury.msc.adaptor.bean.MSCAppMetaInfo;
import com.meituan.android.mercury.msc.adaptor.core.MSCMetaInfoCache;
import com.meituan.android.techstack.CIPDisplayTechType;
import com.meituan.android.techstack.CIPDynamicContentTracker;
import com.meituan.msc.common.aov_task.context.ITaskExecuteContext;
import com.meituan.msc.common.aov_task.context.ITaskResetContext;
import com.meituan.msc.common.config.MSCConfig;
import com.meituan.msc.common.config.MSCPreCreateWebViewConfig;
import com.meituan.msc.common.config.SceneNumber;
import com.meituan.msc.common.constant.APPIDConstants;
import com.meituan.msc.common.constant.PackageNameConstants;
import com.meituan.msc.common.exception.NoStackException;
import com.meituan.msc.common.executor.MSCExecutors;
import com.meituan.msc.common.framework.MPListenerManager;
import com.meituan.msc.common.framework.MSCRunningManager;
import com.meituan.msc.common.framework.interfaces.PageEventListener;
import com.meituan.msc.common.lib.ICleanMMPBizResource;
import com.meituan.msc.common.perf.PerfEventConstant;
import com.meituan.msc.common.report.MetricsEntry;
import com.meituan.msc.common.support.java.util.concurrent.CompletableFuture;
import com.meituan.msc.common.utils.ActivityUtils;
import com.meituan.msc.common.utils.ColorUtil;
import com.meituan.msc.common.utils.ConcaveScreenUtils;
import com.meituan.msc.common.utils.Constants;
import com.meituan.msc.common.utils.DisplayUtil;
import com.meituan.msc.common.utils.FileSizeUtil;
import com.meituan.msc.common.utils.FileUtil;
import com.meituan.msc.common.utils.IntentUtil;
import com.meituan.msc.common.utils.JsonUtil;
import com.meituan.msc.common.utils.MSCResourceWatermarkUtil;
import com.meituan.msc.common.utils.MSCTraceUtil;
import com.meituan.msc.common.utils.MagicWindowUtils;
import com.meituan.msc.common.utils.MiniProgramUtil;
import com.meituan.msc.common.utils.PathUtil;
import com.meituan.msc.common.utils.ScreenUtil;
import com.meituan.msc.common.utils.SystemInfoUtils;
import com.meituan.msc.common.utils.ToastUtils;
import com.meituan.msc.common.utils.UIUtil;
import com.meituan.msc.common.utils.VersionUtil;
import com.meituan.msc.extern.MSCEnvHelper;
import com.meituan.msc.extern.MSCLifecycleCallback;
import com.meituan.msc.lib.R;
import com.meituan.msc.lib.interfaces.IMSCLoadErrorCustom;
import com.meituan.msc.lib.interfaces.container.MSCParams;
import com.meituan.msc.modules.IMSCLibraryInterfaceHelper;
import com.meituan.msc.modules.api.ApiException;
import com.meituan.msc.modules.api.RouteMappingModule;
import com.meituan.msc.modules.api.appLifecycle.MSCAppLifecycle;
import com.meituan.msc.modules.api.appLifecycle.MSCAppLifecycleIPCUtils;
import com.meituan.msc.modules.api.appLifecycle.MSCAppLifecycleManager;
import com.meituan.msc.modules.api.input.KeyboardHeightObserver;
import com.meituan.msc.modules.api.input.KeyboardHeightProvider;
import com.meituan.msc.modules.api.legacy.appstate.AppStateModule;
import com.meituan.msc.modules.api.legacy.appstate.WidgetListener;
import com.meituan.msc.modules.api.msi.api.KeyboardApi;
import com.meituan.msc.modules.api.msi.api.PageBeforeUnloadParam;
import com.meituan.msc.modules.api.msi.navigation.BizNavigationExtraParams;
import com.meituan.msc.modules.api.report.MetricsModule;
import com.meituan.msc.modules.apploader.IAppLoader;
import com.meituan.msc.modules.apploader.InstrumentLaunchManager;
import com.meituan.msc.modules.apploader.LaunchTaskManager;
import com.meituan.msc.modules.apploader.events.AppLoadException;
import com.meituan.msc.modules.apploader.launchtasks.StartPageTask;
import com.meituan.msc.modules.container.fusion.IFusionPageManager;
import com.meituan.msc.modules.container.fusion.MSCFusionActivityMonitor;
import com.meituan.msc.modules.core.DeviceEventManagerInterface;
import com.meituan.msc.modules.engine.AppService;
import com.meituan.msc.modules.engine.EngineHelper;
import com.meituan.msc.modules.engine.MSCApp;
import com.meituan.msc.modules.engine.MSCHornRollbackConfig;
import com.meituan.msc.modules.engine.MSCRuntime;
import com.meituan.msc.modules.engine.RecentMSCManager;
import com.meituan.msc.modules.engine.ResultCallback;
import com.meituan.msc.modules.engine.RuntimeDestroyReason;
import com.meituan.msc.modules.engine.RuntimeManager;
import com.meituan.msc.modules.engine.RuntimeSource;
import com.meituan.msc.modules.engine.requestPrefetch.RequestPrefetchManager;
import com.meituan.msc.modules.manager.IMSCLibraryInterface;
import com.meituan.msc.modules.manager.MSCEvent;
import com.meituan.msc.modules.manager.MSCSubscriber;
import com.meituan.msc.modules.msi.MSIManagerModule;
import com.meituan.msc.modules.page.BasePage;
import com.meituan.msc.modules.page.IPageManagerModule;
import com.meituan.msc.modules.page.IPageModule;
import com.meituan.msc.modules.page.IRuntimeGetter;
import com.meituan.msc.modules.page.Page;
import com.meituan.msc.modules.page.PageManager;
import com.meituan.msc.modules.page.PageManagerModule;
import com.meituan.msc.modules.page.RouteReporter;
import com.meituan.msc.modules.page.TabPage;
import com.meituan.msc.modules.page.UserReporter;
import com.meituan.msc.modules.page.reload.PageInfoArray;
import com.meituan.msc.modules.page.reload.PageStackGlobalCache;
import com.meituan.msc.modules.page.reload.PageStackWatchDog;
import com.meituan.msc.modules.page.render.AppPageReporter;
import com.meituan.msc.modules.page.render.AppRouteParam;
import com.meituan.msc.modules.page.render.ICssPreParseManager;
import com.meituan.msc.modules.page.render.RendererType;
import com.meituan.msc.modules.page.render.webview.WebViewCacheManager;
import com.meituan.msc.modules.page.render.webview.WebViewFirstPreloadStateManager;
import com.meituan.msc.modules.page.transition.PageTransitionConfig;
import com.meituan.msc.modules.page.view.coverview.IPageLifecycleInterceptor;
import com.meituan.msc.modules.page.widget.FlowerLoadingIndicator;
import com.meituan.msc.modules.preload.MSCHornPreloadConfig;
import com.meituan.msc.modules.preload.PendingBizPreloadTasksManager;
import com.meituan.msc.modules.preload.PreloadTasksManager;
import com.meituan.msc.modules.reporter.CommonTags;
import com.meituan.msc.modules.reporter.CrashReporterHelper;
import com.meituan.msc.modules.reporter.JSErrorRecorder;
import com.meituan.msc.modules.reporter.MSCLoadErrorConstants;
import com.meituan.msc.modules.reporter.MSCLog;
import com.meituan.msc.modules.reporter.MSCReporter;
import com.meituan.msc.modules.reporter.PageRecord;
import com.meituan.msc.modules.reporter.ProcessMonitor;
import com.meituan.msc.modules.reporter.ReporterFields;
import com.meituan.msc.modules.reporter.memory.MemoryMonitor;
import com.meituan.msc.modules.reporter.outlink.IOutLinkParams;
import com.meituan.msc.modules.reporter.outlink.OutLinkParamsProvider;
import com.meituan.msc.modules.router.MMPRouterManager;
import com.meituan.msc.modules.service.ServiceInstance;
import com.meituan.msc.modules.update.AppConfigModule;
import com.meituan.msc.modules.update.MSCAppModule;
import com.meituan.msc.modules.update.PackageLoadReporter;
import com.meituan.msc.modules.update.bean.AppMetaInfoWrapper;
import com.meituan.msc.modules.update.bean.PackageInfoWrapper;
import com.meituan.msc.modules.update.metainfo.AppCheckUpdateManager;
import com.meituan.msc.modules.update.metainfo.BackgroundCheckUpdateModule;
import com.meituan.msc.util.perf.PerfTrace;
import com.meituan.msi.BuildConfig;
import com.meituan.msi.bean.LifecycleData;
import com.meituan.msi.bean.NavActivityInfo;
import com.meituan.msi.privacy.permission.MsiPermissionGuard;
import com.sankuai.meituan.serviceloader.ServiceLoader;
import com.squareup.picasso.RequestCreator;

import org.json.JSONException;
import org.json.JSONObject;

import java.util.ArrayList;
import java.util.HashMap;
import java.util.LinkedList;
import java.util.List;
import java.util.Map;
import java.util.Set;
import java.util.concurrent.CopyOnWriteArrayList;

/**
 * 美团小程序页面容器（Activity/Widget）的代码共用部分
 */
public class ContainerController extends ContainerDelegate implements PageEventListener, KeyboardHeightObserver {

    public static final String CLASS_NAME = "ContainerController";
    protected final String TAG = "ContainerController@" + Integer.toHexString(hashCode());

    private static final long SHOW_LOADING_VIEW_DELAY = 500;
    private static final long FIRST_RENDER_TIMEOUT = 5000;

    private static final String MSC_PAGE_STACK_MARK = "__msc_stack_save";
    public static final String EXTRA_RELAUCH_ON_ERROR = "relaunchOnError";  // 禁止复用任何engine（启动失败进行relaunch重启）
    public static final String IS_FUSION_API_STARTED = "isFusionApiStarted";// 融合模式API调用启动
    public static final String IS_LIVE_PIP_STARTED = "isLivePIPStarted"; //画中画启动
    public static final String IS_NEW_PROCESS = "isNewProcess"; // 子进程为本次启动创建

    public static final String START_FROM_MIN_PROGRAM = "startFromMinProgram";
    //public static final String BACK_FROM_MIN_PROGRAM = "backFromMinProgram";
    public static final String BACK_FROM_EXTERNAL_NATIVE_URL = "backFromExternalNativeUrl";

    // 小程序包业务版本号
    private volatile String mmpAppVersion;

    public static volatile boolean sInited;

    // 标记骑行小程序首次创建
    private static boolean sBikeFirst = true;

    private static final Handler mainHandler = new Handler(Looper.getMainLooper());

    // 以下的几个对象在正常完成onCreate后（Intent有效，未直接finish），一定不为null，使用时无需检查
    protected MSCApp mApp;
    protected MSCRuntime mRuntime;
    protected IAppLoader mAppLoader;
    private JSErrorRecorder mJsErrorRecorder;
    private MSIManagerModule mApisManager;
    protected AppService mAppService;
    protected IPageManagerModule mPageManager;

    private FrameLayout mContainerView;
    private FrameLayout mLoadingBg;
    @Nullable
    private LinearLayout mLoadingView;  //viewStub
    @Nullable
    private TextView mTitle;  //in mLoadingView
    @Nullable
    private FrameLayout mIconContainer;
    @Nullable
    private ImageView mIcon;  //in mLoadingView
    @Nullable
    private FlowerLoadingIndicator mFlowerLoadingIndicator; //in mLoadingView

    @Nullable
    private View mErrorView;  //viewStub

    protected long launchStartTimeCurrentTimeMillis;
    protected volatile boolean firstRender = false;

    protected volatile boolean isRelaunching = false;   //本Activity正在执行由onNewIntent触发的relaunch的过程中
    protected boolean isReusingEngine;  // 引擎之前启动过界面，这不是引擎关联的第一个Container；注意很多情况需要结合MiniApp.isPageLaunched进行判断
    protected boolean isFirstPageStackActivity; //第一个页面栈Activity（不记Widget），需要以appLaunch方式启动
    protected volatile boolean isOnAppRouteCalled = false;
    protected long clientReadyDuration = -1; // 页面开始跳转到客户端onAppRoute事件发送前准备耗时
    protected volatile boolean isInitialPage = true;

    protected String mAppId;
    /**
     * 本次切入本小程序的来源，包括activityResult和onNewIntent，用于向前端传入数据，一次性，传完即清空
     */
    protected String mSrcAppID, mSrcExtraData;
    /**
     * 在onCreate或从小程序启动的onNewIntent中赋值，用于记录从哪个小程序跳转（前进）至此，以便back回上一小程序
     */
    // TODO: 8/7/24 linyinong mRealLaunchPath改为真正映射后的页面，并添加origin
    protected String mLaunchRoutePath, mRealLaunchPath;
    protected int mScene;

    private KeyboardHeightProvider keyboardHeightProvider;
    private final List<KeyboardHeightObserver> keyboardHeightObserverList = new ArrayList<>();
    protected boolean isDestroyHandled;
    protected boolean isNavigateBackOnActivityCloseHandled;

    private Runnable activityResultRunnable = null;

    // for page stack re-loader
    private String stackSavedFlag = null;
    @Nullable
    private PageStackWatchDog pageStackWatchDog;

    private String mBackFromExternalNativeUrl;

    private CompletableFuture<AppRouteParam> startPageFuture;
    private boolean isPageNotFound;
    private String handleCloseContainerTriggerSource;

    private boolean disablePreCreatePage;
    // 外链冷启
    private boolean isColdStart;
    // 冷启动的第一个页面
    private boolean isLaunchFirstPage;
    private ContainerDebugLaunchData debugLaunchData;
    private ActivityLifecycleCallbacks activityLifecycleCallbacks;
    private boolean fragmentActivityOnPause;
    private final Object createPageManagerModuleLock = new Object();

    private boolean isSwitchTabOnNewIntent;
    private int mRouteId;
    private boolean isActivityStartedFromScratch;
    private boolean isUrlExternalApp;

    private boolean isOutLinkColdLaunch = false;
    private Map<String, Object> mOutLinkMap = null;

    private boolean enableLoadingViewStyleOpt = true;

    ContainerController(IMSCContainer container) {
        mContainer = container;
        mActivity = container.getActivity();
    }

    public boolean isFragmentActivityOnPause() {
        return fragmentActivityOnPause;
    }

    public void setRouteId(int routeId) {
        this.mRouteId = routeId;
        MSCLog.e(TAG, "setRouteId" + routeId);
    }

    public void setFragmentActivityOnPause(boolean fragmentActivityOnPause) {
        this.fragmentActivityOnPause = fragmentActivityOnPause;
    }

    public boolean isCalledOnAppEnterBackground() {
        return calledOnAppEnterBackground;
    }

    public void setCalledOnAppEnterBackground(boolean calledOnAppEnterBackground) {
        this.calledOnAppEnterBackground = calledOnAppEnterBackground;
    }

    private volatile boolean calledOnAppEnterBackground; // 标识是否调用了onAppEnterBackground，不是代表小程序是否在后台

    public boolean isFirstContainer() {
        return firstContainer;
    }

    public void setFirstContainer(boolean firstContainer) {
        this.firstContainer = firstContainer;
    }

    private boolean firstContainer;

    public void setLeaveAppInfo(String leaveAppInfo) {
        this.leaveAppInfo = leaveAppInfo;
    }

    public void setIsUrlExternalApp(boolean isUrlExternalApp) {
        this.isUrlExternalApp = isUrlExternalApp;
    }

    @Override
    public boolean isLaunchOnError() {
        if (firstRender) {
            return false;
        }
        return IntentUtil.getBooleanExtra(getIntent(), EXTRA_RELAUCH_ON_ERROR, false);
    }

    @Override
    public boolean isTransparentContainer() {
        return getMSCContainer().isTransparentContainer();
    }

    @Override
    public ViewGroup.LayoutParams createPageLayoutParams(BasePage page) {
        FrameLayout.LayoutParams layoutParams = new FrameLayout.LayoutParams(FrameLayout.LayoutParams.MATCH_PARENT,
                FrameLayout.LayoutParams.MATCH_PARENT);
        if (MSCHornRollbackConfig.readConfig().rollbackHalfDialog) {
            return layoutParams;
        }
        if (page instanceof TabPage) {
            // 半屏弹窗容器中，Tab页默认全部为半屏效果
            layoutParams.topMargin = getTopMarginAtTransparentContainer();
            MSCLog.i(TAG, "createRootViewLayoutParams TabPage");
        } else if (page instanceof Page) {
            IPageModule curPageModule = ((Page) page).getCurPageModule();
            PageTransitionConfig pageTransitionConfig = curPageModule.getPageTransitionConfig();
            if (page.isHalfScreenPage()) {
                layoutParams.topMargin = HalfPageUtils.getValidHalfPageTopMargin(getActivity(), pageTransitionConfig);
                MSCLog.i(TAG, "createRootViewLayoutParams Page half");
            }
        }
        return layoutParams;
    }

    @Override
    public boolean isMSCInitedAtContainerOnCreate() {
        return getMSCContainer().isMSCInitedAtContainerOnCreate();
    }

    @Override
    public void sendWidgetData() {
        if (!isFirstOnAppRouteSend) {
            isFirstOnAppRouteSend = true;
            sendPendingWidgetData();
        }
    }

    @Override
    public void setStartPageFuture(CompletableFuture<AppRouteParam> future) {
        startPageFuture = future;
    }

    private String leaveAppInfo;

    public MSCRuntime getRuntime() {
        return mRuntime;
    }

    public MSCApp getMiniApp() {
        return mApp;
    }

    void setAppId(String appId) {
        mAppId = appId;
        RecentMSCManager.recordRecentAPPAsync(appId, isUrlExternalApp);
        enableLoadingViewStyleOpt = MSCHornRollbackConfig.enableLoadingViewStyleOpt(appId);
    }

    public String defaultGetMPAppId() {
        return defaultGetMPAppId(getIntent());
    }

    /**
     * 解析小程序id
     */
    public static String defaultGetMPAppId(Intent intent) {
        String appID = IntentUtil.getStringExtra(intent, MSCParams.APP_ID);
        return TextUtils.isEmpty(appID) ? MSCEnvHelper.getDefaultAppID() : appID;
    }

    @Nullable
    public String defaultGetMPAppVersion() {
        return mmpAppVersion;
    }

    /**
     * 获取小程序启动页面
     */
    public String getMPTargetPath() {
        return mContainer.getMPTargetPath();
    }

    public String defaultGetMPTargetPath() {
        return getStringExtra(MSCParams.TARGET_PATH);
    }

    protected boolean needLoadingView() {
        if (enableLoadingViewStyleOpt) {
            // 跳链中配置mscHideLoadingView=true则可以隐藏LoadingView
            if (IntentUtil.getBooleanExtra(getIntent(), MSCParams.MSC_HIDE_LOADING_VIEW, false)) {
                return false;
            }
        }
        if (TextUtils.equals(getAppId(), APPIDConstants.QI_XING)) {
            if (sBikeFirst) {
                sBikeFirst = false;
                MSCExecutors.runOnUiThread(new Runnable() {
                    @Override
                    public void run() {
                        if (mContainerReporter != null && mRuntime != null) {
                            mContainerReporter.reportProcessTime(mRuntime);
                        }
                    }
                });
            }
            return false;
        }
        if (MSCHornRollbackConfig.enableHideLoadingIconAndAnimation(getAppId())) {
            return false;
        }
        return mContainer.needLoadingView();
    }

    protected boolean needLoadingAppInfo() {
        return mContainer.needLoadingAppInfo();
    }

    /**
     * 为兼容Fragment容器，delegate的接口形式采用Fragment生命周期，此处模仿
     * 调用时机较早，在super.onCreate之前，且调用后Activity还可能因参数检查而直接退出
     * 注意不应进行复杂或生命周期相关的操作，那些可以放到onActivityCreated后
     */
    public void onCreate(@Nullable Bundle savedInstanceState) {
        if (MSCTraceUtil.TRACE_LEVEL >= MSCTraceUtil.LEVEL_LIGHT) {
            PerfTrace.begin("ContainerController#onCreate");
        }
        if (savedInstanceState != null) {
            stackSavedFlag = savedInstanceState.getString(MSC_PAGE_STACK_MARK);
        }
        super.onCreate(savedInstanceState);
        MSCLog.i(TAG, "onCreate,", (savedInstanceState != null ? "recreate" : "first create"), ",appId=", mAppId, ",targetPath=" + getMPTargetPath());

        if (getIntent() != null && getIntent().getData() != null) {
            MSCLog.i(TAG, getActivity().getClass().getSimpleName(), getIntent().getData());
        }

        // 确保ReactChoreographer实例初始化，未初始化时getInstance会未空，初始化需要在主线程执行
        // https://ones.sankuai.com/ones/product/31464/workItem/defect/detail/6033187?activeTabName=first
        ServiceInstance.staticInit();
        initStatus();
        CrashReporterHelper.getContainerRecorder().recordContainerInitTime();
        CrashReporterHelper.getContainerRecorder().recordContainerVersion(BuildConfig.AAR_VERSION);
        isActivityStartedFromScratch = true;
        if (MSCTraceUtil.TRACE_LEVEL >= MSCTraceUtil.LEVEL_LIGHT) {
            PerfTrace.end("ContainerController#onCreate");
        }
        storeOpenLinkParams();
    }

    private void storeOpenLinkParams() {
        if (MSCHornRollbackConfig.readConfig().enableOutLinkParamsReport) {
            IOutLinkParams outLinkParams = OutLinkParamsProvider.getService();
            if (outLinkParams != null) {
                isOutLinkColdLaunch = outLinkParams.isOutLinkColdLaunch(getIntent());
                mOutLinkMap = outLinkParams.getOutLinkMetricsMap();
            }
        }
    }

    private void clearOpenLinkParams() {
        if (MSCHornRollbackConfig.readConfig().enableOutLinkParamsReport) {
            isOutLinkColdLaunch = false;
            mOutLinkMap = null;
        }
    }

    @Override
    public boolean isOutLinkColdLaunch() {
        return isOutLinkColdLaunch;
    }

    @Override
    public Map<String, Object> getOutLinkMap() {
        return mOutLinkMap;
    }

    @Override
    public PageEventListener getPageEventListener() {
        return this;
    }

    @Override
    public void onStart() {
        super.onStart();
        if (!MSCHornRollbackConfig.isRollbackLifecycle(getAppId())) {
            activityLifecycleCallbacks = new ActivityLifecycleCallbacks() {
                @Override
                public void onActivityCreated(@NonNull Activity activity,
                                              @Nullable Bundle savedInstanceState) {

                }

                @Override
                public void onActivityStarted(@NonNull Activity activity) {
                    if (activity == getActivity()) {
                        return;
                    }

                    Intent originalIntent = activity.getIntent();
                    MSCPageStateInstance pageStateInstance = MSCPageStateInstance.getInstance();
                    MSCLaunchParams mscLaunchParams = new MSCLaunchParams(originalIntent);
                    String nextAppId;
                    nextAppId = mscLaunchParams.getAppId();
                    if (nextAppId != null) {
                        pageStateInstance.getNextAppIdSet().add(nextAppId);
                    }
                    String curAppId = getAppId();
                    if (pageStateInstance.isNextPageSameMSC(curAppId)) {
                        if (MSCHornRollbackConfig.isRollbackStartPageAdvanced(curAppId) || MSCHornRollbackConfig.isRollbackActivityOnStartNotBackground()) {
                            onAppEnterBackground(true);
                        } else {
                            // onAppRoute提前后，在某些场景（1.A->中间路由页->B 2.B navigateBack A (A和B均是同小程序page））下，page在此处发onAppEnterBackground，可能会导致栈顶页面onHide
                            if (isActivity()) {
                                // page在下个页面onStart识别到同小程序，不再发onAppEnterBackground ，将标识位设置为true是为了保证以后也不再执行onAppEnterBackground
                                setCalledOnAppEnterBackground(true);
                            } else {
                                // widget仍然发onWidgetEnterBackground,因为会传pageID，不会导致生命周期问题
                                onAppEnterBackground(true);
                            }
                        }
                        return;
                    }

                    String className = activity.getClass().getName();
                    if (MSCConfig.isNeedAppEnterBackground(className)) {
                        onAppEnterBackground(false);
                        return;
                    }
                    TypedValue typedValue = new TypedValue();
                    activity.getTheme()
                            .resolveAttribute(android.R.attr.windowIsTranslucent, typedValue, true);
                    boolean isTranslucent = typedValue.data != 0;
                    if (isTranslucent) {
                        onAppEnterBackground(false);
                        return;
                    }
                }

                @Override
                public void onActivityResumed(@NonNull Activity activity) {
                    if (isTabWidget() && getActivity() == activity) {
                        fragmentActivityOnPause = false;
                    }
                }

                @Override
                public void onActivityPaused(@NonNull Activity activity) {
                    if (isTabWidget() && getActivity() == activity) {
                        fragmentActivityOnPause = true;
                    }
                }

                @Override
                public void onActivityStopped(@NonNull Activity activity) {

                }

                @Override
                public void onActivitySaveInstanceState(@NonNull Activity activity,
                                                        @NonNull Bundle outState) {

                }

                @Override
                public void onActivityDestroyed(@NonNull Activity activity) {

                }
            };
            getActivity().getApplication()
                    .registerActivityLifecycleCallbacks(activityLifecycleCallbacks);
        }
        if (isActivityStartedFromScratch) {
            isActivityStartedFromScratch = false;
        } else {
            // 仅页面从后台进前台时触发，页面新建时不会触发
            if (MSCHornRollbackConfig.enableOnPageStart() && getPageManager() != null) {
                Page topPage = getPageManager().getTopPage();
                if (topPage != null) {
                    topPage.getMSILifecycleCallback().onPageStart(topPage.getViewId(), new LifecycleData());
                }
            }
        }
    }

    @Override
    public void onStop() {
        super.onStop();
        if (!MSCHornRollbackConfig.isRollbackLifecycle(getAppId())) {
            getActivity().getApplication()
                    .unregisterActivityLifecycleCallbacks(activityLifecycleCallbacks);
            onAppEnterBackground(MSCPageStateInstance.getInstance().isNextPageSameMSC(getAppId()));
        }
        // 仅页面从前台进后台时触发,页面退出不触发
        if (MSCHornRollbackConfig.enableOnPageStart() && getPageManager() != null && !isFinishing()) {
            Page topPage = getPageManager().getTopPage();
            if (topPage != null) {
                topPage.getMSILifecycleCallback().onPageStop(topPage.getViewId(), new LifecycleData());
            }
        }
    }

    public void onConfigurationChanged(Configuration newConfig) {
        MSCLog.i(TAG, "onConfigurationChanged", newConfig);
        DisplayUtil.setDisplayMetrics(getActivity());
        // windowInfo 相关有获取实际布局操作，在下一个周期进行调用
        MSCExecutors.postOnUiThread(new Runnable() {
            @Override
            public void run() {
                if (mRuntime != null) {
                    mRuntime.apisManager.onConfigurationChanged(newConfig);
                }
            }
        });
    }

    @Override
    public void onWindowFocusChanged(boolean hasFocus) {
        super.onWindowFocusChanged(hasFocus);
        if (mPageManager != null) {
            IPageModule topPage = mPageManager.getTopPage();
            int viewId = topPage != null ? topPage.getId() : 0;
            AppStateModule appStateModule = mRuntime.getModule(AppStateModule.class);

            if (appStateModule != null) {
                appStateModule.onWindowFocusChange(hasFocus, viewId);
            }
        }
    }

    private void dataPrefetchAfterPageStart() {
        if (RequestPrefetchManager.isAppLevelPrefetch(mRuntime.getMSCAppModule(), getAppId())) {
            return;
        }
        // 业务预热、保活场景在这里执行数据预拉取操作
        final RequestPrefetchManager manager;
        String targetPath = getMPTargetPath();

        manager = mRuntime.getOrCreateRequestPrefetchManager();
        if (!manager.isSyncPrefetching()) {
            boolean perform = true;
            // 新逻辑中，如果当前页面不是闪购商家页，直接退出不提前数据预拉取；如果是闪购商家页且正在发起数据预拉取，也直接退出
            // 如果已经有数据缓存或者正在发起数据预拉取，则退出
            if (manager.isExistCachedDataOrIsFetchingDataFromPageOutSide(targetPath)) {
                MSCLog.i(TAG, "exist cached data or isSyncPrefetching");
                perform = false;
            }
            if (perform) {
                manager.reset();
                manager.startPrefetch(getActivity(), mRuntime.getMSCAppModule().getMetaInfo(), targetPath, mScene);
            }
        } else {
            MSCLog.i(TAG, "isSyncPagePrefetching");
        }
        manager.resetSyncPrefetching();
    }

    /**
     * App级数据预拉取：冷启、预热、保活
     */
    private void dataPrefetchForApp() {
        if (!RequestPrefetchManager.isAppLevelPrefetch(mRuntime.getMSCAppModule(), getAppId())) {
            return;
        }
        // 保活场景不再发送APP级数据预拉取。拼好饭除外
        if (isReusingEngine && MSCHornRollbackConfig.readConfig().disableAppPrefetchWhenKeepAlive && !APPIDConstants.PIN_HAO_FAN.equals(getAppId())) {
            return;
        }
        if (!MSCHornRollbackConfig.isRollbackAppPrefetchFirstContainer()) {
            if (mRuntime.isHasAppPrefetch()) {
                return;
            }
        }
        mRuntime.setHasAppPrefetch(true);
        MSCLog.i(TAG, "app data prefetch");
        RequestPrefetchManager requestPrefetchManager = mRuntime.getOrCreateRequestPrefetchManager();
        if (!requestPrefetchManager.isSyncPrefetching()) {
            requestPrefetchManager.reset();
            requestPrefetchManager.startAppPrefetch(getActivity(), mRuntime.getMSCAppModule().getMetaInfo(), getMPTargetPath(), mScene);
        } else {
            MSCLog.i(TAG, "isSyncAppPrefetching");
        }
    }

    public void createRuntime(Bundle savedInstanceState) {
        if (MSCTraceUtil.TRACE_LEVEL >= MSCTraceUtil.LEVEL_LIGHT) {
            PerfTrace.begin("ContainerController#createRuntime");
        }
        if (getIMSCLibraryInterface() != null) {
            getIMSCLibraryInterface().onContainerControllerCreateRuntime();
        }

        boolean isPendingPreloadBiz = !MSCHornRollbackConfig.get().getConfig().isRollbackPendingPreloadBiz;
        if (isPendingPreloadBiz) {
            ContainerStartState.instance.setIsContainerLaunching(true);
        }
        checkAndInitContainerLaunchData();
        // 在拿到了intent, 确定了appid 和 checkupdateUrl之后，就可以去异步获取版本信息了，不用等待Runtime初
        // 始化完毕。后面LaunchTaskManager执行到FetchMetainfoTask的时候，仍然会去尝试检查更新，在
        // AppCheckUpdateManager的checkUpdate方法内部有去重，不会重复执行。
        boolean disablePrefetch = IntentUtil.getBooleanExtra(getIntent(), MSCParams.DISABLE_PREFETCH, false);
        BizNavigationExtraParams bizNavigationExtraParams = new BizNavigationExtraParams.Builder()
                .setDisablePrefetch(disablePrefetch)
                .build();
        boolean needCheckUpdate = true;
        boolean needCreateNewRuntime = true;
        if (MSCHornRollbackConfig.enableLaunchTaskOnRoute()) {
            String sRouteId = String.valueOf(mRouteId);
            MSCRuntime mscRuntime = InstrumentLaunchManager.getInstance().getRuntime(sRouteId);
            if (mscRuntime != null) { //路由时机已获取引擎。此处使用同一个引擎
                mRuntime = mscRuntime;
                needCheckUpdate = false;
                needCreateNewRuntime = false;
            } else if (InstrumentLaunchManager.getInstance().getLaunchInfo(sRouteId) != null) { //已触发更新
                needCheckUpdate = false;
            }
        }
         if (needCheckUpdate) {
            AppCheckUpdateManager.getInstance().checkUpdateBeforeRuntimeInit(mAppId, debugLaunchData);
        }

        if (needCreateNewRuntime) {
            mRuntime = EngineHelper.setupRuntimeForLaunch(mAppId, getMPTargetPath(), debugLaunchData, isDisableReuseAny(), isUrlExternalApp);
        }

        mRuntime.getMSCAppModule().setUrlExternalApp(isUrlExternalApp);

        // 记录启动时间
        mRuntime.getRuntimeReporter().getStatisticsMap().put(Constants.LAUNCH, launchStartTimeCurrentTimeMillis);
        RuntimeManager.setLatestAttachToContainerRuntime(mRuntime);
        // 重置当前页面是否存在RList，同时启动多个页面场景有覆盖风险，不影响分析，暂不处理
        mRuntime.setHasRListAtCurrentPage(false);
        mRuntime.batchCheckUpdateErrorMsg = AppCheckUpdateManager.sBatchCheckUpdateErrorMsg;
        mRuntime.setIsPendingPreloadBiz(isPendingPreloadBiz);
        mAppLoader = mRuntime.getModule(IAppLoader.class);
        if (MSCHornRollbackConfig.readConfig().disableAppPrefetchWhenKeepAlive) {
            isReusingEngine = mAppLoader.isLaunched();
        }
        // 业务预热场景下，元信息已获取则手动调用update
        if (mRuntime.getModule(IAppLoader.class).isMetaInfoFetchSuccess()) {
            dataPrefetchAfterPageStart();
            dataPrefetchForApp();
            if (mRuntime.getSource() == RuntimeSource.BIZ_PRELOAD) {
                updateAppProp();
            }
        }

        // Native渲染页面，业务预热、保活启动场景，已获取到业务包时 执行CSS预解析
        preParseCssFileWhenUseNativeRender();

        mRuntime.subscribe(IAppLoader.APP_PROP_UPDATED, metainfoSubscriber);
        mRuntime.subscribe(IAppLoader.LOAD_FAILED, loadFailSubscriber);
        mApp = mRuntime.getApp();
        setContainerManagerModule(mRuntime.getContainerManagerModule());
        mAppLoader.cleanTaskExecuteStateForReport();
        if (getContainerManagerModule() != null) {
            getContainerManagerModule().onContainerCreate(this);
        }
        boolean isReLaunchOnError = IntentUtil.getBooleanExtra(getIntent(), EXTRA_RELAUCH_ON_ERROR, false);
        mContainerReporter = ContainerReporter.create(this, mRuntime, isWidget(), getMPTargetPath(), isReLaunchOnError);
        if (isWidget()) {
            mAppLoader.preload(getMPTargetPath(), mRouteId, isWidget(), bizNavigationExtraParams);
        }
        mContainerReporter.onCreate();
        triggerResourceControlWhenPreloadUsed();
        if (MSCTraceUtil.TRACE_LEVEL >= MSCTraceUtil.LEVEL_LIGHT) {
            PerfTrace.end("ContainerController#createRuntime");
        }
    }

    private void triggerResourceControlWhenPreloadUsed() {
        // 业务预热
        if (MSCHornPreloadConfig.enableControlBizPreload()) {
            if (mRuntime.hasTriggerBizPreload) {
                ResourceManager.getInstance().traceWhenPreloadUsed("MSC", "bizPreload", mRuntime.getAppId());
            }
        }
        // 基础库预热
        if (MSCHornPreloadConfig.enableControlBasePreload()) {
            if (mRuntime.hasTriggerBasePreload) {
                ResourceManager.getInstance().traceWhenPreloadUsed("MSC", "basePreload", "mscsdk");
            }
        }
    }

    private void preParseCssFileWhenUseNativeRender() {
        MSCExecutors.submit(new Runnable() {
            @Override
            public void run() {
                if (MSCHornPreloadConfig.disablePreParseCss()) {
                    MSCLog.i(TAG, "disable pre parse css file");
                    return;
                }
                if (MSCHornPreloadConfig.disablePreParseCssWhenBizPreload()) {
                    return;
                }
                if (!mRuntime.getMSCAppModule().hasMetaInfo()) {
                    return;
                }
                if (mRuntime.getMSCAppModule().getMainPackageWrapper() == null) {
                    return;
                }
                ensureLaunchPath();
                RendererType type = mRuntime.getMSCAppModule().getRendererTypeForPage(mRealLaunchPath);
                if (type == RendererType.WEBVIEW) {
                    return;
                }
                ICssPreParseManager cssPreParseManager = mRuntime.getCssPreParseManager();
                if (cssPreParseManager != null) {
                    cssPreParseManager.preParseCss(mRealLaunchPath);
                }
            }
        });
    }

    private void checkAndInitContainerLaunchData() {
        if (debugLaunchData == null) {
            debugLaunchData = new ContainerDebugLaunchData(getIntent());
        }
    }

    private void preCreateWebViewIfNeed(String targetPath) {
        if (MSCPreCreateWebViewConfig.get().enablePreCreateWebView(mAppId)) {
            MSCLog.i(TAG, "preCreateWebViewIfNeed", mAppId, targetPath);
            mRuntime.webViewCacheManager.cacheFirstWebView(getActivity(), WebViewCacheManager.WebViewCreateScene.CREATE_AT_PAGE_LAUNCH, mAppId);

            if (MSCHornPreloadConfig.isInBizPreloadListWhenWidgetSetUri(mAppId)
                    && MSCHornPreloadConfig.needInvokeAfterPreCreateWebViewIfNeed()) {
                preInjectWebViewResource("preCreateWebViewIfNeed");
            }
        }
    }

    private void preInjectWebViewResource(String source) {
        if (getRuntime().getRendererManager().isCreatedPreloadRender) {
            MSCLog.i(TAG, "preInjectWebViewResource canceled", source);
            return;
        }
        getRuntime().getRendererManager().isCreatedPreloadRender = true;
        PackageInfoWrapper basePackage = getRuntime().getMSCAppModule().getBasePackage();
        if (basePackage == null) {
            MSCLog.i(TAG, "preInjectWebViewResource canceled, basePackage is null", source);
            return;
        }
        if (!getRuntime().getMSCAppModule().hasMetaInfo()) {
            MSCLog.i(TAG, "preInjectWebViewResource canceled, metaInfo is null", source);
            return;
        }
        MSCLog.i(TAG, "preInjectWebViewResource", source);


        // 需要在这里在主线程中注入基础库到渲染层
        PerfTrace.online().begin("preInjectWebViewResource").report();
        getRuntime().getRendererManager().preloadWebViewBasePackage(MSCEnvHelper.getContext(),
                basePackage, new ResultCallback() {
                    @Override
                    public void onReceiveFailValue(Exception e) {
                        MSCLog.i(TAG, "preInjectWebViewResource", "preloadBasePackage step4 exit", source);
                        if (MSCHornRollbackConfig.readConfig().rollbackLoadErrorReportFix) {
                            getRuntime().getRuntimeReporter().reportMSCLoadError(MSCLoadErrorConstants.ERROR_INJECT_BASE_JS_WEBVIEW, e);
                        } else {
                            getRuntime().getRuntimeReporter().reportMSCLoadError(getRuntime().hasContainerAttached(),
                                    MSCLoadErrorConstants.ERROR_INJECT_BASE_JS_WEBVIEW, e);
                        }
                    }

                    @Override
                    public void onReceiveValue(String value) {
                        PerfTrace.online().instant("preInjectWebViewResource").report();
                        MSCLog.i(TAG, "preInjectWebViewResource", "preloadBasePackage step4 success", source);
                        WebViewFirstPreloadStateManager.getInstance().updateStateAfterPreload();
                    }
                });
        PerfTrace.online().end("preInjectWebViewResource").report();
    }

    @Override
    public void onLaunchParamsCheckFinished(Bundle savedInstanceState, long routeTime, @NonNull BizNavigationExtraParams bizNavigationExtraParams) {
      //  createRuntime(savedInstanceState);
        preInjectWebViewResourceIfNeed();
        // 保活场景下需要在attachPageManager后执行setupRuntimeAndStart，解决存在视图未初始化导致的Crash
        // 线上部分华为机型启动时偶现Crash，通过Horn配置禁用提前初始化运行时来验证问题原因
        if (enablePreStartPage()) {
            startInitialPage(savedInstanceState, routeTime, bizNavigationExtraParams);
        } else {
            MSCLog.i(TAG, "disable pre setup runtime:", mRuntime.getSource());
        }
    }

    private void preInjectWebViewResourceIfNeed() {
        if (WebViewFirstPreloadStateManager.getInstance().getPreloadState()
                == WebViewFirstPreloadStateManager.PreloadState.WEBVIEW_PRECREATE) {
            if (MSCHornPreloadConfig.isInBizPreloadListWhenWidgetSetUri(mAppId)
                    && MSCHornPreloadConfig.needInvokeAfterOnLaunchParamsCheckFinished()) {
                preInjectWebViewResource("onLaunchParamsCheckFinished");
            }
        }
    }

    public boolean enablePreStartPage() {
        if (isWidget()) {
            return false;
        }
        if (mRuntime.getSource() == RuntimeSource.KEEP_ALIVE
                || mRuntime.getSource() == RuntimeSource.BIZ_PRELOAD
                // todo tianbin 临时方案修复复用运行时启动NPE问题，后续调整任务依赖解决
                || mRuntime.isReuseRuntimeLaunch) {
            return false;
        }
        return MSCConfig.enablePreSetupRuntime();
    }

    public void onInitContainerController(@Nullable Bundle savedInstanceState, long routeTime) {
        mJsErrorRecorder = mRuntime.jsErrorRecorder;
        mAppService = mApp.getRuntime().getModule(AppService.class);
        mPageManager = getPageMangerModule();
        mApisManager = mRuntime.apisManager;
    }

    /**
     * 此时View已attach至Activity，方便进行后续处理
     */
    public void onActivityCreated(@Nullable Bundle savedInstanceState, long routeTime, @NonNull BizNavigationExtraParams bizNavigationExtraParams) { //11ms
        if (MSCTraceUtil.TRACE_LEVEL >= MSCTraceUtil.LEVEL_LIGHT) {
            PerfTrace.begin("ContainerController#onActivityCreated");
        }
        restoreState(savedInstanceState);

        initStatic(mActivity);

        extractOpenSourceFromIntent();

        mJsErrorRecorder = mRuntime.jsErrorRecorder;
        mContainerReporter.recordLaunchStartTimeCurrentTimeMillis(mRuntime, launchStartTimeCurrentTimeMillis);

        MemoryMonitor.startTrackingGCTime();
        ProcessMonitor.startCPUStatForCurrentProcess();

        super.onActivityCreated(savedInstanceState, routeTime, bizNavigationExtraParams);
        initView();

        mAppService = mApp.getRuntime().getModule(AppService.class);
        mPageManager = getPageMangerModule();
        mApisManager = mRuntime.apisManager;
        boolean isProdEnv = MSCEnvHelper.getEnvInfo().isProdEnv();
        if (!isProdEnv) {
            updateShareDebug();
        }
        // 初始化并同步小程序信息
        attachPageManager(mContainerView);

        if (!enablePreStartPage()) {
            startInitialPage(savedInstanceState, routeTime, bizNavigationExtraParams);
        }

        if (isActivity()) {
            if (!isRecreate && isColdStart) {
                mRuntime.setOriginSource(mRuntime.getSource());
                mRuntime.setSource(RuntimeSource.COLD_START);
            }
            MSCFusionActivityMonitor.onActivityCreate((MSCActivity) getActivity(), mAppId, getContainerId(), isReload());
        }

        recordAppStartMemoryStatus();
        if (MSCHornRollbackConfig.enableLocalAppStackPriority()) {
            mRuntime.recordAppStackWhenEnter();
        }

        // MSC启动时，触发已迁移的 MMP 业务资源清除
        triggerMMPBizResourceClean();
        if (MSCTraceUtil.TRACE_LEVEL >= MSCTraceUtil.LEVEL_LIGHT) {
            PerfTrace.end("ContainerController#onActivityCreated");
        }
    }

    private void recordAppStartMemoryStatus() {
        long libcMem_b = MSCResourceWatermarkUtil.getAppLibcMemByte();
        long javaMem_b = MSCResourceWatermarkUtil.getAppUsedJavaMemByte();
        mRuntime.setAppStartLibcMemory(libcMem_b);
        mRuntime.setAppStartJavaMemory(javaMem_b);
    }

    public FrameLayout getContainerView() {
        return mContainerView;
    }

    private void triggerMMPBizResourceClean() {
        if (!MSCEnvHelper.enableCleanMMPBizResource()) {
            return;
        }
        MSCExecutors.submit(new Runnable() {
            @Override
            public void run() {
                String mmpAppIdByMSC = MMPRouterManager.getMMPAppIdByMSC(mAppId);
                if (TextUtils.isEmpty(mmpAppIdByMSC)) {
                    return;
                }
                MSCLog.i(TAG, "triggerMMPBizResourceClean", mmpAppIdByMSC);
                List<ICleanMMPBizResource> cleanMMPBizResourceList = ServiceLoader.load(ICleanMMPBizResource.class, null);
                if (cleanMMPBizResourceList != null && cleanMMPBizResourceList.size() > 0) {
                    ICleanMMPBizResource cleanMMPBizResource = cleanMMPBizResourceList.get(0);
                    cleanMMPBizResource.clean(getActivity(), mmpAppIdByMSC);
                }
            }
        });
    }

    @Nullable
    public String getTopPagePath() {
        if (getPageMangerModule() == null || getPageMangerModule().getTopPage() == null) {
            return null;
        }
        return getPageMangerModule().getTopPage().getPagePath();
    }

    private boolean isColdStart() {
        return IntentUtil.getBooleanExtra(getIntent(), "_isDspColdStart", false);
    }

    @Nullable
    public Map<String, String> getTopPageBizTags() {
        if (mIPageManagerModule == null) {
            return null;
        }
        IPageModule topPage = mIPageManagerModule.getTopPage();
        if (topPage != null) {
            MSCLog.iSync(TAG, "getTopPageBizTags", topPage.getBizTagsForPage());
            return topPage.getBizTagsForPage();
        }
        return null;
    }

    public static class StartPageTaskOfLaunch extends StartPageTask {
        private final String TAG = "StartPageTaskOfLaunch@" + Integer.toHexString(hashCode());
        boolean mFromKeepLive;
        boolean mIsWidget;
        long timeStamp = SystemClock.elapsedRealtime();
        private final long routeTime;
        private int routeId;

        public StartPageTaskOfLaunch(ContainerDelegate containerDelegate, boolean fromKeepLive, boolean isWidget, long routeTime) {
            super(LaunchTaskManager.ITaskName.START_PAGE_OF_LAUNCH_TASK, containerDelegate);
            mFromKeepLive = fromKeepLive;
            mIsWidget = isWidget;
            this.routeTime = routeTime;
        }

        public boolean isWidget() {
            return mIsWidget;
        }

        public void setRouteId(int routeId) {
            this.routeId = routeId;
        }

        public boolean isAdded1SecondsAgo() {
            return SystemClock.elapsedRealtime() - timeStamp > 1000;
        }

        @Override
        public CompletableFuture<AppRouteParam> doExecuteTaskAsync(@NonNull IContainerDelegate controllerDelegate,
                                                                   ITaskExecuteContext executeContext) {
            super.doExecuteTaskAsync(controllerDelegate, executeContext);
            ContainerController controller = (ContainerController) controllerDelegate;
            MSCRuntime runtime = controller.getRuntime();
            UserReporter.create().reportUserFoundationLoadSuccess(controller);
            if (runtime.getMSCAppModule().getMetaInfo() == null) {
                return CompletableFuture.failedFuture(new AppLoadException(
                        MSCLoadErrorConstants.ERROR_FETCH_METAINFO_RESULT_EMPTY, "metaInfo is null"));
            }

            String minVersion = runtime.getMSCAppModule().getMetaInfo().getBasePackageMinVersion();
            String basePkgVersion = runtime.getMSCAppModule().getBasePkgVersion();
            if (!TextUtils.isEmpty(minVersion) && VersionUtil.needUpgradeHostApp(minVersion, basePkgVersion)) {
                PackageLoadReporter.create(runtime).reportBasePackageVersionError(minVersion, true, 1);
                return CompletableFuture.failedFuture(new AppLoadException(
                        MSCLoadErrorConstants.ERROR_CODE_NEED_UPGRADE_HOST_APP, "业务指定的最低版本号与MSC组件对应的基础库版本号不匹配"));
            }

            CompletableFuture<AppRouteParam> future = controller.startPage(mFromKeepLive, this.routeTime, routeId);
//            // fixme 这段预期逻辑是什么 此处移除会导致多次启动后 后续启动firstRenderFuture.complete 触发TaskNonexistentException 导致多次启动后白屏
//            ITask<String> pathConfigTask = executeContext.getTaskManager().findTaskByClassOrThrow(PathCfgTask.class, this);
//            executeContext.getTaskManager().removeTask(pathConfigTask);

            reportMinVersionCompareResult(controller);
            return future;
        }

        /**
         * 上报当前基础库版本与业务指定的基础库版本比对结果，比如minVersion为1.2.3，当前版本为1.2.2
         * PS：获取到元信息结果后，会比对AAR_Version是否满足minVersion（比对前两位），如不满足，会走失败流程提示用户升级
         *
         * @param controller controller
         */
        private void reportMinVersionCompareResult(ContainerController controller) {
            String minVersion = controller.getRuntime().getMSCAppModule().getBasePkgMinVersion();
            if (TextUtils.isEmpty(minVersion)) {
                return;
            }
            String basePkgVersion = controller.getRuntime().getMSCAppModule().getBasePkgVersion();
            int result = VersionUtil.compare(basePkgVersion, minVersion, 0);
            PackageLoadReporter.create(controller.getRuntime()).reportBasePackageVersionError(minVersion, false, result < 0 ? 1 : 0);
        }

        @Override
        public void onReset(ITaskResetContext resetContext) {
        }
    }

    public boolean startInitialPage(@Nullable Bundle savedInstanceState, long routeTime, @NonNull BizNavigationExtraParams bizNavigationExtraParams) {
        if (!isInitialPage) {
            return false;
        }
        isInitialPage = false;
        if (savedInstanceState != null) {
            pageStackWatchDog = PageStackWatchDog.newWatchDog(stackSavedFlag);
        }
        isReusingEngine = mAppLoader.isLaunched();
        if (skipMultiStartPageSameTime()) {
            return false;
        }

        mAppLoader.setLaunched(true);
        ensureLaunchPath();
        String targetPath = mRealLaunchPath;
        // 对齐MMP，页面重建时走reload流程启动
        if (!reloadPageByPageStackWatchDog(routeTime, bizNavigationExtraParams)) {
            //数据预拉取，routeId需和pageId建立关联
            StartPageTaskOfLaunch startPageTaskOfLaunch = new StartPageTaskOfLaunch(this, isReusingEngine,
                    isWidget(), routeTime);
            MSCLog.i("MSCDataPrefetchModule", "startPageTaskOfLaunch set routeId " + this.mRouteId);
            int tmpRouteId = this.mRouteId;
            if (tmpRouteId <= 0) {
                tmpRouteId = startPageTaskOfLaunch.hashCode();
                if (MSCHornRollbackConfig.enableAddLoadPackageDetails()) {
                    this.mRouteId = tmpRouteId;
                }
            }

            startPageTaskOfLaunch.setRouteId(tmpRouteId);

            MSCLog.i("MSCDataPrefetchModule", "startPageTaskOfLaunch routeId ", tmpRouteId, " isWidget ", isWidget());
            MSCApp app = getMiniApp();
            // 判断小程序是否冷启动的第一个页面
            RuntimeSource runtimeSource = getRuntime().getSource();
            isLaunchFirstPage = !isWidget() && !isReusingEngine && !mApp.isPageLaunched && (RuntimeSource.COLD_START.equals(runtimeSource) || RuntimeSource.NEW.equals(runtimeSource));
            mAppLoader.launchPage(targetPath, startPageTaskOfLaunch, false, true, tmpRouteId, routeTime, isWidget(), isIgnoreRouteMapping(true), isLaunchFirstPage, bizNavigationExtraParams);  // 触发AppLoader开始执行启动加载任务

            MSCAppLifecycleManager.getInstance();
            if (app != null && !app.isFirstLaunched() && !isWidget() && !MSCConfig
                    .isAppIdInLifecycleBlackList(getAppId())) {
                app.setFirstLaunched(true);
                setFirstContainer(true);
                dispatchLifecycleEvent(MSCAppLifecycle.MSC_WILL_ENTER_APP_LIFECYCLE);
            }
        }
        // 按名单预创建WebView，需要在launchPage之后执行，启动框架任务并行执行
        preCreateWebViewIfNeed(targetPath);
        return true;
    }

    private void dispatchLifecycleEvent(MSCAppLifecycle mscAppLifecycle) {
        if (mscAppLifecycle == MSCAppLifecycle.MSC_WILL_LEAVE_APP_LIFECYCLE) {
            MSCAppLifecycleIPCUtils.dispatchLifecycle(getMPTargetPath(), getUri(), leaveAppInfo, mscAppLifecycle, getAppId(), getActivity());
        } else {
            MSCAppLifecycleIPCUtils.dispatchLifecycle(getMPTargetPath(), getUri(), null, mscAppLifecycle, getAppId(), getActivity());
        }
    }

    private boolean skipMultiStartPageSameTime() {
        MSCLog.i(TAG, "skipMultiStartPageSameTime isReusingEngine：", isReusingEngine);

        /**
         * 复用引擎 但是没有launch过页面情况，不允许同时start多个Page页面
         */
        if (!isWidget() && mAppLoader.isLaunched()) {
            if (mAppLoader.isFirstPageInLaunchStatus()) {
                MSCLog.e(TAG, "start new page while firstPage is launching,finish current container" + this + getMPTargetPath());
                getActivity().finish();
                return true;
            }
        }
        return false;
    }

    private void startHomePage(long routeTime, int routeId) {
        if (!isWidget()) {
            mApp.isPageLaunched = true;
        }

        // 内有主线程耗时100ms左右的创建Page操作，最好在Service层不再有经过主线程的关键事件、开始异步运行主要前端代码后再执行
        if (checkLaunchRoutedPath()) {
            MSCLog.i(TAG, "launchHomePage");
            //fixme msc 为什么widget的打点不一致
            getPageManager().launchHomePage(mRealLaunchPath, routeTime, routeId);    // 启动第一个页面，在有缓存时，内有同步的Page层View创建，所以需要关注耗时
        } else {
            onPageNotFound(OpenParams.APP_LAUNCH, routeTime);
        }
    }

    /**
     * 小程序来源信息处理
     */
    private void extractOpenSourceFromIntent() {
        mSrcAppID = getStringExtra(MSCParams.SRC_APP_ID);
        if (!TextUtils.isEmpty(mSrcAppID)) {
            mSrcExtraData = getStringExtra(MSCParams.EXTRA_DATA);
            mScene = SceneNumber.OPEN_FROM_MINI_PROGRAM;
            setNavigateResult();
        } else {
            mScene = IntentUtil.getIntExtra(getIntent(), MSCParams.SCENE, SceneNumber.DEFAULT);
        }
    }

    /**
     * 从其他小程序启动时，需要将启动信息回传标识返回
     */
    private void setNavigateResult() {
        Intent intent = new Intent();
        intent.putExtra(MSCParams.SRC_APP_ID, getAppId());
        setResult(Activity.RESULT_OK, intent);
    }

    /**
     * 识别tabWidget
     */
    public boolean isTabWidget() {
        return IntentUtil.getBooleanExtra(getIntent(), MSCParams.IS_TAB_WIDGET, false);
    }

    /**
     * 在onCreate、onNewIntent时会调用，是统计上一次启动的开始，包含relaunch
     */
    protected void initStatus() {
        firstRender = false;
        hasReportedLoadEnd = false;
        isColdStart = isColdStart();
        if (MSCHornRollbackConfig.get().getConfig().isRollbackPendingPreloadBiz) {
            ContainerStartState.instance.setIsContainerLaunching(isColdStart);
        }
    }

    @Override
    public void initLaunchStartTime() {
        this.launchStartTimeCurrentTimeMillis = System.currentTimeMillis();
    }

    public long getLaunchStartTime() {
        return launchStartTimeCurrentTimeMillis;
    }

    public static void initStatic(Activity activity) {
        if (!sInited) {
            sInited = true;
            MSCExecutors.submit(new PreloadRunnable());

            MSCExecutors.postOnUiThread(() -> {
                // 必须在主线程操作，耗时很少，内有缓存，没有的话service层getSystemInfo将同步等待，需要提前执行
                // 如果在setContentView前执行会引起DecorView的一些操作，避开
                ConcaveScreenUtils.notchSupport(activity, true);
            });
        }
    }

    public @LayoutRes
    int getLayout() {
        return R.layout.msc_main_activity;
    }

    private void initView() {

        mContainerView = findViewById(R.id.container);
        mLoadingBg = findViewById(R.id.msc_loading_bg);
        if (!MSCHornRollbackConfig.readConfig().rollbackHalfDialog) {
            FrameLayout.LayoutParams layoutParams = (FrameLayout.LayoutParams) mLoadingBg.getLayoutParams();
            layoutParams.topMargin = getTopMarginAtTransparentContainer();
        }

        if (isWidget()) {
            View placeholder = ((MSCWidgetFragment) getMSCContainer()).getPlaceholder();
            if (placeholder != null) {
                showLoadingBg();
                mLoadingBg.setBackgroundColor(Color.TRANSPARENT);
                // 借用loadingBg作为占位图的容器，在firstRender时一同隐藏
                mLoadingBg.addView(UIUtil.removeFormParentIfNeed(placeholder));
                hideLoadingView();
            } else if (needLoadingView()) {
                showLoadingView();
                return;
            } else {
                hideLoadingView();
            }
            return;
        } else if (needLoadingView()) {
            if (isRecreate) {
                // 重建，加载可能很长，需要显示loading
                showLoadingBg();
                showLoadingView();
            } else {
                // 尝试在加载较快时略过loadingView的显示
                hideLoadingView();
                delayShowLoadingView();
            }
            trySetLoadingBgColor();
        }

        updateLoadingViewStatus();
    }

    private void trySetLoadingBgColor() {
        if (!isWidget() && !MSCHornRollbackConfig.isRollbackBackgroundColor()) {
            int loadingColor = getLoadingBgColor();
            mLoadingBg.setBackgroundColor(loadingColor);
            if (mActivity != null && mActivity.getWindow() != null
                    && mActivity.getWindow().getDecorView() != null) {
                if (mActivity instanceof MSCActivity) {
                    mActivity.getWindow().getDecorView().setBackgroundColor(loadingColor);
                }
            }
        }
    }

    private int getLoadingBgColor() {
        String dataString = getIntent().getDataString();
        String loadingColor = PathUtil.getQueryParameter(dataString, "loadingColor");
        if (TextUtils.isEmpty(loadingColor)) {
            String pagePath = getMPTargetPath();
            loadingColor = PathUtil.getQueryParameter(pagePath, "loadingColor");
        }
        return ColorUtil.parseColor(loadingColor, Color.WHITE);
    }

    protected <T extends View> T findViewById(int id) {
        return mContainer.findViewById(id);
    }

    private void updateShareDebug() {
        String shareEnv = IntentUtil.getStringExtra(getIntent(), MSCParams.SHARE_ENV);
        if (!TextUtils.isEmpty(shareEnv)) {
            mRuntime.getMSCAppModule().setShareEnvironment(shareEnv);
        }
    }

    public void updateAppProp() {
        mContainer.updateAppProp();
    }

    public void defaultUpdateAppProp() {
        // 缓存业务小程序版本号
        mmpAppVersion = mRuntime.getMSCAppModule().getMSCAppVersion();
        MSCExecutors.runOnUiThread((Runnable) () -> {
            updateLoadingViewStatus();
        });
    }

    /**
     * 设置loading图中的图标和应用名
     */
    private void updateLoadingViewStatus() {
        if (!needLoadingView() || mLoadingView == null || !needLoadingAppInfo()) {
            return;
        }
        AppMetaInfoWrapper appMetaInfoWrapper = getMetaInfoWrapper();
        // Icon降级策略：loadingIconURL>AppIcon>链接配置中AppIcon>不显示
        // Title降级策略：loadingTitle>AppName>链接配置中AppName>默认值
        String loadingIconURL = getLoadingIconURL(appMetaInfoWrapper);
        String loadingTitle = getLoadingTitle(appMetaInfoWrapper);
        if (mFlowerLoadingIndicator != null) {
            mTitle.setText("加载中");
            mTitle.setVisibility(View.VISIBLE);
            mIcon.setVisibility(View.GONE);
            mIconContainer.setBackground(null);
            mIconContainer.setPadding(0, 0, 0, 0);
            mIconContainer.setVisibility(View.VISIBLE);
            if (mFlowerLoadingIndicator.getParent() == null) {
                mIconContainer.addView(mFlowerLoadingIndicator);
            }
        } else {
            if (!enableLoadingWithTitle() || TextUtils.isEmpty(loadingTitle)) {
                loadingTitle = "加载中";
            }
            mTitle.setText(loadingTitle);
            mIconContainer.setVisibility(View.VISIBLE);
            mTitle.setVisibility(View.VISIBLE);
            if (!TextUtils.isEmpty(loadingIconURL)) {
                RequestCreator requestCreator = FileUtil.getPicassoRequestCreator(MSCEnvHelper.getContext(), loadingIconURL, mRuntime.getFileModule());
                if (requestCreator != null) {
                    requestCreator.transform(new RoundedCornersTransformation(MSCEnvHelper.getContext(), ScreenUtil.dp2px(12), 0))
                            .into(mIcon);
                }
            }
        }
    }

    private String getLoadingIconURL(AppMetaInfoWrapper appMetaInfoWrapper) {
        String loadingIconURL;
        if (appMetaInfoWrapper != null) {
            if (!TextUtils.isEmpty(appMetaInfoWrapper.getLoadingIconURL())) {
                loadingIconURL = appMetaInfoWrapper.getLoadingIconURL();
            } else if (!TextUtils.isEmpty(appMetaInfoWrapper.getIconPath())) {
                loadingIconURL = appMetaInfoWrapper.getIconPath();
            } else {
                loadingIconURL = getStringExtra(MSCParams.APP_ICON);
            }
        } else {
            loadingIconURL = getStringExtra(MSCParams.APP_ICON);
        }
        return loadingIconURL;
    }

    private String getLoadingTitle(AppMetaInfoWrapper appMetaInfoWrapper) {
        String loadingTitle;
        if (appMetaInfoWrapper != null) {
            if (!TextUtils.isEmpty(appMetaInfoWrapper.getLoadingTitle())) {
                loadingTitle = appMetaInfoWrapper.getLoadingTitle();
            } else if (!TextUtils.isEmpty(appMetaInfoWrapper.getAppName())) {
                loadingTitle = appMetaInfoWrapper.getAppName();
            } else {
                loadingTitle = getStringExtra(MSCParams.APP_NAME);
            }
        } else {
            loadingTitle = getStringExtra(MSCParams.APP_NAME);
        }
        return loadingTitle;
    }

    /**
     * 元信息获取策略，先从Runtime里缓存中获取，如果获取不到再从DDD缓存中尝试获取。
     *
     * @return 返回元信息
     */
    private AppMetaInfoWrapper getMetaInfoWrapper() {
        MSCAppModule mscAppModule = mRuntime.getMSCAppModule();
        if (mscAppModule.hasMetaInfo()) {
            return mscAppModule.getMetaInfo();
        }
        MSCAppMetaInfo appMetaInfo = MSCMetaInfoCache.getInstance().getAppMetaInfo(getAppId());
        return appMetaInfo != null ? new AppMetaInfoWrapper(appMetaInfo) : null;
    }

    protected boolean enableLoadingWithTitle() {
        return true;
    }

    /**
     * 因包下载失败、WebView/JS引擎初始化失败导致的启动失败
     */
    public void onLaunchError(String msg, int code, Throwable e) {
        MSCLog.e(TAG, e, "onLaunchError", msg, code);
        String failMsg = String.format("ErrorCode:%s", code);
        String path = getTopPagePath();
        if (TextUtils.isEmpty(path)) {
            path = getMPTargetPath();
        }
        MPListenerManager.getInstance().onPageLoadFailed(getActivity(), getAppId(), path, isWidget(), failMsg, null);

        MSCExecutors.runOnUiThread((Runnable) () -> {
            MSCLog.i("onLaunchError", "hideLoading");
            hideLoadingView();
            hideLoadingBg();
            dismissRouteLoading();

            if (mContainer.onLaunchError(msg, code, e)) {
                return;
            }

            if (firstRender) {
                // 非首页启动失败，提示重试Toast
                ToastUtils.toast("加载模块失败, 请重试:" + code);
            } else {
                // 首页启动失败，显示启动失败页面
                showLaunchFailView(msg, code, e);

                //容器覆盖率。首次启动失败，用户可感知到页面或卡片展示，上报页面/卡片pv
                cipDynamicContentReport(getMPTargetPath());
            }
        });
    }

    public void downgrade(final String msg, int code, final Throwable error) {
        if (mContainer.isActivity()) {
            ((MSCActivity) mContainer).downgrade(msg, code, error);
        } else {
            defaultDowngrade(msg, code, error);
        }
    }

    /**
     * 降级，尝试打开指定的url
     */
    public void defaultDowngrade(String msg, int code, final Throwable error) {
        MSCLog.e(ReporterFields.REPORT_LAUNCH_POINT_FAILED + " " + msg + " " + error);
        if (error != null) {
            MSCLog.e(TAG, error);
        }
        if (MiniProgramUtil.isDowngrade(getIntent(), getActivity())) {
            return;
        }
        onLaunchError(msg, code, error);
    }

    /**
     * 关联PageManager至容器
     */
    private void attachPageManager(FrameLayout container) {
        final ViewGroup pageContainer = getPageMangerModule().asView();
        MSCLog.i("attachPageManager", this, container, mAppId, pageContainer, pageContainer.getParent() == container);
        UIUtil.removeFormParentIfNeed(pageContainer);
        container.addView(pageContainer, new FrameLayout.LayoutParams(
                FrameLayout.LayoutParams.MATCH_PARENT, FrameLayout.LayoutParams.MATCH_PARENT));
        getMSCContainer().startPageContainerEnterAnimation(pageContainer);
    }

    public void startActivity(Intent intent) {
        mContainer.startActivityForResult(intent, -1, null);
    }

    public void startActivityForResult(Intent intent, int requestCode) {
        mContainer.startActivityForResult(intent, requestCode, null);
    }

    @Override
    public void startActivityForResult(Intent intent, int requestCode, Bundle options, NavActivityInfo navActivityInfo) {
        if (isOpenLink(navActivityInfo)) {
            setBackFromExternalNativeUrl(intent.getDataString());
        }
        if (MSCHornRollbackConfig.enableStartActivityForResultWithOptions()) {
            mContainer.startActivityForResult(intent, requestCode, options);
        } else {
            mContainer.startActivityForResult(intent, requestCode, null);
        }
    }

    private boolean isOpenLink(NavActivityInfo navActivityInfo) {
        return navActivityInfo != null && TextUtils.equals("openLink", navActivityInfo.fromApiName);
    }

    @Override
    public void setResult(int resultCode, Intent data) {
        if (getActivity() != null) {
            getActivity().setResult(resultCode, data);
        }
    }

    public static Intent defaultGetStartContainerActivityIntent(@NonNull String appId, @Nullable Bundle extras) {
        Intent intent = new Intent();
        intent.setComponent(new ComponentName(MSCEnvHelper.getContext(), MSCActivity.class));
        if (extras != null) {
            intent.putExtras(extras);
        }
        intent.putExtra(MSCParams.APP_ID, appId);
        return intent;
    }

    boolean isResumingForOnNewIntent = false;

    public void skipReportResumePagePush() {
        isResumingForOnNewIntent = true;
    }

    // 查看是否是从首页启动
    private boolean isHomeLaunched(Intent intent) {
        if (null == intent) {
            return false;
        }
        final String action = intent.getAction();
        final Set<String> category = intent.getCategories();
        return Intent.ACTION_MAIN.equals(action) && category != null && category.contains(Intent.CATEGORY_LAUNCHER);
    }

    /**
     * @return 是否接受新Intent
     */
    protected boolean handleOnNewIntent(Intent intent, long routeTime) {
        if (!isResumed()) {
            isResumingForOnNewIntent = true;
        }

        if (isHomeLaunched(intent)) {
            MSCLog.i(TAG, "onNewIntent ignore because launched by home");
            return false;
        }
        if (mRuntime == null) {
            MSCLog.w(TAG, getContainerId(), "onNewIntent ignore because mRuntime is null", mRuntime);
            return false;
        }
        //fixme 这些逻辑需要在有元信息之后执行！！ 临时限制需要有启动配置后才接受新的intent 否则直接忽略掉 下个版本修复一下
//        java.lang.RuntimeException: getRootPath when config null
//        at com.meituan.msc.modules.update.AppConfigModule.getRootPath(AppConfigModule.java:382)
//        at com.meituan.msc.modules.update.MSCAppModule.getRootPath(MSCAppModule.java:224)
//        at com.meituan.msc.modules.container.ContainerController.handleOnNewIntent(ContainerController.java:757)
//        at com.meituan.msc.modules.container.MSCActivity.onNewIntent(MSCActivity.java:355)
//        at android.app.Activity.performNewIntent(Activity.java:8259)
        String targetPath = IntentUtil.getStringExtra(intent, MSCParams.TARGET_PATH);
        boolean disablePrefetch = IntentUtil.getBooleanExtra(intent, MSCParams.DISABLE_PREFETCH, false);
        BizNavigationExtraParams bizNavigationExtraParams = new BizNavigationExtraParams.Builder()
                .setDisablePrefetch(disablePrefetch)
                .build();
        if (!mRuntime.getAppConfigModule().hasConfig()) {
            MSCLog.w(TAG, "onNewIntent ignore because mRuntime config is null", targetPath);
            mRuntime.getRuntimeReporter().record(ReporterFields.REPORT_MSC_LAUNCH_NEW_INTENT_ERROR).sendDelay();
            return false;
        }
        if (!mRuntime.getMSCAppModule().checkHasPageIfRouted(targetPath)) {
            targetPath = mRuntime.getMSCAppModule().getRootPath();
        }
        try {
            boolean isPipStarted = IntentUtil.getBooleanExtra(intent, IS_LIVE_PIP_STARTED, false);
            // 内部路由 relaunch -> startActivity(singleTop) -> onNewIntent ，无需走后续分叉，直接走relaunch逻辑
            if (IntentUtil.getBooleanExtra(intent, MSCActivity.RELAUNCH, false)) {
                MSCLog.i(TAG, "onNewIntent relaunch by intent extra");
                onNewIntentRelaunch(intent, routeTime);
                return true;
            }
            // 内部路由 switchTab -> startActivity(singleTop) -> onNewIntent ，无需走后续分叉，直接走switchTab逻辑
            if (!MSCHornRollbackConfig.isRollbackSetRouteMappingFix() && IntentUtil.getBooleanExtra(intent, MSCActivity.SWITCH_TAB, false)) {
                MSCLog.i(TAG, "onNewIntent switchTabPage for fusion mode");
                isSwitchTabOnNewIntent = true;
                getPageManager().switchTabPage(targetPath, routeTime, bizNavigationExtraParams);
                return true;
            }
            // 判断外部路由场景下，当前和映射后的页面路径是否为tab（由于路由映射有内外部路由限制字段，内部路由已前置处理，此处只有可能是外部路由）
            if (mRuntime.getMSCAppModule().checkIsTabPageIfRouted(targetPath)) {
                // api调用启动 直接不需要传递参数 走switchTab就可以
                if (isPipStarted) {
                    MSCLog.i(TAG, "onNewIntent switchTabAction for pip");
                    isSwitchTabOnNewIntent = true;
                    getPageManager().switchTabAction(targetPath, routeTime, bizNavigationExtraParams);
                } else if (IntentUtil.getBooleanExtra(intent, IS_FUSION_API_STARTED, false)) {
                    // 内部api跳转，可switchTab
                    MSCLog.i(TAG, "onNewIntent switchTabPage for fusion mode");
                    isSwitchTabOnNewIntent = true;
                    getPageManager().switchTabPage(targetPath, routeTime, bizNavigationExtraParams);
                } else {
                    // 外部跳入走relaunch
                    onNewIntentRelaunch(intent, routeTime);
                }
            } else {
                // 非tab页
                if (isPipStarted) {
                    MSCLog.i(TAG, "onNewIntent navigateBackToPipPage");
                    getPageManager().navigateBackToPipPage(targetPath, routeTime, bizNavigationExtraParams);
                } else {
                    MSCLog.i(TAG, "onNewIntent navigateToPage");
                    getPageManager().navigateToPage(targetPath, null, routeTime, bizNavigationExtraParams);
                }
            }
            return true;
        } catch (ApiException e) {
            // 异常的可能原因是传入了非法url，尝试保持原页面
            MSCLog.e(TAG, e, "reLaunch failed");
            ToastUtils.toast("页面跳转异常");
        }
        return false;
    }

    /**
     * 启动处理relaunch逻辑
     */
    protected void onNewIntentRelaunch(Intent intent, long routeTime) {
        if (mContainer instanceof Activity) {
            ((Activity) mContainer).setIntent(intent);
        }
        initStatus();
        initLaunchStartTime();

        MSCLog.i("onNewIntent relaunch, appId = " + getStringExtra(MSCParams.APP_ID) + ", targetPath = " + getStringExtra(MSCParams.TARGET_PATH));

        boolean pathAvailable = checkLaunchRoutedPath();

        if (IntentUtil.getBooleanExtra(intent, ContainerController.START_FROM_MIN_PROGRAM, false)) {
            extractOpenSourceFromIntent();
        } else {
            mScene = SceneNumber.DEFAULT;
        }
        boolean disablePrefetch = IntentUtil.getBooleanExtra(intent, MSCParams.DISABLE_PREFETCH, false);
        BizNavigationExtraParams bizNavigationExtraParams = new BizNavigationExtraParams.Builder()
                .setDisablePrefetch(disablePrefetch)
                .build();
        // 在前台以onNewIntent调用自身时，此时不会再走onResume -> onAppEnterForeground，改为主动触发
        if (mAppLoader.isFrameworkReady()) {
            isRelaunching = true;
            if (mApp.isAppInForeground()) {
                onAppEnterForeground();
            }
        }
        if (!MSCHornRollbackConfig.isRollbackSetRouteMappingPersist()) {
            checkClearRouteMappingPersist();
        }
        if (pathAvailable) {
            getPageManager().reLaunchPage(mRealLaunchPath, routeTime, true, bizNavigationExtraParams);
            ToastUtils.toastIfDebug("relaunch existing HeraActivity");
        } else {
            onPageNotFound(OpenParams.RE_LAUNCH, routeTime);
        }
    }

    public void onSaveInstanceState(Bundle outState) {
        String marker = selectStackSavedFlag();
        outState.putString(MSC_PAGE_STACK_MARK, marker);

        PageStackGlobalCache.getGlobalInstance().cachePageStackForGlobalInstance(pageStackWatchDog,
                getAppId(), getPageManager(), marker);
        outState.putString(BACK_FROM_EXTERNAL_NATIVE_URL, mBackFromExternalNativeUrl);
        super.onSaveInstanceState(outState);
    }

    @Override
    public void preCreateRuntime(Bundle savedInstanceState) {
        createRuntime(savedInstanceState);
    }

    private String selectStackSavedFlag() {
        if (stackSavedFlag != null) return stackSavedFlag;
        return getIntent().getDataString() + "@" + hashCode();
    }

    private boolean isFirstResume = true;

    /**
     * 先放在这 之后这块儿重构一下吧
     */
    public void clearRestorePageStack() {
        if (pageStackWatchDog != null) {
            pageStackWatchDog.popAllStack();
        }
    }

    public void onResume() {
        super.onResume();
//        MemoryMonitor.onActivityResume(this);
        if (mAppLoader.isFrameworkReady()) {
            mApisManager.onResume();
        }
        onContainerShow();
        if (!MSCHornRollbackConfig.isRollbackLifecycle(getAppId())) {
            MSCPageStateInstance.getInstance().resetPageState();
            if (!MSCHornRollbackConfig.enableFixOnResumeFocus(getAppId())) {
                // 解决目标页面为透明背景时，生命周期方法执行时序异常导致appId缓存出错问题，A->B, B.onResume -> A.onResume -> B.onPause
                if (!getActivity().hasWindowFocus()) {
                    MSCLog.i(TAG, "Activity resume but not focus");
                    return;
                }
            }
            addCurrentPageAppIdInNextAppIds();
        }
    }

    private void addCurrentPageAppIdInNextAppIds() {
        if (mRuntime == null) {
            return;
        }
        String appId = mRuntime.getAppId();
        if (appId == null) {
            return;
        }
        MSCPageStateInstance.getInstance().getNextAppIdSet().add(appId);
    }

    protected void onContainerShow() {
//        // 修复mAppEngine为空异常，11.11.200新增，11.12.400版本全量后开始明显上涨，Crash链接见Commit记录
//        // 暂未定位到异常发生场景，先做下防护
//        if (mAppLoader == null) {
//            MSCLog.e(TAG, "onResume mAppLoader is null");
//            if (!isWidget()) {
//                getActivity().finish();
//            }
//            return;
//        }

        PageStackGlobalCache.getGlobalInstance().cleanStackForGlobal(selectStackSavedFlag());

        MSCRunningManager.resumeApp(mAppId);
        // windowToken maybe null here
        MPListenerManager.getInstance().pageListener.onMPResumed(mAppId, ActivityUtils.getWindowToken(getActivity()));
        onAppEnterForeground();

        if (activityResultRunnable != null) {
            activityResultRunnable.run();
            activityResultRunnable = null;
        }
//        else if (mRuntime != null && mRuntime.getModule(IStartActivityModule.class) != null) {
//            mRuntime.getModule(IStartActivityModule.class).clearStartActivityCall(this);
//        }
        if (!isResumingForOnNewIntent) {
            // 如onNewIntent至HeraActivity，说明是桌面图标点击
            // onNewIntent至AppBrandHeraActivity，则已有relaunch处理，push过页面，均不需要push页面
            if (mPageManager.getTopPage() != null) {
                CrashReporterHelper.pushPage(mPageManager.getTopPage().getPagePath(), mAppId, "onResumed", isWidget());
            }
        } else {
            isResumingForOnNewIntent = false;
        }

        if (isFirstResume) {
            isFirstResume = false;
            MPListenerManager.getInstance().launchEventListener.onEvent("native_init_end");
        }

        // 低端机存在onResume之后执行无用绘制的问题，提前创建Page，优化启动耗时
        preCreatePage();
    }

    private void preCreatePage() {
        if (!MSCHornPreloadConfig.enablePreCreatePageForLaunch()) {
            return;
        }
        if (MSCHornPreloadConfig.disablePreCreatePageForLaunchByAppId(mAppId)) {
            MSCLog.i(TAG, "disablePreCreatePageForLaunchByAppId", mAppId);
            return;
        }

        if (!MSCHornRollbackConfig.isRollbackStartPageAdvanced(getAppId()) &&
                (mRuntime.getSource() == RuntimeSource.KEEP_ALIVE || mRuntime.getSource() == RuntimeSource.BIZ_PRELOAD)) {
            // 保活场景、业务预热场景下流程较短，优先占据主线程更早发出appRoute，不提前创建Page对象
            return;
        }
        if (mRuntime.getRendererManager().isPackageReady() && mRuntime.getAppConfigModule().hasConfig()
                && isJSECreated() && !disablePreCreatePage) {
            checkLaunchRoutedPath();
            try {
                OpenParams openParams = new OpenParams.Builder()
                        .setUrl(mRealLaunchPath)
                        .setIsPreloadPage(true)
                        .build(mRuntime);
                getPageManager().preloadPage(openParams, null);
            } catch (ApiException e) {
                // 预创建，不影响正常启动，不主动抛异常
                MSCLog.e(TAG, e, "preCreatePage");
            }
        }
    }

    private boolean isJSECreated() {
        return mRuntime.getModule(IAppLoader.class).isJSECreated();
    }

    //todo record popped page temp & push back after onResume
    public void onPause() {
        super.onPause();
        long routeTime = System.currentTimeMillis();
//        MemoryMonitor.onActivityPause(this);


        if (mContainer.isActivity() && getActivity().isFinishing()) {
            // navigateBack需要早于onAppEnterBackground，以避免页面onUnload前多余的onHide
            // 但要注意finish可能发生于pause之后，此处不一定能处理到所有需要navigateBack的场景，所以handleOnDestroy内另有处理
            sendNavigateBackWhenActivityClosed(routeTime);
        }
        // Widget在短视频场景会遇到在setUserVisibleHint(true)前先执行setUserVisibleHint(false)，导致mApisManager对象未赋值，此处做兼容兜底。
        if (mApisManager != null && mAppLoader.isFrameworkReady()) {
            mApisManager.onPause();
        }
        if (MSCHornRollbackConfig.isRollbackLifecycle(getAppId())) {
            onAppEnterBackground(false);
        } else {
            MSCPageStateInstance mscPageStateInstance = MSCPageStateInstance.getInstance();
            if (!mscPageStateInstance.isStartNewActivity()) {
                mscPageStateInstance.getNextAppIdSet().clear();
            } else if (mscPageStateInstance.isNextPageSameMSC(getAppId())) {
                onAppEnterBackground(true);
            }
        }

        if (mPageManager != null) {
            mPageManager.onPause(
                    isPopped ? IPageLifecycleInterceptor.TYPE_PAGE_PAUSE_CAUSE_ENTER_BACKGROUND_POPPED
                            : IPageLifecycleInterceptor.TYPE_PAGE_PAUSE_CAUSE_ENTER_BACKGROUND_OTHERS);
            isPopped = false;
        }
//        BroadcastUtils.unregisterReceiverSafely(getActivity(), systemReceiver);
        if (mPageManager != null && mPageManager.getTopPage() != null) {
            CrashReporterHelper.popPage();
        }
        MPListenerManager.getInstance().pageListener.onMPPaused(mAppId, ActivityUtils.getWindowToken(getActivity()));

        // FIXME 优选独立app带有此逻辑时libwebviewchromium.so崩溃率较高，原因尚不明确，先去掉
        if (!PackageNameConstants.YOU_XUAN.equalsIgnoreCase(getActivity().getPackageName())) {
            if (getActivity().isFinishing()) {
                // 如果正在退出，提前执行onDestroy相关逻辑，以避免融合模式下activity的finish加start操作时，
                // 前后Activity有一小段时间共存的情况，此情况下engine、page方面的时序均有问题
                handleOnDestroy();
            }
        }

    }


    //    @Override
    protected void onContainerDestroy() {

    }

    /**
     * 在Widget中不会被调用
     */
    public boolean handleBackPress(long routeTime) {
        MSCLog.i(TAG, "onBackPressed");
        if (mErrorView != null && mErrorView.getVisibility() == View.VISIBLE) {
            MSCLog.i(TAG, "加载异常，onBackPressed 系统默认实现");
            return false;
        }

        IPageModule topPage = getPageMangerModule().getTopPage();
        if (DisplayUtil.isRNRenderer(topPage)) {
            //RN 的页面退出拦截走这里,发送事件到Fe（RN iOS没有页面退出拦截）
            DeviceEventManagerInterface deviceEventManagerModule = mRuntime.getModule(DeviceEventManagerInterface.class);
            if (deviceEventManagerModule != null && firstRender) {
                deviceEventManagerModule.emitHardwareBackPressed();
                return true;
            }
        }
        //Native渲染下,如果有组件(如page-container弹窗)需要高优拦截,则允许拦截
        if (MSCHornRollbackConfig.enablePageContainerLifecycleIntercept() && DisplayUtil.isNativeRenderer(topPage) && topPage.getRenderer() != null && topPage.getRenderer().getPageLifecycleCallback() != null) {
            if (topPage.getRenderer().getPageLifecycleCallback().onBackPressed(topPage.getId(), new LifecycleData())) {
                return true;
            }
        }

        View lastTopPageView = topPage != null ? topPage.asView() : null;
        if (!pageHandleBackPress(routeTime) && !mContainer.handleBackPress()) {
            MSCLog.i(TAG, "onBackPressed 系统默认实现");
            return false;
        }

        //back事件，MSC内部页面栈发生变化
        IPageModule curTopPage = getPageMangerModule().getTopPage();
        if (topPage != null && topPage != curTopPage) {
            MSCLog.i(TAG, "onBackPressed MSC page back");
            MPListenerManager.getInstance().pageOnBackPressedListener.onBackPressed(mRuntime.getMSCAppModule().getAppId(), topPage.getPagePath(), lastTopPageView != null ? lastTopPageView.getWindowToken() : null);
        }

        return true;
    }

    protected boolean pageHandleBackPress(long routeTime) {
        // 拦截事件逻辑 如果拦截则已处理 如未拦截 交于下方继续处理
        if (interceptBackActionLogic(BackOperator.BACK)) {
            MSCLog.i(TAG, "onBackPressed intercepted");
            return true;
        }

        // Page处理返回事件
        //1.Page内部native处理 2.Page内部由js处理 3.页面堆栈返回
        // fixme msc pageStackWatchDog 相关需要重构
        if (mPageManager != null && getPageManager().handleBackPress(pageStackWatchDog, routeTime)) {
            MSCLog.i(TAG, "onBackPressed handled by page back");
            return true;
        }

        // Page非空但未处理时
        // 只有一个页面的时候不要popPage 会在onPause的时候pop
        if (mPageManager != null && mPageManager.getPageCount() > 1) {
            CrashReporterHelper.popPage();
        }
        return false;
    }

    public enum BackOperator {
        BACK, CLOSE
    }

    // 拦截退出事件逻辑
    // 返回true 代表要与FE一同处理
    // 返回false 不处理
    protected boolean interceptBackActionLogic(BackOperator opt) {
        // 如果相应组件为空，则告知用户未处理
        if (this.mPageManager == null) {
            return false;
        }
//        // 是否为内部、外部小程序 返回拦截不对外部小程序开放
//        if (!this.mRuntime.getMSCAppModule().isInnerApp()) {
//            return false;
//        }

        IPageModule page = mPageManager.getTopPage();
        if (page == null) {
            return false;
        }

        // 当前页面是否配置了退出拦截
        boolean isIntercept = page.isEnableBackActionIntercept();
        // 必须在fp事件之后 防止用户退不出来
        // 新增拦截事件逻辑
        if (hasFirstRender() && isIntercept) {
            MSCLog.i(TAG, "interceptBackActionLogic", page.getPagePath());
            PageBeforeUnloadParam.sendOnPageBeforeUnload(page, opt, mPageManager, mRuntime);
            return true;
        }
        return false;
    }


    /**
     * 容器为Activity且是融合模式，最后一个页面返回的情况下
     * 向前端发送NavigateBack用于移除栈底元素
     */
    void sendNavigateBackWhenActivityClosed(long routeTime) {
        if (isNavigateBackOnActivityCloseHandled) {
            return;
        }
        isNavigateBackOnActivityCloseHandled = true;

        // 在退到同一小程序的activity时，如果发送navigateBack的时间晚于回退目标onAppEnterForeground会有问题，
        // 且回退一定是switchTab或relaunch此类清空页面栈的操作，并不需要navigateBack，因此省略事件
        boolean shouldSkipBack = MSCFusionActivityMonitor.isFinishActivityForSameMP(getContainerId());
        MSCFusionActivityMonitor.markFinishDone(getContainerId());
        if (shouldSkipBack) {
            return;
        }

        if (mPageManager != null) {
            IPageModule bottomPage = mPageManager.getBottomPage();
            if (bottomPage != null) {
                OpenParams openParams;
                try {
                    openParams = new OpenParams.Builder()
                            .setUrl(bottomPage.getPagePath())
                            .setOpenType(isWidget() ? OpenParams.WIDGET_DESTROY : OpenParams.NAVIGATE_BACK_UTIL)
                            .setRouteTime(routeTime)
                            .build(mRuntime);
                    onAppRoute(openParams, bottomPage.getId(), View.NO_ID, null);
                } catch (ApiException e) {
                    MSCLog.e(TAG, e, "sendNavigateBackWhenActivityClosed");
                    ToastUtils.toast("页面跳转异常");
                }
                isCurrentContainerMarkedClosed = true;
                if (mJsErrorRecorder != null) {
                    mJsErrorRecorder.popPage(bottomPage.getPagePath(), String.valueOf(bottomPage.getId()));
                }
            }
        }
    }

    // FIXME: 2022/12/14 临时加上 解决路由时序导致的jsError; 已经发送了栈底移除事件就不要再发送其他事件了；
    // 本质原因是sendNavigateBackWhenActivityClosed是同步发送，但是内部navigateBack路由是异步发送；导致了时序混乱；
    boolean isCurrentContainerMarkedClosed;

    /**
     * 继承版本 {@link MSCActivity#handleCloseApp()} 内有用于将Task切至后台的不同行为，不一定是finish
     * 外部在不一定要finish而允许Task保活的情况下，应调此方法
     */
    public void handleCloseContainer(String triggerSource) {
        MSCLifecycleCallback.getInstance().onBackPressedByUser(mAppId, getIntent());
        MSCLog.i(TAG, "handleCloseApp");
        handleCloseContainerTriggerSource = triggerSource;
        if (mContainer.isActivity()) {
            ((MSCActivity) mContainer).handleCloseApp();
        } else {
            MSCLog.w(TAG, "cannot close app in widget");
        }
    }

    private boolean isPopped = false;

    protected void recordPagePopped() {
        isPopped = true;
    }

    void onActivityResult(final int requestCode, final int resultCode, final Intent data) {
        MSCLog.v(TAG, "onActivityResult: ", requestCode, resultCode);
        if (requestCode == Constants.REQUEST_CODE_MINI_PROGRAM
                && resultCode == Activity.RESULT_OK) {
            mSrcAppID = data.getStringExtra(MSCParams.SRC_APP_ID);
            if (!TextUtils.isEmpty(mSrcAppID)) {
                mSrcExtraData = data.getStringExtra(MSCParams.EXTRA_DATA);
                mScene = SceneNumber.BACK_FROM_MINI_PROGRAM;
            }
        } else if (mBackFromExternalNativeUrl != null && resultCode == Activity.RESULT_OK) {
            if (data != null) {
                Bundle bundle = data.getExtras();
                if (bundle != null) {
                    mSrcExtraData = JsonUtil.parseToJson(bundle).toString();
                }
            }
        } else if (requestCode == Constants.REQUEST_CODE_SCAN && null != data) {
            attachIDE(data);
        }
        //https://ones.sankuai.com/ones/product/6246/workItem/defect/detail/3993864
        //onResume早于onActivityResult，app进入前台的时间要比onActivityResult晚
        activityResultRunnable = () -> {
//                mApisManager.onActivityResult(requestCode, resultCode, data);
            if (mRuntime != null && mRuntime.getModule(IStartActivityModule.class) != null) {
                mRuntime.getModule(IStartActivityModule.class).onActivityResult(this, requestCode, resultCode, data);
            }
        };
    }

    /**
     * attach 到开发者工具
     *
     * @param data
     */
    private void attachIDE(Intent data) {
        String result = data.getStringExtra("result_url");
        boolean isProdEnv = MSCEnvHelper.getEnvInfo().isProdEnv();
        //线下环境小程序才支持打开开发者工具
        if (result == null || isProdEnv) {
            return;
        }
        Uri uriResult = Uri.parse(result);
        if (uriResult == null) {
            return;
        }
        String str = uriResult.getQueryParameter(MSCParams.DEBUG_PROXY);
//        mRuntime.enableDebug(str, getActivity(), IntentUtil.getBooleanExtra(getIntent(), "killWhenSuspend", false));
        mRuntime.enableDebug(getActivity(), getIntent());
//        mDevToolsDelegate.setUp();
    }

    /**
     * 宿主使用小程序申请权限
     */
    public void requestPermissionsOrEnqueue(final @NonNull String[] permissions, String token, MsiPermissionGuard.ICallback callback) {
        if (mActivity == null || mActivity.isFinishing() || mActivity.isDestroyed()) {
            callback.onResult(token, permissions, null, "activity is null");
            return;
        }
        mApisManager.requestPermissions(permissions, token, callback);
    }

    public void onRequestPermissionsResult(int requestCode, @NonNull String[] permissions, @NonNull int[] grantResults) {
        mApisManager.onRequestPermissionsResult(requestCode, permissions, grantResults);
    }

    public void onDestroy() {
        handleOnDestroy();
        super.onDestroy();
        PerfTrace.trimOnlineTrace();
    }

    @MainThread
    protected void handleOnDestroy() {
        if (isDestroyHandled) {
            return;
        }
        long routeTime = System.currentTimeMillis();
        isDestroyHandled = true;
//        MemoryMonitor.onActivityDestroy(this);
        MSCApp app = mRuntime.getApp();
        if (app != null && app.firstLaunched && isFirstContainer() && !MSCConfig
                .isAppIdInLifecycleBlackList(getAppId())) {
            app.setFirstLaunched(false);
            app.setFirstPageLoadSuccess(false);
            setFirstContainer(false);
            dispatchLifecycleEvent(MSCAppLifecycle.MSC_WILL_LEAVE_APP_LIFECYCLE);
        }
        // 路由启动页面任务需要手动释放资源，避免内存泄漏
        releaseReleaseOfStartPageTask();

        // 关闭 & 销毁
        if (mContainer.isActivity() && getActivity().isFinishing()) {
            // 通知前端本Activity退出销毁导致的页面栈变化，用于传统/融合模式保活，不保活时属于多余操作
            sendNavigateBackWhenActivityClosed(routeTime);

            PageStackGlobalCache.getGlobalInstance().cleanStackForGlobal(stackSavedFlag);
            MSCActivity activity = (MSCActivity) mContainer;
            MSCFusionActivityMonitor.onActivityDestroy(activity, getContainerId());
        }
        if (isWidget()) {
            sendNavigateBackWhenActivityClosed(routeTime);
            PageStackGlobalCache.getGlobalInstance().cleanStackForGlobal(stackSavedFlag);
        }
        // widget场景依赖Destroy 调用释放对应内存
        if (getPageManager() != null) {
            getPageManager().onDestroy();
        }

        if (keyboardHeightProvider != null) {
            keyboardHeightProvider.close();
        }

        CrashReporterHelper.getContainerRecorder().removeBundle(mAppId);
        if (mRuntime != null) {
            mRuntime.unsubscribe(metainfoSubscriber);
            mRuntime.unsubscribe(loadFailSubscriber);
            if (!MSCHornRollbackConfig.isRollbackOnDestroyClearStartActivityCall()) {
                IStartActivityModule startActivityModule = mRuntime.getModule(IStartActivityModule.class);
                if (startActivityModule != null) {
                    startActivityModule.clearStartActivityCall(this);
                }
            }
        }
        clearOpenLinkParams();

        // 上报无页面被加载场景、启动失败场景
        reportPageExitSuccessRate();

        //销毁时清理routeId相关的信息，用于解决页面没创建就退出场景
        InstrumentLaunchManager.getInstance().removeLaunchInfo(String.valueOf(mRouteId));

        // 未触发onFirstRender场景下，提出页面时需要将startPageTask执行状态修改为完成，否则引擎复用场景会导致任务执行异常
        completeStartPageTask();

        // 容器退出时上报路由上报异常数据
        RouteReporter.create(mRuntime).reportRouteExceptCount();
    }

    private void releaseReleaseOfStartPageTask() {
        PageManager pageManager = getPageManager();
        if (pageManager != null) {
            pageManager.releaseResourceOfStartPageTask();
        } else {
            MSCLog.i(TAG, "releaseReleaseOfStartPageTask pageManager null");
        }
    }

    private void reportPageExitSuccessRate() {
        if (getPageManager() != null) {
            Page topPage = getPageManager().getTopPage();
            if (topPage != null) {
                topPage.reportPageExit();
                return;
            }
        }

        if (mRuntime != null && (isPageNotFound || !firstRender)) {
            // TODO: 2022/8/30 tianbin runtimeSource状态不正确，原因为异步上报时获取，状态已变更，目前影响不大
            MetricsEntry metricsEntry = mRuntime.getRuntimeReporter().record(ReporterFields.REPORT_PAGE_EXIT_SUCCESS_RATE);
            // TODO: 2022/8/19 tianbin 规范错误码
            String errorCode = "9000";
            if (!firstRender) {
                if (!isOnAppRouteCalled) {
                    errorCode = "7003";
                } else {
                    errorCode = "7001";
                }
            }
            if (clientReadyDuration < 0) {
                //launchStartTimeCurrentTimeMillis 和routeTime是同一时间，ContainerController中未存routeTime，
                // 直接使用launchStartTimeCurrentTimeMillis
                clientReadyDuration = System.currentTimeMillis() - launchStartTimeCurrentTimeMillis;
            }
            metricsEntry.tag("errorCode", errorCode);
            metricsEntry.tag("clientReadyDuration", clientReadyDuration);
            metricsEntry.tag(CommonTags.TAG_PAGE_PATH, mLaunchRoutePath);
            metricsEntry.tag(CommonTags.TAG_PURE_PAGE_PATH, PathUtil.getPath(mLaunchRoutePath));
            metricsEntry.tag(ReporterFields.WIDGET, isWidget());
            metricsEntry.tag(ReporterFields.LAUNCH_START, launchStartTimeCurrentTimeMillis);
            metricsEntry.tag(ReporterFields.EXIT_TIME, System.currentTimeMillis());
            metricsEntry.tag(ReporterFields.PAGE_STAY_TIME, System.currentTimeMillis() - launchStartTimeCurrentTimeMillis);
            AppPageReporter.appendDDDLoadPackageDetails(mRuntime, mRouteId, metricsEntry);
            AppPageReporter.appendLaunchMoment(mRuntime, mRouteId, metricsEntry.getTags());
            metricsEntry.value(0).sendRealTime();

            if (TextUtils.equals(errorCode, "7003") && MSCHornRollbackConfig.enableReportTimeoutTaskWhenPageExit()) {
                mRuntime.getRuntimeReporter().reportTaskTimeoutWhenPageExit(mLaunchRoutePath);
            }
        }
    }

    protected void onAppEnterForeground() {
        if (MSCHornRollbackConfig.isRollbackSetCalledOnAppEnterBackground()) {
            setCalledOnAppEnterBackground(false);
        }
        if (mContainer.isActivity()) {
            ((MSCActivity) mContainer).onAppEnterForeground();
        } else {
            defaultOnAppEnterForeground();
        }
    }

    protected void defaultOnAppEnterForeground() {
        MSCLog.i(TAG, "onAppEnterForeground");
        Map<String, Object> params = new HashMap<>();
        if (mAppLoader.isFrameworkReady()) {

            if (isRelaunching) {
                params.put("openType", OpenParams.RE_LAUNCH);
            } else if (isReusingEngine && !isFirstPageStackActivity && !firstRender && !isRecreate) {
                // 融合模式，非首个activity的创建过程中
                // 前端对携带着RE_LAUNCH、NAVIGATE_TO类型的有特殊处理 仅做app.onShow 不做即将被覆盖的页面的page.onShow，留待马上将发生的onAppRoute触发新页面的page.onShow
                // 融合模式 navigateTo 需要带refer
                boolean isTab = mRuntime.getMSCAppModule().checkIsTabPageIfRouted(mRealLaunchPath);
                if (!isTab) {
                    params = getReferrerInfoParams();
                }
                params.put("openType", isTab ? OpenParams.RE_LAUNCH : OpenParams.NAVIGATE_TO);
            } else if (mSrcAppID != null) {
                if (mScene == SceneNumber.BACK_FROM_MINI_PROGRAM) {
                    params = getReferrerInfoParams();
                    params.put("openType", OpenParams.NAVIGATE_BACK);
                    // mSrcAppID通常在onAppRoute中消耗，但back时不走onAppRoute，只有onAppEnterForeground，
                    // mSrcAppID不应留至下一次无关的onAppRoute时
                    if (isOnAppRouteCalled) {
                        // 若activity已销毁，正在重建，此时onAppRoute还未调用且马上会被调用
                        // 为防止onAppRoute调用时覆盖此处传的srcAppId等信息，暂不清空，在onAppRoute中再次传递
                        mSrcAppID = null;
                        mSrcExtraData = null;
                    }
                } else {
                    params.put("openType", OpenParams.APP_LAUNCH);
                }
            } else if (isSwitchTabOnNewIntent && !MSCHornRollbackConfig.isRollbackSwitchTabLifecycleFix()) {
                params.put("openType", OpenParams.RE_LAUNCH);
            } else {
                // 非小程序打开，或切后台再切回，或重建
                params = getReferrerInfoParams();
            }
            IPageModule topPage = mPageManager.getTopPage();
            if (MSCHornRollbackConfig.enableOnAppEnterForegroundPath(getAppId())) {
                String path = topPage != null ? topPage.getPagePath() : mLaunchRoutePath;
                params.put("path", path);
            } else {
                if (isWidget() || isRelaunching || (!firstRender && !isRecreate)) {
                    // path用于在app.onShow时传给业务，标识接下来将进入前台的页面
                    // 前端在拿不到此参数时会取页面栈最上的页面，此处客户端添加用于应对enterForeground后立即将发生onAppRoute的情况
                    // 如果能确保取到正确的值可以总是传入，但只有容器启动时/widget可以确定path是mRealLaunchPath，因此需要检查
                    params.put("path", mRealLaunchPath);
                }
            }
            params.put(MSCParams.SCENE, getScene());
            JSONObject paramsObj = JsonUtil.parseToJson(params);
            String paramStr = paramsObj.toString();
            int viewId = topPage != null ? topPage.getId() : 0;
            // 此处需要保证在onAppRoute前调用，目前由于AppRoute的执行过程涉及较多次异步消息，能事实上按此顺序执行，但缺乏强制保证，尤其在onNewIntent流程中
            AppStateModule appStateModule = mRuntime.getModule(AppStateModule.class);
            if (appStateModule != null) {
                appStateModule.onAppEnterForeground(paramStr, viewId, isActivity());
                if (!MSCHornRollbackConfig.isRollbackSetCalledOnAppEnterBackground()) {
                    setCalledOnAppEnterBackground(false);
                }
            }

//            mDevToolsDelegate.onAppEnterForeground(paramsObj);
        }

        if (!isRelaunching) {
            String openType = null;
            if (params.get("openType") != null) {
                openType = params.get("openType").toString();
            }
            // 在relaunch时，因马上要切到下一页面，略过当前页面的resume
            getPageMangerModule().onResume(openType);
        }
        isRelaunching = false;
        isSwitchTabOnNewIntent = false;
    }

    /**
     * 获取小程序场景值
     * https://developers.weixin.qq.com/miniprogram/dev/reference/scene-list.html
     *
     * @return
     */
    public int getScene() {
        return mScene;
    }

    /**
     * 小程序之间数据传递 —— extraData
     * https://km.sankuai.com/page/946846627
     */
    protected Map<String, Object> getReferrerInfoParams() {
        Map<String, Object> params = new HashMap<>();
        Map<String, Object> referrerInfo = new HashMap<>();
        referrerInfo.put("appId", mSrcAppID);
        if (mBackFromExternalNativeUrl != null) {
            referrerInfo.put("url", mBackFromExternalNativeUrl);
            mBackFromExternalNativeUrl = null;
        }
        if (mSrcExtraData != null) {
            try {
                JSONObject jsonObject = new JSONObject(mSrcExtraData);
                Object object = jsonObject.opt(MSCParams.EXTRA_DATA);
                referrerInfo.put(MSCParams.EXTRA_DATA, object != null ? object : jsonObject);
            } catch (JSONException e) {
                e.printStackTrace();
            }
            mSrcExtraData = null;
        }
        params.put("referrerInfo", referrerInfo);
        return params;
    }

    protected void onAppEnterBackground(boolean appIsVisible) {
        if (mPageManager == null || mApisManager == null) {
            return;
        }
        if (!MSCHornRollbackConfig.isRollbackLifecycle(getAppId())) {
            if (isCalledOnAppEnterBackground()) {
                return;
            }
        }
        MSCLog.i(TAG, "onAppEnterBackground");
        if (mAppLoader.isFrameworkReady()) {
            String params = "{\"mode\":\"hang\"" + ",\"appIsVisible\":" + appIsVisible + "}";
            int viewId = mPageManager.getTopPageId();
            AppStateModule appStateModule = mRuntime.getModule(AppStateModule.class);
            if (appStateModule != null) {
                // TODO: 2024/2/4 tianbin 埋点适配 setRouteMapping
                // 当调用navigateBack退出容器时，已上报过路由成功，此处不再重复上报
                if (!"navigateBack".equals(handleCloseContainerTriggerSource)) {
                    appStateModule.reportNavigateBackRouteStart(getActivity(), !isActivity());
                }
                appStateModule.onAppEnterBackground(params, viewId, isActivity());
                setCalledOnAppEnterBackground(true);
            }
        }

    }

    public boolean hasFirstRender() {
        return firstRender;
    }

    /**
     * Page首次渲染完成
     * 每个Page会调用一次，发生在Page层JSBridge线程
     */
    @Override
    public synchronized void onPageFirstRender(final String path, HashMap<String, Object> paramMap, String openType) {
        MSCLog.i(TAG, "onPageFirstRender", getAppId(), path);
        if (isAppRouteTaskEnabled(openType)) {
            dismissRouteLoading();
        } else {
            completeStartPageTask();
        }
        if (!firstRender) {
            firstRender = true;

            MSCApp app = mRuntime.getApp();
            if (app != null && app.isFirstLaunched() && !app.isFirstPageLoadSuccess() && !MSCConfig.isAppIdInLifecycleBlackList(mRuntime.getAppId())) {
                app.setFirstPageLoadSuccess(true);
                dispatchLifecycleEvent(MSCAppLifecycle.MSC_DID_ENTER_APP_LIFECYCLE);
            }

            ContainerStartState.instance.setIsContainerLaunching(false);

            onActivityFirstRender(paramMap);
            PendingBizPreloadTasksManager.getInstance().startPreloadPendingBizs();
            PreloadTasksManager.instance.preloadBasePackageAgainAfterFP();
            AppCheckUpdateManager.getInstance().batchCheckUpdateAgain();
            mRuntime.getModule(BackgroundCheckUpdateModule.class).backgroundCheckUpdateAfterFP(mRuntime);
        }

        // 渲染缓存/快照场景下，firstRender时机会很早，此时下载大量子包等操作会拖慢小程序后续显示线上内容，需要寻找时机，适当推后，目前为临时处理
        MSCExecutors.runOnUiThreadIdle(new PreloadTaskAfterPageFP(this, mRuntime, path), MSCHornPreloadConfig.getDeepPreloadDelayTime());
        IMSCContainer container = getMSCContainer();
        if (container instanceof MSCActivity) {
            ((MSCActivity) mContainer).onPageFirstRender(path, paramMap);
        } else if (container instanceof MSCWidgetFragment) {
            ((MSCWidgetFragment) container).onPageFirstRender();
        }
    }

    private void completeStartPageTask() {
        if (startPageFuture != null) {
            startPageFuture.complete(null);
        }

        dismissRouteLoading();
    }

    private void dismissRouteLoading() {
        if (getPageManager() != null) {
            getPageManager().dismissLoading();
        }
    }

    /**
     * 小程序启动完成
     * 保活或融合模式复用引擎，会再记一次
     */
    //TODO Widget不适用
    protected void onActivityFirstRender(HashMap<String, Object> paramMap) {
        MSCLog.i(Constants.LAUNCH, "onActivityFirstRender");
        mainHandler.post(() -> {
            // TODO: 2022/4/26 tianbin 内部逻辑无用，先注释
//            IntentFilter intentFilter = new IntentFilter(Intent.ACTION_CLOSE_SYSTEM_DIALOGS);
//            getActivity().registerReceiver(systemReceiver, intentFilter);
            initKeyboardHeightProvider();
        });

        MSCExecutors.runOnUiThread(new Runnable() {
            @Override
            public void run() {
                hideLoadingView();
                hideLoadingBg();
            }
        });

        if (isRecordRecentApp()) {
            if (MSCHornRollbackConfig.enableExternalAppPrefSourceLimit() && mRuntime.getMSCAppModule().getExternalApp()) {
                return;
            }
            RecentMSCManager.recordRecentAPPAsync(mAppId);
        }
//        mDevToolsDelegate.onFirstRender();
    }

    /**
     * 允许子类重写该方法，处理需要在onPageFirstRender进行的操作
     * 美团极简包在该时机会触发二级页小程序包的预下载和预加载
     */
    protected void onPageFirstRenderAndBeforeReport() {
    }

    /**
     * 允许子类重写该方法，用于修改
     * 使用场景：
     * 1. 美团极简包在首页对二级页小程序进行预加载，不需要记录首页小程序的AppId
     *
     * @return 是否记录当前小程序
     */
    protected boolean isRecordRecentApp() {
        return true;
    }

    //启动成功率统计 一次启动只上报一次（成功/取消）
    protected volatile boolean hasReportedLoadEnd;

    private boolean ensureSuccessOrFailOncePerStart() {
        if (hasReportedLoadEnd) {
            return false;
        }
        hasReportedLoadEnd = true;
        return true;
    }

    @Override
    public IPageManagerModule getPageMangerModule() {
        if (mIPageManagerModule == null) {
            synchronized (createPageManagerModuleLock) {
                if (mIPageManagerModule == null) {
                    mIPageManagerModule = new PageManagerModule(this, mRuntime, new IRuntimeGetter() {
                        @Override
                        public MSCRuntime getRuntime() {
                            return mRuntime;
                        }
                    });
                }
            }
        }
        return mIPageManagerModule;
    }

    public boolean isPageManagerModuleValid() {
        return mIPageManagerModule != null;
    }

    //todo ensure 启动的时候AppServiceReady
    //todo mge
    private void navigateFusionHomePage(long routeTime, int routeId) {
        MSCLog.i(TAG, "navigateFusionHomePage");
        if (MSCEnvHelper.getFusionPageManager() == null && mRuntime.hasContainerAttached() && mRuntime.getMSCAppModule().checkIsTabPageIfRouted(mRealLaunchPath)) {
            ToastUtils.toast("该Tab页面不支持当前启动方式");
            MSCLog.i(TAG, "HeraActivity navigateFusionHomePage");
            handleCloseContainer("navigateFusionHomePage");
            return;
        }

        if (checkLaunchRoutedPath()) {
            if (!MSCHornRollbackConfig.isRollbackStartPageAdvanced(getAppId())) {
                MSCExecutors.runOnUiThread(() -> {
                    Integer openSeq = null;
                    if (getIntent().hasExtra(MSCParams.OPEN_SEQ)) {
                        openSeq = getIntent().getIntExtra(MSCParams.OPEN_SEQ, 0); // 在widget通过navigateTo启动小程序Activity时可能传入
                    }
                    getPageManager().navigateHomePage(mRealLaunchPath, openSeq, routeTime, routeId);
                });
            } else {
                // 回滚
                mainHandler.post(() -> {
                    Integer openSeq = null;
                    if (getIntent().hasExtra(MSCParams.OPEN_SEQ)) {
                        openSeq = getIntent().getIntExtra(MSCParams.OPEN_SEQ, 0); // 在widget通过navigateTo启动小程序Activity时可能传入
                    }
                    getPageManager().navigateHomePage(mRealLaunchPath, openSeq, routeTime, routeId);
                });
            }
        } else {
            onPageNotFound(OpenParams.NAVIGATE_TO, routeTime);
        }
    }

    private boolean reloadPageByPageStackWatchDog(long routeTime, @NonNull BizNavigationExtraParams bizNavigationExtraParams) {
        if (!isReusingEngine) {
            return false;
        }

        if (pageStackWatchDog != null) {
            hideLoadingView();
            if (pageStackWatchDog.canReLoad()) {
                disablePreCreatePage = true;
                pageStackWatchDog.reloadTopOfStack(new PageStackWatchDog.IPageStackLoader() {
                    @Override
                    public void reloadTopOfStack(PageInfoArray pageInfoArray, long routeTime, @NonNull BizNavigationExtraParams bizNavigationExtraParams) {
                        MSCLog.i(TAG, "reloadTopOfStack");
                        getPageMangerModule().reload(pageInfoArray, routeTime, bizNavigationExtraParams);
                    }
                }, routeTime, bizNavigationExtraParams);
                return true;
            }
        }
        return false;
    }

    /**
     * 切换tab
     */
    protected boolean switchTab(String url, long routeTime, @NonNull BizNavigationExtraParams bizNavigationExtraParams) {
        try {
            getPageManager().switchTabPage(url, routeTime, bizNavigationExtraParams);
            return true;
        } catch (ApiException e) {
            MSCLog.e(TAG, e);
        }
        return false;
    }

    private void delayShowLoadingView() {
        mainHandler.postDelayed(mShowLoadingViewRunnable, SHOW_LOADING_VIEW_DELAY);
    }

    private final Runnable mShowLoadingViewRunnable = () -> {
        if (isDestroyed()) {
            return;
        }
        if (!isWidget()) {
            showLoadingBg();
        }
        showLoadingView();
    };

    private void ensureLoadingView() {
        if (mLoadingView == null) {
            ViewStub viewStub = findViewById(R.id.msc_loading);
            if (viewStub != null) {
                if (!MSCHornRollbackConfig.readConfig().rollbackHalfDialog) {
                    FrameLayout.LayoutParams layoutParams = (FrameLayout.LayoutParams) viewStub.getLayoutParams();
                    layoutParams.topMargin = getTopMarginAtTransparentContainer();
                }
                mLoadingView = (LinearLayout) viewStub.inflate();
                mTitle = mLoadingView.findViewById(R.id.msc_title);
                mIconContainer = mLoadingView.findViewById(R.id.msc_icon_container);
                mIcon = mLoadingView.findViewById(R.id.msc_icon);
                if (enableLoadingViewStyleOpt) {
                    mFlowerLoadingIndicator = new FlowerLoadingIndicator(mLoadingView.getContext(), false);
                    FrameLayout.LayoutParams layoutParams = new FrameLayout.LayoutParams(ScreenUtil.dp2px(30), ScreenUtil.dp2px(30));
                    mFlowerLoadingIndicator.setLayoutParams(layoutParams);
                    // 移除老样式LoadingView
                    mLoadingView.removeView(findViewById(R.id.indicator_image));
                }
            } else {
                MSCLog.e(TAG, "ensureLoadingView viewStub is null");
            }
        }
    }

    private int getTopMarginAtTransparentContainer() {
        return mContainer.getTopMarginAtTransparentContainer();
    }

    /**
     * 解偶方式获取业务自定义兜底页显示图片、文案
     */
    private void setCustomErrorView(int errorCode) {
        TextView title = mErrorView.findViewById(R.id.msc_load_failed_title);
        title.setText(String.format("%s:%s", getActivity().getText(R.string.msc_load_failed_title), errorCode));
        TextView subTitle = mErrorView.findViewById(R.id.msc_load_failed_subtitle);
        List<IMSCLoadErrorCustom> callbacks = ServiceLoader.load(IMSCLoadErrorCustom.class, getAppId());
        IMSCLoadErrorCustom imscLoadErrorCustom = (callbacks != null && callbacks.size() > 0) ? callbacks.get(0) : null;
        if (imscLoadErrorCustom == null) {
            MSCLog.i(TAG, "IMSCLoadErrorCustom callback is null");
        } else {
            String titleText = imscLoadErrorCustom.getCustomTitle();
            if (!TextUtils.isEmpty(titleText)) {
                title.setText(String.format("%s:%s", titleText, errorCode));
            }
            String subtitleText = imscLoadErrorCustom.getCustomSubTitle();
            if (!TextUtils.isEmpty(subtitleText)) {
                subTitle.setText(subtitleText);
            }
        }
    }

    /**
     * 错误页面
     * https://km.sankuai.com/collabpage/1632721111
     */
    @SuppressWarnings("WrongViewCast")
    private void ensureErrorView(int errorCode) {
        if (mErrorView == null) {
            ViewStub viewStub = (ViewStub) findViewById(R.id.msc_error_release);
            if (viewStub == null) {
                MSCLog.e(TAG, "ensureErrorView viewStub is null");
                return;
            }

            mErrorView = viewStub.inflate();
            if (mErrorView == null) {//调试过程中,发现可能出现mErrorView为空的npe异常,这里加个判断
                return;
            }
            setCustomErrorView(errorCode);
            mErrorView.setBackgroundColor(Color.WHITE);
            //in error view
            Button errorRetryBtn = mErrorView.findViewById(R.id.load_fail_retry_button);
            //in error view
            Button errorCloseBtn = mErrorView.findViewById(R.id.load_fail_close_button);

            // Container可以重新调整ErrorView布局
            mContainer.customErrorViewLayout(mErrorView);

            errorRetryBtn.setOnClickListener(new View.OnClickListener() {
                @Override
                public void onClick(View v) {
                    if (isWidget()) {
                        if ((getMSCContainer() instanceof MSCWidgetFragment)
                                && ((MSCWidgetFragment) getMSCContainer()).getWidgetReopenListener() != null) {
                            getRuntime().setDestroyWhenUnused(true);
                            getRuntime().setDestroyWhenUnusedReason(RuntimeDestroyReason.RETRY_WHEN_LOAD_ERROR);
                            ((MSCWidgetFragment) getMSCContainer()).notifyReopenWidgetToNative();
                            MSCLog.i(TAG, "AppId: ", getAppId(), ", widget fail retry ");
                        }
                    } else {
                        Intent intent = getActivity().getIntent();
                        intent.putExtra(MSCParams.DISABLE_REUSE_ANY, true);
                        intent.putExtra(ContainerController.EXTRA_RELAUCH_ON_ERROR, true);
                        mRuntime.destroy(false, RuntimeDestroyReason.toString(RuntimeDestroyReason.RETRY_WHEN_LOAD_ERROR));
                        IFusionPageManager.finishAllActivities(mAppId, getIntent());
                        startActivity(intent);
                    }
                }
            });

            errorCloseBtn.setOnClickListener(new View.OnClickListener() {
                @Override
                public void onClick(View v) {
                    mRuntime.destroy(false, RuntimeDestroyReason.toString(RuntimeDestroyReason.CLOSE_WHEN_LOAD_ERROR));
                    IFusionPageManager.finishAllActivities(mAppId, getIntent());
                }
            });
            boolean relaunchOnError = getIntent().getBooleanExtra(EXTRA_RELAUCH_ON_ERROR, false);
            if (isWidget()) {
                /**
                 * widget场景下:
                 * 1、如果没实现WidgetReopenListener，不展示重试按钮
                 * 2、relaunchOnError case，不展示重试按钮
                 */
                boolean hideRetryBtn = ((getMSCContainer() instanceof MSCWidgetFragment)
                        && ((MSCWidgetFragment) getMSCContainer()).getWidgetReopenListener() == null)
                        || relaunchOnError;
                errorRetryBtn.setVisibility(hideRetryBtn ? View.GONE : View.VISIBLE);
            } else {
                errorRetryBtn.setVisibility(relaunchOnError ? View.GONE : View.VISIBLE);
            }
            errorCloseBtn.setVisibility(relaunchOnError ? View.VISIBLE : View.GONE);
        }
    }

    private void showLoadingView() {
        ensureLoadingView();
        MSCLog.i(TAG, "showLoadingView:", mLoadingView);
        if (mLoadingView != null) {
            mLoadingView.setVisibility(View.VISIBLE);
        }
        updateLoadingViewStatus();
    }

    private void hideLoadingView() {
        mainHandler.removeCallbacks(mShowLoadingViewRunnable);
        MSCLog.i(TAG, "hideLoadingView:", mLoadingView);
        if (mLoadingView != null) {
            mLoadingView.setVisibility(View.GONE);
        }
    }

    /**
     * mLoadingBg用于遮挡在firstRender前出现的tab栏和nativeView等，但不遮挡navigationBar，以减少完全白屏时间，观感上会显得更快
     */
    private void showLoadingBg() {
        if (mLoadingBg != null) {
            mLoadingBg.setVisibility(View.VISIBLE);
        }
    }

    private void hideLoadingBg() {
        if (mLoadingBg != null) {
            mLoadingBg.setVisibility(View.GONE);
        }
    }

    @Override
    public void notifyServiceSubscribeUIEventHandler(String event, JSONObject params, int viewId) {
//        try {
//            if (viewId == 0) {
//                viewId = mPageManager.getTopPageId();
//            }
//            JSONObject data = new JSONObject();
//            data.put("type", event);
//            data.put("data", params);
//            notifyServiceSubscribeHandler("custom_event_UI", data.toString(), viewId);
//        } catch (JSONException e) {
//            e.printStackTrace();
//        }
    }

    /**
     * 通知Service层进行路由操作
     * 启动过程中在Page层View创建完成时首次调用，将触发小程序App.onLoad
     * 在onAppRoute中添加launchRefer参数,传给基础库
     *
     * @param cache 渲染缓存内容，大字符串，使用特殊的快速拼接方式，因此需要单独传递
     */
    @MainThread
    @Override
    public void onAppRoute(OpenParams openParams, int viewId, int reloadViewId, String cache) {
        if (isCurrentContainerMarkedClosed) {
            MSCLog.w(TAG, "skip onAppRoute for isCurrentContainerMarkedClosed ", viewId, reloadViewId,
                    openParams.url, openParams.openType);
            return;
        }

        isOnAppRouteCalled = true;  //本方法中调用的onAppEnterForeground要判断，需要先赋值
        if (clientReadyDuration < 0) { //第一个页面的onAppRoute发送前准备时间
            clientReadyDuration = System.currentTimeMillis() - openParams.getRouteTime();
        }

        String openType = openParams.openType;
        String path = openParams.url;

        Map<String, Object> params = new HashMap<>();
        if (mSrcAppID != null) {
            params = getReferrerInfoParams();
            params.put(MSCParams.SCENE, getScene());
            mSrcAppID = null;
            mSrcExtraData = null;
        }
        if (openParams.getExtraParams() != null) {
            params.putAll(openParams.getExtraParams());
        }
//        if (mScene == SceneNumber.OPEN_FROM_WIDGET) {
//            openType = OpenParams.WIDGET_OPEN_APP;
//        }
        /*
         * 栈底页面 发送携带lastRemovedPageId的NAVIGATE_BACK事件，用于pop当前页面及以上的，触发当前页面的onUnload，不触发下一个页面的onShow；
         * 与一般的NAVIGATE_BACK事件事件不同的是，一般的NAVIGATE_BACK事件指返回到当前页面，触发当前页面onShow，被隐藏页面的onUnload；
         */
        if (OpenParams.NAVIGATE_BACK_UTIL.equals(openType)) {
            //TODO : 当前仅对折叠屏设备开启removedPageIdList，后续全量
            if (MSCHornRollbackConfig.enableNavigateBackClearSpecifiedPageAppList(getAppId()) || (MSCHornRollbackConfig.get().getConfig().enableFoldNavigateBackClearSpecifiedPage && isFoldScreenDevice())) {
                MSCLog.i(TAG, "enableNavigateBackClearSpecifiedPage");
                //移除首页前，已经移除了其他页面，此时getAllPageId是不完整的
                if (mPageManager.isNeedRemoveTopPage()) {
                    MSCLog.i(TAG, "has removed page before, pageId list is incomplete");
                } else {
                    params.put("removedPageIdList", mPageManager.getAllPageId());
                }
            }
            params.put("lastRemovedPageId", viewId);
            openType = OpenParams.NAVIGATE_BACK;
        } else if (path != null && !mRuntime.getMSCAppModule().hasPage(path)) {
            //onPageNotFound https://km.sankuai.com/page/372116277
            params.put("pageNotFound", true);
        }
        params.put("path", path);
        if (openParams.openSeq != null) {
            params.put("openSeq", openParams.openSeq);
        }
        if (getPageMangerModule().getTopPage() != null) {
            params.put("engineType", getPageMangerModule().getTopPage().getRendererType().name().toLowerCase());
        }
        params.put("pageFrameId", "page_" + viewId);
        if (!MSCHornRollbackConfig.get().getConfig().rollback_set_route_mapping_onapproute) {
            params.put("originUrl", openParams.originUrl);
            params.put("isTab", openParams.isTab);
        }

        // 如果是Reload模式，viewId需要传旧的reloadViewId, 并增加一个newPageId参数传递新的viewId
        if (OpenParams.RELOAD.equals(openType) && reloadViewId != View.NO_ID) {
            params.put("newPageId", viewId);
            viewId = reloadViewId;
        }

        if (!isRecreate() && openParams.isFromLaunch) {
            params.put("routeTime", openParams.getRouteTime());
        }

        if (isWidget()) {
            Map<String, Object> widgetProperties;
            if (MSCHornRollbackConfig.enableInitialLargeData()) {
                widgetProperties = mContainer instanceof MSCWidgetFragment ? ((MSCWidgetFragment) mContainer).getMergedInitialDataMap() : null;
            } else {
                widgetProperties = JsonUtil.toMap(IntentUtil.getStringExtra(getIntent(), MSCParams.WIDGET_DATA));
            }
            params.put("widgetProperties", widgetProperties);
//            if (containerFragment.getWidgetEventListener() != null) {
//                params.put("registerWidgetEvents", containerFragment.getRegisteredWidgetEvents());
//            }
            if (OpenParams.RELOAD.equals(openType)) {
                openType = OpenParams.WIDGET_RELOAD;
            } else if (OpenParams.WIDGET_LAUNCH.equals(openType)) {
                addRequiredAppInfo(params);
            }
            if (isTabWidget()) {
                params.put("isTabWidget", true);
            }
        } else {
            if (OpenParams.APP_LAUNCH.equals(openType)) {
                addRequiredAppInfo(params);
            }
        }
        String launchRefer = IntentUtil.getStringExtra(getIntent(), Constants.LAUNCH_REFER);
        if (!TextUtils.isEmpty(launchRefer)) {
            params.put(Constants.LAUNCH_REFER, launchRefer);
        }
        // navigateTo / reload 主动触发app.onShow, 修复onAppRoute提前app.onShow晚于page.onShow的问题 https://km.sankuai.com/collabpage/1985759093
        if (!MSCHornRollbackConfig.isRollbackStartPageAdvanced(getAppId()) &&
                (OpenParams.NAVIGATE_TO.equals(openType) || OpenParams.RELOAD.equals(openType))) {
            params.put("triggerAppEnterForeground", true);
        }
        params.put("openType", openType);
        params.put("isNativeLaunch", !firstRender);
        if (!firstRender) {
            String runtimeSource = mRuntime != null ? RuntimeSource.toReportString(mRuntime.getSource()) : UNKNOWN_VALUE;
            params.put("nativeLaunchMode", runtimeSource);
        }
        JSONObject jsonObject = JsonUtil.parseToJson(params);
        String paramsString = jsonObject.toString();

        //TODO: formatStringSize耗时0.2ms
        MSCLog.i(TAG, "onAppRoute", openType, ", to", paramsString, "with render cache", FileSizeUtil.formatStringSize(cache));

        // 执行额外事件 添加initialRenderingData数据 向逻辑层发送缓存数据
        if (!TextUtils.isEmpty(cache)) {
            try {
                paramsString = new JsonUtil.FastBuilder(paramsString)
                        .put("initialRenderingData", cache, false)
                        .build();
            } catch (JSONException e) {
                MSCLog.e(e);
            }
        }

        if (!isAppRouteTaskEnabled(openType)) {
            if (!firstRender) {
                mAppService.injectGlobalField(OpenParams.RE_LAUNCH.equals(openType) ?
                        "__appReLaunchStartTime" : "__appLaunchStartTime", String.valueOf(launchStartTimeCurrentTimeMillis));
            }
        }

        sendOrCacheOnAppRoute(paramsString, openType, viewId);
//        mDevToolsDelegate.onAppRoute(jsonObject);

        if (params.get("pageNotFound") != null) {
            // 上报页面找不到次数
            mContainerReporter.reportPageNotFound(openType, path);
        }
        if (MSCHornRollbackConfig.readConfig().enableReportLaunchToAppRoute && !isRecreate() && openParams.isFromLaunch) {
            mContainerReporter.reportLaunchToAppRoute(openType, openParams.getRouteTime());
        }
//        if (!MSCEnvHelper.getEnvInfo().isProdEnv()) {
//            ReportUtils.reportRealTimePageInfo(mReporter, getIntent(), mAppId, getName(), path);
//        }

        if (getIMSCLibraryInterface() != null) {
            getIMSCLibraryInterface().onAppRoute(mRuntime, openType, paramsString, viewId);
        }

        cipDynamicContentReport(openParams.url);
    }

    private boolean isFoldScreenDevice() {
        // V1版本的判断由于历史对华为折叠屏的判断有问题，所以直接漏掉了，导致所有华为折叠屏的判断都出问题。
        boolean foldScreenV1 = MagicWindowUtils.isMagicWindowDevice(getActivity());
        boolean foldScreenV2 = MagicWindowUtils.isMagicWindowDeviceV2(getActivity());
        return (MSCHornRollbackConfig.get().getConfig().fixFoldScreenJudge && foldScreenV2) || foldScreenV1;
    }

    private IMSCLibraryInterface getIMSCLibraryInterface() {
        return IMSCLibraryInterfaceHelper.getIMSCLibraryInterface();
    }

    private void addRequiredAppInfo(Map<String, Object> params) {
        MSCRuntime mscRuntime = mRuntime;
        MSCAppModule mscAppModule = mscRuntime.getMSCAppModule();
        if (mscAppModule != null && mscAppModule.hasMetaInfo()) {
            params.put("appId", mscRuntime.getAppId());
            params.put("appName", mscAppModule.getAppName());
            params.put("version", mscAppModule.getMSCAppVersion());
            params.put("buildId", mscAppModule.getBuildId());
            params.put("publishId", mscAppModule.getPublishId());
        }
    }

    // 医药业务预热场景onAppRoute问题排查日志，https://km.sankuai.com/collabpage/2706436758
    // Java打印时间戳，毫秒级，输出格式类似: 2023-11-15 14:30:45.123
    // public static final SimpleDateFormat TIME_FORMAT = new SimpleDateFormat("yyyy-MM-dd HH:mm:ss.SSS");
    protected void sendOnAppRouteReal(String paramsString, int viewId, String openType) {
        if (isAppRouteTaskEnabled(openType)) {
            if (!firstRender) {
                mAppService.injectGlobalField(OpenParams.RE_LAUNCH.equals(openType) ?
                        "__appReLaunchStartTime" : "__appLaunchStartTime", String.valueOf(launchStartTimeCurrentTimeMillis));
            }
        }
        AppStateModule appStateModule = mRuntime.getModule(AppStateModule.class);
        if (appStateModule != null) {
            MSCLog.i(TAG, "Real_Send_OnAppRoute", mAppId, paramsString != null && paramsString.length() < MAX_LOGCAT_MESSAGE_LENGTH ? paramsString : "too long...", viewId);
            // MSCLog.i(TAG, "[MSC_LOG]Real_Send_OnAppRoute", mAppId, "appStateModule.onAppRoute", TIME_FORMAT.format(new Date(System.currentTimeMillis())));
            appStateModule.onAppRoute(paramsString, viewId);
            if (!mRuntime.enableReportAPIDataFix()) {
                IContainerManager containerManager = mRuntime.getContainerManagerModule();
                if (containerManager != null && !(TextUtils.equals(openType, OpenParams.NAVIGATE_BACK)
                        || TextUtils.equals(openType, OpenParams.NAVIGATE_BACK_UTIL)
                        || TextUtils.equals(openType, OpenParams.WIDGET_DESTROY))
                ) {
                    IPageModule pageModule = containerManager.getPageByPageId(viewId);
                    if (pageModule != null) {
                        pageModule.getRenderer().updateContainerStage("on_app_route");
                    }
                }
            }
        } else {
            MSCLog.i(TAG, "Cancel_Send_OnAppRoute_When_AppStateModule_Is_Null", paramsString, viewId);
        }
        MetricsModule metricsModule = mRuntime.getModule(MetricsModule.class);
        if (metricsModule != null) {
            metricsModule.sendFFPResetEvent(viewId);
        }
        PerfTrace.online().instant(PerfEventConstant.SEND_APP_ROUTE).report();
        mRuntime.getRuntimeReporter().addStatisticsToMap(Constants.APP_ROUTER);
        if (!isFirstOnAppRouteSend) {
            isFirstOnAppRouteSend = true;
            sendPendingWidgetData();
        }

        if (getIMSCLibraryInterface() != null) {
            getIMSCLibraryInterface().onPendingAppRouteRun(mRuntime, paramsString, viewId);
        }
    }


    @MainThread
    private synchronized void sendOrCacheOnAppRoute(String paramsString, String openType, int viewId) {
        if (MSCHornRollbackConfig.isRollbackPendingFrameWorkReady()) {
            pendingAppRoutes.add(new Runnable() {
                @Override
                public void run() {
                    sendOnAppRouteReal(paramsString, viewId, openType);
                }
            });

            if (mAppLoader.isFrameworkReady()) {
                //通知Service层的订阅处理器处理
                sendPendingOnAppRoutes();
            } else {
                MSCLog.i(TAG, "onAppRoute cached, framework not ready");
            }
        } else {
            if (isAppRouteTaskEnabled(openType)) {
                if (startPageFuture == null || startPageFuture.isDone()) {
                    // 系统按钮回退不走启动任务，需要直接发送onAppRoute
                    sendOnAppRouteReal(paramsString, viewId, openType);
                } else {
                    // StartPageTask任务结束，回传参数给AppRouteTask
                    startPageFuture.complete(new AppRouteParam(paramsString, viewId, firstRender, launchStartTimeCurrentTimeMillis, openType, this));
                }
            } else {
                sendOnAppRouteReal(paramsString, viewId, openType);
            }
        }
    }

    private final List<Runnable> pendingAppRoutes = new LinkedList<>();

    @MainThread
    private synchronized void sendPendingOnAppRoutes() {
        for (Runnable appRoute : pendingAppRoutes) {
            appRoute.run();
        }
        pendingAppRoutes.clear();
    }

    // 注意前后两者的差异，前者不能保证此时已有待执行的onAppRoute，因此与第一个onAppRoute发生没有必然关系
    private volatile boolean isFirstOnAppRouteSend;

    private final List<Map<String, Object>> pendingWidgetData = new CopyOnWriteArrayList<>();

    /**
     * 要求在第一个onAppRoute（导致app.onLaunch执行）发送后才可发送给前端，不满足则先缓存下来
     */
    public void onWidgetDataChange(Map<String, Object> data) {
        if (isFirstOnAppRouteSend) {
            doSendWidgetData(data);
        } else {
            // 因首次onAppRoute前拿不到viewId，延后执行，顺带保证初始数据与更新数据的时序
            pendingWidgetData.add(data);
        }
    }

    private void sendPendingWidgetData() {
        for (Map<String, Object> data : pendingWidgetData) {
            doSendWidgetData(data);
        }
        pendingWidgetData.clear();
    }

    private void doSendWidgetData(Map<String, Object> data) {
        String dataString = JsonUtil.toJsonString(data);
        if (MSCTraceUtil.TRACE_LEVEL >= MSCTraceUtil.LEVEL_LIGHT) {
            PerfTrace.begin("onWidgetDataChange").arg("data", dataString).arg("dataLength", dataString.length());
        }
        mRuntime.getJSModuleDelegate(WidgetListener.class)
                .onWidgetDataChange(
                        dataString,
                        mPageManager.getTopPageId());
        if (MSCTraceUtil.TRACE_LEVEL >= MSCTraceUtil.LEVEL_LIGHT) {
            PerfTrace.end("onWidgetDataChange");
        }
    }

    /**
     * 供优选独立app宿主计算fst用，MMP中没有实现，从Service层报过来
     */
    public void onPageFirstScreen(long timestamp, int pageId) {
        if (mContainer.isActivity()) {
            ((MSCActivity) mContainer).onPageFirstScreen(timestamp, pageId);
        }
    }

    @Override
    public void onKeyboardHeightChanged(int keyboardHeight, int screenOrientation) {
        for (KeyboardHeightObserver onKeyboardHeightChanged : this.keyboardHeightObserverList) {
            onKeyboardHeightChanged.onKeyboardHeightChanged(keyboardHeight, screenOrientation);
        }
        Activity activity = getActivity();
        int naviBarHeight = 0;
        if (keyboardHeight != 0) { // 键盘收起的时候会直接返回0
            naviBarHeight = SystemInfoUtils.getSystemNavigationBarHeight(activity);
            if (naviBarHeight == 0 && Build.VERSION.SDK_INT >= Build.VERSION_CODES.M) {
                // 在Android Q的底部导航条显示时，上一方法拿到的naviBarHeight为0，再次尝试获取
                if (activity != null && activity.getWindow() != null)
                    naviBarHeight = activity.getWindow().getDecorView().getRootWindowInsets().getSystemWindowInsetBottom();
            }
        }
        KeyboardApi.OnKeyboardHeightChangeParams ret = new KeyboardApi.OnKeyboardHeightChangeParams();
        ret.height = DisplayUtil.toWebValueCeil(keyboardHeight - naviBarHeight);
        mRuntime.apisManager.dispatchEvent("onGlobalKeyboardHeightChange", ret);
    }

    public void registerKeyboardListener(KeyboardHeightObserver keyboardHeightObserver) {
        if (keyboardHeightObserver != null) {
            this.keyboardHeightObserverList.add(keyboardHeightObserver);
        }
    }

    public void unRegisterKeyboardListener(KeyboardHeightObserver keyboardHeightObserver) {
        if (keyboardHeightObserver != null) {
            this.keyboardHeightObserverList.remove(keyboardHeightObserver);
        }
    }

    private void initKeyboardHeightProvider() {
        if (keyboardHeightProvider == null) {
            this.keyboardHeightProvider = new KeyboardHeightProvider(getActivity());
            keyboardHeightProvider.setKeyboardHeightObserver(ContainerController.this);
            mContainerView.post(() -> {
                keyboardHeightProvider.start();
            });
        }
    }

    public String getName() {
        return TAG;
    }

    @Deprecated
    public PageManager getPageManager() {
        return getPageMangerModule().getPageManager();
    }

    public String getAppId() {
        return mAppId;
    }

    public boolean isReload() {
        return mAppLoader != null && mAppLoader.isReload();
    }

    private final MSCSubscriber<AppMetaInfoWrapper> metainfoSubscriber = new MSCSubscriber<AppMetaInfoWrapper>() {
        @Override
        public void onReceive(MSCEvent<AppMetaInfoWrapper> event) {
            //TODO: 只有元信息真正有变化的时候，才需要重新发起数据预拉取请求
            MSCLog.i(TAG, "onAppPropUpdated");
            // MetaInfo更新时，执行数据预拉取操作
            dataPrefetchAfterPageStart();
            dataPrefetchForApp();
            updateAppProp();
        }
    };

    private final MSCSubscriber<AppLoadException> loadFailSubscriber = new MSCSubscriber<AppLoadException>() {
        @Override
        public void onReceive(MSCEvent<AppLoadException> event) {
            MSCLog.i(TAG, "loadFailSubscriber", event);
            AppLoadException ex = event.getData();
            if (isWidget()) {
                onLaunchError(ex.getMessage(), ex.getErrorCode(), ex);
            } else {
                downgrade(ex.getMessage(), ex.getErrorCode(), ex);
                MSCApp app = getMiniApp();
                if (app != null && !app.isFirstPageLoadSuccess() && !MSCConfig.isAppIdInLifecycleBlackList(getAppId())) {
                    dispatchLifecycleEvent(MSCAppLifecycle.MSC_LAUNCH_FAIL_APP_LIFECYCLE);
                }
            }

            // 页面已退出 则不再上报页面加载成功率指标，对齐iOS
            if (!getActivity().isDestroyed() && !getActivity().isFinishing() && !isDestroyed()) {
                mContainerReporter.onAppLoaderLoadFail(getActivity(), mRuntime, mRouteId, ex);
            }
        }
    };

    public CompletableFuture<AppRouteParam> startPage(boolean fromKeepAlive, long routeTime, int routeId) {
        String name = PerfTrace.buildEventName(CLASS_NAME, "startPage");
        PerfTrace.begin(name);
        PerfTrace.online().begin(PerfEventConstant.START_PAGE).report();
        startPageFuture = new CompletableFuture<>();

        Runnable runnable = new Runnable() {
            @Override
            public void run() {
                PerfTrace.online().instant(PerfEventConstant.START_PAGE_TASK_INNER).report();
                String innerName = name + "_inner";
                PerfTrace.begin(innerName);
                MSCLog.i("StartPage post=> ", ContainerController.this, isWidget(), fromKeepAlive, mApp.isPageLaunched);
                if (getActivity().isDestroyed() || getActivity().isFinishing() || isDestroyed()) {
                    PerfTrace.end(innerName).arg("error", "pageDestroyed");
                    PerfTrace.end(name).arg("error", "pageDestroyed");
                    return;
                }
                if (!MSCHornRollbackConfig.isRollbackSetRouteMappingPersist()) {
                    checkClearRouteMappingPersist();
                }

                if (isWidget()) {
                    startHomePage(routeTime, routeId);
                } else if (!fromKeepAlive || !mApp.isPageLaunched) {
                    // isReusingEngine && !isPageLaunched，此情况为widget启动过后启动activity，需要按首次启动页面栈处理
                    isFirstPageStackActivity = true;
                    // 前端要求对页面栈（不含widget）的第一个操作是openType=appLaunch，后面的两个分支均是appLaunch
                    // 且appLaunch仅允许发一次，通过mApp.isPageLaunched限制
                    startHomePage(routeTime, routeId);
                } else {
                    // 融合模式下，启动非首个容器时走该流程

                    // 对AppEngine来说isSubPackageLoaded = true其实不能保证，但此时AppEngine中的subPackage可能不是本次要启动的subPackage
                    // 如需要子包会由navigateFusionHomePage通过前端再次发起加载
                    updateAppProp();
                    ensureLaunchPath();
                    // 对已经启动过正常页面栈页面的引擎，再次进入对前端来说是navigate操作
                    navigateFusionHomePage(routeTime, routeId);
                }
                PerfTrace.end(innerName);
                PerfTrace.end(name);
                PerfTrace.online().end(PerfEventConstant.START_PAGE).report();
            }
        };
        MSCLog.i("StartPage => ", ContainerController.this, isWidget(), fromKeepAlive, mApp.isPageLaunched);
        // 连续启动两个页面的场景 https://km.sankuai.com/collabpage/1595618180
        // 避免 false true false  任务在  false false true 之前被执行了
        // 保活场景启动，前置还没launch过，保证runable执行相对顺序，不能将新的页面先执行applaunch，否则前端客户端页面栈会不一致
        if (fromKeepAlive && !mApp.isPageLaunched) {
            MSCExecutors.runOnUiThread(runnable);
        } else {
            MSCExecutors.runOnUiThreadFrontOfQueue(runnable);
        }
        return startPageFuture;
    }

    private void ensureLaunchPath() {
        mRealLaunchPath = getMPTargetPath();
        if (mRuntime.getMSCAppModule() == null || !mRuntime.getMSCAppModule().hasMetaInfo()
                || !mRuntime.getAppConfigModule().hasConfig()) {
            MSCLog.w("checkLaunchPath", "empty metaInfo or config");
            return;
        }
        if (TextUtils.isEmpty(mRealLaunchPath)) {
            mRealLaunchPath = mRuntime.getMSCAppModule().getRootPath();
        }
        AppConfigModule appConfigModule = mRuntime.getAppConfigModule();
        if (appConfigModule == null) {
            return;
        }
        String routePersistPath = appConfigModule.getRoutePathPersistIfRouted(mRealLaunchPath);
        String routePath = appConfigModule.getRoutePathIfRouted(mRealLaunchPath);
        mLaunchRoutePath = mRealLaunchPath;
        if (!TextUtils.isEmpty(routePersistPath)) {
            if (appConfigModule.hasPage(routePersistPath)) {
                mLaunchRoutePath = routePersistPath;
            }
        } else if (!TextUtils.isEmpty(routePath)) {
            if (appConfigModule.hasPage(routePath)) {
                mLaunchRoutePath = routePath;
            }
        }
    }

    public boolean checkLaunchRoutedPath() {
        ensureLaunchPath();
        return mRuntime.getMSCAppModule().checkHasPageIfRouted(mRealLaunchPath);
    }

    public void onTrimMemory(int level) {
        MSCLog.i(TAG, "onTrimMemory level=", level);
        if (level == TRIM_MEMORY_RUNNING_MODERATE ||
                level == TRIM_MEMORY_RUNNING_LOW ||
                level == TRIM_MEMORY_RUNNING_CRITICAL) {
            AppStateModule appStateModule = mRuntime.getModule(AppStateModule.class);
            if (appStateModule != null) {
                appStateModule.onMemoryWarning(level);
            }
            reportMemoryWarning(level, ReporterFields.REPORT_STABILITY_MEMORY_WARNING);
        } else {
            reportMemoryWarning(level, ReporterFields.REPORT_STABILITY_MEMORY_WARNING_V2);
        }
    }

    private void reportMemoryWarning(int level, String reportStabilityMemoryWarning) {
        IPageModule topPage = getPageMangerModule().getTopPage();
        MSCReporter reporter;
        if (topPage == null || (reporter = topPage.getReporter()) == null) {
            reporter = getContainerReporter();
        }
        if (reporter == null) {
            MSCLog.i(TAG, "reportMemoryWarning reporter is null");
            return;
        }
        reporter.record(reportStabilityMemoryWarning)
                .tag("level", level)
                .tag(CommonTags.TAG_PAGE_PATH, PathUtil.getPath(getTopPagePath()))
                .tag("isForeground", isResumed())
                .sendRealTime();
    }

    /**
     * 设置业务Native地址（小程序启动业务Native界面的url）
     */
    public void setBackFromExternalNativeUrl(String url) {
        mBackFromExternalNativeUrl = url;
        if (mJsErrorRecorder != null) {
            mJsErrorRecorder.pushPage(url, PageRecord.ID_NATIVE);
        }
    }

    /**
     * 恢复状态，因activity被系统回收，恢复回收之前页面字段的状态
     */
    private void restoreState(Bundle savedInstanceState) {
        if (savedInstanceState != null) {
            mBackFromExternalNativeUrl = savedInstanceState.getString(BACK_FROM_EXTERNAL_NATIVE_URL);
        }
    }

    private void onPageNotFound(String openType, long routeTime) {
        isPageNotFound = true;
        if (isAppRouteTaskEnabled(openType)) {
            dismissRouteLoading();
        } else {
            completeStartPageTask();
        }
        hideLoadingView();
        hideLoadingBg();
        if (!this.getMSCContainer().onLaunchError(
                "LaunchPath can't find",
                MSCLoadErrorConstants.ERROR_PERF_PAGE_NOT_FOUND_EXCEPTION, new NoStackException())) {
            MSCLog.i(TAG,
                    "onPageNotFound, " +
                            "default decide what to show");
            mPageManager.pageNotFound(mRealLaunchPath,
                    openType, routeTime);
        } else {
            MSCLog.i(TAG, "onPageNotFound, this.getMSCContainer().onLaunchError decide what to show");
        }
        IMSCContainer container = getMSCContainer();
        if (container instanceof MSCWidgetFragment) {
            ((MSCWidgetFragment) container).onPageNotFound();
        }
    }

    @Override
    public String toString() {
        String result = "ContainerController{" + Integer.toHexString(hashCode());
        if (isWidget()) {
            result += " widget in activity: " + getActivity() + "}";
        } else {
            result += " for activity: " + getActivity() + "}";
        }
        return result;
    }
//
//    private final BroadcastReceiver systemReceiver = new BroadcastReceiver() {
//
//        private static final String SYSTEM_DIALOG_REASON_KEY = "reason";
//        private static final String SYSTEM_DIALOG_REASON_HOME_KEY = "homekey";
//        private static final String SYSTEM_DIALOG_REASON_RECENT_APPS = "recentapps";
//
//        @Override
//        public void onReceive(Context context, Intent intent) {
//            String action = intent.getAction();
//            if (action.equals(Intent.ACTION_CLOSE_SYSTEM_DIALOGS)) {
//                String reason = intent.getStringExtra(SYSTEM_DIALOG_REASON_KEY);
//                if (reason == null)
//                    return;
//                if (reason.equals(SYSTEM_DIALOG_REASON_HOME_KEY)) {
//                    IPageModule top = mPageManager.getTopPage();
//                    if (top != null) {
//                        //fixme msc
////                        top.onSystemDialogClose(SYSTEM_DIALOG_REASON_HOME_KEY);
//                    }
//
//                }
//                // 最近任务列表键
//                if (reason.equals(SYSTEM_DIALOG_REASON_RECENT_APPS)) {
//                    IPageModule top = mPageManager.getTopPage();
//                    if (top != null) {
//                        //fixme msc
////                        top.onSystemDialogClose(SYSTEM_DIALOG_REASON_RECENT_APPS);
//                    }
//
//                }
//            }
//        }
//    };

    /**
     * 显示启动错误页面
     *
     * @param msg
     * @param code
     * @param e
     */
    private void showLaunchFailView(String msg, int code, Throwable e) {
        ensureErrorView(code);
        if (mErrorView == null) {
            return;
        }
        // 客户端版本不满足小程序最低基础库版本要求，提示升级并关闭小程序页面
        if (code == MSCLoadErrorConstants.ERROR_CODE_NEED_UPGRADE_HOST_APP) {
            startUpgradeHostAppPageAndFinish();
            return;
        }
        mErrorView.setVisibility(View.VISIBLE);
        if (TextUtils.isEmpty(msg)) {
            return;
        }
        //library Module中没有使用，devtools Module中hook之后展示错误页面
        showLaunchErrorDialog(msg, code, e);
    }

    /**
     * 用于测试环境展示错误信息
     * 集成devtools的情况，通过AOP替换showLaunchErrorDialog方法，展示错误信息
     *
     * @param msg
     * @param code
     * @param e
     * @return
     */
    public void showLaunchErrorDialog(String msg, int code, Throwable e) {
    }

    private void startUpgradeHostAppPageAndFinish() {
        MSCLog.i(TAG, "showLaunchFailView startUpgradeAppPage");

        String appName;
        String appIcon;
        if (mRuntime.getMSCAppModule().hasMetaInfo()) {
            appName = mRuntime.getMSCAppModule().getAppName();
            appIcon = mRuntime.getMSCAppModule().getIconUrl();
        } else {
            appName = getStringExtra(MSCParams.APP_NAME);
            appIcon = getStringExtra(MSCParams.APP_ICON);
        }
        MiniProgramUtil.startUpgradeAppPage(getActivity(), getAppId(), appName, appIcon, mRuntime.getMSCAppModule().getPublishId());
    }

    public void handlePageOverrideContainerTransition() {
        IPageModule bottomPage = getPageMangerModule().getBottomPage();
        if (bottomPage != null) {
            PageTransitionConfig.applyContainerPopTransition(getActivity(), bottomPage.getPageTransitionConfig());
        } else {
            MSCLog.i(TAG, "handlePageOverrideContainerTransition bottomPage is null");
        }
    }

    public Map<String, Object> getFFPTags() {
        IPageModule topPage = getPageMangerModule().getTopPage();
        if (topPage == null) {
            return null;
        }
        AppPageReporter pageReporter = topPage.getReporter();
        return pageReporter != null ? pageReporter.getFFPTags() : null;
    }

    /**
     * 外部路由场景下清空路由映射
     */
    private void checkClearRouteMappingPersist() {
        String clearRouteMappingPersist = IntentUtil.getStringExtra(getIntent(), MSCParams.CLEAR_ROUTE_MAPPING);
        if (MSCParams.PERSIST.equals(clearRouteMappingPersist)) {
            MSCLog.i(TAG, "needClearRouteMappingPersist");
            RouteMappingModule.innerClearSetRouteMappingPersist(getRuntime());
        }
    }

    /**
     * 容器覆盖率上报。onAppRoute时机、首页加载失败场景。二级页面加载失败不会创建page
     *
     * @param path
     */
    private void cipDynamicContentReport(String path) {
        //容器侧关闭覆盖率上报
        if (!MSCHornRollbackConfig.enableDynamicContentReport()) {
            return;
        }
        /**
         * runtime为null的目前已知路径是：appId为empty导致MSCActivity.onCreate直接走进onLauncherError的上报流程：https://ones.sankuai.com/ones/product/31464/workItem/defect/detail/89176329?activeTabName=first
         * 此处直接过滤不再上报。
         */
        if (TextUtils.isEmpty(path) || getRuntime() == null) {
            return;
        }

        String purePath = PathUtil.getPath(path);
        String displayName = String.format("%s_%s", getAppId(), purePath);
        String techType = CIPDisplayTechType.MSC_WEBVIEW;
        RendererType targetType = getRuntime().getMSCAppModule().getRendererTypeForPage(purePath);
        if (targetType == RendererType.NATIVE || targetType == RendererType.RN) {
            techType = CIPDisplayTechType.MSC_NATIVE;
        }
        if (isWidget()) { //卡片
            CIPDynamicContentTracker.reportCardInfo(getActivity(), getAppId(), displayName, techType, null);
        } else { //页面
            CIPDynamicContentTracker.reportPageInfo(getActivity(), getAppId(), displayName, techType, null);
        }
    }

    /**
     * 外部路由场景下判断是否忽略路由映射，单次生效
     *
     * @return true 忽略路由映射 false 不忽略
     */
    @Override
    public boolean isIgnoreRouteMapping(boolean external) {
        if (!external) {
            return false;
        }
        String ignoreRouteMapping = IntentUtil.getStringExtra(getIntent(), MSCParams.IGNORE_ROUTE_MAPPING);
        return ignoreRouteMapping != null && ignoreRouteMapping.equals(MSCParams.PERSIST);
    }

    private boolean isLaunchFirstPage(String openType) {
        return isLaunchFirstPage && OpenParams.APP_LAUNCH.equals(openType);
    }

    @Override
    public boolean isAppRouteTaskEnabled(String openType) {
        return MSCHornPreloadConfig.enableAppRouteTask(getAppId()) && isLaunchFirstPage(openType);
    }

    @VisibleForTesting
    public void setAppLoader(IAppLoader appLoader) {
        mAppLoader = appLoader;
    }

    @VisibleForTesting
    public void setAppService(AppService appService) {
        mAppService = appService;
    }

    @VisibleForTesting
    public void setRuntime(MSCRuntime runtime) {
        mRuntime = runtime;
    }

    @VisibleForTesting
    public void setPageManager(IPageManagerModule pageManager) {
        mPageManager = pageManager;
    }
}
