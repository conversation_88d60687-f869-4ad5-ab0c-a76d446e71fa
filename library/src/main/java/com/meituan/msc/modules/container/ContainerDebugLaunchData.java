package com.meituan.msc.modules.container;

import android.content.Intent;
import android.text.TextUtils;

import com.meituan.msc.common.utils.IntentUtil;
import com.meituan.msc.extern.MSCEnvHelper;
import com.meituan.msc.lib.interfaces.container.MSCParams;
import com.meituan.msc.modules.engine.EngineHelper;

/**
 * 容器调试启动参数
 * PS：频繁调用intent 解析URL获取参数会影响性能
 */
public class ContainerDebugLaunchData {

    private final boolean isReload;
    private final String debugServer;
    private final String checkUpdateUrl;
    private final String mscVersion;
    private final String appEnvironment;
    private final String publishId;
    private final String isDebug;

    public ContainerDebugLaunchData(Intent intent) {
        if (intent == null || MSCEnvHelper.getEnvInfo().isProdEnv()) {
            isReload = false;
            debugServer = "";
            checkUpdateUrl = "";
            mscVersion = "";
            appEnvironment = "";
            publishId = "";
            isDebug = "";
            return;
        }
        isReload = EngineHelper.getReloadStatusOnTestEnv(intent);
        debugServer = IntentUtil.getStringExtra(intent, MSCParams.DEBUG_PROXY);
        checkUpdateUrl = IntentUtil.getStringExtra(intent, MSCParams.CHECK_UPDATE_URL);
        mscVersion = IntentUtil.getStringExtra(intent, MSCParams.MSC_VERSION);
        appEnvironment = IntentUtil.getStringExtra(intent, MSCParams.APP_ENVIRONMENT);
        publishId = IntentUtil.getStringExtra(intent, MSCParams.PUBLISH_ID);
        isDebug = IntentUtil.getStringExtra(intent, MSCParams.DEBUG);
    }

    public boolean isReload() {
        return isReload;
    }

    public String getDebugServer() {
        return debugServer;
    }

    public String getCheckUpdateUrl() {
        return checkUpdateUrl;
    }

    public String getMSCVersion() {
        return mscVersion;
    }

    public String getAppEnvironment() {
        return appEnvironment;
    }

    public String getPublishId() {
        return publishId;
    }

    public String isDebug() {
        return isDebug;
    }

    public boolean needForceCheckUpdate() {
        return isReload() || !TextUtils.isEmpty(getCheckUpdateUrl());
    }

    public boolean needCreateRuntime() {
        return isReload() || !TextUtils.isEmpty(getDebugServer());
    }
}
