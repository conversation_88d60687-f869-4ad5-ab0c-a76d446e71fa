package com.meituan.msc.modules.container;

import android.app.Activity;
import android.arch.lifecycle.Lifecycle;
import android.content.Intent;
import android.os.Bundle;
import android.os.SystemClock;
import android.support.annotation.NonNull;
import android.support.annotation.Nullable;

import com.meituan.msc.common.utils.DisplayUtil;
import com.meituan.msc.common.utils.IntentUtil;
import com.meituan.msc.lib.interfaces.container.MSCParams;
import com.meituan.msc.modules.api.msi.navigation.BizNavigationExtraParams;
import com.meituan.msc.modules.page.IPageManagerModule;
import com.meituan.msc.modules.reporter.MSCLog;

/**
 * Created by letty on 2022/1/14.
 **/
public abstract class ContainerDelegate implements IContainerDelegate, IContainerLifeCycle {
    public static final String CONTAINER_ID = "containerId";
    protected IMSCContainer mContainer;
    protected ContainerReporter mContainerReporter;

    protected Activity mActivity; //为防止Fragment作为Host时detach而获取不到activity，保存下来，目前实际不支持Fragment detach
    protected IContainerManager mContainerManagerModule;
    protected volatile IPageManagerModule mIPageManagerModule;
    protected boolean mIsPaused = false;
    private int mContainerId;
    protected volatile boolean isRecreate;
    private int mRecreateType;

    public ContainerDelegate setContainerManagerModule(IContainerManager containerManagerModule) {
        mContainerManagerModule = containerManagerModule;
        return this;
    }

    public IContainerManager getContainerManagerModule() {
        return mContainerManagerModule;
    }

    @Override
    public boolean isWidget() {
        return !isActivity();
    }

    public boolean isActivity() {
        return getMSCContainer().isActivity();
    }

    @Override
    public void setActivity(Activity activity) {
        mActivity = activity;
    }

    /**
     * 返回对应的activity
     * 注意在Widget情况下，container不是activity，此处获取到的是container fragment所在的外部activity
     * 在onCreate中赋值，不会随fragment detach清除，以避免某些时序问题
     */
    @NonNull
    public Activity getActivity() {
        return mActivity;
    }

    @Override
    public boolean isPaused() {
        return mIsPaused;
    }

    public abstract void preCreateRuntime(Bundle savedInstanceState);

    // TODO: 2022/4/25 tianbin onCreate onLaunchParamsCheckFinished onActivityCreated 命名优化
    @Override
    public void onCreate(Bundle savedInstanceState) {
        if (savedInstanceState == null) {
            mContainerId = hashCode() + (int) SystemClock.elapsedRealtime();
        } else {
            mContainerId = savedInstanceState.getInt(CONTAINER_ID);
            MSCLog.d(CONTAINER_ID, "container restored: ", mContainerId);
        }
        DisplayUtil.setDisplayMetrics(mActivity);
    }

    /**
     * 重建检测，
     * 1、
     *
     * @return
     */
    public boolean isRecreate() {
        return isRecreate;
    }

    public int getRecreateType() {
        return mRecreateType;
    }

    public void setIsRecreate(boolean isRecreate, int recreateType) {
        this.isRecreate = isRecreate;
        mRecreateType = recreateType;
    }

    public void onLaunchParamsCheckFinished(Bundle savedInstanceState, long routeTime, @NonNull BizNavigationExtraParams bizNavigationExtraParams) {
    }

    public void onActivityCreated(@Nullable Bundle savedInstanceState, long routeTime, @NonNull BizNavigationExtraParams bizNavigationExtraParams) {
    }

    @Override
    public void onStart() {

    }

    @Override
    public void onResume() {
        mIsPaused = false;
        if (getContainerManagerModule() != null) {
            getContainerManagerModule().onContainerResume(this);
        }
    }

    @Override
    public void onPause() {
        mIsPaused = true;
        if (getContainerManagerModule() != null) {
            getContainerManagerModule().onContainerPause(this);
        }
    }

    @Override
    public void onStop() {
    }

    @Override
    public void onDestroy() {
        if (getContainerManagerModule() != null) {
            getContainerManagerModule().onContainerDestroy(this);
        }
    }

    @Override
    public void onWindowFocusChanged(boolean hasFocus) {
        if (getContainerManagerModule() != null) {
            getContainerManagerModule().onWindowFocusChanged(this, hasFocus);
        }
    }

    @Override
    public void onSaveInstanceState(Bundle outState) {
        outState.putInt(CONTAINER_ID, mContainerId);
    }

    public int getContainerId() {
        return mContainerId;
    }


    protected String getStringExtra(String name) {
        return IntentUtil.getStringExtra(getIntent(), name);
    }

    public IMSCContainer getMSCContainer() {
        return mContainer;
    }

    public void setContainer(IMSCContainer container) {
        mContainer = container;
        mActivity = container.getActivity();
    }

    @Override
    public Intent getIntent() {
        return mContainer.getIntent();
    }

    public String getUri(){
        Intent intent = getIntent();
        if (intent != null) {
            return intent.getDataString();
        }
        return null;
    }

    public Lifecycle.State getLifecycleState() {
        return mContainer.getLifecycle().getCurrentState();
    }

    public boolean isResumed() {
        return getLifecycleState().isAtLeast(Lifecycle.State.RESUMED);
    }

    public boolean isDestroyed() {
        return !getLifecycleState().isAtLeast(Lifecycle.State.CREATED);
    }

    public boolean isFinishing() {
        return getActivity().isFinishing();
    }

    public ContainerReporter getContainerReporter() {
        return mContainerReporter;
    }

    @Override
    public boolean isDisableReuseAny() {
        Intent intent = getIntent();
        if (intent != null) {
            return intent.getBooleanExtra(MSCParams.DISABLE_REUSE_ANY, false);
        }
        return false;
    }
}
