package com.meituan.msc.modules.container;

import android.text.TextUtils;

import com.meituan.msc.common.config.MSCConfig;
import com.meituan.msc.common.report.MetricsEntry;
import com.meituan.msc.common.utils.MPConcurrentHashMap;
import com.meituan.msc.modules.engine.MSCRuntime;

import org.json.JSONArray;

import java.util.List;
import java.util.Map;
import java.util.concurrent.CopyOnWriteArrayList;

public class ContainerLaunchErrorManager {

    private static volatile ContainerLaunchErrorManager instance;

    public static ContainerLaunchErrorManager getInstance() {
        if (instance == null) {
            synchronized (ContainerLaunchErrorManager.class) {
                if (instance == null) {
                    instance = new ContainerLaunchErrorManager();
                }
            }
        }
        return instance;
    }

    private ContainerLaunchErrorManager() {
    }

    private final Map<String, List<String>> launchErrorCodeMap = new MPConcurrentHashMap<>();

    public void appendLaunchErrorCode(String appId, int launchErrorCode) {
        if (TextUtils.isEmpty(appId)) {
            return;
        }
        List<String> errorCodeList = launchErrorCodeMap.get(appId);
        if (errorCodeList == null) {
            errorCodeList = new CopyOnWriteArrayList<>();
            launchErrorCodeMap.put(appId, errorCodeList);
        }
        errorCodeList.add(String.valueOf(launchErrorCode));
    }

    public JSONArray getLaunchErrorCodeMap(String appId) {
        JSONArray jsonArray = new JSONArray();
        List<String> errorCodeList = launchErrorCodeMap.get(appId);
        if (errorCodeList == null) {
            return jsonArray;
        }
        for (int i = 0; i < errorCodeList.size(); i++) {
            if (i > MSCConfig.getLoadErrorCodesLimit()) {
                break;
            }
            jsonArray.put(errorCodeList.get(i));
        }
        return jsonArray;
    }

    public void clear(String appId) {
        launchErrorCodeMap.remove(appId);
    }

    public static void addLaunchErrorCodesToEntry(MetricsEntry entry, MSCRuntime runtime) {
        if (entry == null || runtime == null) {
            return;
        }
        JSONArray errorCodeList = getInstance().getLaunchErrorCodeMap(runtime.getAppId());
        if (errorCodeList.length() > 0) {
            entry.tag("loadErrorCodes", errorCodeList);
        }
    }
}
