package com.meituan.msc.modules.container;

import android.app.Activity;
import android.support.annotation.NonNull;
import android.view.View;

import com.meituan.msc.modules.container.fusion.MSCFusionActivityMonitor;
import com.meituan.msc.modules.engine.MSCHornRollbackConfig;
import com.meituan.msc.modules.engine.RuntimeMemoryLeakMonitor;
import com.meituan.msc.modules.manager.MSCEvent;
import com.meituan.msc.modules.manager.MSCMethod;
import com.meituan.msc.modules.manager.MSCModule;
import com.meituan.msc.modules.manager.ModuleName;
import com.meituan.msc.modules.page.IPageManagerModule;
import com.meituan.msc.modules.page.IPageModule;
import com.meituan.msc.modules.reporter.MSCLog;

import java.util.List;
import java.util.concurrent.CopyOnWriteArrayList;
import java.util.concurrent.atomic.AtomicInteger;

import javax.annotation.Nullable;

/**
 * 所有容器管理
 * Created by letty on 2022/1/12.
 **/
// 向前端暴露模块名统一为PageManager
@ModuleName(name = "PageManager")
public class ContainerManagerModule extends MSCModule implements IContainerManager {

    //todo iterator thread safety
    public List<IContainerDelegate> mContainerDelegates = new CopyOnWriteArrayList<>();
    public volatile IContainerDelegate mTopContainerDelegate; // last activity
    private String rollbackEfficiencyRateTestCachedWhenLaunch;

    @Override
    public Activity getTopActivity() {
        IContainerDelegate containerDelegate = mTopContainerDelegate;
        return containerDelegate != null ? containerDelegate.getActivity() : null;
    }

    @NonNull
    @Override
    public List<IContainerDelegate> getContainerDelegates() {
        return mContainerDelegates;
    }

    @Override
    public IContainerDelegate getTopContainer() {
        return mTopContainerDelegate;
    }

    @Override
    public boolean isAppForeground() {
        IContainerDelegate topContainerDelegate = mTopContainerDelegate;
        if (topContainerDelegate != null && !topContainerDelegate.isPaused()) {
            return true;
        }
        for (IContainerDelegate containerDelegate : mContainerDelegates) {
            if (containerDelegate != null && !containerDelegate.isPaused()) {
                return true;
            }
        }
        return false;
    }

    @Override
    public boolean isWidget(int pageId) {
        IContainerDelegate containerDelegate = getContainerDelegateByPageIdOrTopPage(pageId);
        if (containerDelegate != null && containerDelegate.isWidget()) {
            return true;
        }
        return false;
    }

    @Override
    public boolean isWidgetForeground() {
        IContainerDelegate topContainerDelegate = mTopContainerDelegate;
        if (topContainerDelegate != null && topContainerDelegate.isWidget() && !topContainerDelegate.isPaused()) {
            return true;
        }
        return false;
    }


    @Nullable
    @Override
    public IPageModule getTopPage() {
        IContainerDelegate topContainerDelegate = mTopContainerDelegate;
        return (topContainerDelegate != null
                && topContainerDelegate.getPageMangerModule() != null) ? topContainerDelegate.getPageMangerModule().getTopPage() : null;
    }

    @Nullable
    @Override
    public IPageManagerModule getTopPageManager() {
        IContainerDelegate topContainerDelegate = mTopContainerDelegate;
        return topContainerDelegate != null ? topContainerDelegate.getPageMangerModule() : null;
    }

    @Override
    @Nullable
    public IPageModule getPageByPageId(int id) {
        IPageManagerModule pageManagerModule = getPageManagerByPageId(id);
        if (pageManagerModule != null) {
            return pageManagerModule.getPage(id);
        }
        return null;
    }

    @Override
    @Nullable
    public IPageModule getPageByPageIdOrTopPage(int id) {
        IPageModule pageModule = null;
        IPageManagerModule pageManagerModule = getPageManagerByPageId(id);
        if (pageManagerModule != null) {
            pageModule = pageManagerModule.getPage(id);
        }
        if (pageModule != null) {
            return pageModule;
        } else {
            return getTopPage();
        }
    }

    @Override
    @Nullable
    public IPageManagerModule getPageManagerByPageId(int id) {
        IContainerDelegate containerDelegate = getContainerDelegateByPageId(id);
        if (containerDelegate != null) {
            return containerDelegate.getPageMangerModule();
        }
        return null;
    }

    @Override
    @Nullable
    public IContainerDelegate getContainerDelegateByPageId(int id) {
        if (id == 0 || id == View.NO_ID) {
            MSCLog.w("getContainerDelegateByPageId: id is invalid", id);
            return null;
        }
        IContainerDelegate container = null;
        if ((container = getTopContainer()) != null
                && container.getPageMangerModule() != null
                && container.getPageMangerModule().hasPage(id)) {
            return container;
        }
        IPageManagerModule pageManagerModule = null;
        for (IContainerDelegate containerDelegate : mContainerDelegates) {
            if ((pageManagerModule = containerDelegate.getPageMangerModule()) != null
                    && pageManagerModule.hasPage(id)) {
                // 如果当前无正在使用的栈顶容器 将最近使用的置为栈顶
                if (mTopContainerDelegate == null && containerDelegate.isFinishing()) {
                    focusTopContainer(containerDelegate, "getContainerDelegateByPageId");
                }
                return containerDelegate;
            }
        }
        MSCLog.w("getContainerDelegateByPageId: cannot find page by id", id);
        return null;
    }

    @Nullable
    @Override
    public IContainerDelegate getContainerDelegateByPageIdOrTopPage(int id) {
        IContainerDelegate iContainerDelegate = getContainerDelegateByPageId(id);
        if (iContainerDelegate == null) {
            return getTopContainer();
        }
        return iContainerDelegate;
    }

    @Override
    public void onContainerCreate(ContainerDelegate container) {
        if (getContainerCount() == 0) {
            rollbackEfficiencyRateTestCachedWhenLaunch = MSCHornRollbackConfig.readConfig().rollbackEfficiencyRateTest;
        }
        mTopContainerDelegate = container;
        retainCount();
        mContainerDelegates.add(container);
    }

    private void focusTopContainer(IContainerDelegate container, String reason) {
        mTopContainerDelegate = container;
        MSCLog.i(this.toString(), getRuntime(), "focusTopContainer", container, "reason", reason);
        if (container != null && !container.isWidget()) {
            MSCFusionActivityMonitor.onActivityResume(container.getActivity(), container.getContainerId());
        }
    }

    @Override
    public void onContainerResume(ContainerDelegate container) {
        focusTopContainer(container, "onContainerResume");
        getRuntime().publish(new MSCEvent<>(MSC_EVENT_CONTAINER_RESUMED, container));
    }

    @Override
    public void onWindowFocusChanged(ContainerDelegate container, boolean focus) {
        if (focus) {
            focusTopContainer(container, "onWindowFocusChanged");
        }
        getRuntime().publish(new MSCEvent<>(MSC_EVENT_WINDOW_FOCUS_CHANGE, focus));
    }


    @Override
    public void onContainerPause(ContainerDelegate container) {
        if (mTopContainerDelegate != null && mTopContainerDelegate == container) {
            getRuntime().publish(new MSCEvent<>(MSC_EVENT_CONTAINER_PAUSED, container));

            // pause 时 ，如果可以的话 去寻找一个 resume 的container 来当 topContainer;
            // 用于纠正 B onResume - A onResume - A onPause - A onStop 场景
            // 小程序前端页面栈为线性栈，栈顶实例不正确，可能导致路由跳转无法获取到正确的 页面栈进行页面操作 ；
            // （eg：navigateTo 、navigateBack 可能出现插入位置出现前端客户端顺序不一致）
            findNextResumedContainer("find next resumed container after current ContainerPaused");
        }
    }

    private void findNextResumedContainer(String reason) {
        for (IContainerDelegate containerDelegate : mContainerDelegates) {
            if (!containerDelegate.isPaused() && !containerDelegate.isFinishing()) {
                focusTopContainer(containerDelegate, reason);
                break;
            }
        }
    }

    @Override
    public void onContainerDestroy(ContainerDelegate container) {
        releaseCount(container);
        mContainerDelegates.remove(container);
        if (mTopContainerDelegate != null && mTopContainerDelegate == container) {
            getRuntime().publish(new MSCEvent<>(MSC_EVENT_CONTAINER_DESTROYED, container));
            focusTopContainer(null, "onContainerDestroy");
            findNextResumedContainer("find next resumed container after current ContainerDestroy ");
        }
        if (!MSCHornRollbackConfig.isRollbackCheckMSCRuntimeLeak()) {
            RuntimeMemoryLeakMonitor.refreshMonitorTimer(getRuntime());
            RuntimeMemoryLeakMonitor.checkAllMSCRuntimeLeaks();
        }
    }

    @MSCMethod
    public void pageNotFoundCallback() {
        IPageManagerModule pageManagerModule = getTopPageManager();
        if (pageManagerModule != null) {
            pageManagerModule.pageNotFoundCallback();
        }
    }

    private final AtomicInteger mRetainCount = new AtomicInteger(0);

    public int getContainerCount() {
        return mRetainCount.get();
    }

    public void retainCount() {
        int retainCount = mRetainCount.incrementAndGet();
        if (retainCount > 0) {
            getRuntime().active();
        }
    }

    /**
     * Container数量减到0时进入保活，保活结束之后销毁
     */
    public void releaseCount(ContainerDelegate controller) {
        int count = mRetainCount.get();
        MSCLog.i("ContainerManger", "releaseCount, current retainCount is" + count);
        if (count >= 1) {
            int retainCount = mRetainCount.decrementAndGet();
            MSCLog.i("ContainerManger", "releaseCount finish, current retainCount is" + retainCount);
            if (retainCount == 0) {
                // 多任务栈情况下由task存活控制保活，此处不控制，最后一个activity销毁时即销毁小程序实例
                getRuntime().deActive(true);
            }
        }
    }

    @Override
    public boolean onSubModuleNotFound(String moduleName, String methodName) {
        getRuntime().getNativeExceptionHandler().handleWarning(
                String.format("cannot find submodule with name '%s' in PageManger for method %s", moduleName, methodName));
        return true;
    }

    @Override
    public String getRollbackEfficiencyRateTestCachedWhenLaunch() {
        return rollbackEfficiencyRateTestCachedWhenLaunch;
    }
}
