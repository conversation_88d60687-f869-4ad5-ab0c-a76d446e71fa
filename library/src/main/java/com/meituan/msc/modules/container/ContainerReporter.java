package com.meituan.msc.modules.container;

import android.content.Context;
import android.os.SystemClock;
import android.text.TextUtils;

import com.meituan.msc.common.report.MetricsEntry;
import com.meituan.msc.common.utils.Constants;
import com.meituan.msc.common.utils.PathUtil;
import com.meituan.msc.lib.BuildConfig;
import com.meituan.msc.modules.apploader.events.AppLoadException;
import com.meituan.msc.modules.engine.MSCHornRollbackConfig;
import com.meituan.msc.modules.engine.MSCRuntime;
import com.meituan.msc.modules.engine.MSCRuntimeReporter;
import com.meituan.msc.modules.page.RouteReporter;
import com.meituan.msc.modules.page.render.AppPageReporter;
import com.meituan.msc.modules.reporter.MSCCommonTagReporter;
import com.meituan.msc.modules.reporter.CommonTags;
import com.meituan.msc.modules.reporter.MSCReporter;
import com.meituan.msc.modules.reporter.ReporterFields;
import com.meituan.msc.modules.reporter.memory.PageMemoryMonitor;
import com.meituan.msc.modules.reporter.preformance.InitRecorder;

public class ContainerReporter extends MSCCommonTagReporter {
    private IContainerDelegate containerDelegate;

    public static ContainerReporter create(IContainerDelegate containerDelegate, MSCRuntime runtime, Boolean isWidget, String pagePath, boolean isReLaunchOnError) {
        ContainerReporter reporter = new ContainerReporter(containerDelegate, CommonTags.build(runtime, null, pagePath, null, isWidget, null), isReLaunchOnError);
        reporter.appId = runtime.getAppId();
        return reporter;
    }

    private long launchStartTimeCurrentTimeMillis;
    private PageMemoryMonitor pageMemoryMonitorOfFirstPage;
    private boolean isReLaunchOnError;
    private String appId;

    private ContainerReporter(IContainerDelegate containerDelegate, CommonTags commonTags, boolean isReLaunchOnError) {
        super(commonTags);
        this.containerDelegate = containerDelegate;
        this.isReLaunchOnError = isReLaunchOnError;
    }

    public void onCreate() {
        pageMemoryMonitorOfFirstPage = new PageMemoryMonitor(appId);
        pageMemoryMonitorOfFirstPage.onCreate();
    }

    public PageMemoryMonitor pollPageMemoryMonitorOfFirstPage() {
        PageMemoryMonitor pageMemoryMonitor = pageMemoryMonitorOfFirstPage;
        pageMemoryMonitorOfFirstPage = null;
        return pageMemoryMonitor;
    }

    public void recordLaunchStartTimeCurrentTimeMillis(MSCRuntime runtime, long launchStartTimeCurrentTimeMillis) {
        this.launchStartTimeCurrentTimeMillis = launchStartTimeCurrentTimeMillis;
        long durationSinceProcessStart = InitRecorder.getDurationSinceApplicationStart();
        commonTag("launchFromProcessStart", durationSinceProcessStart);
        if (runtime.getBasePreloadTime() != 0) {
            commonTag("basePreloadFromProcessStart", runtime.getBasePreloadTime() - InitRecorder.getApplicationStartElapsedRealtime());
            commonTag("launchFromBasePreloadStart", launchStartTimeCurrentTimeMillis - runtime.getBasePreloadTime());
        }
    }

    public long getLaunchStartTimeCurrentTimeMillis() {
        return launchStartTimeCurrentTimeMillis;
    }

    public void onAppLoaderLoadFail(Context context, MSCRuntime runtime, int firstRouteId, AppLoadException exception) {
        if (runtime == null) {
            return;
        }
        // 如果没有 appId 就不上报
        if (TextUtils.isEmpty(runtime.getAppId())) {
            return;
        }
        AppPageReporter appPageReporter = getTopAppPageReporter(runtime);
        if (appPageReporter != null) {
            appPageReporter.onPageLoadFail(context, exception);
        } else {
            MetricsEntry entry = record(ReporterFields.REPORT_PAGE_LOAD_SUCCESS_RATE);
            if (isReLaunchOnError) {
                entry.tag(CommonTags.TAG_SOURCE_FROM, "reload");
            }
            ContainerLaunchErrorManager.addLaunchErrorCodesToEntry(entry, runtime);
            appendOutLinkMetrics(entry, containerDelegate);
            AppPageReporter.appendLaunchMoment(runtime, firstRouteId, entry.getTags());
            entry.tag("errorMessage", exception != null ? exception.getMessage() : null)
                    .tag("errorCode", MSCRuntimeReporter.getErrorCode(exception))
                    .value(0)
                    .sendRealTime();
        }
    }

    public void reportPageNotFound(String openType, String path) {
        MetricsEntry entry = record(ReporterFields.REPORT_PAGE_NOT_FOUND_ROUTE_COUNT);
        appendOutLinkMetrics(entry, containerDelegate);
        entry.tag(RouteReporter.ROUTE_MODE, openType)
                .tag(CommonTags.TAG_PAGE_PATH, path)
                .tag(CommonTags.TAG_PURE_PAGE_PATH, PathUtil.getPath(path))
                .sendDelay();
    }


    public void reportProcessTime(MSCRuntime runtime){
        long time = SystemClock.currentThreadTimeMillis();
        new MSCCommonTagReporter(CommonTags.build(runtime)).once(ReporterFields.MSC_BIKE_FIRST_TIME).
                value(time).sendDelay();
    }

    public void reportLaunchToAppRoute(String openType, long routeTime) {
        record(ReporterFields.REPORT_LAUNCH_TO_APP_ROUTE_COUNT)
                .tag("openType", openType)
                .tag("duration", System.currentTimeMillis() - routeTime)
                .sendDelay();
    }


    public static class CommonReporter extends MSCReporter {

        public static CommonReporter create() {
            return new CommonReporter();
        }

        private CommonReporter() {
            this.commonTag(CommonTags.SDK_VERSION, BuildConfig.AAR_VERSION);
        }

        public void reportContainerStayDuration(String appId, String targetPath, boolean isWidget, long containerCreateTimeMillis) {
            record(ReporterFields.REPORT_MSC_CONTAINER_STAY_DURATION)
                    .tag(CommonTags.TAG_MSC_APP_ID, appId)
                    .tag(CommonTags.TAG_PAGE_PATH, PathUtil.getPath(targetPath))
                    .tag(CommonTags.TAG_WIDGET, isWidget)
                    .value(System.currentTimeMillis() - containerCreateTimeMillis)
                    .sendRealTime();
        }
    }
}
