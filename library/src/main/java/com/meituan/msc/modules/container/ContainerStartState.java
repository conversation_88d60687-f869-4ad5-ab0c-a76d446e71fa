package com.meituan.msc.modules.container;

public class ContainerStartState {

    /**
     * 标记当前有页面冷启，起点为Controller.onCreate，终点为FP
     * 仅标记首个冷启动Activity
     */
    private volatile boolean isContainerLaunching = false;
    public static ContainerStartState instance = new ContainerStartState();

    public synchronized boolean isContainerLaunching() {
        return isContainerLaunching;
    }

    public synchronized void setIsContainerLaunching(boolean hasContainerLaunching) {
        this.isContainerLaunching = hasContainerLaunching;
    }
}
