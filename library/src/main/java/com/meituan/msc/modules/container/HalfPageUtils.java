package com.meituan.msc.modules.container;

import android.app.Activity;
import android.content.Context;
import android.content.Intent;
import android.view.View;

import com.meituan.msc.common.utils.DisplayUtil;
import com.meituan.msc.common.utils.IntentUtil;
import com.meituan.msc.common.utils.SystemInfoUtils;
import com.meituan.msc.lib.interfaces.container.MSCParams;
import com.meituan.msc.modules.page.transition.PageTransitionConfig;
import com.meituan.msc.modules.reporter.MSCLog;

/**
 * <AUTHOR>
 * @Date 2025/2/25 15:54
 * @Description:
 */
public class HalfPageUtils {
    private HalfPageUtils() {
    }

    public static int getValidHalfPageTopMargin(Context context, Intent intent) {
        int pageHeight = IntentUtil.getIntExtra(intent, MSCParams.MSC_HALF_PAGE_HEIGHT, 0);
        float pageHeightPercent = IntentUtil.getFloatExtra(intent, MSCParams.MSC_HALF_PAGE_HEIGHT_PERCENT, 0F);
        return getValidHalfPageSize(context, pageHeight, pageHeightPercent).topMargin;
    }

    public static int getValidHalfPageTopMargin(Context context, PageTransitionConfig config) {
        return getValidHalfPageSize(context, config != null ? config.pageHeight : 0, config != null ? config.pageHeightPercent : 0).topMargin;
    }

    public static PageSizeInfo getValidHalfPageSize(Context context, int pageHeight, float pageHeightPercent) {
        // 配置的是DP数值
        int configPageHeight = pageHeight;
        int windowHeight = 0;
        if (context instanceof Activity) {
            Activity activity = (Activity) context;
            View content = activity.findViewById(android.R.id.content);
            if (content != null && content.getHeight() > 0) {
                windowHeight = content.getHeight();
            }
        }
        if (windowHeight <= 0) {
            int screenHeight = DisplayUtil.getScreenHeightReal();
            int navigationBarHeight = SystemInfoUtils.getSystemNavigationBarHeight(context);
            windowHeight = screenHeight - navigationBarHeight;
        }
        // 最小高度为：页面高度30%，最大高度为：页面高度 - 状态栏-12
        int minHeight = (int) (windowHeight * 0.3);
        int maxHeight = windowHeight - DisplayUtil.getStatusBarHeight() - DisplayUtil.fromDPToPix(12);
        // 因为最小和最大不是1个标准，所以可能出现极限情况，边界倒置，所以需要进行调整。
        if (minHeight > maxHeight) {
            int temp = maxHeight;
            maxHeight = minHeight;
            minHeight = temp;
        }
        // 百分比优先级更高
        if (pageHeightPercent > 0) {
            pageHeight = (int) (windowHeight * pageHeightPercent);
        } else if (pageHeight > 0) {
            pageHeight = DisplayUtil.fromDPToPix(pageHeight);
        }
        if (pageHeight <= 0) {
            // 没有设置pageHeight和pageHeightPercent，兜底为现状高度（页面高度 - 51DP）。
            pageHeight = windowHeight - DisplayUtil.fromDPToPix(51);
        } else if (pageHeight > maxHeight) {
            // 超出最大高度，兜底为最大高度
            pageHeight = maxHeight;
        } else if (pageHeight < minHeight) {
            // 达不到最小高度，兜底为最小高度
            pageHeight = minHeight;
        }
        int topMargin = windowHeight - pageHeight;
        MSCLog.i("HalfPageUtils", "configPageHeight is:", configPageHeight, "configPageHeightPercent is:", pageHeightPercent, "windowHeight is:", windowHeight, "minHeight is:", minHeight, "maxHeight is:", maxHeight, "pageHeight is:", pageHeight, "topMargin is:", topMargin);
        return new PageSizeInfo(topMargin, pageHeight);
    }

    public static class PageSizeInfo {
        public int topMargin;
        public int pageHeight;

        public PageSizeInfo(int topMargin, int pageHeight) {
            this.topMargin = topMargin;
            this.pageHeight = pageHeight;
        }
    }
}
