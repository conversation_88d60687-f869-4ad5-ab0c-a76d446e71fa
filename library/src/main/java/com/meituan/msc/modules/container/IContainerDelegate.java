package com.meituan.msc.modules.container;


import android.app.Activity;
import android.arch.lifecycle.Lifecycle;
import android.content.Intent;
import android.os.Bundle;
import android.view.ViewGroup;

import com.meituan.msc.common.framework.interfaces.PageEventListener;
import com.meituan.msc.common.support.java.util.concurrent.CompletableFuture;
import com.meituan.msc.modules.api.input.KeyboardHeightObserver;
import com.meituan.msc.modules.page.BasePage;
import com.meituan.msc.modules.page.IPageManagerModule;
import com.meituan.msc.modules.page.PageManager;
import com.meituan.msc.modules.page.render.AppRouteParam;
import com.meituan.msi.bean.NavActivityInfo;

import java.util.Map;

/**
 * Created by letty on 2022/1/10.
 **/
public interface IContainerDelegate {

    boolean isWidget();

    void setActivity(Activity activity);

    Activity getActivity();

    IMSCContainer getMSCContainer();

    boolean isPaused();

    boolean isFinishing();

    IPageManagerModule getPageMangerModule();

    Intent getIntent();

    String getUri();

    void handleCloseContainer(String triggerSource);

    PageEventListener getPageEventListener();

    Lifecycle.State getLifecycleState();

    int getContainerId();

    void startActivityForResult(Intent intent, int requestCode);
    void startActivityForResult(Intent intent, int requestCode, Bundle options, NavActivityInfo navActivityInfo);

    void setResult(int resultCode, Intent data);

    //fixme msc remove below methods
    void clearRestorePageStack();

    void registerKeyboardListener(KeyboardHeightObserver keyboardHeightObserver);

    void unRegisterKeyboardListener(KeyboardHeightObserver keyboardHeightObserver);

    boolean hasFirstRender();

    void onLaunchError(String s, int code, Throwable e);

    @Deprecated
    PageManager getPageManager();

    void setBackFromExternalNativeUrl(String url);

    void onPageFirstScreen(long timestamp, int pageId);

    CompletableFuture<AppRouteParam> startPage(boolean fromKeepAlive, long routeTime, int routeId);

    boolean checkLaunchRoutedPath();

    void initLaunchStartTime();

    void setLeaveAppInfo(String leaveAppInfo);

    boolean isDisableReuseAny();

    boolean isLaunchOnError();

    boolean isTransparentContainer();

    ViewGroup.LayoutParams createPageLayoutParams(BasePage page);

    boolean isMSCInitedAtContainerOnCreate();

    void sendWidgetData();

    void setStartPageFuture(CompletableFuture<AppRouteParam> future);

    boolean isIgnoreRouteMapping(boolean external);

    boolean isAppRouteTaskEnabled(String openType);

    boolean isOutLinkColdLaunch();

    Map<String, Object> getOutLinkMap();

    int getRecreateType();
}
