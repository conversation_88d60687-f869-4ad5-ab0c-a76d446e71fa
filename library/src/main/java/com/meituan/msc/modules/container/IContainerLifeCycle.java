package com.meituan.msc.modules.container;

import android.os.Bundle;

/**
 * Created by letty on 2022/1/12.
 **/
public interface IContainerLifeCycle {

    void onCreate(Bundle savedInstanceState);

//    void onFragmentHiddenChanged(boolean isResumed, boolean isHidden);

    void onStart();

    void onResume();

    void onPause();

    void onStop();

    void onDestroy();

    void onWindowFocusChanged(boolean hasFocus);

    void onSaveInstanceState(Bundle outState);

}
