package com.meituan.msc.modules.container;

import android.app.Activity;
import android.support.annotation.NonNull;

import com.meituan.msc.modules.manager.IMSCModule;
import com.meituan.msc.modules.page.IPageManagerModule;
import com.meituan.msc.modules.page.IPageModule;

import java.util.List;

import javax.annotation.Nullable;


/**
 * 容器管理模块
 * Created by letty on 2022/1/12.
 **/
public interface IContainerManager extends IMSCModule {

    String MSC_EVENT_WINDOW_FOCUS_CHANGE = "msc_event_window_focus_change";
    String MSC_EVENT_CONTAINER_RESUMED = "msc_event_container_resumed";
    String MSC_EVENT_CONTAINER_PAUSED = "msc_event_container_paused";
    String MSC_EVENT_CONTAINER_DESTROYED = "msc_event_container_destroyed";

    @Nullable
    Activity getTopActivity();

    @NonNull
    List<IContainerDelegate> getContainerDelegates();

    @Nullable
    IContainerDelegate getTopContainer();

    boolean isAppForeground();

    boolean isWidget(int pageId);

    boolean isWidgetForeground();

    @Nullable
    IPageModule getTopPage();

    @Nullable
    IPageManagerModule getTopPageManager();

    @Nullable
    IPageModule getPageByPageId(int id);

    @Nullable
    IPageModule getPageByPageIdOrTopPage(int id);

    @Nullable
    IPageManagerModule getPageManagerByPageId(int id);

    @Nullable
    IContainerDelegate getContainerDelegateByPageId(int id);

    @Nullable
    IContainerDelegate getContainerDelegateByPageIdOrTopPage(int id);

    int getContainerCount();

    void onContainerCreate(ContainerDelegate container);

    void onContainerResume(ContainerDelegate container);

    void onWindowFocusChanged(ContainerDelegate container, boolean focus);

    void onContainerPause(ContainerDelegate container);

    void onContainerDestroy(ContainerDelegate container);

    String getRollbackEfficiencyRateTestCachedWhenLaunch();
}
