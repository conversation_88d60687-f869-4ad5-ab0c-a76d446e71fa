package com.meituan.msc.modules.container;

import android.app.Activity;
import android.arch.lifecycle.LifecycleOwner;
import android.content.Intent;
import android.os.Bundle;
import android.support.annotation.NonNull;
import android.support.annotation.Nullable;
import android.view.View;
import android.view.ViewGroup;
import android.view.Window;

import com.meituan.msc.modules.api.widget.WidgetPreCreateListener;

import java.util.Map;

public interface IMSCContainer extends LifecycleOwner {

    Intent getIntent();

    Activity getActivity();

    Window getWindow();

    void startActivityForResult(final Intent intent, final int requestCode, @Nullable final Bundle options);

    boolean handleBackPress();

    void invokeBackPress();

    <T extends View> T findViewById(int id);

    boolean isActivity();

    boolean needLoadingView();

    boolean needLoadingAppInfo();

    String getMPTargetPath();

    Intent getStartContainerActivityIntent(@NonNull String appId, @Nullable Bundle extras);

    View getRootView();

    boolean onLaunchError(String msg, int code, Throwable e);

    String getMPAppVersion();

    String getTopPagePath();

    Map<String, String> getTopPageBizTags();

    String getMPAppId();

    void updateAppProp();

    boolean isTransparentContainer();

    int getTopMarginAtTransparentContainer();

    void customErrorViewLayout(View errorView);

    void startPageContainerEnterAnimation(ViewGroup pageContainer);

    boolean isPreCreate();

    void setWidgetPreCreateListener(WidgetPreCreateListener widgetPreCreateListener);

    boolean isMSCInitedAtContainerOnCreate();
}
