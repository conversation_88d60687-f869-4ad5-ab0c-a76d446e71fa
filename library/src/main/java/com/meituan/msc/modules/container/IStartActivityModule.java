package com.meituan.msc.modules.container;

import android.content.Intent;
import android.os.Bundle;
import android.support.annotation.Nullable;

import com.meituan.msi.bean.NavActivityInfo;

/**
 * Created by letty on 2022/1/10.
 **/
public interface IStartActivityModule {
    void startActivityForResult(Intent intent, int requestCode, @Nullable Bundle options, NavActivityInfo navActivityInfo, @Nullable IActivityResultCallBack callback);

    void startActivityForResult(Intent intent, int requestCode, NavActivityInfo navActivityInfo, @Nullable IActivityResultCallBack callback);

    void startActivityForResult(Intent intent, int requestCode, @Nullable IActivityResultCallBack callback);

    void startActivity(Intent intent);

    void clearStartActivityCall(IContainerDelegate containerDelegate);

    void onActivityResult(IContainerDelegate containerDelegate, int requestCode, int resultCode, Intent data);

    interface IActivityResultCallBack {
        void onActivityResult(int requestCode, Intent intent);

        void onFail(int errCode, String errMsg);
    }

}
