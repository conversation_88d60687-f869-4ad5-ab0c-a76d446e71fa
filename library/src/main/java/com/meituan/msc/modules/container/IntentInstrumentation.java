package com.meituan.msc.modules.container;

import android.app.Activity;
import android.app.Application;
import android.app.Fragment;
import android.app.Instrumentation;
import android.content.Context;
import android.content.Intent;
import android.content.pm.ActivityInfo;
import android.content.pm.ResolveInfo;
import android.os.Bundle;
import android.os.IBinder;
import android.os.UserHandle;

import com.meituan.msc.modules.reporter.MSCLog;
import com.sankuai.meituan.arbiter.hook.MTInstrumentation;

import static android.content.pm.PackageManager.MATCH_DEFAULT_ONLY;
import static com.sankuai.meituan.arbiter.hook.Utils.debugExecTimeBegin;
import static com.sankuai.meituan.arbiter.hook.Utils.debugExecTimeEnd;

/**
 *
 * 通用的IntentInstrumentation抽象类
 *
 * 相比平台的IntentInstrumentation 主要修改了{@link IntentInstrumentation#newActivity(ClassLoader, String, Intent)}
 * 修正了无法正确跳转到新页面的问题
 *
 * 为规避处理两次的问题，目前仅处理newActivity（在目标进程，Activity被启动时），不处理execStartActivity（在调用进程，调用startActivity时）
 *
 * Created by letty on 2020-06-02.
 *
 **/

public abstract class IntentInstrumentation extends MTInstrumentation {
    private static final String TAG_INTENT_PROCESSED = "MSC_IntentInstrumentation_intent_processed";

    protected Context mContext;

    public IntentInstrumentation(Context context) {
        mContext = context;
    }

    @Override
    public Instrumentation.ActivityResult execStartActivity(Context who, IBinder contextThread, IBinder token, Activity target, Intent intent, int requestCode) {
        debugExecTimeBegin("IntentInstrumentation.execStartActivity");
        processStartActivity(who, intent);
        Instrumentation.ActivityResult result = super.execStartActivity(who, contextThread, token, target, intent, requestCode);
        debugExecTimeEnd("IntentInstrumentation.execStartActivity");
        return result;
    }

    @Override
    public Instrumentation.ActivityResult execStartActivity(Context who, IBinder contextThread, IBinder token, Fragment target, Intent intent, int requestCode) {
        debugExecTimeBegin("IntentInstrumentation.execStartActivity");
        processStartActivity(who, intent);
        Instrumentation.ActivityResult result = super.execStartActivity(who, contextThread, token, target, intent, requestCode);
        debugExecTimeEnd("IntentInstrumentation.execStartActivity");
        return result;
    }

    @Override
    public Instrumentation.ActivityResult execStartActivity(Context who, IBinder contextThread, IBinder token, String target, Intent intent, int requestCode, Bundle options) {
        debugExecTimeBegin("IntentInstrumentation.execStartActivity");
        processStartActivity(who, intent);
        Instrumentation.ActivityResult result = super.execStartActivity(who, contextThread, token, target, intent, requestCode, options);
        debugExecTimeEnd("IntentInstrumentation.execStartActivity");
        return result;
    }

    @Override
    public Instrumentation.ActivityResult execStartActivity(Context who, IBinder contextThread, IBinder token, Activity target, Intent intent, int requestCode, Bundle bundle) {
        debugExecTimeBegin("IntentInstrumentation.execStartActivity");
        processStartActivity(who, intent);
        Instrumentation.ActivityResult result = super.execStartActivity(who, contextThread, token, target, intent, requestCode, bundle);
        debugExecTimeEnd("IntentInstrumentation.execStartActivity");
        return result;
    }

    @Override
    public Instrumentation.ActivityResult execStartActivity(Context who, IBinder contextThread, IBinder token, Fragment target, Intent intent, int requestCode, Bundle options) {
        debugExecTimeBegin("IntentInstrumentation.execStartActivity");
        processStartActivity(who, intent);
        Instrumentation.ActivityResult result = super.execStartActivity(who, contextThread, token, target, intent, requestCode, options);
        debugExecTimeEnd("IntentInstrumentation.execStartActivity");
        return result;
    }

    @Override
    public Instrumentation.ActivityResult execStartActivity(Context who, IBinder contextThread, IBinder token, Activity target, Intent intent, int requestCode, Bundle options, UserHandle user) {
        debugExecTimeBegin("IntentInstrumentation.execStartActivity");
        processStartActivity(who, intent);
        Instrumentation.ActivityResult result = super.execStartActivity(who, contextThread, token, target, intent, requestCode, options, user);
        debugExecTimeEnd("IntentInstrumentation.execStartActivity");
        return result;
    }

    @Override
    public Instrumentation.ActivityResult execStartActivity(Context who, IBinder contextThread, IBinder token, String target, Intent intent, int requestCode, Bundle options, UserHandle user) {
        debugExecTimeBegin("IntentInstrumentation.execStartActivity");
        processStartActivity(who, intent);
        Instrumentation.ActivityResult result = super.execStartActivity(who, contextThread, token, target, intent, requestCode, options, user);
        debugExecTimeEnd("IntentInstrumentation.execStartActivity");
        return result;
    }

    /**
     * 相比平台的IntentInstrumentation 主要修改了这里
     *
     * 修正了无法正确跳转到新页面的问题
     *
     * 修改了className
     *
     * @param cl
     * @param className
     * @param intent
     * @return
     * @throws InstantiationException
     * @throws IllegalAccessException
     * @throws ClassNotFoundException
     */
    @Override
    public Activity newActivity(ClassLoader cl, String className, Intent intent) throws InstantiationException, IllegalAccessException, ClassNotFoundException {
        debugExecTimeBegin("IntentInstrumentation.newActivity");
        intent.setExtrasClassLoader(cl);
        boolean processed = processNewActivity(mContext, intent);
        String realClassName = className;
        if (processed) {
            if (intent.getComponent() != null) {
                realClassName = intent.getComponent().getClassName();
            } else {
                ResolveInfo resolveInfo = mContext.getPackageManager().resolveActivity(intent, MATCH_DEFAULT_ONLY);
                if (resolveInfo != null && resolveInfo.activityInfo != null) {
                    realClassName = resolveInfo.activityInfo.name;
                }
            }
        }
        Activity activity = super.newActivity(cl, realClassName, intent);
        debugExecTimeEnd("IntentInstrumentation.newActivity");
        return activity;
    }

    @Override
    public Activity newActivity(Class<?> clazz, Context context, IBinder token, Application application, Intent intent, ActivityInfo info, CharSequence title, Activity parent, String id, Object lastNonConfigurationInstance) throws InstantiationException, IllegalAccessException {
        debugExecTimeBegin("IntentInstrumentation.newActivity");
        intent.setExtrasClassLoader(clazz.getClassLoader());
        processNewActivity(context, intent);
        Activity activity = super.newActivity(clazz, context, token, application, intent, info, title, parent, id, lastNonConfigurationInstance);
        debugExecTimeEnd("IntentInstrumentation.newActivity");
        return activity;
    }

    @Override
    public void callActivityOnPause(Activity activity) {
        processActivityOnPause(activity);
        super.callActivityOnPause(activity);
    }

    @Override
    public void callActivityOnCreate(Activity activity, Bundle icicle) {
        processActivityOnCreate(activity, icicle);
        super.callActivityOnCreate(activity, icicle);
    }

    @Override
    public void callActivityOnNewIntent(Activity activity, Intent intent) {
        if (null != intent) {
            intent.removeExtra(TAG_INTENT_PROCESSED);
        }
        super.callActivityOnNewIntent(activity, intent);
    }

    protected boolean isIntentProcessed(Intent intent) {
        return intent.hasExtra(TAG_INTENT_PROCESSED);
    }

    private boolean processStartActivity(Context context, Intent intent) {
        return processIntentInternal(context, intent, true);
    }

    private boolean processNewActivity(Context context, Intent intent) {
        return processIntentInternal(context, intent, false);
    }

    /**
     * @param isStartActivity 调用来自startActivity，或newActivity
     *                        注意，如果已经到了newActivity阶段，已无法通过改变flag来操纵启动目标，只能改intent里的数据了
     */
    private boolean processIntentInternal(Context context, Intent intent, boolean isStartActivity) {
        debugExecTimeBegin("IntentInstrumentation.processIntent");
        boolean isProcessed = false;
        try {
            if (!intent.hasExtra(TAG_INTENT_PROCESSED)) {
                isProcessed = processIntent(context, intent, isStartActivity);
                if (isProcessed) {
                    intent.putExtra(TAG_INTENT_PROCESSED, true);
                }
            } else {
                intent.removeExtra(TAG_INTENT_PROCESSED);
                isProcessed = true;
            }
        } catch (Exception e){
            MSCLog.e("IntentInstrumentation",e);
//            ignore for java.lang.RuntimeException: Parcel android.os.Parcel@b8c8417: Unmarshalling unknown type code 6619245 at offset 1624
        }
        debugExecTimeEnd("IntentInstrumentation.processIntent");
        return isProcessed;
    }

    /**
     * 所有改动必须 在原有intent基础上修改
     * 原因：newActivity 不支持更换新的intent
     *
     * @param context
     * @param originalIntent
     * @return 是否已处理
     */
    public abstract boolean processIntent(Context context, Intent originalIntent, boolean isStartActivity);

    public boolean processActivityOnPause(Activity activity) {
        return false;
    }

    public boolean processActivityOnCreate(Activity activity, Bundle icicle) {
        return false;
    }
}
