package com.meituan.msc.modules.container;

import android.arch.lifecycle.Lifecycle;
import android.arch.lifecycle.LifecycleOwner;
import android.arch.lifecycle.LifecycleRegistry;
import android.os.Bundle;
import android.support.annotation.NonNull;
import android.support.annotation.Nullable;
import android.support.v4.app.Fragment;
import android.view.LayoutInflater;
import android.view.View;
import android.view.ViewGroup;

import com.meituan.msc.modules.reporter.MSCLog;

import java.lang.reflect.InvocationTargetException;
import java.lang.reflect.Method;

/**
 * 因美团support包升级缓慢，supportV4的Fragment不支持lifecycle，自行外挂实现
 * support版本升级至27后可移除本类
 */
public abstract class LifecycleFragment extends LazyFragment implements LifecycleOwner {

    private static final boolean enableLifecycleLog = false;

    private static boolean isLifecycleSupportedBySuper;
    static {
        try {
            LifecycleFragment.class.getSuperclass().getMethod("getLifecycle");
            isLifecycleSupportedBySuper = true;
        } catch (NoSuchMethodException e) {
            MSCLog.i("LifecycleFragment", "Lifecycle not supported by current supportV4 Fragment, use alternative impl");
            isLifecycleSupportedBySuper = false;
        }
    }

    @Nullable
    private final LifecycleRegistry mMMPLifecycleRegistry;
    {
        // 注意高版本support库中FragmentActivity也会依赖Fragment中的Lifecycle
        // 如果super已实现Lifecycle，不可改变其行为，所以此时不创建自己的LifecycleRegistry
        if (isLifecycleSupportedBySuper) {
            mMMPLifecycleRegistry = null;
        } else {
            mMMPLifecycleRegistry = new LifecycleRegistry(this);
        }
    }


    @Override
    public void onHiddenChanged(boolean hidden) {
        super.onHiddenChanged(hidden);
        logLifecycle("onHiddenChanged" + hidden);
    }

    @Nullable
    @Override
    public View onCreateView(@NonNull LayoutInflater inflater, @Nullable ViewGroup container, @Nullable Bundle savedInstanceState) {
        logLifecycle("onCreateView");
        return super.onCreateView(inflater, container, savedInstanceState);
    }

    @Override
    public void onDestroyView() {
        logLifecycle("onDestroyView");
        super.onDestroyView();
    }

    @Override
    public void onActivityCreated(@Nullable Bundle savedInstanceState) {
        super.onActivityCreated(savedInstanceState);
        logLifecycle("onActivityCreated");
    }

    @Override
    public void onCreate(Bundle savedInstanceState) {
        super.onCreate(savedInstanceState);
        logLifecycle("onCreate");
        if (mMMPLifecycleRegistry != null) {    //如果super已实现Lifecycle，不使用自行实现的版本，防止重复设置
            mMMPLifecycleRegistry.handleLifecycleEvent(Lifecycle.Event.ON_CREATE);
        }
    }

    @Override
    public void onStart() {
        super.onStart();
        logLifecycle("onStart");
        if (mMMPLifecycleRegistry != null) {
            mMMPLifecycleRegistry.handleLifecycleEvent(Lifecycle.Event.ON_START);
        }
    }

    @Override
    public void onResume() {
        super.onResume();
        logLifecycle("onResume");
        if (mMMPLifecycleRegistry != null) {
            mMMPLifecycleRegistry.handleLifecycleEvent(Lifecycle.Event.ON_RESUME);
        }
    }

    @Override
    public void onPause() {
        if (mMMPLifecycleRegistry != null) {
            mMMPLifecycleRegistry.handleLifecycleEvent(Lifecycle.Event.ON_PAUSE);
        }
        logLifecycle("onPause");
        super.onPause();
    }

    @Override
    public void onStop() {
        logLifecycle("onStop");
        if (mMMPLifecycleRegistry != null) {
            mMMPLifecycleRegistry.handleLifecycleEvent(Lifecycle.Event.ON_STOP);
        }
        super.onStop();
    }

    @Override
    public void onDestroy() {
        logLifecycle("onDestroy");
        if (mMMPLifecycleRegistry != null) {
            mMMPLifecycleRegistry.handleLifecycleEvent(Lifecycle.Event.ON_DESTROY);
        }
        super.onDestroy();
    }

    @NonNull
    @Override
    public Lifecycle getLifecycle() {
        if (mMMPLifecycleRegistry != null) {
            return mMMPLifecycleRegistry;
        } else {
            Lifecycle lifecycle = null;
            try {
                Method method = LifecycleFragment.class.getSuperclass().getMethod("getLifecycle");
                lifecycle = (Lifecycle) method.invoke(this, (Class<?>) null);
            } catch (NoSuchMethodException e) {
                e.printStackTrace();
            } catch (IllegalAccessException e) {
                e.printStackTrace();
            } catch (InvocationTargetException e) {
                e.printStackTrace();
            }
            return lifecycle;
        }
    }

    private void logLifecycle(String message) {
        if (enableLifecycleLog) {
            MSCLog.i("Lifecycle", message, " ", getClass().getSimpleName(), "@", Integer.toHexString(hashCode()));
        }
    }
}
