package com.meituan.msc.modules.container;

import static com.meituan.msc.lib.interfaces.container.MSCParams.APP_ID;
import static com.meituan.msc.lib.interfaces.container.MSCParams.TARGET_PATH;
import static com.meituan.msc.modules.metrics.ScrollFPSRecorder.FPS_SCROLL;

import android.annotation.SuppressLint;
import android.app.Activity;
import android.content.BroadcastReceiver;
import android.content.ComponentName;
import android.content.Context;
import android.content.Intent;
import android.content.res.Configuration;
import android.content.res.Resources;
import android.net.Uri;
import android.os.Build;
import android.os.Bundle;
import android.os.SystemClock;
import android.support.annotation.Keep;
import android.support.annotation.NonNull;
import android.support.annotation.Nullable;
import android.text.TextUtils;
import android.transition.Fade;
import android.view.View;
import android.view.ViewGroup;
import android.view.Window;

import com.google.gson.JsonArray;
import com.google.gson.JsonObject;
import com.meituan.android.common.statistics.Statistics;
import com.meituan.android.common.statistics.utils.AppUtil;
import com.meituan.android.common.weaver.interfaces.ffp.FFPTags;
import com.meituan.android.techstack.CIPDisplayTechType;
import com.meituan.android.techstack.ICIPDynamicContentProtocol;
import com.meituan.metrics.MetricsNameProvider;
import com.meituan.metrics.MetricsTagsProvider;
import com.meituan.metrics.MetricsTechStackProvider;
import com.meituan.metrics.TechStack;
import com.meituan.metrics.sampler.fps.ScrollFpsEventListener;
import com.meituan.msc.common.config.MSCConfig;
import com.meituan.msc.common.framework.MPListenerManager;
import com.meituan.msc.common.lib.ISetLaunchRefer;
import com.meituan.msc.common.perf.PerfEventConstant;
import com.meituan.msc.common.process.MSCProcess;
import com.meituan.msc.common.utils.ActivityUtils;
import com.meituan.msc.common.utils.Constants;
import com.meituan.msc.common.utils.IntentUtil;
import com.meituan.msc.common.utils.MSCTraceUtil;
import com.meituan.msc.common.utils.PathUtil;
import com.meituan.msc.common.utils.StatusBarUtils;
import com.meituan.msc.extern.MSCEnvHelper;
import com.meituan.msc.extern.MSCLifecycleCallback;
import com.meituan.msc.lib.R;
import com.meituan.msc.lib.interfaces.container.MSCParams;
import com.meituan.msc.modules.api.msi.navigation.BizNavigationExtraParams;
import com.meituan.msc.modules.api.widget.WidgetPreCreateListener;
import com.meituan.msc.modules.container.fusion.MSCFusionActivityMonitor;
import com.meituan.msc.modules.engine.MSCHornRollbackConfig;
import com.meituan.msc.modules.engine.MSCRuntime;
import com.meituan.msc.modules.engine.MSCRuntimeReporter;
import com.meituan.msc.modules.engine.RuntimeManager;
import com.meituan.msc.modules.metrics.ScrollFPSRecorder;
import com.meituan.msc.modules.page.IPageManagerModule;
import com.meituan.msc.modules.page.IPageModule;
import com.meituan.msc.modules.page.RouteReporter;
import com.meituan.msc.modules.page.UserReporter;
import com.meituan.msc.modules.page.render.AppPageReporter;
import com.meituan.msc.modules.page.render.MSCFFPReportListener;
import com.meituan.msc.modules.page.render.MSCHornPerfConfig;
import com.meituan.msc.modules.page.render.RendererType;
import com.meituan.msc.modules.page.render.ReusableRenderer;
import com.meituan.msc.modules.page.render.webview.WebViewCacheManager;
import com.meituan.msc.modules.page.transition.PageTransitionConfig;
import com.meituan.msc.modules.page.view.PageViewWrapper;
import com.meituan.msc.modules.reporter.CommonTags;
import com.meituan.msc.modules.reporter.MSCLoadErrorConstants;
import com.meituan.msc.modules.reporter.MSCLog;
import com.meituan.msc.modules.reporter.ReporterFields;
import com.meituan.msc.modules.reporter.memory.MemoryMonitor;
import com.meituan.msc.modules.router.MMPRouterManager;
import com.meituan.msc.util.perf.PerfEvent;
import com.meituan.msc.util.perf.PerfEventPhase;
import com.meituan.msc.util.perf.PerfEventRecorder;
import com.meituan.msc.util.perf.PerfTrace;
import com.meituan.msi.privacy.permission.MsiPermissionGuard;

import java.util.ArrayList;
import java.util.Collection;
import java.util.Collections;
import java.util.HashMap;
import java.util.List;
import java.util.Map;

/**
 * 美团小程序页面
 */
@SuppressLint("GoogleAppIndexingApiWarning")
public class MSCActivity extends MSCBaseActivity implements IMSCContainer, FFPTags, MetricsNameProvider,
        MetricsTechStackProvider, MetricsTagsProvider, ScrollFpsEventListener, ISetLaunchRefer, ICIPDynamicContentProtocol {

    public static final String EXTRA_LAUNCH_PID = "pid";
    public static final String EXTRA_INTENT_SEND_TIME = "intentSendTime";
    public static final String EXTRA_PUSH_STYLE = "pushStyle";

    private final String[] needResetActivityThemeBrandsLocal = new String[]{"SCH-I959"};
    protected static final String TAG = "MSCActivity";

    public static final String FINISH_AND_START = "finishAndStart"; // 本Activity不是要启动的activity，但在Instrumentation中已无法阻止启动，需要关闭重开
    public static final String EXTRA_FINISH_AND_START_DONE = "finishAndStartDone";    //标识已经经过一次finish，避免某些极端情况造成循环，例如Instrumentation修改flag后又被修改
    public static final String RELAUNCH = "relaunch";
    public static final String SWITCH_TAB = "switchTab";
    public static final String FINISH_BY_EXIT_MINIPROGRAM = "finishByExitMiniProgram";

    public static final String PAGE_SAVE_TIMESTAMP = "pageSaveTimestamp";
    public static final String PAGE_SAVE_MEMORY = "pageSaveMemory";
    public static final String RUNTIME_KEEP_ALIVE_APP_SIZE = "runtimeKeepAliveAppSize";
    public static final String RUNTIME_ALL_APP_SIZE = "runtimeAllAppSize";
    public static final String RUNTIME_ALL_MESSAGE = "runtimeAllMessage";
    public static final String IS_DUMPED = "isDumped";

    protected ContainerController mController = new ContainerController(this);

    protected boolean quitBeforeLaunch; // 未完成初始化即在onCreate中退出

    protected String appId;

    private final List<PerfEvent> cachedEvents = new ArrayList<>();
    private long containerCreateTimeMillis;
    private boolean isMSCInitedAtActivityOnCreate;

    private static boolean hasLaunched = false;

    private void insertCachedEventsIfNeeded(PerfEventRecorder perfEventRecorder) {
        if (cachedEvents.size() <= 0) {
            return;
        }
        for (PerfEvent event : cachedEvents) {
            perfEventRecorder.addEvent(event);
        }
        cachedEvents.clear();
    }

    public void beginDurableEvent(String name) {
        MSCRuntime runtime = mController.getRuntime();
        if (runtime != null) {
            PerfEventRecorder perfEventRecorder = runtime.getPerfEventRecorder();
            insertCachedEventsIfNeeded(perfEventRecorder);
            perfEventRecorder.beginDurableEvent(name);
        } else {
            PerfEvent event = new PerfEvent(name, PerfEventPhase.BEGIN);
            cachedEvents.add(event);
        }
    }

    public void endDurableEvent(String name) {
        MSCRuntime runtime = mController.getRuntime();
        if (runtime != null) {
            PerfEventRecorder perfEventRecorder = runtime.getPerfEventRecorder();
            insertCachedEventsIfNeeded(perfEventRecorder);
            perfEventRecorder.endDurableEvent(name);
        } else {
            PerfEvent event = new PerfEvent(name, PerfEventPhase.END);
            cachedEvents.add(event);
        }
    }

    public PerfEventRecorder getPerfEventRecorder() {
        MSCRuntime runtime = mController.getRuntime();
        if (runtime != null) {
            return runtime.getPerfEventRecorder();
        }
        return null;
    }

    public ContainerController getContainerController() {
        return mController;
    }

    @Override
    public Activity getActivity() {
        return this;
    }

    @Override
    public boolean isActivity() {
        return true;
    }

    /**
     * 宿主可覆盖以提供appId
     * 优先级：宿主覆盖 -> Intent解析 -> 全局默认
     */
    @Override
    public String getMPAppId() {
        return mController.defaultGetMPAppId();
    }

    /**
     * 宿主可覆盖以提供appVersion
     *
     * @return
     */
    @Nullable
    @Override
    public String getMPAppVersion() {
        return mController.defaultGetMPAppVersion();
    }

    @SuppressLint("ObsoleteSdkInt")
    @Override
    protected void onCreate(Bundle savedInstanceState) {
        long routeTime = System.currentTimeMillis();
        if (MSCHornPerfConfig.getInstance().enableFPUsePageStartTime()) {
            routeTime = getIntent().getLongExtra(MSCParams.ROUTE_TIME, routeTime);
            getIntent().removeExtra(MSCParams.ROUTE_TIME);
        }
        containerCreateTimeMillis = routeTime;
        // 启动起始时间，用户启动相关指标计算，勿动
        mController.initLaunchStartTime();
        String routeId = getIntent().getStringExtra(MSCParams.MSC_INNER_ROUTE_ID);
        boolean disablePrefetch = IntentUtil.getBooleanExtra(getIntent(), MSCParams.DISABLE_PREFETCH, false);
        BizNavigationExtraParams bizNavigationExtraParams = new BizNavigationExtraParams.Builder().setDisablePrefetch(disablePrefetch).build();
        if (!TextUtils.isEmpty(routeId)) {
            mController.setRouteId(Integer.parseInt(routeId));
        }

        PerfTrace.instant(PerfEventConstant.PERF_SESSION_START);
        PerfTrace.begin(PerfEventConstant.LAUNCH);
        beginDurableEvent(PerfEventConstant.CONTAINER_CREATE);
        PerfTrace.online().begin(PerfEventConstant.CONTAINER_CREATE_FOR_FFP).report();

        isMSCInitedAtActivityOnCreate = MSCEnvHelper.isInited();
        MSCEnvHelper.onMSCContainerCreate(getActivity());
        boolean isMMPToMSCIntercept = false;
        if (MSCHornRollbackConfig.enableFixMMPToMSCIntercept()) {
            isMMPToMSCIntercept = checkAndRouteMMP();
        }

        appId = getMPAppId();
        boolean isUrlExternalApp = IntentUtil.getBooleanExtra(getIntent(), "externalApp", false);
        mController.setIsUrlExternalApp(isUrlExternalApp);
        mController.setAppId(appId);
        boolean fromMiniProgramApi = IntentUtil.getBooleanExtra(getIntent(),
                ContainerController.START_FROM_MIN_PROGRAM, false);
        String startScene = fromMiniProgramApi ? UserReporter.CONSTANT_NAVIGATE_TO_MINI_PROGRAM :
                UserReporter.CONSTANT_PORTAL;
        Uri uri = getIntent().getData();
        int uriLength = uri == null ? -1 : uri.toString().length();
        int targetPathLength = getMPTargetPath() == null ? -1 : getMPTargetPath().length();
        UserReporter.create().reportUserLaunchStart(appId, false, getMPTargetPath(), startScene, isMMPToMSCIntercept, uriLength, -1, targetPathLength);
        MSCEnvHelper.ensureFullInited();
        handlePushTransition();
        MPListenerManager.getInstance().launchEventListener.onEvent("native_init_begin");

        if (Build.VERSION.SDK_INT >= Build.VERSION_CODES.LOLLIPOP) {
            getWindow().requestFeature(Window.FEATURE_CONTENT_TRANSITIONS);
        } else {
            // 解决替换为FragmentActivity后，5.0以下版本显示标题栏的问题（优选）
            requestWindowFeature(Window.FEATURE_NO_TITLE);
        }
        checkIsRecreate(savedInstanceState);

        // 容器启动时的上报路由
        if (!MSCHornRollbackConfig.readConfig().rollbackReportRouteStartAtContainerCreate && !mController.isRecreate) {
            RouteReporter.CommonReporter.create().reportRouteStart(this, appId, "appLaunch",
                    mController.getMPTargetPath(), false);
        }

        mController.onCreate(savedInstanceState);
        // mi10 耗时5ms，暂时在闪购商家页不执行
        if ((MSCHornRollbackConfig.get().getConfig().isRollbackWindowDecorViewChange || notSGStorePage())
                && MSCHornRollbackConfig.get().getConfig().enableWindowDecorViewChange) {
            //试试能不能解决
            try {
                getWindow().getDecorView();
//            https://issuetracker.google.com/issues/37095334?pli=1#comment2
            } catch (Exception e) {
                //ignore
                MSCLog.e(e);
            }
        }
        super.onCreate(savedInstanceState); //1ms

        // fixme msc 移除mmp发广播通知native的逻辑 2ms
//        MiniProgramUtil.notifyMMPOnCreateToNative(getActivity(), appId);
        checkAndFinishIfNeeded();
        if (quitBeforeLaunch) {
            // 未初始化完成即退出，后续处理中注意各变量可能为null
            MSCLifecycleCallback.getInstance().onBackPressedByUser(appId, getIntent());
            return;
        }
        // 至此已确定会启动，不会退出，可进行初始化

        mController.createRuntime(savedInstanceState);
        // 在setContentView之前初始化引擎并启动任务，低端机可通过提前创建Page消除onResume后的无用绘制
        boolean enableLaunchTaskOnRoute = MSCHornRollbackConfig.enableLaunchTaskOnRoute();
        if (enableLaunchTaskOnRoute) { //启动任务执行快，Controller未完成初始化，onAppRoute事件有NPE
            mController.onInitContainerController(savedInstanceState, routeTime);
        }

        mController.onLaunchParamsCheckFinished(savedInstanceState, routeTime, bizNavigationExtraParams);

        getWindow().setEnterTransition(new Fade());

        // setContentView 6ms
        setContentView(mController.getLayout());
        mController.onActivityCreated(savedInstanceState, routeTime, bizNavigationExtraParams);//11ms

        StatusBarUtils.compatStatusBar(this);
        StatusBarUtils.setStatusBarTextColor(this, true);

        if (savedInstanceState == null) {
            checkReuseActivity(false);
        }

        // keep this line at the end of this method
        endDurableEvent(PerfEventConstant.CONTAINER_CREATE);

        reportActivityRecreateRate(savedInstanceState);
        PerfTrace.online().end(PerfEventConstant.CONTAINER_CREATE_FOR_FFP).report();
    }

    private boolean checkAndRouteMMP() {
        Uri uri = getIntent().getData();
        if (uri == null || !uri.isHierarchical()) {
            MSCLog.e(TAG, "checkAndRouteMMP failed, uri illegal");
            return false;
        }
        String path = uri.getPath();
        if (path == null || TextUtils.isEmpty(path) || path.contains("msc")) {
            MSCLog.e(TAG, "checkAndRouteMMP failed, uri path illegal or is msc");
            return false;
        }
        String appId = uri.getQueryParameter("appId");
        String targetAppId = MMPRouterManager.getMSCAppIdRouteByMMP(appId);
        if (targetAppId == null) {
            MSCLog.e(TAG, "checkAndRouteMMP failed, cannot find msc app id");
            return false;
        }
        MSCLog.i(TAG, "checkAndRouteMMP success");
        final Uri processedUri = Uri.parse(uri.toString().replace("appId=" + appId, "appId=" + targetAppId));
        //change mmp path into msc path
        getIntent().setData(processedUri.buildUpon().path("msc").build());
        return true;
    }

    private boolean notSGStorePage() {
        return !TextUtils.equals(getMPAppId(), Constants.APP_ID_SHAN_GOU)
                || !TextUtils.equals(getMPTargetPath(), Constants.SG_STORE_PAGE_PATH);
    }

    private void reportActivityRecreateRate(Bundle savedInstanceState) {
        MSCRuntimeReporter reporter;
        if (mController != null && mController.mRuntime != null && (reporter = mController.mRuntime.getRuntimeReporter()) != null) {
            boolean recreate = savedInstanceState != null;
            final long saveToCreateInterval;
            if (savedInstanceState != null && savedInstanceState.get(PAGE_SAVE_TIMESTAMP) != null) {
                saveToCreateInterval = System.currentTimeMillis() - savedInstanceState.getLong(PAGE_SAVE_TIMESTAMP);
                savedInstanceState.remove(PAGE_SAVE_TIMESTAMP);
            } else {
                saveToCreateInterval = 0L;
            }
            String targetPath = getMPTargetPath();
            if (TextUtils.isEmpty(targetPath)
                    && mController.mRuntime.getMSCAppModule() != null
                    && mController.mRuntime.getMSCAppModule().hasMetaInfo()
                    && mController.mRuntime.getAppConfigModule().hasConfig()) {
                targetPath = mController.mRuntime.getMSCAppModule().getRootPath();
            }
            final String finalTargetPath = targetPath != null ? targetPath : "";
            final int onSaveMemory;
            if (savedInstanceState != null && savedInstanceState.get(PAGE_SAVE_MEMORY) != null) {
                onSaveMemory = savedInstanceState.getInt(PAGE_SAVE_MEMORY);
                savedInstanceState.remove(PAGE_SAVE_MEMORY);
            } else {
                onSaveMemory = 0;
            }
            final int onSaveRuntimeKeepAliveAppSize;
            if (savedInstanceState != null && savedInstanceState.get(RUNTIME_KEEP_ALIVE_APP_SIZE) != null) {
                onSaveRuntimeKeepAliveAppSize = savedInstanceState.getInt(RUNTIME_KEEP_ALIVE_APP_SIZE);
                savedInstanceState.remove(RUNTIME_KEEP_ALIVE_APP_SIZE);
            } else {
                onSaveRuntimeKeepAliveAppSize = 0;
            }
            final int onSaveRuntimeAllAppSize;
            if (savedInstanceState != null && savedInstanceState.get(RUNTIME_ALL_APP_SIZE) != null) {
                onSaveRuntimeAllAppSize = savedInstanceState.getInt(RUNTIME_ALL_APP_SIZE);
                savedInstanceState.remove(RUNTIME_ALL_APP_SIZE);
            } else {
                onSaveRuntimeAllAppSize = 0;
            }
            final String onSaveRuntimeAllMessage;
            if (savedInstanceState != null && savedInstanceState.get(RUNTIME_ALL_MESSAGE) != null) {
                onSaveRuntimeAllMessage = savedInstanceState.getString(RUNTIME_ALL_MESSAGE);
                savedInstanceState.remove(RUNTIME_ALL_MESSAGE);
            } else {
                onSaveRuntimeAllMessage = "";
            }
            final boolean isDumped;
            if (savedInstanceState != null && savedInstanceState.get(IS_DUMPED) != null) {
                isDumped = savedInstanceState.getBoolean(IS_DUMPED);
                savedInstanceState.remove(IS_DUMPED);
            } else {
                isDumped = false;
            }
            MemoryMonitor.getInstance().getMemoryInMbOfCurrentProcessAsync().thenAccept(integer -> {
                reporter.record(ReporterFields.REPORT_ACTIVITY_RECREATE_RATE).value(recreate ? 1 : 0)
                        .tag("memory", integer)
                        .tag("onSaveMemory", onSaveMemory)
                        .tag("onSaveRuntimeKeepAliveAppSize", onSaveRuntimeKeepAliveAppSize)
                        .tag("onSaveRuntimeAllAppSize", onSaveRuntimeAllAppSize)
                        .tag("onSaveRuntimeAllMessage", onSaveRuntimeAllMessage)
                        .tag("saveToCreateInterval", saveToCreateInterval)
                        .tag("isDumped", isDumped)
                        .tag(CommonTags.TAG_MSC_APP_ID, getMPAppId())
                        .tag(CommonTags.TAG_PAGE_PATH, finalTargetPath)
                        .sendDelay();
            });
        }
    }

    /**
     * 检查是否flag复用Activity
     *
     * @param hasUsed
     */
    private void checkReuseActivity(boolean hasUsed) {
        // 应该复用Activity 但实际创建了新的Activity，用于统计
        if (IntentUtil.getBooleanExtra(getIntent(), MSCParams.REUSE_ACTIVITY, false)) {
            MSCRuntimeReporter reporter;
            if (mController != null && mController.mRuntime != null && (reporter = mController.mRuntime.getRuntimeReporter()) != null) {
                reporter.once(ReporterFields.REPORT_START_ACTIVITY_REUSE).value(hasUsed ? 1 : 0)
                        .lazyTag("calledByMSCActivity", ActivityUtils.getCalledByMSCActivity(this)).sendDelay();
            }
        }
    }

    private void handlePushTransition() {
        int pushStyle = IntentUtil.getIntExtra(getIntent(), MSCParams.PUSH_STYLE, 0);
        MSCLog.i(TAG, "handlePushTransition", pushStyle);
        if (pushStyle == PageTransitionConfig.TransitionStyle.FADE_IN_FADE_OUT) {
            overridePendingTransition(R.anim.msc_fade_in, R.anim.msc_fade_out);
        } else if (pushStyle == PageTransitionConfig.TransitionStyle.SLIDE_UP_SLIDE_DOWN) {
            overridePendingTransitionWithSlideUp();
        } else if (pushStyle == PageTransitionConfig.TransitionStyle.NONE) {
            overridePendingTransition(0, 0);
        }
    }

    protected void overridePendingTransitionWithSlideUp() {
        overridePendingTransition(R.anim.msc_slide_up, R.anim.msc_hold_anim);
    }

    @Override
    public View getRootView() {
        return findViewById(android.R.id.content);
    }

    @Override
    public boolean onLaunchError(String msg, int code, Throwable e) {
        return false;
    }

    protected void checkIsRecreate(Bundle savedInstanceState) {
        if (MSCTraceUtil.TRACE_LEVEL >= MSCTraceUtil.LEVEL_LIGHT) {
            PerfTrace.begin("MSCActivity#checkIsRecreate");
        }
        boolean isRecreate = false;
        int recreateType = 0;
        // 判断是否重建，并做相关处理
        if (savedInstanceState != null) {
            isRecreate = true;
            if(hasLaunched) {
                recreateType = 1; //页面重建
            } else {
                recreateType = 2;  //进程重建
            }
        } else {
            if (MSCProcess.isInMainProcess() && getIntent().hasExtra(EXTRA_LAUNCH_PID)) {
                isRecreate = getIntent().getIntExtra(EXTRA_LAUNCH_PID, 0) != android.os.Process.myPid();
            } else if (getIntent().hasExtra(EXTRA_INTENT_SEND_TIME)) {
                // intent发送到接收的时间大于10s，判断为发生了进程被杀后的重建，此时不会有savedInstanceState
                long now = SystemClock.elapsedRealtime();
                isRecreate = now - getIntent().getLongExtra(EXTRA_INTENT_SEND_TIME, now) > 10000;
            }
        }
        hasLaunched = true;
        MSCLog.i(TAG, "checkIsRecreate", isRecreate, recreateType);
        mController.setIsRecreate(isRecreate,recreateType);

        if (MSCTraceUtil.TRACE_LEVEL >= MSCTraceUtil.LEVEL_LIGHT) {
            PerfTrace.end("MSCActivity#checkIsRecreate");
        }
    }

    @Override
    public void onConfigurationChanged(Configuration newConfig) {
        super.onConfigurationChanged(newConfig);
        if (mController != null) {
            mController.onConfigurationChanged(newConfig);
        }
    }

    protected void checkAndFinishIfNeeded() {
        if (ActivityUtils.quitIfTaskNotAvailable(this)) {
            quitBeforeLaunch = true;
            return;
        }
        Intent intent = getIntent();

        // 获取并校验参数
        if (TextUtils.isEmpty(appId)) {
            MSCLog.e(TAG, "appId invalid, finish");
            quitBeforeLaunch = true;
            mController.onLaunchError("appId invalid", MSCLoadErrorConstants.ERROR_CODE_WRONG_APP_ID, null);
            finish();

            return;
        }

        if (mController.isRecreate()) {
            Intent finishAndStartIntent = MSCFusionActivityMonitor.getFinishAndStartIntent(this);
            if (finishAndStartIntent != null) {
                MSCLog.e(TAG, "need finish activity for fusion mode activity clear, but activity was destroyed, finish now when recreate");
                quitBeforeLaunch = true;
                finish();
                startActivity(finishAndStartIntent);
                return;
            }
        }
        if (IntentUtil.getBooleanExtra(intent, FINISH_AND_START, false)) {
            intent.removeExtra(FINISH_AND_START);
            intent.putExtra(EXTRA_FINISH_AND_START_DONE, true);

            if (mController.isRecreate()) {
                // 重建时的startActivity会被认为需要通过instrumentation改动intent flag
                // 而instrumentation中无法检测重建，只能一律增加FINISH_AND_START标识，到此处忽略
                MSCLog.w(TAG, "started by finish and start intent but recreating, ignore");
            } else {
                MSCLog.w(TAG, "finish and start intent: ", getIntent());
                finish();
                startActivity(intent);
                quitBeforeLaunch = true;
                return;
            }
        }
        //通过调用exitMiniProgram api退出小程序，直接finish
        if (IntentUtil.getBooleanExtra(intent, FINISH_BY_EXIT_MINIPROGRAM, false)) {
            finish();
            quitBeforeLaunch = true;
        }

    }

    @Override
    protected void onDestroy() {
        if (!MSCHornRollbackConfig.readConfig().rollbackReportContainerStayDuration) {
            ContainerReporter.CommonReporter.create().reportContainerStayDuration(getMPAppId(), getMPTargetPath(), false, containerCreateTimeMillis);
        }

        if (!quitBeforeLaunch) { //若是IllegalIntent，从onCreate直接finish，初始化基本没做，也不需要清理
            mController.onDestroy();
        }
//        mController.notifyOnDestroyToNative();
        super.onDestroy();
    }

    @Override
    public void onWindowFocusChanged(boolean hasFocus) {
        super.onWindowFocusChanged(hasFocus);
        mController.onWindowFocusChanged(hasFocus);
    }

    @Override
    protected void onStart() {
        beginDurableEvent(PerfEventConstant.CONTAINER_WILL_APPEAR);
        super.onStart();
        mController.onStart();
        endDurableEvent(PerfEventConstant.CONTAINER_WILL_APPEAR);
    }

    @Override
    protected void onStop() {
        super.onStop();
        if (quitBeforeLaunch) {
            MSCLog.w(TAG, "quitBeforeLaunch but onStop, return");
            return;
        }
        mController.onStop();
    }

    @Override
    protected void onResume() {
        beginDurableEvent(PerfEventConstant.CONTAINER_DID_APPEAR);
        mActivityPaused = false;
        // task id无效时AMS会抛异常，但低版本系统上onResume不调super会抛异常，此时一样是崩溃，不作处理
        if (Build.VERSION.SDK_INT > Build.VERSION_CODES.M
                && ActivityUtils.quitIfTaskNotAvailable(this)) {
            return;
        }
        Statistics.disableAutoPVPD(AppUtil.generatePageInfoKey(this));
        super.onResume();
        if (quitBeforeLaunch) {
            MSCLog.w(TAG, "quitBeforeLaunch but onResume, return");
            return;
        }
        mController.onResume();
        endDurableEvent(PerfEventConstant.CONTAINER_DID_APPEAR);
    }

    private boolean mActivityPaused = false;

    @Override
    protected void onPause() {
        super.onPause();
        mActivityPaused = true;
        if (quitBeforeLaunch) {
            MSCLog.w(TAG, "quitBeforeLaunch but onPause, return");
            return;
        }
        mController.onPause();
    }

    public boolean isActivityPaused() {
        return mActivityPaused;
    }

    @Override
    protected void onSaveInstanceState(Bundle outState) {
        mController.onSaveInstanceState(outState);
        super.onSaveInstanceState(outState);

        // https://km.sankuai.com/docs/mrn/page/789735215
        // 修复java.lang.RuntimeException: android.os.TransactionTooLargeException 问题
        // TODO: 2022/11/2 tianbin Fragment也需要防御，后续排期处理
        if (!MSCConfig.disableRemoveAndroidViewHierarchyState()) {
            if (Build.VERSION.SDK_INT >= Build.VERSION_CODES.N) {
                outState.remove("android:viewHierarchyState");
            }
        }
        // 参考闪购业务代码，com.sankuai.waimai.store.drug.goods.list.delegate.impl.SuperMarketBaseDelegateImpl
        // 尝试修复java.lang.RuntimeException: android.os.TransactionTooLargeException 问题
        // 清除super中保存的Fragment信息，禁止Activity销毁重建时自动创建Fragment
        if (!MSCConfig.disableRemoveAndroidSupportFragmentsState()) {
            try {
                outState.putParcelable("android:support:fragments", null);
            } catch (Exception e) {
                MSCLog.e(TAG, e, "onSaveInstanceState");
            }
        }

        outState.putLong(PAGE_SAVE_TIMESTAMP, System.currentTimeMillis());
        MSCLog.d(TAG, "onSaveInstanceState, pagePauseMemory:" + MemoryMonitor.getInstance().getLastSampleValue());
        outState.putInt(PAGE_SAVE_MEMORY, MemoryMonitor.getInstance().getLastSampleValue());
        outState.putInt(RUNTIME_KEEP_ALIVE_APP_SIZE, RuntimeManager.getKeepAliveAppSize());
        outState.putInt(RUNTIME_ALL_APP_SIZE, RuntimeManager.getAllRuntimes().size());
        outState.putString(RUNTIME_ALL_MESSAGE, getAllRuntimeMessage());
        boolean isDump = MSCDumpUtils.dumpHprofData(getMPAppId());
        outState.putBoolean(IS_DUMPED, isDump);
        MSCLog.d(TAG, "onSaveInstanceState, outState:" + outState.toString());
    }

    private String getAllRuntimeMessage() {
        JsonArray jsonArray = new JsonArray();
        Collection<MSCRuntime> runtimes = RuntimeManager.getAllRuntimes();
        for (MSCRuntime runtime : runtimes) {
            JsonObject json = new JsonObject();
            json.addProperty("appId", runtime.getAppId());
            if (runtime.getRendererManager() != null) {
                List<ReusableRenderer> webViewPool = runtime.getRendererManager().getWebViewPool();
                json.addProperty("webviewPoolSize", webViewPool.size());
                json.addProperty("webviewPoolMessage", webViewPool.toString());
            }
            jsonArray.add(json);
        }
        return jsonArray.toString();
    }

    @Override
    public String toString() {
        return getClass().getSimpleName() + "{appId=" + appId + ", activityId=" + mController.getContainerId() + '}';
    }

    @Override
    protected void onNewIntent(Intent intent) {
        long routeTime = System.currentTimeMillis();
        super.onNewIntent(intent);
        MSCLog.i(TAG, this, "onNewIntent");
        mController.skipReportResumePagePush();
        if (mController.handleOnNewIntent(intent, routeTime)) {
            boolean isExternalAppRelaunchAnimation = intent.getBooleanExtra(MSCParams.MSC_EXTERNAL_APP_NAVIGATE_TO_EXIST_MINI_PROGRAM_ANIMATION, false);
            if (MSCHornRollbackConfig.enableExternalAppPageDepthLimit() && isExternalAppRelaunchAnimation) {
                overridePendingTransition(R.anim.msc_slide_in_right, R.anim.msc_slide_out_left);
            } else if ((intent.getFlags() & Intent.FLAG_ACTIVITY_CLEAR_TOP) != 0) {
                // TODO 清除Activity并回退的场景，存在退出再进入的动画，此操作能消除进入动画，但退出动画还没办法
                overridePendingTransition(0, 0);
            }
        }
        checkReuseActivity(true);
    }

    @Override
    public void onBackPressed() {
        long routeTime = System.currentTimeMillis();
        if (!mController.handleBackPress(routeTime)) {
            super.onBackPressed();
        }
    }

    @Override
    public void invokeBackPress(){
        this.onBackPressed();
    }

    @Override
    public boolean handleBackPress() {
        return false;
    }

    /**
     * 继承版本 {@link AppBrandMSCActivity#handleCloseApp()} 内有用于将Task切至后台的不同行为，不一定是finish
     * 外部在不一定要finish而允许Task保活的情况下，应调此方法
     */
    public void handleCloseApp() {
        MSCLifecycleCallback.getInstance().onBackPressedByUser(mController.getAppId(), getIntent());
        MSCLog.d(TAG, "handleCloseApp");
        finish();
    }

    @Override
    public void finish() {
        mController.recordPagePopped();
        rawFinish();
        handleFinishTransition();
    }

    private void handleFinishTransition() {
        mController.handlePageOverrideContainerTransition();
    }

    protected void rawFinish() {
        super.finish();
    }

    @SuppressLint("RestrictedApi")
    @Override
    public void startActivityForResult(final Intent intent, final int requestCode, @Nullable final Bundle options) {
        // 当在小程序弹出键盘的时候跳转别的Activity，会导致页面中键盘部分虽然键盘不存在了，但是web是空白。延迟跳转貌似可以修复问题
        // 至于为什么不判断键盘是否弹起？因为安卓中没有100%有效的方法
        // 不可使用此处理，因BaseFragmentActivityApi16中有对requestCode的检测，如果不在Fragment.startActivityForResult的同步调用过程中，不允许requestCode使用高16位
//        mainHandler.postDelayed(new Runnable() {
//            @SuppressLint("RestrictedApi")
//            @Override
//            public void run() {
        super.startActivityForResult(intent, requestCode, options);
//            }
//        }, 100);
    }

    @Override
    protected void onActivityResult(int requestCode, int resultCode, Intent data) {
        super.onActivityResult(requestCode, resultCode, data);
        mController.onActivityResult(requestCode, resultCode, data);
    }

    @Override
    public Intent getStartContainerActivityIntent(@NonNull String appId, @Nullable Bundle extras) {
        return ContainerController.defaultGetStartContainerActivityIntent(appId, extras);
    }

    @Override
    public void onRequestPermissionsResult(int requestCode, @NonNull String[] permissions, @NonNull int[] grantResults) {
        super.onRequestPermissionsResult(requestCode, permissions, grantResults);
        mController.onRequestPermissionsResult(requestCode, permissions, grantResults);
    }

    /**
     * 宿主使用小程序申请权限
     */
    public void requestPermissionsOrEnqueue(final @NonNull String[] permissions, final String token, MsiPermissionGuard.ICallback callback) {
        mController.requestPermissionsOrEnqueue(permissions, token, callback);
    }

    public void unregisterReceiver(BroadcastReceiver receiver) {
        try {
            super.unregisterReceiver(receiver);
        } catch (Exception e) {
            // 在onActivityResult时可能发生java.lang.IllegalArgumentException: Receiver not registered，忽略
            MSCLog.w(TAG, "unregisterReceiver ", e);
        }
    }

    @Override
    public void onTrimMemory(int level) {
        super.onTrimMemory(level);
        mController.onTrimMemory(level);
    }

    protected String getStringExtra(String name) {
        return IntentUtil.getStringExtra(getIntent(), name);
    }

    /**
     * 切换tab，宿主用
     */
    protected boolean switchTab(String url, long routeTime) {
        return switchTab(url, routeTime, new BizNavigationExtraParams.Builder().build());
    }

    protected boolean switchTab(String url, long routeTime, @NonNull BizNavigationExtraParams bizNavigationExtraParams) {
        return mController.switchTab(url, routeTime, bizNavigationExtraParams);
    }

    /**
     * 供宿主覆盖
     */
    @Override
    public boolean needLoadingView() {
        return true;
    }

    /**
     * 供宿主覆盖
     */
    @Override
    public boolean needLoadingAppInfo() {
        return true;
    }

    /**
     * 供宿主覆盖
     */
    public String getMPTargetPath() {
        return mController.defaultGetMPTargetPath();
    }

    public synchronized void onPageFirstRender(String path, HashMap<String, Object> paramMap) {
    }

    /**
     * 供优选独立app宿主计算fst用，MMP中没有实现，从Service层报过来
     */
    public void onPageFirstScreen(long timestamp, int pageId) {
    }

    protected void cacheFirstWebView() {
        mController.mApp.getRuntime().webViewCacheManager.cacheFirstWebView(this, WebViewCacheManager.WebViewCreateScene.CREATE_BY_USER, appId);
    }

    protected void reportMPStartTime(long startUpTime) {
        // todo mmp 旧埋点
        // mController.mReporter.addExtraInfo("mpStart", mController.launchStartTime - startUpTime);
    }

    @Keep
    public static void launch(@NonNull Context context, @NonNull String appId,
                              @Nullable String localAppPath, @Nullable String appName, @Nullable String iconPath,
                              @Nullable String targetPath, @Nullable String shareEnv,
                              boolean isReload, @Nullable String srcAppID, @Nullable String srcExtraData) {
        if (context == null || TextUtils.isEmpty(appId)) {
            throw new IllegalArgumentException("context, appId are not null");
        }

        Intent intent = new Intent();
        intent.setComponent(new ComponentName(context, MSCActivity.class));
        intent.putExtra(APP_ID, appId);
        intent.putExtra(MSCParams.APP_NAME, appName);
        intent.putExtra(MSCParams.APP_ICON, iconPath);
        intent.putExtra(MSCParams.RELOAD, isReload);
        intent.putExtra(MSCParams.SRC_APP_ID, srcAppID);
        intent.putExtra(MSCParams.EXTRA_DATA, srcExtraData);
        intent.putExtra(TARGET_PATH, targetPath);
        intent.putExtra(MSCParams.SHARE_ENV, shareEnv);
        intent.addFlags(Intent.FLAG_ACTIVITY_NEW_TASK);
        intent.putExtra(EXTRA_PUSH_STYLE, 1);
        context.startActivity(intent);
    }

    @Override
    public Resources.Theme getTheme() {
        // 本地配置
        for (String brand : needResetActivityThemeBrandsLocal) {
            if (Build.MODEL.equalsIgnoreCase(brand)) {
                Resources.Theme theme = super.getTheme();
                theme.applyStyle(R.style.MSCThemeResetPadding, true);
                return theme;
            }
        }

        // horn远端配置
        List<String> brands = MSCConfig.getNeedResetActivityThemeBrands();
        if (brands == null || brands.isEmpty()) {
            return super.getTheme();
        }

        if (brands.contains(Build.BRAND)) {
            Resources.Theme theme = super.getTheme();
            theme.applyStyle(R.style.MSCThemeResetPadding, true);
            return theme;
        }

        return super.getTheme();
    }

    protected void onAppEnterBackground(boolean appIsVisible) {
        mController.onAppEnterBackground(appIsVisible);
    }

    protected void onAppEnterForeground() {
        mController.defaultOnAppEnterForeground();
    }

    @Override
    public void updateAppProp() {
        mController.defaultUpdateAppProp();
    }

    @Override
    public boolean isTransparentContainer() {
        return false;
    }

    @Override
    public int getTopMarginAtTransparentContainer() {
        return 0;
    }

    @Override
    public void customErrorViewLayout(View errorView) {

    }

    @Override
    public void startPageContainerEnterAnimation(ViewGroup pageContainer) {
        // 全屏页面不需要添加动效
    }

    @Override
    public boolean isPreCreate() {
        return false;
    }

    @Override
    public void setWidgetPreCreateListener(WidgetPreCreateListener widgetPreCreateListener) {

    }

    @Override
    public boolean isMSCInitedAtContainerOnCreate() {
        return isMSCInitedAtActivityOnCreate;
    }

    public void downgrade(String msg, int code, Throwable error) {
        mController.defaultDowngrade(msg, code, error);
    }

    public int getActivityId() {
        return mController != null ? mController.getContainerId() : hashCode();
    }

    /**
     * @return
     * @deprecated 兼容ffp低版本会在MSC场景调用MSCActivity的ffpTags接口问题：如果调用了View的ffpTags接口，就不再调用MSCActivity的ffpTags接口了
     * FIXME by chendacai: 保留几个大版本之后可删掉，从12.6.200版本开始算
     */
    @NonNull
    @Override
    @Deprecated
    public Map<String, Object> ffpTags() {
        if (PageViewWrapper.isEnabledViewFFpTagsFeature) {
            return Collections.emptyMap();
        }
        Map<String, Object> tags = mController != null ? mController.getFFPTags() : null;
        if (tags == null) {
            tags = Collections.emptyMap();
        }
        return tags;
    }

    @Nullable
    public String getTopPagePath() {
        return mController == null ? null : mController.getTopPagePath();
    }

    @Override
    public Map<String, String> getTopPageBizTags() {
        return mController == null ? null : mController.getTopPageBizTags();
    }

    /**
     * MSC交互响应流畅度指标接口:https://km.sankuai.com/collabpage/**********
     *
     * @return 页面路径
     */
    @Override
    public String getName() {
        String path = getMPTargetPath();
        final IPageModule topPage = getTopPage();
        if (topPage != null && !TextUtils.isEmpty(topPage.getPagePath())) {
            path = topPage.getPagePath();
        }
        if (TextUtils.isEmpty(path)) {
            return null;
        }
        String purePath = PathUtil.getPath(path);
        String name = "msc?appid=" + getMPAppId() + "&path=" + purePath;
        MSCLog.i("[MSCActivity@MetricsNameProvider@getName]", name);
        return name;
    }

    /**
     * MSC交互响应流畅度指标接口:https://km.sankuai.com/collabpage/**********
     *
     * @return 技术栈
     */
    @Override
    public String getTechStack() {
        final String defaultTeckStack = TechStack.OTHERS;
        final IPageModule topPage = getTopPage();
        if (topPage == null) {
            return defaultTeckStack;
        }
        final RendererType rendererType = topPage.getRendererType();
        if (rendererType == null) {
            return defaultTeckStack;
        }
        String rendererTypeString = rendererType.toString();
        switch (rendererTypeString) {
            case TechStack.MSC_NATIVE:
                return TechStack.MSC_NATIVE;
            case TechStack.MSC_REACT_NATIVE:
                return TechStack.MSC_REACT_NATIVE;
            case TechStack.MSC_WEBVIEW:
                return TechStack.MSC_WEBVIEW;
            default:
                return defaultTeckStack;
        }
    }

    private IPageModule getTopPage() {
        if (mController == null) {
            return null;
        }
        if (!mController.isPageManagerModuleValid()) {
            return null;
        }
        final IPageManagerModule pageMangerModule = mController.getPageMangerModule();
        if (pageMangerModule == null) {
            return null;
        }
        return pageMangerModule.getTopPage();
    }

    @Override
    public Map<String, Object> getTags(String type) {
        // FIXME by chendacai 支持Widget
        if (mController == null) {
            return null;
        }

        MSCLog.d(TAG, "getTags type:", type, ", mController:", mController.TAG, ", getRuntime:", mController.getRuntime());
        IPageModule topPage = getTopPage();
        if (topPage == null) {
            return null;
        }
        AppPageReporter pageReporter = topPage.getReporter();
        if (pageReporter == null) {
            return null;
        }
        MSCFFPReportListener mscffpReportListener = pageReporter.getFfpReportListener();
        if (mscffpReportListener == null) {
            return null;
        }
        Map<String, Object> tags = mscffpReportListener.getCommonTags();
        if (tags == null) {
            tags = new HashMap<>();
            tags.put("mscAppId", getMPAppId());
            tags.put("pagePath", PathUtil.getPath(getTopPagePath()));
            tags.put("renderType", getTechStack());
        }
        if (TextUtils.equals(type, FPS_SCROLL)) {
            tags = tags != null ? new HashMap<>(tags) : new HashMap<>();
            tags.put("scrollDetail", ScrollFPSRecorder.getInstance().getScrollFPSDetailMap());
            MSCLog.i(TAG, "Metrics Scroll FPS:", tags);

            if (RendererType.NATIVE == topPage.getRendererType()) {
                if (mController.getRuntime() != null && mController.getRuntime().getRuntimeReporter() != null && mController.getRuntime().getRuntimeReporter().getRenderReporter() != null) {
                    Map<String, Object> renderTags = mController.getRuntime().getRuntimeReporter().getRenderReporter().getRenderTags(topPage.getId(), getMPAppId(), topPage.getPagePath());
                    if (renderTags != null) {
                        tags.putAll(renderTags);
                    }
                }
            }
            MSCLog.i(TAG, "Metrics Scroll FPS enableRListPreRenderNative:", tags);
        }
        return tags;
    }

    @Override
    public void onStartToRecordScrollFps(String pageName) {
        ScrollFPSRecorder.getInstance().onStartToRecordScrollFps(pageName);
    }

    @Override
    public void onStopToRecordScrollFps(String pageName, long scrollTimeInNs, int frameCount) {
        ScrollFPSRecorder.getInstance().onStopToRecordScrollFps(pageName, scrollTimeInNs, frameCount);
    }

    @Override
    public void onComputeAvgScrollFpsOfEntirePage(String pageName, long totalScrollTimeInNs, int totalFrameCount, double avgScrollFps) {
        ScrollFPSRecorder.getInstance().onComputeAvgScrollFpsOfEntirePage(pageName, totalScrollTimeInNs, totalFrameCount, avgScrollFps);
    }

    @Override
    public String getCdcTechType() {
        return CIPDisplayTechType.MSC_WEBVIEW;
    }

    @Override
    public String getCdcPageName() {
        String purePath = PathUtil.getPath(getMPTargetPath());
        String displayName = String.format("%s_%s", getMPAppId(), purePath);
        return displayName;
    }

    @Override
    public String getCdcBundleName() {
        return getMPAppId();
    }

    @Override
    public CDCReportType getCdcReportType() {
        return CDCReportType.CDCReportTypeManual;
    }

    @Override
    public Map<String, Object> getCdcExtras() {
        return null;
    }
}
