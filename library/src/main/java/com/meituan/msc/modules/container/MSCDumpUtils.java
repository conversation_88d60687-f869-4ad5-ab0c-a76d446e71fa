package com.meituan.msc.modules.container;

//import android.text.format.DateUtils;
//
//import com.meituan.msc.common.config.MSCConfig;
//import com.meituan.msc.common.executor.MSCExecutors;
//import com.meituan.msc.extern.MSCEnvHelper;
//import com.meituan.msc.modules.reporter.MSCLog;

public class MSCDumpUtils {

//    private static final String TAG = "MSCDumpUtils";
//
//    public static final String DUMP_MEMORY_COUNT = "dumpMemoryCount";
//    public static final String DUMP_MEMORY_DATE = "dumpMemoryDate";

    public static boolean dumpHprofData(String appId) {
        return false;
//        if (!MSCConfig.isEnableDumpMemoryOnSaveInstance()
//                || !MSCConfig.isEnableDumpMemoryForAppId(appId)) {
//            return false;
//        }
//
//        int count = MSCEnvHelper.getDefaultSharedPreferences().getInt(DUMP_MEMORY_COUNT, 0);
//        long date = MSCEnvHelper.getDefaultSharedPreferences().getLong(DUMP_MEMORY_DATE, 0);
//        MSCLog.d(TAG, "dumpHprofData, count:" + count
//                + " , date:" + date
//                + " , config count:" + MSCConfig.getDumpMemoryCountEveryDay());
//        if (!DateUtils.isToday(date)) {
//            count = 1;
//        } else {
//            count++;
//        }
//        if (count > MSCConfig.getDumpMemoryCountEveryDay()) {
//            return false;
//        }
//        MSCEnvHelper.getDefaultSharedPreferences().edit().putInt(DUMP_MEMORY_COUNT, count).apply();
//        MSCEnvHelper.getDefaultSharedPreferences().edit().putLong(DUMP_MEMORY_DATE, System.currentTimeMillis()).apply();
//
//        MSCExecutors.submit(new Runnable() {
//            @Override
//            public void run() {
//                MSCLog.i(TAG, "dumpHprofData");
//                // TODO by chdc Metrics 未开放能力，暂时注释掉，和Metrics沟通优化方案
////                Koom.getInstance().dumpHprofData(KoomDebugger.DIAGNOSE);
//            }
//        });
//        return true;
    }
}
