package com.meituan.msc.modules.container;

import android.content.Context;
import android.content.Intent;
import android.net.Uri;

import com.meituan.msc.modules.router.MSCRouterInstrumentation;

/**
 * 只处理MSC跳链的 Instrumentation
 */
public abstract class MSCIntentInstrumentation extends IntentInstrumentation {
    private Uri mscUri;

    public MSCIntentInstrumentation(Context context, Uri mscUri) {
        super(context);
        this.mscUri = mscUri;
    }

    public MSCIntentInstrumentation(Context context) {
        super(context);
    }

    public final boolean processIntent(Context context, Intent originalIntent, boolean isStartActivity) {
        if (mscUri == null) {
            throw new IllegalArgumentException("Please set mscUri when create MSCIntentInstrumentation");
        }
        Uri uri = originalIntent.getData();
        if (uri == null || !uri.isHierarchical()) {
            return false;
        }
        if (!MSCRouterInstrumentation.matchWithoutQuery(uri, mscUri)) {
            return false;
        }
        return processMSCIntent(context, originalIntent, isStartActivity);
    }

    /**
     * 所有改动必须 在原有intent基础上修改
     * 原因：newActivity 不支持更换新的intent
     *
     * @param context
     * @param originalIntent
     * @return 是否已处理
     */
    public abstract boolean processMSCIntent(Context context, Intent originalIntent, boolean isStartActivity);
}
