package com.meituan.msc.modules.container;

import android.content.Intent;

import com.meituan.msc.common.utils.IntentUtil;
import com.meituan.msc.lib.interfaces.container.MSCParams;

public class MSCLaunchParams {
    private String appId;
    private Intent intent;

    public MSCLaunchParams(Intent intent) {
        this.intent = intent;
    }

    public String getAppId() {
        if (appId == null) {
            appId = IntentUtil.getStringExtra(intent, MSCParams.APP_ID);
        }
        return appId;
    }
}
