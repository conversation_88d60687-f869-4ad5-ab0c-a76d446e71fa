package com.meituan.msc.modules.container;

import android.text.TextUtils;
import java.util.HashSet;
import java.util.Set;

/**
 * 页面跳转状态单例
 */
public class MSCPageStateInstance {

    private static volatile MSCPageStateInstance mscPageStateInstance;
    /**
     * 下个页面的appId
     */
    private final Set<String> nextAppIdSet = new HashSet<>();
    /**
     * 当前页面状态
     */
    private int pageState;
    /**
     * 默认状态
     */
    public static final int INIT_STATE = 0; //默认onStop兜底触发onAppEnterBackground
    /**
     * 新建activity
     */
    public static final int NEW_ACTIVITY = 1;

    public static MSCPageStateInstance getInstance() {
        if (mscPageStateInstance == null) {
            synchronized (MSCPageStateInstance.class) {
                if (mscPageStateInstance == null) {
                    mscPageStateInstance = new MSCPageStateInstance();
                }
            }
        }
        return mscPageStateInstance;
    }

    private MSCPageStateInstance() {
        resetPageState();
    }

    /**
     * 是否为新建Activity
     * @return
     */
    public boolean isStartNewActivity() {
        return pageState == NEW_ACTIVITY;
    }


    /**
     * 是否为默认状态
     * @return
     */
    public boolean isInitState() {
        return pageState == INIT_STATE;
    }

    /**
     * 重置状态为INIT_STATE
     */
    public void resetPageState() {
        pageState = INIT_STATE;
    }

    public void setPageState(int pageState) {
        this.pageState = pageState;
    }

    public int getPageState() {
        return pageState;
    }

    public Set<String> getNextAppIdSet() {
        return nextAppIdSet;
    }

    public boolean isNextPageSameMSC(String curAppId) {
        for (String appId : nextAppIdSet) {
            if (TextUtils.equals(curAppId, appId)) {
                return true;
            }
        }
        return false;
    }
}
