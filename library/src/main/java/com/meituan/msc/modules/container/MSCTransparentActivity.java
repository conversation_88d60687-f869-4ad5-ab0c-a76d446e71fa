package com.meituan.msc.modules.container;

import android.animation.Animator;
import android.animation.AnimatorListenerAdapter;
import android.animation.AnimatorSet;
import android.animation.ArgbEvaluator;
import android.animation.ObjectAnimator;
import android.app.Activity;
import android.graphics.Bitmap;
import android.graphics.Color;
import android.graphics.drawable.ColorDrawable;
import android.graphics.drawable.Drawable;
import android.os.Bundle;
import android.view.View;
import android.view.ViewGroup;
import android.widget.FrameLayout;
import android.widget.ImageView;

import com.meituan.msc.common.config.MSCConfig;
import com.meituan.msc.common.utils.ActivityUtils;
import com.meituan.msc.common.utils.DisplayUtil;
import com.meituan.msc.lib.R;
import com.meituan.msc.modules.engine.MSCHornRollbackConfig;
import com.meituan.msc.modules.page.transition.PageLayoutTransitionHelper;
import com.meituan.msc.modules.reporter.MSCLog;

import java.lang.ref.WeakReference;

public class MSCTransparentActivity extends MSCActivity {

    private final String TAG = "MSCTransparentActivity@" + Integer.toHexString(hashCode());
    private WeakReference<Activity> lastResumeActivityWeak;

    @Override
    protected void onCreate(Bundle savedInstanceState) {
        if (!MSCHornRollbackConfig.readConfig().rollbackTransparentActivityFix) {
            ActivityUtils.fixActivityCreate(this);
        }
        super.onCreate(savedInstanceState);
        lastResumeActivityWeak = new WeakReference<>(ApplicationLifecycleMonitor.ALL.getLastActivity());
        MSCLog.i(TAG, "onCreate lastResumeActivity", lastResumeActivityWeak.get());
        View rootView = findViewById(R.id.msc_root);
        // 外卖上在调用exitMiniProgram时，rootView会为空，原因待确认
        if (rootView != null) {
            rootView.post(new Runnable() {
                @Override
                public void run() {
                    setLastActivityScreenshotToImageBg();
                    addShadowView();
                }
            });
        } else {
            MSCLog.i(TAG, "rootView is null");
        }
    }

    @Override
    protected void overridePendingTransitionWithSlideUp() {
        // 半屏弹窗样式下禁用Activity底部滑入动效，直接显示阴影和背景图，为显示视图添加底部滑入动效
        overridePendingTransition(0, 0);
    }

    private void addShadowView() {
        ColorDrawable shadowDrawable = new ColorDrawable(Color.BLACK);
        // 12.30.200，设计给出透明度规范：0.6
        shadowDrawable.setAlpha(MSCConfig.getHalfDialogShadowAlpha());
        View shadowView = findViewById(R.id.view_shadow);
        if (shadowView != null) {
            shadowView.setVisibility(View.VISIBLE);
            shadowView.setBackground(shadowDrawable);
            shadowView.setOnClickListener(new View.OnClickListener() {
                @Override
                public void onClick(View v) {
                    MSCTransparentActivity.this.onBackPressed();
                }
            });
        }
    }

    public void setLastActivityScreenshotToImageBg() {
        Activity lastResumeActivity;
        if (lastResumeActivityWeak == null || (lastResumeActivity = lastResumeActivityWeak.get()) == null) {
            return;
        }
        DisplayUtil.captureActivityScreen(lastResumeActivity, new DisplayUtil.OnCaptureListener() {
            @Override
            public void onCaptureSuccess(Bitmap bitmap) {
                setBitmapToBackgroundImage(bitmap);
            }

            @Override
            public void onCaptureFailed() {
                MSCLog.e(TAG, "captureActivityScreen failed", lastResumeActivity);
            }
        });
    }

    private void setBitmapToBackgroundImage(Bitmap bitmap) {
        if (bitmap == null) {
            MSCLog.d(TAG, "screenshot is null");
            return;
        }
        if (bitmap.getWidth() < 100 || bitmap.getHeight() < 100) {
            MSCLog.d(TAG, "screenshot size is too small");
            return;
        }

        ImageView bgImage = findViewById(R.id.bgImage);
        if (bgImage != null) {
            ViewGroup.LayoutParams bgImageLayoutParams = bgImage.getLayoutParams();
            int screenWidth = DisplayUtil.getScreenWidth(MSCTransparentActivity.this);
            bgImageLayoutParams.width = screenWidth;
            bgImageLayoutParams.height = (int) ((float) bitmap.getHeight() / bitmap.getWidth() * screenWidth);

            bgImage.setVisibility(View.VISIBLE);
            bgImage.setImageBitmap(bitmap);
            // 对比到店的方案，MSC在设置截图背景后没有重新设置为非透明主题，需要重点关注FPS指标
            // 未对其的原因为，闪购业务有一个70%的半屏页面，退出时隐藏截图背景会出现黑屏问题
            bgImage.post(new Runnable() {
                @Override
                public void run() {
                    if (isFinishing() || isDestroyed()) {
                        return;
                    }
                    DisplayUtil.setWindowFromTranslucent(MSCTransparentActivity.this, getWindow());
                    DisplayUtil.convertFromTranslucent(MSCTransparentActivity.this);
                    MSCLog.i(TAG, "convertFromTranslucent finish");
                }
            });
        } else {
            MSCLog.i(TAG, "bgImage is null");
        }
    }


    @Override
    public void finish() {
        FrameLayout containerView = getContainerController().getContainerView();
        if (containerView != null) {
            View pageContainer = containerView.getChildAt(0);
            if (pageContainer != null) {
                AnimatorSet animatorSet = new AnimatorSet();
                ObjectAnimator animator = ObjectAnimator.ofFloat(pageContainer, "translationY", 0, pageContainer.getHeight());
                animator.setDuration(MSCConfig.getHalfDialogTransitionDuration());
                animator.addListener(new AnimatorListenerAdapter() {
                    @Override
                    public void onAnimationEnd(Animator animation) {
                        MSCTransparentActivity.super.finish();
                        MSCTransparentActivity.this.overridePendingTransition(0, 0);
                    }
                });
                animatorSet.playTogether(animator);
                View shadowView = findViewById(R.id.view_shadow);
                if (shadowView != null) {
                    Drawable background = shadowView.getBackground();
                    if (background instanceof ColorDrawable) {
                        ObjectAnimator animator2 = ObjectAnimator.ofObject(shadowView, "backgroundColor",
                                new ArgbEvaluator(), ((ColorDrawable) background).getColor(), Color.TRANSPARENT);
                        animator2.setDuration(MSCConfig.getHalfDialogTransitionDuration());
                        animatorSet.playTogether(animator2);
                    }
                }
                animatorSet.start();
                return;
            }
        }
        MSCTransparentActivity.super.finish();
    }

    @Override
    public boolean isTransparentContainer() {
        return true;
    }

    @Override
    public int getTopMarginAtTransparentContainer() {
        return HalfPageUtils.getValidHalfPageTopMargin(this, getIntent());
    }

    @Override
    public void customErrorViewLayout(View errorView) {
        super.customErrorViewLayout(errorView);
        if (!MSCHornRollbackConfig.readConfig().rollbackHalfDialog) {
            FrameLayout.LayoutParams layoutParams = (FrameLayout.LayoutParams) errorView.getLayoutParams();
            layoutParams.topMargin = getTopMarginAtTransparentContainer();
            errorView.setLayoutParams(layoutParams);
            View failLogo = errorView.findViewById(R.id.msc_load_failed_logo);
            if (failLogo != null && layoutParams.topMargin > DisplayUtil.getScreenHeightReal() >> 1) {
                ViewGroup.MarginLayoutParams lp = (ViewGroup.MarginLayoutParams) failLogo.getLayoutParams();
                lp.topMargin = 0;
                failLogo.setLayoutParams(lp);
            }
        }
    }

    @Override
    public void startPageContainerEnterAnimation(ViewGroup pageContainer) {
        // 创建并启动从底部滑入的动画
        PageLayoutTransitionHelper.startSlideUpPushAnimation(pageContainer);
    }
}
