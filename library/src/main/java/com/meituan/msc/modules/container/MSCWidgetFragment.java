package com.meituan.msc.modules.container;

import static com.meituan.msc.common.perf.PerfEventConstant.NEW_WIDGET;
import static com.meituan.msc.common.perf.PerfEventConstant.ON_WIDGET_CREATE;
import static com.meituan.msc.modules.container.ContainerController.defaultGetStartContainerActivityIntent;

import android.app.Activity;
import android.content.ComponentCallbacks2;
import android.content.Context;
import android.content.Intent;
import android.content.res.Configuration;
import android.graphics.Color;
import android.net.Uri;
import android.os.Build;
import android.os.Bundle;
import android.os.Handler;
import android.os.Looper;
import android.support.annotation.LayoutRes;
import android.support.annotation.NonNull;
import android.support.annotation.Nullable;
import android.support.annotation.VisibleForTesting;
import android.support.v4.app.FragmentActivity;
import android.text.TextUtils;
import android.view.LayoutInflater;
import android.view.View;
import android.view.ViewGroup;
import android.view.Window;
import android.widget.FrameLayout;
import android.widget.TextView;

import com.meituan.android.techstack.ICIPDynamicContentProtocol;
import com.meituan.metrics.MetricsTagsProvider;
import com.meituan.metrics.MetricsTechStackProvider;
import com.meituan.metrics.TechStack;
import com.meituan.msc.common.config.MSCConfig;
import com.meituan.msc.common.constant.APPIDConstants;
import com.meituan.msc.common.executor.MSCExecutors;
import com.meituan.msc.common.framework.Callback;
import com.meituan.msc.common.utils.ColorUtil;
import com.meituan.msc.common.utils.IntentUtil;
import com.meituan.msc.common.utils.JsonUtil;
import com.meituan.msc.common.utils.PathUtil;
import com.meituan.msc.common.utils.ToastUtils;
import com.meituan.msc.extern.MSCEnvHelper;
import com.meituan.msc.lib.R;
import com.meituan.msc.lib.interfaces.container.MSCParams;
import com.meituan.msc.modules.api.msi.navigation.BizNavigationExtraParams;
import com.meituan.msc.modules.api.widget.WidgetPreCreateListener;
import com.meituan.msc.modules.engine.MSCHornRollbackConfig;
import com.meituan.msc.modules.engine.MSCRuntime;
import com.meituan.msc.modules.engine.ResultCallback;
import com.meituan.msc.modules.engine.RuntimeManager;
import com.meituan.msc.modules.page.IPageManagerModule;
import com.meituan.msc.modules.page.IPageModule;
import com.meituan.msc.modules.page.RouteReporter;
import com.meituan.msc.modules.page.UserReporter;
import com.meituan.msc.modules.page.render.RendererType;
import com.meituan.msc.modules.page.render.webview.PreloadWebViewManager;
import com.meituan.msc.modules.page.render.webview.WebViewCacheManager;
import com.meituan.msc.modules.page.render.webview.WebViewFirstPreloadStateManager;
import com.meituan.msc.modules.preload.MSCHornPreloadConfig;
import com.meituan.msc.modules.preload.PreloadManager;
import com.meituan.msc.modules.preload.PreloadResultData;
import com.meituan.msc.modules.reporter.MSCLoadErrorConstants;
import com.meituan.msc.modules.reporter.MSCLog;
import com.meituan.msc.modules.update.bean.PackageInfoWrapper;
import com.meituan.msc.util.perf.PerfTrace;

import java.util.Collections;
import java.util.HashMap;
import java.util.Map;
import java.util.Set;

public class MSCWidgetFragment extends LifecycleFragment implements IMSCContainer, WidgetEventListener, MetricsTechStackProvider, MetricsTagsProvider, ICIPDynamicContentProtocol {

    protected ContainerController mController = new ContainerController(this);
    protected View rootView;
    private static final String TAG = "MSCWidgetFragment";
    private static final String KEY_STATE = "state";
    private long containerCreateTimeMillis;
    private WidgetPreCreateListener widgetPreCreateListener;
    private boolean isMSCInitedAtFragmentOnCreate;
    private Map<String, Object> largeInitialDataMap;

    public static MSCWidgetFragment createInstanceFromUri(String uri) {
        return createInstance(new MSCWidgetParams().setWidgetUri(uri));
    }

    public static MSCWidgetFragment createInstance(String appId, String targetPath) {
        return createInstance(new MSCWidgetParams().setAppId(appId).setTargetPath(targetPath));
    }

    public static MSCWidgetFragment createInstanceFromUri(String uri, boolean isPreCreate) {
        return createInstance(new MSCWidgetParams().setWidgetUri(uri).setPreCreate(isPreCreate));
    }

    public static MSCWidgetFragment createInstance(String appId, String targetPath, boolean isPreCreate) {
        return createInstance(new MSCWidgetParams().setAppId(appId).setTargetPath(targetPath).setPreCreate(isPreCreate));
    }

    /**
     * 预创建widget场景下，在widget不可见->可见时，通知msc视图已展示，作为秒开开始的回调
     * true FFP上报成功
     * false FFP上报失败
     */
    public boolean notifyWillAppear() {
        long time = System.currentTimeMillis();
        if (widgetPreCreateListener != null) {
            MSCLog.i(TAG, "notifyWillAppear success");
            widgetPreCreateListener.onWillAppear(time);
            //防止内存泄露
            widgetPreCreateListener = null;
            return true;
        } else {
            MSCLog.e(TAG, "notifyWillAppear fail before loadUrl!");
            return false;
        }
    }

    public MSCWidgetFragment() {
        super();
        mController.setRouteId(this.hashCode());
        PerfTrace.online().instant(NEW_WIDGET).report();
    }

    @Nullable
    @Override
    public Map<String, String> getTopPageBizTags() {
        return mController == null ? null : mController.getTopPageBizTags();
    }

    public static MSCWidgetFragment createInstance(MSCWidgetParams params) {
        MSCWidgetFragment fragment = new MSCWidgetFragment();
        fragment.setArguments(params.build());
        fragment.setLargeInitialDataMap(params.largeInitialDataMap);
        return fragment;
    }

    @Override
    public void setArguments(@Nullable Bundle args) {
        super.setArguments(args);
        this.intent = null;
        handleBusinessPreload();
    }

    @VisibleForTesting
    public boolean checkEnvValid(String appId, String targetPath) {
        if (!MSCEnvHelper.isInited()) {
            MSCLog.w(TAG, "BizPreload fail, msc is not init, appId:", appId, ", targetPath:", targetPath);
            return false;
        }
        if (!MSCHornPreloadConfig.isInBizPreloadListWhenWidgetSetUri(appId)) {
            MSCLog.w(TAG, "BizPreload fail, is not in biz preload list");
            return false;
        }
        return true;
    }

    private void handleBusinessPreload() {
        Intent intent = getIntent();
        if (intent == null) {
            return;
        }
        String appId = IntentUtil.getStringExtra(intent, MSCParams.APP_ID);
        String targetPath = IntentUtil.getStringExtra(intent, MSCParams.TARGET_PATH);
        if (!checkEnvValid(appId, targetPath)) {
            // 内部已有log
            return;
        }

        Context context = MSCEnvHelper.getContext();
        if (context == null) {
            MSCLog.w(TAG, "BizPreload fail, context is null, appId:", appId, ", targetPath:", targetPath);
            return;
        }
        // 业务预热会消耗掉基础库预热的runtime。所以这里将基础库预热的runtime提前拿出来，
        // 并且在业务预热前获取一个基础库预热完成的Runtime，并传给后面的injectBaseInfoToWebView，
        // 以保证业务预热和WebView注入基础库操作的是同一个Runtime。否则业务预热获取到的 基础库预热完成 的运行时
        // 注入业务包之后就不一定是基础库预热的运行时了，就会拿到两个不同的运行时。
        final MSCRuntime basePreloadRuntime = RuntimeManager.findBasePreloadRuntime();
        PreloadManager.getInstance().putPreloadBizMetricsInfo(appId, "basePreloadUsedNoTriggerAgain", "base preload engine used, not trigger again");

        PerfTrace.begin("bizPreloadWhenWidgetSetUri");
        PreloadManager.getInstance().preloadBiz(context, appId, targetPath, new Callback<PreloadResultData>() {
            @Override
            public void onSuccess(PreloadResultData data) {
                PerfTrace.end("bizPreloadWhenWidgetSetUri");
                MSCLog.i(TAG, "BizPreload success, appId:", appId, ", targetPath:", targetPath);
            }

            @Override
            public void onFail(String errMsg, Exception error) {
                MSCLog.e(TAG, error, "BizPreload fail, appId:", appId, ", targetPath:", targetPath);
            }

            @Override
            public void onCancel() {
                MSCLog.i(TAG, "BizPreload cancel, appId:", appId, ", targetPath:", targetPath);
            }
        });

        cacheTheWebView(appId);
        injectBaseInfoToWebView(basePreloadRuntime, appId);
    }

    private void cacheTheWebView(String appId) {
        final boolean isInWhiteList = MSCHornPreloadConfig.isInBizPreloadListWhenWidgetSetUri(appId);
        // 功能开关打开 && 地图
        if (MSCHornPreloadConfig.enableCacheFirstWebViewInBusinessPreload()
                && isInWhiteList) {
            PerfTrace.online().begin("cacheTheWebView").report();
            WebViewCacheManager.getInstance().cacheFirstWebView(MSCEnvHelper.getContext(),
                    WebViewCacheManager.WebViewCreateScene.PRE_CREATE, PreloadWebViewManager.PRELOAD_WEBVIEW);
            PerfTrace.online().end("cacheTheWebView").report();
        } else {
            MSCLog.i(TAG, "cacheTheWebView horn closed.");
        }
    }

    private void injectBaseInfoToWebView(MSCRuntime basePreloadRuntime, String appId) {
        final boolean isInWhiteList = MSCHornPreloadConfig.isInBizPreloadListWhenWidgetSetUri(appId);
        if (!MSCHornPreloadConfig.enableWebViewInjectInBusinessPreload()
                || !isInWhiteList) {
            MSCLog.i(TAG, "injectBaseInfoToWebView horn closed.");
            return;
        }
        if (basePreloadRuntime == null) {
            MSCLog.i(TAG, "injectBaseInfoToWebView canceled, basePreloadRuntime is null");
            return;
        }
        PackageInfoWrapper basePackage = basePreloadRuntime.getMSCAppModule().getBasePackage();
        if (basePackage == null || basePackage.getDDResource() == null) {
            MSCLog.e(TAG, "injectBaseInfoToWebView canceled, basePackage is null");
            return;
        }

        MSCLog.i(TAG, "injectBaseInfoToWebView preInjectWebViewResource");
        PerfTrace.online().begin("injectBaseInfoToWebView").report();
        basePreloadRuntime.getRendererManager().preloadWebViewBasePackage(MSCEnvHelper.getContext(),
                basePackage, new ResultCallback() {
                    @Override
                    public void onReceiveFailValue(Exception e) {
                        MSCLog.i("injectBaseInfoToWebView", "preloadBasePackage step4 exit");
                        if (MSCHornRollbackConfig.readConfig().rollbackLoadErrorReportFix) {
                            basePreloadRuntime.getRuntimeReporter().reportMSCLoadError(MSCLoadErrorConstants.ERROR_INJECT_BASE_JS_WEBVIEW, e);
                        } else {
                            basePreloadRuntime.getRuntimeReporter().reportMSCLoadError(basePreloadRuntime.hasContainerAttached(),
                                    MSCLoadErrorConstants.ERROR_INJECT_BASE_JS_WEBVIEW, e);
                        }
                    }

                    @Override
                    public void onReceiveValue(String value) {
                        PerfTrace.online().instant("preInjectWebViewResource").report();
                        MSCLog.i("injectBaseInfoToWebView", "preloadBasePackage step4 success");
                        WebViewFirstPreloadStateManager.getInstance().updateStateAfterPreload();
                    }
                });
        PerfTrace.online().end("injectBaseInfoToWebView").report();
    }

    @Override
    public void onConfigurationChanged(Configuration newConfig) {
        super.onConfigurationChanged(newConfig);
        if (mController != null) {
            mController.onConfigurationChanged(newConfig);
        }
    }

    @Override
    public String getTopPagePath() {
        return mController == null ? null : mController.getTopPagePath();
    }

    @Override
    public String getMPAppVersion() {
        return mController == null ? null : mController.defaultGetMPAppVersion();
    }

    /**
     * 设置 Widget 的背景色, 仅在有相关参数时设置, 未配置 Widget 背景色时不处理
     */
    private void setWidgetBackgroundColorIfNeed(View view) {
        if (null == view) {
            return;
        }
        Bundle bundle = getArguments();
        if (null == bundle || !bundle.containsKey(MSCParams.WIDGET_BACKGROUND_COLOR)) {
            return;
        }
        String colorStr = bundle.getString(MSCParams.WIDGET_BACKGROUND_COLOR, "");
        if (TextUtils.isEmpty(colorStr)) {
            return;
        }
        int color = ColorUtil.parseColor(colorStr, Color.TRANSPARENT);
        MSCLog.i(TAG, "setWidgetBackgroundColorIfNeed", color);
        view.setBackgroundColor(color);
    }

    @Override
    public Map<String, Object> getTags(String type) {
        Map<String, Object> tags = new HashMap<>();
        if (mController == null || mController.getActivity() == null) {
            MSCLog.e(TAG, "getTags controller or activity is null");
            return tags;
        }
        tags.put("mscAppId", getMPAppId());
        tags.put("pagePath", PathUtil.getPath(getTopPagePath()));
        tags.put("renderType", getTechStack());
        return tags;
    }

    @Override
    public String getTechStack() {
        final String defaultTeckStack = TechStack.OTHERS;
        final IPageModule topPage = getTopPage();
        if (topPage == null) {
            return defaultTeckStack;
        }
        final RendererType rendererType = topPage.getRendererType();
        if (rendererType == null) {
            return defaultTeckStack;
        }
        String rendererTypeString = rendererType.toString();
        switch (rendererTypeString) {
            case TechStack.MSC_NATIVE:
                return TechStack.MSC_NATIVE;
            case TechStack.MSC_REACT_NATIVE:
                return TechStack.MSC_REACT_NATIVE;
            case TechStack.MSC_WEBVIEW:
                return TechStack.MSC_WEBVIEW;
            default:
                return defaultTeckStack;
        }
    }

    private IPageModule getTopPage() {
        if (mController == null) {
            return null;
        }
        if (!mController.isPageManagerModuleValid()) {
            return null;
        }
        final IPageManagerModule pageMangerModule = mController.getPageMangerModule();
        if (pageMangerModule == null) {
            return null;
        }
        return pageMangerModule.getTopPage();
    }

    @Override
    public final boolean isActivity() {
        return false;
    }

    Bundle mSavedInstanceState;

    @Override
    public void onCreate(@Nullable Bundle savedInstanceState) {
        containerCreateTimeMillis = System.currentTimeMillis();
        PerfTrace.online().instant(ON_WIDGET_CREATE).report();
        super.onCreate(savedInstanceState);
        String appId = IntentUtil.getStringExtra(getIntent(), MSCParams.APP_ID);
        String targetPath = IntentUtil.getStringExtra(getIntent(), MSCParams.TARGET_PATH);

        // 容器启动时的上报路由
        if (!MSCHornRollbackConfig.readConfig().rollbackReportRouteStartAtContainerCreate) {
            RouteReporter.CommonReporter.create().reportRouteStart(getActivity(), appId, "appLaunch",
                    targetPath, true);
        }

        Uri uri = getIntent().getData();
        int uriLength = uri == null ? -1 : uri.toString().length();
        String widgetData = IntentUtil.getStringExtra(getIntent(), MSCParams.WIDGET_DATA);
        int widgetDataLength = widgetData == null ? -1 : widgetData.length();
        int targetPathLength = getMPTargetPath() == null ? -1 : getMPTargetPath().length();
        UserReporter.create().reportUserLaunchStart(appId, true, targetPath, UserReporter.CONSTANT_PORTAL, false, uriLength, widgetDataLength, targetPathLength);
        mSavedInstanceState = savedInstanceState;
        isMSCInitedAtFragmentOnCreate = MSCEnvHelper.isInited();
        MSCEnvHelper.onMSCContainerCreate(getContext());
        MSCEnvHelper.ensureFullInited();
        if (!TextUtils.isEmpty(getMPAppId())) {
            createContainerController(savedInstanceState);
        } else {
            // unexpected args
        }
    }

    private void createContainerController(@Nullable Bundle savedInstanceState) {
        if (null == mController) {
            mController = new ContainerController(this);
        }
        // mController初始化提前，导致mController#mActivity赋值时机提前，可能获取不到Activity
        mController.setContainer(this);
        mController.setAppId(getMPAppId());
        mController.initLaunchStartTime();
        mController.onCreate(savedInstanceState);
        mController.createRuntime(mSavedInstanceState);
    }

    @Nullable
    @Override
    public View onCreateView(@NonNull LayoutInflater inflater, @Nullable ViewGroup container, @Nullable Bundle savedInstanceState) {
        View view = super.onCreateView(inflater, container, savedInstanceState);
        setWidgetBackgroundColorIfNeed(view);
        return view;
    }

    @Override
    protected View inflateRootView(FragmentActivity activity, ViewGroup container, LayoutInflater inflater, @Nullable Bundle savedInstanceState) {
        if (TextUtils.isEmpty(getMPAppId()) || null == mController) {
            String msg = "启动参数错误，请检查业务AppID";
            if (!onLaunchError(msg, -1, null)) {
                rootView = inflater.inflate(R.layout.msc_load_error_release, container, false);
                ((TextView) rootView.findViewById(R.id.msc_load_failed_title)).setText(msg);
                return rootView;
            } else {
                rootView = new FrameLayout(getContext());
            }
        } else {
            rootView = inflater.inflate(mController.getLayout(), container, false);
        }
        return rootView;
    }

    @Override
    protected void onFragmentFirstVisible() {
        long routeTime = System.currentTimeMillis();
        super.onFragmentFirstVisible();
        if (mController != null) {
            boolean disablePrefetch = IntentUtil.getBooleanExtra(getIntent(), MSCParams.DISABLE_PREFETCH, false);
            BizNavigationExtraParams bizNavigationExtraParams = new BizNavigationExtraParams.Builder()
                    .setDisablePrefetch(disablePrefetch)
                    .build();
            // 修复StartPage任务启动页面时PageManager为空导致的NPE
            if (mController.enablePreStartPage()) {
                mController.startInitialPage(mSavedInstanceState, routeTime, bizNavigationExtraParams);
            }
            mController.onActivityCreated(mSavedInstanceState, routeTime, bizNavigationExtraParams);
        }
    }

    @Override
    protected void onFragmentPause() {
        super.onFragmentPause();
        if (mController == null) {
            return;
        }
        mController.onPause();
        String appId = mController.getAppId();
        if (MSCHornRollbackConfig.isRollbackLifecycle(appId)) {
            return;
        }
        MSCPageStateInstance pageStateInstance = MSCPageStateInstance.getInstance();
        if (!pageStateInstance.isInitState()) {
            return;
        }
        MSCExecutors.postOnUiThread(new Runnable() {
            @Override
            public void run() {
                //适配TabWidget执行switchTab切换tab时，执行onAppEnterBackground。如果感知到activity的onPause，或者非tabWidget，则不走下面逻辑。
                if (mController == null || mController.isFragmentActivityOnPause() || (!mController.isTabWidget() && !MSCHornRollbackConfig.fixWidgetLifeCycleManualInvoke(appId))) {
                    return;
                }
                mController.onAppEnterBackground(pageStateInstance.isNextPageSameMSC(appId));
            }
        });
    }

    @Override
    protected void onFragmentResume() {
        super.onFragmentResume();
        if (mController != null) {
            mController.onResume();
        }
    }

    public View getRootView() {
        return rootView;
    }

    @Override
    public void onDestroyView() {
        super.onDestroyView();
//        if (mController != null) {
//            mController.onDestroy();
//            mController = null;
//        }
    }

    @Override
    public void onStart() {
        super.onStart();
        if (mController != null) {
            mController.onStart();
        }
    }

    @Override
    public void onResume() {
        super.onResume();
    }

    @Override
    public void onPause() {
        super.onPause();
    }

    @Override
    public void onStop() {
        super.onStop();
        if (mController != null) {
            mController.onStop();
        }
    }

    @Override
    public void onDestroy() {
        if (!MSCHornRollbackConfig.readConfig().rollbackReportContainerStayDuration) {
            ContainerReporter.CommonReporter.create().reportContainerStayDuration(getMPAppId(), getMPTargetPath(), true, containerCreateTimeMillis);
        }
        super.onDestroy();
        if (mController != null) {
            mController.onDestroy();
            mController = null;
        }
    }

    public <T extends View> T findViewById(int id) {
        View view = getRootView();
        if (view != null) {
            return view.findViewById(id);
        }
        return null;
    }

    @Override
    public void onSaveInstanceState(@NonNull Bundle outState) {
        if (mController != null) {
            mController.onSaveInstanceState(outState);
        }
        super.onSaveInstanceState(outState);
        outState.putString(KEY_STATE, "");
        new Handler(Looper.getMainLooper()).post(new Runnable() {
            @Override
            public void run() {
                if (outState != null && Build.VERSION.SDK_INT >= Build.VERSION_CODES.N) {
                    outState.remove("android:view_state");
                    outState.remove(KEY_STATE);
                }
            }
        });
    }

    /**
     * 业务定制加载异常
     *
     * @return 业务是否已处理
     */
    @Override
    public boolean onLaunchError(String msg, int code, Throwable e) {
        return false;
    }


    Intent intent;

    @Override
    public Intent getIntent() {
        if (intent == null) {
            Bundle bundle = getArguments();
            Activity activity = getActivity();
            intent = new Intent();
            if (activity != null) {
                Intent activityIntent = activity.getIntent();
                if (activityIntent != null) {
                    boolean disableReuseAny = activityIntent.getBooleanExtra(MSCParams.DISABLE_REUSE_ANY, false);
                    intent.putExtra(MSCParams.DISABLE_REUSE_ANY, disableReuseAny);
                }
            }
            if (bundle != null && bundle.containsKey(MSCParams.WIDGET_PATH)) {
                try {
                    String widgetPath = bundle.getString(MSCParams.WIDGET_PATH);
                    MSCLog.i(TAG, "setWidgetUri", widgetPath);
                    intent.setData(Uri.parse(widgetPath));
                } catch (Exception e) {
                    //ignore
                }
            }
            if (bundle != null) {
                intent.putExtras(bundle);   // 将Fragment.setArgument()传入的参数作为intent的参数
            }
        }
        return intent;
    }

    /**
     * 指定获取窗口，如果在 Dialog 中使用，请主动重写 getWindow 方法
     *
     * @return
     */
    @Override
    public Window getWindow() {
        return getActivity() != null ? getActivity().getWindow() : null;
    }

    /**
     * 宿主可覆盖，用于Widget启动完整版小程序Activity，默认为启动RouterCenterActivity，宿主不走Router时需要注意
     */
    @Override
    public Intent getStartContainerActivityIntent(@NonNull String appId, @Nullable Bundle extras) {
        return defaultGetStartContainerActivityIntent(appId, extras);
    }

    @Override
    public void onActivityResult(int requestCode, int resultCode, Intent data) {
        super.onActivityResult(requestCode, resultCode, data);
        if (mController != null) {
            mController.onActivityResult(requestCode, resultCode, data);
        }
    }

    @Override
    public void onRequestPermissionsResult(int requestCode, @NonNull String[] permissions, @NonNull int[] grantResults) {
        super.onRequestPermissionsResult(requestCode, permissions, grantResults);
        if (mController != null) {
            mController.onRequestPermissionsResult(requestCode, permissions, grantResults);
        }
    }

    //    @Override //android.support.v4.Fragment未实现此方法，在原版Fragment是有的，通过onLowMemory模拟
    public void onTrimMemory(int level) {
        if (mController != null) {
            mController.onTrimMemory(level);
        }
    }

    @Override
    public void onLowMemory() {
        super.onLowMemory();
        onTrimMemory(ComponentCallbacks2.TRIM_MEMORY_COMPLETE);
    }

    /**
     * Fragment本不支持处理back事件，如宿主希望由小程序处理，可在back时调用本方法
     *
     * @return 是否已处理back事件
     */
    public boolean onBackPressed() {
        long routeTime = System.currentTimeMillis();
        if (mController != null) {
            return mController.handleBackPress(routeTime);
        }
        return false;
    }

    @Override
    public void invokeBackPress() {
        MSCLog.e(TAG, "widget not support invokeBackPress!");
    }

    /**
     * 业务可在该方法内模拟系统退出
     *
     * @return
     */
    @Override
    public boolean handleBackPress() {
        // 不支持
        return false;
    }

    /**
     * 宿主可覆盖以提供appId
     */
    @Override
    public String getMPAppId() {
        return ContainerController.defaultGetMPAppId(getIntent());
    }

    @Override
    public void updateAppProp() {
        if (null != mController) {
            mController.defaultUpdateAppProp();
        }
    }

    @Override
    public String getMPTargetPath() {
        if (null == mController) {
            return "";
        }
        // 美团首页优选Tab页启动链接依然包含widgetPath字段，启动链接由玲珑下发，MSC适配下
        if (TextUtils.equals(getMPAppId(), APPIDConstants.YOU_XUAN)) {
            Intent intent = getIntent();
            Uri uri;
            if ((uri = intent.getData()) != null) {
                String widgetPath = uri.getQueryParameter(MSCParams.WIDGET_PATH);
                if (!TextUtils.isEmpty(widgetPath)) {
                    MSCLog.i(TAG, "getMPTargetPath", widgetPath);
                    return widgetPath;
                }
            }
        }
        return mController.defaultGetMPTargetPath();
    }

    @Override
    public boolean needLoadingView() {
        return true;
    }

    /**
     * 供宿主覆盖
     */
    @Override
    public boolean needLoadingAppInfo() {
        return false;
    }

    /**
     * Widget的更新数据，将在 {@link MSCWidgetFragment # setInitialData(Map)} 的初始数据之后发送给前端
     */
    public void updateWidgetData(Map<String, Object> dataMap) {
        if (null != mController) {
            mController.onWidgetDataChange(dataMap);
        }
    }

    private Set<String> registeredWidgetEvents;
    private WidgetEventListener widgetEventListener;

    /**
     * 注册前端事件监听
     *
     * @param events   所有需要监听的事件
     * @param listener
     */
    public void registerWidgetEvent(Set<String> events, WidgetEventListener listener) {
        registeredWidgetEvents = events;
        widgetEventListener = listener;
    }

    /**
     * 内部使用
     *
     * @param event
     * @param params
     */
    @Override
    public void onWidgetEvent(String event, Map<String, Object> params) {
        if (widgetEventListener != null) {
            widgetEventListener.onWidgetEvent(event, params);
        }
    }


    /**
     * 占位图配置
     *
     * @return
     */
    @Nullable
    public View getPlaceholder() {
        if (getArguments() != null && getArguments().containsKey(MSCParams.WIDGET_LOADING)) {
            try {
                return getLayoutInflater().inflate(getArguments().getInt(MSCParams.WIDGET_LOADING), null);
            } catch (Exception e) {
                //ignore
            }
        }
        return null;
    }

    protected WidgetReopenListener widgetReopenListener;

    public interface WidgetReopenListener {
        void reopen();
    }

    public WidgetReopenListener getWidgetReopenListener() {
        return widgetReopenListener;
    }

    public void setWidgetReopenListener(WidgetReopenListener widgetReopenListener) {
        this.widgetReopenListener = widgetReopenListener;
    }

    public void notifyReopenWidgetToNative() {
        if (widgetReopenListener != null) {
            MSCLog.i(TAG, "UpdateManage widget applyUpdate notify reOpen to native, appId: ", getMPAppId());
            widgetReopenListener.reopen();
        }
    }

    /**
     * 在小程序配置中查找不到对应的页面路径时触发回调
     */
    protected void onPageNotFound() {

    }

    /**
     * 在当前页面首帧渲染完成（FP）时触发回调
     */
    protected void onPageFirstRender() {

    }

    @Override
    public boolean isTransparentContainer() {
        return false;
    }

    @Override
    public int getTopMarginAtTransparentContainer() {
        return 0;
    }

    @Override
    public void customErrorViewLayout(View errorView) {

    }

    @Override
    public void startPageContainerEnterAnimation(ViewGroup pageContainer) {
        // Widget场景不需要给Page添加动效
    }

    @Override
    public boolean isPreCreate() {
        if (getArguments() != null) {
            return getArguments().getBoolean(MSCParams.PRE_CREATE, false);
        }
        return false;
    }

    @Override
    public void setWidgetPreCreateListener(WidgetPreCreateListener widgetPreCreateListener) {
        this.widgetPreCreateListener = widgetPreCreateListener;
    }

    @Override
    public boolean isMSCInitedAtContainerOnCreate() {
        return isMSCInitedAtFragmentOnCreate;
    }

    public Map<String, Object> getLargeInitialDataMap() {
        return largeInitialDataMap;
    }

    public void setLargeInitialDataMap(Map<String, Object> largeInitialDataMap) {
        this.largeInitialDataMap = largeInitialDataMap;
    }

    public Map<String, Object> getMergedInitialDataMap() {
        Map<String, Object> mergedMap = new HashMap<>();
        Map<String, Object> initialDataMap = JsonUtil.toMap(IntentUtil.getStringExtra(getIntent(), MSCParams.WIDGET_DATA));
        if (initialDataMap != null) {
            mergedMap.putAll(initialDataMap);
        }
        if (largeInitialDataMap != null) {
            mergedMap.putAll(largeInitialDataMap);
        }
        return mergedMap;
    }

    public static class MSCWidgetParams {

        String widgetUri;
        String appId;
        String targetPath;
        String initialData;

        Map<String, Object> largeInitialDataMap;
        String widgetBackgroundColor;
        @LayoutRes
        int layoutId;
        boolean isPreCreate;

        /**
         * 指定小程序 appID，建议和 {@link #setTargetPath(String)}搭配使用；
         * 不建议和{@link #setWidgetUri(java.lang.String)}共同使用
         *
         * @param appId
         * @return
         */
        public MSCWidgetParams setAppId(String appId) {
            this.appId = appId;
            return this;
        }

        /**
         * 指定小程序落地页 建议和 {@link #setAppId(String)}搭配使用；
         * 不建议和{@link #setWidgetUri(java.lang.String)}共同使用
         *
         * @param targetPath
         * @return
         */
        public MSCWidgetParams setTargetPath(String targetPath) {
            if (targetPath == null) {
                return this;
            }
            if (targetPath.length() > MSCConfig.getWidgetDataMaxSize()) {
                ToastUtils.toastIfDebug("widget targetPath too large, Please try reducing the data size");
            }
            this.targetPath = targetPath;
            return this;
        }

        /**
         * Widget的初始数据，业务小程序可在 Page.onload(data)/ Widget.properties）获取对应值
         * 如不需要可不设置
         * 如设置则必须在Widget创建时即设置，否则设置时可能已错过时机，不会发给前端
         * 如准备完整数据耗时，建议先设置不耗时的基础数据，后续数据通过 {@link #updateWidgetData(Map)} 发送
         * 大小限制 100kb
         *
         * @param data
         * @return
         */
        public MSCWidgetParams setInitialData(Map<String, Object> data) {
            if (data == null) {
                return this;
            }
            initialData = JsonUtil.toJsonString(data);
            if (initialData.length() > MSCConfig.getWidgetDataMaxSize()) {
                ToastUtils.toastIfDebug("widget initial data too large, Please try reducing the data size");
            }
            return this;
        }

        /**
         * Widget的初始数据，业务小程序可在 Page.onload(data)/ Widget.properties）获取对应值
         * 如不需要可不设置
         * 如设置则必须在Widget创建时即设置，否则设置时可能已错过时机，不会发给前端
         * Widget单独销毁重建无法恢复该数据，依赖使用的业务方重建Widget。
         * 方案文档: <a href="https://km.sankuai.com/collabpage/2705361338">...</a>
         *
         * @param largeInitialDataMap 初始化大数据
         * @return
         */
        public MSCWidgetParams setLargeInitialDataMap(Map<String, Object> largeInitialDataMap) {
            if (largeInitialDataMap == null) {
                return this;
            }
            this.largeInitialDataMap = largeInitialDataMap;
            return this;
        }

        /**
         * 设置占位图，用于加载过程占位；如果业务有继承本Fragment 也可复写 com.meituan.msc.modules.container.MSCWidgetFragment#getPlaceholder() 方法
         *
         * @param layoutId 请使用layout 对应的资源id
         * @return
         */
        public MSCWidgetParams setPlaceHolder(@LayoutRes int layoutId) {
            this.layoutId = layoutId;
            return this;
        }

        /**
         * 指定小程序启动页面；兼容启动小程序Activity的uri，例如 imeituan://www.meituan.com/mmp?appId=gh_84b9766b95bc
         * 直接指定完整启动协议即可，MSC 容器主要使用query中的参数参数；开发者工具生成的链接可直接用于启动该页面；
         *
         * @param uri
         * @return
         */
        public MSCWidgetParams setWidgetUri(String uri) {
            if (uri == null) {
                return this;
            }
            if (uri.length() > MSCConfig.getWidgetDataMaxSize()) {
                ToastUtils.toastIfDebug("widget uri too large, Please try reducing the uri size");
            }
            this.widgetUri = uri;
            MSCLog.i(TAG, "setWidgetUri", uri);
            return this;
        }

        /**
         * 小程序widget场景设置透明背景接口
         *
         * @param widgetBackgroundColor color
         * @return this
         */
        public MSCWidgetParams setWidgetBackgroundColor(String widgetBackgroundColor) {
            this.widgetBackgroundColor = widgetBackgroundColor;
            return this;
        }

        public MSCWidgetParams setPreCreate(boolean isPreCreate) {
            this.isPreCreate = isPreCreate && MSCHornRollbackConfig.enablePreCreateWidgetFFPFix();
            return this;
        }

        public Bundle build() {
            Bundle bundle = new Bundle();
            if (!TextUtils.isEmpty(widgetUri)) {
                bundle.putString(MSCParams.WIDGET_PATH, widgetUri);
            }
            if (!TextUtils.isEmpty(appId)) {
                bundle.putString(MSCParams.APP_ID, appId);
            }
            if (!TextUtils.isEmpty(targetPath)) {
                bundle.putString(MSCParams.TARGET_PATH, targetPath);

            }
            if (initialData != null) {
                bundle.putString(MSCParams.WIDGET_DATA, initialData);
            }
            if (layoutId != 0) {
                bundle.putInt(MSCParams.WIDGET_LOADING, layoutId);
            }
            if (!TextUtils.isEmpty(widgetBackgroundColor)) {
                bundle.putString(MSCParams.WIDGET_BACKGROUND_COLOR, widgetBackgroundColor);
            }
            bundle.putBoolean(MSCParams.PRE_CREATE, isPreCreate);
            return bundle;
        }
    }

    @Override
    public String getCdcTechType() {
        return null;
    }

    @Override
    public String getCdcPageName() {
        String purePath = PathUtil.getPath(getMPTargetPath());
        String displayName = String.format("%s_%s", getCdcBundleName(), purePath);
        return displayName;
    }

    @Override
    public String getCdcBundleName() {
        return mController != null ? mController.getAppId() : "";
    }

    @Override
    public CDCReportType getCdcReportType() {
        return null;
    }

    @Override
    public Map<String, Object> getCdcExtras() {
        return Collections.emptyMap();
    }
}
