package com.meituan.msc.modules.container;

import android.text.TextUtils;
import android.util.Log;

import com.meituan.msc.modules.api.ApiException;
import com.meituan.msc.modules.api.msi.navigation.BizNavigationExtraParams;
import com.meituan.msc.modules.engine.MSCHornRollbackConfig;
import com.meituan.msc.modules.engine.MSCRuntime;
import com.meituan.msc.modules.page.RouteReporter;
import com.meituan.msc.modules.reporter.MSCLog;
import com.meituan.msc.modules.update.AppConfigModule;

import java.util.HashMap;
import java.util.Map;

public class OpenParams {

    // OpenType
    public static final String NAVIGATE_TO = "navigateTo";
    public static final String NAVIGATE_BACK = "navigateBack";
    public static final String NAVIGATE_BACK_UTIL = "navigateBackUtil"; // navigateBackUntil，参数为要被退出的最后一个页面
    public static final String REDIRECT_TO = "redirectTo";
    public static final String SWITCH_TAB = "switchTab";

    public static final String RELOAD = "reload";
    public static final String APP_LAUNCH = "appLaunch";
    public static final String RE_LAUNCH = "reLaunch"; // 目前openType前端仅判断了reLaunch一种，其余暂仅作标识用

    // widget 相关
    public static final String WIDGET_LAUNCH = "widgetLaunch";
    public static final String WIDGET_DESTROY = "widgetDestroy";
    public static final String WIDGET_RELOAD = "widgetReload";

    public String url;
    public String originUrl;
    public String openType;
    public BizNavigationExtraParams bizNavigationExtraParams = new BizNavigationExtraParams.Builder().build();
    /**
     * 路由时间点
     */
    private long routeTime;
    private int routeId;
    public Integer openSeq; //用于帮前端在前后两个页面之间标识一次打开，进而实现EventChannel功能
    private Map<String, Object> extraParams;
    public boolean isTab;
    public Boolean isTabDerived;
    public boolean isFromLaunch;

    public OpenParams() {
    }

    public OpenParams(String url, String openType) {
        this.url = url;
        this.openType = openType;
    }

    public OpenParams setOpenType(String openType) {
        this.openType = openType;
        return this;
    }

    public void setUrl(String url) {
        this.url = url;
    }

    public long getRouteTime() {
        return routeTime;
    }

    public OpenParams setRouteTime(long routeTime) {
        this.routeTime = routeTime;
        return this;
    }

    public void setRouteId(int routeId) {
        this.routeId = routeId;
    }

    public int getRouteId() {
        return this.routeId;
    }

    public OpenParams addExtraParam(String key, Object value) {
        if (this.extraParams == null) {
            this.extraParams = new HashMap<>();
        }
        this.extraParams.put(key, value);
        return this;
    }

    public Map<String, Object> getExtraParams() {
        return extraParams;
    }

    public String getOriginUrlOrUrl() {
        return originUrl != null ? originUrl : url;
    }

    public static class Builder {
        private static final String TAG = "OpenParams.Builder";
        private String url;
        private String openType;
        /**
         * 路由时间点
         */
        public long routeTime;
        public int routeId;
        private Integer openSeq;
        private boolean allowInfer;
        private boolean isWidget;
        private boolean isPreloadPage;
        private boolean externalRouter;
        private boolean ignoreRouteMapping;
        private BizNavigationExtraParams bizNavigationExtraParams = new BizNavigationExtraParams.Builder().build();

        public Builder setOpenSeq(Integer openSeq) {
            this.openSeq = openSeq;
            return this;
        }

        public Builder setOpenType(String openType) {
            this.openType = openType;
            return this;
        }

        public Builder setRouteTime(long routeTime) {
            this.routeTime = routeTime;
            return this;
        }

        public Builder setRouteId(int routeId) {
            this.routeId = routeId;
            return this;
        }

        public Builder setUrl(String url) {
            this.url = url;
            return this;
        }

        public Builder setExternalRouter(boolean externalRouter) {
            this.externalRouter = externalRouter;
            return this;
        }

        public Builder setIgnoreRouteMapping(boolean ignoreRouteMapping) {
            this.ignoreRouteMapping = ignoreRouteMapping;
            return this;
        }

        public Builder setBizNavigationExtraParams(BizNavigationExtraParams bizNavigationExtraParams) {
            this.bizNavigationExtraParams = bizNavigationExtraParams;
            return this;
        }

        /**
         * tab页必须处于页面栈底部，此时融合模式Activity一定已被清除至只剩最接近栈底的一个，视为relaunch
         *
         * @param allowInfer
         * @return
         */
        public Builder setAllowInferNavigateToTabPage(boolean allowInfer) {
            this.allowInfer = allowInfer;
            return this;
        }

        public OpenParams build(MSCRuntime runtime) throws ApiException {
            OpenParams openParams = new OpenParams();
            openParams.openSeq = openSeq;
            openParams.url = url;
            openParams.bizNavigationExtraParams = bizNavigationExtraParams;
            if (TextUtils.isEmpty(url)) {
                ApiException e = new ApiException("url is empty");
                reportRouteOpenParamError(runtime, null, openType, e);
                throw e;
            }
            // 预创建Page场景，不确定openType，不进行检查
            if (!isPreloadPage && openType == null) {
                ApiException e = new ApiException("empty openType");
                reportRouteOpenParamError(runtime, url, null, e);
                throw e;
            }
            openParams.openType = openType;
            openParams.routeTime = routeTime;
            openParams.routeId = routeId;
            if (MSCHornRollbackConfig.isRollbackSetRouteMappingPersist()) {
                if (!isWidget && !isPreloadPage) {
                    String target = runtime.getAppConfigModule().getRoutePathIfRouted(url);
                    if (TextUtils.equals(openType, OpenParams.SWITCH_TAB) && runtime.getMSCAppModule().isSubPackagePage(target)) {
                        ApiException e = new ApiException("switchTab url is sub package path");
                        reportRouteOpenParamError(runtime, target, openType, e);
                        throw e;
                    }
                    if (target != null) {
                        openParams.originUrl = url;
                        openParams.url = target;
                    }
                }
                openParams.isTab = checkOrInferTabPage(openParams.getOriginUrlOrUrl(), runtime, openParams);
                if (!MSCHornRollbackConfig.get().getConfig().rollbackOpenParamUrlNotFoundCheck
                        // 当启动页面不存在时会展示页面不存在兜底页，退出时由于会发送navigateBack给前端，不应该抛异常
                        && !TextUtils.equals(openType, OpenParams.NAVIGATE_BACK_UTIL)
                        && !TextUtils.equals(openType, OpenParams.WIDGET_DESTROY)
                        && !runtime.getAppConfigModule().hasPage(url)) {
                    ApiException e = new ApiException("page " + url + " is not found");
                    reportRouteOpenParamError(runtime, url, openType, e);
                    throw e;
                }
            } else {
                // 预热Page不支持setRouteMapping，对齐iOS
                boolean hasRouted;
                hasRouted = routeMappingPersist(openParams, runtime);
                if (!hasRouted) {
                    hasRouted = routeMapping(openParams, runtime);
                }
                if (!hasRouted) {
                    openParams.isTab = checkOrInferTabPage(openParams.url, runtime, openParams);
                }
                if (TextUtils.isEmpty(openParams.url)) {
                    throw new ApiException("url is empty");
                }
                if (!runtime.getMSCAppModule().hasPage(openParams.url)) {
                    throw new ApiException(String.format("page %s is not found", openParams.url));
                }
            }
            return openParams;
        }

        private boolean routeMapping(OpenParams openParams, MSCRuntime runtime) throws ApiException {
            AppConfigModule appConfigModule = runtime.getAppConfigModule();
            boolean onlyExternalRouter = appConfigModule.isOnlyExternalRouter(openParams.url);
            Boolean isTabDerived = appConfigModule.isTabDerived(openParams.url);
            MSCLog.i(TAG, "routeMapping url:", openParams.url, "onlyExternalRouter:", onlyExternalRouter, "external:", externalRouter, "ignoreRouteMapping:", ignoreRouteMapping, "isTabDerived:", isTabDerived);
            if (onlyExternalRouter && !externalRouter) {
                return false;
            }
            String targetPath = appConfigModule.getRoutePathIfRouted(openParams.url);
            if (!TextUtils.isEmpty(targetPath)) {
                openParams.isTabDerived = isTabDerived;
                if (appConfigModule.hasPage(targetPath)) {
                    openParams.originUrl = openParams.url;
                    openParams.url = targetPath;
                    if (Boolean.TRUE.equals(isTabDerived)) {
                        openParams.isTab = checkOrInferTabPage(openParams.originUrl, runtime, openParams);
                    } else {
                        openParams.isTab = checkOrInferTabPage(openParams.url, runtime, openParams);
                    }
                } else {
                    MSCLog.e(TAG, "routeMapping targetPath is not a valid page");
                    RouteReporter.create(runtime).reportRouteMappingPersistFail(runtime.getAppId(), targetPath, openParams.url, runtime.getMSCAppModule().getBuildId(), false);
                    openParams.isTab = checkOrInferTabPage(openParams.url, runtime,openParams);
                }
                return true;
            }
            return false;
        }

        private boolean routeMappingPersist(OpenParams openParams, MSCRuntime runtime) throws ApiException {
            AppConfigModule appConfigModule = runtime.getAppConfigModule();
            boolean onlyExternalRouter = appConfigModule.isOnlyExternalRouterPersist(openParams.url);
            Boolean isTabDerived = appConfigModule.isTabDerivedPersist(openParams.url);
            MSCLog.i(TAG, "routeMappingPersist url:", openParams.url, "onlyExternalRouter:", onlyExternalRouter, "external:", externalRouter, "ignoreRouteMapping:", ignoreRouteMapping, "isTabDerived:", isTabDerived);
            if (onlyExternalRouter && !externalRouter) {
                return false;
            }
            String targetPath = appConfigModule.getRoutePathPersistIfRouted(openParams.url);
            if (!TextUtils.isEmpty(targetPath) && !ignoreRouteMapping) {
                openParams.isTabDerived = isTabDerived;
                if (appConfigModule.hasPage(targetPath)) {
                    openParams.originUrl = openParams.url;
                    openParams.url = targetPath;
                    if (Boolean.TRUE.equals(isTabDerived)) {
                        openParams.isTab = checkOrInferTabPage(openParams.originUrl, runtime, openParams);
                    } else {
                        openParams.isTab = checkOrInferTabPage(openParams.url, runtime, openParams);
                    }
                } else {
                    MSCLog.e(TAG, "routeMappingPersist targetPath is not a valid page");
                    RouteReporter.create(runtime).reportRouteMappingPersistFail(runtime.getAppId(), targetPath, openParams.url, runtime.getMSCAppModule().getBuildId(), true);
                    openParams.isTab = checkOrInferTabPage(openParams.url, runtime, openParams);
                }
                return true;
            }
            return false;
        }

        private void reportRouteOpenParamError(MSCRuntime runtime, String url, String openType, ApiException e) {
            String stackTrace = "";
            // TODO: 2024/3/1 tianbin 不确定是否会出现异常信息过长导致OOM，先防御一下，待确认后优化
            try {
                stackTrace = Log.getStackTraceString(e);
            } catch (Throwable tr) {
                MSCLog.e(TAG, tr, "reportRouteOpenParamError");
            }
            RouteReporter.create(runtime).reportRouteOpenParamError(url, openType, stackTrace);
        }

        private boolean checkOrInferTabPage(String originUrl, MSCRuntime runtime, OpenParams openParams) throws ApiException {
            if (runtime.getAppConfigModule().isTabPage(originUrl)) {
                if (MSCHornRollbackConfig.rollbackSetRouteMapping()) {
                    MSCLog.i("OpenParams", "checkOrInferTabPage rollbackSetRouteMapping");
                    return true;
                }
                if (TextUtils.equals(NAVIGATE_TO, openType) || TextUtils.equals(REDIRECT_TO, openType)) {
                    if (allowInfer) {
                        openType = RE_LAUNCH;
                    } else {
                        if (MSCHornRollbackConfig.isRollbackSetRouteMappingPersist()) {
                            ApiException e = new ApiException("can not " + openType + " tab page");
                            reportRouteOpenParamError(runtime, originUrl, openType, e);
                            throw e;
                        } else {
                            if (!TextUtils.isEmpty(openParams.originUrl) && !runtime.getAppConfigModule().isTabPage(openParams.originUrl)) {
                                openParams.url = openParams.originUrl;
                                return false;
                            } else {
                                ApiException e = new ApiException("can not " + openType + " tab page");
                                reportRouteOpenParamError(runtime, originUrl, openType, e);
                                throw e;
                            }
                        }
                    }
                }
                return true;
            } else {
                if (TextUtils.equals(SWITCH_TAB, openType)) {
                    if (MSCHornRollbackConfig.isRollbackSetRouteMappingPersist()) {
                        ApiException e = new ApiException("can not switchTab to single page");
                        reportRouteOpenParamError(runtime, originUrl, openType, e);
                        throw e;
                    } else {
                        if (runtime.getAppConfigModule().isTabPage(openParams.originUrl)) {
                            openParams.url = openParams.originUrl;
                            return true;
                        } else {
                            ApiException e = new ApiException("can not switchTab to single page");
                            reportRouteOpenParamError(runtime, originUrl, openType, e);
                            throw e;
                        }
                    }
                }
                return false;
            }
        }

        public Builder setIsWidget(boolean isWidget) {
            this.isWidget = isWidget;
            return this;
        }

        public Builder setIsPreloadPage(boolean isPreloadPage) {
            this.isPreloadPage = isPreloadPage;
            return this;
        }
    }
}
