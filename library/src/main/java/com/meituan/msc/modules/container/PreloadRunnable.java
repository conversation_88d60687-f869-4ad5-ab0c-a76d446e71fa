package com.meituan.msc.modules.container;

import android.media.MediaCodecList;
import android.os.Build;

import com.meituan.msc.common.utils.RomUtil;
import com.meituan.msc.extern.MSCCallFactory;

public class PreloadRunnable implements Runnable {

    @Override
    public void run() {
        RomUtil.isEmui();

        // 为webview初始化
        if (Build.VERSION.SDK_INT >= Build.VERSION_CODES.LOLLIPOP) {
            new MediaCodecList(MediaCodecList.REGULAR_CODECS);
        }

        MSCCallFactory.getFrameworkCallFactory();
    }
}