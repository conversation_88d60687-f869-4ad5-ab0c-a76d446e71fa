package com.meituan.msc.modules.container;

import com.meituan.msc.modules.update.pkg.MSCLoadPackageScene;
import com.meituan.msc.common.utils.DisplayUtil;
import com.meituan.msc.modules.engine.IRendererManager;
import com.meituan.msc.modules.engine.MSCRuntime;
import com.meituan.msc.modules.reporter.MSCLog;
import com.meituan.msc.modules.update.pkg.PrefetchPackageManager;

import java.lang.ref.WeakReference;

public class PreloadTaskAfterPageFP implements Runnable {

    private static final String TAG = "PreloadTaskAfterPageFP";
    private final String path;
    private final WeakReference<ContainerDelegate> containerDelegateWeakReference;
    private final WeakReference<MSCRuntime> runtimeWeakReference;

    public PreloadTaskAfterPageFP(ContainerDelegate containerDelegate, MSCRuntime runtime, String path) {
        containerDelegateWeakReference = new WeakReference<>(containerDelegate);
        runtimeWeakReference = new WeakReference<>(runtime);
        this.path = path;
    }

    @Override
    public void run() {
        ContainerDelegate containerDelegate;
        MSCRuntime runtime;
        if ((containerDelegate = containerDelegateWeakReference.get()) == null || (runtime = runtimeWeakReference.get()) == null) {
            MSCLog.i(TAG, "containerDelegate or runtime is null");
            return;
        }

        if (containerDelegate.isFinishing() || containerDelegate.isDestroyed()) {
            MSCLog.i(TAG, "containerDelegate finishing or destroyed");
            return;
        }

        MSCLog.i(TAG, "prefetch sub package after first render");
        PrefetchPackageManager.getInstance().prefetchSubPackage(runtime, path, MSCLoadPackageScene.LOAD_PACKAGE_TYPE_FP_PRE_DOWNLOAD);
        // 渲染缓存/快照场景下，firstRender时机会很早，此时下载大量子包等操作会拖慢小程序后续显示线上内容，需要寻找时机，适当推后，目前为临时处理
//            MSCExecutors.runOnUiThreadIdle(() -> {
//                if (getActivity().isFinishing() || isDestroyed()) {
//                    MSCLog.i(TAG, "containerDelegate destroy no prefetch sub");
//                    return;
//                }
//
//            //TODO 如果按单个页面控制使用的渲染器种类，如何确定应该预加载哪种？目前只针对webview进行预加载
        //firstRender之后开始预热下个page
        if (containerDelegate.isResumed()) {
            if (!DisplayUtil.isWebViewRender(runtime, runtime.getMSCAppModule().getRootPath())) {
                MSCLog.i(TAG, "root path is not webview render, don't cache next page");
                return;
            }
            MSCLog.i(TAG, runtime.getAppId(), "cache one page after first render");
            runtime.getModule(IRendererManager.class).cacheRendererForNextPage(containerDelegate.getActivity(), path);
        }
    }
}
