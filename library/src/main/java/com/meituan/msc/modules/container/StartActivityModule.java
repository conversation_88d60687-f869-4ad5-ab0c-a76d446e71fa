package com.meituan.msc.modules.container;

import android.app.Activity;
import android.content.ActivityNotFoundException;
import android.content.Intent;
import android.os.Bundle;
import android.support.annotation.Nullable;

import com.meituan.msc.modules.engine.MSCHornRollbackConfig;
import com.meituan.msc.modules.manager.MSCModule;
import com.meituan.msc.modules.manager.ModuleName;
import com.meituan.msc.modules.reporter.MSCLog;
import com.meituan.msi.bean.NavActivityInfo;

import java.util.concurrent.ConcurrentHashMap;

/**
 * Created by letty on 2022/1/10.
 **/
@ModuleName(name = "startActivity")
public class StartActivityModule extends MSCModule implements IStartActivityModule {

    private static final String TAG = "StartActivityModule";
    ConcurrentHashMap<Integer, CallRecord> mStartActivityCall = new ConcurrentHashMap<>();

    private static class CallRecord {
        private int requestCode;
        private IActivityResultCallBack callback;

        public CallRecord(int code, @Nullable IActivityResultCallBack callback) {
            this.requestCode = code;
            this.callback = callback;
        }
    }

    @Override
    public void startActivityForResult(Intent intent, int requestCode, @Nullable Bundle options, NavActivityInfo navActivityInfo, @Nullable IActivityResultCallBack callback) {
        int pageId = 0;
        if (navActivityInfo != null) {
            pageId = navActivityInfo.pageId;
        }
        IContainerDelegate containerDelegate = getRuntime().getContainerManagerModule().getContainerDelegateByPageIdOrTopPage(pageId);
        // 防止onDestroy方法后调用
        if (containerDelegate == null) {
            if (callback != null) {
                callback.onFail(2, "can't getContainerDelegateByPageIdOrTopPage by pageId" + pageId);
            } else {
                MSCLog.i(TAG, "startActivityForResult callback is null");
            }
            return;
        }
        if (requestCode == -1) {
            boolean isSuccess = startActivityForResultInner(containerDelegate, intent, requestCode, options, navActivityInfo, callback);
            if (isSuccess && callback != null) {
                callback.onActivityResult(requestCode, intent);
            }
            return;
        }
        CallRecord callRecord = mStartActivityCall.remove(containerDelegate.getContainerId());
        //每个Activity只允许等待一个页面的onActivityResult回调，超过则前一个返回cancel事件
        if (callRecord != null && callRecord.callback != null) {
            callRecord.callback.onActivityResult(Activity.RESULT_CANCELED, new Intent());
        }
        if (callback != null) {
            mStartActivityCall.put(containerDelegate.getContainerId(), new CallRecord(requestCode, callback));
        }
        startActivityForResultInner(containerDelegate, intent, requestCode, options, navActivityInfo, callback);
    }

    private boolean startActivityForResultInner(IContainerDelegate containerDelegate, Intent intent, int requestCode, @Nullable Bundle options, NavActivityInfo navActivityInfo, @Nullable IActivityResultCallBack callback) {
        try {
            containerDelegate.startActivityForResult(intent, requestCode, options, navActivityInfo);
        } catch (ActivityNotFoundException exception) {
            if (callback != null) {
                callback.onFail(Activity.RESULT_CANCELED, exception.toString());
            }
            MSCLog.e(TAG, exception);
            return false;
        }
        return true;
    }

    @Override
    public void startActivityForResult(Intent intent, int requestCode, @Nullable IActivityResultCallBack callback) {
        startActivityForResult(intent, requestCode, null, null, callback);
    }

    @Override
    public void startActivityForResult(Intent intent, int requestCode, NavActivityInfo navActivityInfo, @Nullable IActivityResultCallBack callback) {
        startActivityForResult(intent, requestCode, null, navActivityInfo, callback);
    }

    @Override
    public void startActivity(Intent intent) {
        startActivityForResult(intent, -1, null);
    }

    @Override
    public void clearStartActivityCall(IContainerDelegate containerDelegate) {
        CallRecord callRecord = mStartActivityCall.remove(containerDelegate.getContainerId());
        if (callRecord != null && callRecord.callback != null) {
            callRecord.callback.onActivityResult(Activity.RESULT_CANCELED, new Intent());
        }
    }

    @Override
    public void onActivityResult(IContainerDelegate containerDelegate, int requestCode, int resultCode, Intent data) {
        CallRecord callRecord = mStartActivityCall.get(containerDelegate.getContainerId());
        if (callRecord != null
                && callRecord.callback != null
                && requestCode == callRecord.requestCode) {
            mStartActivityCall.remove(containerDelegate.getContainerId());
            callRecord.callback.onActivityResult(resultCode, data);
        }
    }

}
