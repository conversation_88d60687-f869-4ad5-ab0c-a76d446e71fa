package com.meituan.msc.modules.container.fusion;

import android.content.Context;
import android.content.Intent;
import android.net.Uri;

import com.meituan.msc.common.utils.ActivityUtils;
import com.meituan.msc.common.utils.IntentUtil;
import com.meituan.msc.lib.interfaces.container.MSCParams;
import com.meituan.msc.modules.container.ContainerController;
import com.meituan.msc.modules.container.MSCActivity;
import com.meituan.msc.modules.engine.MSCHornRollbackConfig;
import com.meituan.msc.modules.reporter.MSCLog;

/**
 * 多任务栈不能用这个！
 *
 * Created by letty on 2020/11/23.
 **/
public class FusionPageManager extends IFusionPageManager {

    public FusionPageManager() {
    }

    /**
     * relaunch
     */
    @Override
    public boolean reLaunch(Context context, String appId, String url, Intent origIntent) {
        // TODO chdc 在Intent中携带routeTime传给下一个MSCActivity
        Intent intent = getStartMPActivityIntent(appId, origIntent, url);
        // HeraActivity.onNewIntent的默认行为是navigateTo，加参数以确保按relaunch执行
        intent.putExtra(MSCActivity.RELAUNCH, true);
        return ActivityUtils.startActivitySafely(context, intent);
    }

    @Override
    public boolean switchTab(Context context, String appId, String url, Intent origIntent) {
        // TODO chdc 在Intent中携带routeTime传给下一个MSCActivity
        Intent intent = getStartMPActivityIntent(appId, origIntent, url);
        if (!MSCHornRollbackConfig.isRollbackSetRouteMappingFix()) {
            intent.putExtra(MSCActivity.SWITCH_TAB, true);
        }
        return ActivityUtils.startActivitySafely(context, intent);
    }

    public Intent getStartMPActivityIntent(String appId, Intent origIntent, String url) {  //TODO 为了方便业务测试此行为，需要让reload情况能有多个activity
        Intent intent = new Intent();
        String containerUrl = supportStackChangeAppList.get(appId);
        if (containerUrl != null) {
            Uri uri = Uri.parse(containerUrl)
                    .buildUpon()
                    .appendQueryParameter(MSCParams.TARGET_PATH, url)
                    .build();
            intent.setData(uri);
        } else {
            // 在宿主未提供启动特定小程序的url时，直接启动对应的HeraActivity类，仅能用于宿主未继承HeraActivity时
            intent.setComponent(origIntent.getComponent()); // 经RouterCenterActivity启动时，一定有Component
            Uri data = origIntent.getData();
            if (data != null) {
                try {
                    intent.setData(data.buildUpon().query(null).build());
                } catch (Exception e) {
                    MSCLog.e("FusionPageManager", e);
                }
            }
            intent.setAction(origIntent.getAction());
            origIntent.setType(origIntent.getType());
            if (origIntent.getCategories() != null) {
                for (String category : origIntent.getCategories()) {
                    intent.addCategory(category);
                }
            }

            intent.putExtra(MSCParams.TARGET_PATH, url);
        }
        intent.putExtra(MSCParams.APP_ID, appId);
        IntentUtil.copyMMPDebugExtras(origIntent, intent);
        IntentUtil.copyMMPApiExtras(origIntent, intent);

        intent.putExtra(ContainerController.IS_FUSION_API_STARTED, true);
        return intent;
    }
}
