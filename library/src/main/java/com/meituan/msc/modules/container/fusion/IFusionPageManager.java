package com.meituan.msc.modules.container.fusion;

import static android.content.pm.PackageManager.MATCH_DEFAULT_ONLY;

import android.content.Context;
import android.content.Intent;
import android.content.pm.ResolveInfo;
import android.text.TextUtils;

import com.meituan.msc.common.utils.IntentUtil;
import com.meituan.msc.common.utils.PathUtil;
import com.meituan.msc.extern.MSCEnvHelper;
import com.meituan.msc.lib.interfaces.container.MSCParams;
import com.meituan.msc.modules.container.MSCActivity;
import com.meituan.msc.modules.reporter.MSCLog;
import com.sankuai.common.utils.ProcessUtils;

import java.util.ArrayList;
import java.util.concurrent.ConcurrentHashMap;

/**
 * 融合模式下的页面处理实现
 * <p>
 * Created by letty on 2020/11/21.
 **/
public class IFusionPageManager {

    public static class AppInfo {
        public ArrayList<String> tabList;
        public boolean defaultPageIsTab;
        public boolean isFusion;
    }

    // <appId, AppInfo>
    public static ConcurrentHashMap<String, AppInfo> appTabList = new ConcurrentHashMap<>();

    // 在独立app中，不经过RouterCenterActivity，以此列表来确定是否按融合模式启动
    // <appId, startActivityUrl>
    public static ConcurrentHashMap<String, String> supportStackChangeAppList = new ConcurrentHashMap<>();

    /**
     * 指定独立App在小程序relaunch和switchTab时使用的intent中的url
     * <p>
     * 未注册时将使用HeraActivity类启动Activity，对继承了HeraActivity/RouterCenterActivity的宿主不适用，此情况需要注册
     *
     * @param appId
     * @param url
     */
    public IFusionPageManager addMpContainerSupportStackChange(String appId, String url) {
        if (TextUtils.isEmpty(appId) || TextUtils.isEmpty(url)) {
            return this;
        }
        supportStackChangeAppList.put(appId, url);
        return this;
    }

    public static boolean isFusionModeEnabled(String appId) {
        AppInfo appInfo = appTabList.get(appId);
        if (appInfo != null) {
            return appInfo.isFusion;
        }
        return false;
    }

    public static boolean isMPTabPage(Context context, Intent intent) {
        if (!isStandardModeHeraActivityForCurrProcess(context, intent)) {
            return false;
        }
        String appId = getMPAppID(intent);
        if (TextUtils.isEmpty(appId)) {
            return false;
        }
        AppInfo appInfo = appTabList.get(appId);
        if (appInfo == null) {
            return false;
        }

        String targetPath = IntentUtil.getStringExtra(intent, MSCParams.TARGET_PATH);
        if (TextUtils.isEmpty(targetPath)) {
            return appInfo.defaultPageIsTab;
        } else {
            return appInfo.tabList.contains(PathUtil.getPath(targetPath));
        }
    }

    public static String getMPAppID(Intent intent) {
        String appId = IntentUtil.getStringExtra(intent, MSCParams.APP_ID);
        if (TextUtils.isEmpty(appId)) {
            appId = MSCEnvHelper.getDefaultAppID();
        }
        return appId;
    }

    /**
     * 注意跨进程耗时，关键路径谨慎使用，尽量添加前置过滤
     */
    public static Class<? extends MSCActivity> resolveMSCActivityClassForCurrProcess(Context context, Intent intent) {
        ResolveInfo resolveInfo = context.getPackageManager().resolveActivity(intent, MATCH_DEFAULT_ONLY);
        if (resolveInfo == null || resolveInfo.activityInfo == null) {
            return null;
        }
        try {
            Class<?> activityClass = Class.forName(resolveInfo.activityInfo.name);
            if (MSCActivity.class.isAssignableFrom(activityClass)) {
                if (TextUtils.equals(resolveInfo.activityInfo.processName, ProcessUtils.getCurrentProcessName(context))) {
                    //noinspection unchecked
                    return (Class<? extends MSCActivity>) activityClass;
                }
            }
        } catch (ClassNotFoundException e) {
        }
        return null;
    }

    /**
     * 是小程序传统模式or融合模式对应的页面 & 不是多任务栈模式，且属于本进程，才应处理
     *
     * @param context
     * @param intent
     * @return
     */
    public static boolean isStandardModeHeraActivityForCurrProcess(Context context, Intent intent) {
        Class<?> activityClass = resolveMSCActivityClassForCurrProcess(context, intent);
        return activityClass != null;
    }

    /**
     * 清除同一小程序的activity类至仅剩余一个
     * @param startIntent 用于startActivity的Intent，在暂时无法finish已销毁待重建的Activity时，记录intent，在Activity重建后销毁并起此Intent
     */
    public static void finishActivitiesExceptFirstOne(String appId, Intent startIntent) {
        if (!MSCEnvHelper.isInited()) {
            // 此时一定没有需要进行的操作，且继续执行会出问题
            return;
        }

        MSCFusionActivityMonitor.ActivityRecord targetRecord = null;
        for (MSCFusionActivityMonitor.ActivityRecord record : MSCFusionActivityMonitor.activityRecords) {
            if (TextUtils.equals(record.appId, appId)) {
                // 按从老到新遍历，找到最靠近栈底的一个同小程序activity，即为目标activity
                targetRecord = record;
                break;
            }
        }
        if (targetRecord == null) {
            MSCLog.d("IFusionPageManager", "returnToFirstActivity: no living & reusable activity found for appId ", appId);
            return;
        }

        for (int i = MSCFusionActivityMonitor.activityRecords.size() - 1; i >= 0; i--) {
            MSCFusionActivityMonitor.ActivityRecord record = MSCFusionActivityMonitor.activityRecords.get(i);
            if (record == targetRecord) {
                break;
            }
            if (TextUtils.equals(record.appId, appId)) {
                record.isFinishingForSameMP = true;
            }
            MSCFusionActivityMonitor.needFinishedActivities.put(record, startIntent);

            // 目标之上的同一进程的HeraActivity，全部结束，以避免startActivity时被系统选为回退目标
            MSCActivity activity = record.getActivity();
            if (activity != null && !activity.isDestroyed()) {
                MSCLog.d("clearToFirstActivity: finish activity ", record.activityId, " of appId ", record.appId, " for appId ", appId);
                activity.finish();
            } else {
                // 此情况下要finish的Activity已被销毁，待重建，无法直接finish，先记录，待activity由启动触发重建时再销毁
                MSCLog.d("clearToFirstActivity: activity ", record.activityId, " of appId ", record.appId," already destroyed, will finish it after recreate");
            }
        }
    }



    /**
     * 清除同一小程序的所有activity
     * @param startIntent 用于startActivity的Intent，在暂时无法finish已销毁待重建的Activity时，记录intent，在Activity重建后销毁并起此Intent
     */
    public static void finishAllActivities(String appId, Intent startIntent) {
        if (!MSCEnvHelper.isInited()) {
            // 此时一定没有需要进行的操作，且继续执行会出问题
            return;
        }

        for (int i = MSCFusionActivityMonitor.activityRecords.size() - 1; i >= 0; i--) {
            MSCFusionActivityMonitor.ActivityRecord record = MSCFusionActivityMonitor.activityRecords.get(i);
            if (TextUtils.equals(record.appId, appId)) {
                record.isFinishingForSameMP = true;
            }
            MSCFusionActivityMonitor.needFinishedActivities.put(record, startIntent);

            // 同一进程的HeraActivity，全部结束，以避免startActivity时被系统选为回退目标
            MSCActivity activity = record.getActivity();
            if (activity != null && !activity.isDestroyed()) {
                MSCLog.i("clearAllActivity: finish activity ", record.activityId, " of appId ", record.appId, " for appId ", appId);
                activity.finish();
            } else {
                // 此情况下要finish的Activity已被销毁，待重建，无法直接finish，先记录，待activity由启动触发重建时再销毁
                MSCLog.i("clearAllActivity: activity ", record.activityId, " of appId ", record.appId," already destroyed, will finish it after ",
                        "recreate");
            }
        }
    }



    /**
     * reLaunch处理
     *
     * @param url
     * @param listener
     * @return
     */
    public boolean reLaunch(Context context, String appId, String url, Intent origIntent) {
        return false;
    }

    /**
     * switchTab处理，默认不支持会失败
     *
     * @param url
     * @param listener
     * @return
     */
    public boolean switchTab(Context context, String appId, String url, Intent origIntent) {
        return false;
    }
}
