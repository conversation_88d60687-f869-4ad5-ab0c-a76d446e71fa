package com.meituan.msc.modules.container.fusion;

import android.annotation.SuppressLint;
import android.app.Activity;
import android.content.Intent;
import android.support.annotation.NonNull;
import android.support.annotation.Nullable;
import android.text.TextUtils;

import com.meituan.msc.modules.container.MSCActivity;
import com.meituan.msc.modules.reporter.MSCLog;

import java.lang.ref.WeakReference;
import java.util.List;
import java.util.Map;
import java.util.concurrent.ConcurrentHashMap;
import java.util.concurrent.CopyOnWriteArrayList;

/**
 * Created by letty on 2020/11/26.
 **/
public class MSCFusionActivityMonitor {

    public static class ActivityRecord {
        String appId;
        WeakReference<MSCActivity> activityRef;
        int activityId; // 此为同一任务栈中的第X个HeraActivity
        boolean isFinishingForSameMP;   //正在因回退至同一小程序的之前activity而被销毁

        @Nullable
        public MSCActivity getActivity() {
            return activityRef.get();
        }
    }

    // 认为均处于同一task，涉及独立task时无力处理，只能避免这种情况
    static final List<ActivityRecord> activityRecords = new CopyOnWriteArrayList<>();
    static ActivityRecord currentForegroundRecord = null;

    static final Map<ActivityRecord, Intent> needFinishedActivities = new ConcurrentHashMap<>();

    public static void onActivityResume(@NonNull Activity activity, int activityId) {
        if (activity == null || !MSCActivity.class.isAssignableFrom(activity.getClass())) {
            return;
        }

        for (ActivityRecord record : activityRecords) {
            if (record.activityId == activityId) {
                currentForegroundRecord = record;
                break;
            }
        }
    }

    /**
     * 本进程
     */
    public static String getCurrentForegroundAppIdInThisProcess() {
        MSCLog.i("getCurrentForegroundAppIdInThisProcess"," "+(currentForegroundRecord != null ? currentForegroundRecord.appId : null));
        return currentForegroundRecord != null ? currentForegroundRecord.appId : null;
    }

    /**
     * 需要在activity初始化完成、已可取状态时再调用
     */
    public static void onActivityCreate(@NonNull MSCActivity activity, String appId, int activityId, boolean isReload) {
        ActivityRecord record = new ActivityRecord();
        record.activityRef = new WeakReference<>(activity);
        record.appId = appId;
        record.activityId = activityId;
        activityRecords.add(record);
    }

     @SuppressLint("Iterator") // 这里移除之后就break了 不需要迭代器
    public static void onActivityDestroy(@NonNull MSCActivity activity, int activityId) {
        if (!activity.isFinishing()) {
            return;
        }
        for (ActivityRecord record : activityRecords) {
            if (record.activityId == activityId) {
                activityRecords.remove(record);
                //todo 移除当前栈顶的时候 如何找到下一个栈顶
                if (currentForegroundRecord == record) {
                    currentForegroundRecord = null;
                }
                break;
            }
        }
    }

    public static boolean hasActivityAlive(String appId) {
        return getLivingActivityCount(appId) > 0;
    }

    public static int getLivingActivityCount(String appId) {
        int count = 0;
        for (ActivityRecord record : activityRecords) {
            if (TextUtils.equals(appId, record.appId)) {
                count++;
            }
        }
        return count;
    }

    /**
     * @return 返回intent，如需要finish & start操作则有返回，否则返回null
     */
    public static Intent getFinishAndStartIntent(MSCActivity activity) {
        for (Map.Entry<ActivityRecord, Intent> entry : needFinishedActivities.entrySet()) {
            if (entry.getKey().activityId == activity.getActivityId()) {
                needFinishedActivities.remove(entry.getKey());
                return entry.getValue();
            }
        }
        return null;
    }

    public static boolean isFinishActivityForSameMP(int activityId) {
        for (ActivityRecord record : needFinishedActivities.keySet()) {
            if (record.activityId == activityId && record.isFinishingForSameMP) {
                return true;
            }
        }
        return false;
    }

    public static void markFinishDone(int activityId) {
        for (ActivityRecord record : needFinishedActivities.keySet()) {
            if (record.activityId == activityId) {
                needFinishedActivities.remove(record);
            }
        }
    }
}
