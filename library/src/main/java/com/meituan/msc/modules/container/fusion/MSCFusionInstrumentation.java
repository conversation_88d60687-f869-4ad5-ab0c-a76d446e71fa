package com.meituan.msc.modules.container.fusion;

import android.content.Context;
import android.content.Intent;
import android.support.annotation.Nullable;
import android.text.TextUtils;

import com.meituan.msc.common.utils.IntentUtil;
import com.meituan.msc.lib.interfaces.container.MSCParams;
import com.meituan.msc.modules.container.IntentInstrumentation;
import com.meituan.msc.modules.container.MSCActivity;
import com.meituan.msc.modules.container.MSCPageStateInstance;
import com.meituan.msc.modules.engine.EngineHelper;
import com.meituan.msc.modules.reporter.MSCLog;
import com.meituan.msc.modules.router.MSCInstrumentation;

/**
 * 融合模式switchTab/relaunch回退相关处理
 * <p>
 * Created by letty on 2020/11/23.
 **/
public class MSCFusionInstrumentation extends IntentInstrumentation {

    public static boolean skipActivityReuse = true;

    public MSCFusionInstrumentation(Context context) {
        super(context);
    }

    /**
     * 目前仅在目标进程处理，为避免主进程被杀导致的信息不完全，不在主进程RouterCenterActivity处理对子进程的startActivity
     */
    @Override
    public boolean processIntent(Context context, Intent originalIntent, boolean isStartActivity) {
        MSCPageStateInstance pageStateInstance = MSCPageStateInstance.getInstance();
        pageStateInstance.setPageState(MSCPageStateInstance.NEW_ACTIVITY);
        pageStateInstance.getNextAppIdSet().clear();

        String data = originalIntent.getDataString();
        if (data != null) {
            if (data.contains("msc")) {
                String appId = IFusionPageManager.getMPAppID(originalIntent);
                if (appId != null) {
                    pageStateInstance.getNextAppIdSet().add(appId);
                }
            } else {
                if (MSCInstrumentation.isMeituan()) {
                    return false;
                }
            }
        }

        return Inner.processIntent(context, originalIntent, isStartActivity);
    }

    /**
     * 为避免不必要时加载较复杂的部分，将主要逻辑拆分至静态内部类
     */
    private static class Inner {

        public static boolean processIntent(Context context, Intent originalIntent, boolean isStartActivity) {
            // 此判断较耗时，因此使用上面的条件先过滤掉美团中大部分情况
            Class<? extends MSCActivity> activityClass = IFusionPageManager.resolveMSCActivityClassForCurrProcess(context, originalIntent);
            if (activityClass == null) {
                return false;
            }
            if (processWidgetIntent(context, originalIntent, isStartActivity)) {
                return true;
            }

            boolean isReload = EngineHelper.getReloadStatusOnTestEnv(originalIntent);
            // 独立任务栈目前不支持先别走了
//            if (AppBrandMSCActivity.class.isAssignableFrom(activityClass)) {
//                // 独立任务栈
//                if (isReload) {
//                    originalIntent.addFlags(Intent.FLAG_ACTIVITY_CLEAR_TASK);
//                    return true;
//                }
//                return false;
//            }
            MSCLog.i("MSCInstrumentation", "processIntent" , isStartActivity) ;

            @Nullable String appId = IFusionPageManager.getMPAppID(originalIntent);
            // 默认融合模式
            if (appId != null) {
                int origFlags = originalIntent.getFlags()
                        & (Intent.FLAG_ACTIVITY_CLEAR_TOP | Intent.FLAG_ACTIVITY_SINGLE_TOP);
                int flagsToAdd = 0;

                if (clearTopStartActivityModeByApi(originalIntent, appId)) {
                    return true;
                }

                if (!skipActivityReuse && TextUtils.equals(MSCFusionActivityMonitor.getCurrentForegroundAppIdInThisProcess(), appId)) {
                    // 栈顶是当前小程序的时候以类似singleTop的模式启动
                    // 判断不完全准确，不能识别顶部有其他activity的情况，但多加flag无不良影响，此判断实际只需要避免启动到不同小程序的activity中
                    // 应以此处理替代launchMode=singleTop，因launchMode添加的singleTop无法在需要reload时取消，且按Activity类判断，无法区分不同小程序的Activity
                    // fixme 临时方案：dsp会拦截外链冷启，flag添加之后会作用在dspActivity上 导致MSC页面无法打开；后续让路由侧一起提供解决方案
                    if (IntentUtil.getBooleanExtra(originalIntent, MSCParams.DISABLE_REUSE_ACTIVITY, false)) {
                        MSCLog.i("MSCInstrumentation", "skip add FLAG_ACTIVITY_SINGLE_TOP for top activity for disable reuse activity");
                    } else if (!needSkipSingleTop(context)) {
                        flagsToAdd |= Intent.FLAG_ACTIVITY_SINGLE_TOP;
                        MSCLog.i("MSCInstrumentation", "add FLAG_ACTIVITY_SINGLE_TOP for top activity", isStartActivity);
                    } else {
                        MSCLog.i("MSCInstrumentation", "skip add FLAG_ACTIVITY_SINGLE_TOP for top activity ( from DSP )");
                    }
                }

                // tab或relaunch需要回退，有activity存在才可以回退
                if ((IFusionPageManager.isMPTabPage(context, originalIntent)
                        || IntentUtil.getBooleanExtra(originalIntent, MSCActivity.RELAUNCH, false))
                        && MSCFusionActivityMonitor.hasActivityAlive(appId)) {
                    // 回退至当前存在的包含tab页的HeraActivity
                    // 清空与目标同类型的多余Activity，使要回退到的目标activity是同一类型的Activity中最靠上的一个
                    // 因FLAG_ACTIVITY_CLEAR_TOP是按Activity类型比对的，将退至最上面的一个同类型Activity
                    IFusionPageManager.finishActivitiesExceptFirstOne(appId, originalIntent);

                    // 清空目标之上的其他Activity，不包括目标activity，目标activity将收到onNewIntent
                    // 仅clearTop不加singleTop则将导致目标activity一起被清除后再起
                    flagsToAdd |= Intent.FLAG_ACTIVITY_CLEAR_TOP | Intent.FLAG_ACTIVITY_SINGLE_TOP;
                    MSCLog.i("MSCInstrumentation", "add FLAG_ACTIVITY_CLEAR_TOP | SINGLE_TOP for tab page", isStartActivity);
                }

                if (isReload && (flagsToAdd & Intent.FLAG_ACTIVITY_SINGLE_TOP) != 0) {
                    // 添加singleTop会导致通过onNewIntent复用目标Activity，而不是销毁重建，与reload要求的不复用有冲突，去除
                    MSCLog.i("MSCInstrumentation", "remove FLAG_ACTIVITY_SINGLE_TOP for reload", isStartActivity);
                    flagsToAdd &= ~Intent.FLAG_ACTIVITY_SINGLE_TOP;
                }

                if (origFlags != flagsToAdd) {
                    // 相比原先改动了flag
                    originalIntent.addFlags(flagsToAdd);
                    if (flagsToAdd == Intent.FLAG_ACTIVITY_SINGLE_TOP) {
                        originalIntent.putExtra(MSCParams.REUSE_ACTIVITY, true);
                    }
                    if (!isStartActivity
                            && !originalIntent.getBooleanExtra(MSCActivity.EXTRA_FINISH_AND_START_DONE, false)) {
                        // 已走到newActivity阶段，无法改变启动目标，要改变目标则需要Activity收到intent后自杀并重新startActivity
                        originalIntent.putExtra(MSCActivity.FINISH_AND_START, true);
                    }
                }
                return true;
            }

            return false;
        }

        /**
         * 清除栈底目标appId小程序activity之上的其他页面，由api exitMiniProgram 触发
         * @param originalIntent
         * @param appId
         * @return true 处理过
         */
        private static boolean clearTopStartActivityModeByApi(Intent originalIntent, String appId) {
            if (originalIntent.getBooleanExtra(MSCActivity.FINISH_BY_EXIT_MINIPROGRAM, false)) {
                if (MSCFusionActivityMonitor.hasActivityAlive(appId)) {
                    IFusionPageManager.finishActivitiesExceptFirstOne(appId, originalIntent);
                }
                originalIntent.setFlags(Intent.FLAG_ACTIVITY_CLEAR_TOP);
                originalIntent.putExtra(MSCActivity.FINISH_BY_EXIT_MINIPROGRAM, true);
                return true;
            }
            return false;
        }
    }

    static Class sDspClass = null;
    static boolean needSkipSingleTop(Context context) {
        if (MSCInstrumentation.isMeituan()) {
            if (sDspClass == null) {
                try {
                    sDspClass = Class.forName("com.sankuai.meituan.mbc.dsp.DspActivity");
                } catch (ClassNotFoundException e) {
                    // ignore
                }
            }
            if (sDspClass != null) {
                // skip DSP
                return context.getClass().isAssignableFrom(sDspClass);
            }
        }
        return false;
    }


    /**
     * 内测环境使用 用于方便 widget 测试
     * 使用AOP生效
     *
     * @return
     */
    public static boolean processWidgetIntent(Context context, Intent originalIntent, boolean isStartActivity) {
        return false;
    }
}
