package com.meituan.msc.modules.container.mmpoffline;

import android.content.Intent;
import android.net.Uri;
import android.os.Bundle;
import android.view.View;
import android.widget.ImageView;
import android.widget.LinearLayout;
import android.widget.TextView;

import com.meituan.msc.lib.R;
import com.meituan.msc.modules.container.MSCBaseActivity;

/**
 * 类描述：MMP 下线兜底页
 * 作者：xiaoyunfei
 * 创建时间：2024/11/13
 */
public class MMPOfflineHintActivity extends MSCBaseActivity {

    @Override
    protected void onCreate(Bundle savedInstanceState) {
        super.onCreate(savedInstanceState);
        setContentView(R.layout.msc_load_error_release);
        findViewById(R.id.load_fail_btn_container).setVisibility(View.GONE);
        ImageView logo = findViewById(R.id.msc_load_failed_logo);
        TextView title = findViewById(R.id.msc_load_failed_title);
        TextView subTitle = findViewById(R.id.msc_load_failed_subtitle);
        logo.setImageResource(R.drawable.pic_empty_state_no_order_v2);
        title.setText("服务已下线");
        subTitle.setText("服务已下线，请联系官方客服");
        Intent intent = getIntent();
        Uri uri = intent.getData();
        if (uri != null) {
            String mmpAppName = uri.getQueryParameter("mmpAppName");
            subTitle.setText(mmpAppName + "服务已下线，请联系官方客服");
        }

    }
}
