/*
 * Copyright (c) Facebook, Inc. and its affiliates.
 *
 * This source code is licensed under the MIT license found in the
 * LICENSE file in the root directory of this source tree.
 */

package com.meituan.msc.modules.core;

import android.net.Uri;

import com.meituan.msc.jse.bridge.UiThreadUtil;
import com.meituan.msc.jse.modules.core.DefaultHardwareBackBtnHandler;
import com.meituan.msc.jse.modules.core.JSDeviceEventEmitter;
import com.meituan.msc.modules.engine.AppService;
import com.meituan.msc.modules.manager.MSCMethod;
import com.meituan.msc.modules.manager.MSCModule;
import com.meituan.msc.modules.manager.ModuleName;

import org.json.JSONException;
import org.json.JSONObject;

/**
 * Native module that handles device hardware events like hardware back presses.
 */

@ModuleName(name = "DeviceEventManager")
public class DeviceEventManagerModule extends MSCModule implements DeviceEventManagerInterface{
    private final Runnable mInvokeDefaultBackPressRunnable;

    public DeviceEventManagerModule() {
        mInvokeDefaultBackPressRunnable =
                new Runnable() {
                    @Override
                    public void run() {
                        UiThreadUtil.assertOnUiThread();
                        AppService appService = getRuntime().getModule(AppService.class);
                        if (appService == null) {
                            return;
                        }
                        DefaultHardwareBackBtnHandler backBtnHandler = getRuntime().getModule(AppService.class).getJsExecutor();
                        if (backBtnHandler != null) {
                            backBtnHandler.invokeDefaultOnBackPressed();
                        }
                    }
                };
    }

    /**
     * Sends an event to the JS instance that the hardware back has been pressed.
     */
    @Override
    public void emitHardwareBackPressed() {
        getRuntime().getJSModuleDelegate(JSDeviceEventEmitter.class)
                .emit("hardwareBackPress", null);
    }

    /**
     * Sends an event to the JS instance that a new intent was received.
     */
    public void emitNewIntentReceived(Uri uri) {
        JSONObject map = new JSONObject();
        try {
            map.put("url", uri.toString());
        } catch (JSONException e) {
            e.printStackTrace();
        }
        getRuntime().getJSModuleDelegate(JSDeviceEventEmitter.class).emit("url", map);
    }

    /**
     * Invokes the default back handler for the host of this catalyst instance. This should be invoked
     * if JS does not want to handle the back press itself.
     */
    @MSCMethod
    public void invokeDefaultBackPressHandler() {
        // There should be no need to check if the catalyst instance is alive. After initialization
        // the thread instances cannot be null, and scheduling on a thread after ReactApplicationContext
        // teardown is a noop.
        UiThreadUtil.runOnUiThread(mInvokeDefaultBackPressRunnable);
    }

}
