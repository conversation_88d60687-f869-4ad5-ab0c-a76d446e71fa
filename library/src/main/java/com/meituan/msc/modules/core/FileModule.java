package com.meituan.msc.modules.core;

import android.support.annotation.Nullable;
import android.text.TextUtils;

import com.meituan.dio.easy.DioFile;
import com.meituan.msc.common.executor.MSCExecutors;
import com.meituan.msc.common.utils.FileUtil;
import com.meituan.msc.common.utils.MSCStorageUtil;
import com.meituan.msc.extern.MSCEnvHelper;
import com.meituan.msc.lib.interfaces.IFileModule;
import com.meituan.msc.modules.debug.SourceCodeModule;
import com.meituan.msc.modules.engine.MSCHornRollbackConfig;
import com.meituan.msc.modules.manager.MSCModule;
import com.meituan.msc.modules.manager.ModuleName;
import com.meituan.msc.modules.reporter.MSCLog;
import com.meituan.msc.modules.update.JSResourceData;

import java.io.File;
import java.util.Arrays;
import java.util.List;

/**
 * Created by letty on 2022/2/10.
 **/
@ModuleName(name = FileModule.TAG)
public class FileModule extends MSCModule implements IFileModule {
    static final String TAG = "FileModule";
    public static final String EXTRA_KEY_MMP_APP_ID = "mmpAppId";
    public static final String SP_FORMAT = "%s_%s%s";
    // 2025.4.23 沟通MSI最晚12.38.200上线绝对路径文件读写能力, 上线后MSC可删除兜底代码
    // MSC继续对IM小程序提供兜底能力, mscAppId拓展至3个:IM到综, IM闪购, IM医药O2O
    public static final List<String> IM_APP_ID_LIST = Arrays.asList("d783efcbad0f4cd9", "a3fa86343f3b4159", "6623a8ce375b4889");

    private String appId;
    private volatile String mmpAppId;
    // MMP、MSC 存储互通 https://km.sankuai.com/collabpage/1573282270
    private volatile boolean isCompatMMP;

    @Override
    public boolean canRead(String path) {
        return true;
    }

    @Override
    public boolean canWrite(String path) {
        return true;
    }

    public boolean isSupportScheme(String scheme) {
        return TextUtils.equals(scheme, MSCStorageUtil.SCHEME_RAW_MSC_FILE) ||
                TextUtils.equals(scheme, MSCStorageUtil.SCHEME_RAW_WDFILE);
    }

    @Override
    public String getAbsolutePath(String path) {
        if (path == null) {
            return null;
        }
        // 包内资源获取 https://km.sankuai.com/page/1275086459?kmId=1275086459&linkType=KM
        if (path.startsWith(SourceCodeModule.PREFIX)) {
            DioFile file = getRuntime()
                    .getMSCAppModule()
                    .getResourcePath(path.substring((SourceCodeModule.PREFIX + getRuntime().getAppId()).length()));
            if (file != null && file.exists()) {
                return file.getAbsolutePath();
            } else {
                MSCLog.w(getName(), "file not exists", path);
            }
        } else {
            // IM 特殊处理, 短期方案(https://km.sankuai.com/collabpage/2650403741), 2025.3 MSI侧改动上线后要删除本次提交代码.
            if (MSCHornRollbackConfig.enableIMGetAbsolutePath() && IM_APP_ID_LIST.contains(getAppId())) {
                return FileUtil.getResourcePath(path, this, true);
            }
            return FileUtil.getResourcePath(path, this);
        }
        return null;
    }

    @Nullable
    public DioFile getPackageResourceFile(String url) {
        JSResourceData jsResourceData = getRuntime()
                .getMSCAppModule().getResourcePathWithPackageInfo(url);
        if (jsResourceData == null) {
            return null;
        }
        return jsResourceData.jsResourceFile;
    }

    /**
     * 获取当前小程序的文件存储目录的绝对路径
     *
     * @return 小程序资源存储的路径
     */
    public String getMiniAppStorePath() {
        return MSCStorageUtil.getMiniAppStoreDir(getMiniAppDir()).getAbsolutePath() + MSCStorageUtil.SEPARATOR;
    }

    /**
     * 获取当前小程序的用户数据的绝对路径
     *
     * @return 小程序资源存储的路径
     */
    public String getMiniAppUserDataPath() {
        return MSCStorageUtil.getMiniAppUsrDir(getMiniAppDir()).getAbsolutePath() + MSCStorageUtil.SEPARATOR;
    }

    /**
     * 获取当前小程序的临时文件存储目录的绝对路径
     *
     * @return 小程序临时文件存储路径
     */
    public String getMiniAppTempPath() {
        return MSCStorageUtil.getMiniAppTempDir(getMiniAppDir()).getAbsolutePath() + MSCStorageUtil.SEPARATOR;
    }


    public File getMiniAppDir() {
        return MSCStorageUtil.getMiniAppDir(isCompatMMP ?
                        MSCStorageUtil.getHeraPath(MSCEnvHelper.getContext()) :
                        MSCStorageUtil.getMSCPath(MSCEnvHelper.getContext()),
                getCompatAppId());
    }

    @Override
    public String getContainerFileScheme() {
        return (isCompatMMP ? MSCStorageUtil.SCHEME_WDFILE : MSCStorageUtil.SCHEME_MSC_FILE);
    }

    /**
     * 获取 sp路径，优先获取用户维度的存储空间名称
     *
     * @return
     */
    @Override
    public String getSharePreferencesPath() {
        String prefix = isCompatMMP ? PREFIX_MMP : PREFIX_MSC;
        return String.format(SP_FORMAT, prefix, getCompatAppId(),
                !TextUtils.isEmpty(MSCEnvHelper.getEnvInfo().getUserID()) ? MSCEnvHelper.getEnvInfo().getUserID() : "");
    }

    @Override
    public void setCompatMMPAppId(String appId) {
        if (TextUtils.isEmpty(appId)) {
            mmpAppId = null;
            isCompatMMP = false;
        } else {
            mmpAppId = appId;
            isCompatMMP = true;
        }
        MSCLog.i(TAG, "setCompatMMPAppId，mscAppID: ", getAppId(), " mmpId:", appId);
    }

    private String getCompatAppId() {
        return isCompatMMP ? mmpAppId : getAppId();
    }

    private String getAppId() {
        if (appId == null) {
            appId = getRuntime().getAppId();
        }
        return appId;
    }

    @Override
    public void destroy() {
        super.destroy();
        MSCExecutors.submit(new Runnable() {
            @Override
            public void run() {
                FileUtil.deleteFile(getMiniAppTempPath());
                MSCStorageUtil.clearMiniAppUsrDirTmpFile(appId, MSCStorageUtil.getMiniAppUsrDir(getMiniAppDir()));
            }
        });
    }

}
