package com.meituan.msc.modules.core;


import android.content.res.AssetManager;
import android.graphics.Typeface;
import android.support.annotation.Nullable;
import android.util.SparseArray;

import com.meituan.msc.lib.interfaces.IFontfaceModule;
import com.meituan.msc.modules.manager.MSCModule;
import com.meituan.msc.modules.manager.ModuleName;

import java.util.HashMap;
import java.util.Map;
import java.util.concurrent.ConcurrentHashMap;

@ModuleName(name = FontfaceModule.TAG)
public class FontfaceModule extends MSCModule implements IFontfaceModule {
    static final String TAG = "FontfaceModule";

    private static final String[] FILE_EXTENSIONS = {".ttf", ".otf"};
    private static final String FONTS_ASSET_PATH = "fonts/";
    private final Map<String, Typeface> mFontCache;

    public FontfaceModule() {
        mFontCache = new ConcurrentHashMap<>();
    }

    /**
     * Add additional font family, or replace the exist one in the font memory cache.
     */
    @Override
    public void setTypeface(String fontFamilyName, Typeface typeface) {
        if (typeface != null) {
            mFontCache.put(fontFamilyName, typeface);
        }
    }

    @Override
    public @Nullable Typeface getTypeface(
            String fontFamilyName, int style, AssetManager assetManager) {
        Typeface typeface = mFontCache.get(fontFamilyName);
        if (typeface == null) {
            typeface = createTypeface(fontFamilyName, style, assetManager);
            mFontCache.put(fontFamilyName, typeface);
        }
        return typeface;
    }

    private static @Nullable Typeface createTypeface(
            String fontFamilyName, int style, AssetManager assetManager) {
        for (String fileExtension : FILE_EXTENSIONS) {
            String fileName =
                    new StringBuilder()
                            .append(FONTS_ASSET_PATH)
                            .append(fontFamilyName)
                            .append(fileExtension)
                            .toString();
            try {
                return Typeface.createFromAsset(assetManager, fileName);
            } catch (RuntimeException e) {
                // unfortunately Typeface.createFromAsset throws an exception instead of returning null
                // if the typeface doesn't exist
            }
        }

        return Typeface.create(fontFamilyName, style);
    }
}
