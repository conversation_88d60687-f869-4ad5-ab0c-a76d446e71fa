/*
 * Copyright (c) Facebook, Inc. and its affiliates.
 *
 * This source code is licensed under the MIT license found in the
 * LICENSE file in the root directory of this source tree.
 */

package com.meituan.msc.modules.debug;

import com.meituan.msc.modules.manager.MSCModule;
import com.meituan.msc.modules.manager.ModuleName;

import org.json.JSONObject;

import javax.annotation.Nullable;

/**
 * Module that exposes the URL to the source code map (used for exception stack trace parsing) to JS
 */

@ModuleName(name = "SourceCode")
public class SourceCodeModule extends MSCModule {
    public static final String PREFIX = "mscfile://msc_";

    public SourceCodeModule() {
        super();
    }

    @Override
    public final @Nullable
    JSONObject getConstants() {
        return getTypedExportedConstants();
    }

    protected JSONObject getTypedExportedConstants() {
        JSONObject constants = new JSONObject();
        try {
            String sourceURL = PREFIX + getRuntime().getApp().getAppId();
            constants.put("scriptURL", sourceURL);
        } catch (Throwable e) {
            e.printStackTrace();
        }
        return constants;
    }
}
