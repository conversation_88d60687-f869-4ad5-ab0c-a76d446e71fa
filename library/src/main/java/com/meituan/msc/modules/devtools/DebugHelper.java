package com.meituan.msc.modules.devtools;

import android.support.annotation.IntDef;

import com.meituan.msc.extern.MSCEnvHelper;
import com.meituan.msc.modules.reporter.MSCLog;

import java.lang.annotation.Retention;
import java.lang.annotation.RetentionPolicy;

/**
 * 内部开发者模式帮助类
 */
public class DebugHelper {
    public static final int UNINITIALIZED = -1;
    public static final int DEFAULT_MODE = 0;
    public static final int FORCE_MODE = 1;
    public static final int FORBIDDEN_MODE = 2;

    public static final String MSC_ENABLE_V8_INSPECTOR = "debug_v8_inspector";
    /**
     * @api 组件标准化注释_标准API
     * 标识小程序开发者模式开关状态
     */
    private static boolean isDebug = false;  //小程序debug开关，在美团的调试面板可打开
    /**
     * @api 组件标准化注释_标准API
     * 标识WebView调试状态
     */
    public static boolean debugWebView = false;
    public static boolean showH5ErrorToast = true;
    public static boolean keepCachedVersion = false; //正常启动不检查更新，用于锁包，带checkUpdateUrl仍会更新
    public static boolean forceWidget = false;
    public static Boolean forceMultiAppBrand = null;
    public static boolean enableV8Inspect = false;
    public static boolean forceSameLayerErrorDowngrade = false;
    public static boolean debugMap = false;
    public static boolean navigateInWidget = false;   //在Widget模式内尽可能走到小程序的各页面
    public static boolean ignoreWidgetApiFail = false;   //避免未适配的小程序因Widget下api失败而无法运行
    public static boolean mockPrefetchSubPkgFailed = false;
    public static boolean forceWebViewService = false;

    public static Long keepAliveTime;

    @IntDef({UNINITIALIZED, DEFAULT_MODE, FORCE_MODE, FORBIDDEN_MODE})
    @Retention(RetentionPolicy.SOURCE)
    public @interface MultiProcessMode {
    }

    @MultiProcessMode
    private static int forceMPMode = UNINITIALIZED;

    public static boolean traceJsEval = false;

    public static Boolean useMtWebView;

    public static void throwNotProd(Throwable t) {
        if (MSCEnvHelper.getEnvInfo().isProdEnv()) {
            MSCLog.e("throwNotProd", t);
        } else {
            throw new RuntimeException(t);
        }
    }

    public static void setDebug(boolean debug) {
        isDebug = debug;
    }

    /**
     * @api
     * 组件标准化注释_标准API
     * 获取开发者模式开关状态
     * @return 开关状态
     */
    /*
     * 此函数来判断是否是debug版本，建议使用isDebug判断版本时，使用此函数判断
     * 美团中isDebug赋值的代码
     * http://git.sankuai.com/projects/ANDROID/repos/developer-tools/browse/devtools/src/main/java/com/sankuai/meituan/dev/rn/DevPanelBridge.java
     */
    public static boolean isDebug() {
        return isDebug;
    }

    public static boolean isDebugWebView() {
        return debugWebView;
    }

    public static int getForceMultiProcessMode() {
        return forceMPMode;
    }

    public static void setForceMPMode(int forceMPMode) {
        DebugHelper.forceMPMode = forceMPMode;
    }

    public static boolean getUseMtWebView() {
        return useMtWebView != null && useMtWebView;
    }
}