package com.meituan.msc.modules.devtools;

import com.sankuai.meituan.serviceloader.ServiceLoader;

import java.util.List;

/**
 * <AUTHOR>
 * @since 2021/5/29.
 */

public class DevToolsLoader {
    public static final String DEVTOOLS_SERVICE_NAME = "msc_devtools_provider";

    private static IDevToolsProvider mDevToolsProvider;

    public synchronized static IDevToolsProvider getDevToolsProvider() {
        if (mDevToolsProvider == null) {
            List<IDevToolsProvider> loadedProviders = ServiceLoader.load(IDevToolsProvider.class, DEVTOOLS_SERVICE_NAME);
            mDevToolsProvider = (loadedProviders != null && loadedProviders.size() > 0) ? loadedProviders.get(0) : null;
        }
        return mDevToolsProvider;
    }
}
