package com.meituan.msc.modules.devtools;

import android.content.Context;

import com.sankuai.meituan.retrofit2.Interceptor;

import org.json.JSONObject;

public interface IDebugger {
    void connect();

    boolean isConnected();

    void onAppRoute(String openType, String pagePath);

    void close(String source);

    void pushPage(String url, String appId, String openType, boolean isWidget);

    void popPage();

    boolean liveDebugEnabled();

    JSONObject sendMessage(JSONObject params);

    Interceptor getDevInterceptor(Context context);
}
