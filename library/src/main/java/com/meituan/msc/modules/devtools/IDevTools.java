package com.meituan.msc.modules.devtools;

import android.content.Context;

import com.sankuai.meituan.retrofit2.Interceptor;

import org.json.JSONObject;

/**
 * <AUTHOR>
 * @since 2021/5/28.
 * 开发者工具ide
 */

public interface IDevTools {

    void connect();

    void close(boolean exitDevtools);

    void onFirstRender();

    Interceptor getDevInterceptor(Context context);

    void onAppRoute(JSONObject params);

    void onAppServiceCreated(JSONObject params);

    void onPageStart(JSONObject params);

    void onAppEnterForeground(JSONObject params);

    void updateStorageItem(String key, String oldValue, Object data, String type);

    void removeStorageItem(String key);

    void clearStorage();
}
