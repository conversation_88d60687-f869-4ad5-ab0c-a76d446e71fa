package com.meituan.msc.modules.devtools;

import android.content.Context;
import android.content.Intent;

import com.meituan.msc.modules.engine.MSCApp;
import com.meituan.msc.modules.engine.MSCRuntime;

/**
 * <AUTHOR>
 * @since 2021/5/28.
 */

public interface IDevToolsProvider {
    IDevTools createDevTools(Context context, MSCRuntime runtime, Intent intent);
    void removeDevTools(MSCRuntime runtime);
}
