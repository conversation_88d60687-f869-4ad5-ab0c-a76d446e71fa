package com.meituan.msc.modules.devtools;

import android.content.Context;

import com.meituan.msc.modules.reporter.PerformanceListener;

/**
 * 性能调试面板
 */
public interface IPerformanceManager extends PerformanceListener {

    void showView(Context context, String appId, boolean isLaunch);

    void closeView(Context context, String appId, boolean activityDestroy);

    void reportData(String type, long value);

    void updateFrameData(String type, long frameData);

    void updateFileCache(long cacheData);

    boolean isPerformanceManagerOpened();
}
