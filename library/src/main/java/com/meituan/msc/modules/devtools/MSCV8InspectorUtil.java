package com.meituan.msc.modules.devtools;

import android.content.Context;
import android.content.SharedPreferences;
import android.preference.PreferenceManager;
import android.support.annotation.Keep;

import com.meituan.android.soloader.DirectorySoSource;
import com.meituan.android.soloader.SoLoader;
import com.meituan.android.soloader.SoSource;
import com.meituan.msc.common.utils.CIPStorageFileUtil;

import java.io.File;
import java.io.FileOutputStream;
import java.io.IOException;
import java.io.InputStream;
import java.io.OutputStream;

@Keep
public class MSCV8InspectorUtil {

    public static void initV8DebugSo(Context context) {
        if (context == null) {
            return;
        }
        SharedPreferences sp = PreferenceManager.getDefaultSharedPreferences(context);
        if (DebugHelper.enableV8Inspect || sp.getBoolean(DebugHelper.MSC_ENABLE_V8_INSPECTOR, false)) {
            SoLoader.init(context, false);
            enableV8Inspector(context);
        }
    }

    private static volatile boolean sPreparedAppLibSoSource = false;

    private static void enableV8Inspector(Context mContext) {
        File soDirectory = getWorkaroundLibDir(mContext);
        // 将当前的so加载方式添加到正常的so加载方式里，只需要调用一次，不然会添加很多遍
        if (!sPreparedAppLibSoSource) {
            DirectorySoSource soSource = new DirectorySoSource(soDirectory,
                    SoSource.LOAD_FLAG_ALLOW_IMPLICIT_PROVISION);
            try {
                SoLoader.prependSoSource(soSource);
            } catch (IOException e) {
                e.printStackTrace();
            }
            sPreparedAppLibSoSource = true;
        }
        try {
            doCopy(mContext, "v8inspector");
        } catch (Throwable e) {
            e.printStackTrace();
        }

//        SoLoader.loadLibrary(clearLibrarySo("libv8.mt.so"));
        SoLoader.loadLibrary(clearLibrarySo("libmtv8.so"));
    }

    public static void doCopy(Context context, String assetsPath) throws IOException {
        String desPath = getWorkaroundLibDir(context).getAbsolutePath();

        String[] srcFiles = context.getAssets().list(assetsPath);//for directory
        for (String srcFileName : srcFiles) {
            String outFileName = desPath + File.separator + srcFileName;
            String inFileName = assetsPath + File.separator + srcFileName;
            if (assetsPath.equals("")) {// for first time
                inFileName = srcFileName;
            }
            try {
                InputStream inputStream = context.getAssets().open(inFileName);
                OutputStream out = new FileOutputStream(outFileName);

                byte[] buffer = new byte[1024];
                int n = 0;
                while (-1 != (n = inputStream.read(buffer))) {
                    out.write(buffer, 0, n);
                }
                try {
                    if (inputStream != null) {
                        inputStream.close();
                    }
                } catch (IOException ex) {
                    ex.printStackTrace();
                }
                try {
                    if (out != null) out.close();
                } catch (IOException ex) {
                    ex.printStackTrace();
                }

            } catch (IOException e) {//if directory fails exception
                e.printStackTrace();
                new File(outFileName).mkdir();
                doCopy(context, inFileName);
            }
        }
    }

    private static File getWorkaroundLibDir(final Context context) {
        return CIPStorageFileUtil.getFilesDir(context, APP_LIB_DIR);
    }


    private static String clearLibrarySo(final String libraryName) {
        if (libraryName.startsWith("lib") && libraryName.endsWith(".so")) {
            // Already mapped
            int endIndex = libraryName.indexOf(".so");
            return libraryName.substring(3, endIndex);
        }

        return libraryName;
    }

    private static final String APP_LIB_DIR = "debug_lib";
}
