package com.meituan.msc.modules.devtools;

import com.sankuai.meituan.serviceloader.ServiceLoader;

import java.util.List;

public class PerformanceManagerLoader {
    public static final String PERFORMANCE_SERVICE_NAME = "msc_performance_provider";
    private static IPerformanceManagerProvider mPerformanceProvider;

    public static synchronized IPerformanceManagerProvider getProvider() {
        if (mPerformanceProvider == null) {
            List<IPerformanceManagerProvider> loadedProviders = ServiceLoader.load(IPerformanceManagerProvider.class, PERFORMANCE_SERVICE_NAME);
            mPerformanceProvider = (loadedProviders != null && loadedProviders.size() > 0) ? loadedProviders.get(0) : null;
        }
        return mPerformanceProvider;
    }
}
