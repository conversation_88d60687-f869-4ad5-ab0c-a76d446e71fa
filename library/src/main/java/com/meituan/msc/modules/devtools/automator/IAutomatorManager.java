package com.meituan.msc.modules.devtools.automator;

import android.content.Context;
import android.support.annotation.NonNull;
import android.support.annotation.Nullable;

import com.meituan.msc.modules.engine.MSCRuntime;

public interface IAutomatorManager {

    /**
     * 链接
     * @param context
     * @param automatorServer  自动化服务地址
     * @param automatorDownloadUrl  自动化测试能力包地址
     */
    void connectSocket(Context context, @NonNull String automatorServer, @NonNull String automatorDownloadUrl);

    boolean sendMessage(String text);

    void addPageOperateCallback(@Nullable AutomatorScriptInjectCallback callback);

    void addServiceOperateCallback(@NonNull String appId, @Nullable ServiceOperateCallback callback);

    boolean isConnect();

    void registerModule(MSCRuntime mRuntime);

    void clearPageOperateCallback();
}
