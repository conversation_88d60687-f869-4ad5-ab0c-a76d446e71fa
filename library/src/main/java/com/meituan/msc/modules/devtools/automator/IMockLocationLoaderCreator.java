package com.meituan.msc.modules.devtools.automator;

import android.support.annotation.NonNull;

import com.meituan.msc.modules.api.map.ILocationLoader;
import com.meituan.msi.location.IMsiLocationLoader;
import com.meituan.msi.provider.LocationLoaderConfig;

/**
 * MockLocationLoader接口
 *
 * <AUTHOR>
 * @date 2021/9/23.
 */
public interface IMockLocationLoaderCreator {

    @NonNull
    ILocationLoader createMockLocationLoader(@NonNull ILocationLoader locationLoader,
                                             @NonNull LocationLoaderConfig locationLoaderConfig);

    @NonNull
    IMsiLocationLoader createMsiMockLocationLoader(@NonNull IMsiLocationLoader msiLocationLoader,
                                                   @NonNull LocationLoaderConfig locationLoaderConfig);
}