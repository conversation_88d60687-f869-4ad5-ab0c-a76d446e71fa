package com.meituan.msc.modules.devtools.automator;

import android.support.annotation.Nullable;

/**
 * <AUTHOR>
 * @date 2021/9/14.
 */
public class LocationMockData {

    /**
     * 持续定位Mock数据
     * {
     *   "id": "4cb50887-cff0-42ad-9bdb-ed8d4d257475",
     *   "method": "MSCNative.setLocationSequence",
     *   "params": {
     *     "locations":[
     *       {
     *       "latitude": 39.9219,
     *       "longitude": 116.44355,
     *       "altitude": 0,
     *       "speed": -1,
     *       "accuracy": 0,
     *       "verticalAccuracy": 0,
     *       "horizontalAccuracy": 0,
     *        "provider": "" // gps/wifi/network/unkonwn
     *     },{
     *       "latitude": 39.9219,
     *       "longitude": 116.44355,
     *       "altitude": 0,
     *       "speed": -1,
     *       "accuracy": 0,
     *       "verticalAccuracy": 0,
     *       "horizontalAccuracy": 0,
     *        "provider": "" // gps/wifi/network/unkonwn
     *     }
     *     ]
     *   }
     * }
     */
    @Nullable
    public static volatile String sMockLocationArrayData;
    /**
     * 单次定位Mock数据
     * {
     *   "id": "4cb50887-cff0-42ad-9bdb-ed8d4d257475",
     *   "method": "MSCNative.mockLocation",
     *   "params": {
     *     "latitude": 39.9219,
     *     "longitude": 116.44355,
     *     "altitude": 0,
     *     "speed": -1,
     *     "accuracy": 0,
     *     "verticalAccuracy": 0,
     *     "horizontalAccuracy": 0,
     *     "provider" // gps/wifi/network/unkonwn
     *   }
     * }
     *
     */
    @Nullable
    public static volatile String sMockLocationData;

}
