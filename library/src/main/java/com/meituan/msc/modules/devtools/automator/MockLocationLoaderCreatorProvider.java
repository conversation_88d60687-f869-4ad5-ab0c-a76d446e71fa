package com.meituan.msc.modules.devtools.automator;

import android.support.annotation.Nullable;

import com.sankuai.meituan.serviceloader.ServiceLoader;

import java.util.List;

/**
 * <AUTHOR>
 * @date 2021/9/24.
 */
public class MockLocationLoaderCreatorProvider {

    @Nullable
    public static IMockLocationLoaderCreator create() {
        List<IMockLocationLoaderCreator> mockLocationLoaders =
                ServiceLoader.load(IMockLocationLoaderCreator.class, "mock_location_loader_creator");
        if (mockLocationLoaders == null || mockLocationLoaders.isEmpty()) {
            return null;
        }
        return mockLocationLoaders.get(0);
    }
}