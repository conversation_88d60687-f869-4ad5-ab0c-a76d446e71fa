package com.meituan.msc.modules.devtools.debug;

import android.content.Context;
import android.util.AttributeSet;
import android.widget.ListView;

/*
 *  自适应高度的ListView，（通过自定义onMeasure指定测量类型AT_MOST，解决listView嵌套listView，item高度不固定显示不全问题）
 */
public class SelfAdaptiveHeightListView extends ListView {

    public SelfAdaptiveHeightListView(Context context) {
        super(context);
    }

    public SelfAdaptiveHeightListView(Context context, AttributeSet attrs) {
        super(context, attrs);
    }

    public SelfAdaptiveHeightListView(Context context, AttributeSet attrs, int defStyle) {
        super(context, attrs, defStyle);
    }

    @Override
    protected void onMeasure(int widthMeasureSpec, int heightMeasureSpec) {
        int expandSpec = MeasureSpec.makeMeasureSpec(Integer.MAX_VALUE >> 2, MeasureSpec.AT_MOST);
        super.onMeasure(widthMeasureSpec, expandSpec);
    }
}
