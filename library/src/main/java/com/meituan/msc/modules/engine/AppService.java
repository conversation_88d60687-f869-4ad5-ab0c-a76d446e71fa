package com.meituan.msc.modules.engine;

import com.meituan.msc.modules.manager.ModuleName;
import com.meituan.msc.modules.page.render.webview.OnEngineInitFailedListener;
import com.meituan.msc.modules.reporter.memory.JSMemoryHelper;
import com.meituan.msc.modules.service.JSCServiceEngine;


/**
 * appservice层，小程序运行的基石，即framework的运行时
 */

@ModuleName(name = "Service")
public class AppService extends BaseJSEngineServiceModule<JSCServiceEngine> {

    public AppService(OnEngineInitFailedListener onEngineInitFailedListener) {
        super(onEngineInitFailedListener);
    }

    @Override
    protected JSCServiceEngine newJSCServiceEngine() {
        return new JSCServiceEngine();
    }

    @Override
    public void onDestroy() {
        super.onDestroy();
        if (engine != null) {
            JSMemoryHelper.clearPageMemCache(getRuntime().getAppId());
        }
    }
}
