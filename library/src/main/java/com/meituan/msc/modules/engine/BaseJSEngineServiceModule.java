package com.meituan.msc.modules.engine;

import static com.meituan.msc.common.perf.PerfEventConstant.IMPORT_SCRIPTS;

import android.content.Context;
import android.support.annotation.NonNull;
import android.support.annotation.Nullable;
import android.text.TextUtils;
import android.webkit.ValueCallback;

import com.meituan.dio.easy.DioFile;
import com.meituan.msc.common.framework.MPListenerManager;
import com.meituan.msc.common.perf.PerfEventConstant;
import com.meituan.msc.common.utils.CollectionUtil;
import com.meituan.msc.common.utils.JsonUtil;
import com.meituan.msc.common.utils.MPConcurrentHashMap;
import com.meituan.msc.extern.MSCEnvHelper;
import com.meituan.msc.jse.bridge.ICallFunctionContext;
import com.meituan.msc.jse.bridge.JSInstance;
import com.meituan.msc.jse.bridge.JavaScriptModule;
import com.meituan.msc.jse.bridge.LoadJSCodeCacheCallback;
import com.meituan.msc.modules.apploader.IAppLoader;
import com.meituan.msc.modules.apploader.events.AppLoadException;
import com.meituan.msc.modules.devtools.automator.AutomatorManagerLoader;
import com.meituan.msc.modules.devtools.automator.IAutomatorManager;
import com.meituan.msc.modules.devtools.automator.ServiceOperateCallback;
import com.meituan.msc.modules.engine.async.IBaseEngineFunctionProvider;
import com.meituan.msc.modules.engine.async.ImportAsyncStrategyProvider;
import com.meituan.msc.modules.manager.ExecutorContext;
import com.meituan.msc.modules.manager.IMSCCompletableCallback;
import com.meituan.msc.modules.manager.MSCEvent;
import com.meituan.msc.modules.manager.MSCMethod;
import com.meituan.msc.modules.manager.MSCModule;
import com.meituan.msc.modules.manager.MSCRuntimeException;
import com.meituan.msc.modules.page.render.webview.OnEngineInitFailedListener;
import com.meituan.msc.modules.reporter.MSCLoadErrorConstants;
import com.meituan.msc.modules.reporter.MSCLog;
import com.meituan.msc.modules.reporter.MSCReporter;
import com.meituan.msc.modules.reporter.preformance.PerformanceManager;
import com.meituan.msc.modules.service.IEngineMemoryHelper;
import com.meituan.msc.modules.service.ILaunchJSEngineCallback;
import com.meituan.msc.modules.service.IServiceEngine;
import com.meituan.msc.modules.service.MSCFileUtils;
import com.meituan.msc.modules.service.ServiceInstance;
import com.meituan.msc.modules.service.codecache.CodeCacheConfig;
import com.meituan.msc.modules.service.codecache.CodeCacheManager;
import com.meituan.msc.modules.update.JSFileSourceHelper;
import com.meituan.msc.modules.update.JSResourceData;
import com.meituan.msc.modules.update.PackageLoadReporter;
import com.meituan.msc.modules.update.bean.PackageInfoWrapper;
import com.meituan.msc.modules.update.packageattachment.LruCleaner;
import com.meituan.msc.modules.update.pkg.PackageLoadManager;
import com.meituan.msc.util.perf.PerfTrace;

import org.json.JSONArray;
import org.json.JSONException;
import org.json.JSONObject;

import java.io.PrintWriter;
import java.io.StringWriter;
import java.util.ArrayList;
import java.util.List;
import java.util.concurrent.ConcurrentHashMap;


/**
 * appservice层，小程序运行的基石，即framework的运行时
 */
public abstract class BaseJSEngineServiceModule<E extends IServiceEngine> extends MSCModule implements IAppService<E>, Thread.UncaughtExceptionHandler, IBaseEngineFunctionProvider {

    private static final String TAG = "BaseJSEngineServiceModule";

    protected final Context mApplicationContext;
    protected E engine;
    private final List<String> loadedPackages = new ArrayList<>();
    private volatile boolean isFrameworkPackageLoaded;
    private final OnEngineInitFailedListener mEngineInitFailedListener;
    private String cacheAutomatorScript;
    protected MSCPackageLoadCallback mBasePackageLoadCallback;
    /**
     * 最近一次包加载完毕后V8内存占用，KB
     */
    public long lastJSMemoryUsageAfterPackageLoaded;

    //TODO 创建与初始化分离？方便appLoader监听
    public BaseJSEngineServiceModule(OnEngineInitFailedListener onEngineInitFailedListener) {
        mApplicationContext = MSCEnvHelper.getContext();
        mEngineInitFailedListener = onEngineInitFailedListener;
    }

    // getJSModule方法中已经有T泛型了，这里改成E
    protected abstract E newJSCServiceEngine();

    @Override
    public void onRuntimeAttached(MSCRuntime runtime) {
        super.onRuntimeAttached(runtime);
        try {
            engine = newJSCServiceEngine();
            MSCLog.iSync(TAG, "engine:", engine);
        } catch (Exception e) {
            MSCLog.e(TAG, e, "AppService exception exit");
        }
    }

    @Override
    public void initJSEngine(ILaunchJSEngineCallback launchJSEngineCallback) {
        engine.setOnJsUncaughtErrorHandler(this);
        engine.setOnEngineInitFailedListener(mEngineInitFailedListener);
        engine.launch(getRuntime(), mApplicationContext, launchJSEngineCallback);
        MSCLog.iSync(TAG, "engine:", engine);
    }

    @Override
    public void onAppStart(MSCApp app) {
        addAutomatorCallback();
    }

    private void addAutomatorCallback() {
        IAutomatorManager automatorManager = AutomatorManagerLoader.getInstance();
        if (automatorManager == null) {
            return;
        }

        automatorManager.addServiceOperateCallback(getRuntime().getAppId(), new ServiceOperateCallback() {
            @Override
            public void subscribeHandler(@Nullable String script) {
                if (TextUtils.isEmpty(script)) {
                    MSCLog.i(TAG, "automator script is null");
                    return;
                }
                JSAutomator jsAutomator = getRuntime().getJSModule(JSAutomator.class);
                if (jsAutomator != null && !TextUtils.isEmpty(script)) {
                    jsAutomator.onAutoMessage(script);
                } else {
                    MSCLog.e(TAG, "jsAutomator is null or script is empty");
                }
            }

            @Override
            public void inject(@Nullable String script) {
                if (TextUtils.isEmpty(script)) {
                    MSCLog.i(TAG, "automator script is null");
                    return;
                }
                if (!isFrameworkPackageLoaded) {
                    cacheAutomatorScript = script;
                    return;
                }

                MSCLog.i(TAG, "automator script inject");
                evaluateJavascript("automatorService", script, new ValueCallback<String>() {
                    @Override
                    public void onReceiveValue(String value) {
                        MSCLog.i(TAG, "evaluateJavascript success, appId:", getRuntime().getAppId(), ", onReceiveValue:", value);
                    }
                });
            }
        });
    }

    private interface JSAutomator extends JavaScriptModule {
        void onAutoMessage(@NonNull String script);
    }

    private void replayInjectAutomatorScript() {
        if (TextUtils.isEmpty(cacheAutomatorScript)) {
            return;
        }
        String script = cacheAutomatorScript;
        cacheAutomatorScript = null;
        MSCLog.i(TAG, "replayInjectAutomatorScript");

        evaluateJavascript("automatorService", script, new ValueCallback<String>() {
            @Override
            public void onReceiveValue(String value) {
                MSCLog.i(TAG, "evaluateJavascript success, appId:", getRuntime().getAppId(), ", onReceiveValue:", value);
            }
        });
    }

    @Override
    public JSInstance getJsInstance() {
        return engine.getJSInstance();
    }

    /**
     * 清空Service
     * TODO: 内部 engine.relaunch() 方法尚未完整实现, relaunchEngine 目前无调用, 后期若要调用, 需要注意这个问题
     * @param context
     */
    public void relaunchEngine(Context context) {
        synchronized (loadedPackages) {
            //没加载过就算了
            if (CollectionUtil.isEmpty(loadedPackages)) {
                return;
            }
            loadedPackages.clear();
        }
        engine.relaunch();
    }

    public long getJSRuntimePtr() {
        return getEngine().getJSRuntimePtr();
    }

    public E getEngine() {
        return engine;
    }

    @Override
    public void loadPackage(PackageInfoWrapper packageInfo, MSCPackageLoadCallback callback) {
        String eventName = packageInfo.isBasePackage() ? PerfEventConstant.INJECT_BASE_JS : PerfEventConstant.INJECT_BIZ_JS;
        // MSCLog.i(TAG, "[MSC_LOG]loadPackage eventName:", eventName, ", appId:", packageInfo.appId,
        //                ", packageName:", packageInfo.getPackageName(), ", publishId:", packageInfo.publishId,
        //                ",time:", ContainerController.TIME_FORMAT.format(new Date(System.currentTimeMillis())));
        ConcurrentHashMap<String, Object> eventExtra = new MPConcurrentHashMap<>();
        eventExtra.put("md5", packageInfo.getMd5());
        getRuntime().getPerfEventRecorder().beginDurableEvent(eventName, eventExtra);
        if (getRuntime().isRuntimeBizPreloading()) {
            // 业务预热阶段inject_biz_js无法上报，新增一个点并强制上报
            PerfTrace.online().begin("serviceBizPreloadInjectBiz").arg("forceReport", true).report();
        }
        MSCPackageLoadCallback packageLoadListener = new MSCPackageLoadCallback() {
            @Override
            public void onPackageLoadSuccess(@NonNull PackageInfoWrapper packageInfo, boolean realLoaded) {
                if (callback != null) {
                    callback.onPackageLoadSuccess(packageInfo, realLoaded);
                }
                // MSCLog.i(TAG, "[MSC_LOG]onPackageLoadSuccess eventName:", eventName, ", appId:", packageInfo.appId,
                //                        ", packageName:", packageInfo.getPackageName(), ", publishId:", packageInfo.publishId,
                //                        ",time:", ContainerController.TIME_FORMAT.format(new Date(System.currentTimeMillis())));
                eventExtra.put("status", "ok");
                getRuntime().getPerfEventRecorder().endDurableEvent(eventName, eventExtra);
                if (getRuntime().isRuntimeBizPreloading()) {
                    PerfTrace.online().end("serviceBizPreloadInjectBiz").arg("forceReport", true).report();
                }
            }

            @Override
            public void onPackageLoadFailed(@NonNull PackageInfoWrapper packageInfo, AppLoadException e) {
                if (callback != null) {
                    callback.onPackageLoadFailed(packageInfo, e);
                }
                // MSCLog.i(TAG, "[MSC_LOG]onPackageLoadFailed eventName:", eventName, ", appId:", packageInfo.appId,
                //                        ", packageName:", packageInfo.getPackageName(), ", publishId:", packageInfo.publishId,
                //                        ",time:", ContainerController.TIME_FORMAT.format(new Date(System.currentTimeMillis())));
                eventExtra.put("status", "fail");
                getRuntime().getPerfEventRecorder().endDurableEvent(eventName, eventExtra);
                if (getRuntime().isRuntimeBizPreloading()) {
                    PerfTrace.online().end("serviceBizPreloadInjectBiz").arg("forceReport", true).report();
                }
            }
        };
        boolean isPackageLoaded;
        synchronized (loadedPackages) {
            isPackageLoaded = loadedPackages.contains(packageInfo.getMd5());
            loadedPackages.add(packageInfo.getMd5());
        }
        if (!isPackageLoaded) {
            MSCLog.i(TAG, "loadPage:", packageInfo);
            if (packageInfo.isBasePackage()) {
                MPListenerManager.getInstance().launchEventListener.onEvent("service_runtime_load_begin");
                mBasePackageLoadCallback = packageLoadListener;
            } else if (packageInfo.isMainPackage()) {
                MPListenerManager.getInstance().launchEventListener.onEvent("service_mainpkg_load_begin");
            }
            DioFile serviceFile = packageInfo.getServiceFile();
            if (serviceFile.exists()) {
                String sourceUri = JSFileSourceHelper.getSourceOfImportServiceFile(packageInfo);
                String codeCacheFilePath = CodeCacheManager.getInstance().getCodeCacheFilePath(getRuntime(), packageInfo, serviceFile, sourceUri, true);
                LoadJSCodeCacheCallback loadJSCodeCacheCallback = CodeCacheManager.getInstance().getLoadJSCodeCacheCallback(getRuntime(), serviceFile);
                CodeCacheManager.getInstance().recordCodeCacheUsage(packageInfo.appId, packageInfo.getDDResourceName(), serviceFile.getChildFilePath());
                long evaluateJsStartTime = System.currentTimeMillis();
                engine.evaluateJsFile(serviceFile, sourceUri, packageInfo.packageType, packageInfo.getPackageName(), new ResultCallback() {
                    @Override
                    public void onReceiveFailValue(Exception e) {
                        //加载失败,给个下次重新加载的机会
                        synchronized (loadedPackages) {
                            loadedPackages.remove(packageInfo.getMd5());
                        }
                        MSCLog.e(TAG, e, "loadServicePackageError:", packageInfo.getMd5());
                        boolean isResourceInvalid = !MSCFileUtils.checkMd5AndDeleteIfNeed("loadService", packageInfo);
                        int errorCode = -1;
                        if (!serviceFile.exists()) {
                            errorCode = getErrorCodeByPackageTypeWhenFileNotExist(packageInfo);
                        } else if (isResourceInvalid) {
                            errorCode = getErrorCodeByPackageTypeWhenFileInvalid(packageInfo);
                        } else {
                            errorCode = getErrorCodeByPackageType(packageInfo);
                        }
                        packageLoadListener.onPackageLoadFailed(packageInfo, new AppLoadException(errorCode, e));
                    }

                    @Override
                    public void onReceiveValue(String value) {
                        MSCLog.i(TAG, "loadServicePackageSuccess:", packageInfo.getMd5());
                        if (packageInfo.isBasePackage()) {
                            BaseJSEngineServiceModule.this.isFrameworkPackageLoaded = true;
                            getRuntime().publish(new MSCEvent<>(IAppLoader.FRAMEWORK_PACKAGE_LOADED));
                            replayInjectAutomatorScript();
                            // MSCLog.i(TAG, "[MSC_LOG]loadServicePackageSuccess: notifyContextReady");
                            getJsExecutor().notifyContextReady();
                        } else {
                            recordScriptPerformanceData(evaluateJsStartTime);
                        }
                        packageInfo.markServiceLoadedTime();
                        packageLoadListener.onPackageLoadSuccess(packageInfo, true);
                        updateLastJSMemoryUsage();
                    }
                }, codeCacheFilePath, loadJSCodeCacheCallback);
            } else {
                MSCLog.i(TAG, "loadServicePackage Error!", "serviceFile not exist!", packageInfo.getMd5());
                int errorCode = getErrorCodeByPackageTypeWhenFileNotExist(packageInfo);
                packageLoadListener.onPackageLoadFailed(packageInfo,
                        new AppLoadException(errorCode, "AppService#loadServicePackage serviceFile not exist" + packageInfo));
            }
        } else {
            // 注意，此处直接回调success，在加载尚未完成时是错误的，需要保证加载过程中不再次发起加载
            MSCLog.i(TAG, "loadServicePackage already exist:", packageInfo.getMd5());
            packageLoadListener.onPackageLoadSuccess(packageInfo, false);
        }
    }

    private int getErrorCodeByPackageType(PackageInfoWrapper packageInfo) {
        if (packageInfo == null) {
            return MSCLoadErrorConstants.ERROR_INJECT_PACKAGE_INFO_NOT_EXIST;
        }
        if (packageInfo.isBasePackage()) {
            return MSCLoadErrorConstants.ERROR_INJECT_BASE_FAILED;
        }
        return packageInfo.isMainPackage() ? MSCLoadErrorConstants.ERROR_INJECT_MAIN_PACKAGE_FAILED
                : MSCLoadErrorConstants.ERROR_INJECT_SUB_PACKAGE_FAILED;
    }

    /**
     * 记录业务包 evaluateScript 性能数据, 同时限定 AppService 调用时记录
     * @param startTime js注入起始时间戳
     */
    private void recordScriptPerformanceData(long startTime) {
        if (this instanceof AppService) {
            MSCRuntime runtime = getRuntime();
            JSONObject jsonObject = PerformanceManager.createScriptPerformanceData(startTime);
            runtime.setScriptPerformanceData(jsonObject);
        }
    }

    private int getErrorCodeByPackageTypeWhenFileNotExist(@Nullable PackageInfoWrapper packageInfo) {
        if (packageInfo == null) {
            return MSCLoadErrorConstants.ERROR_INJECT_PACKAGE_INFO_NOT_EXIST;
        }
        if (packageInfo.isBasePackage()) {
            return MSCLoadErrorConstants.ERROR_INJECT_BASE_FILE_NOT_EXIST;
        }
        return packageInfo.isMainPackage() ? MSCLoadErrorConstants.ERROR_INJECT_MAIN_PACKAGE_FILE_NOT_EXIST
                : MSCLoadErrorConstants.ERROR_INJECT_SUB_PACKAGE_FILE_NOT_EXIST;
    }

    private void updateLastJSMemoryUsage() {
        engine.getJsMemoryUsage(new IEngineMemoryHelper.JSMemoryListener() {
            @Override
            public void onGetMemorySuccess(long jsMemoryKB) {
                lastJSMemoryUsageAfterPackageLoaded = jsMemoryKB;
            }
        });
    }

    private int getErrorCodeByPackageTypeWhenFileInvalid(@Nullable PackageInfoWrapper packageInfo) {
        if (packageInfo == null) {
            return MSCLoadErrorConstants.ERROR_INJECT_PACKAGE_INFO_NOT_EXIST;
        }
        if (packageInfo.isBasePackage()) {
            return MSCLoadErrorConstants.ERROR_INJECT_BASE_FILE_INVALID;
        }
        return packageInfo.isMainPackage() ? MSCLoadErrorConstants.ERROR_INJECT_MAIN_PACKAGE_FILE_INVALID
                : MSCLoadErrorConstants.ERROR_INJECT_SUB_PACKAGE_FILE_INVALID;
    }

    public void onDestroy() {
        if (engine == null) {
            return;
        }
        engine.release();
    }

    public void evaluateJavascript(String tag, String script, @Nullable ValueCallback<String> resultCallback) {
        engine.evaluateJavascript("eval: " + tag, script, resultCallback);
    }

    // https://km.sankuai.com/collabpage/**********#
    @MSCMethod(isSync = false)
    public void importScriptsAsync(JSONArray files, String params, IMSCCompletableCallback success, IMSCCompletableCallback fail) {
        ImportAsyncStrategyProvider.getImportScriptsAsyncStrategy(getEngine(), getRuntime(), this).importScriptsAsync(files, params, success, fail);
    }

    /**
     * 同步引入js文件
     *
     * @param files 文件列表
     * @param params 参数信息
     * @return null/ js内容。若withoutEval为true，则返回js文件内容；否则，内容均通过注入方式返回，返回null。
     */
    // 技术方案文档：https://km.sankuai.com/page/917121228
    @MSCMethod(isSync = true)
    public String importScripts(JSONArray files, String params) {
        String[] fileUris = JsonUtil.parseToStringArray(files);
        boolean enableCodeCache = CodeCacheConfig.INSTANCE.isEnableCodeCache(getApp());
        MSCLog.iSync(TAG, "importScripts:", files, params, "enableCodeCache:", enableCodeCache);
        if (!enableCodeCache) {
            // 如果没有启用CodeCache，则走原来的 Combo 逻辑
            return MSCFileUtils.importScript(JsonUtil.parseToStringArray(files), params, getRuntime(), engine);
        }
        MSCRuntime runtime = getRuntime();
        if (fileUris == null) {
            runtime.getNativeExceptionHandler().handleException(new MSCRuntimeException("AppService#importScripts Error: files null"));
            return null;
        }

        // 只加载，不执行
        JSONObject jsonObject = JsonUtil.parseToJson(params);
        boolean withoutEval = jsonObject.optBoolean("withoutEval", false);
        if (withoutEval) {
            List<DioFile> jsFiles = runtime.getMSCAppModule().getDioFiles(fileUris);
            return MSCFileUtils.concatComboFileString(jsFiles, runtime, new ResultCallback() {
                @Override
                public void onReceiveFailValue(Exception e) {
                    if (!MSCHornRollbackConfig.isRollbackImportScriptsDoubleUploadError()) {
                        runtime.getNativeExceptionHandler().handleException(e);
                    }
                }

                @Override
                public void onReceiveValue(String value) {

                }
            });
        }

        // 执行并加载CodeCache
        boolean getLocked = false;
        try {
            getLocked = LruCleaner.getFileDelLock().tryLock();
            if (getLocked) {
                for (String jsFile : fileUris) {
                    PerfTrace.begin(IMPORT_SCRIPTS).arg("file", jsFile);
                    importScriptWithCodeCache(jsFile, null);
                    PerfTrace.end(IMPORT_SCRIPTS);
                }
            } else {
                // 若没有获取锁，则使用cobo逻辑。
                return MSCFileUtils.importScript(JsonUtil.parseToStringArray(files), params, getRuntime(), engine);
            }
        } finally {
            if (getLocked) {
                LruCleaner.getFileDelLock().unlock();
            }
        }
        return null;
    }

    /**
     * 主包加载完成之前，importScriptError 标记为加载失败；不影响后续使用
     *
     * @param packageInfoWrapper packageInfo
     * @param errorCode          errorCode
     * @param errorMsg           errorMsg
     * @param e                  e
     */
    private void markImportScriptError(PackageInfoWrapper packageInfoWrapper, int errorCode, String errorMsg, Exception e) {
        MSCRuntime runtime = getRuntime();
        runtime.getNativeExceptionHandler().handleException(e);
        MSCLog.e(TAG + "ImportScriptError" + errorMsg, e);

        if (isFrameworkPackageLoaded) {
            return;
        }

        if (mBasePackageLoadCallback != null) {
            mBasePackageLoadCallback.onPackageLoadFailed(packageInfoWrapper, new AppLoadException(errorCode, errorMsg, e));
            //避免重复回调
            mBasePackageLoadCallback = null;
        }
    }

    @Override
    public void importScriptWithCodeCache(String jsFile, @Nullable ResultCallback callback) {
        long importScriptStartTimeNs = System.nanoTime();
        MSCLog.i(TAG, "importScript:", jsFile);

        String   pkgName = JSFileSourceHelper.getPkgNameFromUrl(jsFile, getRuntime());
        if (pkgName == null) {
            pkgName = "";
        }
        final String[] pkgNames = new String[]{pkgName};

        MSCRuntime runtime = getRuntime();
        PackageInfoWrapper packageInfo = runtime.getMSCAppModule().getPackageInfoByUrl(jsFile, true);
        final JSResourceData jsResourceData = runtime.getMSCAppModule().getResourcePathWithPackageInfo(jsFile);
        if (packageInfo == null || jsResourceData == null) {
            final String msg = "jsResourceData is null";
            MSCLog.e(TAG, msg);
            int errorCode = getErrorCodeByPackageTypeWhenFileNotExist(packageInfo);
            markImportScriptError(packageInfo, errorCode, msg, null);
            PackageLoadReporter.create(runtime).onInjectPackage(MSCReporter.ReportValue.FAILED,
                    "files", new String[]{jsFile}, pkgNames, -1, msg);

            if (runtime.getRuntimeReporter().getRenderReporter() != null) {
                runtime.getRuntimeReporter().getRenderReporter().reportJSSourceError(runtime.getRuntimeReporter(), importScriptStartTimeNs, jsFile);
            }
            if (null != callback) {
                callback.onReceiveFailValue(new Exception(msg));
            }
            return;
        }

        final DioFile file = jsResourceData.jsResourceFile;
        if (file == null || !file.exists()) {
            Exception ex = new MSCRuntimeException("importScripts not exist: " + jsFile
                    + "," + runtime.getMSCAppModule().getResourceCheckResult(jsFile, jsResourceData));
            if (file != null) {
                MSCLog.e(TAG, "DioFile: " + file.getPath());
            }
            int errorCode = getErrorCodeByPackageTypeWhenFileNotExist(packageInfo);
            markImportScriptError(packageInfo, errorCode, ex.getMessage(), ex);
            // TODO: 2022/8/30 tianbin checkResourceAndReport 后续可下线
            runtime.getMSCAppModule().checkResourceAndReport(jsFile, jsResourceData);
            PackageLoadReporter.create(runtime).onInjectPackage(MSCReporter.ReportValue.FAILED,
                    "files", new String[]{jsFile}, pkgNames, -1, "file is null or not exist");

            if (runtime.getRuntimeReporter().getRenderReporter() != null) {
                runtime.getRuntimeReporter().getRenderReporter().reportFileError(runtime.getRuntimeReporter(), importScriptStartTimeNs, jsFile);
            }

            if (null != callback) {
                callback.onReceiveFailValue(ex);
            }
            return;
        }

        // 执行
        String source = JSFileSourceHelper.getSourceOfImport(jsFile);
        String codeCacheFilePath = CodeCacheManager.getInstance().getCodeCacheFilePath(runtime, packageInfo, file, source, true);
        LoadJSCodeCacheCallback loadJSCodeCacheCallback = CodeCacheManager.getInstance().getLoadJSCodeCacheCallback(runtime, file);
        CodeCacheManager.getInstance().recordCodeCacheUsage(packageInfo.appId, packageInfo.getDDResourceName(), file.getChildFilePath());

        engine.evaluateJsFile(file, source, -1, null, new ResultCallback() {
            @Override
            public void onReceiveFailValue(Exception e) {
                boolean isResourceInvalid = !PackageLoadManager.getInstance().checkDDResourceMd5AndReport("importScript", packageInfo);
                int errorCode = -1;
                if (file.exists()) {
                    errorCode = getErrorCodeByPackageTypeWhenFileNotExist(packageInfo);
                } else if (isResourceInvalid) {
                    errorCode = getErrorCodeByPackageTypeWhenFileInvalid(packageInfo);
                }
                markImportScriptError(packageInfo, errorCode, e.getMessage(), e);
                PackageLoadReporter.create(runtime).onInjectPackage(MSCReporter.ReportValue.FAILED,
                        "files", new String[]{jsFile}, pkgNames, -1, e.toString());

                if (runtime.getRuntimeReporter().getRenderReporter() != null) {
                    runtime.getRuntimeReporter().getRenderReporter().reportEvaluateError(runtime.getRuntimeReporter(), importScriptStartTimeNs, jsFile, e);
                }
                if (null != callback) {
                    callback.onReceiveFailValue(e);
                }
            }

            @Override
            public void onReceiveValue(String value) {
                PackageLoadReporter.create(runtime).onInjectPackage(MSCReporter.ReportValue.SUCCESS,
                        "files", new String[]{jsFile}, pkgNames);

                if (runtime.getRuntimeReporter().getRenderReporter() != null) {
                    runtime.getRuntimeReporter().getRenderReporter().reportRealTime(runtime.getRuntimeReporter(), importScriptStartTimeNs, jsFile);
                }
                if (null != callback) {
                    callback.onReceiveValue(value);
                }
            }
        }, codeCacheFilePath, loadJSCodeCacheCallback);
    }

    /**
     * 在Service层注入全局字段
     *
     * @param name
     * @param value
     */
    public void injectGlobalField(String name, String value) {
        engine.evaluateJavascript("inject: " + name, String.format("%s=%s", name, value), null);
    }

    @Override
    public void uncaughtException(@NonNull Thread t, @NonNull Throwable e) {
        e.printStackTrace();
        StringWriter errors = new StringWriter();
        e.printStackTrace(new PrintWriter(errors));
        String message = errors.toString();
        MSCLog.e("JSThread error", message);
        JSONObject jsonObject = new JSONObject();
        try {
            jsonObject.put("message", e.getMessage());
            jsonObject.put("nativeStack", message);
        } catch (JSONException e1) {
            //ignore
            jsonObject = null;
        }

        // TODO: 2024/9/9 tianbin 待确认是否要上报到msc.load.error.count
        if (mEngineInitFailedListener != null) {
            mEngineInitFailedListener.onEngineInitFailed(new AppLoadException(
                    MSCLoadErrorConstants.ERROR_CODE_FATAL_JS_ERROR, jsonObject != null ? jsonObject.toString() : message, e));
        }
    }

    public <T extends JavaScriptModule> T getJSModule(Class<T> classOfT) {
        IServiceEngine engine = getEngine();
        if (engine != null) return engine.getJSModule(classOfT);
        else return null;
    }

    public ServiceInstance getJsExecutor() {
        IServiceEngine engine = getEngine();
        return engine != null ? engine.getJsExecutor() : null;
    }

    // FIXME: 2022/3/9 liangting how to invokeBack
    @Override
    public Object dispatchCall(ICallFunctionContext context, String moduleName, String methodName, JSONArray params, ExecutorContext executorContext) {
        // execute js submodule call
        engine.getJSInstance().callFunction(moduleName, methodName, params);
        return null;
    }
}
