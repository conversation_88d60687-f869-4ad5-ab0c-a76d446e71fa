package com.meituan.msc.modules.engine;

import android.content.Intent;
import android.support.annotation.NonNull;
import android.text.TextUtils;

import com.meituan.msc.common.utils.Constants;
import com.meituan.msc.common.utils.IntentUtil;
import com.meituan.msc.extern.MSCEnvHelper;
import com.meituan.msc.lib.interfaces.container.MSCParams;
import com.meituan.msc.modules.apploader.IAppLoader;
import com.meituan.msc.modules.container.ContainerDebugLaunchData;
import com.meituan.msc.modules.devtools.DebugHelper;
import com.meituan.msc.modules.preload.PackageDebugHelper;
import com.meituan.msc.modules.preload.PreloadManager;
import com.meituan.msc.modules.reporter.MSCLog;

/**
 * Created by letty on 2020/9/16.
 **/
public class EngineHelper {

    public static final String HOST_DIO_CHECK_UPDATE = "https://dd.meituan.com";
    public static final String HOST_DIO_CHECK_UPDATE_TEST = "http://ddapi.fe.test.sankuai.com";
    public static final String NEW_HOST_DIO_CHECK_UPDATE_TEST = "https://dd.sankuai.com/test";
    public static final String CHECK_UPDATE_TEST_ENVIRONMENT_SW = "checkUpdateTestEnvironmentSW";
    public static final String PATH_PREVIEW = "test/config/preview"; //外部小程序使用预览环境 https://km.sankuai.com/page/1205618824

    public static MSCRuntime setupRuntimeForLaunch(String appId, String targetPath, @NonNull ContainerDebugLaunchData debugLaunchData, boolean disableReuseAny, boolean isUrlExternalApp) {
        MSCRuntime runtime = RuntimeManager.getRuntimeForLaunch(appId, targetPath, debugLaunchData, true, disableReuseAny, isUrlExternalApp);
        setPreloadAndKeepAliveReportInfo(appId, runtime);

        // 缓存是否拉取到基础库强制更新配置，用于排查基础库强制更新配置生效率低问题
        runtime.cacheIsRemoteBasePackageReloadConfigFetched();

        if (!runtime.getModule(IAppLoader.class).isUsed()) {
            runtime.startApp(appId);
        }
        String reason = null;
        if (debugLaunchData.isReload()) {
            reason = RuntimeDestroyReason.toString(RuntimeDestroyReason.RELOAD);
        } else if (disableReuseAny) {
            reason = RuntimeDestroyReason.toString(RuntimeDestroyReason.DISABLE_REUSE_ANY);
        }
        if (!TextUtils.isEmpty(reason)) {
            // reload时不去复用之前启动的引擎，一定会新建，为防止之后再启动时存在多个引擎，无法决策，此处销毁其他引擎
            // 这些引擎所属的activity已通过Instrumentation操作保障进入销毁流程，但不一定走到了destroy
            RuntimeManager.destroyAllOtherSameAppIdEngine(runtime, reason);
        }

        // 初始化WebView调试状态
        EngineHelper.initDebugWebViewSwitch(debugLaunchData);
        return runtime;
    }

    public static String getAppIdOrBaseVersion(MSCRuntime runtime) {
        if (runtime == null) {
            return "";
        }
        if (!TextUtils.isEmpty(runtime.getAppId())) {
            return runtime.getAppId();
        }
        return runtime.getMSCAppModule().getBasePkgVersion();
    }

    public static void initDebugWebViewSwitch(ContainerDebugLaunchData launchData) {
        if (MSCEnvHelper.getEnvInfo().isProdEnv()) {
            return;
        }

        if (!TextUtils.isEmpty(launchData.getDebugServer()) || TextUtils.equals("true", launchData.isDebug())) {
            DebugHelper.debugWebView = true;
        }
    }

    public static void setPreviewCheckUpdateUrlOfDebug(MSCRuntime runtime, String appId, ContainerDebugLaunchData debugLaunchData) {
        if (MSCEnvHelper.getEnvInfo().isProdEnv()) {
            return;
        }
        if (runtime == null) {
            return;
        }

        IAppLoader appLoader = runtime.getModule(IAppLoader.class);
        setCheckUpdateUrlAndMSCVersion(appId, debugLaunchData, appLoader);

        String url = appLoader.getCheckUpdateUrl();
        if (!TextUtils.isEmpty(url)) {
            MSCLog.i(Constants.LAUNCH, "setPreviewCheckUpdateUrl:", url);
        }
    }

    public static void setCheckUpdateUrlAndMSCVersion(String appId, ContainerDebugLaunchData debugLaunchData, IAppLoader appLoader) {
        if (appLoader == null) {
            return;
        }

        String mscVersion = debugLaunchData.getMSCVersion();
        // 如果有 checkUpdateUrl，则 reload 缺省值为 true，否则缺省值为 false
        // 新建/基础库预热场景启动，reload为true 或者 checkUpdateUrl不为空时，需要强制更新
        appLoader.setNeedForceUpdate(debugLaunchData.needForceCheckUpdate());
        appLoader.setCheckUpdateUrl(PackageDebugHelper.instance.getCheckUpdateUrlOfIDEPreview(appId, debugLaunchData));
        appLoader.setBasePkgVersionOfDebug(mscVersion);
    }

    /**
     * 获取reload状态，只在线下环境支持，reload为true时，将新建运行时启动
     */
    public static boolean getReloadStatusOnTestEnv(Intent intent) {
        return MSCEnvHelper.isInited() && !MSCEnvHelper.getEnvInfo().isProdEnv()
                && IntentUtil.getBooleanExtra(intent, MSCParams.RELOAD, false);
    }

    private static void setPreloadAndKeepAliveReportInfo(String appId, MSCRuntime runtime) {
        if (!MSCHornRollbackConfig.enableReportPreloadAndKeepAlive()) {
            return;
        }
        if (runtime.hasContainerAttached()) {
            // 融合模式启动，不重新设置以下状态值。
            return;
        }
        String keepAliveMissReason = RuntimeManager.getAndClearKeepAliveMissReason(appId);
        if (TextUtils.isEmpty(keepAliveMissReason)) {
            keepAliveMissReason = "keepAliveNoLaunch";
        }
        runtime.setKeepAliveMissReason(keepAliveMissReason);

        String preloadBaseMissReason = PreloadManager.getInstance().preloadBaseMissReason;
        if (TextUtils.isEmpty(preloadBaseMissReason)) {
            preloadBaseMissReason = "preloadBaseNoTrigger";
        }
        runtime.setPreloadBaseMissReason(preloadBaseMissReason);

        String preloadBaseErrorMsg = PreloadManager.getInstance().preloadBaseErrorMsg;
        if (TextUtils.isEmpty(preloadBaseErrorMsg)) {
            preloadBaseErrorMsg = "no trigger preload base";
        }
        runtime.setBasePreloadFailReason(preloadBaseErrorMsg);
        runtime.setBasePreloadHitControlDetail(PreloadManager.getInstance().basePreloadHitControlDetail);

        String bizPreloadMissReason = PreloadManager.getInstance().preloadBizMissReasonMap.get(appId);
        if (TextUtils.isEmpty(bizPreloadMissReason)) {
            bizPreloadMissReason = "bizPreloadNoTrigger";
        }
        runtime.setBizPreloadMissReason(bizPreloadMissReason);
        PreloadManager.getInstance().preloadBizMissReasonMap.remove(appId);

        runtime.setBizPreloadErrorMsg(PreloadManager.getInstance().preloadBizErrorMsgMap.get(appId));
        PreloadManager.getInstance().preloadBizErrorMsgMap.remove(appId);

        runtime.setBizPreloadHitControlDetail(PreloadManager.getInstance().bizPreloadHitControlDetail.get(appId));
        PreloadManager.getInstance().bizPreloadHitControlDetail.remove(appId);

    }
}
