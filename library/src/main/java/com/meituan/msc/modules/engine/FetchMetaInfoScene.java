package com.meituan.msc.modules.engine;

import android.support.annotation.IntDef;

import java.lang.annotation.Retention;
import java.lang.annotation.RetentionPolicy;

@IntDef({FetchMetaInfoScene.DEFAULT, FetchMetaInfoScene.PAGE_NOT_FOUND, FetchMetaInfoScene.BIZ_MIN_VERSION})
@Retention(RetentionPolicy.SOURCE)
public @interface FetchMetaInfoScene {

    /**
     * 默认
     */
    int DEFAULT = -1;
    /**
     * 页面不存在，新建/基础库预热启动时，重新拉包
     */
    int PAGE_NOT_FOUND = 1;
    /**
     * 业务最低版本号
     */
    int BIZ_MIN_VERSION = 2;
}
