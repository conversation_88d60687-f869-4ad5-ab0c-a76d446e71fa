package com.meituan.msc.modules.engine;

import android.support.annotation.NonNull;

/**
 * Horn配置类型
 *
 * <AUTHOR>
 * @date 2021/6/30.
 */
public enum HornConfigType {
    /**
     * 默认配置(除美团极简包外的宿主使用)
     */
    DEFAULT("msc_preload"),
    /**
     * 美团极简包配置
     */
    MT_TINY("msc_preload_mt_tiny");

    private final String type;

    HornConfigType(String type) {
        this.type = type;
    }

    @NonNull
    public String getType(){
        return type;
    }
}
