package com.meituan.msc.modules.engine;

import android.content.Context;
import android.support.annotation.Nullable;
import android.webkit.ValueCallback;

import com.meituan.msc.jse.bridge.JSInstance;
import com.meituan.msc.jse.bridge.JavaScriptModule;
import com.meituan.msc.modules.service.ILaunchJSEngineCallback;
import com.meituan.msc.modules.service.IServiceEngine;
import com.meituan.msc.modules.service.ServiceInstance;
import com.meituan.msc.modules.update.bean.PackageInfoWrapper;

public interface IAppService<E extends IServiceEngine> {

    void initJSEngine(ILaunchJSEngineCallback launchJSEngineCallback);

    JSInstance getJsInstance();

    void relaunchEngine(Context context);

    void loadPackage(final PackageInfoWrapper packageInfo, final MSCPackageLoadCallback packageLoadListener);

    E getEngine();

    void injectGlobalField(String name, String value);

    /**
     * 该方法不能用于线上功能。为了JS字节码等功能，不应该拼接生成大片段的JS代码执行，应该将JS代码放到文件中执行。当前只有自动化测试和调试功能在用。
     *
     * @deprecated
     */
    @Deprecated
    void evaluateJavascript(String tag, String script, @Nullable ValueCallback<String> resultCallback);

    <T extends JavaScriptModule> T getJSModule(Class<T> classOfT);

    ServiceInstance getJsExecutor();

}
