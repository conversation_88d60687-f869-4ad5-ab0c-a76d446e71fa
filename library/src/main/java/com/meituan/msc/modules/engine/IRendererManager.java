package com.meituan.msc.modules.engine;

import android.content.Context;
import android.view.View;

import com.meituan.msc.modules.page.render.BaseRenderer;
import com.meituan.msc.modules.page.render.RendererType;
import com.meituan.msc.modules.update.bean.PackageInfoWrapper;

import java.util.List;


public interface IRendererManager {

    void releaseWebView(View view);

    void preloadWebViewBasePackage(Context context, PackageInfoWrapper packageInfo, final ResultCallback resultCallback);

    void clearAllCachedRenderer();

    void setPackageReady(boolean isPackageDownloaded);

    boolean isPackageReady();

    void preloadHomePage(Context context, boolean isLaunch);

    void preloadDefaultResources(Context context, final ResultCallback resultCallback, boolean isLaunch);

    BaseRenderer retainRenderer(String url);

    void recycleOrDestroyRenderer(BaseRenderer renderer);

    void releaseRenderer(BaseRenderer appPage);

    void cacheRendererForNextPage(final Context context, String currPageUrl);

    boolean preloadPage(Context context, String path, boolean isLaunch);

    void preloadBizPagesResouces(Context context, List<String> pagePaths, final ResultCallback resultCallback, boolean isLaunch);

    /**
     * 只针对 rn / mp 两类, 不包括webview
     *
     * @param rendererType
     * @return
     */
    int cacheNativeRenderer(RendererType rendererType);

    /**
     * 只针对 rn / mp 两类, 不包括webview
     *
     * @param rendererType
     * @return
     */
    BaseRenderer retainNativeRenderer(RendererType rendererType);
}
