package com.meituan.msc.modules.engine;

public class MSCApp {

    public static final String TAG = "MSCApp";

    /* 不变部分 */
    private final String appId;

    private final MSCRuntime runtime;

    // 前端要求，页面栈中第一个页面使用appLaunch启动，但widget不计入页面栈，因此需要记录
    // 注意与AppLoader中的Launched状态不同，AppLoader记录的是客户端意义上的启动过第一个页面
    public boolean isPageLaunched;

    public boolean isFirstPageLoadSuccess() {
        return firstPageLoadSuccess;
    }

    public void setFirstPageLoadSuccess(boolean firstPageLoadSuccess) {
        this.firstPageLoadSuccess = firstPageLoadSuccess;
    }

    /**
     * 第一个页面是否加载成功
     */
    public volatile boolean firstPageLoadSuccess;

    public boolean isFirstLaunched() {
        return firstLaunched;
    }

    public void setFirstLaunched(boolean firstLaunched) {
        this.firstLaunched = firstLaunched;
    }

    /**
     * 是否第一次进入小程序
     */
    public volatile boolean firstLaunched;

    MSCApp(String appId, MSCRuntime runtime) {
        this.appId = appId;
        this.runtime = runtime;
    }

    public String getAppId() {
        return appId;
    }

    public MSCRuntime getRuntime() {
        return runtime;
    }

    public boolean isAppInForeground(){
        return runtime.getContainerManagerModule().isAppForeground();
    }
}
