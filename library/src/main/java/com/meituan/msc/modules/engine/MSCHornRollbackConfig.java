package com.meituan.msc.modules.engine;

import android.support.annotation.Keep;
import android.support.annotation.NonNull;
import android.text.TextUtils;

import com.google.gson.annotations.SerializedName;
import com.meituan.msc.common.constant.APPIDConstants;
import com.meituan.msc.common.utils.MSCHornUtils;
import com.meituan.msc.extern.MSCEnvHelper;
import com.meituan.msc.lib.interfaces.BaseRemoteConfig;
import com.meituan.msc.modules.container.fusion.MSCFusionInstrumentation;
import com.meituan.msc.modules.reporter.ReporterFields;

import java.util.Arrays;
import java.util.Collections;
import java.util.HashSet;
import java.util.Map;
import java.util.Set;

/**
 * 功能回滚开关
 */
public class MSCHornRollbackConfig extends BaseRemoteConfig<MSCHornRollbackConfig.Config> {

    @Keep
    public static class Config {

        // 用于gson及其他正常创建的情况
        public Config() {
        }

        // 12.5.200
        public boolean disableNotifyRenderProcessGone = false;
        // Activity复用开关
        boolean isRollbackActivityReuse = true;
        // 长期回滚开关：回滚combo能力
        public boolean isRollbackImportScriptSupportCombo = false;
        boolean isRollbackPageMemoryReport = false;
        // 12.21.400 回滚优选业务的Memory Monitor
        Set<String> isRollbackPageMemoryReportAppIds = Collections.singleton("gh_84b9766b95bc");
        // 12.7.400 回滚TaskManager启动任务调度改动
        public boolean isRollbackTaskManagerExecutePendingTaskChange = false;
        // 12.8.400 回滚用户视角Crash上报业务维度数据
        public String[] rollbackAppendBizTagsAppIds = new String[]{};
        // 12.9.200 回滚延迟执行业务预热改动
        public boolean isRollbackPendingPreloadBiz = false;
        // 12.9.400 MMP 存储共享回滚
        public boolean isRollbackMMPSharedStorage = false;
        // 12.9.200 回滚保活启动场景md5重复校验耗时优化改动
        public boolean isRollbackKeepAliveMd5CheckOptimizeChange = false;
        // 12.10.200 回滚包删除逻辑改动
        public boolean isRollbackDeletePackageChange = false;
        // 12.10.400 回滚文件是否存在校验
        public boolean isRollbackFileExistCheck;
        // 12.12.200 回滚基础库Pike下线逻辑
        public boolean isRollBackPikeOfflineBaseSDKStrategy;
        // 12.12.400 折叠屏开启navigateBack清除指定页面
        public boolean enableFoldNavigateBackClearSpecifiedPage = true;
        // 12.12.400 全量开启navigateBack清除指定页面，12.38.200 改为白名单
        public Set<String> enableNavigateBackClearSpecifiedPageAppList = new HashSet<>();
        // 12.14.200 生命周期回滚开关
        public Set<String> isRollbackLifecycleAppIds = Collections.emptySet();
        // 12.14.400 回滚启动缓存队列加锁改动
        public boolean isRollbackPendingLock;
        // 12.14.400 回滚启动启动任务异常处理优化
        public boolean isRollbackTaskManagerExceptionHandle;
        // 12.15.200 回滚到最低版本号触发网络请求失败后返回缓存数据
        public boolean isRollbackMinVersionCheckChange;
        // 12.15.200 回滚业务预热资源加载改动
        public boolean isRollbackPreloadResource;
        // 12.15.400 回滚 页面外发起数据预拉取请求时做业务预热
        public boolean rollBackBizPreloadWhenDataPrefetch;
        // 12.16.400 闪购商家页新增FFP上报字段回滚
        public boolean rollbackFFPReportInFlashSalePage;
        // 12.15.200独立灰度 回滚未进入保活时执行基础库预热改动
        public boolean isRollbackPreloadBaseWhenNoKeepAlive;
        // 12.16.200 回滚setCalledOnAppEnterBackground时机
        public boolean rollbackSetCalledOnAppEnterBackground;
        // 12.16.400 保活场景下尽早执行startPage优化白名单
        public Set<String> startPageAdvancedWhiteList = Collections.singleton("7122f6e193de47c1");
        // 12.16.400 回滚同小程序 page onStart 不触发 onAppEnterBackground
        public boolean rollbackActivityOnStartNotBackground;
        // 12.16.400 根据appId判断是否开启基础包通过文件路径加载JavaScript的功能
        public Set<String> appIdsOfBasePackageEvaluateJavascriptWithFilePath =
                new HashSet<>(Arrays.asList("73a62054aadc4526", "bike_mmp", "3624e0d16e0f4c8a", "cdfd5e3f523f4b86", "75008250b3d340b2", "b75b8f2e8db84d05", "d1a4603ff20e40a7"));
        // 12.17.200 回滚Window DecorView优化
        public boolean isRollbackWindowDecorViewChange;
        // 12.17.200 运行时缓存池锁耗时优化
        public boolean isRollbackGetRuntimeChange = false;
        // 12.17.200 回滚设置背景色
        public boolean isRollbackBackgroundColor = true;
        // 12.17.400 回滚isPackageLoaded子包判断逻辑
        public boolean rollbackPackageLoaded;
        // 12.17.400 回滚
        public boolean isRollBackForceRouteMapping;
        // 12.17.400 回滚检查元信息缓存结果是否满足最低版本号检查
        public boolean isRollbackCacheVersionCheck;
        // 12.17.400 回滚业务包下线移除运行时缓存修复改动
        public boolean isRollbackBizOfflineRemoveRuntimeCacheFix;
        // 12.18.200 回滚 注入js文件时，jsc 销毁触发回调失败。
        public boolean isRollbackDestroyInEval;
        // 12.18.400 可用 拦截资源时，是否先保证资源在本地
        public boolean isEnableEnsureDioFile = false;
        // 12.17.400 回滚下线业务包改动
        public boolean rollbackOfflineBizPackageChange;
        // 12.18.200 message port消息转发 业务白名单
        public Set<String> messagePortWhiteList;
        // 12.18.200 message port消息转发 总开关
        public boolean isRollbackMessagePort = true;
        // 12.18.200 回滚二次上报错误
        public boolean isRollbackImportScriptsDoubleUploadError;
        // 12.18.400 回滚Widget背景色修改
        public boolean rollbackWidgetDefaultBackgroundColor;
        // 12.18.400 给msc线程池的submit增加异常上报，可能太多将api打挂，增加采样率。默认0.05即5%
        public double exeSubmitUploadRate = 0.05;
        // 12.19.200 rollback webview预热恢复支持预热webview
        public boolean isRollbackPreheatSupportWebView;
        // 12.18.200 以下AppId，Page destroy时删除hideSoftInputFromWindow
        // FIXME 12.18.200仅修复反馈问题的业务（神枪手），12.18.400全量回归出现bug，预期12.19.200
        private final String[] APP_IDS_REMOVE_HIDE_KEYBOARD = {"5d07631731cd49a9", "927b936e6cc94d87"};
        public Set<String> appIdsRemovePageDestroyHideKeyboard = new HashSet<>(Arrays.asList(APP_IDS_REMOVE_HIDE_KEYBOARD));
        // 12.19.200 回滚 setRouteMapping功能
        public boolean rollback_set_route_mapping;
        // 12.19.200 回滚 setRouteMapping功能中onAppRoute新增参数
        public boolean rollback_set_route_mapping_onapproute;
        // 12.19.200 回滚OpenParam构造时url找不到检查变更
        public boolean rollbackOpenParamUrlNotFoundCheck;
        // 12.19.200 回滚页面渲染超时进行白屏检测，默认关闭
        public boolean enableNoFirstRenderCheckWhiteScreen;
        // 12.19.400 回滚messagePort postMessage加锁
        public boolean rollbackMessagePortLock;
        // 12.20.200 键盘高度修改生效业务
        public Set<String> appIdsOnKeyBoardHeightChangeFix = Collections.singleton("b0a6ea51ea2d4901");
        // 12.20.200 回滚：MSC navigateToMiniProgram api 可以路由到MMP功能 (https://km.sankuai.com/collabpage/2170059126)
        // 12.20.200 MSC navigateToMiniProgram api 可以路由到MMP的小程序id白名单(mmp的appid, https://km.sankuai.com/collabpage/2183445657)
        // 内置8个appid：团好货、智能配送、优选、免费小说、拼好饭、团好货、打车、医药、闪购小程序
        public Set<String> mscNavigateToMMPWhiteList =
                new HashSet<>(Arrays.asList("mmp_87dffc23944d", "7fda774d6980468c", "gh_84b9766b95bc", "86464ace2bce4d6c", "a8720b841a3d4b1d", "mmp_ffd0ee8b449c", "0493c7b31c6f45ce", "bafee49867764599"));
        // 12.20.400 回滚navigateTo时禁止app级数据预拉取
        public boolean rollbackNavigateToAppPrefetch;
        // 12.21.200 回滚：沉浸模式事件双线分发策略
        public boolean rollbackSinkModeEventTwoLineDispatch;
        // 12.21.400 回滚 非Native渲染不读取Wxs配置
        public boolean rollbackNoMainThreadJSEngineLoadWhenNotNativeRender;
        // 12.21.400 回滚 页面加载过程中不执行Native渲染的预热操作
        public boolean rollbackNoPreloadNativeRenderWhenWebViewRender;

        // 12.21.400 回滚 HTML提前加载优化
        public boolean rollbackLoadHTMLOptimize;
        /**
         * 回滚onPageFinished提前优化
         */
        public boolean rollbackOnPageFinishedInAdvanced;
        // 12.21.400 回滚performance添加宿主app启动时机
        public boolean rollbackPerformanceAppAttach;
        // 12.21.400 回滚新增的预热禁止清除配置改动
        public boolean rollbackKeepPreloadApps;
        // 12.21.200 独立灰度/12.21.400 textarea/input组件adjustPosition的延时时间
        public int adjustPositionDelayTime = 500;
        // 12.21.200 独立灰度/12.21.400 textarea/input组件adjustPosition延时执行的业务白名单
        public Set<String> adjustPositionDelayWhiteList = new HashSet<>(Arrays.asList("gh_84b9766b95bc"));
        // 12.21.400 优选迁移回滚生效率验证
        public String rollbackEfficiencyRateTest;
        // 12.21.400 回滚getMenuButtonBoundingClientRect判断activity
        public boolean rollbackGetMenuButtonBoundingClientRect;
        // 12.22.200 回滚approute和数据预拉取等待基础库注入完成
        public boolean rollbackPendingFrameWorkReady;
        // 12.22.400 回滚添加 renderer hashcode 改动
        public boolean rollbackAppendRendererHashCode;
        // 12.23.200 回滚复用 renderer 时禁用基础库检查改动
        public boolean rollbackOfflineFrameworkCheck;
        // 12.23.200 回滚修复tabPage路由映射失败
        public boolean rollbackTabPageRouteMappingFix;
        // 12.23.400 回滚修复MMP迁移业务残余流量问题
        public boolean rollbackRouteConfigFix;

        public Set<String> prePageStartOptimizeWhiteList = Collections.singleton(APPIDConstants.YOU_XUAN);
        public Set<String> onAppRouteOptimizeWhiteList = Collections.singleton(APPIDConstants.YOU_XUAN);
        public int pageInAdvancedTime = 0;
        // 12.23.400 回滚修复tabPage路由映射失败
        public boolean rollbackInterceptBackFix;
        // 12.23.400 回滚修复tabBarConfig
        public boolean rollbackTabBarConfig;
        // 12.24.200 回滚setRouteMappingPersist
        @SerializedName("rollback_set_route_mapping_persist")
        public boolean rollbackSetRouteMappingPersist;
        // 12.24.400 回滚临时文件清理改动
        public boolean rollbackTempFileCleanChange;
        // 12.24.400 回滚半屏弹窗需求
        public boolean rollbackHalfDialog;
        // 12.24.400 回滚修复MSC安卓侧导航栏标题不居中的问题
        public boolean rollbackMSCNavigationBarTitleFix;
        // 12.24.400 回滚WebView复用并发问题修复
        public boolean rollbackWebViewReuseFix;
        // 12.24.400 回滚WebView可复用检查改动
        public boolean enableVerifyRendererValidAtFindRenderer = true;
        // 12.24.400 回滚启动检查资源
        public boolean rollbackCheckResource;
        // 12.24.400 回滚js error上报去重策略
        public boolean enableJSErrorReportStrategy = false;
        // 12.24.400 回滚透明Activity崩溃修复
        public boolean rollbackTransparentActivityFix;
        // 12.24.400 回滚App级数据预拉取类型使用"/app_data_prefetch"判断
        public boolean rollbackAppDataPrefetchJudge;
        // 12.25.200 回滚半屏弹窗需求截图问题修复
        public boolean rollbackPixelCopyCaptureActivity;
        // 12.25.200 主线程引擎执行增加引擎状态判断
        public boolean rollbackMainThreadEngineFilter;
        // 12.25.200 回滚MenuButtonApi PageId问题修复
        public boolean rollbackMenuButtonPageIdFix;
        // 12.25.200 回滚appId改造，https://km.sankuai.com/collabpage/2327613889
        public boolean rollbackNewAppId;
        // 12.25.200 回滚点评中msi初始化收口，https://km.sankuai.com/collabpage/2350259931
        public boolean rollbackGatherInitMsi;
        // 12.25.200 回滚小说业务Widget评论弹框部分机型无法感应键盘弹起的问题,去掉对底部导航栏高度不必要的校准动作
        public boolean rollbackKeyboardHeightFix;
        // 12.25.200 回滚setRouteMapping修复
        public boolean rollbackSetRouteMappingFix;
        // 12.25.200 回滚switchTabByClick修复
        public boolean rollbackSwitchTabByClick;
        // 12.25.200 回滚容器加载异常指标问题修复改动
        public boolean rollbackLoadErrorReportFix;
        // 12.25.200 回滚容器读取元信息缓存错误改动
        public boolean rollbackGetMetaInfoCacheError;
        // 12.25.400 回滚onDestroy清除activityCall
        public boolean rollbackOnDestroyClearStartActivityCall;
        // 12.25.400 回滚定位失败不重试app级数据预拉取
        public boolean rollbackDisableRetryAppPrefetchIfLocateFail;
        // 12.25.400 回滚分包异步化开关调整改动
        public boolean rollbackInjectAdvanceBuildConfig;
        // 12.25.400 回滚优选预下载改动
        public boolean rollbackYouXuanPreDownloadChange;
        // 12.25.400 回滚监控MSCRuntime泄漏
        public boolean rollbackCheckMSCRuntimeLeak;
        // 12.25.400 回滚拼好饭预拉取新增地址
        public boolean rollbackPHFDataPrefetchParam;
        // 12.25.400 回滚latestAttachToContainerRuntime内存泄漏修复
        public boolean rollbackLatestAttachToContainerRuntimeLeakFix;
        // 12.25.400 回滚switchTab生命周期修复
        public boolean rollbackSwitchTabLifecycleFix;
        // 12.25.400 回滚对sinkMode模式native组件层级背景色的设置，对应前端页面设置字段 sinkModeBackgroundColor
        public boolean rollbackSinkModeBackgroundColor;
        // 12.25.400 回滚relaunch路由上报改动
        public boolean rollbackRelaunchReportFix;
        // 12.25.400 回滚reload路由上报改动
        public boolean rollbackReloadReportFix;
        // 12.25.400 回滚灵犀上报改动
        public boolean rollbackStatisticsReporter;
        // 12.25.400 回滚容器创建时上报路由的改动
        public boolean rollbackReportRouteStartAtContainerCreate;
        // 12.25.400 对渲染缓存存储问题的修复（https://km.sankuai.com/collabpage/2461312800）
        public boolean renderCacheStorageFix;
        // 12.25.400 在使用渲染缓存的小程序列表（在使用渲染缓存的业务老版本都有缓存积压问题，需要清理）
        public String[] usingRenderCacheAppIds = {
                "gh_84b9766b95bc",// 优选
                "33976e84dd654a2d", // 团好货
                "73a62054aadc4526" // 免费小说
        };
        // 12.25.400 渲染缓存存储上限：默认30M
        public long renderCacheStorageLimit = 30 * 1024 * 1024;
        // 12.25.400 回滚容器停留时长上报
        public boolean rollbackReportContainerStayDuration;
        // 12.26.200 回滚修复每次activity启动都会app级数据预拉取,仅首个container触发app级数据预拉取
        public boolean rollbackAppPrefetchFirstContainer;
        // 12.25.400 回滚页面路由上报页面路径参数调整
        public boolean rollbackPageRoutePathChange;
        // 12.26.200 渲染侧支持textarea/input fontFamily属性
        public boolean enableTextAreaInputFontFamily = true;
        // 12.26.200 上报从启动（appLaunch、widgetLaunch）到发onAppRoute的耗时
        public boolean enableReportLaunchToAppRoute = true;
        // 12.26.400 开启基础库预热引擎的上锁状态
        public boolean enableRuntimeLocked = true;
        // 12.26.412 修复navigateToMiniProgram API，在MMP下线后，兜底拉起RouterCenterActivity将会抛异常。https://km.sankuai.com/collabpage/2583997336
        public boolean fixNavigateToMiniProgramWhenMMPOffline = true;
        // 12.27.200 开启支持自定义下拉刷新图标
        public boolean enableCustomPullLoadingIcon = true;
        // 12.27.200 开启startActivity场景下，数据预拉取获取基础库引擎未使用时，解锁引擎，可被其他业务使用（基础库预热引擎）
        public boolean enablePrefetchUnlockRuntime = true;
        // 12.27.400 修复小米全面屏手机获取NavigationBar高度，默认：true：开启修复，false：回滚修复
        public boolean enableXiaomiFullScreenDeviceWithNavigationBar = true;
        //12.27.400 setPagePopTransitionStyle桥API错误码改造开启
        public boolean enableSetPagePopTransitionStyleErrno = true;
        //12.27.400 容器API改造，使用新版错误码开关，Android端一些api已经加了errno，对这类api改造添加开关
        public boolean enableUseNewFormatMsiApiErrno = true;
        // 12.27.400 修复Widget和二级页FFP上报实现和口径不对齐的问题
        public boolean enableFFPReporterAppendRouteTimeFix = true;
        //12.27.400 修复在H5页面下的键盘遮挡输入框的问题
        public boolean enableFixH5KeyboardCoverInput = true;
        // 12.27.400 onAppEnterForeground携带参数
        public Set<String> enableOnAppEnterForegroundPathAppList = Collections.singleton(APPIDConstants.YOU_XUAN);
        // 12.27.400 二级页面任务未执行完成状态补充页面退出上报&添加客户端准备时间
        public boolean enablePageExitFixMissSceneAndDuration = true;
        // 12.27.400 开启预创建widget FFP延迟上报
        public boolean enablePreCreateWidgetFFPFix = true;
        // 12.27.400 修复MMP到MSC路由拦截未注册问题
        public boolean enableFixMMPToMSCIntercept = true;
        // 12.28.200 支持IM获取视频图片绝对路径
        public boolean enableIMGetAbsolutePath = true;
        //12.28.200 page-container组件拦截页面返回生命周期
        public boolean enablePageContainerLifecycleIntercept = true;
        //12.28.200 startActivityForResult支持options参数开启
        public boolean enableStartActivityForResultWithOptions = true;
        // 12.28.200 MSC初始化时提前加载JS引擎SO文件
        public boolean enablePreloadJSE = true;
        // 12.28.200 视图层注入hornConfig确保开启了messagePort的业务onPageStart在注入后执行
        public boolean enablePageStartSequenceFix = true;
        //12.28.400 开启容器覆盖率数据上报
        public boolean enableDynamicContentReport = true;
        // 12.28.400 小程序页面启动隐藏Loading图标和动画app列表
        public Set<String> enableHideLoadingIconAndAnimationAppList = Collections.singleton(APPIDConstants.DAO_HANG);
        // 12.29.200 修复路由映射版本判断逻辑
        public boolean enableFixRouteMappingValidVersion = true;
        // 12.29.200 设备未开启无障碍模式时禁用WebView无障碍功能
        public String[] enableCloseWebViewAccessibilityServiceAppList = new String[]{APPIDConstants.XIAO_XIANG};
        // 12.29.400 开启 tabBar徽标高度等参数单位从px修改为dp
        public boolean enableTabBarBadgeUnitChange = true;
        // 12.29.400 开启WebView深度预加载子包页面不生效问题修复
        public boolean enableFixSubPkgPagePreload = true;
        // 12.29.400 新增onPageStart
        public boolean enableOnPageStart = true;
        // 12.30.200 只对特定的指标进行采样
        public Set<String> samplingIndicatorsWhiteList = new HashSet<>(Arrays.asList(
                ReporterFields.MSC_EXE_POOL_ERROR_RATE, ReporterFields.REPORT_MSC_METAINFO_LOAD_DURATION,
                ReporterFields.REPORT_MSC_PACKAGE_LOAD_SUCCESS_RATE, ReporterFields.REPORT_MSC_PACKAGE_LOAD_DURATION,
                ReporterFields.REPORT_MSC_PACKAGE_INJECT_SUCCESS_RATE));
        // 12.30.200 全量关闭Window DecorView优化时使用的Horn配置,为了不对历史版本产生不可知的影响，新增开关进行控制。全量没问题后可删除改判断相关逻辑
        // 默认为true，不影响现有逻辑。后续全量关闭时，将此值改为false。
        public boolean enableWindowDecorViewChange = true;
        // 12.30.400 修复onTabItemTab和switchTab执行顺序
        public boolean enableFixOnTabItemTabAndSwitchTab = true;
        //12.30.400 包加载染色开关。如果关闭，所有包加载设置为立即使用
        public boolean enableLoadPackageWithColor = true;
        //12.30.400 包加载染色业务预热开关。如果打开，业务预热场景包加载设置为立即使用
        public boolean enableBizPreloadImmediately = false;
        // 12.31.200 AppRouteTask获取参数不抛异常
        public boolean enableAppRouteTaskGetParamsNoException = true;
        //12.31.200 业务预热添加任务重复时直接返回开关，默认开启
        public boolean enableReturnWhenHasSameBizPreloadTask = true;
        // 12.31.200 处理native渲染下地图的视图更新回调函数
        public boolean enableMsiMapListener = true;
        //12.31.400 FP、秒开等埋点添加DDD返回的分阶段耗时数据
        public boolean enableAddLoadPackageDetails = true;
        // 12.32.200 widgetData大数据
        public boolean enableInitialLargeData = true;
        // 12.32.200 对齐鸿蒙在客户端维护onPageStart消息队列, webview复用recycle执行时会清理消息队列
        public boolean enableOnPageStartQueue = true;
        // 12.32.200 三方小程序存储限制
        public boolean enableExternalAppStorageLimit = true;
        // 12.32.200 三方小程序页面栈深度限制
        public boolean enableExternalAppPageDepthLimit = true;
        //12.32.200 包加载成功率使用新指标口径，对前端调用注入多个文件时，按包分别上报
        public boolean enableUseNewInjectPackageRate = true;
        //12.32.200 页面退出时，上报未执行完成的超时任务
        public boolean enableReportTimeoutTaskWhenPageExit = true;
        // 数据预拉取onBackgroundFetchData返回时间戳修改
        public boolean enableFixOnBackgroundFetchTimestamp = true;
        //12.32.200 调整预创建后展示Widget白屏检测策略 https://km.sankuai.com/collabpage/2628235980
        public boolean enableFixWhiteScreenCheckStrategy = true;
        // 是否开启数据预拉取优化，包括支持获取缓存定位、支持业务预热阶段提前拉包
        public boolean enablePrefetchOptimizer = false;
        // 12.32.400 修复业务主动调用Widget生命周期方法不生效问题：Android单独判断了TabWidget才能调用，iOS没有限制，但由于排期太紧，先用白名单过渡。
        public Set<String> fixWidgetLifeCycleManualInvokeWhiteList = Collections.singleton(APPIDConstants.TIAN_XING_JIAN);
        //12.33.200 修正保活场景下 FP、FFP 指标维度上报逻辑
        public boolean enableFixCodeCacheLaunchReport = true;
        //12.33.200 三方小程序不生成 CodeCache
        public boolean enableExternalAppCodeCacheLimit = true;
        // 12.33.200 移除无用JS代码注入
        public Set<String> enableRemoveUnusedJSCodeWhiteList = Collections.emptySet();
        // 12.33.200 更早执行load_html方法
        public Set<String> enableAdvanceLoadHtmlWhiteList = Collections.emptySet();
        //12.33.400 修复透明背景widget首次加载闪白问题
        public boolean enableFixWidgetWhiteBackground = true;
        // 允许后台检查更新、批量检查更新时预下载配置包
        public boolean enablePreDownloadConfigPkg = true;
        // 12.34.200 Webview File同源策略绕过漏洞 https://km.sankuai.com/page/1415152334
        public boolean enableFixFileAccessSecurity = false;
        // 12.34.200 引擎管理数据上报，包含预热和保活
        public boolean enableReportPreloadAndKeepAlive = true;
        // 12.34.200 删除onResume的focus判断，修复透明背景Activity返回时未识别到上一Activity是同小程序,异常触发app.onhide的问题(在onResume时focus恒为false，不会将上一页面的appId加进去，从而识别为非同小程序；透明背景返回时上一页面只会触发onResume，没有其他时机兜底）
        public Set<String> enableFixOnResumeFocusAppList = Collections.singleton(APPIDConstants.SHAN_GOU);
        // 12.35.200 通过merge方式注入包
        public Set<String> enableLoadBasicPackagesByMergeAppList = Collections.emptySet();
        // 12.35.200 上报视图层基础库和业务主包注入的包大小
        public boolean enableWebViewPkgSizeReport= true;
        // 12.35.200 三方小程序性能优化资源使用限制
        public boolean enableExternalAppPrefSourceLimit = true;
        // 12.35.200 向秒开指标中新增容器的Tag
        public boolean enableMSCDimensionReportToFFP = true;

        // 12.37.200 引擎数量阈值共享
        public boolean enableAppSharedCountLimit = false;
        // 12.37.200 三方小程序与内部小程序保活策略隔离开关
        public boolean enableExternalAppKeepAliveRule = true;
        // 12.37.200 预热、复用webview场景提前发送onPageStart事件
        public boolean enablePreSendOnPageStart = false;
        // 12.37.200 FFPEnd事件只发送一次
        public boolean enableFFPEndSendOnce = true;
        // 12.37.200 上报需要复合计算的指标
        public boolean enableNewStagesDimension = true;
        // 12.37.200 自定义拉包支持appId维度
        public boolean enablePkgExtraParam = true;
        // 12.37.200 保活启动场景不发起APP级数据预拉取
        public boolean disableAppPrefetchWhenKeepAlive = true;
        // 12.37.200 外链冷启埋点上报
        public boolean enableOutLinkParamsReport = true;
        // 12.37.200 指定 loadingView 样式优化回滚列表
        public String[] disableLoadingViewStyleOptBlackList = new String[]{};
        // 12.37.200 秒开上报MSI API耗时数据开关
        public boolean enableReportAPIPerformanceStatisticData = true;
        // 12.37.200 发送FFPEnd事件到MSI
        public boolean enableSendFFPEndToMSI = true;
        // 12.38.200 开启splitChunks
        public boolean enableSplitChunks = true;

        // 12.38.200 路由时机执行启动任务
        public boolean enableLaunchTaskOnRoute = false;
        // 12.31.200 webview组件方法回调到主线程
        public boolean enableWebViewMainThread = true;
        // 12.38.200 引擎优先级策略
        public boolean enableLocalAppStackPriority = false;
        // 12.38.200 优先级具体生效策略, https://km.sankuai.com/collabpage/2707197355
        public String localAppStackPriorityStrategy = null;
        // 12.38.200 秒开分阶段指标单独上报
        public boolean enablePhasedPrimaryMetricsReport = true;
        // 12.38.200 修复折叠屏判断问题：漏掉了华为折叠屏
        public boolean fixFoldScreenJudge = true;
        // 12.39.200 支持业务通过跳链参数、API参数自定义是否发起数据预拉取
        public boolean enableControlPrefetchInSpecialScene = true;
        // 12.39.200 修复H5 Video全屏底部导航栏遮挡问题
        public boolean fixH5VideoFullScreenBottomNaviBarCover = true;
        // 12.39.200 js运行信息收集
        public boolean enableJsRuntimeInfoProvider = true;
        // 12.39.200 修复API数据上报问题
        public boolean enableReportAPIDataFix = true;
        // 12.39.200 API数据是否上报秒开
        public boolean enableReportAPIDataToFFP = false;
        // 12.39.200 上报首ABTest
        public boolean enableReportFirstABTest = true;
        // 12.39.211 独立灰度，上报JS引擎埋点
        public boolean enableJSEngineReport = true;
    }

    private static MSCHornRollbackConfig sInstance;

    public static MSCHornRollbackConfig get() {
        if (sInstance == null) {
            synchronized (MSCHornRollbackConfig.class) {
                if (sInstance == null) {
                    sInstance = new MSCHornRollbackConfig();
                }
            }
        }
        return sInstance;
    }

    @NonNull
    public static MSCHornRollbackConfig.Config readConfig() {
        return MSCHornRollbackConfig.get().config;
    }

    private MSCHornRollbackConfig() {
        super("msc_feature_rollback", Config.class);
    }

    @Override
    protected void onRemoteConfigChanged(String rawConfigString) {
        super.onRemoteConfigChanged(rawConfigString);
        if (TextUtils.isEmpty(rawConfigString)) {
            return;
        }

        config = parseRemoteConfig(rawConfigString);
        if (config != null) {
            MSCFusionInstrumentation.skipActivityReuse = config.isRollbackActivityReuse;
        }
    }

    @Override
    protected Map<String, Object> getHornQuery() {
        if (MSCEnvHelper.isInited()) {
            return MSCEnvHelper.getMSCRenderHornQuery();
        }
        return super.getHornQuery();
    }

    public static boolean isRollbackPageMemoryReport() {
        return MSCHornRollbackConfig.get().config.isRollbackPageMemoryReport;
    }

    public static boolean isRollbackPageMemoryReportWithAppId(String appid) {
        return MSCHornUtils.isMatchWhiteConfigRule(MSCHornRollbackConfig.get().config.isRollbackPageMemoryReportAppIds, appid);
    }

    public static boolean isRollbackMMPSharedStorage() {
        return MSCHornRollbackConfig.get().config.isRollbackMMPSharedStorage;
    }

    public static boolean isRollbackLifecycle(String appId) {
        return MSCHornRollbackConfig.get().config.isRollbackLifecycleAppIds.contains(appId);
    }

    public static boolean isRollbackSetCalledOnAppEnterBackground() {
        return MSCHornRollbackConfig.get().config.rollbackSetCalledOnAppEnterBackground;
    }

    public static boolean isRollbackStartPageAdvanced(String appId) {
        Config config = MSCHornRollbackConfig.get().config;
        return !MSCHornUtils.isMatchWhiteConfigRule(config.startPageAdvancedWhiteList, appId);
    }

    public static boolean isEnablePrePageStartOptimize(String appId) {
        Config config = MSCHornRollbackConfig.get().config;
        return MSCHornUtils.isMatchWhiteConfigRule(config.prePageStartOptimizeWhiteList, appId);
    }

    public static boolean isEnableRemoveUnusedJSCode(String appId) {
        Config config = MSCHornRollbackConfig.get().config;
        return MSCHornUtils.isMatchWhiteConfigRule(config.enableRemoveUnusedJSCodeWhiteList, appId);
    }

    public static boolean isEnableAdvanceLoadHtml(String appId) {
        Config config = MSCHornRollbackConfig.get().config;
        return MSCHornUtils.isMatchWhiteConfigRule(config.enableAdvanceLoadHtmlWhiteList, appId);
    }

    public static boolean isEnableOnAppRouteOptimize(String appId) {
        Config config = MSCHornRollbackConfig.get().config;
        return MSCHornUtils.isMatchWhiteConfigRule(config.onAppRouteOptimizeWhiteList, appId);
    }

    public static int getPageInAdvancedTime() {
        Config config = MSCHornRollbackConfig.get().config;
        return config.pageInAdvancedTime;
    }

    public static boolean isRollbackFFPReportInFlashSalePage() {
        return MSCHornRollbackConfig.get().config.rollbackFFPReportInFlashSalePage;
    }

    public static boolean isRollbackActivityOnStartNotBackground() {
        return MSCHornRollbackConfig.get().config.rollbackActivityOnStartNotBackground;
    }

    public static boolean isBasePackageEvaluateJavascriptWithFilePath(String appId) {
        Set<String> appIds = MSCHornRollbackConfig.get().getConfig().appIdsOfBasePackageEvaluateJavascriptWithFilePath;
        return appIds != null && appIds.contains(appId);
    }

    public static boolean isRollbackBackgroundColor() {
        return MSCHornRollbackConfig.get().config.isRollbackBackgroundColor;
    }

    public static boolean isRollbackPackageLoaded() {
        return MSCHornRollbackConfig.get().config.rollbackPackageLoaded;
    }

    public static boolean isRollBackForceRouteMapping() {
        return MSCHornRollbackConfig.get().config.isRollBackForceRouteMapping;
    }

    public static boolean isRollbackDestroyInEval() {
        return MSCHornRollbackConfig.get().config.isRollbackDestroyInEval;
    }

    /**
     * 是否支持文件保证。默认false。等12.18.400放开。
     *
     * @return
     */
    public static boolean isEnableEnsureDioFile() {
        return MSCHornRollbackConfig.get().config.isEnableEnsureDioFile;
    }

    public static Set<String> getMessagePortWhiteList() {
        return MSCHornRollbackConfig.get().config.messagePortWhiteList;
    }

    public static boolean isRollbackMessagePortWithAppId(String mscAppId) {
        return MSCHornRollbackConfig.get().config.isRollbackMessagePort ||
                mscAppId == null ||
                !MSCHornRollbackConfig.get().config.messagePortWhiteList.contains(mscAppId);
    }

    public static boolean isRollbackMessagePort() {
        return MSCHornRollbackConfig.get().config.isRollbackMessagePort;
    }

    public static boolean isRemovePageDestroyHideKeyboard(String appId) {
        return MSCHornRollbackConfig.get().config.appIdsRemovePageDestroyHideKeyboard.contains(appId);
    }

    public static boolean isRollbackImportScriptsDoubleUploadError() {
        return MSCHornRollbackConfig.get().config.isRollbackImportScriptsDoubleUploadError;
    }

    public static boolean isRollbackWidgetDefaultBackgroundColor() {
        return MSCHornRollbackConfig.get().config.rollbackWidgetDefaultBackgroundColor;
    }

    public static double getExeSubmitUploadRate() {
        return readConfig().exeSubmitUploadRate;
    }

    public static boolean rollbackSetRouteMapping() {
        return MSCHornRollbackConfig.get().config.rollback_set_route_mapping;
    }

    public static boolean isEnableNoFirstRenderCheckWhiteScreen() {
        return readConfig().enableNoFirstRenderCheckWhiteScreen;
    }

    public static boolean isFixOnKeyBoardHeightChange(String appId) {
        return readConfig().appIdsOnKeyBoardHeightChangeFix != null && readConfig().appIdsOnKeyBoardHeightChangeFix.contains(appId);
    }

    /**
     * MSC navigateToMiniProgram api是否可以从MSC路由到MMP
     * https://km.sankuai.com/collabpage/2170059126
     *
     * @param mmpAppId
     * @return
     */
    public static boolean canNavigateToMMPFromMSC(String mmpAppId) {
        MSCHornRollbackConfig.Config config = readConfig();
        return config.mscNavigateToMMPWhiteList != null && config.mscNavigateToMMPWhiteList.contains(mmpAppId);
    }

    public static boolean isRollbackNavigateToAppPrefetch() {
        return readConfig().rollbackNavigateToAppPrefetch;
    }

    /**
     * 是否回滚：沉浸模式事件双线分发策略
     *
     * @return
     */
    public static boolean isRollbackSinkModeEventTwoLineDispatch() {
        return readConfig().rollbackSinkModeEventTwoLineDispatch;
    }

    public static boolean isRollbackLoadHTMLOptimize() {
        return readConfig().rollbackLoadHTMLOptimize;
    }

    public static boolean isRollbackPerformanceAppAttach() {
        return readConfig().rollbackPerformanceAppAttach;
    }

    /**
     * 获取TextArea组件adjustPosition延迟时间。
     *
     * @return
     */
    public static int getAdjustPositionDelayTime() {
        return readConfig().adjustPositionDelayTime;
    }

    /**
     * 回滚：textarea/input组件 adjustPosition延时执行。
     *
     * @param appId
     * @return
     */
    public static boolean isAdjustPositionDelay(String appId) {
        Set<String> whiteList = readConfig().adjustPositionDelayWhiteList;
        if (whiteList == null) {
            return false;
        }
        if (whiteList.contains("__ALL__")) {
            return true;
        }
        return whiteList.contains(appId);
    }

    public static boolean isRollbackGetMenuButtonBoundingClientRect() {
        return readConfig().rollbackGetMenuButtonBoundingClientRect;
    }

    public static boolean isRollbackPendingFrameWorkReady() {
        return readConfig().rollbackPendingFrameWorkReady;
    }

    public static boolean isRollbackTabPageRouteMappingFix() {
        return readConfig().rollbackTabPageRouteMappingFix;
    }

    public static boolean isRollbackTabBarConfig() {
        return readConfig().rollbackTabBarConfig;
    }

    public static boolean isRollbackSetRouteMappingPersist() {
        return readConfig().rollbackSetRouteMappingPersist;
    }

    public static boolean isRollbackMSCNavigationBarTitleFix() {
        return readConfig().rollbackMSCNavigationBarTitleFix;
    }

    public static boolean isRollbackCheckResource() {
        return readConfig().rollbackCheckResource;
    }

    public static boolean enableJSErrorReportStrategy() {
        return readConfig().enableJSErrorReportStrategy;
    }

    public static boolean isRollbackAppDataPrefetchJudge() {
        return readConfig().rollbackAppDataPrefetchJudge;
    }

    public static boolean isRollbackNewAppID() {
        return readConfig().rollbackNewAppId;
    }

    public static boolean isRollbackGatherInitMsi() {
        return readConfig().rollbackGatherInitMsi;
    }

    public static boolean isRollbackKeyboardHeightFix() {
        return readConfig().rollbackKeyboardHeightFix;
    }

    public static boolean isRollbackSetRouteMappingFix() {
        return readConfig().rollbackSetRouteMappingFix;
    }

    public static boolean isRollbackSwitchTabByClick() {
        return readConfig().rollbackSwitchTabByClick;
    }

    public static boolean isRollbackOnDestroyClearStartActivityCall() {
        return readConfig().rollbackOnDestroyClearStartActivityCall;
    }

    public static boolean isRollbackDisableRetryAppPrefetchIfLocateFail() {
        return readConfig().rollbackDisableRetryAppPrefetchIfLocateFail;
    }

    public static boolean isRollbackCheckMSCRuntimeLeak() {
        return readConfig().rollbackCheckMSCRuntimeLeak;
    }

    public static boolean isRollbackPHFDataPrefetchParam() {
        return readConfig().rollbackPHFDataPrefetchParam;
    }

    public static boolean isRollbackLatestAttachToContainerRuntimeLeakFix() {
        return readConfig().rollbackLatestAttachToContainerRuntimeLeakFix;
    }

    public static boolean isRollbackSwitchTabLifecycleFix() {
        return readConfig().rollbackSwitchTabLifecycleFix;
    }

    public static boolean isRollbackSinkModeBackgroundColor() {
        return readConfig().rollbackSinkModeBackgroundColor;
    }

    public static boolean isRenderCacheStorageFix() {
        return readConfig().renderCacheStorageFix;
    }

    public static String[] getUsingRenderCacheAppIds() {
        return readConfig().usingRenderCacheAppIds;
    }

    public static long getRenderCacheStorageLimit() {
        return readConfig().renderCacheStorageLimit;
    }

    public static boolean isRollbackAppPrefetchFirstContainer() {
        return readConfig().rollbackAppPrefetchFirstContainer;
    }

    public static boolean enableTextAreaInputFontFamily() {
        return readConfig().enableTextAreaInputFontFamily;
    }

    public static boolean enableRuntimeLocked() {
        return readConfig().enableRuntimeLocked;
    }

    public static boolean enableCustomPullLoadingIcon() {
        return readConfig().enableCustomPullLoadingIcon;
    }

    public static boolean fixNavigateToMiniProgramWhenMMPOffline() {
        return readConfig().fixNavigateToMiniProgramWhenMMPOffline;
    }

    public static boolean enableXiaomiFullScreenDeviceWithNavigationBar() {
        return readConfig().enableXiaomiFullScreenDeviceWithNavigationBar;
    }

    public static boolean enablePrefetchUnlockRuntime() {
        return readConfig().enablePrefetchUnlockRuntime;
    }

    public static boolean enableSetPagePopTransitionStyleErrno() {
        return readConfig().enableSetPagePopTransitionStyleErrno;
    }

    public static boolean enableUseNewFormatMsiApiErrno() {
        return readConfig().enableUseNewFormatMsiApiErrno;
    }

    public static boolean enableFixH5KeyboardCoverInput() {
        return readConfig().enableFixH5KeyboardCoverInput;
    }

    public static boolean enableOnAppEnterForegroundPath(String appId) {
        Set<String> enableOnAppEnterForegroundPathAppList = readConfig().enableOnAppEnterForegroundPathAppList;
        return enableOnAppEnterForegroundPathAppList != null &&
                (enableOnAppEnterForegroundPathAppList.contains("__ALL__") || enableOnAppEnterForegroundPathAppList.contains(appId));
    }

    public static boolean enablePageExitFixMissSceneAndDuration() {
        return readConfig().enablePageExitFixMissSceneAndDuration;
    }

    public static boolean enablePreCreateWidgetFFPFix() {
        return readConfig().enablePreCreateWidgetFFPFix;
    }

    public static boolean enableFixMMPToMSCIntercept() {
        return readConfig().enableFixMMPToMSCIntercept;
    }

    public static boolean enableIMGetAbsolutePath() {
        return readConfig().enableIMGetAbsolutePath;
    }

    public static boolean enablePageContainerLifecycleIntercept() {
        return readConfig().enablePageContainerLifecycleIntercept;
    }

    public static boolean enableStartActivityForResultWithOptions() {
        return readConfig().enableStartActivityForResultWithOptions;
    }

    public static boolean enableDynamicContentReport() {
        return readConfig().enableDynamicContentReport;
    }

    public static boolean enableHideLoadingIconAndAnimation(String appId) {
        Set<String> enableHideLoadingIconAndAnimationAppList = readConfig().enableHideLoadingIconAndAnimationAppList;
        return enableHideLoadingIconAndAnimationAppList != null &&
                (enableHideLoadingIconAndAnimationAppList.contains("__ALL__") || enableHideLoadingIconAndAnimationAppList.contains(appId));
    }

    public static boolean enableFixRouteMappingValidVersion() {
        return readConfig().enableFixRouteMappingValidVersion;
    }

    public static boolean enableCloseWebViewAccessibilityService(String appId) {
        return MSCHornUtils.isMatchWhiteConfigRule(readConfig().enableCloseWebViewAccessibilityServiceAppList, appId);
    }

    public static boolean enableTabBarBadgeUnitChange() {
        return readConfig().enableTabBarBadgeUnitChange;
    }

    public static boolean enableFixSubPkgPagePreload() {
        return readConfig().enableFixSubPkgPagePreload;
    }

    public static boolean enableOnPageStart() {
        return readConfig().enableOnPageStart;
    }

    /**
     * 预期采样上报的指标，防止预期以外的指标被采样上报
     *
     * @param key
     * @return
     */
    public static boolean isSamplingIndicators(String key) {
        return MSCHornUtils.isMatchWhiteConfigRule(readConfig().samplingIndicatorsWhiteList, key);
    }

    public static boolean enableFixOnTabItemTabAndSwitchTab() {
        return readConfig().enableFixOnTabItemTabAndSwitchTab;
    }

    public static boolean enableLoadPackageWithColor() {
        return readConfig().enableLoadPackageWithColor;
    }

    public static boolean enableBizPreloadImmediately() {
        return readConfig().enableBizPreloadImmediately;
    }

    public static boolean enableAppRouteTaskGetParamsNoException() {
        return readConfig().enableAppRouteTaskGetParamsNoException;
    }

    public static boolean enableReturnWhenHasSameBizPreloadTask() {
        return readConfig().enableReturnWhenHasSameBizPreloadTask;
    }

    public static boolean enableMsiMapListener() {
        return readConfig().enableMsiMapListener;
    }

    public static boolean enableWebViewMainThread() {
        return readConfig().enableWebViewMainThread;
    }

    public static boolean enableAddLoadPackageDetails() {
        return readConfig().enableAddLoadPackageDetails;
    }

    public static boolean enableOnPageStartQueue() {
        return readConfig().enableOnPageStartQueue;
    }

    public static boolean enableExternalAppStorageLimit() {
        return readConfig().enableExternalAppStorageLimit;
    }

    public static boolean enableExternalAppPageDepthLimit() {
        return readConfig().enableExternalAppPageDepthLimit;
    }

    public static boolean enableUseNewInjectPackageRate() {
        return readConfig().enableUseNewInjectPackageRate;
    }

    public static boolean enableReportTimeoutTaskWhenPageExit() {
        return readConfig().enableReportTimeoutTaskWhenPageExit;
    }

    public static boolean enableInitialLargeData() {
        return readConfig().enableInitialLargeData;
    }

    public static boolean enableFixOnBackgroundFetchTimestamp() {
        return readConfig().enableFixOnBackgroundFetchTimestamp;
    }

    public static boolean enablePrefetchOptimizer() {
        return readConfig().enablePrefetchOptimizer;
    }

    public static boolean fixWidgetLifeCycleManualInvoke(String appId) {
        return MSCHornUtils.isMatchWhiteConfigRule(readConfig().fixWidgetLifeCycleManualInvokeWhiteList, appId);
    }

    public static boolean enableFixCodeCacheLaunchReport() {
        return readConfig().enableFixCodeCacheLaunchReport;
    }

    public static boolean enableExternalAppCodeCacheLimit() {
        return readConfig().enableExternalAppCodeCacheLimit;
    }

    public static boolean enablePreDownloadConfigPkg() {
        return readConfig().enablePreDownloadConfigPkg;
    }

    public static boolean enableReportPreloadAndKeepAlive() {
        return readConfig().enableReportPreloadAndKeepAlive;
    }

    public static boolean enableFixOnResumeFocus(String appId) {
        return MSCHornUtils.isMatchWhiteConfigRule(readConfig().enableFixOnResumeFocusAppList, appId);
    }

    public static boolean enableLoadBasicPackagesByMerge(String appId) {
        return MSCHornUtils.isMatchWhiteConfigRule(readConfig().enableLoadBasicPackagesByMergeAppList, appId);
    }

    public static boolean enableExternalAppPrefSourceLimit() {
        return readConfig().enableExternalAppPrefSourceLimit;
    }

    public static boolean enableAppSharedCountLimit() {
        return readConfig().enableAppSharedCountLimit;
    }

    public static boolean enableExternalAppKeepAliveRule() {
        return readConfig().enableExternalAppKeepAliveRule;
    }

    public static boolean enablePreSendOnPageStart() {
        return readConfig().enablePreSendOnPageStart;
    }

    public static boolean enablePkgExtraParam() {
        return readConfig().enablePkgExtraParam;
    }

    public static boolean enableLoadingViewStyleOpt(String appId) {
        String[] disableLoadingViewStyleOptBlackList = readConfig().disableLoadingViewStyleOptBlackList;
        return !MSCHornUtils.isMatchWhiteConfigRule(disableLoadingViewStyleOptBlackList,appId);
    }

    public static boolean enableReportAPIPerformanceStatisticData() {
        return readConfig().enableReportAPIPerformanceStatisticData;
    }

    public static boolean enableSendFFPEndToMSI() {
        return readConfig().enableSendFFPEndToMSI;
    }

    public static boolean enableSplitChunks() {
        return readConfig().enableSplitChunks;
    }

    public static boolean enableLaunchTaskOnRoute() {
        return readConfig().enableLaunchTaskOnRoute;
    }

    public static boolean enableLocalAppStackPriority() {
        return readConfig().enableLocalAppStackPriority;
    }

    public static String localAppStackPriorityStrategy() {
        if (!enableLocalAppStackPriority()) {
            return null;
        }
        return readConfig().localAppStackPriorityStrategy;
    }
    public static boolean enablePhasedPrimaryMetricsReport() {
        return readConfig().enablePhasedPrimaryMetricsReport;
    }

    public static boolean enableJsRuntimeInfoProvider() {
        return readConfig().enableJsRuntimeInfoProvider;
    }

    public static boolean enableNavigateBackClearSpecifiedPageAppList(String appId) {
        return MSCHornUtils.isMatchWhiteConfigRule(readConfig().enableNavigateBackClearSpecifiedPageAppList, appId);
    }

    public static boolean enableControlPrefetchInSpecialScene() {
        return readConfig().enableControlPrefetchInSpecialScene;
    }

    public static boolean enableReportAPIDataFix() {
        return readConfig().enableReportAPIDataFix;
    }

    public static boolean enableReportAPIDataToFFP() {
        return readConfig().enableReportAPIDataToFFP;
    }

    public static boolean enableReportFirstABTest() {
        return readConfig().enableReportFirstABTest;
    }

    public static boolean enableJSEngineReport() {
        return readConfig().enableJSEngineReport;
    }
}
