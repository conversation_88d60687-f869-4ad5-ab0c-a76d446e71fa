package com.meituan.msc.modules.engine;

import android.content.SharedPreferences;
import com.meituan.msc.common.executor.MSCExecutors;
import com.meituan.msc.extern.MSCEnvHelper;
import com.meituan.msc.modules.reporter.MSCLog;
import org.json.JSONArray;
import org.json.JSONObject;
import java.util.ArrayList;
import java.util.List;
import java.util.concurrent.CopyOnWriteArrayList;
import java.util.concurrent.atomic.AtomicBoolean;

/**
 * 根据本地小程序栈设置权重，用于管理保活和预热引擎
 * https://km.sankuai.com/collabpage/2707197355
 */
public class MSCLocalAppStackHelper {
    private static final String TAG = "MSCLocalAppStackHelper";
    private static final String KEY_LOCAL_APP_STACK = "msc_local_app_stack";
    private static final int MAX_APP_STACK_SIZE = 10;
    // 当前应用生命周期内的应用栈
    private static final ArrayList<AppRecord> appStack = new ArrayList<>();
    // 宿主 app 首次进入小程序时为 false，此时在栈中新建一项  
    private static final AtomicBoolean hasRecorded = new AtomicBoolean(false);
    private static final CopyOnWriteArrayList<JSONObject> cachedStackList = new CopyOnWriteArrayList<>();
    // 标识 cachedStackList 是否已从 SharedPreferences 初始化
    private static final AtomicBoolean cachedStackListInitialized = new AtomicBoolean(false);
    // 缓存当前应用栈的JSONObject，避免每次都重新构建
    private static final JSONObject cachedCurrentStackJson = new JSONObject();

    /**
     * 确保 cachedStackList 已从 SharedPreferences 初始化
     */
    private static void ensureCachedStackListInitialized() {
        if (cachedStackListInitialized.compareAndSet(false, true)) {
            long start = System.currentTimeMillis();
            SharedPreferences sp = MSCEnvHelper.getSharedPreferences(KEY_LOCAL_APP_STACK);
            String stackStr = sp.getString(KEY_LOCAL_APP_STACK, "");
            List<JSONObject> stackList = new ArrayList<>();
            if (stackStr != null && !stackStr.isEmpty()) {
                try {
                    JSONArray stackArray = new JSONArray(stackStr);
                    for (int i = 0; i < stackArray.length(); i++) {
                        stackList.add(stackArray.getJSONObject(i));
                    }
                } catch (Exception e) {
                    // ignore parse error
                }
            }
            cachedStackList.clear();
            cachedStackList.addAll(stackList);
            long end = System.currentTimeMillis();
            MSCLog.i(TAG, "ensureCachedStackListInitialized cost: " + (end - start));
        }
    }

    public static void addAppRecord(AppRecord appRecord) {
        appStack.add(appRecord);
        // 同步更新缓存
        addRecordToCache(appRecord);
        addAppStackToSharedPreferences();
    }

    private static void addAppStackToSharedPreferences() {
        List<JSONObject> stackList = getStackListFromPreferences();
        /**
         * stackList数据结构为：
         * [
         *     {
         *         "appStack": [
         *             {
         *                 "appId": "appId1",
         *                 "isBizPreload": true
         *             },
         *             {
         *                 "appId": "appId2",
         *                 "isBizPreload": false
         *             }
         *         ]
         *     },
         *     {
         *         "appStack": [
         *             {
         *                 "appId": "appId3",
         *                 "isBizPreload": true
         *             }
         *         ]
         *     }
         * ]
         */
        if (hasRecorded.compareAndSet(false, true)) {
            // 宿主应用首次启动时，若超出栈大小，则移除最早的记录，并新增记录项
            if (stackList.size() >= MAX_APP_STACK_SIZE) {
                stackList.remove(0);
            }
            if (!appStack.isEmpty()) {
                stackList.add(cachedCurrentStackJson);
            }
        } else {
            if (!stackList.isEmpty()) {
                stackList.set(stackList.size() - 1, cachedCurrentStackJson);
            } else {
                stackList.add(cachedCurrentStackJson);
            }
        }
        saveStackListToPreferences(stackList);
    }

    private static List<JSONObject> getStackListFromPreferences() {
        ensureCachedStackListInitialized();
        return new ArrayList<>(cachedStackList);
    }

    /**
     * 获取只读的栈列表，用于权重计算等只读操作
     * 使用CopyOnWriteArrayList提供更好的并发读性能
     */
    public static CopyOnWriteArrayList<JSONObject> getReadOnlyStackListFromPreferences() {
        ensureCachedStackListInitialized();
        return cachedStackList;
    }

    private static void saveStackListToPreferences(final List<JSONObject> stackList) {
        cachedStackList.clear();
        cachedStackList.addAll(stackList);
        // 异步写入磁盘
        MSCExecutors.submit(new Runnable() {
            @Override
            public void run() {
                SharedPreferences sp = MSCEnvHelper.getSharedPreferences(KEY_LOCAL_APP_STACK);
                SharedPreferences.Editor editor = sp.edit();
                JSONArray newArray = new JSONArray(stackList);
                editor.putString(KEY_LOCAL_APP_STACK, newArray.toString());
                editor.commit();
            }
        });
    }
    
    /**
     * 将单个记录添加到缓存的JSONObject中
     */
    private static void addRecordToCache(AppRecord record) {
        try {
            // 首次调用时初始化 cachedCurrentStackJson
            if (!cachedCurrentStackJson.has("appStack")) {
                cachedCurrentStackJson.put("appStack", new JSONArray());
            }
            
            JSONObject obj = new JSONObject();
            obj.put("appId", record.appId);
            obj.put("isBizPreload", record.isBizPreload);
            cachedCurrentStackJson.getJSONArray("appStack").put(obj);
        } catch (Exception e) {
            // ignore
        }
    }

    public static int getAppIdKeepAliveWeight(String appId) {
        // 使用只读方法，获得更好的并发性能
        CopyOnWriteArrayList<JSONObject> stackList = getReadOnlyStackListFromPreferences();
        int weight = 1;
        for (JSONObject stackItem : stackList) {
            try {
                JSONArray appStackJson = stackItem.optJSONArray("appStack");
                if (appStackJson != null) {
                    int count = 0;
                    for (int i = 0; i < appStackJson.length(); i++) {
                        JSONObject record = appStackJson.optJSONObject(i);
                        if (record != null && appId.equals(record.optString("appId"))) {
                            count++;
                        }
                    }
                    if (count > 1) {
                        weight += (count - 1);
                    }
                }
            } catch (Exception e) {
                // ignore
            }
        }
        return weight;
    }

    public static int getAppIdBizPreloadWeight(String appId) {
        // 使用只读方法，获得更好的并发性能
        CopyOnWriteArrayList<JSONObject> stackList = getReadOnlyStackListFromPreferences();
        int weight = 1;
        try {
            for (JSONObject stackItem : stackList) {
                JSONArray appStackJson = stackItem.optJSONArray("appStack");
                if (appStackJson != null) {
                    for (int i = 0; i < appStackJson.length(); i++) {
                        JSONObject record = appStackJson.optJSONObject(i);
                        if (record != null && appId.equals(record.optString("appId")) && record.optBoolean("isBizPreload", false)) {
                            weight++;
                        }
                    }
                }
            }
        } catch (Exception e) {
            // ignore
        }
        return weight;
    }

    public static class AppRecord {
        public String appId;
        public boolean isBizPreload;

        public AppRecord(String appId, boolean isBizPreload) {
            this.appId = appId;
            this.isBizPreload = isBizPreload;
        }
    }
}
