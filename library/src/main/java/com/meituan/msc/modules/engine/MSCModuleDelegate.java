package com.meituan.msc.modules.engine;

import com.meituan.msc.common.utils.Primitives;
import com.meituan.msc.jse.bridge.NativeModuleCallExceptionHandler;
import com.meituan.msc.modules.manager.MSCRuntimeException;
import com.meituan.msc.modules.reporter.MSCLog;

import java.lang.reflect.InvocationHandler;
import java.lang.reflect.Method;
import java.lang.reflect.Proxy;

/**
 * Created by letty on 2022/3/16.
 **/
class MSCModuleDelegate {
    static <T> T createMSCModuleDelegate(Class<T> classOfT, NativeModuleCallExceptionHandler nativeModuleCallExceptionHandler) {
        if (!classOfT.isInterface()) {
            return null;
        }
        return (T) Proxy.newProxyInstance(
                classOfT.getClassLoader(),
                new Class[]{classOfT},
                new InvocationHandler() {
                    @Override
                    public Object invoke(Object proxy, Method method, Object[] args) throws Throwable {
                        if (method.getDeclaringClass() == Object.class) {
                            return method.invoke(this, args);
                        }
                        String msg = "can't find module:" + classOfT.getName() + ". While invoking method: " + method.getName();
                        if (nativeModuleCallExceptionHandler != null) {
                            nativeModuleCallExceptionHandler.handleException(new MSCRuntimeException(msg));
                        } else {
                            MSCLog.w(msg,"after destroy runtime");
                        }
                        return Primitives.returnForMethod(method);
                    }
                });
    }


}
