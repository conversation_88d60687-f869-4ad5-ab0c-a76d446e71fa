package com.meituan.msc.modules.engine;

import android.support.annotation.NonNull;

import com.meituan.msc.modules.apploader.events.AppLoadException;
import com.meituan.msc.modules.update.bean.PackageInfoWrapper;

public interface MSCPackageLoadCallback {

    void onPackageLoadSuccess(@NonNull PackageInfoWrapper packageInfo, boolean realLoaded);

    void onPackageLoadFailed(@NonNull PackageInfoWrapper packageInfo, AppLoadException e);
}
