package com.meituan.msc.modules.engine;

import android.app.Activity;
import android.content.Context;
import android.content.Intent;
import android.os.Handler;
import android.os.Looper;
import android.support.annotation.NonNull;
import android.support.annotation.Nullable;
import android.text.TextUtils;

import com.meituan.android.mercury.msc.adaptor.bean.MSCAppMetaInfo;
import com.meituan.msc.common.abtest.IMSCABTestKV;
import com.meituan.msc.common.config.MSCConfig;
import com.meituan.msc.common.executor.MSCExecutors;
import com.meituan.msc.common.framework.Callback;
import com.meituan.msc.common.framework.MPListenerManager;
import com.meituan.msc.common.framework.MSCRunningManager;
import com.meituan.msc.common.framework.interfaces.IBridge2JS;
import com.meituan.msc.common.perf.PerfEventConstant;
import com.meituan.msc.common.remote.RemoteService;
import com.meituan.msc.common.utils.MSCResourceWatermarkUtil;
import com.meituan.msc.common.utils.PathUtil;
import com.meituan.msc.common.utils.ToastUtils;
import com.meituan.msc.extern.MSCEnvHelper;
import com.meituan.msc.jse.bridge.CatalystInstance;
import com.meituan.msc.jse.bridge.ICallFunctionContext;
import com.meituan.msc.jse.bridge.JavaScriptModule;
import com.meituan.msc.jse.bridge.NativeModuleCallExceptionHandler;
import com.meituan.msc.lib.interfaces.IFileModule;
import com.meituan.msc.lib.interfaces.IFontfaceModule;
import com.meituan.msc.modules.IMSCLibraryInterfaceHelper;
import com.meituan.msc.modules.api.PageApi;
import com.meituan.msc.modules.api.legacy.appstate.AppStateModule;
import com.meituan.msc.modules.api.msi.permission.IPermission;
import com.meituan.msc.modules.api.network.RequestPrefetchModule;
import com.meituan.msc.modules.api.report.LogModule;
import com.meituan.msc.modules.api.report.MetricsModule;
import com.meituan.msc.modules.api.timing.TimingModule;
import com.meituan.msc.modules.api.widget.WidgetEventModule;
import com.meituan.msc.modules.apploader.IAppLoader;
import com.meituan.msc.modules.apploader.InstrumentLaunchManager;
import com.meituan.msc.modules.apploader.MSCAppLoader;
import com.meituan.msc.modules.container.ContainerManagerModule;
import com.meituan.msc.modules.container.IContainerDelegate;
import com.meituan.msc.modules.container.IContainerManager;
import com.meituan.msc.modules.container.IStartActivityModule;
import com.meituan.msc.modules.container.StartActivityModule;
import com.meituan.msc.modules.core.DeviceEventManagerInterface;
import com.meituan.msc.modules.core.DeviceEventManagerModule;
import com.meituan.msc.modules.core.FileModule;
import com.meituan.msc.modules.core.FontfaceModule;
import com.meituan.msc.modules.debug.SourceCodeModule;
import com.meituan.msc.modules.devtools.DevToolsLoader;
import com.meituan.msc.modules.devtools.IDevTools;
import com.meituan.msc.modules.devtools.IDevToolsProvider;
import com.meituan.msc.modules.engine.dataprefetch.IDataPrefetchModule;
import com.meituan.msc.modules.engine.dataprefetch.MSCDataPrefetchModule;
import com.meituan.msc.modules.engine.requestPrefetch.PageOutSideRequestPrefetchManagerCacheManager;
import com.meituan.msc.modules.engine.requestPrefetch.RequestPrefetchManager;
import com.meituan.msc.modules.env.MSCEnvironment;
import com.meituan.msc.modules.exception.ExceptionsInterface;
import com.meituan.msc.modules.exception.MSCExceptionsManagerModule;
import com.meituan.msc.modules.exception.MSCModuleCallExceptionHandler;
import com.meituan.msc.modules.manager.ExecutorContext;
import com.meituan.msc.modules.manager.IMSCLibraryInterface;
import com.meituan.msc.modules.manager.MSCEvent;
import com.meituan.msc.modules.manager.MSCEventBus;
import com.meituan.msc.modules.manager.MSCModule;
import com.meituan.msc.modules.manager.MSCModuleManager;
import com.meituan.msc.modules.manager.MSCRuntimeException;
import com.meituan.msc.modules.manager.MSCSubscriber;
import com.meituan.msc.modules.manager.UpdateManager;
import com.meituan.msc.modules.msi.IMSIManagerModule;
import com.meituan.msc.modules.msi.MSIManagerModule;
import com.meituan.msc.modules.navigation.INavigationModule;
import com.meituan.msc.modules.navigation.NavigationModule;
import com.meituan.msc.modules.page.IPageModule;
import com.meituan.msc.modules.page.reload.PageStackGlobalCache;
import com.meituan.msc.modules.page.render.ICssPreParseManager;
import com.meituan.msc.modules.page.render.IRenderer;
import com.meituan.msc.modules.page.render.webview.OnEngineInitFailedListener;
import com.meituan.msc.modules.page.render.webview.WebViewCacheManager;
import com.meituan.msc.modules.preload.MSCHornPreloadConfig;
import com.meituan.msc.modules.preload.PreloadManager;
import com.meituan.msc.modules.preload.PreloadTasksManager;
import com.meituan.msc.modules.reporter.CommonTags;
import com.meituan.msc.modules.reporter.JSErrorRecorder;
import com.meituan.msc.modules.reporter.MSCLog;
import com.meituan.msc.modules.reporter.MSCReporter;
import com.meituan.msc.modules.reporter.ReporterFields;
import com.meituan.msc.modules.reporter.preformance.PerformanceManager;
import com.meituan.msc.modules.router.MMPRouterRollbackManager;
import com.meituan.msc.modules.service.IJSRunningInfoCallback;
import com.meituan.msc.modules.service.IServiceEngine;
import com.meituan.msc.modules.sound.SoundManagerModule;
import com.meituan.msc.modules.statusbar.StatusBarModule;
import com.meituan.msc.modules.update.AppConfigModule;
import com.meituan.msc.modules.update.IPageLoadModule;
import com.meituan.msc.modules.update.MSCABTestModule;
import com.meituan.msc.modules.update.MSCAppModule;
import com.meituan.msc.modules.update.MSCHornBasePackageReloadConfig;
import com.meituan.msc.modules.update.PageLoadModule;
import com.meituan.msc.modules.update.bean.AppMetaInfoWrapper;
import com.meituan.msc.modules.update.metainfo.BackgroundCheckUpdateModule;
import com.meituan.msc.modules.websocket.WebSocketModule;
import com.meituan.msc.util.perf.PerfEventRecorder;
import com.meituan.msc.util.perf.PerfTrace;
import com.sankuai.android.jarvis.Jarvis;

import org.json.JSONArray;
import org.json.JSONObject;

import java.util.List;
import java.util.UUID;
import java.util.concurrent.ConcurrentHashMap;
import java.util.concurrent.ScheduledExecutorService;
import java.util.concurrent.atomic.AtomicInteger;

import static com.meituan.msc.common.utils.Constants.APP_ID_SHAN_GOU;
import static com.meituan.msc.modules.engine.RuntimeDestroyReason.NO_FIRST_RENDER;
import static com.meituan.msc.modules.reporter.ReporterFields.REPORT_MSC_DESTROY_RUNTIME_COUNT;

/**
 * 负责各主要对象的初始化及生命周期
 */
public class MSCRuntime {
    public final String TAG = "MSCRuntime@" + Integer.toHexString(hashCode());
    private final int runtimeId;
    // 标识首次启动过程中（未进入过保活状态），复用运行时启动后续页面的场景
    public boolean isReuseRuntimeLaunch;
    public boolean hasTriggerBasePreload;
    public boolean hasTriggerBizPreload;
    @Nullable
    private volatile MSCApp app;
    private final MSCEventBus eventBus;
    private final MSCModuleManager moduleManager;
    private final MSCAppLoader appLoader;
    private final RendererManager rendererManager;
    @Nullable
    private IDevTools devTools;
    private volatile RequestPrefetchManager requestPrefetchManager;   //NonNull但可能更换实例
    private volatile RuntimeSource source = RuntimeSource.NEW;
    // 每个小程序提供一个单线程调度池
    private final ScheduledExecutorService scheduledExecutorService = Jarvis.newSingleThreadScheduledExecutor("MSC-" + hashCode());
    /**
     * 当前绑定的页面个数
     */
    private int pageCount = 0;
    /**
     * 历史绑定过的页面个数，包含当前绑定的页面个数
     */
    private int historyPageCount = 0;

    public final WebViewCacheManager webViewCacheManager;
    public MSIManagerModule apisManager;
    private final AppService appService;
    public JSErrorRecorder jsErrorRecorder;
    private PerfEventRecorder perfEventRecorder;
    private final MSCRuntimeReporter runtimeReporter;
    private final MSCModuleCallExceptionHandler nativeExceptionHandler;
    private final MSCAppModule mMSCAppModule;
    private final AppConfigModule mAppConfigModule;

    private long basePreloadStartMilli = 0;

    /* 状态部分 */
    public boolean isDestroyed;

    private boolean hasEverFirstRender; //有渲染成功过
    public AtomicInteger messagePortLeakSize = new AtomicInteger(0);
    // 12.25.400 渲染缓存存储修复开关存在runtime生命周期内。
    private volatile Boolean renderCacheStorageFix = null;
    private volatile Boolean enableReportAPIDataFixCacheHorn = null;

    /**
     * 当运行时没有被使用的时候，直接销毁，不进行保活
     * 临时方案，后面将保活逻辑从Runtime抽取到运行时管理模块去
     */
    private volatile boolean destroyWhenUnused = false;
    private volatile RuntimeDestroyReason destroyWhenUnusedReason;
    // 基础包预热失败原因记录，用于启动耗时指标分析
    public String preloadBaseErrorMsg;
    // 基础包预热归因
    public String preloadBaseMissReason;
    // 基础库预热被降级框架管控原因
    private String basePreloadHitControlDetail;
    private String preloadBizErrorMsg;
    // 业务预热归因
    private String preloadBizMissReason;
    // 未命中保活原因
    private String keepAliveMissReason = "keepAliveNoLaunch";
    private String bizPreloadHitControlDetail;

    // 预下载失败原因
    public String batchCheckUpdateErrorMsg;
    // 基础库检查更新失败原因
    public String checkUpdateBasePackageErrorMsg;
    // 是否已进行业务包下载任务
    public volatile boolean existBizPackageDownload;
    // 是否禁止重新拉取元信息
    public volatile boolean forbidReFetchMetaInfoWhenPageNotFound;

    JSONObject mScriptPerformanceData;
    // 是否业务预热时复用了基础库预热的运行时
    private boolean isReuseBasePreloadWhenPreloadBiz;
    /**
     * 冷启动场景下，source会被修改，补充上报原始来源
     */
    // TODO: 2023/2/14 tianbin 待对齐runtimeSource 双端 不同容器 调研业务用法
    private RuntimeSource originSource;
    private final PerformanceManager performanceManager;
    private RuntimeStateBeforeLaunch runtimeStateBeforeLaunch;
    private boolean isPendingPreloadBiz;
    private volatile boolean hasRListAtCurrentPage;
    private boolean isRemoteBasePackageReloadConfigFetched;
    private volatile boolean disableReuse;
    private String preloadStrategyStr;
    private volatile boolean hasAppPrefetch;

    private boolean isServicePreInit = false;
    // 本次启动是否触发了基础库强更
    private boolean hasForceUpdateBasePkg = false;
    // 记录小程序启动时内存占用情况
    private long appStartLibcMemory = 0;
    private long appStartJavaMemory = 0;

    public CatalystInstance mCatalystInstance;

    public boolean isHasAppPrefetch() {
        return hasAppPrefetch;
    }

    public void setHasAppPrefetch(boolean hasAppPrefetch) {
        this.hasAppPrefetch = hasAppPrefetch;
    }

    //锁住引擎，isLocked=true，表示该引擎已经被业务锁定，其他业务不可获取使用。startApp前，Runtime没有与App信息绑定，使用isUsed判断存在基础库预热引擎被多业务使用问题。
    private volatile boolean isLocked;

    //引擎 appUUID
    private String appUUID;

    // 记录已加载的targetPath，value表示是否打开过此页面
    private final ConcurrentHashMap<String, Boolean> loadedTargetPaths = new ConcurrentHashMap<String, Boolean>();

    public boolean isDisableReuse() {
        return disableReuse;
    }

    public void setDisableReuse(boolean disableReuse) {
        this.disableReuse = disableReuse;
    }

    @NonNull
    public PerformanceManager getPerformanceManager() {
        return performanceManager;
    }

    public PerfEventRecorder getPerfEventRecorder() {
        return perfEventRecorder;
    }

    public void setPerfEventRecorder(PerfEventRecorder recorder) {
        perfEventRecorder = recorder;
    }

    /* 启动部分 */
    MSCRuntime() {
        PerfTrace.online().begin(PerfEventConstant.MSC_APP_CREATE).report();
        // keep this line at the beginning of this method
        perfEventRecorder = new PerfEventRecorder(true, true);
        perfEventRecorder.beginDurableEvent(PerfEventConstant.INIT_RUNTIME);

        RemoteService.init(MSCEnvHelper.getContext());
        eventBus = new MSCEventBus();
        moduleManager = new MSCModuleManager(this);
        moduleManager.registerModule(MSCExceptionsManagerModule.class, ExceptionsInterface.class);
        nativeExceptionHandler = new MSCModuleCallExceptionHandler(this);
        moduleManager.registerModule(ContainerManagerModule.class, IContainerManager.class);
        mMSCAppModule = new MSCAppModule();
        mAppConfigModule = new AppConfigModule();
        moduleManager.registerModule(mMSCAppModule);
        moduleManager.registerModule(mAppConfigModule);
        moduleManager.registerModule(MSCABTestModule.class, IMSCABTestKV.class);
        moduleManager.registerModule(NavigationModule.class, INavigationModule.class);
        moduleManager.registerModule(MSCEnvironment.class);
        moduleManager.registerModule(StartActivityModule.class, IStartActivityModule.class);
        moduleManager.registerModule(FileModule.class, IFileModule.class);
        moduleManager.registerModule(FontfaceModule.class, IFontfaceModule.class);
        jsErrorRecorder = new JSErrorRecorder();
        runtimeReporter = MSCRuntimeReporter.create(this);
        PageLoadModule pageLoadModule = new PageLoadModule();
        moduleManager.registerModule(pageLoadModule, IPageLoadModule.class);

        appLoader = new MSCAppLoader(MSCEnvHelper.getContext());
        moduleManager.registerModule(appLoader, IAppLoader.class);
        runtimeId = appLoader.getLoaderId();

        moduleManager.registerModule(MSCDataPrefetchModule.class, IDataPrefetchModule.class);

        webViewCacheManager = WebViewCacheManager.getInstance(); //appService可能依赖webViewCacheManager
        moduleManager.registerModule(LogModule.class);
        appService = new AppService(mOnEngineInitFailedListener);
        moduleManager.registerModule(appService, AppService.class, IBridge2JS.class);
        onMSCRuntimeCreate();
        TimingModule timingModule = new TimingModule();
        moduleManager.registerModule(timingModule, TimingModule.class);
        AppStateModule appStateModule = new AppStateModule();
        moduleManager.registerModule(appStateModule, AppStateModule.class);
        apisManager = new MSIManagerModule();
        moduleManager.registerModule(apisManager, IMSIManagerModule.class, IPermission.class);
        moduleManager.registerModule(MetricsModule.class);
        moduleManager.registerModule(WidgetEventModule.class);

        rendererManager = new RendererManager();
        rendererManager.setOnEngineInitFailedListener(mOnEngineInitFailedListener);
        moduleManager.registerModule(rendererManager, IRendererManager.class);
        moduleManager.registerModule(StatusBarModule.class);
        moduleManager.registerModule(SourceCodeModule.class);
        moduleManager.registerModule(DeviceEventManagerModule.class, DeviceEventManagerInterface.class);
        moduleManager.registerModule(SoundManagerModule.class);
        moduleManager.registerModule(WebSocketModule.class);
        moduleManager.registerModule(PageApi.class);
        moduleManager.registerModule(RequestPrefetchModule.class);
        subscribe(IRenderer.EVENT_PAGE_FIRST_RENDER, new MSCSubscriber() {
            @Override
            public void onReceive(MSCEvent event) {
                MSCLog.i(TAG, "onPageFirstRender received ");
                hasEverFirstRender = true;
                unsubscribe(this);
            }
        });
        moduleManager.registerModule(UpdateManager.class);
        moduleManager.registerModule(BackgroundCheckUpdateModule.class);
        MSCLog.iSync(TAG, "runtime created");
        // keep this line at the end of this method
        perfEventRecorder.endDurableEvent(PerfEventConstant.INIT_RUNTIME);
        performanceManager = new PerformanceManager(this);
        PerfTrace.online().end(PerfEventConstant.MSC_APP_CREATE).report();
    }

    private void onMSCRuntimeCreate() {
        IMSCLibraryInterface libraryInterface = IMSCLibraryInterfaceHelper.getIMSCLibraryInterface();
        if (libraryInterface != null) {
            libraryInterface.onMSCRuntimeCreate(moduleManager, mOnEngineInitFailedListener);
        }
    }

    /**
     * 启动App, 调用此方法之前，Runtime没有与App信息绑定
     *
     * @param appId
     * @return
     */
    public MSCApp startApp(String appId) {
        this.isLocked = true;
        this.app = new MSCApp(appId, this);
        moduleManager.attachApp(app);
        getMSCAppModule().setAppId(appId);
        getOrCreateRequestPrefetchManager();
        return app;
    }

    /**
     * 获取运行时唯一Id
     *
     * @return
     */
    public int getRuntimeId() {
        return runtimeId;
    }


    // 以下为获取小程序级别信息的方法，如果没有启动小程序，就Throw
    // 调用者如果没有处理App为null的情况，即要求必须有App信息，则先都使用此系列方法调用

    @NonNull
    public MSCApp getAppOrThrow() {
        if (app == null) throw new MSCRuntimeException("msc app not started");
        return app;
    }

    // 以下为获取小程序级别信息的方法，如果没有启动小程序，返回null
    // 使用此系列方法，调用者需要能够处理App为null的情况
    @Nullable
    public MSCApp getApp() {
        return app;
    }

    public String getAppId() {
        if (app != null) {
            return app.getAppId();
        }
        return null;
    }

    // 获取常用模块
    public IContainerManager getContainerManagerModule() {
        return getModule(IContainerManager.class);
    }

    public IPageModule getTopPageModule() {
        IContainerManager containerManager = getContainerManagerModule();
        if (containerManager != null) {
            return containerManager.getTopPage();
        }
        return null;
    }

    public Activity getTopActivity() {
        IContainerManager containerManager = getContainerManagerModule();
        if (containerManager != null) {
            return containerManager.getTopActivity();
        }
        return null;
    }

    public IFileModule getFileModule() {
        return getModule(IFileModule.class);
    }

    public IPageLoadModule getPageLoadModule() {
        return getModule(IPageLoadModule.class);
    }

    public MSCAppModule getMSCAppModule() {
        return mMSCAppModule;
    }

    public AppConfigModule getAppConfigModule() {
        return mAppConfigModule;
    }

    @Nullable
    public ICssPreParseManager getCssPreParseManager() {
        return getModule(ICssPreParseManager.class);
    }

    @Nullable
    public ICssPreParseManager getCssPreParseManagerWithoutDelegate() {
        return getModuleWithoutDelegate(ICssPreParseManager.class);
    }

    /**
     * api获取请求预拉取信息
     *
     * @return
     */
    public RequestPrefetchManager getRequestPrefetchManager() {
        return requestPrefetchManager;
    }

    @NonNull
    public RequestPrefetchManager getOrCreateRequestPrefetchManager() {
        if (requestPrefetchManager == null) {
            synchronized (this) {
                if (requestPrefetchManager == null) {
                    requestPrefetchManager = PageOutSideRequestPrefetchManagerCacheManager.getInstance().pollOrCreateRequestPrefetchManager(getAppId());
                    requestPrefetchManager.bindMSCRuntime(this);
                }
            }
        }
        return requestPrefetchManager;
    }

    /**
     * 新建请求预拉取管理者 或 将请求预拉取管理者重制状态
     *
     * @return
     */
    public RequestPrefetchManager resetOrCreateRequestPrefetchManager() {
        if (requestPrefetchManager != null && requestPrefetchManager.isStarted()) {
            requestPrefetchManager.reset();
        }
        if (requestPrefetchManager == null) {
            requestPrefetchManager = PageOutSideRequestPrefetchManagerCacheManager.getInstance().pollOrCreateRequestPrefetchManager(getAppId());
        }
        return requestPrefetchManager;
    }

    public RendererManager getRendererManager() {
        return rendererManager;
    }


    // MSCModule 模块注册/查询/方法调用相关

    public void registerModule(Class<? extends MSCModule> moduleImplClazz, Class... interfaces) throws RuntimeException {
        moduleManager.registerModule(moduleImplClazz, interfaces);
    }

    public void registerModule(MSCModule module, Class<?>... interfaces) {
        moduleManager.registerModule(module, interfaces);
    }

    public void unregisterModule(MSCModule module) {
        moduleManager.unregisterModule(module);
    }

    public void unregisterModule(String moduleName) {
        moduleManager.unregisterModule(getModule(moduleName));
    }

    /**
     * 直接创建JSModule代理方法避免每个方法都要判断npe
     *
     * @param classOfT
     * @param <T>
     * @return
     */
    @NonNull
    public <T extends JavaScriptModule> T getJSModuleDelegate(Class<T> classOfT) {
        return getJSModuleDelegate(classOfT, getNativeExceptionHandlerIfRuntimeAlive());
    }

    public <T extends JavaScriptModule> T getJSModuleDelegate(Class<T> classOfT, NativeModuleCallExceptionHandler exceptionHandler) {
        T module = getJSModule(classOfT);
        if (module != null) {
            return module;
        }
        return JavaScriptModuleDelegate.createJSModuleDelegate(classOfT, exceptionHandler);
    }

    public ScheduledExecutorService getScheduledExecutorService() {
        return scheduledExecutorService;
    }

    @Nullable
    public <T extends JavaScriptModule> T getJSModule(Class<T> classOfT) {
        if (isDestroyed) {
            MSCLog.i("getJSModule '" + classOfT.getName() + "' after destroyed");
            return null;
        }
        if (appService == null) {
            MSCLog.throwIfDebug("getJSModule '" + classOfT.getName() + "'while service is null");
            return null;
        }
        return appService.getJSModule(classOfT);
    }


    public <T> T getModule(Class<T> classOfT) {
        T module = moduleManager.getModule(classOfT);
        if (module != null) {
            return module;
        }
        // 仅在未销毁时做上报
        return MSCModuleDelegate.createMSCModuleDelegate(classOfT,
                getNativeExceptionHandlerIfRuntimeAlive());
    }

    public <T> T getModuleWithoutDelegate(Class<T> classOfT) {
        return moduleManager.getModule(classOfT);
    }

    public MSCModule getModule(String name) {
        return moduleManager.getModule(name);
    }

    public Object invoke(ICallFunctionContext context, String moduleName, String methodName, JSONArray params, ExecutorContext executorContext) {
        return moduleManager.invoke(context, moduleName, methodName, params, executorContext);
    }

    public Object invokeSync(String moduleName, String methodName, JSONArray params) {
        return moduleManager.invokeSync(moduleName, methodName, params);
    }

    public JSONArray getConfig(String moduleName) {
        return moduleManager.getConfig(moduleName);
    }

    // EventBus 消息发布/订阅相关

    public void publish(MSCEvent event) {
        eventBus.publish(event);
    }

    public void subscribe(String eventName, MSCSubscriber subscriber) {
        eventBus.register(eventName, subscriber);
    }

    public void unsubscribe(MSCSubscriber subscriber) {
        eventBus.unregister(subscriber);
    }

    public void unsubscribe(String eventName) {
        eventBus.unregister(eventName);
    }


    // 开启运行时调试
    public void enableDebug(final Context context, Intent intent) {
        if (intent != null) {
            IDevToolsProvider provider = DevToolsLoader.getDevToolsProvider();
            if (provider != null) {
                if (devTools != null) {
                    // 同一个Runtime复用vConsole中的WebView,不做销毁/释放,从缓存中获取
                    // devTools.close(true);
                }
                devTools = DevToolsLoader.getDevToolsProvider().createDevTools(context, this, intent);
                moduleManager.registerModule((MSCModule) devTools, IDevTools.class);
            }
            if (devTools != null) {
                devTools.connect();
            }
        }
    }

    public IDevTools getDevTools() {
        return devTools;
    }

    /**
     * 销毁运行时相关逻辑
     */
    public void destroy(boolean shouldRePreload, final String reason) {
        destroy(shouldRePreload, reason, null);
    }

    public void destroy(boolean shouldRePreload, final String reason, @Nullable Callback<Void> callback) {
        if (isDestroyed) {
            return;
        }
        destructHandler.post(new Runnable() {
            @Override
            public void run() {
                destroyInner(shouldRePreload, reason, callback);
            }
        });
    }

    private final Handler destructHandler = new Handler(Looper.getMainLooper());

    private void destroyInner(boolean shouldRePreload, String reason) {
        destroyInner(shouldRePreload, reason, null);
    }

    private void destroyInner(boolean shouldRePreload, String reason, @Nullable Callback<Void> callback) {
        destructHandler.removeCallbacksAndMessages(null);
        if (isDestroyed) {
            MSCLog.w(TAG, "runtime already destroyed");
            return;
        }
        long libcMem_b = MSCResourceWatermarkUtil.getAppLibcMemByte();
        long javaMem_b = MSCResourceWatermarkUtil.getAppUsedJavaMemByte();
        isDestroyed = true;
        perfEventRecorder.clearEvents();
        webViewCacheManager.release();
        MPListenerManager.getInstance().eventRecords.clear();
        RemoteService.unbindToMainProcess();
        if (RuntimeSource.BASE_PRELOAD.equals(getSource())) {
            PreloadManager.getInstance().setPreloadBaseMetricsInfo(RuntimeDestroyReason.getPreloadBaseMissReasonWhenDestroy(reason), "destroy runtime " + reason);
        }

        //移除和引擎相关的页面启动信息
        InstrumentLaunchManager.getInstance().removeLaunchInfo(this);

        String appId = "";
        if (app != null) {
            appId = app.getAppId();
            PageStackGlobalCache.getGlobalInstance().cleanStackForGlobalByAppId(app.getAppId());
            if (appLoader != null) {
                MSCRunningManager.finishApp(app.getAppId(), appLoader);
            }
            RuntimeManager.removeApp(app);
            RuntimeManager.addDestroyRuntimeReason(app.getAppId(), reason);
            setEngineDestroyReason(reason);
            // 触发状态监听器
            if (appLoader != null && appLoader.getStatusChangeListener() != null) {
                appLoader.getStatusChangeListener().onDestroyed(this, shouldRePreload);
            }
        }
        if (appLoader != null && !appLoader.isUsed()) {
            // TODO: 2022/5/24 tianbin 未预热完成场景 如何处理？
            String reportTag = appLoader.isBizPreloadReady() ?
                    ReporterFields.REPORT_MSC_BIZ_PRELOAD_USAGE_RATE : ReporterFields.REPORT_MSC_BASE_PRELOAD_USAGE_RATE;
            runtimeReporter.commonTag("reason", reason).once(reportTag).value(0).sendRealTime();
        }
        // 运行时销毁时，清除缓存的CSS解析结果
        if (!MSCHornPreloadConfig.disablePreParseCss() && getCssPreParseManager() != null &&
                TextUtils.equals(getAppId(), APP_ID_SHAN_GOU)) {
            // TODO 12.16.400 lizhen 只有闪购清理CSS预解析器
            MSCLog.i(TAG, "clearAllCssCache");
            getCssPreParseManager().clearAllCssCache();
        }
        ICssPreParseManager preParseCSSManager = getCssPreParseManagerWithoutDelegate();
        if (preParseCSSManager != null) {
            preParseCSSManager.clearAllCssCacheV2();
        }
        eventBus.destroy();
        moduleManager.destroy();
        if (devTools != null) {
            devTools.close(true);
            devTools = null;
            DevToolsLoader.getDevToolsProvider().removeDevTools(this);
        }

        long libcMem_e = MSCResourceWatermarkUtil.getAppLibcMemByte();
        long javaMem_e = MSCResourceWatermarkUtil.getAppUsedJavaMemByte();
        // 上报运行时销毁原因，用于排查运行时保活占比波动原因
        if (MSCHornRollbackConfig.enableReportPreloadAndKeepAlive()) {
            runtimeReporter.record(REPORT_MSC_DESTROY_RUNTIME_COUNT)
                .tag("reason", reason)
                .tag(CommonTags.TAG_LIBC_MEMORY_BEGIN, libcMem_b)
                .tag(CommonTags.TAG_LIBC_MEMORY_END, libcMem_e)
                .tag(CommonTags.TAG_JAVA_MEMORY_BEGIN, javaMem_b)
                .tag(CommonTags.TAG_JAVA_MEMORY_END, javaMem_e)
                .tag(CommonTags.TAG_BIZ_PRELOAD_MAX_COUNT, MSCHornPreloadConfig.get().getConfig().preloadAppLimitCount)
                .tag(CommonTags.TAG_KEEP_ALIVE_MAX_COUNT, RuntimeManager.getKeepAliveMaxSize())
                .tag(CommonTags.TAG_KEEP_ALIVE_MAX_TIME, MSCConfig.getEngineKeepAliveTime())
                .sendRealTime();
        } else {
            runtimeReporter.record(REPORT_MSC_DESTROY_RUNTIME_COUNT).tag("reason", reason).sendRealTime();
        }


        MSCLog.d(TAG, "destroy runtime:", this);
        checkMessagePortLeakAndReport(appId);

        if (callback != null) {
            callback.onSuccess(null);
        }

        if (!MSCHornRollbackConfig.isRollbackCheckMSCRuntimeLeak()) {
            RuntimeMemoryLeakMonitor.addRuntime(this);
            RuntimeMemoryLeakMonitor.checkAllMSCRuntimeLeaks();
        }
    }

    private void setEngineDestroyReason(String destroyReason) {
        if (!MSCHornRollbackConfig.enableReportPreloadAndKeepAlive()) {
            return;
        }
        if (appLoader != null && appLoader.isLaunched()) {
            RuntimeManager.keepAliveMissReasonMap.put(getAppId(), RuntimeDestroyReason.getKeepAliveMissReason(destroyReason));
        } else if (RuntimeSource.BIZ_PRELOAD.equals(getSource())) {
            String reason = RuntimeDestroyReason.getPreloadBizMissReasonWhenDestroy(destroyReason);
            PreloadManager.getInstance().putPreloadBizMetricsInfo(getAppId(), reason, "destroy biz preload runtime, " + reason);
        }
    }

    private void checkMessagePortLeakAndReport(String appId) {
        String runtimeHashCode = TAG;
        MSCExecutors.runOnUiThread(new Runnable() {
            @Override
            public void run() {
                int i = messagePortLeakSize.get();
                if (i > 0) {
                    new MSCReporter().record(ReporterFields.REPORT_MSC_WEBVIEW_MESSAGEPORT_LEAK_COUNT)
                            .tag("mscAppId", appId)
                            .tag("runtime", runtimeHashCode)
                            .tag("messagePortLeakSize", i)
                            .sendDelay();
                }
            }
        });
    }

    public void deActive(boolean preferKeepAlive) {
        MSCLog.i(TAG, "deActive runtime");
        if (!appLoader.isUsable()) {
            MSCLog.w(TAG, "cannot be reused by state");
            destroyInner(false, RuntimeDestroyReason.toString(RuntimeDestroyReason.NOT_USABLE));
            return;
        }
        boolean canKeepAlive = preferKeepAlive;

        // 如果需要回滚MMP/MSC路由映射配置, 则不进行保活
        canKeepAlive = canKeepAlive && !MMPRouterRollbackManager.needRollback(app != null ? app.getAppId() : null);

        // 没有成功渲染结束的不保留引擎 page层文件错误导致的加载失败
        if (!hasEverFirstRender) {
            canKeepAlive = false;
        }

        if (canKeepAlive) { // 如果元信息来源S3，不保活
            MSCAppModule mscAppModule = getMSCAppModule();
            if (mscAppModule != null) {
                AppMetaInfoWrapper metaInfo = mscAppModule.getMetaInfo();
                if (metaInfo != null && metaInfo.getMetaFrom() == MSCAppMetaInfo.META_FROM_S3) {
                    canKeepAlive = false;
                    MSCLog.i(TAG, "deActive runtime, meta from s3, not keep alive");
                }
            }
        }

//        MSCRuntime全局是否firstRender过 有现成的地方吗？
//        if (canKeepAlive && app != null && !getAppConfig().isFusionModeEnabled()) {
//            boolean isStandardModeKeepAlive;
//            // 传媒模式保活，仅保活传统模式的最后一个实例，要求在activity的onDestroy被记录后再调用
//            isStandardModeKeepAlive = MSCConfig.isEnableStandardModeKeepAlive() && !MSCFusionActivityMonitor.hasActivityAlive(getAppId());
//            MSCLog.d(TAG, "standard mode keep alive: " + isStandardModeKeepAlive);
//            // 传统模式且不保活时立即释放
//            if (!isStandardModeKeepAlive) {
//                canKeepAlive = false;
//            }
//        }
        boolean finalCanKeepAlive = canKeepAlive;

//        final IServiceEngine engine = appService.getEngine();
//        // 如果发现保活的引擎内存已经超过阈值，则不再保活，直接销毁
//        if (engine instanceof IEngineMemoryHelper) {
//            ((IEngineMemoryHelper) engine).getJsMemoryUsage(new IEngineMemoryHelper.JSMemoryListener() {
//                @Override
//                public void onGetMemorySuccess(long jsMemoryKB) {
//                    // 获取v8内存是异步操作，回调回来之后还需要检查一下retainCount, 避免此期间有activity启动
//                    if (mRetainCount.get() == 0) {
//                        long jsMemoryMB = jsMemoryKB / 1024;
//                        if (MSCConfig.isEnableEngineReleaseOnMemoryExceed() && jsMemoryMB > MSCConfig.getEngineMemoryThreshold()) {
//                            // 内存过大直接销毁
//                            handleNormalRelease(false);
//                            MSCLog.w(TAG, "MMP JSEngine memory heap size too large: " + jsMemoryMB + " MB, AppEngine is released");
//                        } else {
//                            // 走正常保活/销毁逻辑
//                            handleNormalRelease(finalCanKeepAlive);
//                        }
//                    }
//                }
//            });
//        } else {
        // 走正常保活/销毁逻辑

        handleNormalRelease(finalCanKeepAlive);
//        }
    }

    public void destroyEngineIfNoCount(String reason) {
        destroyEngineIfNoCountWithCallback(reason, null);
    }

    public void destroyEngineIfNoCount(String reason, Callback<Void> callback) {
        destroyEngineIfNoCountWithCallback(reason, callback);
    }

    public void destroyEngineIfNoCountWithCallback(String reason, @Nullable Callback<Void> callback) {
        if (!hasContainerAttached()) {
            if (source == RuntimeSource.BASE_PRELOAD) {
                PreloadManager.getInstance().preloadBaseErrorMsg = "destroy runtime," + reason;
            }
            MSCLog.i(TAG, "destroyEngineIfNoCountWithCallback", this);
            // 下一周期再释放，预防当前在进行中的操作需要用到engine，若用到可以通过retainCount取消退出
            destroy(false, reason, callback);
        } else {
            MSCLog.i(TAG, "hasContainerAttached is true");
        }
    }

    public void active() {
        MSCLog.i(TAG, "active runtime");

        RuntimeManager.reActiveKeepAliveEngine(getApp());
        destructHandler.removeCallbacksAndMessages(null);
    }

    /**
     * 引擎因用户退出而正常销毁，而非失败退出/内存不足被清除等情况
     * 也可能因允许保活而进入已释放引用而暂未销毁的状态
     */
    private void handleNormalRelease(boolean canKeepAlive) {
        if (!isDestroyWhenUnused() && canKeepAlive) {
            enterKeepAlive();
        } else {
            MSCLog.i(TAG, "normal destroy app engine and keep alive not allowed");
            if (!MSCHornRollbackConfig.get().getConfig().isRollbackPreloadBaseWhenNoKeepAlive) {
                PreloadTasksManager.instance.preloadBasePackageAgain(0);
            }

            String reason;
            if (!canKeepAlive) {
                reason = RuntimeDestroyReason.toString(NO_FIRST_RENDER);
            } else {
                reason = RuntimeDestroyReason.toString(destroyWhenUnusedReason);
            }
            destroy(MSCConfig.shouldReloadEngineAfterMemoryExceed(), reason);
        }
    }

    /**
     * 引擎进入保活，包含融合模式/传统模式保活
     */
    private void enterKeepAlive() {
        if (app == null) return;
        // 自动化测试Log 勿删
        MSCLog.i(TAG, "[MSC][KeepAlive]start:", app.getAppId());

        // 尝试进入保活状态，如果权重不足则无法保活
        boolean keepAliveSuccess = RuntimeManager.addKeepAliveApp(app);
        if (!keepAliveSuccess) {
            // 权重不足，无法保活，直接销毁运行时
            destroyInner(getAppConfigModule().shouldRePreload(), RuntimeDestroyReason.toString(RuntimeDestroyReason.KEEP_ALIVE_WEIGHT_NOT_ENOUGH));
            return;
        }

        long keepAliveTime;
        MSCRuntime runtime = app.getRuntime();
        if (MSCHornRollbackConfig.enableExternalAppKeepAliveRule() && runtime != null && runtime.getMSCAppModule().getExternalApp()) {
            keepAliveTime = MSCConfig.getExternalAppKeepAliveTime();
        } else {
            keepAliveTime = MSCConfig.getEngineKeepAliveTime();
        }
        destructHandler.postDelayed(new Runnable() {
            @Override
            public void run() {
                // 自动化测试Log 勿删
                MSCLog.i(TAG, "[MSC][KeepAlive]end:", app.getAppId());
                MSCLog.i(TAG, "normal destroy app engine by keep alive time out: ", keepAliveTime / 1000);
                ToastUtils.toastIfDebug("保活时间到，销毁引擎：" + getAppId());
                destroyInner(getAppConfigModule().shouldRePreload(), RuntimeDestroyReason.toString(RuntimeDestroyReason.KEEP_ALIVE_TIME_EXCEED));
            }
        }, keepAliveTime);

        // 清除本次启动的记录，以避免干扰relaunch
        // 放在此时机偏保守，且reporter的info map也应清除，后续考虑实现
        MPListenerManager.getInstance().eventRecords.clear();

        // 触发状态监听器
        if (appLoader != null) {
            appLoader.onKeepAlive();
            appLoader.getRuntime().setSource(RuntimeSource.KEEP_ALIVE);
            appLoader.getRuntime().setHasAppPrefetch(false);
            RuntimeManager.addDestroyRuntimeReason(app.getAppId(), "");
            appLoader.getRuntime().setRuntimeStateBeforeLaunch(RuntimeStateBeforeLaunch.KEEP_ALIVE);
            RuntimeManager.addLastEnterKeepAliveTime(app.getAppId(), System.currentTimeMillis());
        }
        if (MSCHornRollbackConfig.enableReportPreloadAndKeepAlive()) {
            runtimeReporter.reportKeepAliveCount(this);
        }
    }

    // 通过事件直接发送
    /**
     * 包含前端Service JS引擎/Page层WebView的初始化失败
     */
    @SuppressWarnings("FieldCanBeLocal")
    private final OnEngineInitFailedListener mOnEngineInitFailedListener = new OnEngineInitFailedListener() {
        @Override
        public void onEngineInitFailed(Exception e) {
            MSCExecutors.postOnUiThread(new Runnable() {
                @Override
                public void run() {
                    appLoader.onEngineInitFailed(e);
                }
            });
        }
    };

    public boolean isDestroyWhenUnused() {
        return destroyWhenUnused;
    }

    public void setDestroyWhenUnused(boolean destroyWhenUnused) {
        this.destroyWhenUnused = destroyWhenUnused;
    }

    public void setDestroyWhenUnusedReason(RuntimeDestroyReason destroyWhenUnusedReason) {
        this.destroyWhenUnusedReason = destroyWhenUnusedReason;
    }

    public boolean hasContainerAttached() {
        return getContainerManagerModule() != null && getContainerManagerModule().getContainerCount() > 0;
    }

    public RuntimeSource getSource() {
        return source;
    }

    public boolean isServicePreInit() {
        return isServicePreInit;
    }

    public void setServicePreInit(boolean servicePreInit) {
        this.isServicePreInit = servicePreInit;
    }

    public int getPageCount() {
        return pageCount;
    }

    public void incrementPageCount(int incrementCount) {
        pageCount += incrementCount;
    }

    public int getHistoryPageCount() {
        return historyPageCount;
    }

    public void incrementHistoryPageCount() {
        historyPageCount += 1;
    }

    public void setSource(RuntimeSource source) {
        this.source = source;
    }

    public boolean isPreload() {
        return source == RuntimeSource.BASE_PRELOAD || source == RuntimeSource.BIZ_PRELOAD;
    }

    public MSCRuntimeReporter getRuntimeReporter() {
        return runtimeReporter;
    }

    public MSCModuleCallExceptionHandler getNativeExceptionHandler() {
        return nativeExceptionHandler;
    }

    public MSCModuleCallExceptionHandler getNativeExceptionHandlerIfRuntimeAlive() {
        return isDestroyed ? null : nativeExceptionHandler;
    }

    public long getBasePreloadTime() {
        return basePreloadStartMilli;
    }

    public void setBasePreloadTime(long basePreloadTime) {
        this.basePreloadStartMilli = basePreloadTime;
    }

    public void setBasePreloadFailReason(String preloadBaseErrorMsg) {
        this.preloadBaseErrorMsg = preloadBaseErrorMsg;
    }

    public void setPreloadBaseMissReason(String preloadBaseMissReason) {
        this.preloadBaseMissReason = preloadBaseMissReason;
    }

    public void setBasePreloadHitControlDetail(String hitControlDetail) {
        this.basePreloadHitControlDetail = hitControlDetail;
    }

    public String getBasePreloadHitControlDetail() {
        return this.basePreloadHitControlDetail;
    }

    public void setBizPreloadHitControlDetail(String hitControlDetail) {
        this.bizPreloadHitControlDetail = hitControlDetail;
    }

    public String getBizPreloadHitControlDetail() {
        return this.bizPreloadHitControlDetail;
    }

    public void setBizPreloadErrorMsg(String preloadBizErrorMsg) {
        this.preloadBizErrorMsg = preloadBizErrorMsg;
    }

    public String getBizPreloadErrorMsg() {
        return this.preloadBizErrorMsg;
    }
    public void setBizPreloadMissReason(String preloadBizMissReason) {
        this.preloadBizMissReason = preloadBizMissReason;
    }

    public String getBizPreloadMissReason() {
        return this.preloadBizMissReason;
    }

    public void setKeepAliveMissReason(String keepAliveMissReason) {
        this.keepAliveMissReason = keepAliveMissReason;
    }

    public String getKeepAliveMissReason() {
        return this.keepAliveMissReason;
    }

    public void markBasePkgAsForceUpdate() {
        hasForceUpdateBasePkg = true;
    }

    public boolean hasForceUpdateBasePkg() {
        return hasForceUpdateBasePkg;
    }

    public JSONObject getScriptPerformanceData() {
        return mScriptPerformanceData;
    }

    public void setScriptPerformanceData(JSONObject mScriptPerformanceData) {
        this.mScriptPerformanceData = mScriptPerformanceData;
    }

    /**
     * 当前小程序只有一个容器
     *
     * @return
     */
    public boolean hasOnlyOneContainer() {
        List<IContainerDelegate> containerDelegates = getContainerManagerModule().getContainerDelegates();
        return containerDelegates == null || containerDelegates.size() <= 1;
    }

    /**
     * 是否为页面退出
     */
    private boolean isExit = false;

    public void setIsExit(boolean isExit) {
        this.isExit = isExit;
    }

    public boolean getIsExit() {
        return isExit;
    }

    private boolean isProcessGone = false;
    public void setIsProcessGone(boolean isProcessGone) {
        this.isProcessGone = isProcessGone;
    }

    public boolean getIsProcessGone() {
        return isProcessGone;
    }

    public void setIsReuseBasePreload() {
        isReuseBasePreloadWhenPreloadBiz = true;
    }

    public boolean isReuseBasePreloadWhenPreloadBiz() {
        return isReuseBasePreloadWhenPreloadBiz;
    }

    public void setOriginSource(RuntimeSource source) {
        originSource = source;
    }

    public RuntimeSource getOriginSource() {
        return originSource;
    }

    public RuntimeStateBeforeLaunch getRuntimeStateBeforeLaunch() {
        return runtimeStateBeforeLaunch;
    }

    public void setRuntimeStateBeforeLaunch(RuntimeStateBeforeLaunch runtimeStateBeforeLaunch) {
        this.runtimeStateBeforeLaunch = runtimeStateBeforeLaunch;
    }

    public void setIsPendingPreloadBiz(boolean isPendingPreloadBiz) {
        this.isPendingPreloadBiz = isPendingPreloadBiz;
    }

    public boolean isPendingPreloadBiz() {
        return isPendingPreloadBiz;
    }

    public void setHasRListAtCurrentPage(boolean hasRListAtCurrentPage) {
        this.hasRListAtCurrentPage = hasRListAtCurrentPage;
    }

    public boolean hasRListAtCurrentPage() {
        return hasRListAtCurrentPage;
    }

    public void cacheIsRemoteBasePackageReloadConfigFetched() {
        isRemoteBasePackageReloadConfigFetched = MSCHornBasePackageReloadConfig.get().isFetchedRemoteConfig;
    }

    public boolean isRemoteBasePackageReloadConfigFetched(){
        return isRemoteBasePackageReloadConfigFetched;
    }

    public boolean isBizPreloadAndNoLaunched() {
        return getModule(IAppLoader.class).isBizPreloadAndNoLaunched();
    }

    public long getLastJSMemoryUsageAfterPackageLoaded() {
        return appService.lastJSMemoryUsageAfterPackageLoaded;
    }

    public WebViewCacheManager getWebViewCacheManager() {
        return webViewCacheManager;
    }

    public void setPreloadStrategyStr(String preloadStrategyStr) {
        this.preloadStrategyStr = preloadStrategyStr;
    }

    public String getPreloadStrategyStr() {
        return preloadStrategyStr;
    }

    public boolean isRenderCacheStorageFix() {
        if (renderCacheStorageFix == null) {
            renderCacheStorageFix = MSCHornRollbackConfig.isRenderCacheStorageFix();
        }
        return renderCacheStorageFix;
    }

    public boolean isLocked() {
        //关闭获取引擎后锁住能力，返回false
        if (!MSCHornRollbackConfig.enableRuntimeLocked()) {
            return false;
        }

        MSCLog.i(TAG, "[MSC]isLocked: ", this.isLocked);
        return this.isLocked;
    }

    public void lock() {
        this.isLocked = true;
    }

    public void unLock() {
        this.isLocked = false;
    }

    //如果已经被业务使用，不能解锁。只有基础库预热引擎，还未设置app时可解锁
    public void unLockWhenNotUsed() {
        if (TextUtils.isEmpty(getAppId())
                && getSource() == RuntimeSource.BASE_PRELOAD
                && (appLoader == null || !appLoader.isUsed())) {
            this.isLocked = false;
            MSCLog.i(TAG, "[MSC]unLock");
        }
    }

    public String getAppUUID() {
        if (TextUtils.isEmpty(appUUID)) {
            appUUID = UUID.randomUUID().toString();
        }

        return appUUID;
    }

    public void setAppStartLibcMemory(long appStartLibcMemory) {
        this.appStartLibcMemory = appStartLibcMemory;
    }

    public void setAppStartJavaMemory(long appStartJavaMemory) {
        this.appStartJavaMemory = appStartJavaMemory;
    }

    public long getAppStartLibcMemory() {
        return appStartLibcMemory;
    }

    public long getAppStartJavaMemory() {
        return appStartJavaMemory;
    }

    public void addLoadedTargetPath(String targetPath, boolean isLaunch) {
        String purePath = PathUtil.getPath(targetPath);
        if (!TextUtils.isEmpty(purePath)) {
            boolean isPathLaunched = loadedTargetPaths.containsKey(purePath) && loadedTargetPaths.get(purePath);
            loadedTargetPaths.put(purePath, isPathLaunched || isLaunch);
        }
    }

    public void removeLoadedTargetPath(String targetPath) {
        String purePath = PathUtil.getPath(targetPath);
        if (!TextUtils.isEmpty(purePath)) {
            loadedTargetPaths.remove(purePath);
        }
    }

    public boolean canPreloadTargetPath() {
        int count = 0;
        for (Boolean value : loadedTargetPaths.values()) {
            if (Boolean.FALSE.equals(value)) {
                count++;
            }
            if (count >= MSCHornPreloadConfig.bizPreloadPageLimitCount()) {
                MSCLog.i(TAG, "cannot preload more path");
                return false;
            }
        }
        return true;
    }

    public boolean isTargetPathLoaded(String targetPath) {
        String purePath = PathUtil.getPath(targetPath);
        return !TextUtils.isEmpty(purePath) && loadedTargetPaths.containsKey(purePath);
    }

    public boolean isRuntimeBizPreloading() {
        if (appLoader != null && appLoader.getRuntimeStateBeforeLaunch() == RuntimeStateBeforeLaunch.BIZ_PRELOADING_FROM_BASE || appLoader.getRuntimeStateBeforeLaunch() == RuntimeStateBeforeLaunch.BIZ_PRELOADING_FROM_NEW) {
            return true;
        }
        return false;
    }

    public void recordAppStackWhenEnter() {
        String appId = getAppId();
        if (!TextUtils.isEmpty(appId)) {
            MSCLocalAppStackHelper.addAppRecord(new MSCLocalAppStackHelper.AppRecord(appId, source == RuntimeSource.BIZ_PRELOAD));
        }
    }

    public void getJsRunningInfo(IJSRunningInfoCallback callback) {
        if (callback == null) {
            return;
        }
        IServiceEngine serviceEngine = appService.getEngine();
        if (serviceEngine == null) {
            callback.onGetRunningInfo(null);
            return;
        }
        serviceEngine.getJsRunningInfo(callback);
    }

    public boolean enableReportAPIDataFix() {
        if (enableReportAPIDataFixCacheHorn == null) {
            enableReportAPIDataFixCacheHorn = MSCHornRollbackConfig.enableReportAPIDataFix();
        }
        return enableReportAPIDataFixCacheHorn;
    }
}
