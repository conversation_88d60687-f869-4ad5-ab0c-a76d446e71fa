package com.meituan.msc.modules.engine;

import com.meituan.msc.modules.update.MSCAppModule;
import com.meituan.msc.modules.update.bean.AppMetaInfoWrapper;

/**
 * 放一些不属于MSCRuntime的职责，但是在其他地方会经常使用的方法
 */
public class MSCRuntimeHelper {
    public static String getAppVersion(MSCRuntime runtime) {
        if (runtime == null) {
            return null;
        }
        MSCAppModule appModule = runtime.getMSCAppModule();
        if (appModule == null) {
            return null;
        }
        AppMetaInfoWrapper metaInfo = appModule.getMetaInfo();
        return metaInfo != null ? metaInfo.getVersion() : null;
    }
    /**
     * 销毁保活和预热引擎
     */
    public static void destroyKeepAliveAndBizPreloadEngine() {
        RuntimeManager.destroyKeepAliveEngineAndBizPreloadEngine(RuntimeDestroyReason.ACTIVE_CALL);
    }
}
