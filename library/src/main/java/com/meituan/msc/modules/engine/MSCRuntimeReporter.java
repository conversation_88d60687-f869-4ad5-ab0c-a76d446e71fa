package com.meituan.msc.modules.engine;

import android.support.annotation.NonNull;
import android.support.annotation.Nullable;
import android.text.TextUtils;

import com.meituan.dio.easy.DioFile;
import com.meituan.metrics.MetricsRuntime;
import com.meituan.msc.common.config.MSCConfig;
import com.meituan.msc.common.report.MetricsEntry;
import com.meituan.msc.common.utils.Constants;
import com.meituan.msc.common.utils.MPConcurrentHashMap;
import com.meituan.msc.common.utils.PathUtil;
import com.meituan.msc.common.utils.MSCResourceWatermarkUtil;
import com.meituan.msc.extern.MSCEnvHelper;
import com.meituan.msc.jse.bridge.LoadJSCodeCacheCallback;
import com.meituan.msc.modules.apploader.IAppLoader;
import com.meituan.msc.modules.apploader.LaunchTaskManager;
import com.meituan.msc.modules.apploader.events.AppLoadException;
import com.meituan.msc.modules.container.ContainerLaunchErrorManager;
import com.meituan.msc.modules.container.IContainerDelegate;
import com.meituan.msc.modules.page.render.AppPageReporter;
import com.meituan.msc.modules.preload.MSCHornPreloadConfig;
import com.meituan.msc.modules.reporter.CommonTags;
import com.meituan.msc.modules.reporter.MSCCommonTagReporter;
import com.meituan.msc.modules.reporter.MSCLoadErrorConstants;
import com.meituan.msc.modules.reporter.MSCLog;
import com.meituan.msc.modules.reporter.ReporterFields;
import com.meituan.msc.modules.service.codecache.CodeCacheLoadStatusCounter;
import com.meituan.msc.modules.update.MSCAppModule;
import com.meituan.msc.modules.update.bean.AppMetaInfoWrapper;
import com.meituan.msc.modules.update.bean.PackageInfoWrapper;

import org.json.JSONException;
import org.json.JSONObject;

import java.lang.ref.WeakReference;
import java.util.Collection;
import java.util.HashMap;
import java.util.List;
import java.util.UUID;
import java.util.concurrent.ConcurrentHashMap;

public class MSCRuntimeReporter extends MSCCommonTagReporter {
    public static MSCRuntimeReporter create(MSCRuntime mscRuntime) {
        MSCRuntimeReporter mscRuntimeReporter = new MSCRuntimeReporter(CommonTags.build(mscRuntime), mscRuntime);
        Object T2Time = MSCEnvHelper.getTag("T2");
        if (T2Time instanceof Long) {
            mscRuntimeReporter.addStatisticsToMap("T2", (Long) T2Time);
        }
        return mscRuntimeReporter;
    }

    private final WeakReference<MSCRuntime> runtimeWeakReference;
    private int evaluateJsFileCount = 0;
    private CodeCacheLoadStatusCounter codeCacheLoadStatusCounter = new CodeCacheLoadStatusCounter();
    private long preloadStartTime = 0;
    private long preloadEndTime = 0;
    private String preloadAppId = UNKNOWN_VALUE;

    private long readBasePkgServiceFileDuration = 0;
    private long readMainBizPkgServiceFileDuration = 0;
    private long readSubBizPkgServiceFileDuration = 0;
    private int readBasePkgServiceFileSize = 0;
    private int readMainBizPkgServiceFileSize = 0;
    private int readSubBizPkgServiceFileSize = 0;
    private String serviceInjectSubBizPkgName = UNKNOWN_VALUE;
    private int prefetchResponseSize = 0;
    private long preloadLibcMemoryByteBegin = 0;
    private long preloadLibcMemoryByteEnd = 0;
    private long preloadUsedJavaMemoryByteBegin = 0;
    private long preloadUsedJavaMemoryByteEnd = 0;
    private long preloadTaskFromApplicationStartTime = 0;

    /**
     * MSC 优选启动关键节点指标同步
     * <a href="https://km.sankuai.com/collabpage/2108447301">...</a>
     */
    private final ConcurrentHashMap<String, Long> statisticsMap = new MPConcurrentHashMap<>();

    private MSCRuntimeReporter(CommonTags commonTags, MSCRuntime mscRuntime) {
        super(commonTags);
        runtimeWeakReference = new WeakReference<>(mscRuntime);
    }

    @Override
    public void reportJSError(JSONObject error, MSCRuntime runtime) {
        // 获取顶部页面的 Reporter
        MSCRuntime mscRuntime = runtimeWeakReference.get();

        //如果不包含from参数，添加from="fe", 表示上报来源FE
        if (error != null && !error.has(REPORT_JS_FROM_KEY)) {
            try {
                error.put(REPORT_JS_FROM_KEY, "fe"); //异常上报来源FE
            } catch (JSONException jsonException) {
                MSCLog.e(TAG, jsonException, "AddFromParam");
            }
        }

        if (mscRuntime == null) {
            super.reportJSError(error, null);
            return;
        }

        appendExtraInfoTag(mscRuntime, error);

        // 先尝试获取当前页面的 reporter，获取不到再直接上报
        AppPageReporter appPageReporter = getTopAppPageReporter(mscRuntime);
        if (appPageReporter != null) {
            appPageReporter.reportJSError(error, mscRuntime);
        } else {
            super.reportJSError(error, mscRuntime);
        }
    }

    private void appendExtraInfoTag(@NonNull MSCRuntime mscRuntime, JSONObject error) {
        MSCAppModule mscAppModule = mscRuntime.getMSCAppModule();
        if (mscAppModule == null) {
            return;
        }
        try {
            AppMetaInfoWrapper metaInfo = mscAppModule.getMetaInfo();
            if (metaInfo != null) {
                PackageInfoWrapper mainPackageWrapper = mscAppModule.getMainPackageWrapper();
                if (mainPackageWrapper != null) {
                    error.put("isDeletedMainPackage", mainPackageWrapper.isPackageDeleted);
                    error.put("isMainPackageFromNet", mainPackageWrapper.isFromNet());
                    error.put("preCheckMainPackageFileExist", mainPackageWrapper.preCheckFileExist());
                    error.put("preCheckMainPackageIsMd5Same", mainPackageWrapper.preCheckIsMd5Same());
                }
            }
            PackageInfoWrapper basePackage = mscAppModule.getBasePackage();
            if (basePackage != null) {
                error.put("isDeletedBasePackage", basePackage.isPackageDeleted);
                error.put("isBasePackageFromNet", basePackage.isFromNet());
                error.put("preCheckBasePackageFileExist", basePackage.preCheckFileExist());
                error.put("preCheckBasePackageIsMd5Same", basePackage.preCheckIsMd5Same());
            }
            String preloadStrategyStr = mscRuntime.getPreloadStrategyStr();
            if (!TextUtils.isEmpty(preloadStrategyStr)) {
                error.put("afterT3PreloadStrategy", preloadStrategyStr);
            }
            error.put("isRuntimeDestroy", mscRuntime.isDestroyed);
            // TODO: 2023/5/30 tianbin 暂不处理分包场景
        } catch (JSONException e) {
            MSCLog.e(TAG, e, "appendDeletePackageTag");
        }
    }

    // 供 render组件 com.meituan.msc.exception.MSCRenderExceptionHandler 调用
    public void reportRenderError(JSONObject error, String errorKey) {
        MSCRuntime runtime = runtimeWeakReference.get();
        reportRenderError(error, errorKey, runtime);
    }

    @Override
    public void reportRenderError(JSONObject error, String errorKey, MSCRuntime mscRuntime) {
        if (mscRuntime == null) {
            super.reportRenderError(error, errorKey, null);
            return;
        }

        // 先尝试获取当前页面的 reporter，获取不到再直接上报
        AppPageReporter appPageReporter = getTopAppPageReporter(mscRuntime);
        if (appPageReporter != null) {
            appPageReporter.reportRenderError(error, errorKey, mscRuntime);
        } else {
            super.reportRenderError(error, errorKey, mscRuntime);
        }
    }

    public void reportRuntimeLeak(String leakType) {
        MetricsEntry entry = record(ReporterFields.MSC_RUNTIME_LEAK);
        String leakReportID = UUID.randomUUID().toString();
        entry.tag("leakType", leakType)
                .tag("leakReportID", leakReportID);
        HashMap<String, String> koomTags = new HashMap<>();
        koomTags.put("isMSCRuntimeLeak", "true");
        // 在上报的hprof中添加维度：https://km.sankuai.com/collabpage/2486080057
        String firstLeakReportID = MetricsRuntime.instance().getBizTags().get("bizTag-koom-firstLeakReportID");
        int leakReportCount = 1;
        if (firstLeakReportID == null) {
            koomTags.put("firstLeakReportID", leakReportID);
        } else {
            koomTags.put("latestLeakReportID", leakReportID);
            leakReportCount = Integer.parseInt(MetricsRuntime.instance().getBizTags().get("bizTag-koom-leakReportCount")) + 1;
        }
        koomTags.put("leakReportCount", Integer.toString(leakReportCount));
        MetricsRuntime.instance().attachBizTag(MetricsRuntime.Module_KOOM, koomTags);
        entry.sendDelay();
    }

    public void reportRenderMessage(String message) {
        if (getRenderReporter() == null || !getRenderReporter().enableReportRenderMessage()) {
            return;
        }
        if (TextUtils.isEmpty(message)) {
            return;
        }
        JSONObject jsonObject = new JSONObject();
        try {
            jsonObject.put("message", message);
            jsonObject.put("isFatal", false);
            jsonObject.put("isNativeError", true);
        } catch (JSONException e) {
            e.printStackTrace();
        }
        // 获取顶部页面的 Reporter
        MSCRuntime mscRuntime = runtimeWeakReference.get();
        reportRenderError(jsonObject, ReporterFields.REPORT_RENDER_NATIVE_MESSAGE_COUNT, mscRuntime);
    }

    public void reportLoadFail(AppLoadException exception) {
        once(ReporterFields.REPORT_APP_LOAD_FAIL)
                .tag("errorMessage", exception != null ? exception.getMessage() : null)
                .tag("errorCode", exception != null ? exception.getErrorCode() : -1)
                .sendRealTime();
    }

    public void recordJsFileEvaluate(DioFile jsFile) {
        if (jsFile == null) {
            return;
        }
        evaluateJsFileCount += 1;
    }

    public void recordJsFileEvaluate(Collection<DioFile> jsFiles) {
        if (jsFiles == null) {
            return;
        }
        evaluateJsFileCount += jsFiles.size();
    }

    /**
     * 记录执行的JS文件，用于统计执行的JS文件数量
     *
     * @param mscRuntime
     * @param jsFile
     */
    public static void recordJsFileEvaluate(MSCRuntime mscRuntime, DioFile jsFile) {
        MSCRuntimeReporter runtimeReporter = getRuntimeReporter(mscRuntime);
        if (runtimeReporter != null) {
            runtimeReporter.recordJsFileEvaluate(jsFile);
        }
    }

    public static void recordJsFileEvaluate(MSCRuntime mscRuntime, Collection<DioFile> jsFiles) {
        MSCRuntimeReporter runtimeReporter = getRuntimeReporter(mscRuntime);
        if (runtimeReporter != null) {
            runtimeReporter.recordJsFileEvaluate(jsFiles);
        }
    }

    public void recordJsCodeCacheLoadStatus(String jsCodeCachePath, LoadJSCodeCacheCallback.LoadStatus status) {
        codeCacheLoadStatusCounter.increment(status);
    }

    public static MSCRuntimeReporter getRuntimeReporter(MSCRuntime mscRuntime) {
        return mscRuntime != null ? mscRuntime.getRuntimeReporter() : null;
    }

    public CodeCacheLoadStatusCounter getCodeCacheLoadStatusCounter() {
        return codeCacheLoadStatusCounter;
    }

    public int getEvaluateJsFileCount() {
        return evaluateJsFileCount;
    }

    public String getPreloadBaseErrorMsg() {
        if (runtimeWeakReference == null) {
            return UNKNOWN_VALUE;
        }
        MSCRuntime mscRuntime = runtimeWeakReference.get();
        if (mscRuntime == null) {
            return UNKNOWN_VALUE;
        }
        return mscRuntime.preloadBaseErrorMsg;
    }

    public String getPreloadBaseMissReason() {
        if (runtimeWeakReference == null) {
            return "basePreloadUnknownError";
        }
        MSCRuntime mscRuntime = runtimeWeakReference.get();
        if (mscRuntime == null) {
            return "basePreloadUnknownError";
        }
        return mscRuntime.preloadBaseMissReason;
    }

    public String getBatchCheckUpdateErrorMsg() {
        if (runtimeWeakReference == null) {
            return UNKNOWN_VALUE;
        }
        MSCRuntime mscRuntime = runtimeWeakReference.get();
        if (mscRuntime == null) {
            return UNKNOWN_VALUE;
        }
        return mscRuntime.batchCheckUpdateErrorMsg;
    }

    public String getCheckUpdateBasePackageErrorMsg() {
        MSCRuntime mscRuntime = getRuntime();
        if (mscRuntime == null) {
            return UNKNOWN_VALUE;
        }
        return mscRuntime.checkUpdateBasePackageErrorMsg;
    }

    @Nullable
    private MSCRuntime getRuntime() {
        if (runtimeWeakReference == null) {
            return null;
        }
        return runtimeWeakReference.get();
    }

    public long getPreloadStartTime() {
        return preloadStartTime;
    }

    public MSCRuntimeReporter setPreloadStartTime(long preloadStartTime) {
        this.preloadStartTime = preloadStartTime;
        return this;
    }

    public long getPreloadEndTime() {
        return preloadEndTime;
    }

    public MSCRuntimeReporter setPreloadEndTime(long preloadEndTime) {
        this.preloadEndTime = preloadEndTime;
        return this;
    }

    public String getPreloadAppId() {
        return preloadAppId;
    }

    public MSCRuntimeReporter setPreloadAppId(String preloadAppId) {
        if (preloadAppId == null) {
            // 如果是null，则设置为基础库预热类型
            preloadAppId = "base";
        }
        this.preloadAppId = preloadAppId;
        return this;
    }

    public long getReadBasePkgServiceFileDuration() {
        return readBasePkgServiceFileDuration;
    }

    public MSCRuntimeReporter setReadBasePkgServiceFileDuration(long readBasePkgServiceFileDuration) {
        this.readBasePkgServiceFileDuration = readBasePkgServiceFileDuration;
        return this;
    }

    public long getReadMainBizPkgServiceFileDuration() {
        return readMainBizPkgServiceFileDuration;
    }

    public MSCRuntimeReporter setReadMainBizPkgServiceFileDuration(long readMainBizPkgServiceFileDuration) {
        this.readMainBizPkgServiceFileDuration = readMainBizPkgServiceFileDuration;
        return this;
    }

    public long getReadSubBizPkgServiceFileDuration() {
        return readSubBizPkgServiceFileDuration;
    }

    public MSCRuntimeReporter setReadSubBizPkgServiceFileDuration(long readSubBizPkgServiceFileDuration) {
        this.readSubBizPkgServiceFileDuration = readSubBizPkgServiceFileDuration;
        return this;
    }

    public int getReadBasePkgServiceFileSize() {
        return readBasePkgServiceFileSize;
    }

    public MSCRuntimeReporter setReadBasePkgServiceFileSize(int readBasePkgServiceFileSize) {
        this.readBasePkgServiceFileSize = readBasePkgServiceFileSize;
        return this;
    }

    public int getReadMainBizPkgServiceFileSize() {
        return readMainBizPkgServiceFileSize;
    }

    public MSCRuntimeReporter setReadMainBizPkgServiceFileSize(int readMainBizPkgServiceFileSize) {
        this.readMainBizPkgServiceFileSize = readMainBizPkgServiceFileSize;
        return this;
    }

    public int getReadSubBizPkgServiceFileSize() {
        return readSubBizPkgServiceFileSize;
    }

    public MSCRuntimeReporter setReadSubBizPkgServiceFileSize(int readSubBizPkgServiceFileSize) {
        this.readSubBizPkgServiceFileSize = readSubBizPkgServiceFileSize;
        return this;
    }

    public String getServiceInjectSubBizPkgName() {
        return serviceInjectSubBizPkgName;
    }

    public MSCRuntimeReporter setServiceInjectSubBizPkgName(String serviceInjectSubBizPkgName) {
        this.serviceInjectSubBizPkgName = serviceInjectSubBizPkgName;
        return this;
    }

    public int getPrefetchResponseSize() {
        return prefetchResponseSize;
    }

    public MSCRuntimeReporter setPrefetchResponseSize(int prefetchResponseSize) {
        this.prefetchResponseSize = prefetchResponseSize;
        return this;
    }

    public void resetPackageServiceInfo() {
        readBasePkgServiceFileDuration = 0;
        readMainBizPkgServiceFileDuration = 0;
        readSubBizPkgServiceFileDuration = 0;
        readBasePkgServiceFileSize = 0;
        readMainBizPkgServiceFileSize = 0;
        readSubBizPkgServiceFileSize = 0;
        serviceInjectSubBizPkgName = UNKNOWN_VALUE;
    }

    public void resetPrefetchResponseSize() {
        prefetchResponseSize = 0;
    }

    public void resetReportDataWhenLaunchPage() {
        // 每次启动页面之前，重置FFP分阶段指标将要上报的信息
        resetPackageServiceInfo();
        resetPrefetchResponseSize();
    }

    public boolean isPendingPreloadBiz() {
        MSCRuntime mscRuntime = getRuntime();
        if (mscRuntime == null) {
            return false;
        }
        return mscRuntime.isPendingPreloadBiz();
    }

    public void reportWebViewUrl(String fromUrl, String toUrl) {
        record(ReporterFields.MSC_WEBVIEW_COMPONENT_URL_PORTAL)
                .tag("from_url", fromUrl)
                .tag("to_url", toUrl)
                .sendRealTime();
    }

    @Deprecated
    public void reportMSCLoadError(int errorCode, Throwable e) {
        realReportMSCLoadError(null, errorCode, e == null ? "" : e.toString());
    }

    @Deprecated
    public void reportMSCLoadError(int errorCode, String errorMessage) {
        realReportMSCLoadError(null, errorCode, errorMessage);
    }

    public void reportMSCLoadError(boolean hasContainerAttached, int errorCode, Throwable e) {
        realReportMSCLoadError(hasContainerAttached, errorCode, e == null ? "" : e.toString());
    }

    public void reportMSCLoadError(boolean hasContainerAttached, int errorCode, String errorMessage) {
        realReportMSCLoadError(hasContainerAttached, errorCode, errorMessage);
    }

    private void realReportMSCLoadError(Boolean hasContainerAttached, int errorCode, String errorMessage) {
        MetricsEntry record = record(ReporterFields.REPORT_MSC_LOAD_ERROR_COUNT);
        if (hasContainerAttached != null) {
            record.tag("isLaunched", hasContainerAttached); //runtime是否页面启动状态
        }
        record.tag("errorCode", getErrorCode(errorCode, errorMessage))
                .tag("errorMessage", errorMessage)
                .sendRealTime();
        // 缓存启动过程中的错误码，用户点击重新加载后随页面加载成功率指标上报，辅助分析重试可恢复的问题场景
        MSCRuntime runtime = getRuntime();
        if (runtime != null) {
            ContainerLaunchErrorManager.getInstance().appendLaunchErrorCode(runtime.getAppId(), errorCode);
        }
    }

    public static int getErrorCode(AppLoadException exception) {
        if (exception == null) {
            return -1;
        }
        String message = exception.getMessage();
        return getErrorCode(exception.getErrorCode(), message);
    }

    public static int getErrorCode(int errorCode, String errorMessage) {
        if (errorMessage != null) {
            if (errorMessage.contains("OOM")) {
                return MSCLoadErrorConstants.ERROR_CODE_OOM;
            } else if (errorMessage.contains("checkUpdate mscApps is empty")) {
                // TODO: 2024/6/18 tianbin 临时修正下，需要分析为什么被重置了
                return MSCLoadErrorConstants.ERROR_FETCH_METAINFO_RESULT_EMPTY;
            } else if (errorMessage.contains("task is nonexistent")) {
                // TODO: 2024/6/18 tianbin 待排查问题
                return MSCLoadErrorConstants.ERROR_TASK_NOT_EXIST;
            }
        }
        return errorCode;
    }

    public Boolean isRemoteBasePackageReloadConfigFetched() {
        if (runtimeWeakReference == null) {
            return null;
        }
        MSCRuntime mscRuntime = runtimeWeakReference.get();
        if (mscRuntime == null) {
            return null;
        }
        return mscRuntime.isRemoteBasePackageReloadConfigFetched();
    }

    public void addStatisticsToMap(String key) {
        long time = System.currentTimeMillis();
        MSCLog.i("MMPStatics", "key: " + key + " time:" + time);
        statisticsMap.put(key, time);
    }

    public void addStatisticsToMap(String key, long time) {
        MSCLog.i("MMPStatics", "key: " + key + " time:" + time);
        statisticsMap.put(key, time);
    }

    public ConcurrentHashMap<String, Long> getStatisticsMap() {
        return statisticsMap;
    }

    public long getLauncherTime() {
        Long launcherTime = statisticsMap.get(Constants.LAUNCH);
        if (launcherTime != null) {
            return launcherTime;
        } else {
            return 0;
        }
    }

    // TODO: 2024/5/13 tianbin 修改美团中其他组件设置 T2时间的代码
    public long getT2Time() {
        Long t2Time = statisticsMap.get("T2");
        if (t2Time != null) {
            return t2Time;
        } else {
            return 0;
        }

    }

    public void reportWebViewCreateAfterDestroy() {
        record(ReporterFields.REPORT_WEBVIEW_CREATE_AFTER_DESTROY_COUNT).sendRealTime();
    }

    public void reportTaskTimeoutWhenPageExit(String path) {
        MSCRuntime mscRuntime = getRuntime();
        if (mscRuntime == null) {
            return;
        }

        MetricsEntry metricsEntry = record(ReporterFields.REPORT_MSC_TASK_TIMEOUT_COUNT);
        List<String> timeoutTasks = mscRuntime.getModule(IAppLoader.class).getRunningTimeoutTasks(MSCConfig.getLoadTaskTimeout());
        if (timeoutTasks == null || timeoutTasks.isEmpty()) {
            return;
        }
        int errorCode = MSCLoadErrorConstants.ERROR_CODE_LOAD_OTHER_TASK_TIMEOUT;
        if (timeoutTasks.size() == 1) {
            String taskName = timeoutTasks.get(0);
            if (TextUtils.equals(taskName, LaunchTaskManager.ITaskName.FETCH_BASE_PKG_TASK)) {
                errorCode = MSCLoadErrorConstants.ERROR_CODE_LOAD_BASE_TASK_TIMEOUT;
            } else if (TextUtils.equals(taskName, LaunchTaskManager.ITaskName.FETCH_BUZ_PKG_TASK)) {
                errorCode = MSCLoadErrorConstants.ERROR_CODE_LOAD_MAIN_TASK_TIMEOUT;
            } else if (TextUtils.equals(taskName, LaunchTaskManager.ITaskName.FETCH_SUB_BUZ_PKG_TASK)) {
                errorCode = MSCLoadErrorConstants.ERROR_CODE_LOAD_SUB_TASK_TIMEOUT;
            } else if (TextUtils.equals(taskName, LaunchTaskManager.ITaskName.FETCH_META_INFO_TASK)) {
                errorCode = MSCLoadErrorConstants.ERROR_CODE_LOAD_META_INFO_TASK_TIMEOUT;
            }
        }

        metricsEntry.tag("errorCode", errorCode);
        if (!TextUtils.isEmpty(path)) {
            metricsEntry.tag(CommonTags.TAG_PAGE_PATH, path);
            metricsEntry.tag(CommonTags.TAG_PURE_PAGE_PATH, PathUtil.getPath(path));
        }
        metricsEntry.tag("timeoutTasks", timeoutTasks);
        metricsEntry.sendRealTime();
    }

    public MSCRuntimeReporter setPreloadLibcMemoryByteBegin(long preloadLibcMemoryByteBegin) {
        this.preloadLibcMemoryByteBegin = preloadLibcMemoryByteBegin;
        return this;
    }

    public MSCRuntimeReporter setPreloadLibcMemoryByteEnd(long preloadLibcMemoryByteEnd) {
        this.preloadLibcMemoryByteEnd = preloadLibcMemoryByteEnd;
        return this;
    }

    public MSCRuntimeReporter setPreloadUsedJavaMemoryByteBegin(long preloadUsedJavaMemoryByteBegin) {
        this.preloadUsedJavaMemoryByteBegin = preloadUsedJavaMemoryByteBegin;
        return this;
    }

    public MSCRuntimeReporter setPreloadUsedJavaMemoryByteEnd(long preloadUsedJavaMemoryByteEnd) {
        this.preloadUsedJavaMemoryByteEnd = preloadUsedJavaMemoryByteEnd;
        return this;
    }

    public MSCRuntimeReporter setPreloadTaskFromApplicationStartTime(long preloadTaskFromApplicationStartTime) {
        this.preloadTaskFromApplicationStartTime = preloadTaskFromApplicationStartTime;
        return this;
    }

    public long getPreloadLibcMemoryByteBegin() {
        return preloadLibcMemoryByteBegin;
    }

    public long getPreloadUsedJavaMemoryByteBegin() {
        return preloadUsedJavaMemoryByteBegin;
    }

    public long getPreloadTaskFromApplicationStartTime() {
        return preloadTaskFromApplicationStartTime;
    }

    public void reportKeepAliveCount(MSCRuntime runtime) {
        if (runtime == null) {
            return;
        }
        long libcMem_e = MSCResourceWatermarkUtil.getAppLibcMemByte();
        long javaMem_e = MSCResourceWatermarkUtil.getAppUsedJavaMemByte();
        record(ReporterFields.REPORT_KEEP_ALIVE_COUNT)
            .tag("mscAppId", runtime.getAppId())
            .tag(CommonTags.TAG_LIBC_MEMORY_BEGIN, runtime.getAppStartLibcMemory())
            .tag(CommonTags.TAG_LIBC_MEMORY_END, libcMem_e)
            .tag(CommonTags.TAG_JAVA_MEMORY_BEGIN, runtime.getAppStartJavaMemory())
            .tag(CommonTags.TAG_JAVA_MEMORY_END, javaMem_e)
            .tag(CommonTags.TAG_KEEP_ALIVE_MAX_TIME, MSCConfig.getEngineKeepAliveTime())
            .tag(CommonTags.TAG_CURRENT_BIZ_PRELOAD_COUNT, RuntimeManager.getPreloadBizAppIds().size())
            .tag(CommonTags.TAG_CURRENT_KEEP_ALIVE_COUNT, RuntimeManager.getKeepAliveAppSize())
            .tag(CommonTags.TAG_BIZ_PRELOAD_MAX_COUNT, MSCHornPreloadConfig.get().getConfig().preloadAppLimitCount)
            .tag(CommonTags.TAG_KEEP_ALIVE_MAX_COUNT, RuntimeManager.getKeepAliveMaxSize())
            .sendDelay();
    }
}
