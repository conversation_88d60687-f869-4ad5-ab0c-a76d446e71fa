package com.meituan.msc.modules.engine;

import android.content.Context;
import android.content.SharedPreferences;
import android.support.annotation.NonNull;
import android.support.annotation.Nullable;
import android.text.TextUtils;

import com.meituan.msc.common.config.MSCConfig;
import com.meituan.msc.common.executor.MSCExecutors;
import com.meituan.msc.extern.MSCEnvHelper;
import com.meituan.msc.modules.reporter.MSCLog;

import org.json.JSONArray;

import java.util.ArrayList;
import java.util.Collections;
import java.util.LinkedHashSet;
import java.util.List;
import java.util.Set;

/**
 * 记录最近打开的小程序
 * Created by letty on 2019/11/18.
 **/
public class RecentMSCManager {
    private static final String SP_RECENT_RECORD = "mmp_recent_app";
    private static final String KEY_RECENT_RECORD_APP_ID = "mmp_recent_app_appid";
    private static final String SP_RECENT_LIST_RECORD = "mmp_recent_app_list";
    private static final String KEY_RECENT_RECORD_APP_ID_LIST = "mmp_recent_app_appid_list";
    private static final String TAG = "RecentMMPManager";
    private static final Set<String> appIdSet = Collections.synchronizedSet(new LinkedHashSet<>());
    private static volatile boolean isLoadedRecentAppList = false;

    public static void recordRecentAPPAsync(String appId, boolean isUrlExternalApp) {
        if (MSCHornRollbackConfig.enableExternalAppPrefSourceLimit()) {
            if (!isUrlExternalApp) {
                RecentMSCManager.recordRecentAPPAsync(appId);
            }
        } else {
            RecentMSCManager.recordRecentAPPAsync(appId);
        }
    }

    public static void recordRecentAPPAsync(String appId) {
        MSCExecutors.ioSerialized.submit(new Runnable() {
            @Override
            public void run() {
                recordRecentAPP(appId);
            }
        });
    }

    public static void recordRecentAPP(String appId) {
        if (TextUtils.isEmpty(appId)) {
            return;
        }

        SharedPreferences sharedPreferences = MSCEnvHelper.getSharedPreferences(SP_RECENT_RECORD);
        sharedPreferences.edit().putString(KEY_RECENT_RECORD_APP_ID, appId).apply();

        SharedPreferences recentAppListSP = MSCEnvHelper.getSharedPreferences(SP_RECENT_LIST_RECORD);
        recentAppListSP.edit().putString(KEY_RECENT_RECORD_APP_ID_LIST, createRecentAppsJSONStr(appId)).apply();
    }

    private static String createRecentAppsJSONStr(@NonNull String appId) {
        if (!isLoadedRecentAppList) {
            getRecentApps();
        }
        // 细节：最近打开的小程序，如已记录，需要调整顺序到最后
        if (!appIdSet.add(appId)) {
            appIdSet.remove(appId);
            appIdSet.add(appId);
        }
        JSONArray array = new JSONArray(appIdSet);
        return array.toString();
    }

    public static String getRecentAPP() {
        return MSCEnvHelper.getSharedPreferences(SP_RECENT_RECORD).getString(KEY_RECENT_RECORD_APP_ID, null);
    }

    @Nullable
    public static Set<String> getRecentApps() {
        String recentAppIds = MSCEnvHelper.getSharedPreferences(SP_RECENT_LIST_RECORD).getString(KEY_RECENT_RECORD_APP_ID_LIST, null);
        if (TextUtils.isEmpty(recentAppIds)) {
            return null;
        }

        JSONArray array;
        try {
            array = new JSONArray(recentAppIds);
            for (int i = 0; i < array.length(); i++) {
                appIdSet.add(array.getString(i));
            }
        } catch (Throwable e) {
            MSCLog.e(TAG, e);
        }
        isLoadedRecentAppList = true;
        return appIdSet;
    }

    @NonNull
    public static List<String> getRecentAppList() {
        Set<String> recentApps = getRecentApps();
        List<String> recentAppList = new ArrayList<>();
        if (recentApps != null) {
            recentAppList.addAll(recentApps);
        }
        int maxSize = MSCConfig.getBatchUpdateRecentAppMaxSize();
        if (recentAppList.size() <= maxSize) {
            return recentAppList;
        }
        return recentAppList.subList(recentAppList.size() - maxSize, recentAppList.size());
    }

    private static final String KEY_WEB_VIEW_INIT_ERROR = "mmp_webview_init_error";

    public static void recordWebViewError(Context context) {
        SharedPreferences sharedPreferences = MSCEnvHelper.getSharedPreferences(context.getApplicationContext(), SP_RECENT_RECORD);
        sharedPreferences.edit().putBoolean(KEY_WEB_VIEW_INIT_ERROR, true).apply();
    }

    public static boolean isWebViewErrorHappened(Context context) {
        return MSCEnvHelper.getSharedPreferences(context.getApplicationContext(), SP_RECENT_RECORD).getBoolean(KEY_WEB_VIEW_INIT_ERROR, false);
    }
}
