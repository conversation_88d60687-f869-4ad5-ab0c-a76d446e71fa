package com.meituan.msc.modules.engine;

import android.annotation.SuppressLint;
import android.content.Context;
import android.support.annotation.NonNull;
import android.support.annotation.Nullable;
import android.text.TextUtils;
import android.view.View;

import com.meituan.android.degrade.interfaces.resource.ExecuteResultCallback;
import com.meituan.android.degrade.interfaces.resource.PreloadBlock;
import com.meituan.android.degrade.interfaces.resource.ResourceManager;
import com.meituan.msc.common.config.MSCConfig;
import com.meituan.msc.common.utils.CollectionUtil;
import com.meituan.msc.common.utils.DisplayUtil;
import com.meituan.msc.common.utils.PathUtil;
import com.meituan.msc.common.utils.ToastUtils;
import com.meituan.msc.extern.MSCEnvHelper;
import com.meituan.msc.modules.devtools.DebugHelper;
import com.meituan.msc.modules.manager.MSCModule;
import com.meituan.msc.modules.manager.ModuleName;
import com.meituan.msc.modules.page.render.BaseRenderer;
import com.meituan.msc.modules.page.render.IRendererView;
import com.meituan.msc.modules.page.render.RendererFactory;
import com.meituan.msc.modules.page.render.RendererType;
import com.meituan.msc.modules.page.render.ReusableRenderer;
import com.meituan.msc.modules.page.render.webview.IWebView;
import com.meituan.msc.modules.page.render.webview.MSCWebView;
import com.meituan.msc.modules.page.render.webview.MSCWebViewRenderer;
import com.meituan.msc.modules.page.render.webview.OnEngineInitFailedListener;
import com.meituan.msc.modules.page.render.webview.WebViewCacheManager;
import com.meituan.msc.modules.page.render.webview.impl.MTWebViewImp;
import com.meituan.msc.modules.preload.MSCHornPreloadConfig;
import com.meituan.msc.modules.reporter.MSCLog;
import com.meituan.msc.modules.update.bean.PackageInfoWrapper;

import org.json.JSONObject;

import java.util.ArrayList;
import java.util.HashMap;
import java.util.Iterator;
import java.util.List;
import java.util.ListIterator;
import java.util.Map;
import java.util.concurrent.CopyOnWriteArrayList;

/**
 * 小程序渲染器的管理类，负责创建/预加载/复用渲染器
 * 对外返回{@link BaseRenderer}，内部会决定创建的Renderer的类型，仅在Renderer是{@link ReusableRenderer}时支持预加载/复用
 * <p>
 * Created by letty on 2019/11/16.
 **/

@ModuleName(name = "RendererManager")
public class RendererManager extends MSCModule implements IRendererManager {

    private final String TAG = "RendererManager@" + Integer.toHexString(hashCode());

    private static final int RN_RENDERER_POOL_SIZE = 1;
    private static final int NATIVE_RENDERER_POOL_SIZE = 1;
    private volatile boolean isBasicPackageDownloaded;   // 主包子包下载完成，此后允许开始预加载
    private OnEngineInitFailedListener onEngineInitFailedListener;
    private boolean launched;
    //已经释放所有缓存的render
    private volatile boolean isReleased;

    // 复用池，以队列方式组织，LRU淘汰
    private final List<ReusableRenderer> webviewPoolUnsafe = new CopyOnWriteArrayList<>();
    private final List<ReusableRenderer> webviewPoolSafe = new CopyOnWriteArrayList<>();
    private final List<BaseRenderer> mscRendererCache = new CopyOnWriteArrayList<>();
    private final List<BaseRenderer> rnRendererCache = new CopyOnWriteArrayList<>();

    private final boolean rollbackWebViewReuseFix = MSCHornRollbackConfig.readConfig().rollbackWebViewReuseFix;
    /**
     * 是否已经创建了webview3段的render
     */
    public volatile boolean isCreatedPreloadRender = false;

    // 改为事件
    public void setPackageReady(boolean isPackageDownloaded) {
        this.isBasicPackageDownloaded = isPackageDownloaded;
    }

    @Override
    public boolean isPackageReady() {
        return isBasicPackageDownloaded;
    }

    private int rendererPoolSize(RendererType rendererType) {
        return rendererType == RendererType.RN ? RN_RENDERER_POOL_SIZE : NATIVE_RENDERER_POOL_SIZE;
    }

    @Override
    public int cacheNativeRenderer(RendererType rendererType) {
        List<BaseRenderer> rendererCache = rendererType == RendererType.RN ? rnRendererCache : mscRendererCache;
        int result = View.NO_ID;
        if (rendererCache.size() < rendererPoolSize(rendererType)) {
            MSCLog.d("cache one rn renderer");
            //默认创建的是RN类型的,目前RN和Native公用一个Render,后续需要更新rendererType
            BaseRenderer renderer = RendererFactory.createRender(rendererType, MSCEnvHelper.getContext(), getRuntime());
            rendererCache.add(renderer);
            result = renderer.getViewId();
        } else {
            BaseRenderer renderer = rendererCache.get(0);
            if (renderer != null) {
                result = renderer.getViewId();
            }
        }
        return result;
    }

    @Override
    public BaseRenderer retainNativeRenderer(RendererType rendererType) {
        BaseRenderer renderer = null;
        List<BaseRenderer> rendererCache = rendererType == RendererType.RN ? rnRendererCache : mscRendererCache;
        if (!rendererCache.isEmpty()) {
            MSCLog.d("consume one rn renderer");
            renderer = rendererCache.remove(0);
            if (renderer != null) {
                renderer.setPreloadType(RendererPreloadType.PRE_CREATE);
            }
        }
        return renderer;
    }

    /**
     * 确定小程序id后才能调用此方法
     *
     * @param url
     * @return
     */
    public BaseRenderer retainRenderer(String url) {
        launched = true;

        MSCApp app = getAppOrThrow();
        MSCRuntime runtime = app.getRuntime();

        RendererType targetType = runtime.getMSCAppModule().getRendererTypeForPage(url);
        BaseRenderer renderer;
        if (targetType == RendererType.RN || targetType == RendererType.NATIVE) {
            //此时获取的是RN类型的renderer
            renderer = retainNativeRenderer(targetType);
        } else {
            renderer = getReusableRendererFromPool(url);
        }
        if (renderer != null) {
            if (targetType == renderer.getType()) {
                MSCLog.i(TAG, "reuse render", url, renderer);
                if (renderer instanceof MSCWebViewRenderer) {
                    ((MSCWebViewRenderer) renderer).markPageLaunchStarted();
                }
                return renderer;
            } else {
                MSCLog.i(TAG, "get renderer from pool but type ", renderer.getType()
                        , " not matching target type ", targetType, ", destroy: ", url);
                renderer.onDestroy();
            }
        }
        renderer = RendererFactory.createRender(targetType, MSCEnvHelper.getContext(), getRuntime());
        if (renderer instanceof MSCWebViewRenderer) {
            ((MSCWebViewRenderer) renderer).markPageLaunchStarted();
        }
        MSCLog.i(TAG, "retainRenderer by create", url, renderer);
        return renderer;
    }

    /**
     * 获取Renderer实例，如有将从池中移除，如没有将创建
     */
    private ReusableRenderer getReusableRendererFromPool(String url) {
        String path = PathUtil.getPath(url);
        final MSCApp app = getAppOrThrow();
        ReusableRenderer result = findRenderer(path);
        if (result != null) {
            if (!MSCHornRollbackConfig.readConfig().enableVerifyRendererValidAtFindRenderer) {
                // 回滚逻辑
                // webview那边的recycle指令未执行完成，不能复用。
                if (!result.isWebViewRecycleCmdFinished()) {
                    MSCLog.i(TAG, "#getReusableRendererFromPool, recycle not finished, can't reuse cache webview.", result);
                    return null;
                }
            }
            if (!checkIfReuseWebviewRenderer(app.getAppId(), result)) {
                MSCLog.i(TAG, "#getReusableRendererFromPool, can't reuse cache webview.", result);
                return null;
            }
            removeRenderFromPool(result);
            ToastUtils.toastIfDebug("从复用池取出Renderer，checkIfRecycled, result=" + result + ",path:" + result.getPagePath()
                    + ", 资源：" + CollectionUtil.toString(result.getResourcePaths())
                    + ", 剩余" + getRenderSize());
            logPoolContentIfDebug();

            if (result instanceof MSCWebViewRenderer
                    && result.isNeedReload()) {
                ToastUtils.toastIfDebug("复用池中的Renderer已经发生RenderProcessGone，无法复用");
                result = null;
            }
        } else {
            ToastUtils.toastIfDebug("复用池中未找到合适的Renderer，新建");
        }
        return result;
    }

    private int getRenderSize() {
        if (rollbackWebViewReuseFix) {
            return webviewPoolUnsafe.size();
        }
        synchronized (webviewPoolSafe) {
            return webviewPoolSafe.size();
        }
    }

    private void removeRenderFromPool(ReusableRenderer result) {
        if (rollbackWebViewReuseFix) {
            webviewPoolUnsafe.remove(result);
        } else {
            synchronized (webviewPoolSafe) {
                webviewPoolSafe.remove(result);
            }
        }
    }

    /**
     * 判断当前appid对应的小程序是否能复用这个renderer
     * @return
     */
    public boolean checkIfReuseWebviewRenderer(@NonNull String appId, @NonNull ReusableRenderer renderer) {
        MSCLog.i(TAG, "#checkValid, start.");
        IRendererView rendererView = renderer.getRendererView();
        boolean isMscWebView = rendererView instanceof MSCWebView;
        if (!isMscWebView) {
            // 非webview，可以复用。
            MSCLog.i(TAG, "#checkValid, rendererview is not a MSCWebView.");
            return true;
        }
        final WebViewCacheManager webViewCacheManager = WebViewCacheManager.getInstance();
        MSCWebView outWebView = (MSCWebView) renderer.getRendererView();
        IWebView innerWebView = outWebView.getIWebView();
        if (null == innerWebView) {
            // 和原逻辑保持一致，理论上不为空
            MSCLog.i(TAG, "#checkValid, inner webview is null. Unbelievable!");
            return true;
        }
        boolean useMt = webViewCacheManager.useMtWebViewByAppId(appId);
        boolean isMtWebView = innerWebView instanceof MTWebViewImp;
        MSCLog.i(TAG, "#checkValid, inner webview is not null.", useMt, isMtWebView);
        // 能使用自研内核的&&MTWebview
        // 不能使用自研内核的&&非MTWebView
        final boolean ret = (useMt && isMtWebView) || (!useMt && !isMtWebView);
        return ret;
    }

    /**
     * 获取现存的Renderer实例，不会创建
     */
    public ReusableRenderer findRenderer(String url) {
        //已预加载具体页面
        ReusableRenderer result = findFirstResourceLoadedRenderer(url, true, false, false);
        if (result != null) {
            MSCLog.i(TAG, "find Renderer that current page matched: ", url);
            result.setPreloadType(RendererPreloadType.PRELOAD_PAGE);
            return result;
        }
        //加载过具体页面
        result = findFirstResourceLoadedRenderer(url, false, true, false);
        if (result != null) {
            MSCLog.i(TAG, "find Renderer that loaded page matched: ", url);
            result.setPreloadType(RendererPreloadType.PRELOAD_PAGE);
            return result;
        }
        //加载过页面资源
        result = findFirstResourceLoadedRenderer(url, false, false, true);
        if (result != null) {
            // 加载了资源
            MSCLog.i(TAG, "find Renderer that loaded resource: ", url);
            result.setPreloadType(RendererPreloadType.PRELOAD_BUSINESS);
            return result;
        }
        // 未找到已加载过所需页面的Renderer，需要找到可复用的然后加载，因此需要一个resourceSpace
        result = findFirstReusableRenderer();
        if (result != null) {
            result.setPreloadType(RendererPreloadType.PRELOAD_BASE);
        }
        return result;
    }

    /**
     * 校验render加载过基础包
     * 除webview渲染，其他渲染模式默认返回true，不需要加载基础包
     * @param renderer
     * @return
     */
    private boolean verifyLoadedFrameworkPackage(ReusableRenderer renderer) {
        if (!MSCHornRollbackConfig.readConfig().rollbackOfflineFrameworkCheck) {
            return true;
        }
        if (!(renderer instanceof MSCWebViewRenderer)) {
            return true;
        }
        PackageInfoWrapper basePackage = getRuntime().getMSCAppModule().getBasePackage();
        boolean basePackageVersionMatched = ((MSCWebViewRenderer) renderer).isBasePackageVersionMatched(basePackage);
        if (!basePackageVersionMatched) {
            MSCLog.i(TAG, "verifyLoadedFrameworkPackage", "MSCWebViewRenderer@" + renderer.hashCode());
            releaseRenderer(renderer);
        }
        return basePackageVersionMatched;
    }

    private ReusableRenderer findFirstResourceLoadedRenderer(@NonNull String url, boolean currentPageMatch, boolean loadedPathMatch, boolean preloadedResourceMatch) {
        return findFirstMatchedRenderer(url, currentPageMatch, loadedPathMatch, preloadedResourceMatch, false);
    }

    private ReusableRenderer findFirstReusableRenderer() {
        return findFirstMatchedRenderer(null, false, false, false, true);
    }

    /**
     * 多功能匹配，按布尔型参数指定的条件返回匹配的第一个结果
     * 同时指定多个主要条件时返回满足任一一个条件的
     *  @param url                 用于currentPageMatch及loadedResourceMatch的判断
     * @param currentPageMatch    与Renderer当前的url匹配，处于预加载命中的状态
     * @param loadedPathMatch     与Renderer曾加载的url匹配，处于已从界面摘除准备复用状态
     * @param preloadedResourceMatch 与Renderer预加载资源的url匹配
     * @param needLoadOther       没有加载特定页面，且还有资源空位（因未经过前几个条件匹配特定资源，复用一定需要加载新的资源）
     */
    private ReusableRenderer findFirstMatchedRenderer(@Nullable String url,
                                                      boolean currentPageMatch,
                                                      boolean loadedPathMatch,
                                                      boolean preloadedResourceMatch,
                                                      boolean needLoadOther) {
        List<ReusableRenderer> result = findAllMatchedRenderer(true, url, currentPageMatch, loadedPathMatch, preloadedResourceMatch, needLoadOther);
        if (!result.isEmpty()) {
            return result.get(0);
        } else {
            return null;
        }
    }

    /**
     *
     * @param returnFirst               匹配上就返回
     * @param url                       用于currentPageMatch及loadedResourceMatch的判断
     * @param currentPageMatch          与Renderer当前的url匹配，处于预加载命中的状态
     * @param loadedPathMatch           与Renderer曾加载的url匹配，处于已从界面摘除准备复用状态
     * @param preloadedResourceMatch    与Renderer预加载资源的url匹配
     * @param needLoadOther             没有加载特定页面，且还有资源空位（因未经过前几个条件匹配特定资源，复用一定需要加载新的资源）
     *
     * @return
     */
    @NonNull
    private List<ReusableRenderer> findAllMatchedRenderer(boolean returnFirst,
                                                          @Nullable String url,
                                                          boolean currentPageMatch,
                                                          boolean loadedPathMatch,
                                                          boolean preloadedResourceMatch,
                                                          boolean needLoadOther) {
        if (rollbackWebViewReuseFix) {
            return realFindAllMatchedRenderer(returnFirst, url, currentPageMatch, loadedPathMatch, preloadedResourceMatch, needLoadOther, webviewPoolUnsafe);
        } else {
            List<ReusableRenderer> result;
            synchronized (webviewPoolSafe) {
                result = realFindAllMatchedRenderer(returnFirst, url, currentPageMatch, loadedPathMatch, preloadedResourceMatch, needLoadOther, webviewPoolSafe);
            }
            return result;
        }
    }

    private List<ReusableRenderer> realFindAllMatchedRenderer(boolean returnFirst,
                                                              @Nullable String url,
                                                              boolean currentPageMatch,
                                                              boolean loadedPathMatch,
                                                              boolean preloadedResourceMatch,
                                                              boolean needLoadOther,
                                                              List<ReusableRenderer> webviewPool) {
        List<ReusableRenderer> result = new ArrayList<>();
        if (webviewPool.isEmpty()) {
            return result;
        }

        String path = TextUtils.isEmpty(url) ? null : PathUtil.getPath(url);
        // 逆序遍历，优先使用最新进入复用池的，有助于在反复进出时保持池子稳定
        for (ListIterator<ReusableRenderer> it = webviewPool.listIterator(webviewPool.size()); it.hasPrevious(); ) {
            ReusableRenderer renderer = it.previous();
            boolean matched = false;
            if (path != null) {
                matched |= currentPageMatch && canPathMatch(renderer.getPagePath(), path);
                //使用过快照的render只能被首页复用
                if (renderer.isUsedSnapshotTemplate() && !TextUtils.equals(getRuntime().getMSCAppModule().getRootPath(), path)) {
                    continue;
                }
                if (!renderer.isPathLoaded()) {  // 如果已加载具体页面，处于不可更改目标状态，不考虑其包含的其他资源
                    matched |= loadedPathMatch && renderer.getLoadedPaths().contains(path);
                    matched |= preloadedResourceMatch && renderer.getResourcePaths().contains(path);
                }
            }
            matched |= needLoadOther && !renderer.isPathLoaded() && canLoadMore(renderer) && verifyLoadedFrameworkPackage(renderer)
                    && verifyRendererValid(renderer);
            if (matched) {
                result.add(renderer);
                if (returnFirst) {
                    return result;
                }
            }
        }
        return result;
    }

    private boolean verifyRendererValid(ReusableRenderer renderer) {
        if (MSCHornRollbackConfig.readConfig().enableVerifyRendererValidAtFindRenderer) {
            // webview那边的recycle指令未执行完成，不能复用。
            if (!renderer.isWebViewRecycleCmdFinished()) {
                MSCLog.i(TAG, "#getReusableRendererFromPool verifyRendererValid, recycle not finished, can't reuse cache webview.", renderer);
                return false;
            }
            if (renderer.getRendererView().asView().isAttachedToWindow()) {
                MSCLog.i(TAG, "#getReusableRendererFromPool verifyRendererValid, can't reuse attached webview.", renderer);
                return false;
            }
        }
        return true;
    }

    /**
     * 目标路径同render匹配
     *
     * @param rendererUrl 当前render的路径
     * @param newUrl      目标匹配路径
     * @return
     */
    private boolean canPathMatch(String rendererUrl, String newUrl) {
        if (rendererUrl == null) {
            return false;
        }
        String newPath = PathUtil.getPath(newUrl);
        String previousPath = PathUtil.getPath(rendererUrl);
        if (!TextUtils.equals(newPath, previousPath)) {
            return false;
        }
        return true;
    }

    /**
     * 一个Renderer中最多允许加载一定数量的页面资源，允许一次加载多个资源时超过限制，此后不能再加载未加载过的页面，等待被淘汰
     */
    private boolean canLoadMore(@NonNull ReusableRenderer renderer) {
        return renderer.getResourcePaths().size() < MSCConfig.getWebViewResourceLimit();
    }

    private ReusableRenderer createPreloadRenderer(Context context) {
        ReusableRenderer renderer = RendererFactory.createRender(RendererType.WEBVIEW, context, getRuntime());
        if (!launched) {
            // 用于在预加载阶段遇到错误时销毁引擎，如已启动则应走其他流程，不能仅销毁引擎了
            renderer.setOnEngineInitFailedListener(onEngineInitFailedListener);
        }
        addRendererToPool(renderer);
        return renderer;
    }

    /**
     * @return 是否发生预加载
     */
    //TODO 在目标页面要求的渲染器为不可复用类型时，不预加载，另有其余几处预加载需要同样处理
    @Override
    public boolean preloadPage(Context context, String path, boolean isLaunch) {
        ReusableRenderer result = findRenderer(path);
        MSCLog.i(TAG, "preload App Page in ContainerController OnCreate", result);
        if (result == null) {
            isCreatedPreloadRender = true;
            createPreloadRenderer(context).preloadPage(path, isLaunch);
            return true;
        } else {
            if (!result.isPathLoaded()) { // 有可能取出的是空白模版
                result.preloadPage(path, isLaunch);
                return true;
            } else {
                return false;
            }
        }
    }

    /**
     * 页面firstRender之后缓存一个WebView，以加快下一个页面启动
     */
    @Override
    public void cacheRendererForNextPage(final Context context, String currPageUrl) {
        if (isReleased) {
            return;
        }
        MSCLog.i(TAG, "cacheRendererForNextPage, curr: ", currPageUrl);
        preloadResources(context, getWebViewPreloadPages(currPageUrl), null, false);
    }

    @Override
    public void preloadWebViewBasePackage(Context context, PackageInfoWrapper packageInfo, final ResultCallback resultCallback) {
        MSCLog.i("webviewInjectBase", "preloadBasePackage step5 start");
        if (isReleased) {
            MSCLog.i("webviewInjectBase", "preloadBasePackage step5 released exit");
            if (resultCallback != null) {
                resultCallback.onReceiveFailValue(null);
            }
            return;
        }
        ReusableRenderer webViewRender = createPreloadRenderer(context);
        webViewRender.setHasTriggerFourthSegmentPreloadWebView(true);
        isCreatedPreloadRender = true;
        if (webViewRender.isPathLoaded()) {
            MSCLog.i("webviewInjectBase", "preloadBasePackage step5 is path loaded exit");
            return;
        }
        webViewRender.setOnEngineInitFailedListener(new OnEngineInitFailedListener() {
            @Override
            public void onEngineInitFailed(Exception e) {
                if (resultCallback != null) {
                    resultCallback.onReceiveFailValue(e);
                }
                releaseRenderer(webViewRender);
            }
        });

        webViewRender.loadWebViewBasePackage(new ResultCallback() {
            @Override
            public void onReceiveFailValue(Exception ex) {
                if (resultCallback != null) {
                    resultCallback.onReceiveFailValue(ex);
                }
                releaseRenderer(webViewRender);
            }

            @Override
            public void onReceiveValue(String value) {
                if (resultCallback != null) {
                    resultCallback.onReceiveValue(value);
                }
            }
        });
    }

    @Override
    public void preloadDefaultResources(Context context, final ResultCallback resultCallback, boolean isLaunch) {
        MSCLog.i(TAG, "preloadDefaultResources");
        preloadResources(context, getWebViewPreloadPages("/"), resultCallback, isLaunch);
    }

    @Override
    public void preloadBizPagesResouces(Context context, List<String> pagePaths, ResultCallback resultCallback, boolean isLaunch) {
        MSCLog.i(TAG, "preloadPagesResouces");
        preloadResources(context, pagePaths, resultCallback, isLaunch);
    }

    /**
     * 预热基础包+主包，得到未确定页面的空白WebView，并可选预加载页面资源
     */
    private void preloadResources(Context context, @Nullable List<String> preloadResourcePaths,
                                  final ResultCallback resultCallback, boolean isLaunch) {
        MSCLog.i(TAG, "preloadResources: ", CollectionUtil.toString(preloadResourcePaths));
        if (isReleased) {
            return;
        }
        if (!MSCHornPreloadConfig.get().enableBlankPagePreload()) {
            MSCLog.i(TAG, "page resource preload disabled by config");
            return;
        }

        List<String> needLoadPaths = new ArrayList<>();
        if (preloadResourcePaths != null) {
            needLoadPaths.addAll(preloadResourcePaths);
        }
        // 目标为保证池中有页面符合本次加载所要求的结果，如有则不需要再预加载
        Map<ReusableRenderer, Integer> matchedMap = new HashMap<>(); //记录命中多少次
        for (Iterator<String> it = needLoadPaths.iterator(); it.hasNext(); ) {
            String path = it.next();
            //复用池中已存在，移除
            List<ReusableRenderer> matchedPages = findAllMatchedRenderer(false, path, true, true, true, false);
            if (!matchedPages.isEmpty()) {
                it.remove();    //移除，本资源无需再加载
                for (ReusableRenderer page : matchedPages) {
                    Integer count = matchedMap.get(page);
                    matchedMap.put(page, count == null ? 1 : count + 1);
                }
            }
        }
        if (needLoadPaths.isEmpty()) {
            String msg = "";
            if (CollectionUtil.isEmpty(preloadResourcePaths)) {
                msg = "resource not requested, no need to preload resource";
            } else {
                msg = "Renderer in pool have all requested resources, no need to preload resource";
            }
            MSCLog.i(TAG, msg);
            if (resultCallback != null) {
                resultCallback.onReceiveValue(msg);
            }
        } else {
            if (MSCHornPreloadConfig.enableControlPreloadWebViewPage()) {
                ResourceManager.getInstance().submitPreloadBlock(new PreloadBlock() {
                    @Override
                    public String getBusinessName() {
                        return "MSC";
                    }

                    @Override
                    public String getPreloadType() {
                        return "preloadWebViewPageDeep";
                    }

                    @Override
                    public String getBusinessId() {
                        return getRuntime().getAppId();
                    }

                    @Override
                    public void onExecute() {
                        MSCLog.i(TAG, "doDeepPreloadWebView by degradeFramework");
                        doPreloadWebViewPageDeep(needLoadPaths, matchedMap, context, resultCallback, isLaunch);
                    }
                }, new ExecuteResultCallback() {
                    @Override
                    public void onExecuteAllow() {

                    }

                    @Override
                    public void onExecuteDenied(String deniedReason, JSONObject adopt) {
                        MSCLog.i(TAG, "doDeepPreloadWebView is rejected by degradeFramework, reason:" + deniedReason);
                    }
                });
            } else {
                MSCLog.i(TAG, "doDeepPreloadWebView by normal");
                doPreloadWebViewPageDeep(needLoadPaths, matchedMap, context, resultCallback, isLaunch);
            }
        }

        ReusableRenderer renderer = findFirstReusableRenderer();
        if (MSCHornPreloadConfig.enableControlPreloadWebViewBlankPage()) {
            ResourceManager.getInstance().submitPreloadBlock(new PreloadBlock() {
                @Override
                public String getBusinessName() {
                    return "MSC";
                }

                @Override
                public String getPreloadType() {
                    return "preloadWebViewBlankPage";
                }

                @Override
                public String getBusinessId() {
                    return getRuntime().getAppId();
                }

                @Override
                public void onExecute() {
                    MSCLog.i(TAG, "doPreloadWebViewBlankPage by degradeFramework, appId:" + getRuntime().getAppId());
                    doPreloadWebViewBlankPage(renderer, context, isLaunch);
                }

            }, new ExecuteResultCallback() {
                @Override
                public void onExecuteAllow() {

                }

                @Override
                public void onExecuteDenied(String deniedReason, JSONObject adopt) {
                    MSCLog.i(TAG, "doPreloadWebViewBlankPage is rejected by degradeFramework, appId:"+ getRuntime().getAppId() + ",reason:" + deniedReason);
                }
            });
        } else {
            MSCLog.i(TAG, "doPreloadWebViewBlankPage by normal, appId:" + getRuntime().getAppId());
            doPreloadWebViewBlankPage(renderer, context, isLaunch);
        }
    }

    private void doPreloadWebViewPageDeep(List<String> needLoadPaths, Map<ReusableRenderer, Integer> matchedMap,
                                          Context context, final ResultCallback resultCallback, boolean isLaunch) {
        // 需要加载资源
        MSCLog.i(TAG, "need preload resource: ", CollectionUtil.toString(needLoadPaths));

        // 资源优先加载至已有资源命中最多的Renderer，保持相关性
        int maxMatched = 0;
        ReusableRenderer mostMatchedRenderer = null;
        for (Map.Entry<ReusableRenderer, Integer> entry : matchedMap.entrySet()) {
            ReusableRenderer matchedRenderer = entry.getKey();
            if (matchedRenderer.isPathLoaded() || !canLoadMore(matchedRenderer)) {
                continue;
            }
            if (entry.getValue() > maxMatched) {
                maxMatched = entry.getValue();
                mostMatchedRenderer = matchedRenderer;
            }
        }

        ReusableRenderer renderer = mostMatchedRenderer;
        if (renderer == null) {
            renderer = findFirstReusableRenderer();
        }
        if (renderer == null) {
            renderer = createPreloadRenderer(context);
        } else {
            //移至池子中最新的位置
            updateRendererIndex(renderer);
            MSCLog.i(TAG, "preload resource to Renderer that have: ", CollectionUtil.toString(renderer.getResourcePaths()));
        }
        if (!isBasicPackageDownloaded) {
            // 包未下载完时无法加载具体包，只能走到创建WebView这一步，且预先触发WebView内核初始化仍有助于启动速度
            MSCLog.i(TAG, "Cancel_Preload_Resource_When_Basic_Package_Not_Downloaded", CollectionUtil.toString(renderer.getResourcePaths()));
            return;
        }
        renderer.setHasTriggerDeepPreloadWebView(true);
        if (!needLoadPaths.isEmpty()) {
            MSCLog.i(TAG, "preload resource to Renderer: ", CollectionUtil.toString(needLoadPaths));
            renderer.preloadResource(needLoadPaths);
            logPoolContentIfDebug();
        }
        ReusableRenderer finalRenderer = renderer;
        //加载基础包、主包、模版
        renderer.loadBasicPackages(new ResultCallback() {
            @Override
            public void onReceiveFailValue(Exception ex) {
                if (resultCallback != null) {
                    resultCallback.onReceiveFailValue(ex);
                }
                releaseRenderer(finalRenderer);
            }

            @Override
            public void onReceiveValue(String value) {
                if (resultCallback != null) {
                    resultCallback.onReceiveValue(value);
                }
            }
        }, isLaunch);
    }

    private void doPreloadWebViewBlankPage(ReusableRenderer renderer, Context context, boolean isLaunch) {
        if (renderer == null) {
            // 防范预加载资源导致整个池内所有Renderer资源数全满，导致下一个页面没命中时需要重新创建，此时预先创建一个未加载资源的Renderer
            MSCLog.i(TAG, "no Renderer in pool have resource space, create one");
            ReusableRenderer preCreateRenderer = createPreloadRenderer(context);
            preCreateRenderer.setHasTriggerPreloadBlankWebView(true);
            preCreateRenderer.loadBasicPackages(new ResultCallback() {
                @Override
                public void onReceiveFailValue(Exception e) {
                }

                @Override
                public void onReceiveValue(String value) {
                    // 只有新创建Renderer时需要提前注入bundle，复用池取出的renderer无需再次执行onResourcePreload
                    if (preCreateRenderer instanceof MSCWebViewRenderer) {
                        ((MSCWebViewRenderer) preCreateRenderer).preloadWebViewBundle();
                    }
                }
            }, isLaunch);
        } else {
            if (!MSCHornRollbackConfig.get().getConfig().isRollbackPreloadResource) {
                if (renderer instanceof MSCWebViewRenderer) {
                    MSCWebViewRenderer mscWebViewRenderer = (MSCWebViewRenderer) renderer;
                    // 三段式预热，此处会出现业务包未加载问题，在业务预热时补上
                    if (!mscWebViewRenderer.isMainPackageLoaded()) {
                        MSCLog.i(TAG, "find Renderer in poll,load basePackage and main Package");
                        //加载基础包、主包、模版
                        ReusableRenderer finalRenderer = renderer;
                        finalRenderer.setHasTriggerPreloadBlankWebView(true);
                        renderer.loadBasicPackages(new ResultCallback() {
                            @Override
                            public void onReceiveFailValue(Exception ex) {
                                releaseRenderer(finalRenderer);
                            }

                            @Override
                            public void onReceiveValue(String value) {
                            }
                        }, isLaunch);
                    } else {
                        MSCLog.i(TAG, "find Renderer in poll,already load main package");
                    }
                }
            }
        }
    }

    private void updateRendererIndex(ReusableRenderer renderer) {
        if (rollbackWebViewReuseFix) {
            webviewPoolUnsafe.remove(renderer);
            webviewPoolUnsafe.add(renderer);
        } else {
            synchronized (webviewPoolSafe) {
                webviewPoolSafe.remove(renderer);
                webviewPoolSafe.add(renderer);
            }
        }
    }

    @Override
    public void preloadHomePage(Context context, boolean isLaunch) {
        if (isReleased) {
            return;
        }
        String rootPath = getRuntime().getMSCAppModule().getRootPath();
        ReusableRenderer renderer = findRenderer(rootPath);
        if (renderer == null) {
            renderer = createPreloadRenderer(context);
        }
        if (!isBasicPackageDownloaded) {
            return;
        }
        if (!renderer.isPathLoaded()) {
            renderer.preloadPage(rootPath, isLaunch);
        }
    }

    public void recycleOrDestroyRenderer(BaseRenderer renderer) {
        String pagePath = PathUtil.getPath(renderer.getPagePath());

        if (canRecycle(renderer) && ((ReusableRenderer) renderer).recycle()) {
            renderer.setContainerDelegate(null);
            addRendererToPool((MSCWebViewRenderer) renderer);
            ToastUtils.toastIfDebug("Renderer进入复用池：" + getRenderSize() + "个, " + pagePath);

            logPoolContentIfDebug();
        } else {
            ToastUtils.toastIfDebug("Renderer无法复用，销毁：" + pagePath);
            renderer.onDestroy();
        }
    }

    private boolean canRecycle(BaseRenderer renderer) {
        if (!(renderer instanceof ReusableRenderer)) {
            return false;
        }
        ReusableRenderer webViewRenderer = (ReusableRenderer) renderer;
        if (isReleased) {
            MSCLog.i(TAG, "app released, destroy webView");
            return false;
        }
        if (getApp() != null && MSCConfig.disableWebViewRecycle(getApp().getAppId())) {
            MSCLog.i(TAG, getApp().getAppId(), "webView recycle not enabled");
            return false;
        }
        if (!getRuntime().getMSCAppModule().isWebViewRecycleEnabled()) {
            MSCLog.i(TAG, "webView recycle not enabled");
            return false;
        }
        if (webViewRenderer instanceof MSCWebViewRenderer
                && webViewRenderer.isNeedReload()) {
            MSCLog.i(TAG, "webView render process gone, should destroy");
            return false;
        }
        return true;
    }

    private void addRendererToPool(ReusableRenderer renderer) {
        String runtimeHashCode = getRuntime() == null ? "Runtime Empty" : "Runtime@" + Integer.toHexString(getRuntime().hashCode());
        MSCLog.i(TAG, "addRendererToPool", renderer, runtimeHashCode);
        if (rollbackWebViewReuseFix) {
            webviewPoolUnsafe.add(renderer);
        } else {
            synchronized (webviewPoolSafe) {
                webviewPoolSafe.add(renderer);
            }
        }

        logPoolContentIfDebug();
        if (getRenderSize() > MSCConfig.getWebViewPoolSize()) {
            ReusableRenderer expiredRenderer = null;
            if (rollbackWebViewReuseFix) {
                expiredRenderer = webviewPoolUnsafe.remove(0);
            } else {
                synchronized (webviewPoolSafe) {
                    expiredRenderer = webviewPoolSafe.remove(0);
                }
            }
            ToastUtils.toastIfDebug("复用池满，" + getRenderSize() + "个，销毁最老的Renderer");
            MSCLog.i(TAG, "addRendererToPool remove expired render", expiredRenderer, runtimeHashCode);
            expiredRenderer.onDestroy();
        }
    }

    public void release() {
        isReleased = true;
        clearAllCachedRenderer();
    }

    public void clearAllCachedRenderer() {
        MSCLog.i(TAG, "clearAllCachedRenderer");
        if (rollbackWebViewReuseFix) {
            realClearAllCachedRenderer(webviewPoolUnsafe);
        } else {
            synchronized (webviewPoolSafe) {
                realClearAllCachedRenderer(webviewPoolSafe);
            }
        }
    }

    public void realClearAllCachedRenderer(List<ReusableRenderer> webviewPool) {
        for (ReusableRenderer renderer : webviewPool) {
            MSCLog.i(TAG, "clearAllCachedRenderer onDestroy", renderer);
            renderer.onDestroy();
        }
        webviewPool.clear();
    }

    public void releaseRenderer(BaseRenderer renderer) {
        //noinspection SuspiciousMethodCalls
        MSCLog.i(TAG, "releaseRenderer", renderer);
        if (rollbackWebViewReuseFix) {
            webviewPoolUnsafe.remove(renderer);
        } else {
            synchronized (webviewPoolSafe) {
                webviewPoolSafe.remove(renderer);
            }
        }
        renderer.onDestroy();
    }

    private void logPoolContentIfDebug() {
        if (DebugHelper.isDebug()) {
            // 打印整个池子的内容
            String log = "pool: \n";
            List<ReusableRenderer> webviewPool = getWebViewPool();
            if (webviewPool.isEmpty()) {
                log += "empty";
            } else {
                for (ReusableRenderer poolPage : webviewPool) {
                    if (poolPage.getPagePath() != null) {
                        log += "current: " + poolPage.getPagePath() + ", ";
                    }
                    log += poolPage.getResourcePaths().size() + " resources, ";
                    log += CollectionUtil.toString(poolPage.getResourcePaths());
                    log += "\n";
                }
            }
            MSCLog.v(TAG, log);
        }
    }

    public RendererManager setOnEngineInitFailedListener(OnEngineInitFailedListener onEngineInitFailedListener) {
        this.onEngineInitFailedListener = onEngineInitFailedListener;
        return this;
    }

    private boolean releaseWebViewRendererWithSpecificWebView(ReusableRenderer renderer, View view) {
        if (!(renderer instanceof MSCWebViewRenderer)) {
            return false;
        }
        IWebView iWebView = ((MSCWebViewRenderer) renderer).getIWebView();
        if (WebViewCacheManager.releaseIWebViewWithSpecificWebView(iWebView, view)) {
            renderer.onDestroy();
            MSCLog.i(null, "releaseRendererIfWebViewCrashed renderer:", renderer, ", view: ", view);
            return true;
        }
        return false;
    }

    // CopyOnWriteArrayList 内部的Iterator实现不支持remove
    @SuppressLint("Iterator")
    public void releaseWebView(View view) {
        if (rollbackWebViewReuseFix) {
            realReleaseWebView(view, webviewPoolUnsafe);
        }else{
            synchronized (webviewPoolSafe) {
                realReleaseWebView(view, webviewPoolSafe);
            }
        }
    }

    private void realReleaseWebView(View view, List<ReusableRenderer> webviewPool) {
        for (ReusableRenderer next : webviewPool) {
            if (releaseWebViewRendererWithSpecificWebView(next, view)) {
                MSCLog.i(TAG, "releaseWebView releaseWebViewRendererWithSpecificWebView", next);
                webviewPool.remove(next);
            }
        }
    }


    @Nullable
    public List<String> getWebViewPreloadPages(String path) {
        List<String> list = getRuntime().getMSCAppModule().getPreloadPagesForThisPage(path);
        if (list == null) {
            return null;
        }
        Iterator<String> iterator = list.iterator();
        while (iterator.hasNext()) {
            if (!DisplayUtil.isWebViewRender(getRuntime(), iterator.next())) {
                iterator.remove();
            }
        }
        return list;
    }

    public List<ReusableRenderer> getWebViewPool() {
        if (rollbackWebViewReuseFix) {
            return new ArrayList<>(webviewPoolUnsafe);
        } else {
            synchronized (webviewPoolSafe) {
                return new ArrayList<>(webviewPoolSafe);
            }
        }
    }

    @Override
    public void onDestroy() {
        release();
    }
}
