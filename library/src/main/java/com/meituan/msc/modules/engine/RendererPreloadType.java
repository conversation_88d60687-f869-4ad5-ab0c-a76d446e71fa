package com.meituan.msc.modules.engine;

public enum RendererPreloadType {
    NONE,
    PRE_CREATE,
    PRELOAD_BASE,
    PRELOAD_BUSINESS,
    PRELOAD_PAGE;

    public static String toReportString(RendererPreloadType preloadType) {
        if (preloadType == null) {
            return "none";
        }
        switch (preloadType) {
            case PRE_CREATE:
                return "preCreate";
            case PRELOAD_BASE:
                return "preloadBase";
            case PRELOAD_BUSINESS:
                return "preloadBusiness";
            case PRELOAD_PAGE:
                return "preloadPage";
            default:
                return "none";
        }
    }
}