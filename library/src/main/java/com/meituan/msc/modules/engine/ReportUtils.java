package com.meituan.msc.modules.engine;

import com.meituan.msc.modules.page.IPageModule;
import com.meituan.msc.modules.update.bean.AppMetaInfoWrapper;
import com.meituan.msc.modules.update.bean.PackageInfoWrapper;

import java.util.Map;

/**
 * Created by letty on 2019/11/29.
 **/

public class ReportUtils {
    // 小程序包类型（构建的小程序包是测试包还是线上包）

    public final static String FIELD_BIZ_PKG_ENV = "bizPkgEnv";
    // 小程序构建ID
    public final static String FIELD_BUILD_ID = "buildId";
    // 小程序基础库包版本号
    public final static String FIELD_BASE_PKG_VERSION = "basePkgVersion";
    // 渲染类型(WebView渲染还是Native渲染)
    public final static String FIELD_RENDERER_TYPE = "rendererType";
    public static final String TAG_READ_BASE_PKG_SERVICE_FILE_DURATION = "readBasePkgServiceFileDuration";
    public static final String TAG_READ_MAIN_BIZ_PKG_SERVICE_FILE_DURATION = "readMainBizPkgServiceFileDuration";
    public static final String TAG_READ_SUB_BIZ_PKG_SERVICE_FILE_DURATION = "readSubBizPkgServiceFileDuration";
    public static final String TAG_READ_BASE_PKG_SERVICE_FILE_SIZE = "readBasePkgServiceFileSize";
    public static final String TAG_READ_MAIN_BIZ_PKG_SERVICE_FILE_SIZE = "readMainBizPkgServiceFileSize";
    public static final String TAG_READ_SUB_BIZ_PKG_SERVICE_FILE_SIZE = "readSubBizPkgServiceFileSize";
    public static final String TAG_SERVICE_INJECT_SUB_BIZ_PKG_NAME = "serviceInjectSubBizPkgName";
    public static final String TAG_PREFETCH_RESPONSE_SIZE = "prefetchResponseSize";
    private static final String TAG_DYNAMIC_PREFETCH_RESPONSE_SIZE = "dynamicPrefetchResponseSize";

    /**
     * 给MSI ContainerInfo 注入业务MetaInfo相关信息
     * 时机：业务包元信息获取成功回调时
     */
    public static void addBizMetaInfoToMSIContainer(MSCRuntime runtime, AppMetaInfoWrapper metaInfoWrapper) {
        if (runtime == null || metaInfoWrapper == null || runtime.apisManager == null) {
            return;
        }
        runtime.apisManager.addContainerConfig(FIELD_BIZ_PKG_ENV, metaInfoWrapper.isDebug() ? "develop" : "release");
        runtime.apisManager.addContainerConfig(FIELD_BUILD_ID, metaInfoWrapper.getBuildId());
    }

    /**
     * 给MSI ContainerInfo 注入基础库包相关信息
     * 时机：基础库获取成功时
     *
     * @param runtime
     * @param basePkgInfo
     */
    public static void addBasePkgInfoToMSIContainer(MSCRuntime runtime, PackageInfoWrapper basePkgInfo) {
        if (runtime == null || basePkgInfo == null || runtime.apisManager == null) {
            return;
        }
        runtime.apisManager.addContainerConfig(FIELD_BASE_PKG_VERSION, basePkgInfo.getVersion());
    }

    /**
     * 给MSI ContainerInfo 注入业务包相关信息
     * 时机：Page onShow，renderType本身在业务包的app-config.json配置里，但属于Page级的配置，所以此处含义为栈顶Page的rendererType
     * @param runtime
     */
    public static void addBizPkgInfoToMSIContainer(MSCRuntime runtime) {
        if (runtime == null || runtime.apisManager == null) {
            return;
        }
        IPageModule pageModule = runtime.getTopPageModule();
        if (pageModule == null) {
            return;
        }
        runtime.apisManager.addContainerConfig(FIELD_RENDERER_TYPE, pageModule.getRendererType().toString());
    }

    /**
     * 给秒开(ffp_msc)、秒开分阶段(msc.ffp.stages)指标添加包注入相关信息
     * 时机：收到秒开回调时
     */
    public static void addPkgSizeTagsForFFP(MSCRuntime runtime, Map<String, Object> tags) {
        if (runtime == null) {
            return;
        }
        // 逻辑层读取包文件耗时、大小
        tags.put(TAG_READ_BASE_PKG_SERVICE_FILE_DURATION, runtime.getRuntimeReporter().getReadBasePkgServiceFileDuration());
        tags.put(TAG_READ_MAIN_BIZ_PKG_SERVICE_FILE_DURATION, runtime.getRuntimeReporter().getReadMainBizPkgServiceFileDuration());
        tags.put(TAG_READ_SUB_BIZ_PKG_SERVICE_FILE_DURATION, runtime.getRuntimeReporter().getReadSubBizPkgServiceFileDuration());
        tags.put(TAG_READ_BASE_PKG_SERVICE_FILE_SIZE, runtime.getRuntimeReporter().getReadBasePkgServiceFileSize());
        tags.put(TAG_READ_MAIN_BIZ_PKG_SERVICE_FILE_SIZE, runtime.getRuntimeReporter().getReadMainBizPkgServiceFileSize());
        tags.put(TAG_READ_SUB_BIZ_PKG_SERVICE_FILE_SIZE, runtime.getRuntimeReporter().getReadSubBizPkgServiceFileSize());
        tags.put(TAG_SERVICE_INJECT_SUB_BIZ_PKG_NAME, runtime.getRuntimeReporter().getServiceInjectSubBizPkgName());
        // 旧版数据预拉取响应体大小
        tags.put(TAG_PREFETCH_RESPONSE_SIZE, runtime.getRuntimeReporter().getPrefetchResponseSize());
    }

}
