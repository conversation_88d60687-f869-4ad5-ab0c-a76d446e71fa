package com.meituan.msc.modules.engine;

public enum RuntimeDestroyReason {
    NO_FIRST_RENDER,
    DISALLOW_ENTER_KEEP_ALIVE,
    RETRY_WHEN_LOAD_ERROR,
    CLOSE_WHEN_LOAD_ERROR,
    BASE_PACKAGE_RESOURCE_INVALID,
    BASE_<PERSON><PERSON>KAGE_NEED_RELOAD,
    BASE_<PERSON>CKAGE_ON_RELOAD,
    BASE_PACKAGE_FAILED,
    BIZ_PA<PERSON>KAGE_FAILED,
    NOT_USABLE,
    PAGE_NOT_FOUND,
    EXCEED_KEEP_ALIVE_LIMIT,
    KEEP_ALIVE_WEIGHT_NOT_ENOUGH,
    ON_LOW_MEMORY,
    BUN<PERSON><PERSON>_OFFLINE,
    KEEP_ALIVE_COUNT_EXCEED,
    KEEP_ALIVE_TIME_EXCEED,
    C<PERSON>AN_PRELOAD_APP,
    // 主动调用
    ACTIVE_CALL,
    // debug env
    RELOAD,
    DEBUG_DESTROY_CACHE_RUNTIME,
    LOGIN_STATUS_CHANGE,
    MMP_ROUTER_ROLL<PERSON>CK,
    DISABLE_REUSE_ANY,
    UNKNOWN;

    public static String toString(RuntimeDestroyReason reason) {
        if (reason == null) {
            return "unknown";
        }
        switch (reason) {
            case NO_FIRST_RENDER:
                return "noFirstRender";
            case DISALLOW_ENTER_KEEP_ALIVE:
                return "disallowEnterKeepAlive";
            case RETRY_WHEN_LOAD_ERROR:
                return "retryWhenLoadError";
            case BASE_PACKAGE_RESOURCE_INVALID:
                return "basePackageResourceInvalid";
            case BASE_PACKAGE_NEED_RELOAD: // 基础包下线
                return "basePackageNeedReload";
            case BASE_PACKAGE_ON_RELOAD: // 强制更新
                return "basePackageOnReload";
            case BASE_PACKAGE_FAILED:
                return "basePreloadFailed";
            case BIZ_PACKAGE_FAILED:
                return "bizPackageFailed";
            case NOT_USABLE:
                return "notUsable";
            case PAGE_NOT_FOUND:
                return "pageNotFound";
            case CLOSE_WHEN_LOAD_ERROR:
                return "closeWhenLoadError";
            case EXCEED_KEEP_ALIVE_LIMIT:
                return "exceedKeepAliveLimit";
            case KEEP_ALIVE_WEIGHT_NOT_ENOUGH:
                return "keepAliveWeightNotEnough";
            case KEEP_ALIVE_COUNT_EXCEED:
                return "keepAliveMaxCountExceed";
            case BUNDLE_OFFLINE: // 业务下线
                return "bundleOffline";
            case ON_LOW_MEMORY:
                return "onLowMemory";
            case KEEP_ALIVE_TIME_EXCEED:
                return "keepAliveTimeExceed";
            case CLEAN_PRELOAD_APP:
                return "cleanPreloadApp";
            case ACTIVE_CALL:
                return "activeCall";
            case RELOAD:
                return "reload";
            case DEBUG_DESTROY_CACHE_RUNTIME:
                return "DEBUG_DESTROY_CACHE_RUNTIME";
            case LOGIN_STATUS_CHANGE:
                return "loginStatusChange";
            case MMP_ROUTER_ROLLBACK:
                return "mmpRouterRollback";
            case DISABLE_REUSE_ANY:
                return "disableReuseAny";
            default:
                return "unknown";
        }
    }

    public static String getKeepAliveMissReason(String reason) {
        if (reason == null) {
            return "keepAliveDestroyUnknown";
        }
        switch (reason) {
            case "keepAliveTimeExceed":
                return "keepAliveTimeExceed";
            case "loginStatusChange":
                return "keepAliveLoginStatusChange";
            case "exceedKeepAliveLimit":
                return "keepAliveCountExceed";
            case "keepAliveWeightNotEnough":
                return "keepAliveWeightNotEnough";
            case "onLowMemory":
                return "keepAliveOnLowMemory";
            case "basePackageNeedReload":
                return "keepAliveBasePackageOffline";
            case "bundleOffline":
                return "keepAliveBizPackageOffline";
            case "noFirstRender":
                return "keepAliveNoFirstRender";
            case "mmpRouterRollback":
                return "keepAliveMmpRouterRollback";
            case "basePackageOnReload":
                return "keepAliveBasePackageReload";
            case "disableReuseAny":
                return "keepAliveApplyUpdate";
            default:
                return "keepAliveDestroyUnknown";
        }
    }

    public static String getPreloadBaseMissReasonWhenDestroy(String reason) {
        if (reason == null) {
            return "basePreloadDestroyUnknown";
        }
        switch (reason) {
            case "basePackageNeedReload":
                return "basePreloadDestroyPackageOffline";
            case "basePackageOnReload":
                return "basePreloadDestroyPackageReload";
            default:
                return "basePreloadDestroyUnknown";
        }
    }

    public static String getPreloadBizMissReasonWhenDestroy(String reason) {
        if (reason == null) {
            return "bizPreloadDestroyUnknown";
        }
        switch (reason) {
            case "basePackageNeedReload":
                return "bizPreloadDestroyBasePackageOffline";
            case "bundleOffline":
                return "bizPreloadDestroyBizPackageOffline";
            case "cleanPreloadApp":
                return "bizPreloadDestroyMaxSizeLru";
            case "basePackageOnReload":
                return "bizPreloadDestroyBasePackageReload";
            case "loginStatusChange":
                return "bizPreloadDestroyLoginStatusChange";
            case "mmpRouterRollback":
                return "bizPreloadDestroyMmpRouterRollback";
            default:
                return "bizPreloadDestroyUnknown";
        }
    }
}
