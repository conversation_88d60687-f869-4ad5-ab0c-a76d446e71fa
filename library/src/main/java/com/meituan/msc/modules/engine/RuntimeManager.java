package com.meituan.msc.modules.engine;

import static com.meituan.msc.modules.update.MSCAppModule.OFFLINE_BASE_FAILED_REASON_NO_BASE_PACKAGE_INFO;

import android.support.annotation.NonNull;
import android.support.annotation.Nullable;
import android.support.annotation.VisibleForTesting;
import android.text.TextUtils;
import android.util.LruCache;
import android.util.Pair;

import com.meituan.met.mercury.load.bean.MSCAppIdPublishId;
import com.meituan.met.mercury.load.core.DDResource;
import com.meituan.msc.common.config.MSCConfig;
import com.meituan.msc.common.executor.MSCExecutors;
import com.meituan.msc.common.framework.Callback;
import com.meituan.msc.common.process.GlobalEngineMonitor;
import com.meituan.msc.common.process.MSCProcess;
import com.meituan.msc.common.support.java.util.Objects;
import com.meituan.msc.common.support.java.util.concurrent.CompletableFuture;
import com.meituan.msc.common.support.java.util.function.BiFunction;
import com.meituan.msc.common.support.java.util.function.Consumer;
import com.meituan.msc.common.support.java.util.function.Predicate;
import com.meituan.msc.common.utils.Constants;
import com.meituan.msc.common.utils.MPConcurrentHashMap;
import com.meituan.msc.common.utils.ToastUtils;
import com.meituan.msc.extern.MSCEnvHelper;
import com.meituan.msc.modules.apploader.IAppLoader;
import com.meituan.msc.modules.apploader.IEngineStatusChangeListener;
import com.meituan.msc.modules.container.ContainerDebugLaunchData;
import com.meituan.msc.modules.page.RouteReporter;
import com.meituan.msc.modules.preload.MSCHornPreloadConfig;
import com.meituan.msc.modules.preload.PackageDebugHelper;
import com.meituan.msc.modules.preload.PendingBizPreloadTasksManager;
import com.meituan.msc.modules.preload.PreloadManager;
import com.meituan.msc.modules.preload.PreloadTasksManager;
import com.meituan.msc.modules.preload.UnusedPreloadBizAppManager;
import com.meituan.msc.modules.reporter.MSCLog;
import com.meituan.msc.modules.update.AppConfigModule;
import com.meituan.msc.modules.update.MSCAppModule;
import com.meituan.msc.modules.update.MSCHornBasePackageReloadConfig;
import com.meituan.msc.modules.update.PackageLoadReporter;
import com.meituan.msc.modules.update.PackagePreLoadReporter;
import com.meituan.msc.modules.update.bean.AppMetaInfoWrapper;
import com.meituan.msc.modules.update.bean.PackageInfoWrapper;
import com.meituan.msc.modules.update.metainfo.AppCheckUpdateManager;
import com.meituan.msc.modules.update.pkg.PackageLoadManager;

import java.util.ArrayList;
import java.util.Arrays;
import java.util.Collection;
import java.util.Collections;
import java.util.HashMap;
import java.util.HashSet;
import java.util.List;
import java.util.Map;
import java.util.Set;

/**
 * 管理小程序引擎
 * <p>
 * Notice：测试情况下reload的引擎不纳入管理！
 * <p>
 * Created by letty on 2020/9/3.
 **/
public class RuntimeManager {
    /**
     * 最大保活个数
     */
    private static final int KEEP_ALIVE_MAX_SIZE = 3;
    public static final int MAX_PRELOAD_AND_KEEP_ALIVE_SHARE_ENGINE_COUNT = MSCHornRollbackConfig.enableAppSharedCountLimit() ? KEEP_ALIVE_MAX_SIZE + MSCHornPreloadConfig.get().getConfig().preloadAppLimitCount : KEEP_ALIVE_MAX_SIZE;

    // FIXME by chendacai: 2/22/22 保活池中存活逻辑是基于FIFO的，不需要使用LruCache
    /**
     * 保活状态中的引擎 内存警告可直接回收
     * <p>
     * <appId, AppEngine>
     * 引擎在所有页面销毁之后自动进入保活状态；最多保留3个；最多存活{@link MSCConfig#getEngineKeepAliveTime()}时间，默认5min
     * 目前3种模式均有保活行为：传统模式、融合模式使用此处保活引擎，独立任务栈模式通过task切换保活Activity
     */
    private static final LruCache<String, MSCApp> KEEP_ALIVE_APPS = new LruCache<>(MAX_PRELOAD_AND_KEEP_ALIVE_SHARE_ENGINE_COUNT);

    /**
     * 三方小程序单独设立保活队列 https://km.sankuai.com/collabpage/2707197355
    */
    private static final int EXTERNAL_KEEP_ALIVE_MAX_SIZE = 1;
    private static final LruCache<String, MSCApp> KEEP_ALIVE_EXTERNAL_APPS = new LruCache<>(EXTERNAL_KEEP_ALIVE_MAX_SIZE);


    /**
     * 所有引擎 包含保活及预加载的，reload的一开始不在，在启动至HeraActivity、保障了唯一性后会被加入
     * <p>
     * <AppEntity.instanceId, AppEntity>
     */
    private static final Map<Integer, MSCRuntime> ALL_RUNTIMES = new MPConcurrentHashMap<>();
    private static final String TAG = "RuntimeManager";
    private static IEngineStatusChangeListener engineStatusChangeListener;
    private static final MPConcurrentHashMap<String, String> destroyRuntimeReasonMap = new MPConcurrentHashMap<>();
    public static final MPConcurrentHashMap<String, String> keepAliveMissReasonMap = new MPConcurrentHashMap<>();
    private static final MPConcurrentHashMap<String, Long> lastEnterKeepAliveTimeMap = new MPConcurrentHashMap<>();

    private static volatile MSCRuntime latestAttachToContainerRuntime;

    private static final Set<String> TAG_LAS_KEEP_ALIVE_STRATEGY = new HashSet<>(Arrays.asList("A", "D", "E"));

    public static MSCRuntime getLatestAttachToContainerRuntime() {
        return latestAttachToContainerRuntime;
    }

    public static void setLatestAttachToContainerRuntime(MSCRuntime latestAttachToContainerRuntime) {
        RuntimeManager.latestAttachToContainerRuntime = latestAttachToContainerRuntime;
    }

    public static void init() {
        // 包下线的时，禁用对应运行时保活 & 销毁对应的保活/预热运行时
        registerAppVersionOfflineListener();

        // 拉取到新的需要重新加载的基础库版本时，禁用对应运行时保活 & 销毁对应的保活/预热运行时
        registerBasePackageReloadListener();
    }

    private static void registerAppVersionOfflineListener() {
        AppCheckUpdateManager.getInstance().addAppVersionOfflineListener(new AppCheckUpdateManager.OnAppVersionOfflineListener() {
            @Override
            public void onOffline(List<MSCAppIdPublishId> appIdVersions) {
                MSCLog.i(TAG, "[MSC][MSCRuntime] app offline");
                if (MSCHornRollbackConfig.get().getConfig().rollbackOfflineBizPackageChange) {
                    MSCLog.i(TAG, "[MSC][MSCRuntime] app offline rollback");
                } else {
                    // [策略] 包下线的时候销毁保活的引擎
                    for (MSCAppIdPublishId appIdPublishId : appIdVersions) {
                        if (appIdPublishId == null) {
                            continue;
                        }
                        markOrDestroyRuntimes(appIdPublishId);
                    }
                }
            }
        });
    }

    private static void markOrDestroyRuntimes(@NonNull MSCAppIdPublishId appIdPublishId) {
        markOrDestroyRuntimes(appIdPublishId.getAppId(), appIdPublishId.getPublishId());
    }

    public static void markOrDestroyRuntimes(String appId, String publishId) {
        if (MSCHornRollbackConfig.get().getConfig().isRollbackGetRuntimeChange) {
            synchronized (ALL_RUNTIMES) {
                for (MSCRuntime runtime : ALL_RUNTIMES.values()) {
                    MSCAppModule mscAppModule = runtime.getMSCAppModule();
                    if (mscAppModule == null || !mscAppModule.hasMetaInfo()) {
                        MSCLog.i(TAG, "markOrDestroyRuntimes no metaInfo");
                        continue;
                    }
                    if (!(Objects.equals(runtime.getAppId(), appId)
                            && Objects.equals(mscAppModule.getPublishId(), publishId))) {
                        continue;
                    }
                    // 业务预热中，不立即销毁，否则基础库注入会上报模块找不到错误
                    if (isRuntimePreloading(runtime)) {
                        // 标记运行时为下线状态，运行时征用时会禁止复用并销毁
                        MSCLog.i(TAG, "[MSC][MSCRuntime] mark runtime offline:", runtime);
                        runtime.getModule(IAppLoader.class).setBizPackageOffline();
                    } else {
                        MSCLog.i(TAG, "[MSC][MSCRuntime] destroy runtime on app offline:", runtime);
                        destroyRuntime(runtime, RuntimeDestroyReason.BUNDLE_OFFLINE);
                    }
                }
            }
        } else {
            asyncDestroyRuntimeByCondition(new Predicate<MSCRuntime>() {
                @Override
                public boolean test(MSCRuntime runtime) {
                    MSCAppModule mscAppModule = runtime.getMSCAppModule();
                    if (mscAppModule == null || !mscAppModule.hasMetaInfo()) {
                        MSCLog.i(TAG, "markOrDestroyRuntimes no metaInfo");
                        if (mscAppModule != null) {
                            mscAppModule.setOfflineBizFailReason(MSCAppModule.OFFLINE_BIZ_FAILED_REASON_NO_VERSION_INFO);
                        }
                        return false;
                    }
                    if (!(Objects.equals(runtime.getAppId(), appId)
                            && Objects.equals(mscAppModule.getPublishId(), publishId))) {
                        return false;
                    }
                    if (isRuntimePreloading(runtime)) {
                        MSCLog.i(TAG, "[MSC][MSCRuntime] mark runtime offline:", runtime);
                        runtime.getModule(IAppLoader.class).setBizPackageOffline();
                        return false;
                    }
                    if (!MSCHornRollbackConfig.get().getConfig().isRollbackBizOfflineRemoveRuntimeCacheFix
                            && runtime.hasContainerAttached()) {
                        markRuntimeUnusable(runtime, RuntimeDestroyReason.BUNDLE_OFFLINE);
                        MSCLog.i(TAG, "[MSC][MSCRuntime] hasContainerAttached:", runtime);
                        return false;
                    }
                    return true;
                }
            }, new Consumer<MSCRuntime>() {
                @Override
                public void accept(MSCRuntime runtime) {
                    MSCLog.i(TAG, "[MSC][MSCRuntime] destroy runtime on app offline:", runtime);
                    if (runtime.hasContainerAttached()) {
                        MSCAppModule mscAppModule = runtime.getMSCAppModule();
                        if (mscAppModule != null) {
                            mscAppModule.setOfflineBizFailReason(MSCAppModule.OFFLINE_FAILED_REASON_ATTACHED_PAGE);
                        }
                    }
                    destroyRuntime(runtime, RuntimeDestroyReason.BUNDLE_OFFLINE);
                }
            });
        }
    }

    private static void markRuntimeUnusable(MSCRuntime runtime, RuntimeDestroyReason destroyReason) {
        // 销毁运行时，如果运行时正在使用，则标记其不保活
        runtime.setDestroyWhenUnused(true);
        runtime.setDestroyWhenUnusedReason(destroyReason);
    }

    private static boolean isRuntimePreloading(MSCRuntime runtime) {
        IAppLoader appLoader = runtime.getModule(IAppLoader.class);
        return !appLoader.isBizPreloadReady() || !appLoader.isFrameworkReady();
    }

    private static void registerBasePackageReloadListener() {
        MSCHornBasePackageReloadConfig.get().addBasePackageReloadListener(new MSCHornBasePackageReloadConfig.BasePackageReloadListener() {
            @Override
            public void onReload(String[] sdkReloadVersions) {
                for (String version : sdkReloadVersions) {
                    if (TextUtils.isEmpty(version)) {
                        continue;
                    }

                    if (MSCHornRollbackConfig.get().getConfig().isRollbackGetRuntimeChange) {
                        synchronized (ALL_RUNTIMES) {
                            for (MSCRuntime runtime : ALL_RUNTIMES.values()) {
                                if (!TextUtils.equals(runtime.getMSCAppModule().getBasePkgVersion(), version)) {
                                    continue;
                                }
                                MSCLog.i(TAG, "[MSC][MSCRuntime] destroy runtime on base package reload:", runtime);
                                // 当前基础库版本 在强制更新名单，需要重新拉取
                                PackageLoadManager.getInstance().cleanMSCAARVersionCache();
                                destroyAndRePreload(runtime, RuntimeDestroyReason.BASE_PACKAGE_ON_RELOAD);
                            }
                        }
                    } else {
                        asyncDestroyRuntimeByCondition(new Predicate<MSCRuntime>() {
                            @Override
                            public boolean test(MSCRuntime runtime) {
                                boolean needReloadBasePackage = TextUtils.equals(runtime.getMSCAppModule().getBasePkgVersion(), version);
                                if (needReloadBasePackage) {
                                    // TODO: 2023/12/12 tianbin 重新拉取基础库需要立即生效，Consumer会切线程会影响生效时机，暂无更好方案
                                    // 当前基础库版本 在强制更新名单，需要重新拉取
                                    MSCLog.i(TAG, "cleanMSCAARVersionCache", version);
                                    PackageLoadManager.getInstance().cleanMSCAARVersionCache();
                                }
                                return needReloadBasePackage;
                            }
                        }, new Consumer<MSCRuntime>() {
                            @Override
                            public void accept(MSCRuntime runtime) {
                                MSCLog.i(TAG, "[MSC][MSCRuntime] destroy runtime on base package reload:", runtime);
                                destroyAndRePreload(runtime, RuntimeDestroyReason.BASE_PACKAGE_ON_RELOAD);
                            }
                        });
                    }
                }
            }
        });
    }

    private static void destroyAndRePreload(MSCRuntime runtime, RuntimeDestroyReason destroyReason) {
        destroyRuntime(runtime, destroyReason, new Callback<Void>() {
            @Override
            public void onSuccess(Void data) {
                RuntimeSource runtimeSource = runtime.getSource();
                if (runtimeSource == RuntimeSource.BASE_PRELOAD) {
                    rePreloadBase();
                } else if (runtimeSource == RuntimeSource.BIZ_PRELOAD) {
                    rePreloadBiz(runtime.getAppId());
                }
            }

            @Override
            public void onFail(String errMsg, Exception error) {
                MSCLog.e(TAG, error, errMsg);
            }

            @Override
            public void onCancel() {
            }
        });
    }

    private static void rePreloadBiz(String appId) {
        MSCLog.i(Constants.PRELOAD_BIZ, "preload app after reload base package");
        PreloadManager.getInstance().preloadMSCApp(appId, new Callback<MSCRuntime>() {
            @Override
            public void onSuccess(MSCRuntime data) {
                MSCLog.i(Constants.PRELOAD_BIZ, "preload app after reload end:", data);
            }

            @Override
            public void onFail(String errMsg, Exception error) {
                MSCLog.e(Constants.PRELOAD_BIZ, error, "preload app after reload error", errMsg);
            }

            @Override
            public void onCancel() {
            }
        });
    }

    private static void rePreloadBase() {
        long delayMillis = MSCHornPreloadConfig.getBasePreloadDelayWhenUsed() * 1000L;
        CompletableFuture<MSCRuntime> future = PreloadTasksManager.instance.preloadBasePackageAgain(delayMillis);
        if (future == null) {
            return;
        }

        future.handle(new BiFunction<MSCRuntime, Throwable, Object>() {
            @Override
            public Object apply(MSCRuntime runtime, Throwable throwable) {
                if (throwable != null) {
                    MSCLog.i(Constants.PRELOAD_BASE, "rePreload base after reload error", throwable);
                } else {
                    MSCLog.i(Constants.PRELOAD_BASE, "rePreload base after reload end:", runtime);
                }
                return null;
            }
        });
    }

    public static void setEngineStatusChangeListener(IEngineStatusChangeListener engineStatusChangeListener) {
        RuntimeManager.engineStatusChangeListener = engineStatusChangeListener;
    }

    /**
     * 仅在线下使用
     * @param runtimeId runtimeId
     * @param appId appId
     * @return runtime
     */
    public static MSCRuntime getRuntimeWithId(int runtimeId, String appId) {
        MSCRuntime result = null;
        MSCRuntime runtime = ALL_RUNTIMES.get(runtimeId);
        if (runtime != null && (runtime.getAppId() == null || TextUtils.equals(runtime.getAppId(), appId))) {
            result = runtime;
        }
        return result;
    }

    // TODO: 2022/8/16 tianbin 单侧建立后进行重构，减少圈复杂度
    public static MSCRuntime getRuntimeForLaunch(String appId, String targetPath, ContainerDebugLaunchData debugLaunchData, boolean createIfAbsence, boolean disableReuseAny, boolean isUrlExternalApp) {
        Map<MSCRuntime, RuntimeDestroyReason> runtimeDestroyPendingMap = new HashMap<>();
        boolean isRollbackGetRuntimeChange = MSCHornRollbackConfig.get().getConfig().isRollbackGetRuntimeChange;
        MSCRuntime runtime = getRuntimeForLaunch(appId, targetPath, debugLaunchData, runtimeDestroyPendingMap,
                isRollbackGetRuntimeChange, createIfAbsence, disableReuseAny, isUrlExternalApp);
        // 延迟触发运行时销毁，解决锁耗时过长问题
        if (!isRollbackGetRuntimeChange) {
            delayAsyncDestroyRuntimes(runtimeDestroyPendingMap);
        }
        return runtime;
    }

    private static MSCRuntime getRuntimeForLaunch(String appId, String targetPath, ContainerDebugLaunchData debugLaunchData,
                                                  Map<MSCRuntime, RuntimeDestroyReason> runtimeDestroyPendingMap,
                                                  boolean isRollbackGetRuntimeChange,
                                                  boolean createIfAbsence,
                                                  boolean disableReuseAny,
                                                  boolean isUrlExternalApp) {
        synchronized (ALL_RUNTIMES) {
            if (debugLaunchData.needCreateRuntime()) {
                MSCLog.i(TAG, "needCreateRuntime is true on debug env");
                if (MSCEnvHelper.isInited() && !MSCEnvHelper.getEnvInfo().isProdEnv()) {
                    // 线下测试模式，扫码直接删掉原runtime，同步iOS改动。
                    MSCRuntime last = getRuntimeWithAppId(appId);
                    if (null != last) {
                        if (isRollbackGetRuntimeChange) {
                            MSCLog.i(TAG, "need destroy cached runtime when in debug env.");
                            destroyRuntime(last, RuntimeDestroyReason.DEBUG_DESTROY_CACHE_RUNTIME);
                        } else {
                            runtimeDestroyPendingMap.put(last, RuntimeDestroyReason.DEBUG_DESTROY_CACHE_RUNTIME);
                        }
                    }
                }
                return createIfAbsence ? createMSCRuntime(appId, debugLaunchData) : null;
            }
            if (disableReuseAny) {
                MSCLog.i(TAG, "disableReuseAny");
                return createIfAbsence ? createMSCRuntime(appId, debugLaunchData) : null;
            }
            // 0. 运行中 widget场景 + 融合模式
            // 1. 获取保活
            // 2. 获取业务预热
            // 3. 获取基础库预热
            // 4. 新建
            MSCRuntime reuseNew = null;
            MSCRuntime aliveRuntime = null;
            MSCRuntime bizPreloadRuntime = null;
            MSCRuntime basePreloadRuntime = null;
            for (MSCRuntime runtime : ALL_RUNTIMES.values()) {
                // 线下调试场景下，基础库版本不一致时，不可复用运行时
                if (!PackageDebugHelper.instance.isBasePackageVersionMatchForDebug(debugLaunchData, runtime)) {
                    MSCLog.i(TAG, "isBasePackageVersionMatchForDebug false");
                    continue;
                }

                if (!canUseRuntime(runtime, isRollbackGetRuntimeChange, runtimeDestroyPendingMap)){
                    continue;
                }

                IAppLoader appLoader = runtime.getModule(IAppLoader.class);
                MSCLog.i(TAG, "getRuntimeForLaunch", runtime);
                if (TextUtils.equals(runtime.getAppId(), appId) && !disableReuseRuntimeOfDebug(runtime, debugLaunchData)) {
                    if (runtime.getSource() == RuntimeSource.KEEP_ALIVE && appLoader.isLaunched()) {
                        // 保活的
                        aliveRuntime = runtime;
                    } else if (runtime.getSource() == RuntimeSource.BIZ_PRELOAD) {
                        // 业务预热的
                        bizPreloadRuntime = runtime;
                    } else {
                        // 运行中的 widget场景 + 融合模式
                        reuseNew = runtime;
                    }
                } else {
                    MSCLog.i(TAG, "getRuntimeForLaunch", "appId not equals or disableReuseRuntime", runtime);
                }
                if (!appLoader.isUsed()
                        && TextUtils.isEmpty(runtime.getAppId())
                        && runtime.getSource() == RuntimeSource.BASE_PRELOAD
                        && !runtime.isLocked()) {
                    // 3. 获取基础库预热
                    // 如果是三方小程序，不允许复用基础库预热的runtime
                    if (MSCHornRollbackConfig.enableExternalAppPrefSourceLimit() && isUrlExternalApp) {
                        MSCLog.d("external App, not allow to reuse base preload runtime");
                        continue;
                    }
                    runtime.lock();
                    if (basePreloadRuntime != null) { //新找到的基础库预期，覆盖前面找到的
                        basePreloadRuntime.unLock();
                    }
                    basePreloadRuntime = runtime;
                }
            }

            if (reuseNew != null) {
                String msg = "复用运行时:" + appId;
                reuseNew.isReuseRuntimeLaunch = true;
                showRuntimeSourceToast(msg, createIfAbsence);
                unLockBasePreloadRuntime(basePreloadRuntime);
                MSCLog.i(TAG, msg, reuseNew);
                return reuseNew;
            } else if (aliveRuntime != null) {
                String msg = "复用保活的运行时:" + appId;
                showRuntimeSourceToast(msg, createIfAbsence);
                unLockBasePreloadRuntime(basePreloadRuntime);
                MSCLog.i(TAG, msg, aliveRuntime);
                return aliveRuntime;
            } else if (bizPreloadRuntime != null) {
                String msg = "使用预热业务包的运行时:" + EngineHelper.getAppIdOrBaseVersion(bizPreloadRuntime);
                showRuntimeSourceToast(msg, createIfAbsence);
                // 自动化测试依赖的日志标志，请勿删除
                MSCLog.i(TAG, "reuse preload runtime:" + EngineHelper.getAppIdOrBaseVersion(bizPreloadRuntime), bizPreloadRuntime, msg);

                IAppLoader appLoader = bizPreloadRuntime.getModule(IAppLoader.class);
                bizPreloadRuntime.setRuntimeStateBeforeLaunch(appLoader.getRuntimeStateBeforeLaunch());
                unLockBasePreloadRuntime(basePreloadRuntime);
                return bizPreloadRuntime;
            } else if (basePreloadRuntime != null) {
                String msg = "使用预热基础包的运行时:" + EngineHelper.getAppIdOrBaseVersion(basePreloadRuntime);
                showRuntimeSourceToast(msg, createIfAbsence);
                // 自动化测试依赖的日志标志，请勿删除
                MSCLog.i(TAG, "[MSC][Preload] use preload engine", ", version:", basePreloadRuntime.getMSCAppModule().getBasePkgVersion(),
                        basePreloadRuntime, msg);
                EngineHelper.setPreviewCheckUpdateUrlOfDebug(basePreloadRuntime, appId, debugLaunchData);
                IAppLoader appLoader = basePreloadRuntime.getModule(IAppLoader.class);
                basePreloadRuntime.setRuntimeStateBeforeLaunch(appLoader.getRuntimeStateBeforeLaunch());
                return basePreloadRuntime;
            } else {
                // 4. 新建
                return createIfAbsence ? createMSCRuntime(appId, debugLaunchData) : null;
            }
        }
    }

    public static boolean canUseRuntime(MSCRuntime runtime,  boolean isRollbackGetRuntimeChange, Map<MSCRuntime,RuntimeDestroyReason> runtimeDestroyPendingMap) {
        // applyUpdate不可复用
        if (runtime.isDisableReuse()) {
            MSCLog.i(TAG, "isDisableReuse true");
            runtimeDestroyPendingMap.put(runtime, RuntimeDestroyReason.DISABLE_REUSE_ANY);
            return false;
        }

        // 基础包版本需要强制更新
        String basePkgVersion = runtime.getMSCAppModule().getBasePkgVersion();
        if (MSCHornBasePackageReloadConfig.get().isInReloadVersions(basePkgVersion)
                && !runtime.hasContainerAttached()) {
            MSCLog.i(TAG, "base package version in reload list");
            if (isRollbackGetRuntimeChange) {
                destroyRuntime(runtime, RuntimeDestroyReason.BASE_PACKAGE_NEED_RELOAD);
            } else {
                runtimeDestroyPendingMap.put(runtime, RuntimeDestroyReason.BASE_PACKAGE_NEED_RELOAD);
            }
            return false;
        }

        IAppLoader appLoader = runtime.getModule(IAppLoader.class);
        // 运行时预热/启动时出错 不再复用
        if (!appLoader.isUsable()) {
            MSCLog.i(TAG, "runtime is not usable", runtime);
            if (!runtime.hasContainerAttached()) {
                if (isRollbackGetRuntimeChange) {
                    destroyRuntime(runtime, RuntimeDestroyReason.NOT_USABLE);
                } else {
                    runtimeDestroyPendingMap.put(runtime, RuntimeDestroyReason.NOT_USABLE);
                }
            }
            return false;
        }

        return true;
    }

    private static void unLockBasePreloadRuntime(MSCRuntime mscRuntime) {
        if (mscRuntime != null && mscRuntime.getSource() == RuntimeSource.BASE_PRELOAD
                && TextUtils.isEmpty(mscRuntime.getAppId())) {
            mscRuntime.unLock();
        }
    }

    private static void showRuntimeSourceToast(String msg, boolean createIfAbsence) {
        // 数据预拉取场景也会获取运行时，但不需要弹Toast
        if (createIfAbsence) {
            ToastUtils.toastIfDebug(msg);
        }
    }

    public static void delayAsyncDestroyRuntimes(Map<MSCRuntime, RuntimeDestroyReason> runtimeDestroyPendingMap) {
        MSCExecutors.submit(new Runnable() {
            @Override
            public void run() {
                MSCLog.i(TAG, "delayAsyncDestroyRuntimes");
                for (Map.Entry<MSCRuntime, RuntimeDestroyReason> entry : runtimeDestroyPendingMap.entrySet()) {
                    destroyRuntime(entry.getKey(), entry.getValue());
                }
            }
        });
    }

    public static boolean isPageNotFound(MSCRuntime runtime, String targetPath) {
        if (TextUtils.isEmpty(targetPath)) {
            return false;
        }
        AppConfigModule appConfigModule = runtime.getAppConfigModule();
        // 业务预热场景下，可能存在业务包未下载完毕的场景，返回true，页面不存在场景由启动流程兜底
        if (appConfigModule.mPages == null) {
            MSCLog.i(TAG, "isPageNotFound pages is null");
            return false;
        }
        return !appConfigModule.hasPage(targetPath);
    }

    private static MSCRuntime createMSCRuntime(String appId, ContainerDebugLaunchData debugLaunchData) {
        String msg = "新建运行时:" + appId;
        ToastUtils.toastIfDebug(msg);
        MSCRuntime runtime = RuntimeManager.createRuntime(appId, debugLaunchData.getCheckUpdateUrl(), debugLaunchData.isReload());
        runtime.setRuntimeStateBeforeLaunch(RuntimeStateBeforeLaunch.NEW);
        EngineHelper.setPreviewCheckUpdateUrlOfDebug(runtime, appId, debugLaunchData);

        // 重置路由埋点计数器
        RouteReporter.routeExceptCount.set(0);

        MSCLog.i(TAG, msg, runtime);
        return runtime;
    }

    public static MSCRuntime getRuntimeWithAppId(String appId) {
        if (MSCHornRollbackConfig.get().getConfig().isRollbackGetRuntimeChange) {
            return getRuntimeWithAppIdByLock(appId);
        } else {
            return realGetMSCRuntimeByAppId(appId);
        }
    }

    public static MSCRuntime getRuntimeWithAppIdByLock(String appId) {
        synchronized (ALL_RUNTIMES) {
            return realGetMSCRuntimeByAppId(appId);
        }
    }

    @Nullable
    private static MSCRuntime realGetMSCRuntimeByAppId(String appId) {
        for (MSCRuntime runtime : ALL_RUNTIMES.values()) {
            IAppLoader appLoader = runtime.getModule(IAppLoader.class);
            if (appLoader.isUsable() && TextUtils.equals(runtime.getAppId(), appId)) {
                return runtime;
            }
        }
        return null;
    }

    public static MSCRuntime findBizPreloadRuntime(String appId) {
        if (MSCHornRollbackConfig.get().getConfig().isRollbackGetRuntimeChange) {
            return findBizPreloadRuntimeByLock(appId);
        } else {
            return realFindBizPreloadRuntime(appId);
        }
    }

    public static MSCRuntime findBizPreloadRuntimeByLock(String appId) {
        synchronized (ALL_RUNTIMES) {
            return realFindBizPreloadRuntime(appId);
        }
    }

    private static MSCRuntime realFindBizPreloadRuntime(String appId) {
        for (MSCRuntime runtime : ALL_RUNTIMES.values()) {
            IAppLoader appLoader = runtime.getModule(IAppLoader.class);
            if (appLoader.isUsable() && TextUtils.equals(runtime.getAppId(), appId)
                    && runtime.getSource() == RuntimeSource.BIZ_PRELOAD) {
                return runtime;
            }
        }
        return null;
    }

    /**
     * 为闪购提供的获取在使用的基础库版本接口，暂时保留锁
     */
    public static List<Pair<String, String>> getUsingBasePackageVersions() {
        List<Pair<String, String>> versions = new ArrayList<>();
        synchronized (ALL_RUNTIMES) {
            for (MSCRuntime runtime : ALL_RUNTIMES.values()) {
                IAppLoader appLoader = runtime.getModule(IAppLoader.class);
                if (appLoader.isUsable()) {
                    MSCAppModule mscAppModule = runtime.getMSCAppModule();
                    versions.add(new Pair<>(mscAppModule.getBasePkgName(), mscAppModule.getBasePkgVersion()));
                }
            }
        }
        return versions;
    }

    private static boolean disableReuseRuntimeOfDebug(MSCRuntime runtime, ContainerDebugLaunchData launchData) {
        if (MSCEnvHelper.getEnvInfo().isProdEnv()) {
            return false;
        }
        IAppLoader appLoader = runtime.getModule(IAppLoader.class);
        String checkUpdateUrlOfRuntime = appLoader.getCheckUpdateUrl();
        String checkUpdateUrlOfLaunch = launchData.getCheckUpdateUrl();
        // 启动参数checkUpdateUrl为空，则允许复用
        if (TextUtils.isEmpty(checkUpdateUrlOfLaunch)) {
            return false;
        }
        // 启动参数checkUpdateUrl不为空 且 已启动运行时中checkUpdateUrl为空，则不允许复用
        if (TextUtils.isEmpty(checkUpdateUrlOfRuntime)) {
            return true;
        }
        // 两者都不为空时，如果两者相同，则允许复用，否则不复用
        return !TextUtils.equals(checkUpdateUrlOfRuntime, checkUpdateUrlOfLaunch);
    }

    public static MSCRuntime findBasePreloadRuntime() {
        if (MSCHornRollbackConfig.get().getConfig().isRollbackGetRuntimeChange) {
            return findBasePreloadRuntimeByLock();
        } else {
            return realFindBasePreloadRuntime();
        }
    }

    /**
     * 启动、业务预热场景获取引擎时并锁住，避免多个业务使用
     * @return
     */
    public static MSCRuntime findBasePreloadRuntimeWithLock() {
        MSCRuntime mscRuntime = findBasePreloadRuntime();
        PreloadManager.getInstance().setPreloadBaseMetricsInfo("basePreloadUsedNoTriggerAgain", "base preload engine used, not trigger again");
        if  (mscRuntime != null) {
            if (mscRuntime.isLocked()) {  //获取到基础库预热引擎已被其他业务获取使用，返回null，无可用的基础库预热引擎
                return null;
            } else {
                mscRuntime.lock();
            }
        }

        return mscRuntime;
    }

    public static MSCRuntime findBasePreloadRuntimeByLock() {
        synchronized (ALL_RUNTIMES) {
            return realFindBasePreloadRuntime();
        }
    }

    private static MSCRuntime realFindBasePreloadRuntime() {
        for (MSCRuntime runtime : ALL_RUNTIMES.values()) {
            IAppLoader appLoader = runtime.getModule(IAppLoader.class);
            if (appLoader.isUsable() && !appLoader.isUsed()
                    && TextUtils.isEmpty(runtime.getAppId())
                    && runtime.getSource() == RuntimeSource.BASE_PRELOAD
                    && !runtime.isLocked()) {
                return runtime;
            }
        }
        return null;
    }

    @Nullable
    public static MSCApp getKeepAliveAppWithAppId(String appId) {
        MSCApp app;
        synchronized (KEEP_ALIVE_APPS) {
            app = KEEP_ALIVE_APPS.get(appId);
            if (app != null) {
                return app;
            }
        }
        if (MSCHornRollbackConfig.enableExternalAppKeepAliveRule()) {
            synchronized (KEEP_ALIVE_EXTERNAL_APPS) {
                return KEEP_ALIVE_EXTERNAL_APPS.get(appId);
            }
        }
        return null;
    }

    /**
     * 如不存在当前业务的运行时创建一个，否则返回
     * @param appId appId
     * @return runtime
     */
    public static MSCRuntime createRuntimeIfNotExist(String appId) {
        synchronized (ALL_RUNTIMES) {
            MSCRuntime runtime = realGetMSCRuntimeByAppId(appId);
            if (runtime != null) {
                // 已存在当前业务运行时，不需要再次创建
                return null;
            }
            return createRuntime(appId, null, false);
        }
    }

    /**
     * 创建新的引擎，唯一的一个创建引擎的地方，引擎指的是 MSCRuntime
     */
    @NonNull
    public static MSCRuntime createRuntime(String appId, String checkUpdateUrl, boolean reload) {
        MSCRuntime runtime = new MSCRuntime();
        IAppLoader appLoader = runtime.getModule(IAppLoader.class);
        appLoader.setNeedForceUpdate(reload);
        appLoader.setCheckUpdateUrl(checkUpdateUrl);
        appLoader.setStatusChangeListener(engineStatusChangeListener);
        if (appId != null) {
            runtime.startApp(appId);
        }
        // 引擎一旦创建就会放到 RUNNING_APPS 中去，所以 RUNNING_APPS 不单单包含"正在运行的引擎"还包含预加载的引擎
        synchronized (ALL_RUNTIMES) {
            ALL_RUNTIMES.put(runtime.getRuntimeId(), runtime);
        }
        GlobalEngineMonitor.getInstance().recordRuntimeCreate(runtime);

        if (reload) {
            // 创建reload情况下的新引擎，不纳入管理范围
            runtime.getModule(IAppLoader.class).setReload(true);
        }
        return runtime;
    }

    @VisibleForTesting
    public static void addAppEngine(MSCRuntime runtime) {
        synchronized (ALL_RUNTIMES) {
            ALL_RUNTIMES.put(runtime.getRuntimeId(), runtime);
        }
        GlobalEngineMonitor.getInstance().recordRuntimeCreate(runtime);
    }

    /**
     * 移除小程序引擎，由引擎销毁时自行调用！
     */
    public static void removeApp(MSCApp app) {
        removeApp(app.getRuntime());
    }

    /**
     * 从引擎池中移除指定的引擎
     */
    public static void removeApp(MSCRuntime mscRuntime) {
        if (mscRuntime == latestAttachToContainerRuntime && !MSCHornRollbackConfig.isRollbackLatestAttachToContainerRuntimeLeakFix()) {
            latestAttachToContainerRuntime = null;
        }
        if (!TextUtils.isEmpty(mscRuntime.getAppId())) {
            synchronized (KEEP_ALIVE_APPS) {
                KEEP_ALIVE_APPS.remove(mscRuntime.getAppId());
            }
            if (MSCHornRollbackConfig.enableExternalAppKeepAliveRule()) {
                synchronized (KEEP_ALIVE_EXTERNAL_APPS) {
                    KEEP_ALIVE_EXTERNAL_APPS.remove(mscRuntime.getAppId());
                }
            }
        }

        synchronized (ALL_RUNTIMES) {
            ALL_RUNTIMES.remove(mscRuntime.getRuntimeId());
        }
        GlobalEngineMonitor.getInstance().recordRuntimeDestroy(MSCProcess.getCurrentProcessShortName());
    }

    // 基于全部可执行AppEngine均应分配在同一进程，未做GlobalEngineManager跨进程同步
    public static void destroyAllOtherSameAppIdEngine(MSCRuntime currentRuntime, String reason) {
        if (MSCHornRollbackConfig.get().getConfig().isRollbackGetRuntimeChange) {
            synchronized (ALL_RUNTIMES) {
                for (MSCRuntime runtime : ALL_RUNTIMES.values()) {
                    if (TextUtils.equals(runtime.getAppId(), currentRuntime.getAppId()) && runtime != currentRuntime) {
                        MSCLog.i(TAG, "destroy running engine", runtime);
                        runtime.destroyEngineIfNoCount(reason);
                    }
                }
            }
        } else {
            asyncDestroyRuntimeByCondition(new Predicate<MSCRuntime>() {
                @Override
                public boolean test(MSCRuntime runtime) {
                    return TextUtils.equals(runtime.getAppId(), currentRuntime.getAppId()) && runtime != currentRuntime;
                }
            }, new Consumer<MSCRuntime>() {
                @Override
                public void accept(MSCRuntime runtime) {
                    MSCLog.i(TAG, "destroy running engine", runtime);
                    runtime.destroyEngineIfNoCount(reason);
                }
            });
        }
    }

    /**
     * 内存紧张情况出咯
     */
    public static void onLowMemory() {
        if (MSCConfig.shouldDestroyEngineOnTrimMemory()) {
            MSCLog.i(TAG, "onLowMemory destroyAllKeepAliveEngine");
            destroyAllKeepAliveEngine(RuntimeDestroyReason.ON_LOW_MEMORY);
        } else {
            MSCLog.i(TAG, "shouldDestroyEngineOnTrimMemory is off");
        }
    }

    private static boolean isLASKeepAliveStrategy() {
        return TAG_LAS_KEEP_ALIVE_STRATEGY.contains(MSCHornRollbackConfig.localAppStackPriorityStrategy());
    }

    /**
     * 引擎进入保活状态
     *
     * @param app
     * @return 是否成功保活
     */
    public static boolean addKeepAliveApp(MSCApp app) {
        if (app == null) {
            return false;
        }

        if (MSCHornRollbackConfig.enableExternalAppKeepAliveRule() && app.getRuntime().getMSCAppModule().getExternalApp()) {
            synchronized (KEEP_ALIVE_EXTERNAL_APPS) {
                if (KEEP_ALIVE_EXTERNAL_APPS.size() == EXTERNAL_KEEP_ALIVE_MAX_SIZE) {
                    destroyEarliestKeepAliveExternalEngine();
                }
                KEEP_ALIVE_EXTERNAL_APPS.put(app.getAppId(), app);
                GlobalEngineMonitor.getInstance().recordRuntimeKeepAliveChange(app.getRuntime(), true);
            }
            return true;
        }

        synchronized (KEEP_ALIVE_APPS) {
            boolean shouldKeepAlive = true;
            if (MSCHornRollbackConfig.enableAppSharedCountLimit()) {
                // 引擎数量阈值共享：https://km.sankuai.com/collabpage/2707197355
                int curRuntimeCount = KEEP_ALIVE_APPS.size() + UnusedPreloadBizAppManager.instance.getAppSizePreloadedAndPending();
                if (curRuntimeCount >= MAX_PRELOAD_AND_KEEP_ALIVE_SHARE_ENGINE_COUNT) {
                    if (KEEP_ALIVE_APPS.size() < KEEP_ALIVE_MAX_SIZE) {
                        UnusedPreloadBizAppManager.instance.cleanPreloadAppStrategy(null);
                    } else {
                        shouldKeepAlive = destroyEarliestKeepAliveEngine(app);
                    }
                }
            } else if (KEEP_ALIVE_APPS.size() == KEEP_ALIVE_MAX_SIZE) {  //TODO 计数应该包含主进程和子进程，重建时的排序是个难点
                shouldKeepAlive = destroyEarliestKeepAliveEngine(app);
            }
            
            if (shouldKeepAlive) {
                KEEP_ALIVE_APPS.put(app.getAppId(), app);
                GlobalEngineMonitor.getInstance().recordRuntimeKeepAliveChange(app.getRuntime(), true);

                ToastUtils.toastIfDebug("引擎进入保活状态");
                MSCLog.d("EngineManager", "addKeepAliveEngine");
                return true;
            } else {
                return false;
            }
        }
    }

    public static boolean destroyEarliestKeepAliveEngine(MSCApp app) {
        try {
            if (KEEP_ALIVE_APPS.snapshot() != null) {
                MSCApp previousInstance;
                if (isLASKeepAliveStrategy() && app != null) {  // 只有当app不为null时才执行LAS策略
                    previousInstance = getRemoveKeepAliveAppLAS(app);
                    if (previousInstance == null) {
                        // 新 app 权重低于队列所有 appId，不保活
                        return false;
                    }
                } else {
                     previousInstance = KEEP_ALIVE_APPS.remove(
                        KEEP_ALIVE_APPS.snapshot().entrySet().iterator().next().getKey());
                }
                destroyKeepAliveEngine(previousInstance, RuntimeDestroyReason.EXCEED_KEEP_ALIVE_LIMIT);
            }
        } catch (Throwable th) {
            //todo report
        }
        return true;
    }

    public static void destroyEarliestKeepAliveExternalEngine() {
        try {
            if (KEEP_ALIVE_EXTERNAL_APPS.snapshot() != null) {
                MSCApp previousInstance = KEEP_ALIVE_EXTERNAL_APPS.remove(
                        KEEP_ALIVE_EXTERNAL_APPS.snapshot().entrySet().iterator().next().getKey());
                destroyKeepAliveEngine(previousInstance, RuntimeDestroyReason.EXCEED_KEEP_ALIVE_LIMIT);
            }
        } catch (Throwable th) {
            //todo report
        }
    }

    private static MSCApp getRemoveKeepAliveAppLAS(MSCApp app) {
        // 获取队列最小权重
        int minWeight = Integer.MAX_VALUE;
        for (String appId : KEEP_ALIVE_APPS.snapshot().keySet()) {
            int weight = MSCLocalAppStackHelper.getAppIdKeepAliveWeight(appId);
            if (weight < minWeight) {
                minWeight = weight;
            }
        }
        if (app != null && !TextUtils.isEmpty(app.getAppId())) {
            int newAppWeight = MSCLocalAppStackHelper.getAppIdKeepAliveWeight(app.getAppId());
            // 新 app 权重低于队列所有 appId，则不保活
            if (newAppWeight < minWeight) {
                return null;
            }
        }

        // 清理一个 LAS 项
        String removeAppId = null;
        for (String appId : KEEP_ALIVE_APPS.snapshot().keySet()) {
            int weight = MSCLocalAppStackHelper.getAppIdKeepAliveWeight(appId);
            if (weight == minWeight) {
                removeAppId = appId;
                break;
            }
        }
        if (removeAppId != null) {
            return KEEP_ALIVE_APPS.remove(removeAppId);
        }
        return null;
    }


    public static void destroyKeepAliveEngine(MSCApp app, RuntimeDestroyReason destroyReason) {
        if (app != null) {
            ToastUtils.toastIfDebug("销毁保活的引擎");
            MSCLog.d("RuntimeManager destroyKeepAliveEngine appId", app.getAppId(), "reason:", RuntimeDestroyReason.toString(destroyReason));
            app.getRuntime().destroyEngineIfNoCount(RuntimeDestroyReason.toString(destroyReason));
        }
    }

    public static void destroyKeepAliveEngineAndBizPreloadEngineWhenLogin() {
        destroyKeepAliveEngineAndBizPreloadEngine(RuntimeDestroyReason.LOGIN_STATUS_CHANGE);
    }

    /**
     * 杀死业务保活引擎、业务预热引擎
     */
    public static void destroyKeepAliveEngineAndBizPreloadEngine(RuntimeDestroyReason runtimeDestroyReason) {
        // 销毁保活引擎
        destroyAllKeepAliveEngine(runtimeDestroyReason);
        if (MSCHornRollbackConfig.get().getConfig().isRollbackGetRuntimeChange) {
            synchronized (ALL_RUNTIMES) {
                for (MSCRuntime runtime : ALL_RUNTIMES.values()) {
                    // 销毁业务预热引擎
                    if (runtime.isBizPreloadAndNoLaunched()) {
                        destroyRuntime(runtime, runtimeDestroyReason);
                    }
                }
            }
        } else {
            asyncDestroyRuntimeByCondition(new Predicate<MSCRuntime>() {
                @Override
                public boolean test(MSCRuntime runtime) {
                    return runtime.isBizPreloadAndNoLaunched();
                }
            }, new Consumer<MSCRuntime>() {
                @Override
                public void accept(MSCRuntime runtime) {
                    destroyRuntime(runtime, runtimeDestroyReason);
                }
            });
        }
    }

    public static void asyncDestroyRuntimeByCondition(Predicate<MSCRuntime> predicate, Consumer<MSCRuntime> consumer) {
        List<MSCRuntime> runtimes = new ArrayList<>();
        synchronized (ALL_RUNTIMES) {
            for (MSCRuntime runtime : ALL_RUNTIMES.values()) {
                if (runtime == null) {
                    continue;
                }
                if (predicate.test(runtime)) {
                    ALL_RUNTIMES.remove(runtime.getRuntimeId());
                    runtimes.add(runtime);
                }
            }
        }

        MSCExecutors.submit(new Runnable() {
            @Override
            public void run() {
                for (MSCRuntime runtime : runtimes) {
                    consumer.accept(runtime);
                }
            }
        });
    }

    public static void destroyRuntime(MSCRuntime runtime, RuntimeDestroyReason destroyReason) {
        MSCLog.i(TAG, "destroyRuntime", runtime, "appId:", runtime == null ? "" : runtime.getAppId(),
                "reason:", RuntimeDestroyReason.toString(destroyReason));
        if (runtime == null) {
            return;
        }
        // 销毁运行时，如果运行时正在使用，则标记其不保活
        runtime.setDestroyWhenUnused(true);
        runtime.setDestroyWhenUnusedReason(destroyReason);
        runtime.destroyEngineIfNoCount(RuntimeDestroyReason.toString(destroyReason));
    }

    public static void destroyRuntime(MSCRuntime runtime, RuntimeDestroyReason destroyReason, Callback<Void> callback) {
        // 销毁运行时，如果运行时正在使用，则标记其不保活
        runtime.setDestroyWhenUnused(true);
        runtime.setDestroyWhenUnusedReason(destroyReason);
        runtime.destroyEngineIfNoCount(RuntimeDestroyReason.toString(destroyReason), callback);
    }

    /**
     * 重新激活保活引擎
     *
     * @param app
     */
    public static void reActiveKeepAliveEngine(MSCApp app) {
        if (app == null) return;
        MSCApp removedApp = null;
        synchronized (KEEP_ALIVE_APPS) {
            removedApp = KEEP_ALIVE_APPS.remove(app.getAppId());
        }
        if (MSCHornRollbackConfig.enableExternalAppKeepAliveRule() && removedApp == null) {
            synchronized (KEEP_ALIVE_EXTERNAL_APPS) {
                KEEP_ALIVE_EXTERNAL_APPS.remove(app.getAppId());
            }
        }
        GlobalEngineMonitor.getInstance().recordRuntimeKeepAliveChange(app.getRuntime(), false);
    }

    /**
     * 销毁所有保活的引擎
     */
    public static void destroyAllKeepAliveEngine(RuntimeDestroyReason destroyReason) {
        synchronized (KEEP_ALIVE_APPS) {
            if (KEEP_ALIVE_APPS.size() <= 0) {
                return;
            }
            for (Map.Entry<String, MSCApp> entry : KEEP_ALIVE_APPS.snapshot().entrySet()) {
                destroyKeepAliveEngine(entry.getValue(), destroyReason);
            }
            KEEP_ALIVE_APPS.evictAll();
        }
        if (MSCHornRollbackConfig.enableExternalAppKeepAliveRule()) {
            synchronized (KEEP_ALIVE_EXTERNAL_APPS) {
                if (KEEP_ALIVE_EXTERNAL_APPS.size() <= 0) {
                    return;
                }
                for (Map.Entry<String, MSCApp> entry : KEEP_ALIVE_EXTERNAL_APPS.snapshot().entrySet()) {
                    destroyKeepAliveEngine(entry.getValue(), destroyReason);
                }
                KEEP_ALIVE_EXTERNAL_APPS.evictAll();
            }
        }

    }

    public static Map<String, MSCApp> getAllKeepAliveEngines() {
        Map<String, MSCApp> result = new HashMap<>(KEEP_ALIVE_APPS.snapshot());
        if (MSCHornRollbackConfig.enableExternalAppKeepAliveRule()) {
            result.putAll(KEEP_ALIVE_EXTERNAL_APPS.snapshot());
        }
        return result;
    }

    public static Map<Integer, MSCRuntime> getAllRunningEngines() {
        synchronized (ALL_RUNTIMES) {
            return new HashMap<>(ALL_RUNTIMES);
        }
    }

    @PackagePreLoadReporter.PackageLoadSourceFrom
    public static String getSourceFrom(MSCRuntime runtime) {
        if (runtime.getModule(IAppLoader.class).isLaunched()) {
            return PackagePreLoadReporter.SOURCE_FROM_TYPE_LAUNCH;
        }
        return PackagePreLoadReporter.SOURCE_FROM_TYPE_PRELOAD;
    }

    /**
     * 在指定的时间范围内，查找执行过预热操作的运行时的appId
     *
     * @param startTime
     * @param endTime
     * @return
     */
    @NonNull
    public static List<String> findPreloadAppIdWithinSpecifiedDuration(long startTime, long endTime, MSCRuntime excludeRuntime) {
        if (endTime <= startTime) {
            return Collections.emptyList();
        }
        List<String> preloadAppIdList;
        if (MSCHornRollbackConfig.get().getConfig().isRollbackGetRuntimeChange) {
            synchronized (ALL_RUNTIMES) {
                preloadAppIdList = realGetPreloadAppIdWithinSpecifiedDuration(startTime, endTime, excludeRuntime);
            }
        } else {
            preloadAppIdList = realGetPreloadAppIdWithinSpecifiedDuration(startTime, endTime, excludeRuntime);
        }
        return preloadAppIdList;
    }

    private static List<String> realGetPreloadAppIdWithinSpecifiedDuration(long startTime, long endTime, MSCRuntime excludeRuntime) {
        List<String> preloadAppIdList = new ArrayList<>();
        for (MSCRuntime runtime : ALL_RUNTIMES.values()) {
            if (runtime == excludeRuntime) {
                continue;
            }
            MSCRuntimeReporter runtimeReporter = runtime.getRuntimeReporter();
            if (runtimeReporter == null) {
                continue;
            }
            // 预热过程只要和指定的时间段有交叉就返回
            if (runtimeReporter.getPreloadEndTime() > startTime && runtimeReporter.getPreloadStartTime() < endTime) {
                preloadAppIdList.add(runtimeReporter.getPreloadAppId());
            }
        }
        return preloadAppIdList;
    }

    /**
     * 用于检查是否已存在对应业务的运行时，暂不去除锁
     *
     * @param appId appId
     * @return existUsableRuntime
     */
    public static boolean existUsableRuntime(String appId) {
        synchronized (ALL_RUNTIMES) {
            for (MSCRuntime runtime : ALL_RUNTIMES.values()) {
                IAppLoader appLoader = runtime.getModule(IAppLoader.class);
                if (appLoader != null && !appLoader.isUsable()) {
                    continue;
                }
                if (TextUtils.equals(runtime.getAppId(), appId)) {
                    return true;
                }
            }
        }
        return false;
    }

    /**
     * 全局仅允许创建一个基础库预热运行时
     *
     * @return runtime
     */
    @Nullable
    public static MSCRuntime createRuntimeIfNotExistBasePreload() {
        synchronized (ALL_RUNTIMES) {
            MSCRuntime basePreloadRuntime = findBasePreloadRuntime();
            if (basePreloadRuntime == null) {
                return createRuntime(null, null, false);
            }
            return null;
        }
    }

    public static void destroyRuntimesAndDeleteDDResource(DDResource ddResource) {
        if (MSCHornRollbackConfig.get().getConfig().isRollbackGetRuntimeChange) {
            synchronized (ALL_RUNTIMES) {
                // 销毁运行时
                realDestroyRuntimesAndDeleteDDResource(ddResource);
            }
        } else {
            realDestroyRuntimesAndDeleteDDResource(ddResource);
        }
    }

    private static void realDestroyRuntimesAndDeleteDDResource(DDResource ddResource) {
        // 销毁运行时
        destroyAllRuntime(RuntimeDestroyReason.BASE_PACKAGE_RESOURCE_INVALID);
        // 删除基础库缓存
        PackageLoadManager.getInstance().deleteDDResource(ddResource);
        // 上报已挂载页面且启动过的运行时个数
        reportUsingRuntimeCountWhenDDResourceInvalid();
    }

    private static void reportUsingRuntimeCountWhenDDResourceInvalid() {
        int usingRuntimeCount = 0;
        synchronized (ALL_RUNTIMES) {
            for (MSCRuntime runtime : ALL_RUNTIMES.values()) {
                if (runtime.hasContainerAttached() && runtime.getModule(IAppLoader.class).isLaunched()) {
                    usingRuntimeCount++;
                }
            }
        }
        PackageLoadReporter.CommonReporter.create()
                .reportUsingRuntimeCountWhenDDResourceInvalid(usingRuntimeCount);
    }

    private static void destroyAllRuntime(RuntimeDestroyReason destroyReason) {
        synchronized (ALL_RUNTIMES) {
            for (MSCRuntime runtime : ALL_RUNTIMES.values()) {
                destroyRuntime(runtime, destroyReason);
            }
        }
    }

    public static void addDestroyRuntimeReason(String appId, String reason) {
        destroyRuntimeReasonMap.put(appId, reason);
    }

    public static String getDestroyRuntimeReason(String appId) {
        return destroyRuntimeReasonMap.get(appId);
    }

    public static String getAndClearKeepAliveMissReason(String appId) {
        String reason = keepAliveMissReasonMap.get(appId);
        keepAliveMissReasonMap.remove(appId);
        return reason;
    }

    @NonNull
    public static List<String> getPreloadBizAppIds() {
        List<String> appIdList = new ArrayList<>();
        synchronized (ALL_RUNTIMES) {
            for (MSCRuntime runtime : ALL_RUNTIMES.values()) {
                if (runtime.isBizPreloadAndNoLaunched()) {
                    appIdList.add(runtime.getAppId());
                }
            }
        }
        MSCLog.i(TAG, "getPreloadBizAppIds", Arrays.toString(appIdList.toArray()));
        return appIdList;
    }

    /**
     * 获取业务预热中/预热完的所有运行时V8内存占用
     *
     * @return V8内存占用
     */
    public static long getAllPreloadRuntimeTotalMemorySize() {
        long jsMemoryKB = 0;
        synchronized (ALL_RUNTIMES) {
            for (MSCRuntime runtime : ALL_RUNTIMES.values()) {
                if (runtime.isBizPreloadAndNoLaunched()) {
                    jsMemoryKB += runtime.getLastJSMemoryUsageAfterPackageLoaded();
                }
            }
        }
        return jsMemoryKB;
    }

    public static void destroyRuntimesWhenBasePackageOffline(String name, String version) {
        if (MSCHornRollbackConfig.get().getConfig().isRollbackGetRuntimeChange) {
            synchronized (ALL_RUNTIMES) {
                for (MSCRuntime runtime : ALL_RUNTIMES.values()) {
                    String basePkgName = runtime.getMSCAppModule().getBasePkgName();
                    String basePkgVersion = runtime.getMSCAppModule().getBasePkgVersion();
                    if (TextUtils.equals(basePkgName, name) && TextUtils.equals(basePkgVersion, version)) {
                        destroyRuntime(runtime, RuntimeDestroyReason.BASE_PACKAGE_NEED_RELOAD);
                    }
                }
            }
        } else {
            asyncDestroyRuntimeByCondition(new Predicate<MSCRuntime>() {
                @Override
                public boolean test(MSCRuntime runtime) {
                    String basePkgName = runtime.getMSCAppModule().getBasePkgName();
                    String basePkgVersion = runtime.getMSCAppModule().getBasePkgVersion();
                    if (TextUtils.isEmpty(basePkgName) || TextUtils.isEmpty(basePkgVersion)) {
                        runtime.getMSCAppModule().setOfflineBaseFailReason(OFFLINE_BASE_FAILED_REASON_NO_BASE_PACKAGE_INFO);
                    }
                    return TextUtils.equals(basePkgName, name) && TextUtils.equals(basePkgVersion, version);
                }
            }, new Consumer<MSCRuntime>() {
                @Override
                public void accept(MSCRuntime runtime) {
                    if (runtime.hasContainerAttached()) {
                        MSCAppModule mscAppModule = runtime.getMSCAppModule();
                        if (mscAppModule != null) {
                            mscAppModule.setOfflineBaseFailReason(MSCAppModule.OFFLINE_FAILED_REASON_ATTACHED_PAGE);
                        }
                    }
                    destroyRuntime(runtime, RuntimeDestroyReason.BASE_PACKAGE_NEED_RELOAD);
                }
            });
        }
    }

    /**
     * 获取所有 DDResourceName
     *
     * @return
     */
    public static Set<String> getUsingBizResources() {
        Set<String> resourceSet = new HashSet<>();
        synchronized (ALL_RUNTIMES) {
            for (MSCRuntime runtime : ALL_RUNTIMES.values()) {
                MSCAppModule mscAppModule = runtime.getMSCAppModule();
                AppMetaInfoWrapper metaInfo = mscAppModule.getMetaInfo();
                if (null == metaInfo) {
                    continue;
                }
                PackageInfoWrapper packageInfoWrapper = metaInfo.getMainPackageCached();
                resourceSet.add(getPackageResourceName(packageInfoWrapper));
                List<PackageInfoWrapper> packageInfoWrappers = metaInfo.getSubPackagesCached();
                for (PackageInfoWrapper packageInfo : packageInfoWrappers) {
                    resourceSet.add(packageInfo.getDDResourceName());
                }
            }
        }
        return resourceSet;
    }

    private static String getPackageResourceName(PackageInfoWrapper packageInfoWrapper) {
        if (packageInfoWrapper != null) {
            return packageInfoWrapper.getDDResourceName();
        }
        return null;
    }

    public static Collection<MSCRuntime> getAllRuntimes() {
        return new ArrayList<>(ALL_RUNTIMES.values());
    }

    public static int getKeepAliveAppSize() {
        return KEEP_ALIVE_APPS.snapshot().size();
    }

    public static int getKeepAliveMaxSize() {
        return KEEP_ALIVE_MAX_SIZE;
    }

    public static void addLastEnterKeepAliveTime(String appId, long time) {
        lastEnterKeepAliveTimeMap.put(appId, time);
    }

    public static Long getLastEnterKeepAliveTime(String appId) {
        if (appId == null) {
            return null;
        }
        return lastEnterKeepAliveTimeMap.get(appId);
    }
}
