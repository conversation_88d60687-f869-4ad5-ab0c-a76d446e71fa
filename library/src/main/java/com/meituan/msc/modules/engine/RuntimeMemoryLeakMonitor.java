package com.meituan.msc.modules.engine;

import android.support.annotation.NonNull;

import com.meituan.msc.common.executor.MSCExecutors;
import com.meituan.msc.modules.reporter.MSCLog;

import java.lang.ref.WeakReference;
import java.util.Map;
import java.util.WeakHashMap;
import java.util.concurrent.ScheduledFuture;

public class RuntimeMemoryLeakMonitor {
    private static final String MSC_RUNTIME_LEAK = "MSCRuntimeLeak";
    private static final String MSC_RUNTIME_POSSIBLE_LEAK = "MSCRuntimePossibleLeak";
    /**
     * 垃圾回收后检测runtime是否泄漏，6min
     */
    private static final int CHECK_MSC_RUNTIME_MEMORY_LEAK_DELAY_IN_MS = 6 * 60 * 1000;
    private static final String TAG = "RuntimeMemoryLeakMonitor";
    /**
     * 未回收的引擎 当引擎正常回收或者确认泄漏后会被移除
     * <p>
     * MSCRuntime AppEntity
     * ScheduledFuture<?> 与 MSCRuntime 可能泄漏时关联的延时检测任务
     */
    private static final Map<MSCRuntime, ScheduledFuture<?>> runtimesNotRecycled = new WeakHashMap<>();


    private static boolean isMSCRuntimeLeakPossible(@NonNull MSCRuntime runtime) {
        MSCLog.d(TAG, "isMSCRuntimeLeakPossible: start");
        if (runtime == RuntimeManager.getLatestAttachToContainerRuntime()){
            return false;
        }
        if (runtime.isDestroyed) {
            return true;
        }
        if (runtime.getHistoryPageCount() <= 0) {
            return false;
        }
        return runtime.getTopActivity() == null;
    }

    public static void checkAllMSCRuntimeLeaks() {
        MSCLog.i(TAG, "checkAllMSCRuntimeLeaks: start");
        synchronized (runtimesNotRecycled) {
            for (Map.Entry<MSCRuntime, ScheduledFuture<?>> entry : runtimesNotRecycled.entrySet()) {
                MSCRuntime keyRuntime = entry.getKey();
                ScheduledFuture<?> task = entry.getValue();
                if (task != null) {
                    continue;
                }
                if (isMSCRuntimeLeakPossible(keyRuntime)) {
                    MSCLog.i(TAG, "checkAllMSCRuntimeLeaks: schedule check after 6min");
                    // 6min后进行检测
                    ScheduledFuture<?> pendingTask = MSCExecutors.Serialized.schedule(getRunnable(keyRuntime), CHECK_MSC_RUNTIME_MEMORY_LEAK_DELAY_IN_MS);
                    entry.setValue(pendingTask);
                }
            }
        }
    }

    private static Runnable getRunnable(MSCRuntime runtime) {
        final WeakReference<MSCRuntime> runtimeWeakReference = new WeakReference<>(runtime);
        return new Runnable() {
            @Override
            public void run() {
                MSCLog.i(TAG, "MSCRuntimeCheckAfterGC: start");
                MSCRuntime runtime = runtimeWeakReference.get();
                if (runtime != null) {
                    // runtime进入保活状态后被重新激活过
                    if (!isMSCRuntimeLeakPossible(runtime)){
                        synchronized (runtimesNotRecycled) {
                            runtimesNotRecycled.put(runtime, null);
                        }
                        return;
                    }
                    if (runtime.isDestroyed) {
                        MSCLog.i(TAG, "MSCRuntimeCheckAfterGC: MSC_RUNTIME_LEAK");
                        runtime.getRuntimeReporter().reportRuntimeLeak(MSC_RUNTIME_LEAK);
                    } else {
                        MSCLog.i(TAG, "MSCRuntimeCheckAfterGC: MSC_RUNTIME_POSSIBLE_LEAK");
                        runtime.getRuntimeReporter().reportRuntimeLeak(MSC_RUNTIME_POSSIBLE_LEAK);
                    }
                    synchronized (runtimesNotRecycled) {
                        runtimesNotRecycled.remove(runtime);
                    }
                }
            }
        };
    }

    public static void addRuntime(MSCRuntime runtime) {
        MSCLog.d(TAG, "add MSCRuntime");
        synchronized (runtimesNotRecycled) {
            if (!runtimesNotRecycled.containsKey(runtime)) {
                runtimesNotRecycled.put(runtime, null);
            }

        }
    }

    public static void refreshMonitorTimer(MSCRuntime runtime) {
        MSCLog.d(TAG, "cancel monitorTimer when reActive keepAliveEngine");
        synchronized (runtimesNotRecycled) {
            ScheduledFuture<?> task = runtimesNotRecycled.get(runtime);
            if (task != null) {
                task.cancel(true);
            }
            runtimesNotRecycled.put(runtime, null);
        }
    }
}