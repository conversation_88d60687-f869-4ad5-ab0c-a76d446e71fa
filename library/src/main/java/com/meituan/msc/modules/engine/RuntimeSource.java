package com.meituan.msc.modules.engine;

public enum RuntimeSource {
    BASE_PRELOAD,
    BIZ_PRELOAD,
    NEW,
    KEEP_ALIVE,
    COLD_START,
    UNKNOWN;

    public static String toReportString(RuntimeSource runtimeSource) {
        if (runtimeSource == null) {
            return "unknown";
        }
        switch (runtimeSource) {
            case NEW:
                return "new";
            case KEEP_ALIVE:
                return "keepAlive";
            case BASE_PRELOAD:
                return "basePreload";
            case BIZ_PRELOAD:
                return "bizPreload";
            case COLD_START:
                return "coldStart";
            default:
                return "unknown";
        }
    }
}
