package com.meituan.msc.modules.engine;

public enum RuntimeStateBeforeLaunch {

    /**
     * 业务预热中，且运行时来自新建
     */
    BIZ_PRELOADING_FROM_NEW,
    /**
     * 业务预热完成，且运行时来自新建
     */
    BIZ_PRELOAD_FROM_NEW,
    /**
     * 业务预热中，且运行时来自基础库预热
     */
    BIZ_PRELOADING_FROM_BASE,
    /**
     * 业务预热完成，且运行时来自基础库预热
     */
    BIZ_PRELOAD_FROM_BASE,
    /**
     * 业务预热被降级框架阻塞
     */
    BIZ_PRELOAD_IS_BLOCK,
    /**
     * 基础库预热中
     */
    BASE_PRELOADING,
    /**
     * 基础库预热完成
     */
    BASE_PRELOAD,
    /**
     * 新建
     */
    NEW,
    /**
     * 保活
     */
    KEEP_ALIVE,

    UNKNOWN;

    public static String toReportString(RuntimeStateBeforeLaunch runtimeSource) {
        if (runtimeSource == null) {
            return "unknown";
        }
        switch (runtimeSource) {
            case NEW:
                return "new";
            case KEEP_ALIVE:
                return "keepAlive";
            case BASE_PRELOAD:
                return "basePreload";
            case BASE_PRELOADING:
                return "basePreloading";
            case BIZ_PRELOADING_FROM_NEW:
                return "bizPreloadingFromNew";
            case BIZ_PRELOADING_FROM_BASE:
                return "bizPreloadingFromBase";
            case BIZ_PRELOAD_FROM_BASE:
                return "bizPreloadFromBase";
            case BIZ_PRELOAD_FROM_NEW:
                return "bizPreloadFromNew";
            default:
                return "unknown";
        }
    }
}
