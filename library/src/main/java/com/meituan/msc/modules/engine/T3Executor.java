package com.meituan.msc.modules.engine;

import com.meituan.msc.common.executor.MSCExecutors;
import com.meituan.msc.common.process.MSCProcess;
import com.meituan.msc.extern.MSCEnvHelper;
import com.meituan.msc.modules.container.ApplicationLifecycleMonitor;
import com.meituan.msc.modules.reporter.MSCLog;

import java.lang.ref.WeakReference;
import java.util.ArrayList;
import java.util.List;

/**
 * 用于让任务在美团首页T3后执行
 * 如不经T3启动，将立即执行
 */
public class T3Executor {
    private static volatile boolean t3Finished;
    private static final List<WeakReference<Runnable>> t3Runnables = new ArrayList<>();

    public static void onHomeT3Finished() {
        MSCLog.i("HeraTrace-T3Executor: on T3 finished");

        MSCExecutors.Serialized.submit(new Runnable() {
            @Override
            public void run() {
                t3Finished = true;

                if (!MSCEnvHelper.isInited()) {
                    MSCLog.e("T3Executor", "init not started, ignore T3 event");
                    return;
                }
                MSCEnvHelper.ensureFullInited();

                ApplicationLifecycleMonitor.register(MSCEnvHelper.getContext());
                runTasksIfNeeded();
            }
        });
    }

    private static void runTasksIfNeeded() {
        if (shouldRunT3Task()) {
            List<WeakReference<Runnable>> runnables;
            synchronized (t3Runnables) {
                runnables = new ArrayList<>(t3Runnables);
                t3Runnables.clear();
            }
            for (WeakReference<Runnable> runnableRef : runnables) {
                Runnable runnable = runnableRef.get();
                if (runnable != null) {
                    MSCExecutors.runOnUiThread(runnable);
                } else {
                    MSCLog.w("T3Executor", "weak referenced runnable already released");
                }
            }
        }
    }

    /**
     * 在T3是否已结束的基础上，添加不经T3启动判断
     */
    public static boolean shouldRunT3Task() {
        if (MSCProcess.isInMainProcess()) {
            if (ApplicationLifecycleMonitor.MSC.isFirstActivityCreated()) {
                return true;    // 不经T3启动
            } else {
                return t3Finished;
            }
        } else {
            return true;    // 非主进程收不到T3事件，也不需要为避免影响首页而做限制
        }
    }
}
