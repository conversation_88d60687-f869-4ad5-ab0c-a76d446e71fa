//package com.meituan.msc.modules.engine;
//
//import com.meituan.msc.modules.page.render.webview.WebViewCacheManager;
//import com.meituan.msc.modules.reporter.MSCLog;
//import com.meituan.msc.extern.MSCEnvHelper;
//import com.tencent.smtt.sdk.QbSdk;
//
//import java.util.concurrent.CountDownLatch;
//import java.util.concurrent.TimeUnit;
//
//public class X5Init {
//
//    private static final String TAG = "X5Init";
//
//    private static volatile boolean inited = false;
//    private static volatile boolean notNeeded = false;
//    private static final CountDownLatch mX5InitLatch = new CountDownLatch(1);
//
//    public static void initIfNeeded() {
//        if (isInited()) {
//            return;
//        }
//        MSCLog.d(TAG, "initX5IfNeed, notNeeded: ", notNeeded);
//
//        if (WebViewCacheManager.useX5()) {
//            QbSdk.PreInitCallback cb = new QbSdk.PreInitCallback() {
//                @Override
//                public void onViewInitFinished(boolean arg0) {
//                    MSCLog.d(TAG, "X5Init onViewInitFinished", arg0);
//                    inited = true;
//                    mX5InitLatch.countDown();
//                }
//
//                @Override
//                public void onCoreInitFinished() {
//                    MSCLog.d("X5Init", "onCoreInitFinished");
//                }
//            };
//            try {
//                //x5内核初始化接口
//                QbSdk.initX5Environment(MSCEnvHelper.getContext(), cb);
//            } catch (Throwable throwable) {
//                MSCLog.e("TAG", null, "x5 init ", throwable.getMessage());
//            }
//            MSCLog.d(TAG, "X5Init initX5Environment");
//        } else {
//            notNeeded = true;
//            mX5InitLatch.countDown();
//        }
//    }
//
//    public static boolean isInited() {
//        return inited || notNeeded;
//    }
//
//    public static void waitUntilInitFinish() {
//        if (isInited()) {
//            return;
//        }
//        try {
//            mX5InitLatch.await(1, TimeUnit.SECONDS);
//        } catch (InterruptedException e) {
//            MSCLog.e(e);
//        }
//    }
//}
