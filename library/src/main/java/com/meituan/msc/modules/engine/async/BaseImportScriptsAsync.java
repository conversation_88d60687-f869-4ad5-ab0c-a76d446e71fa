package com.meituan.msc.modules.engine.async;

import android.support.annotation.NonNull;
import android.support.annotation.Nullable;
import android.text.TextUtils;

import com.meituan.msc.common.ensure.InputOptions;
import com.meituan.msc.common.ensure.OutputResult;
import com.meituan.msc.common.ensure.ResFilesEnsure;
import com.meituan.msc.common.executor.MSCExecutors;
import com.meituan.msc.common.utils.JsonUtil;
import com.meituan.msc.modules.devtools.DebugHelper;
import com.meituan.msc.modules.engine.MSCHornRollbackConfig;
import com.meituan.msc.modules.engine.MSCRuntime;
import com.meituan.msc.modules.engine.ResultCallback;
import com.meituan.msc.modules.manager.IMSCCompletableCallback;
import com.meituan.msc.modules.reporter.MSCLog;
import com.meituan.msc.modules.service.MSCFileUtils;

import org.json.JSONArray;
import org.json.JSONException;
import org.json.JSONObject;

import java.util.concurrent.CountDownLatch;
import java.util.concurrent.atomic.AtomicInteger;

/**
 * 基础import
 */
public class BaseImportScriptsAsync implements IImportScriptsAsync {
    private static final String TAG = "BaseImportScriptsAsync";
    private static final String KEY_COMBO = "combo";

    protected final IBaseEngine mEngine;
    protected final MSCRuntime mMscRuntime;
    protected final IBaseEngineFunctionProvider mFunctionProvider;
    private final ResFilesEnsure mFileEnsure;

    public BaseImportScriptsAsync(@NonNull IBaseEngine engine, @NonNull MSCRuntime mscRuntime, @Nullable IBaseEngineFunctionProvider provider) {
        MSCLog.i(TAG, "#constructors:", engine, mscRuntime);
        mEngine = engine;
        mMscRuntime = mscRuntime;
        mFunctionProvider = provider;
        mFileEnsure = new ResFilesEnsure(mscRuntime.getMSCAppModule());
    }

    /**
     * 转换入参
     *
     * @param files
     * @param params
     * @param success
     * @param fail
     * @return
     */
    private InputOptions convertToOpts(JSONArray files, String params, IMSCCompletableCallback<Void> success, IMSCCompletableCallback<JSONObject> fail) {
        InputOptions opts = new InputOptions();
        opts.fileUris = JsonUtil.parseToStringArray(files);
        opts.originParams = params;
        opts.success = success;
        opts.fail = fail;
        try {
            JSONObject paramsObject = new JSONObject(params);
            boolean enableComboFeature = !MSCHornRollbackConfig.get().getConfig().isRollbackImportScriptSupportCombo;
            opts.combo = enableComboFeature && paramsObject.optBoolean(KEY_COMBO, false);
        } catch (JSONException e) {
            MSCLog.i(TAG, "#convertToOpts,", e.getMessage(), ",originParams=", params);
        }

        return opts;
    }

    /**
     * 返回空表示不需要。
     *
     * @param errMsg
     * @return
     */
    private JSONObject createErrMsg(String errMsg) {
        if (TextUtils.isEmpty(errMsg)) {
            return null;
        }
        JSONObject jsonObject = new JSONObject();
        try {
            jsonObject.put("errMsg", errMsg);
        } catch (JSONException e) {
            MSCLog.i(TAG, "#createErrMsg,=", e.getMessage());
        }
        return jsonObject;
    }

    @Override
    public void importScriptsAsync(JSONArray files, String params,
                                   final @Nullable IMSCCompletableCallback<Void> success,
                                   final @Nullable IMSCCompletableCallback<JSONObject> fail) {
        MSCLog.i(TAG, "#importScriptsAsync,files=", files);
        final Runnable runnable = new Runnable() {
            @Override
            public void run() {
                MSCLog.i(TAG, "#importScriptsAsync,sub thread work start. files=", files);
                // 返回数组至少有一个值
                InputOptions inputOptions = convertToOpts(files, params, success, fail);
                OutputResult outputResult = new OutputResult(success);
                if (mMscRuntime.isDestroyed) {
                    // runtime销毁，直接返回。
                    outputResult.loadResult = "runtime destroyed.";
                    outputResult.callback = fail;
                    notifyResultToService(outputResult);
                    return;
                }
                // check input.
                if (inputOptions.fileUris.length == 1 && inputOptions.fileUris[0] == null) {
                    outputResult.loadResult = "input files path array is null";
                    outputResult.callback = fail;
                    notifyResultToService(outputResult);
                    return;
                }
                // v1版本本地文件。文件都ready了。
                // v2版本需要先保证文件ready才进入下一步。
                ensureFilesReady(mMscRuntime.getMSCAppModule().enableAsyncSubPkg(), inputOptions, outputResult);
                if (!TextUtils.isEmpty(outputResult.loadResult)) {
                    outputResult.callback = fail;
                    notifyResultToService(outputResult);
                    return;
                }
                doImportAsync(inputOptions, outputResult);
            }
        };
        submit(runnable);
    }

    /**
     * 子类可以覆盖该方法，用来实现其他的加载逻辑
     *
     * @param inputOptions 参数
     * @param outputResult 结果
     */
    protected void doImportAsync(final InputOptions inputOptions,
                                 final OutputResult outputResult) {
        final String[] fileUris = inputOptions.fileUris;
        final boolean useCombo = inputOptions.combo && !DebugHelper.debugWebView;
        final AtomicInteger count = new AtomicInteger(useCombo ? 1 : fileUris.length);
        final CountDownLatch launch = new CountDownLatch(1);
        final ResultCallback injectResult = new ResultCallback() {
            @Override
            public void onReceiveFailValue(Exception e) {
                MSCLog.i(TAG, "#onReceiveFailValue,", e.getMessage());
                outputResult.loadResult = e.getMessage();
                outputResult.loadError = e;
                outputResult.callback = inputOptions.fail;
                count.set(-1);
                checkNotify(launch, count);
            }

            @Override
            public void onReceiveValue(String value) {
                MSCLog.i(TAG, "#onReceiveValue,", String.valueOf(value));
                count.decrementAndGet();
                checkNotify(launch, count);
            }
        };
        try {
            // debugService时需要通过AppPage.APPEND_SCRIPT加载js文件，以便调试时能找到文件，不使用combo
            if (useCombo) {
                // 支持combo，多个文件一次注入
                MSCFileUtils.importScriptsAsyncWithComboSafe(fileUris, mMscRuntime, mEngine, injectResult);
            } else {
                // 不支持combo，则每次注入一个
                for (String file : fileUris) {
                    MSCFileUtils.importScriptsAsyncWithComboSafe(new String[]{file}, mMscRuntime, mEngine, injectResult);
                }
            }
        } catch (Exception e) {
            MSCLog.e(TAG, e, "Import_Script_With_Combo_Failed", fileUris, inputOptions.combo);
            outputResult.callback = inputOptions.fail;
            outputResult.loadResult = e.getMessage();
            outputResult.loadError = e;
            mMscRuntime.getNativeExceptionHandler().handleException(e);
            notifyResultToService(outputResult);
            // 出现异常直接结束。
            return;
        }
        try {
            launch.await();
        } catch (InterruptedException e) {
            // 超时打断。忽略。
            MSCLog.e(TAG, e,"#doImportAsync");
        }
        // 通知前端任务完成。
        notifyResultToService(outputResult);
    }

    private void ensureFilesReady(boolean enableAsyncSubPkg, @NonNull InputOptions inputOptions, @NonNull OutputResult result) {
        mFileEnsure.ensure(enableAsyncSubPkg, inputOptions, result);
    }


    /**
     * 提交io任务到子线程。
     *
     * @param runnable
     */
    protected void submit(Runnable runnable) {
        MSCExecutors.submit(new MSCExecutors.PrintExceptionRunnable(runnable));
    }

    /**
     * 将结果返回给前端。内部已进行线程切换。
     */
    protected void notifyResultToService(@NonNull final OutputResult outputResult) {
        final String errorMsg = outputResult.loadResult;
        final IMSCCompletableCallback callback = outputResult.callback;
        if (mMscRuntime.isDestroyed) {
            MSCLog.i(TAG, "#notifyResultToService, msc runtime is destroyed. return.");
            return;
        }
        if (null == this.mEngine) {
            MSCLog.i(TAG, "#notifyResultToService, engine is null. callback canceled. return.");
            return;
        }
        if (null == callback) {
            MSCLog.i(TAG, "#notifyResultToService, callback is null. return.");
            return;
        }
        // 切换到js线程，执行回调
        this.mEngine.runOnJSQueueThreadSafe(new Runnable() {
            @Override
            public void run() {
                callback.onComplete(createErrMsg(errorMsg));
            }
        });
    }

    protected void checkNotify(@NonNull CountDownLatch latch, @NonNull AtomicInteger count) {
        if (count.get() <= 0 && latch.getCount() > 0) {
            // 数据都返回 && 还在await。
            latch.countDown();
        }
    }

}
