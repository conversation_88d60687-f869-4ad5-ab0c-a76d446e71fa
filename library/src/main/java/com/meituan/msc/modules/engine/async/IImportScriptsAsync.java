package com.meituan.msc.modules.engine.async;

import android.support.annotation.Nullable;

import com.meituan.msc.modules.manager.IMSCCompletableCallback;

import org.json.JSONArray;
import org.json.JSONObject;

/**
 * 异步加载js接口
 */
public interface IImportScriptsAsync {

    void importScriptsAsync(JSONArray files,
                            String params,
                            @Nullable IMSCCompletableCallback<Void> success,
                            @Nullable IMSCCompletableCallback<JSONObject> fail);
}
