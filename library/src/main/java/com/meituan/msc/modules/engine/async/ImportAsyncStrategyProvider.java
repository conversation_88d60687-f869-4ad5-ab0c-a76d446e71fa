package com.meituan.msc.modules.engine.async;

import android.support.annotation.NonNull;

import com.meituan.msc.modules.engine.MSCRuntime;

/**
 * 异步引入script文件的策略提供类。
 */
public class ImportAsyncStrategyProvider {

    @NonNull
    public static IImportScriptsAsync getImportScriptsAsyncStrategy(IBaseEngine engine, MSCRuntime mscRuntime, IBaseEngineFunctionProvider provider) {
        return new BaseImportScriptsAsync(engine, mscRuntime, provider);
    }
}
