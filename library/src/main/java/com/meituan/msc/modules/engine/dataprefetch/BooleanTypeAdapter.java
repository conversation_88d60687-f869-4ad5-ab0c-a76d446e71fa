package com.meituan.msc.modules.engine.dataprefetch;

import com.google.gson.TypeAdapter;
import com.google.gson.stream.JsonReader;
import com.google.gson.stream.JsonWriter;

import java.io.IOException;

public class <PERSON>oleanTypeAdapter extends TypeAdapter<Boolean> {
    @Override
    public void write(JsonWriter out, Boolean value) throws IOException {
        out.value(value);
    }

    @Override
    public Boolean read(<PERSON><PERSON><PERSON><PERSON><PERSON> in) throws IOException {
        switch (in.peek()) {
            case BOOLEAN:
                return in.nextBoolean();
            default:
                throw new IllegalStateException("Expected BOOLEAN, but was " + in.peek());
        }
    }
}
