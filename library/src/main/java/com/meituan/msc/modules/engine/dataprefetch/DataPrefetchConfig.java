package com.meituan.msc.modules.engine.dataprefetch;

import android.support.annotation.Keep;

import com.google.gson.JsonElement;
import com.google.gson.JsonObject;
import com.google.gson.JsonPrimitive;
import com.google.gson.annotations.SerializedName;
import com.meituan.msc.lib.interfaces.prefetch.PrefetchURLConfig;

import java.util.List;
import java.util.Map;

/**
 * 数据预拉取配置信息
 */
@Keep
public class DataPrefetchConfig {
    public Map<String, Map<String, PrefetchURLConfig>> pageConfigs;
    public PrefetchSharedConfig sharedConfigs;
    public List<String> valueParsers;

    @Keep
    public static class PrefetchSharedConfig {
        public LocationConfig location;
        public RequestConfig request;
        public List<String> colorTags;
    }

    @Keep
    public static class LocationConfig {
        public String sceneToken;
        public String type;
        public Long cacheLocationValidTime;
    }

    @Keep
    public static class RequestConfig {
        public Long timeout;
        public boolean enableShark = true;
        public boolean enableSecuritySiua = false;
        public boolean enableSecuritySign = false;
    }
}

