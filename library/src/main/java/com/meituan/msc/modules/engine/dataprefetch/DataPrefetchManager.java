package com.meituan.msc.modules.engine.dataprefetch;

import android.net.Uri;
import android.support.annotation.Keep;
import android.support.annotation.NonNull;
import android.text.TextUtils;

import com.google.gson.Gson;
import com.google.gson.GsonBuilder;
import com.google.gson.JsonArray;
import com.google.gson.JsonElement;
import com.google.gson.JsonObject;
import com.google.gson.JsonPrimitive;
import com.google.gson.reflect.TypeToken;
import com.meituan.msc.common.utils.MSCTraceUtil;
import com.meituan.msc.lib.interfaces.prefetch.MSCBaseValueParser;
import com.meituan.msc.lib.interfaces.prefetch.PrefetchURLConfig;
import com.meituan.msc.modules.engine.dataprefetch.msi.MSIApiRequest;
import com.meituan.msc.modules.engine.dataprefetch.msi.MSIApiResponse;
import com.meituan.msc.modules.engine.dataprefetch.msi.MSINetRequestParam;
import com.meituan.msc.modules.msi.MSIManagerModule;
import com.meituan.msc.modules.reporter.MSCLog;
import com.meituan.msi.ApiPortal;
import com.meituan.msi.api.ApiCallback;
import com.meituan.msi.bean.StringRequestData;
import com.sankuai.android.jarvis.Jarvis;

import java.lang.reflect.Type;
import java.util.HashMap;
import java.util.Iterator;
import java.util.List;
import java.util.Map;
import java.util.Random;
import java.util.Set;
import java.util.concurrent.ExecutorService;


public class DataPrefetchManager {
    ApiPortal apiPortal;
    private Random random = new Random();

    private static final Gson gson = new GsonBuilder().registerTypeAdapter(Boolean.TYPE, new BooleanTypeAdapter()).create();

    public static String toJson(Object src) {
        return gson.toJson(src);
    }

    public static <T> T fromJson(String json, Type typeOfT) {
        return gson.fromJson(json, typeOfT);
    }

    //参数解析比较耗时，新线程处理每一个request
    public static ExecutorService executorService = Jarvis.newScheduledThreadPool("msc-dynamic-data-prefetch-request", 1);

    public DataPrefetchManager(@NonNull MSIManagerModule msiManagerModule) {
        apiPortal = msiManagerModule.getApiPortal();
    }

    public void sendDataPrefetchRequest(String baseUrl, PrefetchURLConfig prefetchURLConfig,
                                        DataPrefetchConfig.PrefetchSharedConfig sharedConfigs, List<MSCBaseValueParser> parserList, IPrefetchRequestCallback callback) {
        executorService.submit(new Runnable() {
            @Override
            public void run() {
                MSCTraceUtil.instant("sendDataPrefetchRequest");
                sendDataPrefetchRequestInner(baseUrl, prefetchURLConfig, sharedConfigs, parserList, callback);
            }
        });
    }

    public void sendDataPrefetchRequestInner(String baseUrl, PrefetchURLConfig prefetchURLConfig,
                                                    DataPrefetchConfig.PrefetchSharedConfig sharedConfigs, List<MSCBaseValueParser> parserList, IPrefetchRequestCallback callback){
        try {
            if (callback != null) {
                callback.onStartValueParser();
            }

            //url处理
            MSCTraceUtil.begin("ParserDataPrefetch" + baseUrl);
            String path = resolveUrlPath(baseUrl, parserList, prefetchURLConfig);
            Uri uri = Uri.parse(path);
            MSCLog.i("MSCDynamicDataPrefetch", " Start  resolveUrlQuery");
            Map<String, String> queryMap = resolveUrlQuery(uri, parserList ,prefetchURLConfig);
            MSCLog.i("MSCDynamicDataPrefetch", " Start  resolveUrlPath");
            Uri.Builder builder = uri.buildUpon();
            builder.clearQuery();
            //返回解析出来的url，同步调用桥使用解析后的url做匹配
            if (callback != null) {
                String dynamicUrlWithoutQuery = builder.build().toString();
                callback.onPathParserFinish(baseUrl, dynamicUrlWithoutQuery);
            }

            if (queryMap != null && queryMap.size() > 0) {
                for (String key : queryMap.keySet()) {
                    String queryValue = queryMap.get(key);
                    if (!TextUtils.isEmpty(queryValue)) {
                        builder.appendQueryParameter(key, queryValue);
                    }
                }
            }
            String dynamicUrl = builder.build().toString();
            //header处理
            MSCLog.i("MSCDynamicDataPrefetch", " Start  resolveHeader");
            Map<String, String> header = resolveHeader(prefetchURLConfig.header, parserList, prefetchURLConfig);

            //data处理
            MSCLog.i("MSCDynamicDataPrefetch", " Start  resolveData");
            JsonElement dataJsonObject = resolveData(prefetchURLConfig.data, parserList, prefetchURLConfig);
            MSCTraceUtil.end("ParserDataPrefetch" + baseUrl);

            Map<String, Object> _mtMap = new HashMap<>();
            if (prefetchURLConfig.colorTags != null) {
                _mtMap.put("colorTags", prefetchURLConfig.colorTags);
            } else if (sharedConfigs != null && sharedConfigs.colorTags != null) {
                _mtMap.put("colorTags", sharedConfigs.colorTags);
            }
            //创建request请求体
            MSCTraceUtil.begin("DataPrefetchMsiRequestBuild" + baseUrl);
            MSINetRequestParam msiRequestApiParam = new MSINetRequestParam();
            msiRequestApiParam.url = dynamicUrl;
            msiRequestApiParam.data = dataJsonObject;
            msiRequestApiParam.header = header;
            msiRequestApiParam.method = prefetchURLConfig.method;
            if (!_mtMap.isEmpty()) {
                msiRequestApiParam._mt = _mtMap;
            }
            //超时时间，优先request中配置，如果未配置，查看公共超时配置
            DataPrefetchConfig.RequestConfig requestConfig = sharedConfigs != null ? sharedConfigs.request: null;
            Long timeOut = prefetchURLConfig.timeout;
            if (timeOut == null && requestConfig != null){
                timeOut = requestConfig.timeout;
            }
            if (timeOut != null && timeOut.intValue() > 0) {
                msiRequestApiParam.timeout = timeOut.intValue();
            }

            //enableShark
            if (prefetchURLConfig.enableShark != null) {
                msiRequestApiParam.enableShark = prefetchURLConfig.enableShark;
            } else if (requestConfig!= null) {
                msiRequestApiParam.enableShark = requestConfig.enableShark;
            }

            //enableSecuritySign
            if (prefetchURLConfig.enableSecuritySign != null) {
                msiRequestApiParam.mtSecuritySign = prefetchURLConfig.enableSecuritySign;
            } else if (requestConfig!= null) {
                msiRequestApiParam.mtSecuritySign = requestConfig.enableSecuritySign;
            }

            //mtSecuritySiua
            if (prefetchURLConfig.enableSecuritySiua != null) {
                msiRequestApiParam.mtSecuritySiua = prefetchURLConfig.enableSecuritySiua;
            } else if (requestConfig!= null) {
                msiRequestApiParam.mtSecuritySiua = requestConfig.enableSecuritySiua;
            }

            MSCLog.i("MSCDynamicDataPrefetch", " Start  sendMsiRequest");
            sendMsiRequest(msiRequestApiParam, baseUrl, callback);

        } catch (Exception e) {
            if (callback != null) {
                callback.onFail(MSCPrefetchRequestReporter.ERROR_CODE_PREFETCH_PARSE_URL_FAIL, e.getMessage());
            }
        }
    }

    @Keep
    private String resolveUrlPath(String urlPath, List<MSCBaseValueParser> parserList, PrefetchURLConfig urlConfig){
        if (TextUtils.isEmpty(urlPath)){
            return urlPath;
        }

        String newUrlPath = urlPath;
        String prePath = "";
        int startPos = urlPath.indexOf("//");
        if (startPos > 0) {
            newUrlPath = urlPath.substring(startPos + 2);
            prePath = urlPath.substring(0, startPos + 2);
        }

        String[] pathFields = newUrlPath.split("/");
        if (pathFields != null && pathFields.length > 0){
            StringBuilder stringBuilder = new StringBuilder();
            if (!TextUtils.isEmpty(prePath)) {
                stringBuilder.append(prePath);
            }
            int addCount = 0;
            for(int i=0; i<pathFields.length; i++){
                //path中不支持可选
                if (!DynamicParser.validDynamicPathParam(pathFields[i])) {
                    throw new MSCDynamicParserException("parser param failed, path can not use option :" + pathFields[i]);
                }
                String dynamicRes = DynamicParser.parserForString(pathFields[i], parserList, urlConfig);
                if (TextUtils.isEmpty(dynamicRes)){
                    continue;
                }
                if (addCount > 0) {
                    stringBuilder.append("/");
                }
                stringBuilder.append(dynamicRes);
                addCount++;
            }

            return stringBuilder.toString();
        }

        return urlPath;
    }


    @Keep
    private Map<String, String> resolveUrlQuery(@NonNull Uri requestUri, List<MSCBaseValueParser> parserList, PrefetchURLConfig prefetchURLConfig) throws MSCDynamicParserException{
        Map<String, String> queryMap = new HashMap<>();
        Set<String> querySet = requestUri.getQueryParameterNames();
        if (querySet != null && querySet.size() > 0){
            queryMap = new HashMap<>();
            for (String key: querySet){
                String value = DynamicParser.parserForString(key, parserList, prefetchURLConfig);
                if (!TextUtils.isEmpty(value)) {
                    queryMap.put(key, value);
                }
            }
        }

        if (prefetchURLConfig != null && prefetchURLConfig.query != null && prefetchURLConfig.query.size() > 0) {
            queryMap = new HashMap<>();
            for (Map.Entry<String, String> entry: prefetchURLConfig.query.entrySet()) {
                String value = DynamicParser.parserForString(entry.getValue(), parserList, prefetchURLConfig);
                if (!TextUtils.isEmpty(value)) {
                    queryMap.put(entry.getKey(), value);
                }
            }
        }

        return queryMap;
    }

    private Map<String, String> resolveHeader(Map<String, String> header, List<MSCBaseValueParser> parserList, PrefetchURLConfig urlConfig){
        if (header == null || header.size() == 0){
            return null;
        }

        Map<String, String> newHeader = new HashMap<>();
        for (Map.Entry<String, String> entry : header.entrySet()) {
            String key = entry.getKey();
            String value = DynamicParser.parserForString(entry.getValue(), parserList, urlConfig);
            if (!TextUtils.isEmpty(value)){
                newHeader.put(key, value);
            }
        }

        return newHeader;
    }

    private JsonElement resolveData(JsonElement data, List<MSCBaseValueParser> parserList, PrefetchURLConfig urlConfig){
        if (data == null){
            return null;
        }

        if (data instanceof JsonObject){
            JsonObject newData = null;
            Set<Map.Entry<String, JsonElement>> entrySet = ((JsonObject)data).entrySet();
            if (entrySet != null && entrySet.size() > 0){
                newData = new JsonObject();
                for(Map.Entry<String, JsonElement> entry: entrySet){
                    JsonElement newElement = resolveData(entry.getValue(), parserList, urlConfig);
                    if (newElement != null) {
                        newData.add(entry.getKey(), newElement);
                    }
                }
            }
            return newData;
        } else if (data instanceof JsonArray){
            JsonArray jsonArray = (JsonArray)data;
            JsonArray newJsonArray = new JsonArray();
            Iterator<JsonElement> iterator = jsonArray.iterator();
            while (iterator.hasNext()){
                JsonElement newElement = resolveData(iterator.next(), parserList, urlConfig);
                if (newElement != null){
                    newJsonArray.add(newElement);
                }
            }

            return newJsonArray;
        } else if (data.isJsonPrimitive()){
            if (((JsonPrimitive)data).isString()){
                String value = data.getAsString();
                Object dynamicValue = DynamicParser.parser(value, parserList, urlConfig);
                if (dynamicValue == null){
                    return null;
                }
                if (dynamicValue instanceof Double){
                    return new JsonPrimitive(((Double)dynamicValue).doubleValue());
                } else if (dynamicValue instanceof Integer){
                    return new JsonPrimitive(((Integer)dynamicValue).intValue());
                } else if (dynamicValue instanceof Boolean) {
                    return new JsonPrimitive((Boolean)dynamicValue);
                } else if (dynamicValue instanceof String){
                    return new JsonPrimitive((String) dynamicValue);
                }

                JsonPrimitive jsonPrimitive = new JsonPrimitive(dynamicValue.toString());
                return jsonPrimitive;
            }
        }

        //重新创建新节点，不影响原来的配置
        return data.deepCopy();
    }

    private JsonObject parserMsiResponseBody(Object data){
        MSIApiResponse<JsonObject> apiResponse = null;
        if (data instanceof String){
            try {
                apiResponse = fromJson((String) data, new TypeToken<MSIApiResponse<JsonObject>>(){}.getType());
            } catch (Exception e){
            }
        }

        if (apiResponse != null){
            return apiResponse.responseBody;
        }

        return null;
    }

    private MSIApiResponse parserMsiResponse(Object data) {
        MSIApiResponse<JsonObject> apiResponse = null;
        if (data instanceof String){
            try {
                apiResponse = fromJson((String) data, new TypeToken<MSIApiResponse<Object>>(){}.getType());
            } catch (Exception e){
                e.printStackTrace();
            }
        }

        return apiResponse;
    }

    public void invokeAsync(String data, ApiCallback apiCallback){
        StringRequestData stringRequestData = new StringRequestData.Builder()
                .nativeStartTime(System.currentTimeMillis())
                .requestData(data)
                .build();

        apiPortal.invokeAsync(stringRequestData, apiCallback);
    }

    public void sendMsiRequest(MSINetRequestParam requestParam, String configUrl, IPrefetchRequestCallback callback) {
        MSIApiRequest msiApiRequestNode = MSIApiRequest.create("request", "default", requestParam);
        int taskId = random.nextInt();
        JsonObject innerArgs = new JsonObject();
        innerArgs.addProperty("taskId", taskId);
        msiApiRequestNode.setInnerArgs(innerArgs);

        String requestData = toJson(msiApiRequestNode);
        MSCLog.i("[PrefetchMsiModule@msiRequest]", "RequestData " + requestData);
        if (callback != null) {
            callback.onMsiRequestBuildFinish(requestParam);
        }
        MSCTraceUtil.end("DataPrefetchMsiRequestBuild" + configUrl);
        MSCTraceUtil.begin("DataPrefetchStartMsiRequest" + configUrl);
        invokeAsync(requestData, new ApiCallback() {
            @Override
            public void onSuccess(Object data) {
                MSCTraceUtil.end("DataPrefetchStartMsiRequest" + configUrl);
                MSCLog.i("[PrefetchMsiModule@msiRequest]", " Success: " + data.toString());
                JsonObject jsonObject = parserMsiResponseBody(data);
                if (callback != null){
                    callback.onSuccess(jsonObject);
                }
            }

            @Override
            public void onFail(Object error) {
                MSCLog.i("[PrefetchMsiModule@msiRequest]", " Failed: " + error.toString());
                MSCTraceUtil.end("DataPrefetchStartMsiRequest" + configUrl);
                MSIApiResponse apiResponse = parserMsiResponse(error.toString());
                if (callback != null){
                    if (apiResponse != null){
                        callback.onFail(MSCPrefetchRequestReporter.ERROR_CODE_PREFETCH_MSI_REQUEST_FAIL, apiResponse.statusMsg);
                    } else {
                        callback.onFail(MSCPrefetchRequestReporter.ERROR_CODE_PREFETCH_MSI_REQUEST_FAIL, error.toString());
                    }
                }
            }
        });
    }
}
