package com.meituan.msc.modules.engine.dataprefetch;

import android.text.TextUtils;

import com.meituan.msc.lib.interfaces.prefetch.MSCBaseValueParser;
import com.meituan.msc.lib.interfaces.prefetch.PrefetchURLConfig;

import java.util.List;

/**
 * 动态参数解析
 */
public class DynamicParser {
    public static String parserForString(String input, List<MSCBaseValueParser> parserList, PrefetchURLConfig urlConfig) throws MSCDynamicParserException {
        Object value = parser(input, parserList, urlConfig);
        if (value == null) {
            return null;
        }

        if (value instanceof Double || value instanceof Integer || value instanceof Boolean) {
            return String.valueOf(value);
        } else if (value instanceof String) {
            return (String) value;
        }

        return value.toString();
    }

    public static Object parser(String input, List<MSCBaseValueParser> parserList, PrefetchURLConfig urlConfig) throws MSCDynamicParserException {
        if (TextUtils.isEmpty(input)) {
            return input;
        }

        if (!input.startsWith("{{") || !input.endsWith("}}")) {
            return input;
        }

        input = input.substring(2, input.length() - 2);
        int length = input.length();
        DynamicValue dynamicValue = new DynamicValue();
        //默认值
        int defaultValPos = input.indexOf("??");
        String conditions;
        if (defaultValPos != -1) {
            //默认值
            if (length > defaultValPos + 2) {
                dynamicValue.defaultValue = input.substring(defaultValPos + 2, length);
            } else {
                dynamicValue.defaultValue = "";
            }

            conditions = input.substring(0, defaultValPos);
        } else {
            conditions = input;
        }

        //默认值不存在，判断是否可选。最后一个字符为？
        if (dynamicValue.defaultValue == null && input.endsWith("?")) {
            dynamicValue.optional = true;
            conditions = input.substring(0, length - 1);
        }

        //除了尾部，key中不能包含？
        if (conditions.indexOf("?") != -1) {
            throw new MSCDynamicParserException("parser param failed, condition key has ？ :" + input);
        }

        //按|| 获取 condition
        String[] conditionItems = conditions.split("\\|\\|");

        //解析器获取condition的值
        Object paramValue = null;
        if (conditionItems != null && conditionItems.length > 0) {
            for (String itemValue : conditionItems) {
                paramValue = getValueFromParser(itemValue, urlConfig, parserList);
                if (paramValue != null) { //获取到值，返回
                    break;
                }
            }
        }

        //获取不到值时，设置默认值
        if (paramValue == null) {
            paramValue = dynamicValue.defaultValue;
        }

        //返回解析得到的值，如果解析失败，且不可选，抛出异常
        if (paramValue == null && !dynamicValue.optional) {
            throw new MSCDynamicParserException("parser param failed: " + input);
        }

        return paramValue;
    }

    private static Object getValueFromParser(String param, PrefetchURLConfig urlConfig, List<MSCBaseValueParser> parserList) {
        if (parserList == null || parserList.size() == 0) {
            return null;
        }

        //list顺序 解析参数
        for (MSCBaseValueParser valueParser : parserList) {
            if (valueParser != null && valueParser.isSupport(param)) {
                Object value = valueParser.getValue(param, urlConfig);
                if (value != null) {
                    return value;
                }
                return valueParser.getValue(param);
            }
        }

        return null;
    }

    public static boolean validDynamicPathParam(String param) {
        if (TextUtils.isEmpty(param)) {
            return true;
        }

        if (!param.startsWith("{{") || !param.endsWith("}}")) {
            return true;
        }

        String newParam = param.substring(2, param.length() - 2);
        //默认值
        int defaultValPos = newParam.indexOf("??");

        //不存在默认值。path路径不支持可选
        if (defaultValPos == -1 && newParam.endsWith("?")) {
            return false;
        }

        return true;
    }
}
