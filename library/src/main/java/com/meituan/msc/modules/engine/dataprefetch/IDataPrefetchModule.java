package com.meituan.msc.modules.engine.dataprefetch;

import org.json.JSONArray;

import java.util.List;
import java.util.Map;


public interface IDataPrefetchModule {
    void startDataPrefetch(String targetPath, int routeId, long routeTime, boolean isWidget);
    void attachToPage(int routeId, int pageId);
    void onPageDestroy(int pageId);
    void getConfigPackage();
    void loadDataPrefetchConfigFromDio();
    Map<String, MSCPrefetchPhaseRecord>  getPrefetchPhaseRecordOfUrl(int routeId);
    /**
     * 获取通用预拉取record，包括配置包获取、配置解析
     * @param routeId
     * @return
     */
    MSCPrefetchPhaseRecord getCommonPrefetchRecord(int routeId);
    JSONArray getPrefetchDataSync(String pageId, String[] urls);
}
