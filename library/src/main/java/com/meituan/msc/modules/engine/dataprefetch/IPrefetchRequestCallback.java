package com.meituan.msc.modules.engine.dataprefetch;

import com.google.gson.JsonObject;
import com.meituan.msc.modules.engine.dataprefetch.msi.MSINetRequestParam;

public interface IPrefetchRequestCallback {
    void onSuccess(JsonObject data);
    void onFail(int errorCode, String errorMessage);
    //开始解析动态参数
    void onStartValueParser();
    //解析完path
    void onPathParserFinish(String configUrl, String urlWithoutQuery);
    //msi request 构建完成，开始发起request请求
    void onMsiRequestBuildFinish(MSINetRequestParam requestParam);
}
