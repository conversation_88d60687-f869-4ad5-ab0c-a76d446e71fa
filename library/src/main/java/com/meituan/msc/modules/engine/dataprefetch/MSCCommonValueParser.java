package com.meituan.msc.modules.engine.dataprefetch;

import android.net.Uri;
import android.os.Build;
import android.text.TextUtils;

import com.meituan.msc.lib.interfaces.prefetch.MSCBaseValueParser;
import com.meituan.msc.extern.IMSCUserCenter;
import com.meituan.msc.extern.MSCEnvHelper;
import com.meituan.msc.modules.update.bean.AppMetaInfoWrapper;

/**
 * MSC 数据预拉取通用参数解析器
 */
public class MSCCommonValueParser extends MSCBaseValueParser {
    //小程序相关信息
    //小程序id
    private static final String SUPPORT_PARAM_APP_ID = "app.id";
    //小程序版本号
    private static final String SUPPORT_PARAM_APP_VERSION = "app.version";
    //小程序唯一id，小程序生命周期内不会发生改变
    private static final String SUPPORT_PARAM_APP_UUID = "app.uuid";
    //打开小程序的路径。不包含query
    private static final String SUPPORT_PARAM_PATH = "path";
    //打开小程序的 query。
    private static final String SUPPORT_PARAM_QUERY = "query";

    //账号相关信息
    //已登录用户的 Token
    private static final String SUPPORT_PARAM_USER_TOKEN = "user.token";
    //已登录用户的 id
    private static final String SUPPORT_PARAM_USER_ID = "user.id";

    //设备相关信息
    //系统名称 iOS/Android/harmony
    private static final String SUPPORT_PARAM_OS_NAME = "os.name";
    //系统版本
    private static final String SUPPORT_PARAM_OS_VERSION = "os.version";
    //设备型号，如 ALN-AL00
    private static final String SUPPORT_PARAM_DEVICE_PRODUCT_MODEL = "device.productModel";
    //设备 uuid，如 0000000000000325FEB2CAA6A41D3899F712D981F1DDAC171863838667884060
    private static final String SUPPORT_PARAM_UUID = "uuid";

    //宿主相关信息
    //宿主名称 https://km.sankuai.com/page/366947792?from=citadel_collabpage
    private static final String SUPPORT_PARAM_HOST_NAME = "host.name";
    //宿主 app 版本
    private static final String SUPPORT_PARAM_HOST_VERSION = "host.version";

    //发起请求时刻的时间戳
    private static final String SUPPORT_PARAM_TIMESTAMP = "timestamp";

    //城市
    //用户选择的城市 ID
    private static final String SUPPORT_PARAM_SELECT_CITY_ID = "selected_city.id";

    private AppMetaInfoWrapper appMetaInfoWrapper;
    private String url;
    private String appUUID;

    private static String[] SUPPORT_PARAMS = new String[] {
            SUPPORT_PARAM_APP_ID,
            SUPPORT_PARAM_APP_VERSION,
            SUPPORT_PARAM_APP_UUID,
            SUPPORT_PARAM_PATH,
            SUPPORT_PARAM_QUERY,
            SUPPORT_PARAM_USER_TOKEN,
            SUPPORT_PARAM_USER_ID,
            SUPPORT_PARAM_OS_NAME,
            SUPPORT_PARAM_OS_VERSION,
            SUPPORT_PARAM_DEVICE_PRODUCT_MODEL,
            SUPPORT_PARAM_UUID,
            SUPPORT_PARAM_HOST_NAME,
            SUPPORT_PARAM_HOST_VERSION,
            SUPPORT_PARAM_TIMESTAMP,
            SUPPORT_PARAM_SELECT_CITY_ID
    };

    public MSCCommonValueParser(AppMetaInfoWrapper appMetaInfoWrapper, String url, String appUUID){
        this.appMetaInfoWrapper = appMetaInfoWrapper;
        this.url = url;
        this.appUUID = appUUID;
    }

    @Override
    public boolean isSupport(String param) {
        for (String item: SUPPORT_PARAMS) {
            if (TextUtils.equals(param, item)) {
                return true;
            }
        }

        return false;
    }

    @Override
    public Object getValue(String param) {
        if (TextUtils.equals(param, SUPPORT_PARAM_APP_ID)) {
            return appMetaInfoWrapper != null ? appMetaInfoWrapper.getAppId(): null;
        } else if (TextUtils.equals(param, SUPPORT_PARAM_APP_VERSION)) {
            return appMetaInfoWrapper != null ? appMetaInfoWrapper.getVersion(): null;
        } else if (TextUtils.equals(param, SUPPORT_PARAM_APP_UUID)) {
            return this.appUUID;
        } else if (TextUtils.equals(param, SUPPORT_PARAM_PATH)) {
            Uri uri = Uri.parse(this.url);
            return uri.getPath();
        } else if (TextUtils.equals(param, SUPPORT_PARAM_QUERY)) {
            Uri uri = Uri.parse(this.url);
            return uri.getQuery();
        } else if (TextUtils.equals(param, SUPPORT_PARAM_USER_TOKEN)) {
            IMSCUserCenter userCenter = MSCEnvHelper.getMSCUserCenter();
            if (userCenter != null && userCenter.isLogin()) {
                return userCenter.getToken();
            }
        } else if (TextUtils.equals(param, SUPPORT_PARAM_USER_ID)) {
            String userId = MSCEnvHelper.getEnvInfo().getUserID();
            if (!TextUtils.isEmpty(userId)){
                return userId;
            }
        } else if (TextUtils.equals(param, SUPPORT_PARAM_OS_NAME)) {
            return "Android";
        } else if (TextUtils.equals(param, SUPPORT_PARAM_OS_VERSION)) {
            return Build.VERSION.RELEASE;
        } else if (TextUtils.equals(param, SUPPORT_PARAM_DEVICE_PRODUCT_MODEL)) {
            return Build.MODEL;
        } else if (TextUtils.equals(param, SUPPORT_PARAM_UUID)) {
            String uuid = MSCEnvHelper.getEnvInfo().getUUID();
            if (!TextUtils.isEmpty(uuid)){
                return uuid;
            }
        } else if (TextUtils.equals(param, SUPPORT_PARAM_HOST_NAME)) {
            String hostName = MSCEnvHelper.getEnvInfo().getAppName();
            if (!TextUtils.isEmpty(hostName)) {
                return hostName;
            }
        } else if (TextUtils.equals(param, SUPPORT_PARAM_HOST_VERSION)) {
            String hostVersion = MSCEnvHelper.getEnvInfo().getAppVersionName();
            if (!TextUtils.isEmpty(hostVersion)) {
                return hostVersion;
            }
        } else if (TextUtils.equals(param, SUPPORT_PARAM_TIMESTAMP)) {
            return System.currentTimeMillis();
        } else if (TextUtils.equals(param, SUPPORT_PARAM_SELECT_CITY_ID)) {
            return MSCEnvHelper.getCityController().getCityId();
        }
        return null;
    }
}
