package com.meituan.msc.modules.engine.dataprefetch;

import android.support.annotation.NonNull;
import android.text.TextUtils;

import com.google.gson.JsonObject;
import com.google.gson.JsonPrimitive;
import com.meituan.dio.easy.DioFile;
import com.meituan.met.mercury.load.core.DDResource;
import com.meituan.msc.lib.interfaces.prefetch.PrefetchURLConfig;
import com.meituan.msc.modules.engine.MSCHornRollbackConfig;
import com.meituan.msc.modules.update.pkg.MSCLoadPackageScene;
import com.meituan.msc.common.perf.PerfEventConstant;
import com.meituan.msc.common.utils.MSCTraceUtil;
import com.meituan.msc.lib.interfaces.prefetch.IPrefetchMSCContext;
import com.meituan.msc.lib.interfaces.prefetch.MSCBaseValueParser;
import com.meituan.msc.common.utils.FileUtil;
import com.meituan.msc.common.utils.PathUtil;
import com.meituan.msc.modules.apploader.events.AppLoadException;
import com.meituan.msc.modules.engine.MSCRuntimeHelper;
import com.meituan.msc.modules.engine.dataprefetch.msi.MSINetRequestParam;
import com.meituan.msc.modules.manager.MSCModule;
import com.meituan.msc.modules.manager.ModuleName;
import com.meituan.msc.modules.msi.MSIManagerModule;
import com.meituan.msc.modules.reporter.MSCLog;
import com.meituan.msc.modules.reporter.MSCReporter;
import com.meituan.msc.modules.update.MSCAppModule;
import com.meituan.msc.modules.update.bean.AppMetaInfoWrapper;
import com.meituan.msc.modules.update.bean.PackageInfoWrapper;
import com.meituan.msc.modules.update.pkg.PackageLoadCallback;
import com.meituan.msc.modules.update.pkg.PackageLoadManager;
import com.meituan.msc.util.perf.PerfEventName;
import com.meituan.msc.util.perf.PerfTrace;
import com.sankuai.android.jarvis.Jarvis;
import com.sankuai.meituan.serviceloader.ServiceLoader;

import org.json.JSONArray;
import org.json.JSONObject;

import java.io.IOException;
import java.util.ArrayList;
import java.util.List;
import java.util.Map;
import java.util.concurrent.ConcurrentHashMap;
import java.util.concurrent.CountDownLatch;
import java.util.concurrent.ExecutorService;
import java.util.concurrent.TimeUnit;
import java.util.concurrent.atomic.AtomicInteger;

@ModuleName(name = "DataPrefetch")
public class MSCDataPrefetchModule extends MSCModule implements IDataPrefetchModule {
    //业务级别解析器service_loader key。msc_value_parser_{parerName}, parerName为解析器名称
    private static final String BIZ_VALUE_PARSER_KEY = "msc_value_parser_%s";

    //数据预拉取配置信息
    private DataPrefetchConfig dataPrefetchConfig = null;
    private volatile Boolean configIsValid = null;
    private ExecutorService executorService;
    private PackageInfoWrapper configPackageInfoWrapper;
    private DataPrefetchManager dataPrefetchManager;
    private MSCPrefetchRequestReporter mscPrefetchRequestReporter;

    //routeId 和发起的数据预拉取请求map
    private Map<String, MSCDataPrefetchRouteModule> routePrefetchRequestInfoMap = new ConcurrentHashMap<>();
    private Map<String, String> widgetRouteTargetMap = new ConcurrentHashMap<>();

    public MSCDataPrefetchModule() {
        executorService = Jarvis.newSingleThreadExecutor("msc-dynamic-data-prefetch-schedule");
    }

    @Override
    public void startDataPrefetch(String targetPath, int routeId, long routeTime, boolean isWidget) {
        PerfTrace.online().begin(PerfEventConstant.DYNAMIC_PREFETCH).report().arg("targetPath", targetPath).arg("isWidget", isWidget);
        MSCTraceUtil.instant("startDataPrefetch");
        if (!MSCHornDynamicPrefetchConfig.enablePrefetch()) {
            MSCLog.i("MSCDynamicDataPrefetch", " DynamicPrefetch disable targetPath: " + targetPath);
            PerfTrace.online().end(PerfEventConstant.DYNAMIC_PREFETCH).report();
            return;
        }

        //widget场景，如果preload时机发起数据预拉取，launch时机不再发起数据预拉取。preload和launch使用同一个routeId
        if (isWidget || MSCHornRollbackConfig.enableLaunchTaskOnRoute()) {
            String routeKey = String.valueOf(routeId);
            if (widgetRouteTargetMap.containsKey(routeKey) && TextUtils.equals(widgetRouteTargetMap.get(routeKey), targetPath)) {
                MSCLog.i("MSCDynamicDataPrefetch", " DynamicPrefetch finish. Widget has startDataPrefetch targetPath: " + targetPath);
                PerfTrace.online().end(PerfEventConstant.DYNAMIC_PREFETCH).report();
                return;
            }
            widgetRouteTargetMap.put(routeKey, targetPath);
        }

        MSCDataPrefetchRouteModule routePrefetchRequestInfo = new MSCDataPrefetchRouteModule(routeId, targetPath, getRuntime(), routeTime);
        routePrefetchRequestInfo.getCommonPhaseRecord().startPrefetchTime = System.currentTimeMillis();
        routePrefetchRequestInfoMap.put(String.valueOf(routeId), routePrefetchRequestInfo);
        executorService.submit(new Runnable() {
            @Override
            public void run() {
                try {
                    MSCTraceUtil.instant("startDataPrefetch in Thread");
                    startDataPrefetchInner(targetPath, routeId);
                } catch (Exception e) {

                }
            }
        });
    }

    @Override
    public void attachToPage(int routeId, int pageId) {
        if (!MSCHornDynamicPrefetchConfig.enablePrefetch()) {
            MSCLog.i("MSCDynamicDataPrefetch", "attachToPage DynamicPrefetch disable");
            return;
        }

        executorService.submit(new Runnable() {
            @Override
            public void run() {
                attachToPageInner(routeId, pageId);
            }
        });
    }

    @Override
    public void onPageDestroy(int pageId) {
        //页面销毁时，清空和页面相关的数据
        if (routePrefetchRequestInfoMap.size() > 0) {
            for (String routeId: routePrefetchRequestInfoMap.keySet()) {
                MSCDataPrefetchRouteModule routePrefetchRequestInfo = routePrefetchRequestInfoMap.get(routeId);
                if (routePrefetchRequestInfo != null && routePrefetchRequestInfo.getAttachPageId() == pageId) {
                    routePrefetchRequestInfo.setState(MSCDataPrefetchRouteModule.PrefetchRequestState.PAGE_DESTROY);
                    routePrefetchRequestInfoMap.remove(routeId);
                    break;
                }
            }
        }
    }

    @Override
    public JSONArray getPrefetchDataSync(String pageId, String[] urls) {
        MSCDataPrefetchRouteModule routePrefetchRequestModule = null;
        int iPageId = Integer.valueOf(pageId);
        if (routePrefetchRequestInfoMap.size() > 0) {
            for (String routeId: routePrefetchRequestInfoMap.keySet()) {
                MSCDataPrefetchRouteModule tmp = routePrefetchRequestInfoMap.get(routeId);
                if (tmp != null && tmp.getAttachPageId() == iPageId) {
                    routePrefetchRequestModule = tmp;
                    break;
                }
            }
        }
        JSONArray jsonArray= null;
        if (routePrefetchRequestModule != null) {
            if (urls != null && urls.length > 0) {
                jsonArray = new JSONArray();
                for (String url: urls) {
                    JSONObject jsonObject = routePrefetchRequestModule.getPrefetchRequestRes(url);
                    if (jsonObject != null) {
                        jsonArray.put(jsonObject);
                    }
                }
            } else if (urls != null) { //返回空数据
                jsonArray = new JSONArray();
            } else { //返回所有的
                jsonArray = routePrefetchRequestModule.getRequestResList();
            }
        }

        return jsonArray;
    }

    private void startDataPrefetchInner(String targetPath, int routeId) {
        MSCLog.i("MSCDynamicDataPrefetch", "startDataPrefetchInner " + targetPath + " routeId " + routeId);
        MSCDataPrefetchRouteModule routePrefetchRequestInfo = routePrefetchRequestInfoMap.get(String.valueOf(routeId));
        //启动任务在启动页面前加到map中，如果此时获取不到，可能因为页面关闭清除，不发起数据预拉取
        if (routePrefetchRequestInfo == null ||
                routePrefetchRequestInfo.getState() == MSCDataPrefetchRouteModule.PrefetchRequestState.PAGE_DESTROY) {
            return;
        }

        if (dataPrefetchManager == null) {
            MSIManagerModule msiManagerModule = getRuntime().getModule(MSIManagerModule.class);
            if (msiManagerModule != null) {
                dataPrefetchManager = new DataPrefetchManager(msiManagerModule);
            }
        }
        if (dataPrefetchManager == null) {
            routePrefetchRequestInfo.setState(MSCDataPrefetchRouteModule.PrefetchRequestState.FINISH);
            return;
        }

        try {
            PerfTrace.online().begin(PerfEventConstant.DYNAMIC_PREFETCH_CONFIG_PKG_FETCH).report().arg("targetPath", targetPath);
            MSCPrefetchPhaseRecord pathPrefetchPhaseRecord = routePrefetchRequestInfo.getCommonPhaseRecord();
            //获取配置包
            routePrefetchRequestInfo.setState(MSCDataPrefetchRouteModule.PrefetchRequestState.FETCH_CONFIG_PACKAGE);
            pathPrefetchPhaseRecord.startFetchConfigTime = System.currentTimeMillis();
            getConfigPackage();
            PerfTrace.online().end(PerfEventConstant.DYNAMIC_PREFETCH_CONFIG_PKG_FETCH).report();

            //获取数据预拉取配置文件并解析
            routePrefetchRequestInfo.setState(MSCDataPrefetchRouteModule.PrefetchRequestState.LOAD_PREFETCH_CONFIG);
            pathPrefetchPhaseRecord.startParseConfigTime = System.currentTimeMillis();
            loadDataPrefetchConfigFromDio();

            //按path获取配置
            String purePath = PathUtil.getPath(targetPath);
            if (dataPrefetchConfig != null && dataPrefetchConfig.pageConfigs != null &&
                    dataPrefetchConfig.pageConfigs.containsKey(purePath)) {
                routePrefetchRequestInfo.setState(MSCDataPrefetchRouteModule.PrefetchRequestState.SEND_REQUESTS);
                Map<String, PrefetchURLConfig> pathURLConfigs = dataPrefetchConfig.pageConfigs.get(purePath);
                if (pathURLConfigs != null && pathURLConfigs.size() > 0) {
                    DataPrefetchConfig.LocationConfig locationConfig = dataPrefetchConfig.sharedConfigs != null ? dataPrefetchConfig.sharedConfigs.location: null;
                    List<MSCBaseValueParser> valueParserList = getValueParsers(targetPath, locationConfig, dataPrefetchConfig.valueParsers);
                    int urlConfigCount = pathURLConfigs.size();
                    AtomicInteger atomicInteger = new AtomicInteger(urlConfigCount);
                    for (Map.Entry<String, PrefetchURLConfig> entry: pathURLConfigs.entrySet()) {
                        String eventName = PerfEventName.REQUEST_PREFETCH + "::" + entry.getKey();
                        MSCTraceUtil.begin(eventName);
                        PrefetchRequestRes prefetchRequestRes = new PrefetchRequestRes(entry.getKey());
                        MSCPrefetchPhaseRecord urlPrefetchPhaseRecord = new MSCPrefetchPhaseRecord(pathPrefetchPhaseRecord);
                        urlPrefetchPhaseRecord.requestUrl = entry.getKey();
                        routePrefetchRequestInfo.addUrlRecord(urlPrefetchPhaseRecord);
                        routePrefetchRequestInfo.addPrefetchRequestRes(prefetchRequestRes);
                        dataPrefetchManager.sendDataPrefetchRequest(entry.getKey(), entry.getValue(), dataPrefetchConfig.sharedConfigs, valueParserList, new IPrefetchRequestCallback() {
                            @Override
                            public void onSuccess(JsonObject data) {
                                MSCTraceUtil.end(eventName);
                                MSCLog.i("MSCDynamicDataPrefetch", " success");
                                urlPrefetchPhaseRecord.endMsiRequestTime = System.currentTimeMillis();
                                routePrefetchRequestInfo.onMsiRequestSuccess(prefetchRequestRes, data);
                                if (atomicInteger.decrementAndGet() == 0) {
                                    routePrefetchRequestInfo.setState(MSCDataPrefetchRouteModule.PrefetchRequestState.FINISH);
                                }

                                onPrefetchSuccess(targetPath, prefetchRequestRes.url, entry.getKey(), urlPrefetchPhaseRecord);
                                PerfTrace.online().end(PerfEventConstant.DYNAMIC_PREFETCH).report();
                            }

                            @Override
                            public void onFail(int errorCode, String errorMessage) {
                                MSCTraceUtil.end(eventName);
                                MSCLog.i("MSCDynamicDataPrefetch", " fail: " + errorMessage);
                                urlPrefetchPhaseRecord.endMsiRequestTime = System.currentTimeMillis();
                                routePrefetchRequestInfo.onMsiRequestFail(prefetchRequestRes, errorCode, errorMessage);
                                if (atomicInteger.decrementAndGet() == 0) {
                                    routePrefetchRequestInfo.setState(MSCDataPrefetchRouteModule.PrefetchRequestState.FINISH);
                                }

                                onPrefetchFail(targetPath, prefetchRequestRes.url, entry.getKey(), errorCode, errorMessage);
                                PerfTrace.online().end(PerfEventConstant.DYNAMIC_PREFETCH).report();
                            }

                            @Override
                            public void onStartValueParser() {
                                urlPrefetchPhaseRecord.startParseValueTime = System.currentTimeMillis();
                            }

                            @Override
                            public void onPathParserFinish(String configUrl, String urlWithoutQuery) {
                                prefetchRequestRes.setRealRequestUrl(urlWithoutQuery);

                            }

                            @Override
                            public void onMsiRequestBuildFinish(MSINetRequestParam requestParam) {
                                urlPrefetchPhaseRecord.startMsiRequestTime = System.currentTimeMillis();
                            }
                        });
                    }
                    routePrefetchRequestInfo.setState(MSCDataPrefetchRouteModule.PrefetchRequestState.WAIT_REQUEST_RES);
                } else {
                    routePrefetchRequestInfo.setState(MSCDataPrefetchRouteModule.PrefetchRequestState.FINISH);
                }
            } else {
                MSCLog.i("MSCDynamicDataPrefetch", " no match path " + purePath);
                routePrefetchRequestInfo.setState(MSCDataPrefetchRouteModule.PrefetchRequestState.FINISH);
            }
        } catch (Exception e) {
            MSCLog.i("MSCDynamicDataPrefetch", " Exception " + e.getMessage());
            onPrefetchFail(targetPath, null, null, MSCPrefetchRequestReporter.ERROR_CODE_PREFETCH_PARSE_URL_FAIL, e.getMessage());
        }
    }

    /**
     * 获取参数解析器。通用解析器、宿主级别解析器、业务级别解析器
     * @param url
     * @param locationConfig
     * @return
     */
    private List<MSCBaseValueParser> getValueParsers(String url, DataPrefetchConfig.LocationConfig locationConfig, List<String> valueParsers){
        List<MSCBaseValueParser> parserList = new ArrayList<>();
        IPrefetchMSCContext prefetchMSCContext = null;
        if (valueParsers != null && valueParsers.size() > 0) {
            for (String parserName: valueParsers) {
                String serviceLoaderKey = String.format(BIZ_VALUE_PARSER_KEY, parserName);
                List<MSCBaseValueParser> mscValueParsers = ServiceLoader.load(MSCBaseValueParser.class, serviceLoaderKey);
                if (mscValueParsers != null && mscValueParsers.size() > 0) {
                    MSCBaseValueParser bizValueParser = mscValueParsers.get(0);
                    if (bizValueParser != null) {
                        if (prefetchMSCContext == null) {
                            prefetchMSCContext = createPrefetchMSCContext(url);
                        }
                        bizValueParser.attachMSCContext(prefetchMSCContext);
                    }
                    parserList.add(bizValueParser);
                }
            }
        }

        AppMetaInfoWrapper appMetaInfoWrapper = getRuntime().getMSCAppModule().getMetaInfo();
        MSCCommonValueParser commonValueParser = new MSCCommonValueParser(appMetaInfoWrapper, url, getRuntime().getAppUUID());
        MSCLocationValueParser locationValueParser = new MSCLocationValueParser(locationConfig, getRuntime());

        MSCQueryValueParser queryValueParser = new MSCQueryValueParser(url);

        parserList.add(commonValueParser);
        parserList.add(locationValueParser);
        parserList.add(queryValueParser);

        return parserList;
    }

    private void attachToPageInner(int routeId, int pageId) {
        MSCLog.i("MSCDynamicDataPrefetch", " attachToPage pageId: " + pageId + " routeId:" + routeId);
        MSCDataPrefetchRouteModule dataPrefetchRouteModule = routePrefetchRequestInfoMap.get(String.valueOf(routeId));
        if (dataPrefetchRouteModule != null) {
            if (isPageAttached(pageId)) { //已经绑定过，表明未创建新页面，不再次绑定。移除PathPrefetchRequestModule
                routePrefetchRequestInfoMap.remove(String.valueOf(routeId));
            } else {
                dataPrefetchRouteModule.attachToPage(pageId);
            }
        }
    }

    private boolean isPageAttached(int pageId) {
        for (MSCDataPrefetchRouteModule requestModule: routePrefetchRequestInfoMap.values()) {
            if (requestModule != null && requestModule.getAttachPageId() == pageId) {
                return true;
            }
        }

        return false;
    }

    private IPrefetchMSCContext createPrefetchMSCContext(String url) {
        return new IPrefetchMSCContext() {
            @Override
            public String getAppId() {
                return getApp() != null ? getApp().getAppId() : null;
            }

            @Override
            public String getAppVersion() {
                return MSCRuntimeHelper.getAppVersion(getRuntime());
            }

            @Override
            public String getJumpPath() {
                return url;
            }
        };
    }

    /**
     * 1、业务预热时会单独开线程会调用该方法  2、跳转进页面时也会单独开线程调用该方法。synchronized保证只会加载一次
     */
    @Override
    public synchronized void loadDataPrefetchConfigFromDio() {
        // 如果configIsValid!=null说明尝试加载过，直接返回
        if (configIsValid != null) {
            return;
        }
        MSCTraceUtil.begin("loadDataPrefetchConfigFromDio_" + getRuntime().getAppId());
        if (configPackageInfoWrapper == null || configPackageInfoWrapper.getDDResource() == null) {
            return;
        }

        try {
            MSCTraceUtil.begin("DataPrefetchGetFromFile");
            DDResource ddResource = configPackageInfoWrapper.getDDResource();
            DioFile configFile = new DioFile(ddResource.getLocalPath(), "prefetch.json");

            String content = FileUtil.readContent(configFile);
            MSCTraceUtil.end("DataPrefetchGetFromFile");
            MSCLog.i("MSCDynamicDataPrefetch", "Config: " + content);
            MSCTraceUtil.begin("DataPrefetchStartParserJson");
            DataPrefetchConfig tmpDataPrefetchConfig = DataPrefetchManager.fromJson(content, DataPrefetchConfig.class);
            if (tmpDataPrefetchConfig != null && tmpDataPrefetchConfig.pageConfigs != null && tmpDataPrefetchConfig.pageConfigs.size() > 0) {
                for (String pageKey: tmpDataPrefetchConfig.pageConfigs.keySet()) {
                    Map<String, PrefetchURLConfig> urlConfig = tmpDataPrefetchConfig.pageConfigs.get(pageKey);
                    if (urlConfig != null && urlConfig.size() > 0) {
                        for (String urlKey: urlConfig.keySet()) {
                            PrefetchURLConfig prefetchURLConfig = urlConfig.get(urlKey);
                            if (prefetchURLConfig != null) {
                                prefetchURLConfig.enableShark = getBooleanFromJsonPrimitive(prefetchURLConfig.configEnableShark);
                                prefetchURLConfig.enableSecuritySiua = getBooleanFromJsonPrimitive(prefetchURLConfig.configEnableSecuritySiua);
                                prefetchURLConfig.enableSecuritySign = getBooleanFromJsonPrimitive(prefetchURLConfig.configEnableSecuritySign);
                            }
                        }
                    }
                }
            }
            dataPrefetchConfig = tmpDataPrefetchConfig;
            configIsValid = true;
            MSCTraceUtil.end("loadDataPrefetchConfigFromDio_" + getRuntime().getAppId());
            MSCTraceUtil.end("DataPrefetchStartParserJson");
            getMscPrefetchRequestReporter().reportFetchConfigSuccessRate(MSCReporter.ReportValue.SUCCESS, MSCPrefetchRequestReporter.ERROR_CODE_PREFETCH_SUCCESS, null);
        } catch (Exception e) {
            MSCLog.e("MSCDynamicDataPrefetch", "LoadConfig fail: " + e.getMessage());
            configIsValid = false;
            getMscPrefetchRequestReporter().reportFetchConfigSuccessRate(MSCReporter.ReportValue.FAILED, MSCPrefetchRequestReporter.ERROR_CODE_PREFETCH_CONFIG_PARSE_FAIL, e.getMessage());
        }
    }

    private Boolean getBooleanFromJsonPrimitive(JsonPrimitive jsonPrimitive) throws IOException {
        if (jsonPrimitive == null) {
            return null;
        }

        if (!jsonPrimitive.isBoolean()) {
            throw new IOException("is not Boolean type. Value: " + jsonPrimitive.getAsString());
        }

        return jsonPrimitive.getAsBoolean();
    }

    /**
     * 1、业务预热时会单独开线程会调用该方法  2、跳转进页面时也会单独开线程调用该方法。synchronized保证只会加载一次
     */
    @Override
    public synchronized void getConfigPackage() {
        //已成功获取配置包
        if (configPackageInfoWrapper != null && configPackageInfoWrapper.getDDResource() != null) {
            return;
        }


        MSCAppModule appModule = getRuntime().getMSCAppModule();
        if (appModule == null) {
            return;
        }

        configPackageInfoWrapper = appModule.createConfigPackageWrapper();
        if (configPackageInfoWrapper == null) {
            return;
        }
        CountDownLatch countDownLatch = new CountDownLatch(1);
        MSCTraceUtil.begin("getConfigPackage_" + getRuntime().getAppId());
        PackageLoadManager.getInstance().loadPackageWithInfo(null, configPackageInfoWrapper,
                false, "", MSCLoadPackageScene.LOAD_PACKAGE_TYPE_LAUNCH, new PackageLoadCallback<PackageInfoWrapper>() {
                    @Override
                    public void onSuccess(@NonNull PackageInfoWrapper data) {
                        MSCLog.i("MSCDynamicDataPrefetch", "LoadPackage success");
                        countDownLatch.countDown();
                        MSCTraceUtil.end("getConfigPackage_" + getRuntime().getAppId());
                    }

                    @Override
                    public void onFail(String errMsg, AppLoadException error) {
                        MSCLog.i("MSCDynamicDataPrefetch", "LoadPackage fail, " + errMsg);
                        countDownLatch.countDown();
                        getMscPrefetchRequestReporter().reportFetchConfigSuccessRate(MSCReporter.ReportValue.FAILED, MSCPrefetchRequestReporter.ERROR_CODE_PREFETCH_CONFIG_FETCH_FAIL, errMsg);
                    }
                });
        try {
            countDownLatch.await(120, TimeUnit.SECONDS);
        } catch (InterruptedException interruptedException) {
            MSCLog.i("MSCDynamicDataPrefetch", "LoadPackage fail, timeout " + interruptedException.getMessage());
            getMscPrefetchRequestReporter().reportFetchConfigSuccessRate(MSCReporter.ReportValue.FAILED, MSCPrefetchRequestReporter.ERROR_CODE_PREFETCH_CONFIG_FETCH_TIMEOUT_FAIL, interruptedException.getMessage());
        }
    }

    @Override
    public Map<String, MSCPrefetchPhaseRecord> getPrefetchPhaseRecordOfUrl(int routeId) {
        MSCDataPrefetchRouteModule mscDataPrefetchRouteModule = routePrefetchRequestInfoMap.get(String.valueOf(routeId));
        if (mscDataPrefetchRouteModule != null) {
            return mscDataPrefetchRouteModule.getUrlRecordMap();
        }
        return null;
    }

    @Override
    public MSCPrefetchPhaseRecord getCommonPrefetchRecord(int routeId) {
        MSCDataPrefetchRouteModule mscDataPrefetchRouteModule = routePrefetchRequestInfoMap.get(String.valueOf(routeId));
        if (mscDataPrefetchRouteModule != null) {
            return mscDataPrefetchRouteModule.getCommonPhaseRecord();
        }
        return null;
    }

    @NonNull
    private MSCPrefetchRequestReporter getMscPrefetchRequestReporter() {
        if (mscPrefetchRequestReporter == null) {
            mscPrefetchRequestReporter = MSCPrefetchRequestReporter.create(getRuntime());
        }

        return mscPrefetchRequestReporter;
    }

    private void onPrefetchSuccess(String pagePath, String requestUrlWithoutQuery, String configUrl, MSCPrefetchPhaseRecord phaseRecord) {
        MSCPrefetchRequestReporter prefetchRequestReporter = getMscPrefetchRequestReporter();
        if (prefetchRequestReporter != null) {
            //上报成功率
            prefetchRequestReporter.reportDynamicPrefetchSuccessRate(MSCReporter.ReportValue.SUCCESS, pagePath,
                    requestUrlWithoutQuery, configUrl, MSCPrefetchRequestReporter.ERROR_CODE_PREFETCH_SUCCESS, null);

            //上报耗时，成功场景才上报耗时
            prefetchRequestReporter.reportDynamicPrefetchDuration(pagePath, requestUrlWithoutQuery, configUrl, phaseRecord);
        }
    }

    private void onPrefetchFail(String pagePath, String requestUrlWithoutQuery, String configUrl, int errorCode, String errorMsg) {
        getMscPrefetchRequestReporter().reportDynamicPrefetchSuccessRate(MSCReporter.ReportValue.SUCCESS, pagePath,
                requestUrlWithoutQuery, configUrl, errorCode, errorMsg);
    }
}
