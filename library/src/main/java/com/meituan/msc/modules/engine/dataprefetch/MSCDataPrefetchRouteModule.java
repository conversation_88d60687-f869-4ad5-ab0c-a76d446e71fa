package com.meituan.msc.modules.engine.dataprefetch;


import android.support.annotation.NonNull;
import android.text.TextUtils;

import com.google.gson.JsonElement;
import com.google.gson.JsonObject;
import com.meituan.msc.common.executor.MSCExecutors;
import com.meituan.msc.common.utils.MSCTraceUtil;
import com.meituan.msc.modules.engine.MSCRuntime;
import com.meituan.msc.modules.page.PageListener;
import com.meituan.msc.modules.reporter.MSCLog;

import org.json.JSONArray;
import org.json.JSONException;
import org.json.JSONObject;

import java.util.List;
import java.util.Map;
import java.util.concurrent.ConcurrentHashMap;
import java.util.concurrent.CopyOnWriteArrayList;

/**
 * 页面按一次跳转的预请求Module。一次路由切换对应一个
 */
public class MSCDataPrefetchRouteModule {
    private int routeId;
    private String targetPath;
    private List<PrefetchRequestRes> requestResList = new CopyOnWriteArrayList<>();
    private PrefetchRequestState state;
    private int pageId = -1;
    private MSCRuntime mscRuntime;
    // 记录预请求的通用阶段信息
    private MSCPrefetchPhaseRecord commonPhaseRecord;
    // 记录预请求特定url信息
    private Map<String, MSCPrefetchPhaseRecord> urlRecordMap;

    enum PrefetchRequestState {
        START, FETCH_CONFIG_PACKAGE, LOAD_PREFETCH_CONFIG, SEND_REQUESTS, WAIT_REQUEST_RES, FINISH, PAGE_DESTROY
    }

    public MSCDataPrefetchRouteModule(int routeId, String targetPath, MSCRuntime mscRuntime, long routeTime) {
        this.routeId = routeId;
        this.targetPath = targetPath;
        this.mscRuntime = mscRuntime;
        this.commonPhaseRecord = new MSCPrefetchPhaseRecord(routeTime);
        this.urlRecordMap = new ConcurrentHashMap<>();
    }

    public void addPrefetchRequestRes(PrefetchRequestRes prefetchRequestRes) {
        if (prefetchRequestRes != null) {
            requestResList.add(prefetchRequestRes);
        }
    }

    public JSONObject getPrefetchRequestRes(String url) {
        for (PrefetchRequestRes requestRes: requestResList) {
            if (TextUtils.equals(requestRes.url, url)) {
                return createJSONObject(requestRes);
            }
        }

        return createEmptyJSONObject(url);
    }

    public JSONArray getRequestResList() {
        JSONArray array = new JSONArray();
        for (PrefetchRequestRes requestRes: requestResList) {
            JSONObject jsonObject = createJSONObject(requestRes);
            if (jsonObject != null){
                array.put(jsonObject);
            }
        }
        return array;
    }

    public void addUrlRecord(MSCPrefetchPhaseRecord record) {
        if (record != null) {
            urlRecordMap.put(record.requestUrl, record);
        }
    }

    public Map<String, MSCPrefetchPhaseRecord> getUrlRecordMap() {
        return urlRecordMap;
    }

    public void setState(PrefetchRequestState state){
        if (this.state == PrefetchRequestState.FINISH) {
            return;
        }

        this.state = state;
    }

    public PrefetchRequestState getState() {
        return this.state;
    }

    @NonNull
    public MSCPrefetchPhaseRecord getCommonPhaseRecord(){
        return this.commonPhaseRecord;
    }

    public void attachToPage(int pageId){
        MSCLog.i("MSCDynamicDataPrefetch", " attachToPage " + pageId);
        this.pageId = pageId;
        for (PrefetchRequestRes requestRes: requestResList) {
            if (requestRes.isFinish()) {
                onPrefetchData(requestRes, this.pageId);
            }
        }
    }

    private void onPrefetchData(@NonNull PrefetchRequestRes requestRes, int pageId) {
        MSCExecutors.postOnUiThread(new Runnable() {
            @Override
            public void run() {
                MSCTraceUtil.instant("onPrefetchData" + requestRes.configUrl);
                JSONObject object = new JSONObject();
                try {
                    object.put("success", requestRes.success);
                    object.put("reason", requestRes.reason);
                    object.put("url", requestRes.url);
                    object.put("data", requestRes.data != null ? requestRes.data : "");
                    object.put("configURL", requestRes.configUrl);
                } catch (JSONException e) {
                    e.printStackTrace();
                }
                MSCLog.i("MSCDynamicDataPrefetch", " onPrefetchData " + object.toString());
                if (requestRes.url != null) {
                    MSCPrefetchPhaseRecord mscPrefetchPhaseRecord = urlRecordMap.get(requestRes.url);
                    if (mscPrefetchPhaseRecord != null) {
                        mscPrefetchPhaseRecord.sendDataToFeTime = System.currentTimeMillis();
                    }
                }
                mscRuntime.getJSModuleDelegate(PageListener.class).onPrefetchData(object, pageId);
            }
        });
    }

    public static JSONObject createJSONObject(@NonNull PrefetchRequestRes requestRes) {
        JSONObject object = new JSONObject();
        try {
            if (requestRes != null) {
                object.put("success", requestRes.success);
                object.put("reason", requestRes.reason);
                object.put("url", requestRes.url);
                object.put("data", requestRes.data != null ? requestRes.data : "");
                object.put("status", requestRes.status);
            }
        } catch (JSONException e) {
            e.printStackTrace();
            object = null;
        }

        return object;
    }

    public static JSONObject createEmptyJSONObject(String url) {
        JSONObject object = new JSONObject();
        try {
                object.put("success", false);
                object.put("reason", "url not match， url is error or fetch config is not finish");
                object.put("url", url);
                object.put("data", "");
                object.put("status", "fail");
        } catch (JSONException e) {
            e.printStackTrace();
            object = null;
        }

        return object;
    }


    public void onMsiRequestSuccess(@NonNull PrefetchRequestRes requestRes, JsonObject data) {
        requestRes.status = "success";
        requestRes.success = true;
        //取msi request返回值里的data项
        if (data != null && data.has("data")) {
            JsonElement requestData = data.get("data");
            requestRes.data = requestData != null ? requestData.toString(): null;
        }

        if (this.pageId != -1) {
            onPrefetchData(requestRes, this.pageId);
        }
    }

    public void onMsiRequestFail(@NonNull PrefetchRequestRes requestRes, int errorCode, String errorMessage) {
        requestRes.status = "fail";
        requestRes.success = false;
        requestRes.reason = errorMessage;
        if (this.pageId != -1) {
            onPrefetchData(requestRes, this.pageId);
        }
    }

    public int getAttachPageId(){
        return this.pageId;
    }
}
