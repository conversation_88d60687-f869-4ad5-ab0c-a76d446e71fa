package com.meituan.msc.modules.engine.dataprefetch;

import android.support.annotation.Keep;

import com.google.gson.annotations.SerializedName;
import com.meituan.msc.lib.interfaces.BaseRemoteConfig;
import com.meituan.msc.modules.preload.MSCHornPreloadConfig;

public class MSCHornDynamicPrefetchConfig extends BaseRemoteConfig<MSCHornDynamicPrefetchConfig.Config> {
    @Keep
    public static class Config {
        // 用于gson及其他正常创建的情况
        public Config() {
        }

        @SerializedName("enablePrefetch")
        public boolean enablePrefetch = true;
    }

    private static MSCHornDynamicPrefetchConfig sInstance;

    public static MSCHornDynamicPrefetchConfig get() {
        if (sInstance == null) {
            synchronized (MSCHornDynamicPrefetchConfig.class) {
                if (sInstance == null) {
                    sInstance = new MSCHornDynamicPrefetchConfig();
                }
            }
        }
        return sInstance;
    }

    private MSCHornDynamicPrefetchConfig() {
        super("msc_dynamic_prefetch", MSCHornDynamicPrefetchConfig.Config.class);
    }

    public static boolean enablePrefetch(){
        return MSCHornDynamicPrefetchConfig.get().config.enablePrefetch;
    }
}
