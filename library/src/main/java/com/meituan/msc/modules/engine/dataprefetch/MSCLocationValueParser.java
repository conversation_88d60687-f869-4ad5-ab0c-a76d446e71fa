package com.meituan.msc.modules.engine.dataprefetch;

import android.app.Activity;
import android.text.TextUtils;

import com.meituan.android.common.locate.MtLocation;
import com.meituan.android.privacy.locate.MtLocationCache;
import com.meituan.msc.common.utils.MSCTraceUtil;
import com.meituan.msc.common.utils.LocationUtils;
import com.meituan.msc.lib.interfaces.prefetch.MSCBaseValueParser;
import com.meituan.msc.extern.MSCEnvHelper;
import com.meituan.msc.lib.interfaces.prefetch.PrefetchURLConfig;
import com.meituan.msc.modules.api.map.ILocation;
import com.meituan.msc.modules.api.map.ILocationLoader;
import com.meituan.msc.modules.engine.MSCHornRollbackConfig;
import com.meituan.msc.modules.engine.MSCRuntime;
import com.meituan.msc.modules.reporter.MSCLog;
import com.meituan.msi.api.location.MsiLocation;
import com.meituan.msi.provider.LocationLoaderConfig;

import java.util.concurrent.CountDownLatch;
import java.util.concurrent.TimeUnit;

public class MSCLocationValueParser extends MSCBaseValueParser {
    //定位相关信息
    //经度。有实时值时用实时值，未取到实时值时用缓存值。
    private static final String SUPPORT_PARAM_LOCATION_LONGITUDE = "location.longitude";
    //纬度。有实时值时用实时值，未取到实时值时用缓存值
    private static final String SUPPORT_PARAM_LOCATION_LATITUDE = "location.latitude";
    //真实定位经度。
    private static final String SUPPORT_PARAM_ACTUAL_LOCATION_LONGITUDE = "actual_location.longitude";
    //真实定位纬度。
    private static final String SUPPORT_PARAM_ACTUAL_LOCATION_LATITUDE = "actual_location.latitude";
    //经度缓存值。使用缓存值，不发起定位请求
    private static final String SUPPORT_PARAM_LAST_LOCATION_LONGITUDE = "last_location.longitude";
    //纬度缓存值。使用缓存值，不发起定位请求
    private static final String SUPPORT_PARAM_LAST_LOCATION_LATITUDE = "last_location.latitude";
    // 定位经度，优先缓存，缓存过期或无缓存场景取最新定位
    public static final String SUPPORT_PARAM_CACHE_FIRST_LONGITUDE = "cache_first_location.longitude";
    // 定位纬度，优先缓存，缓存过期或无缓存场景取最新定位
    public static final String SUPPORT_PARAM_CACHE_FIRST_LATITUDE = "cache_first_location.latitude";

    private static String[] SUPPORT_PARAMS =  {
            SUPPORT_PARAM_LOCATION_LONGITUDE,
            SUPPORT_PARAM_LOCATION_LATITUDE,
            SUPPORT_PARAM_ACTUAL_LOCATION_LONGITUDE,
            SUPPORT_PARAM_ACTUAL_LOCATION_LATITUDE,
            SUPPORT_PARAM_LAST_LOCATION_LONGITUDE,
            SUPPORT_PARAM_LAST_LOCATION_LATITUDE,
            SUPPORT_PARAM_CACHE_FIRST_LONGITUDE,
            SUPPORT_PARAM_CACHE_FIRST_LATITUDE
    };

    private DataPrefetchConfig.LocationConfig locationConfig = new DataPrefetchConfig.LocationConfig();

    private MsiLocation lastLocation;
    private MsiLocation actualLocation;
    private volatile boolean hasGetLastLocation = false;
    private volatile boolean hasGetActualLocation = false;
    private MSCRuntime mscRuntime;

    public MSCLocationValueParser(DataPrefetchConfig.LocationConfig locationConfig, MSCRuntime mscRuntime) {
        if (locationConfig != null) {
            this.locationConfig = locationConfig;
        }

        this.mscRuntime = mscRuntime;
    }

    @Override
    public boolean isSupport(String param) {
        for (String supportParam: SUPPORT_PARAMS) {
            if (TextUtils.equals(supportParam, param)) {
                return true;
            }
        }

        return false;
    }

    @Override
    public Double getValue(String param) {
        if (TextUtils.equals(param, SUPPORT_PARAM_LOCATION_LONGITUDE)){
            return getLocationLongitude();
        } else if (TextUtils.equals(param, SUPPORT_PARAM_LOCATION_LATITUDE)) {
            return getLocationLatitude();
        } else if (TextUtils.equals(param, SUPPORT_PARAM_ACTUAL_LOCATION_LONGITUDE)) {
            return getActualLocationLongitude();
        } else if (TextUtils.equals(param, SUPPORT_PARAM_ACTUAL_LOCATION_LATITUDE)) {
            return getActualLocationLatitude();
        } else if (TextUtils.equals(param, SUPPORT_PARAM_LAST_LOCATION_LONGITUDE)) {
            return getLastLocationLongitude();
        } else if (TextUtils.equals(param, SUPPORT_PARAM_LAST_LOCATION_LATITUDE)) {
            return getLastLocationLatitude();
        }

        return null;
    }

    @Override
    public Object getValue(String param, PrefetchURLConfig urlConfig) {
        if (TextUtils.equals(param, SUPPORT_PARAM_CACHE_FIRST_LONGITUDE)) {
            return getLocationLongitudeWithCacheFirst(urlConfig);
        } else if (TextUtils.equals(param, SUPPORT_PARAM_CACHE_FIRST_LATITUDE)) {
            return getLocationLatitudeWithCacheFirst(urlConfig);
        }
        return null;
    }

    private Double getLocationLongitude(){
        getActualLocation();
        if (actualLocation == null){
            getLastLocation();
            if (lastLocation != null) {
                return lastLocation.longitude;
            }
        } else {
            return actualLocation.longitude;
        }
        return null;
    }

    private Double getLocationLatitude(){
        getActualLocation();
        if (actualLocation == null){
            getLastLocation();
            if (lastLocation != null) {
                return lastLocation.latitude;
            }
        } else {
            return actualLocation.latitude;
        }
        return null;
    }

    private Double getLocationLatitudeWithCacheFirst(PrefetchURLConfig urlConfig){
        MsiLocation cacheLocation = LocationUtils.getCacheLocation(locationConfig.sceneToken, locationConfig.type);
        if (cacheLocation == null) {
            return getActualLocationLatitude();
        }
        Long validTime = urlConfig.cacheLocationValidTime;
        // 单个请求配置如果为空，取sharedConfig
        if (validTime == null) {
            validTime = locationConfig.cacheLocationValidTime;
        }
        // sharedConfig如果为空，默认永不过期
        if (validTime == null) {
            validTime = -1L;
        }
        Long now = System.currentTimeMillis();
        Long cacheTime = cacheLocation.mtTimestamp;
        if (now - cacheTime < validTime || validTime == -1) {
            return cacheLocation.latitude;
        }
        return getActualLocationLatitude();
    }

    private Double getLocationLongitudeWithCacheFirst(PrefetchURLConfig urlConfig){
        MsiLocation cacheLocation = LocationUtils.getCacheLocation(locationConfig.sceneToken, locationConfig.type);
        if (cacheLocation == null) {
            return getActualLocationLongitude();
        }
        Long validTime = urlConfig.cacheLocationValidTime;
        // 单个请求配置如果为空，取sharedConfig
        if (validTime == null) {
            validTime = locationConfig.cacheLocationValidTime;
        }
        // sharedConfig如果为空，默认永不过期
        if (validTime == null) {
            validTime = -1L;
        }
        Long now = System.currentTimeMillis();
        Long cacheTime = cacheLocation.mtTimestamp;
        if (now - cacheTime < validTime || validTime == -1) {
            return cacheLocation.longitude;
        }
        return getActualLocationLongitude();
    }

    private Double getLastLocationLongitude(){
        getLastLocation();
        if (lastLocation != null) {
            return lastLocation.longitude;
        }
        return null;
    }

    private Double getLastLocationLatitude(){
        getLastLocation();
        if (lastLocation != null) {
            return lastLocation.latitude;
        }
        return null;
    }

    private Double getActualLocationLongitude(){
        getActualLocation();
        if (actualLocation != null){
            return actualLocation.longitude;
        }
        return null;
    }

    private Double getActualLocationLatitude(){
        getActualLocation();
        if (actualLocation != null){
            return actualLocation.latitude;
        }
        return null;
    }

    private void getActualLocation() {
        //一次数据预拉取已经获取过
        if (hasGetActualLocation) {
            return;
        }

        if (!LocationUtils.isGrantedLocationPermission(MSCEnvHelper.getContext(), locationConfig.sceneToken)) {
            String errMsg = "auth denied before request location";
            MSCLog.i("MSCDynamicDataPrefetch", errMsg);
            return;
        }

        String type = locationConfig.type;
        if (TextUtils.isEmpty(type)) {
            type = ILocationLoader.TYPE_WGS84;
        }

        LocationLoaderConfig loaderConfig = new LocationLoaderConfig();
        loaderConfig.token = locationConfig.sceneToken;
        loaderConfig.loadStrategy = LocationLoaderConfig.LoadStrategy.normal;

        if (mscRuntime == null) {
            return;
        }

        Activity activity = mscRuntime.getContainerManagerModule().getTopActivity();
        final ILocationLoader locationLoader = MSCEnvHelper.getILocationLoaderProvider().create(activity, loaderConfig);
        if (locationLoader == null) {
            String errMsg = "location failed, locationLoader is null";
            MSCLog.i("MSCDynamicDataPrefetch", errMsg);
            return;
        }
        MSCTraceUtil.begin("DataPrefetchGetActualLocation");
        CountDownLatch countDownLatch = new CountDownLatch(1);
        MSCLog.i("MSCDynamicDataPrefetch", "startLocation type: " + type + " sceneToken: " + loaderConfig.token);
        locationLoader.startLocation(new ILocation() {
            @Override
            public void onLocation(int error, MsiLocation location, String errMsg) {
                MSCTraceUtil.end("DataPrefetchGetActualLocation");
                locationLoader.stopLocation();
                if (error == 0) {
                    if (location == null) {
                        return;
                    }
                    actualLocation = location;
                } else {
                    if (!LocationUtils.isGrantedLocationPermission(MSCEnvHelper.getContext(), locationConfig.sceneToken)) {
                        errMsg = "auth denied after request location";
                    }
                    MSCLog.i("MSCDynamicDataPrefetch", errMsg);
                }
                countDownLatch.countDown();
            }
        }, type);

        try {
            countDownLatch.await(30, TimeUnit.SECONDS);
        } catch (InterruptedException interruptedException) {
            interruptedException.printStackTrace();
            MSCLog.i("MSCDynamicDataPrefetch", "getLocation timeout");
            MSCTraceUtil.end("DataPrefetchGetActualLocation");
        }
        hasGetActualLocation = true;
    }

    private void getLastLocation() {
        //一次数据预拉取发起一次
        if (hasGetLastLocation) {
            return;
        }
        MSCTraceUtil.begin("DataPrefetchGetLastLocation");
        if (MSCHornRollbackConfig.enablePrefetchOptimizer()) {
            MsiLocation cacheLocation = LocationUtils.getCacheLocation(locationConfig.sceneToken, locationConfig.type);
            if (cacheLocation != null) {
                lastLocation = cacheLocation;
            }
        } else {
            MtLocation mtLocation = MtLocationCache.getInstance().getLastKnownLocation(locationConfig.sceneToken);
            if (mtLocation != null) {
                lastLocation = new MsiLocation();
                lastLocation.longitude = mtLocation.getLongitude();
                lastLocation.latitude = mtLocation.getLatitude();
            }
        }
        hasGetLastLocation = true;
        MSCTraceUtil.end("DataPrefetchGetLastLocation");
    }
}
