package com.meituan.msc.modules.engine.dataprefetch;

public class MSCPrefetchPhaseRecord {
    public String requestUrl;
    //路由开始时间
    public long routeTime;
    //数据预拉取开始处理时间
    public long startPrefetchTime;
    //获取预拉取配置dio开始时间
    public long startFetchConfigTime;
    //解析预拉取配置开始时间
    public long startParseConfigTime;

    //url 开始处理时间
    public long startParseValueTime;
    //url msi request发起时间
    public long startMsiRequestTime;
    //url msi request收到结果的时间
    public long endMsiRequestTime;
    // 发送数据到前端的时间
    public long sendDataToFeTime;

    MSCPrefetchPhaseRecord(long routeTime) {
        this.routeTime = routeTime;
    }

    MSCPrefetchPhaseRecord(MSCPrefetchPhaseRecord phaseRecord) {
        this.routeTime = phaseRecord.routeTime;
        this.startPrefetchTime = phaseRecord.startPrefetchTime;
        this.startFetchConfigTime = phaseRecord.startFetchConfigTime;
        this.startParseConfigTime = phaseRecord.startParseConfigTime;
        this.startParseValueTime = phaseRecord.startParseValueTime;
        this.startMsiRequestTime = phaseRecord.startMsiRequestTime;
        this.endMsiRequestTime = phaseRecord.endMsiRequestTime;
    }
}
