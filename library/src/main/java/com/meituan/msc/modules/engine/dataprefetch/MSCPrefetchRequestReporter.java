package com.meituan.msc.modules.engine.dataprefetch;

import com.meituan.msc.common.utils.PathUtil;
import com.meituan.msc.modules.engine.MSCRuntime;
import com.meituan.msc.modules.reporter.CommonTags;
import com.meituan.msc.modules.reporter.MSCCommonTagReporter;
import com.meituan.msc.modules.reporter.MSCReporter;
import com.meituan.msc.modules.reporter.ReporterFields;

public class MSCPrefetchRequestReporter extends MSCCommonTagReporter {
    public static final int ERROR_CODE_PREFETCH_SUCCESS = 1000;
    public static final int ERROR_CODE_PREFETCH_PARSE_URL_FAIL = 1020;
    public static final int ERROR_CODE_PREFETCH_MSI_REQUEST_FAIL = 1030;

    public static final int ERROR_CODE_PREFETCH_CONFIG_FETCH_FAIL = 1010;
    public static final int ERROR_CODE_PREFETCH_CONFIG_PARSE_FAIL = 1020;
    public static final int ERROR_CODE_PREFETCH_CONFIG_FETCH_TIMEOUT_FAIL = 1030;


    private MSCPrefetchRequestReporter(CommonTags commonTags) {
        super(commonTags);
    }

    public static MSCPrefetchRequestReporter create(MSCRuntime runtime) {
        return new MSCPrefetchRequestReporter(CommonTags.build(runtime));
    }

    public void reportDynamicPrefetchSuccessRate(@ReportValue int value, String pagePath, String requestUrlWithoutQuery,
                                                 String configUrl, int errorCode, String errorMsg) {
        record(ReporterFields.REPORT_DYNAMIC_PARSE_PREFETCH_SUCCESS_RATE)
                .value(value)
                .tag("pagePath", pagePath)
                .tag("purePath", PathUtil.getPath(pagePath))
                .tag("url", requestUrlWithoutQuery)
                .tag("configUrl", configUrl)
                .tag("errorCode", errorCode)
                .tag("errorMsg", errorMsg)
                .sendRealTime();
    }

    public void reportDynamicPrefetchDuration(String pagePath, String requestUrlWithoutQuery,
                                              String configUrl, MSCPrefetchPhaseRecord phaseRecord) {
        if (phaseRecord == null) {
            return;
        }
        //开始预拉取处理--> msi request结果返回 时间
        long value = phaseRecord.endMsiRequestTime - phaseRecord.startPrefetchTime;
        //页面路由至开始预拉取的耗时
        long routeToPrefetchTime = phaseRecord.startPrefetchTime - phaseRecord.routeTime;
        //获取预拉取配置耗时
        long fetchConfigTime = phaseRecord.startParseConfigTime - phaseRecord.startFetchConfigTime;
        //解析预拉取配置耗时
        long parseConfigTime = phaseRecord.startParseValueTime - phaseRecord.startParseConfigTime;
        //解析预拉取变量标识耗时
        long parseValueTime = phaseRecord.startMsiRequestTime - phaseRecord.startParseValueTime;
        //msi请求耗时
        long requestTime = phaseRecord.endMsiRequestTime - phaseRecord.startMsiRequestTime;

        record(ReporterFields.REPORT_DYNAMIC_PARSE_PREFETCH_DURATION)
                .value(value)
                .tag("pagePath", pagePath)
                .tag("purePath", PathUtil.getPath(pagePath))
                .tag("url", requestUrlWithoutQuery)
                .tag("configUrl", configUrl)
                .tag("routeToPrefetchTime", routeToPrefetchTime)
                .tag("fetchConfigTime", fetchConfigTime)
                .tag("parseConfigTime", parseConfigTime)
                .tag("parseValueTime", parseValueTime)
                .tag("requestTime", requestTime)
                .sendDelay();
    }

    public void reportFetchConfigSuccessRate(@MSCReporter.ReportValue int value, int errorCode, String errorMsg) {
        record(ReporterFields.REPORT_DYNAMIC_PARSE_CONFIG_SUCCESS_RATE)
                .value(value)
                .tag("errorCode", errorCode)
                .tag("errorMsg", errorMsg)
                .sendRealTime();
    }
}