package com.meituan.msc.modules.engine.dataprefetch;

import android.support.annotation.NonNull;
import android.text.TextUtils;

import com.meituan.msc.lib.interfaces.prefetch.MSCBaseValueParser;
import com.meituan.msc.common.utils.PathUtil;

import java.util.Map;

public class MSCQueryValueParser extends MSCBaseValueParser {
    private static final String QUERY_PREFIX = "query.";
    private static final String QUERY_KEY = "query";

    private Map<String, String> queryMap;
    private String targetPath;

    public MSCQueryValueParser(String targetPath) {
        this.targetPath = targetPath;
    }

    @Override
    public boolean isSupport(String param) {
        if (param != null && (param.startsWith(QUERY_PREFIX)) || TextUtils.equals(param, QUERY_KEY)) {
            return true;
        }

        return false;
    }

    @Override
    public Object getValue(String param) {
        if (queryMap == null) {
            queryMap = PathUtil.getQueryParameterToMap(targetPath);
        }

        String queryKey = getQueryKey(param);
        if (queryKey != null && queryMap != null){
            return queryMap.get(queryKey);
        }

        if (TextUtils.equals(param, QUERY_KEY)) {
            return PathUtil.getQuery(targetPath);
        }

        return null;
    }

    private String getQueryKey(@NonNull String param) {
        if (param != null && param.startsWith(QUERY_PREFIX)) {
            return param.substring(QUERY_PREFIX.length());
        }

        return null;
    }
}
