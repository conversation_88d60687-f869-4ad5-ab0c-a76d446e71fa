package com.meituan.msc.modules.engine.dataprefetch;

import android.text.TextUtils;

public class PrefetchRequestRes {
    boolean success;
    String reason;
    String url;
    String data;
    String configUrl;

    //success/fail/pending
    String status;

    public PrefetchRequestRes(String configUrl){
        this.configUrl = configUrl;
        this.status = "pending";
    }

    public void setRealRequestUrl(String url) {
        this.url = url;
    }

    public boolean isFinish(){
        return !TextUtils.equals(this.status, "pending");
    }
}
