package com.meituan.msc.modules.engine.dataprefetch.msi;

import android.support.annotation.Keep;

import com.google.gson.JsonObject;

/**
 * MSI api 入参
 */
@Keep
public class MSIApiRequest {
    String name;
    String scope;
    Object args;
    String callbackId;
    JsonObject innerArgs = new JsonObject();

    public static MSIApiRequest create(String name, String scope, Object args){
        MSIApiRequest msiApiRequestNode = new MSIApiRequest();
        msiApiRequestNode.name = name;
        msiApiRequestNode.scope = scope;
        msiApiRequestNode.args = args;
        msiApiRequestNode.callbackId = "1000";
        return msiApiRequestNode;
    }

    public void setInnerArgs(JsonObject innerArgs){
        this.innerArgs = innerArgs;
    }
}
