package com.meituan.msc.modules.engine.dataprefetch.msi;

import android.support.annotation.Keep;

import com.google.gson.annotations.SerializedName;

import java.util.Map;

/**
 * msi api 出参
 * @param <T>
 */
@Keep
public class MSIApiResponse<T> {
    // API callbackId
    String callbackId;
    // API 调用方式
    @SerializedName("type")
    String invokeType;
    // API 调用码
    @SerializedName("code")
    public int statusCode;
    // API 正常响应
    @SerializedName("data")
    public T responseBody;
    // API 错误响应
    @SerializedName("msg")
    public String statusMsg;
    // 内部参数,比如taskId、instanceId
    Map innerData;
    Map uiData;
}
