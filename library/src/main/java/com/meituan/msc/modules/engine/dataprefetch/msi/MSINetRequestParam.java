package com.meituan.msc.modules.engine.dataprefetch.msi;

import android.support.annotation.Keep;

import com.google.gson.JsonElement;

import java.util.Map;

/**
 * msi request api 入参
 */
@Keep
public class MSINetRequestParam {
    /**
     * 开发者服务器接口地址
     */
    public String url;
    /**
     * 请求的参数
     */
    public JsonElement data;
    /**
     * 设置请求的 header，header 中不能设置 Referer。
     * content-type 默认为 application/json
     */
    public Map<String, String> header;
    /**
     * 超时时间，单位为毫秒, 默认5S
     */
    public int timeout = 5000;

    /**
     * HTTP 请求方法
     */
    public String method = "GET";

    /**
     * 是否启用shark
     */
    public boolean enableShark = true;


    /**
     * 返回的数据格式
     */
    public String dataType = "json";

    /**
     * 是否使用mtgaurd验签功能
     */
    public boolean mtSecuritySign = false;

    /**
     * 是否使用mtgaurdsiua功能
     */
    public boolean mtSecuritySiua = false;

    //POST 请求body的数据类型
    public String requestDataType;

    public Map<String, Object> _mt;
}
