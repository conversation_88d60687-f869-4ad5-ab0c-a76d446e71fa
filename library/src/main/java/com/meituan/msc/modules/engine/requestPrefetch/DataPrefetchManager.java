package com.meituan.msc.modules.engine.requestPrefetch;

import android.app.Activity;
import android.content.Intent;
import android.support.annotation.NonNull;
import android.support.annotation.Nullable;
import android.text.TextUtils;

import com.meituan.msc.common.config.SceneNumber;
import com.meituan.msc.common.process.MSCProcess;
import com.meituan.msc.common.utils.IntentUtil;
import com.meituan.msc.lib.interfaces.container.MSCParams;
import com.meituan.msc.modules.apploader.events.AppLoadException;
import com.meituan.msc.modules.container.ContainerDebugLaunchData;
import com.meituan.msc.modules.engine.MSCHornRollbackConfig;
import com.meituan.msc.modules.engine.MSCRuntime;
import com.meituan.msc.modules.engine.RuntimeManager;
import com.meituan.msc.modules.page.render.MSCHornPerfConfig;
import com.meituan.msc.modules.reporter.MSCLog;
import com.meituan.msc.modules.update.MSCAppModule;
import com.meituan.msc.modules.update.bean.AppMetaInfoWrapper;
import com.meituan.msc.modules.update.bean.CheckUpdateParams;
import com.meituan.msc.modules.update.metainfo.AppCheckUpdateManager;
import com.meituan.msc.modules.update.metainfo.CheckUpdateCallback;

public class DataPrefetchManager {
    private static final String TAG = "DataPrefetchManager";

    private static final PageOutSideRequestPrefetchManagerCacheManager REQUEST_PREFETCH_MANAGER_CACHE_MANAGER = PageOutSideRequestPrefetchManagerCacheManager.getInstance();

    private static boolean lastPrefetchDataWithoutRuntime = false;
    private static boolean lastPrefetchDataWithoutMetaInfo = false;
    // 拆分出两个场景，两者缺少一个，就会走到无runtime的数据预拉取
    private DataPrefetchManager() {

    }

    private static void log(Object... objects) {
        MSCLog.i(TAG, objects);
    }

    /**
     * 获取页面外预拉取的runtime是否存在
     * @return 是否存在
     */
    public static boolean getPrefetchDataWithoutRuntime() {
        return lastPrefetchDataWithoutRuntime;
    }
    /**
     * 获取页面外预拉取时metaInfo是否存在
     * @return 是否存在
     */
    public static boolean getPrefetchDataWithoutMetaInfo() {
        return lastPrefetchDataWithoutMetaInfo;
    }

    /**
     * 从metlauncher中调用的接口，有问题，先空实现
     *
     * @param activity
     * @param intent
     */
    @Deprecated
    public static void prefetchDataIfNeeded(@Nullable Activity activity, Intent intent) {

    }

    /**
     * 从闪购路由决策时机调用的接口
     *
     * @param activity   activity 获取定位需要
     * @param appId      appId
     * @param targetPath targetPath 不要是%开头的已经编码的targetPath，要是解码之后的，如 /pages/store/index?isSg=1&channel=waimai
     * @api
     * @api 从闪购路由决策时机调用的接口
     */
    public static void prefetchDataIfNeeded(Activity activity, String appId, String targetPath) {
        prefetchDataIfNeeded(activity, appId, targetPath, true);
    }

    /**
     * 从闪购路由决策时机调用的接口
     *
     * @param activity   activity 获取定位需要
     * @param appId      appId
     * @param targetPath targetPath 不要是%开头的已经编码的targetPath，要是解码之后的，如 /pages/store/index?isSg=1&channel=waimai
     * @api
     * @api 从闪购路由决策时机调用的接口
     */
    public static void prefetchDataIfNeeded(Activity activity, String appId, String targetPath, boolean bizPreload) {
        log("prefetchDataIfNeeded", "appId:", appId, ", targetPath:", targetPath);
        prefetchDataBeforePageStart(activity, null, appId, targetPath, SceneNumber.DEFAULT, false, TriggerPrefetchDataScene.PAGE_OUTSIDE);
    }

    /**
     * 不依赖运行时执行数据预拉取
     *
     * @param activity
     * @param appId
     * @param targetPath
     * @param triggerPrefetchDataScene
     */
    private static void prefetchDataWithoutRuntime(Activity activity, String appId, String targetPath, TriggerPrefetchDataScene triggerPrefetchDataScene) {
        RequestPrefetchManager requestPrefetchManager = new RequestPrefetchManager();
        // 先将 RequestPrefetchManager 缓存起来，进入页面后方便 MSCRuntime 立即获取到
        REQUEST_PREFETCH_MANAGER_CACHE_MANAGER.cacheData(appId, requestPrefetchManager);
        requestPrefetchManager.reset();
        // 从DD获取MetaInfo
        CheckUpdateParams checkUpdateParams = new CheckUpdateParams(appId, CheckUpdateParams.Type.CACHE_OR_NETWORK);
        AppCheckUpdateManager.getInstance().checkUpdate(checkUpdateParams, new CheckUpdateCallback<AppMetaInfoWrapper>() {
            @Override
            public void onSuccess(@NonNull AppMetaInfoWrapper metaInfo) {
                MSCLog.i(TAG, "FetchMetaInfo Success, start to prefetch data, appId:", appId, ", targetPath:", targetPath);
                requestPrefetchManager.startPrefetchBeforeActivityCreate(activity, metaInfo, targetPath, SceneNumber.DEFAULT, triggerPrefetchDataScene);
            }

            @Override
            public void onFail(String errMsg, AppLoadException error) {
                MSCLog.w(TAG, error, "FetchMetaInfo Failed, appId:", appId, ", targetPath:", targetPath);
            }
        });
    }

    /**
     * 在导航处发起数据预拉取
     *
     * @param activity
     * @param intent
     */
    public static void prefetchDataIfNeededInMSCInstrumentation(@Nullable Activity activity, Intent intent) {
        log("prefetchDataIfNeededInMSCInstrumentation");
        String appId = IntentUtil.getStringExtra(intent, MSCParams.APP_ID);
        String targetPath = IntentUtil.getStringExtra(intent, MSCParams.TARGET_PATH);
        int scene = getScene(intent);
        prefetchDataBeforePageStart(activity, intent, appId, targetPath, scene, true, TriggerPrefetchDataScene.ROUTER);
    }

    private static void prefetchDataBeforePageStart(Activity activity, Intent intent, String appId, String targetPath, int scene, boolean abortIfFetchingOrCached, TriggerPrefetchDataScene triggerPrefetchDataScene) {
        if (!enableInCurrentProcess()) {
            log("only support in msc related process");
            return;
        }
        if (!MSCHornPerfConfig.getInstance().useInstrumentPrefetch(appId)) {
            log("do not in instrument prefetch white list");
            return;
        }

        MSCRuntime runtime = RuntimeManager.getRuntimeForLaunch(appId, targetPath, new ContainerDebugLaunchData(intent), false, false, false);
        MSCAppModule appModule = null;
        boolean prefetchDataWithoutRuntime = false;
        boolean prefetchDataWithoutMetaInfo = false;
        if (runtime == null || (appModule = runtime.getMSCAppModule()) == null) {
            log("runtime:", runtime);
            prefetchDataWithoutRuntime = true;
        } else {
            AppMetaInfoWrapper metaInfo = appModule.getMetaInfo();
            if (metaInfo == null) {
                log("metaInfo is null");
                prefetchDataWithoutMetaInfo = true;
            }
        }
        if (prefetchDataWithoutRuntime || prefetchDataWithoutMetaInfo) {
            //如果获取的引擎还未创建MSCAppModule，或没有元信息。且是基础库预热引擎，释放引擎，其他业务可获取使用
            if (runtime !=  null && MSCHornRollbackConfig.enablePrefetchUnlockRuntime()) {
                log("runtime not use。try unlock runtime");
                runtime.unLockWhenNotUsed();
            }

            if (MSCHornRollbackConfig.get().getConfig().rollBackBizPreloadWhenDataPrefetch) {
                log("rollBackBizPreloadWhenDataPrefetch");
                return;
            }
            RequestPrefetchManager requestPrefetchManager = REQUEST_PREFETCH_MANAGER_CACHE_MANAGER.getPrefetchManager(appId);
            if (abortIfFetchingOrCached && requestPrefetchManager != null && requestPrefetchManager.isExistCachedDataOrIsFetchingDataFromPageOutSide(targetPath)) {
                log("exist cached data or isSyncPrefetching");
                return;
            } else {
                // 先通过DD的接口获取缓存或者网络中的MetaInfo，如果能拿到就直接发起数据预拉取
                prefetchDataWithoutRuntime(activity, appId, targetPath, triggerPrefetchDataScene);
            }
        } else {
            RequestPrefetchManager requestPrefetchManager = runtime.getOrCreateRequestPrefetchManager();
            // 新逻辑中，如果当前页面不是闪购商家页，直接退出不提前数据预拉取；如果是闪购商家页且正在发起数据预拉取，也直接退出
            // 如果已经有数据缓存或者正在发起数据预拉取，则退出
            if (abortIfFetchingOrCached && requestPrefetchManager.isExistCachedDataOrIsFetchingDataFromPageOutSide(targetPath)) {
                log("exist cached data or isSyncPrefetching");
                return;
            }
            requestPrefetchManager.reset();
            requestPrefetchManager.startPrefetchBeforeActivityCreate(activity, appModule.getMetaInfo(), targetPath, scene, triggerPrefetchDataScene);
        }
        lastPrefetchDataWithoutRuntime = prefetchDataWithoutRuntime;
        lastPrefetchDataWithoutMetaInfo = prefetchDataWithoutMetaInfo;
    }

    private static int getScene(Intent intent) {
        String srcAppId = IntentUtil.getStringExtra(intent, MSCParams.EXTRA_DATA);
        int scene;
        if (!TextUtils.isEmpty(srcAppId)) {
            scene = SceneNumber.OPEN_FROM_MINI_PROGRAM;
        } else {
            scene = IntentUtil.getIntExtra(intent, MSCParams.SCENE, SceneNumber.DEFAULT);
        }
        return scene;
    }

    private static boolean enableInCurrentProcess() {
        return MSCProcess.isInMainProcess() || MSCProcess.STANDARD.isCurrentProcess();
    }
}
