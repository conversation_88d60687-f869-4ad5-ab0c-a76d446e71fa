package com.meituan.msc.modules.engine.requestPrefetch;

import android.util.LruCache;

/**
 * 用于缓存在页面打开前发起的数据预拉取所使用的 RequestPrefetchManager 对象
 */
public class PageOutSideRequestPrefetchManagerCacheManager {
    private final LruCache<String, RequestPrefetchManager> cache;

    private static final PageOutSideRequestPrefetchManagerCacheManager INSTANCE = new PageOutSideRequestPrefetchManagerCacheManager();
    private static final int DEFAULT_CACHE_NUMBER = 3;

    public static PageOutSideRequestPrefetchManagerCacheManager getInstance() {
        return INSTANCE;
    }

    private PageOutSideRequestPrefetchManagerCacheManager() {
        // 缺省缓存3个
        cache = new LruCache<>(DEFAULT_CACHE_NUMBER);
    }

    public void cacheData(String appId, RequestPrefetchManager requestPrefetchManager) {
        cache.put(appId, requestPrefetchManager);
    }

    public RequestPrefetchManager pollRequestPrefetchManager(String appId) {
        return cache.remove(appId);
    }

    public RequestPrefetchManager getPrefetchManager(String appId) {
        return cache.get(appId);
    }

    public RequestPrefetchManager pollOrCreateRequestPrefetchManager(String appId) {
        RequestPrefetchManager requestPrefetchManager = pollRequestPrefetchManager(appId);
        if (requestPrefetchManager == null) {
            requestPrefetchManager = new RequestPrefetchManager();
        }
        return requestPrefetchManager;
    }
}
