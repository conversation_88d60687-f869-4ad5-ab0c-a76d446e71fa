package com.meituan.msc.modules.engine.requestPrefetch;

import android.support.annotation.Keep;

import com.google.gson.annotations.SerializedName;
import com.meituan.msc.lib.interfaces.requestprefetch.IPrefetchConfig;
import com.meituan.msc.modules.engine.MSCHornRollbackConfig;

import java.util.ArrayList;
import java.util.List;
import java.util.Map;

/**
 * 数据预拉取配置
 * "externalConfig": {
 *      "fusion": true,
 *      "prefetch": {
 *      "method":"GET"
 *      "keyMap": {},
 *      "enableShark": true,
 *      "enableSecuritySign": true,
 *      "location": {
 *          "enable": false,
 *          "type": "gcj02"
 *          "sceneToken": ""
 *      },
 *      "enableSecuritySiua": true,
 *      "url": "https://thh.meituan.com/api/mtmall/product/v4/detail",
 *      "timeout": 5000
 * }
 */
@Keep
public class PrefetchConfig extends IPrefetchConfig {
    public static final String PREFETCH_METHOD_GET = "GET";
    public static final String PREFETCH_METHOD_POST = "POST";

    public static final String PREFETCH_POST_CONTENT_TYPE_JSON = "application/json";
    public static final String PREFETCH_POST_CONTENT_TYPE_FORM = "application/x-www-form-urlencoded";

    private interface PrefetchConfigKey {
        String PAGE_PATH = "pagePath";
        String URL = "url";
        String KEY_MAP = "keyMap";
        String ENABLE_SHARK = "enableShark";
        String TIMEOUT = "timeout";
        String LOCATION = "location";
        String METHOD = "method";
        String CONTENT_TYPE = "contentType";
        String ENABLE_SECURITY_SIGN = "enableSecuritySign";
        String ENABLE_SECURITY_SIUA = "enableSecuritySiua";
        String COLOR_TAGS = "colorTags";
    }

    private interface LocationConfigKey {
        String ENABLE = "enable";
        String TYPE = "type";
        String SCENE_TOKEN = "sceneToken";
        String ENABLE_CACHE = "enableCache";
        String CACHE_DURATION = "cacheDuration";
    }

    @SerializedName(PrefetchConfigKey.PAGE_PATH)
    public String pagePath;

    @SerializedName(PrefetchConfigKey.URL)
    public String url;

    @SerializedName(PrefetchConfigKey.KEY_MAP)
    public Map<String, String> keyMap;

    @SerializedName(PrefetchConfigKey.ENABLE_SHARK)
    public boolean enableShark;

    @SerializedName(PrefetchConfigKey.TIMEOUT)
    public long timeout;    //单位：ms

    @SerializedName(PrefetchConfigKey.LOCATION)
    public LocationConfig locationConfig;

    @SerializedName(PrefetchConfigKey.METHOD)
    public String method = PREFETCH_METHOD_GET;

    @SerializedName(PrefetchConfigKey.CONTENT_TYPE)
    public String contentType = PREFETCH_POST_CONTENT_TYPE_JSON;

    @Keep
    public static class LocationConfig extends ILocationConfig {
        @SerializedName(LocationConfigKey.ENABLE)
        public boolean enable;

        @SerializedName(LocationConfigKey.TYPE)
        public String type;

        @SerializedName(LocationConfigKey.SCENE_TOKEN)
        public String sceneToken;

        @SerializedName(LocationConfigKey.ENABLE_CACHE)
        public boolean enableCache;

        // 单位：ms，定位缓存有效期
        @SerializedName(LocationConfigKey.CACHE_DURATION)
        public Long cacheDuration = -1L;

        @Override
        public boolean isEnable() {
            return enable;
        }

        @Override
        public String getType() {
            return type;
        }

        @Override
        public String getSceneToken() {
            return sceneToken;
        }
    }

    @SerializedName(PrefetchConfigKey.ENABLE_SECURITY_SIGN)
    public boolean enableSecuritySign;

    @SerializedName(PrefetchConfigKey.ENABLE_SECURITY_SIUA)
    public boolean enableSecuritySiua;

    @SerializedName(PrefetchConfigKey.COLOR_TAGS)
    public List<String> colorTags;


    @Override
    public String getPagePath() {
        return pagePath;
    }

    public String getMethod() {
        return method.toUpperCase();
    }

    public String getContentType() {
        return contentType;
    }

    @Override
    public String getUrl() {
        return url;
    }

    @Override
    public Map<String, String> getKeyMap() {
        return keyMap;
    }

    @Override
    public boolean isEnableShark() {
        return enableShark;
    }

    @Override
    public long getTimeout() {
        return timeout;
    }

    @Override
    public ILocationConfig getLocation() {
        return locationConfig;
    }

    @Override
    public boolean isEnableSecuritySign() {
        return enableSecuritySign;
    }

    @Override
    public boolean isEnableSecuritySiua() {
        return enableSecuritySiua;
    }

    public static PrefetchConfig parse(Map<String, Object> map) throws IllegalArgumentException, ClassCastException {
        PrefetchConfig config = new PrefetchConfig();
        config.pagePath = getAsString(map.get(PrefetchConfigKey.PAGE_PATH));
        config.url = getAsString(map.get(PrefetchConfigKey.URL));
        config.enableShark = getAsBoolean(map.get(PrefetchConfigKey.ENABLE_SHARK));
        config.timeout = getAsLong(map.get(PrefetchConfigKey.TIMEOUT));
        String method = getAsString(map.get(PrefetchConfigKey.METHOD));
        if (method != null) {
            config.method = method;
        }
        String contentType = getAsString(map.get(PrefetchConfigKey.CONTENT_TYPE));
        if (contentType != null) {
            config.contentType = contentType;
        }
        config.enableSecuritySign = getAsBoolean(map.get(PrefetchConfigKey.ENABLE_SECURITY_SIGN));
        config.enableSecuritySiua = getAsBoolean(map.get(PrefetchConfigKey.ENABLE_SECURITY_SIUA));
        config.colorTags = getAsStringList(map.get(PrefetchConfigKey.COLOR_TAGS));
        LocationConfig config2 = new LocationConfig();
        config.locationConfig = config2;
        Map<String, Object> locationMap = (Map<String, Object>) map.get(PrefetchConfigKey.LOCATION);
        if (locationMap != null) {
            config2.enable = getAsBoolean(locationMap.get(LocationConfigKey.ENABLE));
            config2.type = getAsString(locationMap.get(LocationConfigKey.TYPE));
            config2.sceneToken = getAsString(locationMap.get(LocationConfigKey.SCENE_TOKEN));
            if (MSCHornRollbackConfig.enablePrefetchOptimizer()) {
                config2.enableCache = getAsBoolean(locationMap.get(LocationConfigKey.ENABLE_CACHE));
                config2.cacheDuration = locationMap.get(LocationConfigKey.CACHE_DURATION) != null ? getAsLong(locationMap.get(LocationConfigKey.CACHE_DURATION)) : -1;
            }
        }
        config.keyMap = (Map<String, String>) map.get(PrefetchConfigKey.KEY_MAP);
        return config;
    }

    private static String getAsString(Object object) throws IllegalArgumentException {
        if (object == null) {
            return null;
        } else if (object instanceof String) {
            return (String) object;
        } else if (object instanceof Number || object instanceof Boolean || object instanceof Character) {
            return String.valueOf(object);
        } else {
            throw new IllegalArgumentException("only support String, Number, Boolean or Character");
        }
    }

    private static long getAsLong(Object object) throws IllegalArgumentException {
        if (object == null) {
            return 0;
        } else if (object instanceof Number) {
            return ((Number) object).longValue();
        } else if (object instanceof String) {
            return Long.parseLong((String) object);
        } else if (object instanceof Character) {
            return Long.parseLong(String.valueOf(object));
        } else {
            throw new IllegalArgumentException("only support String or Number");
        }
    }

    private static boolean getAsBoolean(Object object) {
        if (object == null) {
            return false;
        } else if (object instanceof Boolean) {
            return (Boolean) object;
        } else if (object instanceof String) {
            return Boolean.parseBoolean((String) object);
        } else {
            return false;
        }
    }

    private static List<String> getAsStringList(Object object) throws IllegalArgumentException {
        if (object == null) {
            return null;
        } else if (object instanceof List) {
            List<?> list = (List<?>) object;
            List<String> result = new ArrayList<>();
            for (Object item : list) {
                if (item instanceof String) {
                    result.add((String) item);
                } else {
                    throw new IllegalArgumentException("List contains non-String element: " + item);
                }
            }
            return result;
        } else {
            throw new IllegalArgumentException("Only support List<String>");
        }
    }
}
