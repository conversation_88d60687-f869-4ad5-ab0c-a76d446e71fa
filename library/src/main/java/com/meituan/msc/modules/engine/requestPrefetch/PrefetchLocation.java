package com.meituan.msc.modules.engine.requestPrefetch;

import android.app.Activity;
import android.text.TextUtils;

import com.meituan.msc.extern.MSCEnvHelper;
import com.meituan.msc.modules.api.map.ILocation;
import com.meituan.msc.modules.api.map.ILocationLoader;
import com.meituan.msc.modules.reporter.MSCLog;
import com.meituan.msc.util.perf.PerfTrace;
import com.meituan.msi.api.location.MsiLocation;
import com.meituan.msi.provider.LocationLoaderConfig;
import com.meituan.msi.util.LocationUtils;

import static com.meituan.msc.util.perf.PerfEventName.REQUEST_PREFETCH_LOCATE;

/**
 * 数据预拉取获取定位信息类
 */
public class PrefetchLocation {
    public static final String LOCATION_TYPE_CACHE = "cache";
    public static final String LOCATION_TYPE_REAL = "real";
    public static final String LOCATION_TYPE_FAIL = "fail";
    public String locationType = "";

    /**
     * 需要定位参数
     *
     * @return
     */
    public boolean needLocationParams(PrefetchConfig.LocationConfig locationConfig) {
        return locationConfig != null && locationConfig.enable;
    }


    public void getLocation(Activity activity,
                            RequestPrefetchManager.PrefetchListener prefetchListener,
                            PrefetchParam prefetchParam,
                            RequestRunnable onFinishRunnable) {
        PerfTrace.begin(REQUEST_PREFETCH_LOCATE);
        MSCLog.i("getLocation starting");
        onFinishRunnable
                .setPrefetchParam(prefetchParam)
                .setPrefetchListener(prefetchListener);
        if (activity == null) {
            String errMsg = "activity is null";
            prefetchListener.setLocationType(LOCATION_TYPE_FAIL);
            onFinishRunnable.setErrorMessage(errMsg).run();
            return;
        }
        final PrefetchConfig.LocationConfig locationParam = prefetchParam.prefetchConfig.locationConfig;
        LocationParam locationResult = new LocationParam();
        if (locationParam != null && locationParam.enableCache) {
            MsiLocation cacheLocation = com.meituan.msc.common.utils.LocationUtils.getCacheLocation(locationParam.sceneToken, locationParam.type);
            if (cacheLocation != null && ((System.currentTimeMillis() - cacheLocation.mtTimestamp) < locationParam.cacheDuration || locationParam.cacheDuration == -1)) {
                long currentTime = System.currentTimeMillis();
                locationResult.latitude = cacheLocation.latitude;
                locationResult.longitude = cacheLocation.longitude;
                locationResult.fromCache = true;
                locationResult.readCacheTimeSinceCacheSaved = currentTime - cacheLocation.mtTimestamp;
                prefetchListener.setLocationType(LOCATION_TYPE_CACHE);
                onFinishRunnable.setLocationParam(locationResult).run();
                return;
            }
            if (cacheLocation != null) {
                locationResult.hasCacheButExpired = true;
            }
        }
        final String sceneToken = locationParam != null ? locationParam.sceneToken : "";
        if (!LocationUtils.isGrantedLocationPermission(MSCEnvHelper.getContext(), sceneToken)) {
            String errMsg = "auth denied before request location";
            prefetchListener.setLocationType(LOCATION_TYPE_FAIL);
            onFinishRunnable.setErrorMessage(errMsg).run();
            return;
        }

        String type = locationParam != null ? locationParam.type : "";
        if (TextUtils.isEmpty(type)) {
            type = ILocationLoader.TYPE_WGS84;
        }

        LocationLoaderConfig loaderConfig = new LocationLoaderConfig();
        loaderConfig.token = sceneToken;
        loaderConfig.loadStrategy = LocationLoaderConfig.LoadStrategy.normal;

        final ILocationLoader locationLoader = MSCEnvHelper.getILocationLoaderProvider().create(activity, loaderConfig);
        if (locationLoader == null) {
            String errMsg = "location failed, mini program is not in the foreground";
            prefetchListener.setLocationType(LOCATION_TYPE_FAIL);
            onFinishRunnable.setErrorMessage(errMsg).run();
            return;
        }
        locationLoader.startLocation(new ILocation() {
            @Override
            public void onLocation(int error, MsiLocation location, String errMsg) {
                locationLoader.stopLocation();
                if (error == 0) {
                    if (location == null) {
                        prefetchListener.setLocationType(LOCATION_TYPE_FAIL);
                        onFinishRunnable.setErrorMessage(errMsg).run();
                        return;
                    }
                    locationResult.latitude = location.latitude;
                    locationResult.longitude = location.longitude;
                    locationResult.fromCache = false;
                    prefetchListener.setLocationType(LOCATION_TYPE_REAL);
                    onFinishRunnable.setLocationParam(locationResult).run();
                } else {
                    if (!LocationUtils.isGrantedLocationPermission(MSCEnvHelper.getContext(), sceneToken)) {
                        errMsg = "auth denied after request location";
                    }
                    prefetchListener.setLocationType(LOCATION_TYPE_FAIL);
                    onFinishRunnable.setErrorMessage(errMsg).run();
                }
            }
        }, type);
    }
}
