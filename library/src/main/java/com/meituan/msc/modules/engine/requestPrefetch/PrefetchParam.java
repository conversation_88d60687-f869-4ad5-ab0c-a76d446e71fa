package com.meituan.msc.modules.engine.requestPrefetch;

import com.meituan.msc.lib.interfaces.requestprefetch.IPrefetchConfig;
import com.meituan.msc.lib.interfaces.requestprefetch.IPrefetchEnvInfo;
import com.meituan.msc.modules.update.bean.AppMetaInfoWrapper;

public class PrefetchParam extends IPrefetchEnvInfo {

    PrefetchConfig prefetchConfig;

    String version;

    String path;

    String query;

    int scene;

    String appId;

    AppMetaInfoWrapper metaInfo;

    @Override
    public String getAppId() {
        return appId;
    }

    @Override
    public String getVersion() {
        return version;
    }

    @Override
    public String getPath() {
        return path;
    }

    @Override
    public String getQuery() {
        return query;
    }

    @Override
    public int getScene() {
        return scene;
    }

    @Override
    public IPrefetchConfig getPrefetchConfig() {
        return prefetchConfig;
    }
}
