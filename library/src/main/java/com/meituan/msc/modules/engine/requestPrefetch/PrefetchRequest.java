package com.meituan.msc.modules.engine.requestPrefetch;

import static com.meituan.msc.modules.engine.requestPrefetch.RequestPrefetchManager.TAG_URL;
import static com.meituan.msc.modules.reporter.ReporterFields.REPORT_DURATION_REQUEST_PREFETCH_TOTAL;
import static com.meituan.msc.util.perf.PerfEventName.REQUEST_PREFETCH_CONVERT_BIN_TO_STRING;
import static com.meituan.msc.util.perf.PerfEventName.REQUEST_PREFETCH_GET_BUSINESS_BODY_PARAMS;
import static com.meituan.msc.util.perf.PerfEventName.REQUEST_PREFETCH_GET_BUSINESS_URL_PARAMS;
import static com.meituan.msc.util.perf.PerfEventName.REQUEST_PREFETCH_NETWORK;
import static com.meituan.msc.util.perf.PerfEventName.REQUEST_PREFETCH_PREPARE;

import android.net.Uri;
import android.os.Build;
import android.support.annotation.NonNull;
import android.text.TextUtils;

import com.google.gson.JsonElement;
import com.meituan.msc.common.report.BaseMetricsReporter;
import com.meituan.msc.common.report.MetricsEntry;
import com.meituan.msc.common.support.java.util.concurrent.CompletableFuture;
import com.meituan.msc.common.support.java.util.function.BiConsumer;
import com.meituan.msc.common.utils.ExceptionHelper;
import com.meituan.msc.extern.IMSCUserCenter;
import com.meituan.msc.extern.MSCCallFactory;
import com.meituan.msc.extern.MSCEnvHelper;
import com.meituan.msc.jse.bridge.ConversionUtil;
import com.meituan.msc.lib.interfaces.IRequestPrefetchListener;
import com.meituan.msc.lib.interfaces.requestprefetch.IPrefetchEnvInfo;
import com.meituan.msc.lib.interfaces.requestprefetch.IRequestPrefetchInterceptor;
import com.meituan.msc.lib.interfaces.requestprefetch.RequestParams;
import com.meituan.msc.modules.api.network.FetchTokenResponse;
import com.meituan.msc.modules.api.network.RequestPrefetchModule;
import com.meituan.msc.modules.engine.MSCHornRollbackConfig;
import com.meituan.msc.modules.reporter.CommonTags;
import com.meituan.msc.modules.reporter.MSCLog;
import com.meituan.msc.modules.reporter.ReporterFields;
import com.meituan.msc.util.perf.PerfTrace;
import com.sankuai.meituan.retrofit2.Call;
import com.sankuai.meituan.retrofit2.Callback;
import com.sankuai.meituan.retrofit2.Constant;
import com.sankuai.meituan.retrofit2.FormBody;
import com.sankuai.meituan.retrofit2.Interceptor;
import com.sankuai.meituan.retrofit2.MSCClientCall;
import com.sankuai.meituan.retrofit2.MSIRequestHelper;
import com.sankuai.meituan.retrofit2.MediaType;
import com.sankuai.meituan.retrofit2.Request;
import com.sankuai.meituan.retrofit2.RequestBody;
import com.sankuai.meituan.retrofit2.Response;
import com.sankuai.meituan.retrofit2.ResponseBody;
import com.sankuai.meituan.retrofit2.raw.RawCall;
import com.sankuai.meituan.serviceloader.ServiceLoader;

import java.util.HashMap;
import java.util.List;
import java.util.Map;
import java.util.Set;

/**
 * 数据预拉取发起网络请求类
 */
public class PrefetchRequest {
    private static final String REFERER = "https://mmp.meituan.com/%s/%s/service";
    /**
     * 最近一次请求的 call
     */
    private MSCClientCall clientCall;
    /**
     * 最近一次请求的页面路径
     */
    String lastPagePath;
    /**
     * 最近一次请求的url
     */
    String lastUrl;

    /**
     * 发起预拉取请求
     *
     * @param prefetchParam
     * @param prefetchListener
     * @param locationParam
     */
    public void request(final PrefetchParam prefetchParam,
                        final RequestPrefetchManager.PrefetchListener prefetchListener,
                        final LocationParam locationParam,
                        boolean isAsync) {
        prefetchListener.setStatusState(RequestPrefetchManager.Status.REQUESTING);
        final String appId = prefetchParam.appId;
        MSCLog.i(RequestPrefetchManager.TAG, "start request:", appId);
        BaseMetricsReporter reporter = prefetchListener.getReporter();

        final PrefetchConfig prefetchConfig = prefetchParam.prefetchConfig;
        lastPagePath = prefetchConfig.pagePath;
        lastUrl = prefetchConfig.url;

        final String method = prefetchConfig.getMethod();
        final String targetPath = prefetchParam.path;
        final String query = prefetchParam.query;
        final String extraConfigUrl = prefetchConfig.url;

        final RequestParams.Builder requestBuilder = new RequestParams.Builder()
                .header(Constant.RETROFIT_MT_REQUEST_TIMEOUT, String.valueOf(prefetchConfig.timeout))
                .header("Referer", String.format(REFERER, appId, prefetchParam.version));
        String token = getToken(prefetchParam);
        final String userToken = getUserToken();
        if (!TextUtils.isEmpty(userToken)) {
            requestBuilder.header("t", userToken);
        }

        final long startTimestamp = System.currentTimeMillis();

        // 公共参数
        final Map<String, String> params = getParams(token, prefetchParam, locationParam, startTimestamp);
        // 业务body参数
        final Map<String, String> businessBodyParams;
        // 业务url参数
        final Map<String, String> businessUrlParams;
        final IRequestPrefetchListener requestPrefetchListener = getRequestPrefetchListener(appId);
        if (requestPrefetchListener != null) {
            PerfTrace.online().begin(REQUEST_PREFETCH_GET_BUSINESS_BODY_PARAMS);
            businessBodyParams = requestPrefetchListener.customRequestBodyParams(method, targetPath, query, extraConfigUrl);
            PerfTrace.online().end(REQUEST_PREFETCH_GET_BUSINESS_BODY_PARAMS);
            PerfTrace.online().begin(REQUEST_PREFETCH_GET_BUSINESS_URL_PARAMS);
            businessUrlParams = requestPrefetchListener.customRequestUrlParams(method, targetPath, query, extraConfigUrl);
            PerfTrace.online().end(REQUEST_PREFETCH_GET_BUSINESS_URL_PARAMS);
        } else {
            businessBodyParams = null;
            businessUrlParams = null;
        }
        final String url = getUrl(extraConfigUrl, method, params, businessUrlParams);
        requestBuilder.url(url).method(method);
        if (TextUtils.equals(PrefetchConfig.PREFETCH_METHOD_POST, method)) {
            //post请求，参数放在body中：公共参数+业务参数
            if (businessBodyParams != null && businessBodyParams.size() > 0) {
                params.putAll(businessBodyParams);
            }
            requestBuilder.body(ConversionUtil.getGson().toJsonTree(params));
        }

        final RequestParams originalRequest = requestBuilder.build();

        CompletableFuture<Response<ResponseBody>> responseCompletableFuture = new CompletableFuture<>();
        IRequestPrefetchInterceptor.Chain chain = new IRequestPrefetchInterceptor.Chain() {
            @Override
            @NonNull
            public RequestParams getRequestParams() {
                return originalRequest;
            }

            @Override
            @NonNull
            public IPrefetchEnvInfo getEnvInfo() {
                return prefetchParam;
            }

            @Override
            public void proceed(RequestParams request) {
                PerfTrace.online().end(REQUEST_PREFETCH_PREPARE);
                PerfTrace.online().begin(REQUEST_PREFETCH_NETWORK);
                String locationType = null;
                if (locationParam != null) {
                    locationType = locationParam.fromCache ? "cache" : "network";
                }
                MetricsEntry tags = reporter.record(ReporterFields.REPORT_DURATION_REQUEST_PREFETCH_PREPARE);
                if (locationParam != null && locationParam.readCacheTimeSinceCacheSaved > 0) {
                    tags = tags.tag("readCacheLocationTimeSinceCacheSaved", locationParam.readCacheTimeSinceCacheSaved);
                }
                if (locationParam != null && !locationParam.fromCache && locationParam.hasCacheButExpired) {
                    tags = tags.tag("hasCacheButExpired", true);
                }
                tags.tag(CommonTags.TAG_PAGE_PATH, prefetchConfig.pagePath)
                        .tag(TAG_URL, prefetchConfig.url)
                        .tag("isAsync", isAsync)
                        .tag("locationType", locationType)
                        // 使用总的开始时间点作为阶段开始时间点
                        .durationEnd(REPORT_DURATION_REQUEST_PREFETCH_TOTAL)
                        .sendDelay();
                realRequest(request, prefetchParam, prefetchConfig, responseCompletableFuture, reporter);
            }

            @Override
            public void completeExceptionally(Throwable throwable) {
                responseCompletableFuture.completeExceptionally(throwable);
            }
        };

        IRequestPrefetchInterceptor requestPrefetchInterceptor = getRequestPrefetchInterceptor(appId);
        if (requestPrefetchInterceptor != null) {
            requestPrefetchInterceptor.intercept(chain);
        } else {
            chain.proceed(originalRequest);
        }

        responseCompletableFuture.whenComplete(new BiConsumer<Response<ResponseBody>, Throwable>() {
            @Override
            public void accept(Response<ResponseBody> response, Throwable e) {
                if (e != null) {
                    // fail
                    PrefetchRequest.this.setClientCall(null);
                    prefetchListener.onFail(ExceptionHelper.getMessage(e), targetPath, url);
                } else {
                    // success
                    PrefetchRequest.this.setClientCall(null);
                    FetchTokenResponse fetchTokenResponse = new FetchTokenResponse();
                    ResponseBody responseBody = response.body() != null ? response.body() : response.errorBody();
                    if (responseBody != null) {
                        PerfTrace.begin(REQUEST_PREFETCH_CONVERT_BIN_TO_STRING);
                        fetchTokenResponse.fetchedData = responseBody.string();
                        PerfTrace.end(REQUEST_PREFETCH_CONVERT_BIN_TO_STRING);
                    }
                    fetchTokenResponse.url = extraConfigUrl;
                    if (MSCHornRollbackConfig.enableFixOnBackgroundFetchTimestamp()) {
                        fetchTokenResponse.timeStamp = System.currentTimeMillis();
                    } else {
                        fetchTokenResponse.timeStamp = startTimestamp;
                    }
                    if (!TextUtils.isEmpty(prefetchParam.path)) {
                        fetchTokenResponse.path = prefetchParam.path;
                    }
                    if (!TextUtils.isEmpty(prefetchParam.query)) {
                        fetchTokenResponse.query = prefetchParam.query;
                    }
                    fetchTokenResponse.scene = prefetchParam.scene;
                    prefetchListener.onSuccess(fetchTokenResponse, prefetchParam);
                }
            }
        });
    }

    private static Request buildRequest(RequestParams requestParams, PrefetchConfig prefetchConfig) {
        Request.Builder requestBuilder = new Request.Builder();
        requestBuilder.url(requestParams.url());
        requestBuilder.method(requestParams.method());
        requestBuilder.body(getBody(requestParams.body(), prefetchConfig.contentType));
        requestBuilder.addColorTags(prefetchConfig.colorTags);
        // header
        Map<String, String> headers = requestParams.headers();
        if (headers != null && !headers.isEmpty()) {
            for (Map.Entry<String, String> header : headers.entrySet()) {
                requestBuilder.header(header.getKey(), header.getValue());
            }
        }
        return requestBuilder.build();
    }

    private void realRequest(RequestParams requestParams, PrefetchParam prefetchParam, PrefetchConfig prefetchConfig, CompletableFuture<Response<ResponseBody>> responseCompletableFuture, BaseMetricsReporter reporter) {
        reporter.markDurationStart(ReporterFields.REPORT_DURATION_REQUEST_PREFETCH_REQUEST);
        boolean enableShark = prefetchConfig.enableShark;
        RawCall.Factory callFactory = MSCCallFactory.getApiCallFactory(enableShark);
        Request request = buildRequest(requestParams, prefetchConfig);
        MSCClientCall call = new MSCClientCall(callFactory, getInterceptors(enableShark, prefetchParam, prefetchConfig))
                .setRequest(request);
        call.enqueue(new Callback<ResponseBody>() {
            @Override
            public void onFailure(Call call, Throwable e) {
                PerfTrace.online().end(REQUEST_PREFETCH_NETWORK);
                responseCompletableFuture.completeExceptionally(e);
            }

            @Override
            public void onResponse(Call<ResponseBody> call, Response<ResponseBody> response) {
                PerfTrace.online().end(REQUEST_PREFETCH_NETWORK);
                reporter.record(ReporterFields.REPORT_DURATION_REQUEST_PREFETCH_REQUEST)
                        .tag(CommonTags.TAG_PAGE_PATH, prefetchConfig.pagePath)
                        .tag(TAG_URL, prefetchConfig.url)
                        .durationEnd()
                        .sendDelay();
                responseCompletableFuture.complete(response);
            }
        });
        setClientCall(call);
    }

    /**
     * 获取token
     *
     * @param
     * @return
     */
    private static String getToken(PrefetchParam prefetchParam) {
        String token = RequestPrefetchModule.getBackgroundFetchToken(prefetchParam.metaInfo.getAppId());
        if (TextUtils.isEmpty(token)) {
            IMSCUserCenter userCenter = MSCEnvHelper.getMSCUserCenter();
            if (userCenter != null && userCenter.isLogin()) {
                token = userCenter.getToken();
            }
        }
        return token;
    }

    /**
     * 获取用户token
     *
     * @param
     * @return
     */
    private static String getUserToken() {
        String userToken = "";
        IMSCUserCenter userCenter = MSCEnvHelper.getMSCUserCenter();
        if (userCenter != null && userCenter.isLogin()) {
            userToken = userCenter.getToken();
        }
        return userToken;
    }

    /**
     * 参数
     *
     * @param token
     * @param prefetchParam
     * @param locationParam
     * @param startTimestamp
     * @return
     */
    private static Map<String, String> getParams(String token, PrefetchParam prefetchParam, LocationParam locationParam, long startTimestamp) {
        Map<String, String> params = new HashMap<>();
        // 微信对齐参数
        params.put("appid", prefetchParam.appId);
        params.put("version", prefetchParam.version);

        if (!TextUtils.isEmpty(token)) {
            params.put("token", token);
        }
        params.put("timestamp", String.valueOf(startTimestamp));
        params.put("appVersion", MSCEnvHelper.getEnvInfo().getAppVersionName());
        params.put("appName", MSCEnvHelper.getEnvInfo().getAppName());
        if (!TextUtils.isEmpty(prefetchParam.path)) {
            params.put("path", prefetchParam.path);
        }
        if (!TextUtils.isEmpty(prefetchParam.query)) {
            params.put("query", prefetchParam.query);
        }
        params.put("scene", String.valueOf(prefetchParam.scene));

        // 美团特有参数
        params.put("uuid", MSCEnvHelper.getEnvInfo().getUUID());
        params.put("os", "android");
        params.put("osVersion", Build.VERSION.RELEASE);
        if (MSCEnvHelper.getCityController() != null) {
            params.put("cityId", String.valueOf(MSCEnvHelper.getCityController().getCityId()));
        }

        if (locationParam != null) {
            params.put("longitude", String.valueOf(locationParam.longitude));
            params.put("latitude", String.valueOf(locationParam.latitude));
        }

        Map<String, String> keyMap = prefetchParam.prefetchConfig.keyMap;
        if (keyMap != null) {
            // 进行参数映射，解决MMP提供的参数名与业务自定义的不同的问题
            Map<String, String> newParams = new HashMap<>();
            for (Map.Entry<String, String> entry : params.entrySet()) {
                String newKey = keyMap.get(entry.getKey());
                if (newKey != null) {
                    newParams.put(newKey, entry.getValue());
                } else {
                    newParams.put(entry.getKey(), entry.getValue());
                }
            }
            params = newParams;
        }
        return params;
    }

    private static String appendUrlParams(String url, Map<String, String> params) {
        Uri uri = Uri.parse(url);
        Uri.Builder builder = uri.buildUpon();
        Set<String> names = uri.getQueryParameterNames();
        Map<String, String> queries = new HashMap<>();
        for (String name : names) {
            queries.put(name, uri.getQueryParameter(name));
        }
        for (Map.Entry<String, String> param : params.entrySet()) {
            String key = param.getKey();
            String queryValue = queries.get(key);
            if (TextUtils.isEmpty(queryValue)) {
                builder.appendQueryParameter(key, param.getValue());
            }
        }
        return builder.build().toString();
    }

    /**
     * 获取url
     *
     * @return
     */
    private static String getUrl(String url, String method, Map<String, String> params, Map<String, String> businessUrlParam) {
        if (TextUtils.equals(method, PrefetchConfig.PREFETCH_METHOD_GET)) {
            //get请求：公共参数、业务参数添加到url上
            if (businessUrlParam != null && businessUrlParam.size() > 0) {
                params.putAll(businessUrlParam);
            }
            return appendUrlParams(url, params);
        }
        if (businessUrlParam != null && businessUrlParam.size() > 0) {
            //post请求：业务参数需要放在url上
            return appendUrlParams(url, businessUrlParam);
        }
        return url;
    }

    private static RequestBody getBody(JsonElement param, String contentType) {
        if (param == null) {
            return null;
        }
        RequestBody body;
        if (PrefetchConfig.PREFETCH_POST_CONTENT_TYPE_FORM.equalsIgnoreCase(contentType) && param.isJsonObject()) {
            FormBody.Builder formBuilder = new FormBody.Builder();
            for (Map.Entry<String, JsonElement> entry : param.getAsJsonObject().entrySet()) {
                JsonElement value = entry.getValue();
                if (value != null && !value.isJsonNull()) {
                    String valueString = value.isJsonPrimitive() ? value.getAsString() : value.toString();
                    formBuilder.add(entry.getKey(), valueString);
                }
            }
            body = formBuilder.build();
        } else {
            String str = param.toString();
            body = MSIRequestHelper.createRequestBody(MediaType.parse("application/json"), str, null);
        }
        return body;
    }

    /**
     * 获取拦截器
     *
     * @param enableShark
     * @return
     */
    private static List<Interceptor> getInterceptors(boolean enableShark, PrefetchParam prefetchParam, PrefetchConfig prefetchConfig) {
        List<Interceptor> interceptors = MSCCallFactory.getApiInterceptors(!enableShark);
        interceptors.addAll(MSCCallFactory.getMtGuardInterceptors(
                prefetchConfig.enableSecuritySign, prefetchConfig.enableSecuritySiua));
        return interceptors;
    }

    /**
     * 解耦注册，用来获取业务参数的监听器，用appId区分不同业务
     *
     * @param appId
     * @return
     */
    private static IRequestPrefetchListener getRequestPrefetchListener(String appId) {
        List<IRequestPrefetchListener> requestPrefetchListenerList = ServiceLoader.load(IRequestPrefetchListener.class, appId);
        if (requestPrefetchListenerList != null && requestPrefetchListenerList.size() > 0) {
            return requestPrefetchListenerList.get(0);
        }
        return null;
    }

    /**
     * 解耦注册，用来获取业务数据预拉取拦截器，用appId区分不同业务
     *
     * @param appId
     * @return
     */
    private static IRequestPrefetchInterceptor getRequestPrefetchInterceptor(String appId) {
        List<IRequestPrefetchInterceptor> requestPrefetchInterceptorList = ServiceLoader.load(IRequestPrefetchInterceptor.class, appId);
        if (requestPrefetchInterceptorList != null && requestPrefetchInterceptorList.size() > 0) {
            return requestPrefetchInterceptorList.get(0);
        }
        return null;
    }

    public synchronized void cancel() {
        if (clientCall != null && !clientCall.isCanceled()) {
            clientCall.cancel();
        }
    }

    private synchronized void setClientCall(MSCClientCall call) {
        this.clientCall = call;
    }
}
