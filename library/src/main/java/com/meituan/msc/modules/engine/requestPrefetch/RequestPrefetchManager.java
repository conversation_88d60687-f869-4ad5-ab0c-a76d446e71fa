package com.meituan.msc.modules.engine.requestPrefetch;

import static com.meituan.msc.common.utils.Constants.SG_STORE_PAGE_PATH;
import static com.meituan.msc.util.perf.PerfEventName.GET_BACKGROUND_FETCH_DATA;
import static com.meituan.msc.util.perf.PerfEventName.REQUEST_PREFETCH;
import static com.meituan.msc.util.perf.PerfEventName.REQUEST_PREFETCH_PREPARE;

import android.app.Activity;
import android.support.annotation.AnyThread;
import android.support.annotation.UiThread;
import android.text.TextUtils;

import com.meituan.msc.common.config.MSCConfig;
import com.meituan.msc.common.executor.MSCExecutors;
import com.meituan.msc.common.framework.Callback;
import com.meituan.msc.common.report.BaseMetricsReporter;
import com.meituan.msc.common.support.java.util.function.BiConsumer;
import com.meituan.msc.common.utils.Constants;
import com.meituan.msc.common.utils.PathUtil;
import com.meituan.msc.common.utils.ToastUtils;
import com.meituan.msc.jse.modules.core.JSDeviceEventEmitter;
import com.meituan.msc.modules.api.network.FetchTokenResponse;
import com.meituan.msc.modules.apploader.IAppLoader;
import com.meituan.msc.modules.container.ApplicationLifecycleMonitor;
import com.meituan.msc.modules.engine.MSCHornRollbackConfig;
import com.meituan.msc.modules.engine.MSCRuntime;
import com.meituan.msc.modules.page.render.MSCHornPerfConfig;
import com.meituan.msc.modules.reporter.CommonTags;
import com.meituan.msc.modules.reporter.MSCLog;
import com.meituan.msc.modules.reporter.MSCReporter;
import com.meituan.msc.modules.reporter.ReporterFields;
import com.meituan.msc.modules.update.MSCAppModule;
import com.meituan.msc.modules.update.bean.AppMetaInfoWrapper;
import com.meituan.msc.util.perf.PerfTrace;
import com.sankuai.android.jarvis.Jarvis;

import org.json.JSONException;
import org.json.JSONObject;

import java.lang.ref.WeakReference;
import java.util.List;
import java.util.Map;
import java.util.concurrent.CopyOnWriteArrayList;
import java.util.concurrent.ExecutorService;
import java.util.concurrent.ScheduledFuture;
import java.util.concurrent.TimeUnit;

/**
 * https://km.sankuai.com/page/1329980547
 * 数据预拉取管理类
 * 触发预拉取，临时存储数据，getBackgroundFetchData api调用时提供回调
 */
public class RequestPrefetchManager {
    public static final String TAG_URL = "url";

    public static final String REPORT_STATE_FAIL = "fail";
    public static final String REPORT_STATE_SUCCESS = "success";
    public static final String REPORT_STATE_CANCEL = "cancel";

    public static final String TAG = "RequestPrefetchManager";

    private MSCRuntime runtime;

    private volatile String errorMsg;
    private volatile FetchTokenResponse result;
    private volatile JSONObject appPrefetchResult;
    private final List<Callback<FetchTokenResponse>> feCallbackList = new CopyOnWriteArrayList<>();
    private long lastStartTime = -1;
    private TriggerPrefetchDataScene lastTriggerPrefetchDataScene;
    private String lastAppId;
    private String lastTargetPath;
    private WeakReference<TimeoutTimerRunnable> lastTimeoutTimerRunnableWeakReference;
    private ScheduledFuture<?> lastScheduledFuture;
    // 最新预拉取请求中，获取定位的方式。缓存/实时/失败
    private String lastPrefetchLocationType;
    private Boolean lastPrefetchIsAsync = null;
    private final PrefetchListener prefetchListener = new PrefetchListener();
    private final PrefetchRequest prefetchRequest = new PrefetchRequest();

    private static final ExecutorService sExecutor;

    static {
        if (MSCHornPerfConfig.getInstance().useMSCExecutors()) {
            sExecutor = MSCExecutors.Serialized.getExecutor();
        } else {
            sExecutor = Jarvis.newSingleThreadExecutor("msc-data-prefetch");
        }
    }

    private volatile boolean syncPrefetching = false;

    private volatile Status status = Status.NOT_STARTED;

    public enum Status {
        NOT_STARTED,
        PREPARING_DATA,
        REQUESTING,
        SUCCESS,
        FAIL,
        CANCELED
    }

    RequestPrefetchManager() {

    }

    public synchronized void getData(Callback<FetchTokenResponse> callback) {
        PerfTrace.online().begin(GET_BACKGROUND_FETCH_DATA);
        if (status == Status.NOT_STARTED) {
            PerfTrace.online().end(GET_BACKGROUND_FETCH_DATA);
            // 现预取发起时间应早于业务代码执行时间，所以业务获取数据时如果没有开始，则以后也不会开始了，此处处理以保证有回调
            callback.onFail("fetch not started", null);
        } else if (status == Status.SUCCESS) {
            result.__mtFinishTimeStamp = System.currentTimeMillis();
            callback.onSuccess(result);
            PerfTrace.online().end(GET_BACKGROUND_FETCH_DATA);
        } else if (status == Status.FAIL) {
            callback.onFail(errorMsg, null);
            PerfTrace.online().end(GET_BACKGROUND_FETCH_DATA);
        } else {
            feCallbackList.add(callback);
        }
    }

    public synchronized FetchTokenResponse getDataSync() {
        if (status == Status.SUCCESS) {
            PerfTrace.online().begin(GET_BACKGROUND_FETCH_DATA);
            result.__mtFinishTimeStamp = System.currentTimeMillis();
            PerfTrace.online().end(GET_BACKGROUND_FETCH_DATA);
            return result;
        } else {
            return null;
        }
    }

    public synchronized JSONObject getAppDataSync() throws JSONException {
        if (status == Status.SUCCESS) {
            PerfTrace.online().begin(GET_BACKGROUND_FETCH_DATA);
            appPrefetchResult.put("__mtFinishTimeStamp", System.currentTimeMillis());
            PerfTrace.online().end(GET_BACKGROUND_FETCH_DATA);
            return appPrefetchResult;
        } else {
            return null;
        }
    }

    public boolean isStarted() {
        return status != Status.NOT_STARTED;
    }

    private boolean isFinished() {
        return status == Status.SUCCESS || status == Status.FAIL || status == Status.CANCELED;
    }


    /**
     * 判断是否为App级数据预拉取，回滚开关内置，不回滚则使用新版本的逻辑，否则使用老版本的逻辑
     *
     * @param mscAppModule
     * @param appId
     * @return
     */
    public static boolean isAppLevelPrefetch(MSCAppModule mscAppModule, String appId) {
        if (!mscAppModule.hasMetaInfo()) {
            return false;
        }
        AppMetaInfoWrapper metaInfo = mscAppModule.getMetaInfo();
        return isAppLevelPrefetchByMetaInfo(metaInfo, appId);
    }

    /**
     * 通过元信息判断是否为App级数据预拉取，回滚开关内置，不回滚则使用新版本的逻辑，否则使用老版本的逻辑
     *
     * @param metaInfo 小程序元信息
     */
    public static boolean isAppLevelPrefetchByMetaInfo(AppMetaInfoWrapper metaInfo, String appId) {
        if (!MSCHornRollbackConfig.isRollbackAppDataPrefetchJudge()) {
            if (metaInfo == null) {
                return false;
            }
            Map<String, Object> map = (Map<String, Object>) metaInfo.getExtraConfigValue(Constants.TARGET_PATH_PREFETCH);
            return map != null && map.containsKey(Constants.APP_DATA_PREFETCH);
        } else {
            return MSCConfig.isAppLevelBackgroundFetchData(appId);
        }
    }

    /**
     * @param activity
     * @param targetPath 目标页面
     * @param scene      跳转场景置
     */
    @AnyThread
    public void startPrefetch(Activity activity, AppMetaInfoWrapper metaInfo, String targetPath, int scene) {
        lastStartTime = System.currentTimeMillis();
        lastTriggerPrefetchDataScene = TriggerPrefetchDataScene.PAGE_START;
        lastTargetPath = targetPath;
        if (!isEnablePrefetch(metaInfo) || isAppLevelPrefetchByMetaInfo(metaInfo, metaInfo.getAppId())) {
            return;
        }
        lastAppId = metaInfo.getAppId();
        prefetchListener.getReporter().markDurationStart(ReporterFields.REPORT_DURATION_REQUEST_PREFETCH_TOTAL);
        PerfTrace.online().begin(REQUEST_PREFETCH).arg("targetPath", targetPath);
        PerfTrace.online().begin(REQUEST_PREFETCH_PREPARE);
        sExecutor.execute(new Runnable() {
            @Override
            public void run() {
                MSCLog.i(TAG, "在PageStart时发起数据预拉取");
                startPrefetchInner(activity, metaInfo, targetPath, scene, false);
            }
        });
    }

    /**
     * App级数据预拉取
     */
    @AnyThread
    public void startAppPrefetch(Activity activity, AppMetaInfoWrapper metaInfo, String targetPath, int scene) {
        // isEnablePrefetch中有对metaInfo的判空
        if (!isEnablePrefetch(metaInfo) || !isAppLevelPrefetchByMetaInfo(metaInfo, metaInfo.getAppId())) {
            return;
        }
        this.lastStartTime = System.currentTimeMillis();
        this.lastTriggerPrefetchDataScene = null;
        this.lastPrefetchLocationType = null;
        this.lastPrefetchIsAsync = null;
        this.lastTargetPath = targetPath;
        this.lastAppId = metaInfo.getAppId();
        PerfTrace.online().begin(REQUEST_PREFETCH_PREPARE);
        prefetchListener.getReporter().markDurationStart(ReporterFields.REPORT_DURATION_REQUEST_PREFETCH_TOTAL);
        sExecutor.execute(new Runnable() {
            @Override
            public void run() {
                startPrefetchInner(activity, metaInfo, targetPath, scene, false);
                ToastUtils.toastIfDebug("发起App数据预拉取");
            }
        });
    }

    @UiThread
    public void startPrefetchBeforeActivityCreate(Activity activity, AppMetaInfoWrapper metaInfo, String targetPath, int scene, TriggerPrefetchDataScene source) {
        MSCLog.i(TAG, "startPrefetchBeforeActivityCreate, source:", source);
        this.lastStartTime = System.currentTimeMillis();
        this.lastTriggerPrefetchDataScene = source;
        this.lastAppId = metaInfo.getAppId();
        this.lastTargetPath = targetPath;
        if (!isEnablePrefetch(metaInfo)) {
            return;
        }
        prefetchListener.getReporter().markDurationStart(ReporterFields.REPORT_DURATION_REQUEST_PREFETCH_TOTAL);
        PerfTrace.online().begin(REQUEST_PREFETCH).arg("targetPath", targetPath);
        PerfTrace.online().begin(REQUEST_PREFETCH_PREPARE);
        startPrefetchInner(activity, metaInfo, targetPath, scene, true);
        ToastUtils.toastIfDebug("在" + (source == TriggerPrefetchDataScene.PAGE_OUTSIDE ? "页面外" : "路由") + "时发起数据预拉取");
    }

    private void startPrefetchInner(Activity activity, AppMetaInfoWrapper metaInfo, String targetPathWithQuery, int scene, boolean sendAsync) {
        if (metaInfo == null) {
            //小程序元信息丢失，没有预拉取地址，直接返回
            PerfTrace.online().end(REQUEST_PREFETCH);
            MSCLog.i(TAG, "startPrefetchInner failed, metaInfo is null");
            return;
        }
        String targetPathWithoutQuery = getTargetPathWithoutQuery(targetPathWithQuery, metaInfo);
        if (TextUtils.isEmpty(targetPathWithoutQuery)) {
            //targetPathWithoutQuery为空，直接返回
            dealTargetPathIsEmpty(metaInfo);
            PerfTrace.online().end(REQUEST_PREFETCH);
            return;
        }
        if (TextUtils.isEmpty(lastTargetPath)) {
            lastTargetPath = targetPathWithoutQuery;
        }
        PrefetchConfig requestPrefetchConfig = metaInfo.getPrefetchConfig(targetPathWithoutQuery);
        if (requestPrefetchConfig == null || TextUtils.isEmpty(requestPrefetchConfig.url)) {
            //预拉取信息丢失，直接返回
            MSCLog.i(TAG, "requestPrefetchConfig is null or url is null!");
            PerfTrace.online().end(REQUEST_PREFETCH);
            return;
        }
        if (sendAsync) {
            syncPrefetching = true;
            lastPrefetchIsAsync = true;
            sExecutor.execute(new Runnable() {
                @Override
                public void run() {
                    sendPrefetchRequest(activity, targetPathWithQuery, targetPathWithoutQuery, scene, requestPrefetchConfig, metaInfo, true, true);
                }
            });
        } else {
            lastPrefetchIsAsync = false;
            sendPrefetchRequest(activity, targetPathWithQuery, targetPathWithoutQuery, scene, requestPrefetchConfig, metaInfo, false, false);
        }
    }

    private static String getTargetPathWithoutQuery(String targetPath, AppMetaInfoWrapper metaInfo) {
        String targetPathWithoutQuery = PathUtil.getPath(targetPath);
        if (TextUtils.isEmpty(targetPathWithoutQuery)) {
            //业务跳链没有传递targetPath, 从metaInfo获取
            targetPathWithoutQuery = metaInfo.getMainPath();
        }
        return targetPathWithoutQuery;
    }

    private void sendPrefetchRequest(Activity activity, String targetPathWithQuery, String targetPathWithoutQuery, int scene,
                                     PrefetchConfig requestPrefetchConfig, AppMetaInfoWrapper metaInfo, boolean assertActivityIsNotNull, boolean isAsync) {
        status = Status.PREPARING_DATA;
        String appId = metaInfo.getAppId();
        MSCLog.i(TAG, "start RequestPrefetch: ", appId);

        PrefetchParam param = new PrefetchParam();
        param.prefetchConfig = requestPrefetchConfig;
        // 12.20.400 预拉取version字段修正：之前传包编译号（传错），改成传包版本号
        param.version = metaInfo.getVersion();
        param.path = targetPathWithoutQuery;
        param.query = PathUtil.getQuery(targetPathWithQuery);
        param.scene = scene;
        param.appId = appId;
        param.metaInfo = metaInfo;
        boolean isAppLevel = isAppLevelPrefetchByMetaInfo(metaInfo, appId);
        if (isAppLevel) {
            param.version = metaInfo.getVersion();
        }
        startTimeoutTimer(targetPathWithoutQuery, requestPrefetchConfig.url, requestPrefetchConfig.timeout, appId);

        PrefetchLocation prefetchLocation = new PrefetchLocation();
        if (prefetchLocation.needLocationParams(requestPrefetchConfig.locationConfig)) {
            //先取定位参数，再发起预拉取请求
            // 这里对于Activity为null的情况做一个兜底，如果拿不到Top的Activity就不再将数据预拉取提前了
            if (assertActivityIsNotNull) {
                if (activity == null) {
                    activity = ApplicationLifecycleMonitor.ALL.getLastActivity();
                    MSCLog.i(RequestPrefetchManager.TAG, "activity is null, get top activity, top activity is ", activity != null ? activity.toString() : null);
                }
            }
            if (assertActivityIsNotNull && activity == null) {
                // 不再发起数据源预拉取
                MSCLog.i(RequestPrefetchManager.TAG, "activity is null, stop prefetch resetSyncPrefetching");
                // FIXME by chendacai 临时先这么写，如果拿不到Activity就不再将数据预拉取提前了，后续数据预拉取重构时给数据预拉取加个回调
                resetSyncPrefetching();
            } else {
                prefetchLocation.getLocation(activity, prefetchListener, param, new RequestRunnable(prefetchRequest, isAppLevel, isAsync));
            }
        } else {
            //发起预拉取请求
            prefetchRequest.request(param, prefetchListener, null, isAsync);
        }
    }

    private void resetTimeoutTimer() {
        ScheduledFuture<?> scheduledFuture = this.lastScheduledFuture;
        if (scheduledFuture != null && !scheduledFuture.isCancelled()) {
            scheduledFuture.cancel(false);
            this.lastScheduledFuture = null;
        }
        TimeoutTimerRunnable timeoutTimerRunnable = this.lastTimeoutTimerRunnableWeakReference != null ? this.lastTimeoutTimerRunnableWeakReference.get() : null;
        if (timeoutTimerRunnable != null) {
            // 双重保险，避免线程池的任务不能被取消导致后续执行状态出问题
            timeoutTimerRunnable.cancel();
            this.lastTimeoutTimerRunnableWeakReference = null;
        }
    }

    /**
     * 启动超时定时器
     *
     * @param timeout
     */
    private void startTimeoutTimer(String pagePath, String url, long timeout, String appId) {
        if (timeout > 0) {
            TimeoutTimerRunnable timeoutTimerRunnable = new TimeoutTimerRunnable(this, pagePath, url, appId, timeout);
            this.lastTimeoutTimerRunnableWeakReference = new WeakReference<>(timeoutTimerRunnable);
            this.lastScheduledFuture = MSCExecutors.ioSerialized.schedule(timeoutTimerRunnable, timeout, TimeUnit.MILLISECONDS);
        }
    }

    private boolean isEnablePrefetch(AppMetaInfoWrapper metaInfo) {
        if (!MSCConfig.isEnablePrefetch()) {
            //线上开关控制降级
            MSCLog.i(TAG, "close prefetch by horn!");
            return false;
        }
        if (status != Status.NOT_STARTED) {
            // 同时只允许一个页面发起
            MSCLog.i(TAG, "prefetch already started, status:", status);
            return false;
        }
        if (metaInfo == null) {
            MSCLog.i(TAG, "prefetch needs metaInfo");
            return false;
        }
        return true;
    }

    /**
     * 取消预拉取
     */
    private synchronized void cancel() {
        resetTimeoutTimer();
        if (isFinished() || !isStarted()) {
            return;
        }
        PerfTrace.online().end(REQUEST_PREFETCH);
        status = Status.CANCELED;

        errorMsg = "canceled";
        MSCLog.i(TAG, "request prefetch for", lastAppId, " fail:", errorMsg);
        reportSuccessRate(prefetchRequest.lastPagePath, prefetchRequest.lastUrl, "cancel", false);

        if (!feCallbackList.isEmpty()) {
            for (Callback<FetchTokenResponse> feCallback : feCallbackList) {
                feCallback.onFail(errorMsg, null);
            }
            feCallbackList.clear();
            PerfTrace.online().end(GET_BACKGROUND_FETCH_DATA);
        }
        prefetchRequest.cancel();
    }

    public boolean isSyncPrefetching() {
        return syncPrefetching;
    }

    public void resetSyncPrefetching() {
        syncPrefetching = false;
    }

    public void reset() {
        MSCLog.i(TAG, "reset, status:", status);
        cancel();
        status = Status.NOT_STARTED;
        lastStartTime = -1;
        lastTriggerPrefetchDataScene = null;
        lastPrefetchLocationType = null;
        lastPrefetchIsAsync = null;
        lastTargetPath = null;
    }

    private void reportSuccessRate(String targetPathPath, String url, String errorMsg, boolean success) {
        prefetchListener.getReporter()
                .record(ReporterFields.REPORT_LAUNCH_POINT_REQUEST_PREFETCH_RATE)
                .tag(CommonTags.TAG_PAGE_PATH, targetPathPath)
                .tag(TAG_URL, url)
                .tag("errorMessage", errorMsg)
                .value(success ? 1 : 0)
                .sendDelay();
    }

    public void bindMSCRuntime(MSCRuntime mscRuntime) {
        this.runtime = mscRuntime;
    }

    /**
     * 数据预拉取请求结果监听
     * 前端已经调用getBackgroundFetchData，直接回调结果；反之，临时存储结果，待调用返回前端
     */
    public class PrefetchListener {

        public void setStatusState(Status statusState) {
            status = statusState;
        }

        public void setLocationType(String locationType) {
            lastPrefetchLocationType = locationType;
        }

        public BaseMetricsReporter getReporter() {
            if (runtime != null) {
                return runtime.getRuntimeReporter();
            }
            return new MSCReporter()
                    .commonTag(CommonTags.TAG_MSC_APP_ID, lastAppId)
                    .commonTag(CommonTags.TAG_PAGE_PATH, lastTargetPath);
        }

        /**
         * 回调成功
         *
         * @param result
         * @param prefetchParam
         */
        public synchronized void onSuccess(FetchTokenResponse result, PrefetchParam prefetchParam) {
            // TODO: 8/22/24 shiguozhe  在发起预拉取的时候就应该将预拉取的模式存下来，比如存到PrefetchParam中，然后在onSuccess中取
            if (isFinished()) {
                return;
            }
            PerfTrace.online().end(REQUEST_PREFETCH);
            resetTimeoutTimer();
            status = Status.SUCCESS;
            RequestPrefetchManager.this.result = result;
            MSCAppModule mscAppModule = runtime.getMSCAppModule();
            runtime.getRuntimeReporter().setPrefetchResponseSize(result.fetchedData.length());
            if (mscAppModule != null && isAppLevelPrefetch(mscAppModule, prefetchParam.getAppId())) {
                JSONObject appPrefetchResult = new JSONObject();
                try {
                    appPrefetchResult.put("fetchedData", result.fetchedData);
                    appPrefetchResult.put("url", result.url);
                    appPrefetchResult.put("timeStamp", result.timeStamp);
                    appPrefetchResult.put("path", result.path);
                    appPrefetchResult.put("query", result.query);
                    appPrefetchResult.put("scene", result.scene);
                    appPrefetchResult.put("__mtFinishTimeStamp", result.__mtFinishTimeStamp);
                    appPrefetchResult.put("fetchType", "pre");
                } catch (JSONException e) {
                    MSCLog.e(e);
                }
                RequestPrefetchManager.this.appPrefetchResult = appPrefetchResult;
                if (MSCHornRollbackConfig.isRollbackPendingFrameWorkReady()) {
                    runtime.getJSModuleDelegate(JSDeviceEventEmitter.class).emit("onBackgroundFetchData", appPrefetchResult);
                } else {
                    // App级数据预拉取
                    runtime.getModule(IAppLoader.class).getFrameworkReadyFuture().whenComplete(new BiConsumer<Void, Throwable>() {
                        @Override
                        public void accept(Void aVoid, Throwable throwable) {
                            runtime.getJSModuleDelegate(JSDeviceEventEmitter.class).emit("onBackgroundFetchData", appPrefetchResult);
                        }
                    });
                }
            }

            MSCLog.i(TAG, "request prefetch for", lastAppId, REPORT_STATE_SUCCESS);

            String targetPath = prefetchParam.prefetchConfig.pagePath;
            String url = prefetchParam.prefetchConfig.url;
            reportSuccessRate(targetPath, url, null, true);

            if (!feCallbackList.isEmpty()) {
                for (Callback<FetchTokenResponse> feCallback : feCallbackList) {
                    result.__mtFinishTimeStamp = System.currentTimeMillis();
                    feCallback.onSuccess(result);
                }
                if (runtime != null) {
                    runtime.getPerformanceManager().onDataPrefetch(prefetchParam.path, lastStartTime);
                }
                feCallbackList.clear();
                PerfTrace.online().end(GET_BACKGROUND_FETCH_DATA);
            }
            prefetchListener.getReporter()
                    .record(ReporterFields.REPORT_DURATION_REQUEST_PREFETCH_TOTAL)
                    .tag(CommonTags.TAG_PAGE_PATH, prefetchParam.prefetchConfig.pagePath)
                    .tag(TAG_URL, prefetchParam.prefetchConfig.url)
                    .durationEnd()
                    .sendDelay();
        }

        /**
         * 回调失败
         *
         * @param errorMsg
         * @param targetPathPath
         * @param url
         */
        public synchronized void onFail(String errorMsg, String targetPathPath, String url) {
            if (isFinished()) {
                return;
            }
            resetTimeoutTimer();
            // 取消请求
            prefetchRequest.cancel();
            PerfTrace.online().end(REQUEST_PREFETCH);
            status = Status.FAIL;
            RequestPrefetchManager.this.errorMsg = errorMsg;

            MSCLog.i(TAG, "request prefetch for", lastAppId, "fail:", errorMsg);
            reportSuccessRate(targetPathPath, url, errorMsg, false);

            if (!feCallbackList.isEmpty()) {
                for (Callback<FetchTokenResponse> feCallback : feCallbackList) {
                    feCallback.onFail(errorMsg, null);
                }
                feCallbackList.clear();
                PerfTrace.online().end(GET_BACKGROUND_FETCH_DATA);
            }
        }
    }

    private void dealTargetPathIsEmpty(AppMetaInfoWrapper metaInfo) {
        String msg = "RequestPrefetch failed targetPath is null and metaInfo mainPath is null!";
        MSCLog.i(TAG, msg);
        MSCLog.i(TAG, "metaInfo:", metaInfo.getMetaInfoToString());
    }

    public TriggerPrefetchDataScene getLastTriggerPrefetchDataScene() {
        return lastTriggerPrefetchDataScene;
    }

    public String getLastPrefetchLocationType() {
        return lastPrefetchLocationType;
    }

    public Boolean getLastPrefetchIsAsync() {
        return lastPrefetchIsAsync;
    }

    /**
     * 判断当前是否有页面外发起的指定页面的数据预拉取数据存在
     *
     * @param targetPath 带query的targetPath
     * @return
     */
    public boolean isExistCachedDataOrIsFetchingDataFromPageOutSide(String targetPath) {
        // 上次发起的数据预拉取不是页面外发起的
        if (lastTriggerPrefetchDataScene != TriggerPrefetchDataScene.PAGE_OUTSIDE) {
            return false;
        }
        Boolean isKeyMatched = null;
        // 有缓存数据，且Key匹配
        if (result != null) {
            // isKeyMatched() 可能耗时，减少一次调用
            isKeyMatched = isKeyMatched(targetPath);
            if (isKeyMatched) {
                return true;
            }
        }
        // 正在发起数据预拉取，且Key匹配
        if (status != Status.NOT_STARTED) {
            if (isKeyMatched == null) {
                // isKeyMatched() 可能耗时，减少一次调用
                isKeyMatched = isKeyMatched(targetPath);
            }
            if (isKeyMatched) {
                return true;
            }
        }
        return false;
    }

    /**
     * 使用targetPath和当前存储的targetPath匹配，看下数据缓存可以复用
     *
     * @param targetPath
     * @return
     */
    private boolean isKeyMatched(String targetPath) {
        // TODO @lizhen91 先只用于闪购商家页
        return targetPath != null && lastTargetPath != null && targetPath.startsWith(SG_STORE_PAGE_PATH) && lastTargetPath.startsWith(SG_STORE_PAGE_PATH);
    }

    private static class TimeoutTimerRunnable implements Runnable {
        private final WeakReference<RequestPrefetchManager> requestPrefetchManagerWeakReference;
        private final String pagePath;
        private final String url;
        private final String appId;
        private final long timeout;
        private volatile boolean isCancelled = false;

        public TimeoutTimerRunnable(RequestPrefetchManager requestPrefetchManager, String pagePath, String url, String appId, long timeout) {
            this.requestPrefetchManagerWeakReference = new WeakReference<>(requestPrefetchManager);
            this.pagePath = pagePath;
            this.url = url;
            this.appId = appId;
            this.timeout = timeout;
        }

        public void cancel() {
            this.isCancelled = true;
        }

        public final void run() {
            if (this.isCancelled) {
                return;
            }
            RequestPrefetchManager requestPrefetchManager = this.requestPrefetchManagerWeakReference.get();
            if (requestPrefetchManager == null) {
                return;
            }
            if (requestPrefetchManager.isFinished()) {
                return;
            }
            MSCLog.i("request prefetch timeout:", timeout, "ms,", appId);
            requestPrefetchManager.prefetchListener.onFail("timeout", pagePath, url);
        }
    }
}
