package com.meituan.msc.modules.engine.requestPrefetch;


import com.meituan.msc.modules.engine.MSCHornRollbackConfig;
import com.meituan.msc.modules.reporter.MSCLog;
import com.meituan.msc.util.perf.PerfTrace;

import static com.meituan.msc.util.perf.PerfEventName.REQUEST_PREFETCH_LOCATE;

public class RequestRunnable implements Runnable {

    public final static String TAG = "RequestRunnable";
    private LocationParam locationParam;
    final PrefetchRequest prefetchRequest;
    PrefetchParam prefetchParam;
    RequestPrefetchManager.PrefetchListener prefetchListener;
    /**
     * 是否为app级数据预拉取
     */
    private final boolean isAppLevel;
    /**
     * 是否异步线程触发
     */
    private final boolean isAsync;
    private String errMsg;

    RequestRunnable(PrefetchRequest prefetchRequest, boolean isAppLevel, boolean isAsync) {
        this.prefetchRequest = prefetchRequest;
        this.isAppLevel = isAppLevel;
        this.isAsync = isAsync;
    }

    @Override
    public void run() {
        PerfTrace.end(REQUEST_PREFETCH_LOCATE);
        // app级数据预拉取 定位失败 不再进行数据预拉取
        if (locationParam == null) {
            MSCLog.e(TAG, "location failed, ", errMsg);
            if (isAppLevel && !MSCHornRollbackConfig.isRollbackDisableRetryAppPrefetchIfLocateFail()) {
                MSCLog.i(TAG, "stop prefetch");
                prefetchListener.onFail(errMsg, prefetchParam.getPath(), prefetchParam.prefetchConfig.getUrl());
                return;
            }
        }
        // page级数据预拉取 定位失败 继续进行数据预拉取
        MSCLog.i(TAG, "continue prefetch");
        prefetchRequest.request(prefetchParam, prefetchListener, locationParam, isAsync);
    }

    public RequestRunnable setLocationParam(LocationParam locationParam) {
        this.locationParam = locationParam;
        return this;
    }

    public RequestRunnable setPrefetchParam(PrefetchParam prefetchParam) {
        this.prefetchParam = prefetchParam;
        return this;
    }

    public RequestRunnable setPrefetchListener(RequestPrefetchManager.PrefetchListener prefetchListener) {
        this.prefetchListener = prefetchListener;
        return this;
    }

    public RequestRunnable setErrorMessage(String errMsg) {
        this.errMsg = errMsg;
        return this;
    }
}

