package com.meituan.msc.modules.engine.requestPrefetch;

public enum TriggerPrefetchDataScene {
    /**
     * 页面外发起的数据预拉取
     */
    PAGE_OUTSIDE("outside"),
    /**
     * Activity的execStartActivity时发起的数据预拉取
     */
    ROUTER("router"),
    /**
     * 页面开始时发起的数据预拉取
     */
    PAGE_START("pageStart");

    private String reportValue;

    TriggerPrefetchDataScene(String reportValue) {
        this.reportValue = reportValue;
    }

    public String getReportValue() {
        return reportValue;
    }
}
