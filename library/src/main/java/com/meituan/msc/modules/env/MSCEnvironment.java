package com.meituan.msc.modules.env;

import android.os.Build;

import com.meituan.msc.extern.MSCEnvHelper;
import com.meituan.msc.modules.manager.MSCModule;
import com.meituan.msc.modules.manager.ModuleName;

import org.json.JSONException;
import org.json.JSONObject;

@ModuleName(name = "Environment")
public class MSCEnvironment extends MSCModule {

    @Override
    public JSONObject getConstants() {
        JSONObject result = new JSONObject();
        try {
            result.put("appID", MSCEnvHelper.getEnvInfo().getAppID());
            result.put("version", MSCEnvHelper.getEnvInfo().getAppVersionCode());
            result.put("buildNumber", MSCEnvHelper.getEnvInfo().getBuildNumber());
            result.put("package", MSCEnvHelper.getContext().getPackageName());
            // 手机机型
            result.put("device", Build.MODEL);
            // 系统版本(如8.0.0)，不同于Platform.Version(Build.VERSION.SDK_INT如26)
            result.put("systemVersion", Build.VERSION.RELEASE);
        } catch (JSONException e) {
            e.printStackTrace();
        }
        return result;
    }
}
