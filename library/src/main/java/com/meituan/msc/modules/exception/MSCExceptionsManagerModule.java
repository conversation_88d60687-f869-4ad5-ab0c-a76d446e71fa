package com.meituan.msc.modules.exception;

import android.app.Activity;

import com.meituan.msc.extern.MSCEnvHelper;
import com.meituan.msc.jse.bridge.UiThreadUtil;
import com.meituan.msc.modules.container.IContainerDelegate;
import com.meituan.msc.modules.container.IContainerManager;
import com.meituan.msc.modules.devtools.DebugHelper;
import com.meituan.msc.modules.engine.MSCRuntime;
import com.meituan.msc.modules.manager.MSCEvent;
import com.meituan.msc.modules.manager.MSCMethod;
import com.meituan.msc.modules.manager.MSCModule;
import com.meituan.msc.modules.manager.MSCSubscriber;
import com.meituan.msc.modules.manager.ModuleName;
import com.meituan.msc.modules.page.IPageModule;
import com.meituan.msc.modules.reporter.MSCCommonTagReporter;
import com.meituan.msc.modules.reporter.MSCLog;

import org.json.JSONArray;
import org.json.JSONException;
import org.json.JSONObject;

@ModuleName(name = MSCExceptionsManagerModule.NAME)
public class MSCExceptionsManagerModule extends MSCModule implements ExceptionsInterface {
    public static final String NAME = "ExceptionsManager";


    private final MSCSubscriber lifecycleSubscriber = new MSCSubscriber() {
        @Override
        public void onReceive(MSCEvent event) {
            onHostDestroy();
        }
    };

    @Override
    public String getName() {
        return NAME;
    }


    @Override
    public void onRuntimeAttached(MSCRuntime runtime) {
        super.onRuntimeAttached(runtime);
        getRuntime().subscribe(IContainerManager.MSC_EVENT_CONTAINER_DESTROYED, lifecycleSubscriber);
    }

    @Override
    public void onDestroy() {
        onHostDestroy();
        getRuntime().unsubscribe(lifecycleSubscriber);
        super.onDestroy();
    }

    @MSCMethod
    public void reportFatalException(String message, JSONArray stack, double idDouble) {
        int id = (int) idDouble;
        JSONObject data = new JSONObject();
        try {
            data.put("message", message);
            data.put("stack", stack);
            data.put("id", id);
            data.put("isFatal", true);
        } catch (JSONException e) {
            e.printStackTrace();
        }
        reportException(data);
    }

    @MSCMethod
    public void reportSoftException(String message, JSONArray stack, double idDouble) {
        int id = (int) idDouble;
        JSONObject data = new JSONObject();
        try {
            data.put("message", message);
            data.put("stack", stack);
            data.put("id", id);
            data.put("isFatal", false);
        } catch (JSONException e) {
            e.printStackTrace();
        }
        reportException(data);
    }

    @MSCMethod
    public void reportException(JSONObject data) {
        handleJSError(data, getRuntime().getRuntimeReporter());
    }

    public void reportException(JSONObject data, IPageModule pageModule) {
        handleJSError(data, pageModule != null ? pageModule.getReporter() : getRuntime().getRuntimeReporter());
    }

    @MSCMethod
    public void updateExceptionMessage(
            String title, JSONArray details, double exceptionIdDouble) {
        int exceptionId = (int) exceptionIdDouble;
        updateJSError(title, details, exceptionId);
    }

    @MSCMethod
    public void dismissRedbox() {
        // [MRN 60 chendacai] 在 UI 线程执行，避免多线程并发操作 mRedBoxDialog 导致 NPE 问题
        UiThreadUtil.runOnUiThread(new Runnable() {
            @Override
            public void run() {
                hideRedboxDialog();
            }
        });
    }

    public static boolean isFatalError(JSONObject exceptionInfo) {
        return exceptionInfo.optBoolean("isFatal");
    }


    public static boolean isInternalEnv() {
        return (MSCEnvHelper.isInited() && !MSCEnvHelper.getEnvInfo().isProdEnv()) || DebugHelper.isDebug();
    }

    private static final String OOM_STR = "java.lang.OutOfMemoryError";

    /**
     * JSError 不上报的场景
     *
     * @param message
     * @return
     */
    private boolean needSkipReportJSError(String message, JSONObject exceptionInfo) {
        // 引擎无挂载容器时，上报页面栈为空 https://km.sankuai.com/collabpage/1479204338
        if (!hasContainerInTask() && message.contains("page stack is empty")) {
            logSkippedErrorMessage(message, exceptionInfo);
            return true;
        }
        // OOM 临时聚类一下，之后再让前端改，截断OOM后面的信息，后面的信息可以通过originMessage查看到
        int oomIndex = message.indexOf(OOM_STR);
        if (oomIndex != -1) {
            try {
                exceptionInfo.put("message", message.substring(0, oomIndex + OOM_STR.length()));
            } catch (JSONException e) {
                e.printStackTrace();
            }
        }
        // default
        return false;
    }

    private void logSkippedErrorMessage(String message, JSONObject exceptionInfo) {
        MSCLog.w(NAME, "logSkippedErrorMessage", exceptionInfo);
    }

    private void handleJSError(JSONObject exceptionInfo, MSCCommonTagReporter reporter) {
        try {
            String message = exceptionInfo.optString("message");
            // 不上报的场景
            if (!needSkipReportJSError(message, exceptionInfo)) {
                reporter.reportJSError(exceptionInfo, getRuntime());
            }
        } catch (Throwable th) {
            reporter.reportJSError(exceptionInfo, getRuntime());
        }

        boolean isFatal = exceptionInfo.optBoolean("isFatal");

        // TODO: 出现JS错误时，引擎如何处理
        if (isFatal) { //fatal错误才将引擎状态设置为Error，显示错误页面
//            mrnInstance.updateError();
        }
    }

    @Override
    public void showNewJavaError(String message, Throwable e, Activity activity) {
    }

    public void updateJSError(
            final String message, final JSONArray details, final int errorCookie) {
    }

    public void hideRedboxDialog() {

    }

    public void onHostDestroy() {
        hideRedboxDialog();
    }

    /**
     * 是否有MSC页面在任务栈中（实验一下 先放在这里）
     * @return
     */
    private boolean hasContainerInTask() {
        if (getRuntime() == null) {
            return false;
        }
        IContainerManager containerManagerModule = getRuntime().getContainerManagerModule();
        if (containerManagerModule == null || containerManagerModule.getContainerCount() <= 0) {
            return false;
        } else {
            // 任务栈操作，eg：美团直接启动首页，清栈退出页面时，Activity销毁顺序与创建顺序一致
            // 与小程序页面栈销毁顺序要求相反，因此触发page stack empty
            IContainerDelegate containerDelegate = containerManagerModule.getTopContainer();
            if (containerDelegate != null && containerDelegate.getActivity() != null
                    && !containerDelegate.getActivity().isFinishing()) {
                MSCLog.i("TAG hasContainerInTask", containerDelegate, containerDelegate.getActivity().getTaskId());
                if (containerDelegate.getActivity().getTaskId() != -1) {
                    return true;
                }
            }
            return false;
        }
    }

}
