package com.meituan.msc.modules.exception;

import android.util.Pair;

import com.meituan.crashreporter.CrashReporter;
import com.meituan.msc.common.utils.ExceptionHelper;
import com.meituan.msc.jse.bridge.NativeModuleCallExceptionHandler;
import com.meituan.msc.jse.devsupport.JSException;
import com.meituan.msc.modules.engine.MSCRuntime;
import com.meituan.msc.modules.manager.MSCModuleNotFoundException;
import com.meituan.msc.modules.reporter.MSCLog;

import org.json.JSONException;
import org.json.JSONObject;

import static com.meituan.msc.modules.reporter.MSCCommonTagReporter.REPORT_JS_FROM_KEY;

/**
 * 所有的NativeModule异常捕获的地方
 */
public class MSCModuleCallExceptionHandler implements NativeModuleCallExceptionHandler {
    protected final MSCRuntime runtime;

    public MSCModuleCallExceptionHandler(MSCRuntime runtime) {
        this.runtime = runtime;
    }

    @Override
    public void handleException(Exception e) {
        try {
            JSONObject jsonObject = generateErrorInfo(e);
            if (jsonObject == null) {
                MSCLog.e("[MSCModuleCallExceptionHandler@handleException]", "jsonObject null");
                return;
            }
            jsonObject.put(REPORT_JS_FROM_KEY, "native"); //异常上报来源客户端
            runtime.getRuntimeReporter().reportJSError(jsonObject, runtime);
        } catch (JSONException jsonException) {
            MSCLog.e("[MSCModuleCallExceptionHandler@handleException]", jsonException);
        }
    }

    protected JSONObject generateErrorInfo(Exception e) throws JSONException {
        // 异常上报
        if (runtime == null || e == null) {
            return null;
        }
        // 运行时销毁后，模块找不到异常不再上报到jserror
        if (runtime.isDestroyed && e instanceof MSCModuleNotFoundException) {
            return null;
        }
        CrashReporter.storeCrash(e, "MSC", false);

        Pair<String, Throwable> pair = getExceptionInfo(e);
        if (MSCExceptionsManagerModule.isInternalEnv()) {
            ExceptionsInterface exceptionsInterface = runtime.getModuleWithoutDelegate(ExceptionsInterface.class);
            if (exceptionsInterface != null) {
                exceptionsInterface.showNewJavaError(pair.first, pair.second, runtime.getContainerManagerModule().getTopActivity());
            }
        }
        JSONObject jsonObject = new JSONObject();
        jsonObject.put("message", "[SystemError]" + pair.first);
        jsonObject.put("nativeStack", ExceptionHelper.readStack(e));
        jsonObject.put("isFatal", false);
        jsonObject.put("isNativeError", true);
        return jsonObject;
    }

    public void handleWarning(String strings) {
        if (MSCExceptionsManagerModule.isInternalEnv()) {
//            if (runtime != null && runtime.getContainerManagerModule().getTopActivity() != null) {
//                new AlertDialog.Builder(runtime.getContainerManagerModule().getTopActivity())
//                        .setMessage(strings)
//                        .setTitle("Warning:")
//                        .setNegativeButton("知道了", null)
//                        .show();
//                return;
//            }
        }
        MSCLog.w(null, null, strings);
    }

    protected Pair<String, Throwable> getExceptionInfo(Exception e) {
        // Debug 环境
        StringBuilder message = new StringBuilder();
        message.append(e.getMessage() == null ? "Exception in native call from JS" : e.getMessage());
        Throwable cause = e.getCause();
        Throwable originCause = e;
        while (cause != null) {
            message.append("\n\n").append(cause.getMessage());
            cause = cause.getCause();
            originCause = originCause.getCause();
        }
        // TODO #11638796: convert the stack into something useful
        if (e instanceof JSException) {
            String stack = ((JSException) e).getStack();
            message.append("\n\n").append(stack);
        }
        return new Pair<String, Throwable>(message.toString(), originCause);
    }
}

