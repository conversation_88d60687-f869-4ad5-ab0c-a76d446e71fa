package com.meituan.msc.modules.manager;

/**
 * Created by letty on 2022/3/7.
 **/
public abstract class ExecutorContext {
    public abstract void invokeCallback(int callbackID, Object arguments);

    public MSCHandler acquireAsyncMethodHandler() {
        return null;
    } // 不提供则默认使用总线提供异步线程池执行
//
//    public IDataParser acquireDataParser() {
//        return null;
//    }

    public void invokeCallbackInner(int callbackId, Object value) {
        invokeCallback(callbackId, value);
    }



}