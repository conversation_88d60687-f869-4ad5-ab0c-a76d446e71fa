package com.meituan.msc.modules.manager;

import android.content.Context;

import com.meituan.msc.jse.bridge.JSInstance;
import com.meituan.msc.modules.engine.MSCRuntime;
import com.meituan.msc.modules.page.render.webview.OnEngineInitFailedListener;

import org.json.JSONArray;

import java.util.Map;

/**
 * 传递msc library 模块一些时机给调用
 */
public interface IMSCLibraryInterface {

    void onHornInit(Context context, Map<String, Object> query);

    // module 注册相关
    void onMSCRuntimeCreate(MSCModuleManager moduleManager, OnEngineInitFailedListener onEngineInitFailedListener);

    // css lib预加载相关
    void onMSCEnvHelperInit(Context context);

    // MSCModuleManager.invoke
    void onMSCModuleManagerInvoke(MSCModule module, String methodName, JSONArray params);

    void onJSCServiceEngineRelease(JSInstance jsInstance);

    void onContainerControllerCreateRuntime();

    void onAppRoute(MSCRuntime runtime, String openType, String paramsString, int viewId);

    void onPendingAppRouteRun(MSCRuntime runtime, String paramsString, int viewId);
}
