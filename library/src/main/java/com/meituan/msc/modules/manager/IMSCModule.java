package com.meituan.msc.modules.manager;

import android.support.annotation.Nullable;

import com.meituan.msc.jse.bridge.ICallFunctionContext;

import org.json.JSONArray;
import java.util.Set;

/**
 * Created by letty on 2022/1/17.
 **/
public interface IMSCModule {
    void destroy();

    // 注册类名，用于懒加载模块，调用getModule的时候再创建模块实例，要求
    void registerSubModule(Class<? extends MSCModule> moduleImplClazz, Class... interfaces) throws NoSuchFieldException, IllegalAccessException;

    void registerSubModule(@Nullable MSCModule module, Class... interfaces);

    void registerSubModule(@Nullable Set<MSCModule> module, Class... interfaces);

    void unregisterSubModule(@Nullable MSCModule module);

    /**
     * 全局模块方法调用  & 当前模块方法调用，moduleName = currentModuleName时为当前模块方法调用
     */
    Object invoke(String moduleName, String methodName, JSONArray params, ExecutorContext instance);

    /**
     * 子模块调用
     */
    Object dispatchCall(ICallFunctionContext context, String moduleName, String methodName, JSONArray params, ExecutorContext executorContext);

    /**
     * 获取子模块
     */
    @Nullable
    <T> T getSubModule(Class<T> classOfT);

    MSCModule getParentModule();
}
