package com.meituan.msc.modules.manager;

import android.support.annotation.NonNull;

import com.meituan.msc.jse.bridge.Arguments;

import org.json.JSONArray;

/**
 * Created by letty on 2023/3/27.
 **/
public class JSONDataParser implements IDataParser<JSONArray> {
    @Override
    public JSONArray serialize(Object object) {
        return getJSONArg(object);
    }

    private static volatile JSONDataParser sInstance;

    public static JSONDataParser getInstance() {
        if (sInstance == null) {
            synchronized (JSONDataParser.class) {
                if (sInstance == null) {
                    sInstance = new JSONDataParser();
                }
            }
        }
        return sInstance;
    }

    /**
     * 通信仅支持基础数据类型、map、array
     */
    @NonNull
    public static JSONArray getJSONArg(Object value) {
        if (value == null) {
            return new JSONArray();
        }
        // 用于兼容可变参数
        if (value.getClass().isArray()) {
            return Arguments.getJSArgs((Object[]) value);
        } else {
            return Arguments.getJSArgs(new Object[]{value});
        }
    }

    @NonNull
    public static JSONArray getJSONArgContainNativeMap(Object value) {
        if (value == null) {
            return new JSONArray();
        }
        // 兼容可变参数
        if (value.getClass().isArray()) {
            return Arguments.getJSArgsContainNativeMap((Object[]) value);
        } else {
            return Arguments.getJSArgsContainNativeMap(new Object[]{value});
        }
    }

    @NonNull
    public static JSONArray getJSONArgs(Object... value) {
        return Arguments.getJSArgs(value);
    }
}
