package com.meituan.msc.modules.manager;

import com.meituan.msc.common.executor.MSCExecutors;
import com.meituan.msc.modules.reporter.MSCLog;

import java.lang.reflect.InvocationTargetException;
import java.lang.reflect.Method;
import java.util.List;
import java.util.Map;
import java.util.Set;
import java.util.concurrent.ConcurrentHashMap;
import java.util.concurrent.CopyOnWriteArraySet;

public class MSCEventBus {

    private final String TAG = "MSCEventBus " + this.hashCode();
    private Map<String, Set<MSCSubscriber>> subscribers = new ConcurrentHashMap<>();


    public void destroy() {
        subscribers.clear();
    }

    public void register(String eventName, MSCSubscriber subscriber) {
        if (subscriber != null) {
            Set<MSCSubscriber> channel = subscribers.get(eventName);
            if (channel == null) {
                channel = new CopyOnWriteArraySet<>();
                subscribers.put(eventName, channel);
            }
            channel.add(subscriber);
        }
    }

    public void unregister(MSCSubscriber subscriber) {
        if(subscriber == null){
            return;
        }
        for (Set<MSCSubscriber> channel : subscribers.values()) {
            if (channel.contains(subscriber)) {
                channel.remove(subscriber);
            }
        }
    }


    public void unregister(String eventName) {
        subscribers.remove(eventName);
    }

    /**
     * 通过总线向各个模块广播一个事件，只接受单一参数
     */
    public void publish(MSCEvent event) {

        MSCExecutors.submit(new Runnable() {
            @Override
            public void run() {
                if (event == null || event.getName() == null) return;
                Set<MSCSubscriber> channel = subscribers.get(event.getName());
                if (channel == null) return;
                MSCLog.i(TAG, "publish msc event ", event.getName());
                for (MSCSubscriber subscriber : channel) {
                    subscriber.onReceive(event);
                }
            }
        });
    }
}