package com.meituan.msc.modules.manager;

import com.meituan.msc.common.executor.MSCExecutors;

/**
 * Created by letty on 2022/3/8.
 **/
public abstract class MSCHandler {
    public abstract void handle(Runnable runnable);

    static MSCHandler mscHandler = new MSCHandler() {
        @Override
        public void handle(Runnable runnable) {
            MSCExecutors.submit(runnable);
        }
    };

    public static MSCHandler getMSCHandler(ExecutorContext executorContext, MSCModule module) {
        if (module != null && module.acquireAsyncMethodHandler() != null) {
            return module.acquireAsyncMethodHandler();
        } else if (executorContext != null && executorContext.acquireAsyncMethodHandler() != null) {
            return executorContext.acquireAsyncMethodHandler();
        } else {
            return mscHandler;
        }
    }
}
