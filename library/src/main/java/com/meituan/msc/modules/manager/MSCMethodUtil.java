package com.meituan.msc.modules.manager;

import com.facebook.infer.annotation.Assertions;
import com.meituan.msc.jse.bridge.ICallFunctionContext;

import org.json.JSONArray;
import org.json.JSONObject;

import java.lang.reflect.Method;

public class MSCMethodUtil {

    public static Object[] parseParams(ICallFunctionContext context, ExecutorContext executorContext, Method method, JSONArray params) {
        Class[] parameterTypes = method.getParameterTypes();
        Object[] result = new Object[parameterTypes.length];
        int jsIndex = 0; // js侧给的参数
        int nativeIndex = 0; //native侧接收的参数，native侧参数个数和js侧给的参数个数可能不一致，promise会占用2个参数
        int extraParams = 0; //native比js多的参数
        while (nativeIndex < parameterTypes.length && jsIndex < params.length()) {
            Class argumentClass = parameterTypes[nativeIndex];
            if (argumentClass == Boolean.class || argumentClass == boolean.class) {
                result[nativeIndex] = params.optBoolean(jsIndex);
            } else if (argumentClass == Long.class || argumentClass == long.class) {
                result[nativeIndex] = params.optLong(jsIndex);
            } else if (argumentClass == Integer.class || argumentClass == int.class) {
                result[nativeIndex] = params.optInt(jsIndex);
            } else if (argumentClass == Double.class || argumentClass == double.class) {
                result[nativeIndex] = params.optDouble(jsIndex);
            } else if (argumentClass == Float.class || argumentClass == float.class) {
                result[nativeIndex] = (float) params.optDouble(jsIndex);
            } else if (argumentClass == String.class) {
                result[nativeIndex] = params.optString(jsIndex);
            } else if (argumentClass == IMSCCompletableCallback.class || IMSCCompletableCallback.class.isAssignableFrom(argumentClass)) {
                if (params.isNull(jsIndex)) {
                    result[nativeIndex] = null;
                } else {
                    int id = (int) params.optDouble(jsIndex);
                    result[nativeIndex] = new MSCNativeCompletableCallback(executorContext, id);
                }
            } else if (argumentClass == IMSCPromiseCallback.class || IMSCPromiseCallback.class.isAssignableFrom(argumentClass)) { //IMSCPromiseCallback必须是方法的最后一个参数
                Assertions.assertCondition(
                        nativeIndex == parameterTypes.length - 1, "Promise must be used as last parameter only");
                if (params.isNull(jsIndex) || params.isNull(jsIndex + 1)) {
                    result[nativeIndex] = null;
                } else {
                    int successId = (int) params.optDouble(jsIndex);
                    int failId = (int) params.optDouble(jsIndex + 1);
                    MSCNativeCompletableCallback successCallback = new MSCNativeCompletableCallback(executorContext, successId);
                    MSCNativeCompletableCallback failCallback = new MSCNativeCompletableCallback(executorContext, failId);
                    result[nativeIndex] = new MSCNativePromiseCallback(successCallback, failCallback);
                }
                jsIndex++; //promise多加一次
            } else if (argumentClass == JSONObject.class) {
                result[nativeIndex] = params.optJSONObject(jsIndex);
            } else if (argumentClass == JSONArray.class) {
                result[nativeIndex] = params.optJSONArray(jsIndex);
            } else if (ICallFunctionContext.class.isAssignableFrom(argumentClass)) {
                result[nativeIndex] = context;
                nativeIndex += 1;
                extraParams++;
                continue;
            } else {
                throw new RuntimeException("Got unknown argument class: " + argumentClass.getSimpleName());
            }
            nativeIndex++;
            jsIndex++;
        }
        if (parameterTypes.length - extraParams > params.length()) {
            throw new RuntimeException("The number of parameters does not match ！！" + method);
        }
        return result;
    }
}
