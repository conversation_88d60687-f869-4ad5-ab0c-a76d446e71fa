package com.meituan.msc.modules.manager;

import android.support.annotation.NonNull;
import android.support.annotation.VisibleForTesting;
import android.text.TextUtils;

import com.meituan.crashreporter.CrashReporter;
import com.meituan.msc.jse.bridge.CallFunctionContext;
import com.meituan.msc.jse.bridge.ICallFunctionContext;
import com.meituan.msc.modules.container.IContainerManager;
import com.meituan.msc.modules.engine.MSCApp;
import com.meituan.msc.modules.engine.MSCHornRollbackConfig;
import com.meituan.msc.modules.engine.MSCRuntime;
import com.meituan.msc.modules.page.IPageModule;
import com.meituan.msc.modules.reporter.MSCLog;

import org.json.JSONArray;
import org.json.JSONObject;

import java.lang.reflect.InvocationTargetException;
import java.lang.reflect.Method;
import java.util.HashSet;
import java.util.Iterator;
import java.util.Map;
import java.util.Set;
import java.util.concurrent.ConcurrentHashMap;

import javax.annotation.Nullable;

/**
 * <AUTHOR>
 * @since 2021/12/22.
 */

/**
 * 抽象类，所有模块必须集成自此类，才能添加到总线中
 */
public abstract class MSCModule implements IMSCModule {
    private static final String TAG = "MSCModule";
    private String name;
    private MSCModule mParentModule;
    private volatile boolean initiated = false;
    private MSCRuntime runtime;
    private final Map<String, Method> mscMethods = new ConcurrentHashMap<>();
    private final Map<String, Method> mscSyncMethods = new ConcurrentHashMap<>();
    private final Map<String, Method> mscPromiseMethods = new ConcurrentHashMap<>();

    private MSCModuleManager subModuleManager;
    private volatile MSCApp app;

    public JSONObject getConstants() {
        return new JSONObject();
    }

    public String getName() {
        if (TextUtils.isEmpty(name)) {
            name = ModuleManagerUtil.getName(getClass(), true);
        }
        return name;
    }


    public JSONArray getConfig() {
        JSONArray array = new JSONArray();
        array.put(getName());
        array.put(getConstants());
        array.put(new JSONArray(mscMethods.keySet()));
        array.put(new JSONArray(mscPromiseMethods.keySet()));
        array.put(new JSONArray(mscSyncMethods.keySet()));
        return array;
    }

    // 模块注册到runtime时调用
    public void attachedRuntime(MSCRuntime runtime) {
        this.runtime = runtime;
        app = runtime.getApp();
        subModuleManager = new MSCModuleManager(runtime).setParentModule(this);
        //onRuntimeAttached(runtime);
    }

    // 小程序启动时调用，模块需要已经注册到runtime才能收到此调用
    public void attachApp(MSCApp app) {
        this.app = app;
        if (subModuleManager != null) {
            subModuleManager.attachApp(app);
        }
        onAppStart(app);
    }

    public void onRuntimeAttached(MSCRuntime runtime) {

    }

    public void onAppStart(MSCApp app) {

    }

    public void onDestroy() {

    }

    /**
     * 处理子模块找无法找到
     *
     * @param moduleName
     * @param methodName
     * @return 返回 false 默认不处理，交由总线统一处理
     */
    public boolean onSubModuleNotFound(String moduleName, String methodName) {
        return false;
    }

    protected MSCHandler acquireAsyncMethodHandler() {
        return null;
    } // 不提供则默认使用总线提供异步线程池执行

    public Object invoke(String moduleName, String methodName, JSONArray params, ExecutorContext instance) {
        if (TextUtils.equals(getName(), moduleName)) {
            return MSCModuleManager.invoke(CallFunctionContext.DO_NOTHING_CONTEXT, this, methodName, params, instance);
        } else {
            if (runtime == null) {
                throw new MSCRuntimeException(String.format("error runtime null , called %s , %s ", moduleName, methodName));
            }
            return runtime.invoke(CallFunctionContext.DO_NOTHING_CONTEXT, moduleName, methodName, params, instance);
        }
    }

    protected Object invoke(ICallFunctionContext context, Method method, JSONArray params, ExecutorContext executorContext) {
        if (method == null) {
            throw new RuntimeException(String.format("Can't find method '%s' in '%s' module", "null", this.getName()));
        }
        context.getTrace().instant("parseParamsStart");
        Object[] parsedParams = MSCMethodUtil.parseParams(context, executorContext, method, params);
        context.getTrace().instant("parseParamsEnd");
        try {
            return method.invoke(this, parsedParams);
        } catch (IllegalAccessException e) {
            String msg = "invoking method '" + method.getName() + "' failed";
            MSCLog.e(TAG, e, msg);
            throw new MSCMethodInvocationException("invoking method '" + method.getName() + "' failed", e);
        } catch (InvocationTargetException e) {
            /**
             * 捕获异常上报 方便排查问题
             */
            CrashReporter.storeCrash(e.getCause(), "MSC-ModuleManager", false);
            // 捕获异常被采样了 暂时沟通未果 先往logan里打一份 方便排查问题
            String msg = "invoking method '" + method.getName() + "' failed";
            MSCLog.e(TAG, e, msg);
            throw new MSCMethodInvocationException(msg, e);
        }
    }

    public MSCRuntime getRuntime() {
        checkRuntimeOrThrow();
        return runtime;
    }

    @Nullable
    public MSCApp getApp() {
        return app;
    }

    @NonNull
    public MSCApp getAppOrThrow() {
        checkAppOrThrow();
        return app;
    }

    // 子模块不想注册，想要自行分发或动态创建对象进行调用时可重写此方法，不走subModuleManager.invoke
    public Object dispatchCall(ICallFunctionContext context, String moduleName, String methodName, JSONArray params, ExecutorContext executorContext) {
        return subModuleManager != null ? subModuleManager.invoke(context, moduleName, methodName, params, executorContext) : null;
    }


    // 子模块不想注册，想要自行分发或动态创建对象进行调用时可重写此方法，不走subModuleManager.invoke
    public JSONArray dispatchGetConfig(String moduleName) {
        return subModuleManager != null ? subModuleManager.getConfig(moduleName) : null;
    }


    // 注册类名，用于懒加载模块，调用getModule的时候再创建模块实例，要求
    public void registerSubModule(Class<? extends MSCModule> moduleImplClazz, Class... interfaces) throws NoSuchFieldException, IllegalAccessException {
        if (subModuleManager != null) {
            subModuleManager.registerModule(moduleImplClazz, interfaces);
        }
    }

    @Override
    public void registerSubModule(Set<MSCModule> module, Class... interfaces) {
        Iterator<MSCModule> iterator = module.iterator();
        while (iterator.hasNext()) {
            registerSubModule(iterator.next(), interfaces);
        }
    }

    public void registerSubModule(MSCModule module, Class... interfaces) {
        if (subModuleManager != null) {
            subModuleManager.registerModule(module, interfaces);
        }
    }

    public void unregisterSubModule(MSCModule module) {
        if (subModuleManager != null) {
            subModuleManager.unregisterModule(module);
        }
    }

    @Override
    public void destroy() {
        onDestroy();
        //从父模块中移除
        if (getParentModule() != null) {
            getParentModule().unregisterSubModule(this);
            mParentModule = null;
        }
        //销毁子模块
        if (subModuleManager != null) {
            subModuleManager.destroy();
            subModuleManager = null;
        }
    }

    public MSCModule getModule(String name) {
        if (runtime == null) {
            throw new MSCModuleNotFoundException("no module manager attached");
        } else {
            MSCModule module = runtime.getModule(name);
            if (module == null)
                throw new MSCModuleNotFoundException(" module with name " + name + " not found");
            return module;
        }
    }

    public <T> T getModule(Class<T> classOfT) {
        if (runtime == null) {
            throw new MSCModuleNotFoundException("no module manager attached");
        } else {
            T module = runtime.getModule(classOfT);
            if (module == null)
                MSCLog.e(TAG, " module with class " + classOfT.getCanonicalName() + " not found");
            return module;
        }
    }

    public MSCModule getSubModule(String name) {
        if (subModuleManager == null || TextUtils.isEmpty(name)) {
            return null;
        }
        MSCModule module = subModuleManager.getModule(name);
        if (module == null) {
            throw new MSCModuleNotFoundException(" submodule with name " + name + " not found");
        }
        return module;
    }

    public <T> T getSubModule(Class<T> classOfT) {
        if (subModuleManager == null || classOfT == null) {
            return null;
        }
        T module = subModuleManager.getModule(classOfT);
        if (module == null) {
            MSCLog.e(TAG, " submodule with class " + classOfT.getCanonicalName() + " not found");
        }
        return module;
    }

    public void ensureInit() {
        if (!initiated) {
            initMethods();
            initiated = true;
        }
    }

    public Method getMSCMethod(String name) {
        return mscMethods.get(name);
    }

    //fixme 同步方法临时这么判断
    public boolean isSyncMethod(String name) {
        return mscSyncMethods.get(name) != null;
    }

    @Override
    public MSCModule getParentModule() {
        return mParentModule;
    }

    public MSCModule setParentModule(MSCModule parentModule) {
        mParentModule = parentModule;
        return this;
    }


    // 初始化模块方法
    protected void initMethods() {
        initMSCMethods();
        //initMRNMethods();
    }

    /**
     * 初始化耗时：初始化耗时 50ms -> 6ms 减少无用方法遍历
     */
    private void initMSCMethods() {
        Class<?> clazz = getClass();
        Set<? extends Class<?>> supers = getSuperClass(clazz);
        Iterator i = supers.iterator();
        while (i.hasNext()) {
            Class<?> superClazz = (Class) i.next();
            Method[] arr = superClazz.getDeclaredMethods();
            int len = arr.length;

            for (int methodIndex = 0; methodIndex < len; ++methodIndex) {
                Method superClazzMethod = arr[methodIndex];

                // 查询所有MSCMethod注解标记的方法
                if (superClazzMethod.isAnnotationPresent(MSCMethod.class)) {

                    MSCMethod annotation = superClazzMethod.getAnnotation(MSCMethod.class);
                    // 获取注解值
                    String invokePath = superClazzMethod.getName();
                    mscMethods.put(invokePath, superClazzMethod);
                    if (annotation.isSync()) {
                        mscSyncMethods.put(invokePath, superClazzMethod);
                    }
                    Class[] parameterTypes = superClazzMethod.getParameterTypes();
                    int mParamLength = parameterTypes.length;
                    if (mParamLength > 0 && (parameterTypes[mParamLength - 1] == IMSCPromiseCallback.class)) {
                        mscPromiseMethods.put(invokePath, superClazzMethod);
                    }
                }
            }
        }
    }


    static Class<MSCModule> sMscClassName = MSCModule.class;

    private static Set<Class<?>> getSuperClass(Class<?> clazz) {
        Set<Class<?>> clazzs = new HashSet<Class<?>>();
        clazzs.add(clazz);
        Class<?> suCl = clazz.getSuperclass();
        // 获取到MSCModule的子类为止
        while (suCl != null && suCl != sMscClassName) {
            clazzs.add(suCl);
            suCl = suCl.getSuperclass();
        }
        return clazzs;
    }

    private void checkRuntimeOrThrow() {
        if (runtime == null) {
            throw new MSCRuntimeException("no msc runtime attached, check if the module is registered to runtime");
        }
    }


    private void checkAppOrThrow() {
        if (app == null)
            throw new MSCRuntimeException("no msc app context attached, check if msc App is started");
    }


    protected IPageModule getPageById(int id) {
        if (runtime == null) {
            return null;
        }
        IContainerManager containerManager = runtime.getContainerManagerModule();
        if (containerManager == null) {
            return null;
        }
        return containerManager.getPageByPageId(id);
    }

    @VisibleForTesting
    public void setRuntime(MSCRuntime runtime) {
        this.runtime = runtime;
    }
}
