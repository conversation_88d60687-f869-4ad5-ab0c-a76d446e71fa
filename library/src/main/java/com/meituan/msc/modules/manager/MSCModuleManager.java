package com.meituan.msc.modules.manager;

import android.text.TextUtils;

import com.meituan.msc.common.utils.MSCTraceUtil;
import com.meituan.msc.jse.bridge.CallFunctionContext;
import com.meituan.msc.jse.bridge.ICallFunctionContext;
import com.meituan.msc.modules.IMSCLibraryInterfaceHelper;
import com.meituan.msc.modules.engine.MSCApp;
import com.meituan.msc.modules.engine.MSCRuntime;
import com.meituan.msc.modules.page.PageModule;
import com.meituan.msc.modules.reporter.MSCLog;
import com.meituan.msc.util.perf.PerfTrace;

import org.json.JSONArray;
import org.json.JSONException;
import org.json.JSONObject;

import java.lang.reflect.Method;
import java.util.HashSet;
import java.util.Map;
import java.util.Set;
import java.util.concurrent.ConcurrentHashMap;

/**
 * <AUTHOR>
 * @since 2021/12/22.
 */

public class MSCModuleManager {

    private final static String TAG = "MSCModuleManager";

    private final static String MSC_LIST_REACT_CLASS = "MSCListView";
    private final static String PERF_LIST_REACT_CLASS = "MSCRList";

    private Map<String, MSCModule> modules = new ConcurrentHashMap<>();
    private Map<String, Class<? extends MSCModule>> moduleClazz = new ConcurrentHashMap<>();
    private Map<Class, Set<String>> typeToNamesMap = new ConcurrentHashMap<>();
    private MSCRuntime runtime;
    private MSCModule mParentModule;
    private volatile boolean destroyed = false;

    public MSCModuleManager(MSCRuntime runtime) {
        this.runtime = runtime;
    }

    public MSCRuntime getRuntime() {
        return runtime;
    }

    public MSCApp getApp() {
        if (runtime != null) {
            return runtime.getApp();
        }
        return null;
    }

    public void attachApp(MSCApp app) {
        for (MSCModule module : modules.values()) {
            module.attachApp(app);
        }
    }

    // 注册类名，用于懒加载模块，调用getModule的时候再创建模块实例，要求
    public void registerModule(Class<? extends MSCModule> moduleImplClazz, Class... interfaces) throws RuntimeException {
        if (moduleImplClazz != null) {
            String name = ModuleManagerUtil.getName(moduleImplClazz, true);
            synchronized (this) {
                moduleClazz.put(name, moduleImplClazz);
                if (interfaces != null) {
                    for (Class i : interfaces) {
                        Set<String> names = typeToNamesMap.get(i);
                        if (names == null) {
                            names = new HashSet<>();
                        }
                        names.add(name);
                        typeToNamesMap.put(i, names);
                    }
                }
            }
        }
    }

    public void registerModule(MSCModule module, Class... interfaces) {
        if (module != null) {
            MSCLog.d(TAG, "registerModule", module);
            module.setParentModule(getParentModule());
            module.attachedRuntime(runtime);
            String name = module.getName();
            synchronized (this) {
                modules.put(name, module);
                if (interfaces != null) {
                    for (Class i : interfaces) {
                        Set<String> names = typeToNamesMap.get(i);
                        if (names == null) {
                            names = new HashSet<>();
                        }
                        names.add(name);
                        typeToNamesMap.put(i, names);
                    }
                }
            }
            module.onRuntimeAttached(runtime);
        }
    }

    public synchronized void unregisterModule(MSCModule module) {
        if (module != null && modules.containsValue(module)) {
            modules.values().remove(module);
            module.destroy();
        }
    }


    public void destroy() {
        if (destroyed) {
            return;
        }
        destroyed = true;
        for (MSCModule module : modules.values()) {
            module.destroy();
        }
        synchronized (this) {
            modules.clear();
            moduleClazz.clear();
            typeToNamesMap.clear();
        }
    }

    public <T> T getModule(Class<T> classOfT) {
        Set<String> names = typeToNamesMap.get(classOfT);
        String name = null;
        if (names != null) {
            name = names.iterator().next();
        }
        // 支持根据 class 的注解找对应的模块
        if (TextUtils.isEmpty(name)) {
            name = ModuleManagerUtil.getName(classOfT, false);
        }
        if (TextUtils.isEmpty(name)) {
            return null;
        }
        return (T) (getModule(name));
    }


    public MSCModule getModule(String name) {
        MSCModule module = null;
        if (destroyed) {
            MSCLog.e(TAG, "trying to get module " + name + " after runtime destroyed");
            return module;
        }
        boolean lazyCreated = false;
        synchronized (this) {
            module = modules.get(name);
            if (module == null) {
                Class<? extends MSCModule> moduleClass = moduleClazz.get(name);
                if (moduleClass != null) {
                    try {
                        lazyCreated = true;
                        module = moduleClass.newInstance();
                        module.setParentModule(getParentModule());
                        module.attachedRuntime(runtime);
                        modules.put(name, module);
                        Set<String> names = typeToNamesMap.get(moduleClass);
                        if (names == null) {
                            names = new HashSet<>();
                        }
                        names.add(name);
                        typeToNamesMap.put(moduleClass, names);
                    } catch (Throwable e) {
                        MSCLog.e("[MSCModuleManager@getModule] " + name, e);
                    }
                }
            }
        }
        if (lazyCreated && module != null) {
            module.onRuntimeAttached(runtime);
        } else if (module == null){
            MSCLog.e("[MSCModuleManager@getModule]", "module " + name + " null, lazyCreated: " + lazyCreated);
        }

        return module;
    }

    public Object invoke(ICallFunctionContext context, String moduleName, String methodName, JSONArray params, ExecutorContext instance) {
        return invokeInner(context, moduleName, methodName, params, instance);
    }

    /**
     * 通过总线同步调用模块提供的方法，指定模块名，方法名，参数。同步
     */
    public Object invokeSync(String moduleName, String methodName, JSONArray params) {
        return invokeInner(CallFunctionContext.DO_NOTHING_CONTEXT, moduleName, methodName, params, null);
    }

    /**
     * 获取桥的信息
     *
     * @param moduleName
     * @return
     */
    public JSONArray getConfig(String moduleName) {
        String[] submoduleNames = moduleName.split("\\.", 2);
        if (submoduleNames.length == 1) {
            MSCModule module = getModule(moduleName);
            if (module == null) {
                handleModuleNotFound(moduleName, null);
                return null;
            } else {
                module.ensureInit();
                return module.getConfig();
            }
        } else {
            MSCModule module = getModule(submoduleNames[0]);
            if (module == null) {
                handleModuleNotFound(submoduleNames[0], null);
                return null;
            }
            return module.dispatchGetConfig(submoduleNames[1]);
        }
    }

    private Object invokeInner(ICallFunctionContext context, String moduleName, String methodName, JSONArray params, ExecutorContext executorContext) {
        String[] submoduleNames = moduleName.split("\\.", 2);
        if (submoduleNames.length == 1) {
            context.getTrace().instant("findSubModuleStart");
            MSCModule module = getModule(moduleName);
            context.getTrace().instant("findSubModuleEnd");
            if (module == null) {
                return handleModuleNotFound(moduleName, methodName);
            } else {
                return invoke(context, module, methodName, params, executorContext);
            }
        } else {
            context.getTrace().instant("findPageModuleStart");
            MSCModule module = getModule(submoduleNames[0]);
            context.getTrace().instant("findPageModuleEnd");
            if (module == null) {
                return handleModuleNotFound(moduleName, methodName);
            }
            return module.dispatchCall(context, submoduleNames[1], methodName, params, executorContext);
        }
    }

    private Object handleModuleNotFound(String moduleName, String methodName) {
        if (getParentModule() != null && getParentModule().onSubModuleNotFound(moduleName, methodName)) {
            MSCLog.e(TAG, getParentModule().getName() + " handled subModuleNotFound with name '" + moduleName + "'");
            return null;
        }
        String parentModuleName = mParentModule != null ?
                (mParentModule instanceof PageModule ? "PageModule" : mParentModule.getName()) : "native";
        String errInfo = String.format("cannot find module with name '%s' in %s for method %s", moduleName, parentModuleName, methodName);
        throw new MSCModuleNotFoundException(errInfo);
    }

    public static Object invoke(ICallFunctionContext context, MSCModule module, String methodName, JSONArray params, ExecutorContext executorContext) {
        module.ensureInit();
        Method method = module.getMSCMethod(methodName);
        if (method == null) {
            throw new MSCRuntimeException(String.format("Can't find method '%s' in '%s' module", methodName, module.getName()));
        }
        String traceEventName = module.getName() + "_" + methodName + "_js";
        if (MSCTraceUtil.TRACE_LEVEL >= MSCTraceUtil.LEVEL_HEAVY) {
            PerfTrace.begin(traceEventName);
        }
        JSONArray finalParams;
        if (methodName.equals("batchDidComplete")) {
            finalParams = new JSONArray();
            finalParams.put(System.currentTimeMillis());
        } else if (methodName.equals("batchDidCompleteWithOption")) {
            finalParams = params;
            if (finalParams.optJSONObject(0) != null) {
                try {
                    finalParams.optJSONObject(0).put("jsTimeStamp", System.currentTimeMillis());
                } catch (JSONException e) {
                    MSCLog.e(TAG, e);
                }
            }
        } else {
            finalParams = params;
        }

        onMSCModuleInvoke(module, methodName, params);

        if (module.isSyncMethod(methodName)) {
            Object result = invokeMSCMethod(context, module, method, finalParams, executorContext);
            if (MSCTraceUtil.TRACE_LEVEL >= MSCTraceUtil.LEVEL_HEAVY) {
                if ("MSIManager_syncInvoke_js".equals(traceEventName)) {
                    Object firstParam = finalParams == null ? null : finalParams.opt(0);
                    PerfTrace.end(traceEventName).arg("params", firstParam);
                } else {
                    PerfTrace.end(traceEventName);
                }
            }
            return result;
        } else {
            MSCHandler mscHandler = MSCHandler.getMSCHandler(executorContext, module);
            if (mscHandler instanceof MSCRawParamHandler) {
                ((MSCRawParamHandler) mscHandler).handle(module, method, finalParams, executorContext);
            } else {
                Runnable runnable = new Runnable() {
                    @Override
                    public void run() {
                        // fixme renjihai
                        // 当前为了显示native_modules所有任务，在这里添加了冗余trace
                        // 后期渲染线程独立后，只需要在MSCUIManagerModule记录即可
                        if (MSCTraceUtil.TRACE_LEVEL >= MSCTraceUtil.LEVEL_HEAVY) {
                            PerfTrace.begin(module.getName() + "_" + methodName + "_" + Thread.currentThread().getName());
                        }
                        invokeMSCMethod(context, module, method, finalParams, executorContext);
                        // fixme renjihai
                        // 当前为了显示native_modules所有任务，在这里添加了冗余trace
                        // 后期渲染线程独立后，只需要在MSCUIManagerModule记录即可
                        if (MSCTraceUtil.TRACE_LEVEL >= MSCTraceUtil.LEVEL_HEAVY) {
                            PerfTrace.end(module.getName() + "_" + methodName + "_" + Thread.currentThread().getName());
                        }
                    }
                };
                mscHandler.handle(runnable);
            }
            if (MSCTraceUtil.TRACE_LEVEL >= MSCTraceUtil.LEVEL_HEAVY) {
                PerfTrace.end(traceEventName);
            }
            return null;
        }
    }

    private static void onMSCModuleInvoke(MSCModule module, String methodName, JSONArray params) {
        IMSCLibraryInterface libraryInterface = IMSCLibraryInterfaceHelper.getIMSCLibraryInterface();
        if (libraryInterface != null) {
            libraryInterface.onMSCModuleManagerInvoke(module, methodName, params);
        }
    }

    public static Object invokeMSCMethod(ICallFunctionContext context, MSCModule module, Method method, JSONArray params, ExecutorContext executorContext) {
        return module.invoke(context, method, params, executorContext);
    }

    public MSCModule getParentModule() {
        return mParentModule;
    }

    public MSCModuleManager setParentModule(MSCModule parentModule) {
        mParentModule = parentModule;
        return this;
    }
}
