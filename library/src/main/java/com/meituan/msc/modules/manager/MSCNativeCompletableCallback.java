package com.meituan.msc.modules.manager;

import com.meituan.msc.modules.reporter.MSCLog;

public class MSCNativeCompletableCallback implements IMSCCompletableCallback {
    private final ExecutorContext mExecutorContext;
    private final int mCallbackId;
    private boolean mInvoked;

    public MSCNativeCompletableCallback(ExecutorContext jsInstance, int callbackId) {
        mExecutorContext = jsInstance;
        mCallbackId = callbackId;
        mInvoked = false;
    }

    @Override
    public void onComplete(Object value) {
        if (mInvoked) {
            return;
        }
        if (mExecutorContext == null) {
            MSCLog.throwIfDebug("MSCNativeCompletableCallback when mExecutorContext is null");
            return;
        }
        mExecutorContext.invokeCallbackInner(mCallbackId, value);
        mInvoked = true;
    }
}
