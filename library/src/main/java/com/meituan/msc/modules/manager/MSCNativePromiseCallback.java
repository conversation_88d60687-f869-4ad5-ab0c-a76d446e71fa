package com.meituan.msc.modules.manager;

import android.support.annotation.Nullable;

import org.json.JSONArray;
import org.json.JSONException;
import org.json.JSONObject;

public class MSCNativePromiseCallback implements IMSCPromiseCallback {
    // for ERROR_MAP_KEY_NATIVE_STACK
    private static final int ERROR_STACK_FRAME_LIMIT = 50;

    private static final String ERROR_DEFAULT_CODE = "EUNSPECIFIED";
    private static final String ERROR_DEFAULT_MESSAGE = "Error not specified.";

    // Keys for mReject's WritableMap
    private static final String ERROR_MAP_KEY_CODE = "code";
    private static final String ERROR_MAP_KEY_MESSAGE = "message";
    private static final String ERROR_MAP_KEY_USER_INFO = "userInfo";
    private static final String ERROR_MAP_KEY_NATIVE_STACK = "nativeStackAndroid";

    // Keys for ERROR_MAP_KEY_NATIVE_STACK's StackFrame maps
    private static final String STACK_FRAME_KEY_CLASS = "class";
    private static final String STACK_FRAME_KEY_FILE = "file";
    private static final String STACK_FRAME_KEY_LINE_NUMBER = "lineNumber";
    private static final String STACK_FRAME_KEY_METHOD_NAME = "methodName";

    private @Nullable
    IMSCCompletableCallback mSuccess;
    private @Nullable
    IMSCCompletableCallback mFail;

    public MSCNativePromiseCallback(IMSCCompletableCallback success, IMSCCompletableCallback fail) {
        this.mSuccess = success;
        this.mFail = fail;
    }

    @Override
    public void onSuccess(Object value) {
        if (mSuccess != null) {
            mSuccess.onComplete(value);
            mSuccess = null;
            mFail = null;
        }
    }

    @Override
    public void onError(String msg) {
        onError(msg, null, null);
    }

    @Override
    public void onError(String msg, Throwable error) {
        onError(msg, error, null);
    }

    @Override
    public void onError(String msg, Throwable error, JSONObject userInfo) {
        if (mFail == null) {
            mSuccess = null;
            return;
        }
        JSONObject errorInfo = new JSONObject();
        try {
            if (msg != null) {
                errorInfo.put(ERROR_MAP_KEY_MESSAGE, msg);
            } else if (error != null) {
                errorInfo.put(ERROR_MAP_KEY_MESSAGE, error.getMessage());
            } else {
                // The JavaScript side expects a map with at least an error message.
                // /Libraries/BatchedBridge/NativeModules.js -> createErrorFromErrorData
                // TYPE: (errorData: { message: string })
                errorInfo.put(ERROR_MAP_KEY_MESSAGE, ERROR_DEFAULT_MESSAGE);
            }
            if (error != null) {
                StackTraceElement[] stackTrace = error.getStackTrace();
                JSONArray nativeStackAndroid = new JSONArray();

                // Build an an Array of StackFrames to match JavaScript:
                // iOS: /Libraries/Core/Devtools/parseErrorStack.js -> StackFrame
                for (int i = 0; i < stackTrace.length && i < ERROR_STACK_FRAME_LIMIT; i++) {
                    StackTraceElement frame = stackTrace[i];
                    JSONObject frameMap = new JSONObject();
                    // NOTE: no column number exists StackTraceElement
                    frameMap.put(STACK_FRAME_KEY_CLASS, frame.getClassName());
                    frameMap.put(STACK_FRAME_KEY_FILE, frame.getFileName());
                    frameMap.put(STACK_FRAME_KEY_LINE_NUMBER, frame.getLineNumber());
                    frameMap.put(STACK_FRAME_KEY_METHOD_NAME, frame.getMethodName());
                    nativeStackAndroid.put(frameMap);
                }

                errorInfo.put(ERROR_MAP_KEY_NATIVE_STACK, nativeStackAndroid);
            } else {
                errorInfo.put(ERROR_MAP_KEY_NATIVE_STACK, new JSONArray());
            }
        } catch (JSONException e) {
            e.printStackTrace();
        }

        if (userInfo != null) {
            try {
                errorInfo.put(ERROR_MAP_KEY_USER_INFO, userInfo);
            } catch (JSONException e) {
                e.printStackTrace();
            }
        }

        mFail.onComplete(errorInfo);
        mSuccess = null;
        mFail = null;
    }
}
