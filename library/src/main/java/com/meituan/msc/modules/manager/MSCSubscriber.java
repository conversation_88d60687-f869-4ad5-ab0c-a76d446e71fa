package com.meituan.msc.modules.manager;

import com.meituan.msc.modules.reporter.MSCLog;

import java.lang.reflect.Method;
import java.util.ArrayList;
import java.util.HashSet;
import java.util.Iterator;
import java.util.List;
import java.util.Map;
import java.util.Set;
import java.util.concurrent.ConcurrentHashMap;


public interface MSCSubscriber<T> {
    public void onReceive(MSCEvent<T> event);
}
