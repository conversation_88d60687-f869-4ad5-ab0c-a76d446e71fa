package com.meituan.msc.modules.manager;

import android.text.TextUtils;

import com.meituan.msc.modules.reporter.MSCLog;

/**
 * Created by letty on 2022/1/14.
 **/
class ModuleManagerUtil {
    public static <T> String getName(Class<T> classT,boolean throwIfEmptyName) throws RuntimeException {
        String name = null;
        try {
            if (classT.isAnnotationPresent(ModuleName.class)) {
                name = ((ModuleName) classT.getAnnotation(ModuleName.class)).name();
            }
        } catch (Exception e2) {
            MSCLog.e("MSCModule getName exp", e2);
        }
        if (throwIfEmptyName && TextUtils.isEmpty(name)) {
            throw new RuntimeException("Must declare name in ModuleName annotation for class " + classT.toString());
        }
        return name;
    }
}
