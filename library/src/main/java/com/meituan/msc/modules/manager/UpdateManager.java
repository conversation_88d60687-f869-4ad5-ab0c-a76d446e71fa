package com.meituan.msc.modules.manager;

import com.meituan.msc.common.utils.MiniProgramUtil;
import com.meituan.msc.lib.interfaces.container.MSCParams;
import com.meituan.msc.modules.container.IContainerDelegate;
import com.meituan.msc.modules.container.MSCWidgetFragment;
import com.meituan.msc.modules.engine.MSCRuntime;
import com.meituan.msc.modules.reporter.MSCLog;
import com.meituan.msi.bean.MsiContext;

import org.json.JSONException;
import org.json.JSONObject;

/**
 * 需求文档：https://km.sankuai.com/page/1450436218
 */

@ModuleName(name = "UpdateManager")
public class UpdateManager extends MSCModule {

    private static final String TAG = "UpdateManager";

    private volatile Status mStatus = Status.STATUS_INIT;

    public static enum Status {
        /**
         * 初始状态
         */
        STATUS_INIT,
        /**
         * 拉取元信息，当前有更新
         */
        STATUS_APP_HAS_UPDATE,
        /**
         * 拉取元信息，当前没有更新
         */
        STATUS_APP_NOT_HAS_UPDATE,
        /**
         * 下载新版本成功
         */
        STATUS_APP_DOWNLOAD_SUCCESS,
        /**
         * 下载新版本失败
         */
        STATUS_APP_DOWNLOAD_FAIL
    }

    private static final String MSC_UPDATE_MANAGE_CHECK = "onCheckForUpdate";
    private static final String MSC_UPDATE_MANAGE_READY = "onUpdateReady";
    private static final String MSC_UPDATE_MANAGE_FAILED = "onUpdateFailed";

    private void dispatchEvent(Status stauts) {
        MSCLog.i(TAG, "dispatchEvent, stauts = ", stauts);
        MSCRuntime runtime = getRuntime();
        if (stauts == Status.STATUS_APP_HAS_UPDATE || stauts == Status.STATUS_APP_NOT_HAS_UPDATE) {

            boolean hasUpdate = (stauts == Status.STATUS_APP_HAS_UPDATE) ? true : false;
            JSONObject data = new JSONObject();
            try {
                data.put("hasUpdate", hasUpdate);
            } catch (JSONException e) {
                MSCLog.e(TAG, "dispatchEvent, JSON exception");
                return;
            }
            runtime.apisManager.dispatchEvent(MSC_UPDATE_MANAGE_CHECK, data);

        } else if (stauts == Status.STATUS_APP_DOWNLOAD_SUCCESS) {

            runtime.apisManager.dispatchEvent(MSC_UPDATE_MANAGE_READY, null);

        } else if (stauts == Status.STATUS_APP_DOWNLOAD_FAIL) {

            runtime.apisManager.dispatchEvent(MSC_UPDATE_MANAGE_FAILED, null);

        }
    }

    public void setStatus(Status status) {
        mStatus = status;
        dispatchEvent(status);
    }

    public void applyUpdate(MsiContext context) {

        MSCRuntime runtime = getRuntime();
        MSCLog.i(TAG, "applyUpdate, mStatus = ", mStatus);

        if (mStatus == Status.STATUS_INIT) {
            context.onError(-1, "background has not checked");
            return;
        }

        if (mStatus == Status.STATUS_APP_NOT_HAS_UPDATE) {
            context.onError(-1, "update is not ready");
            return;
        }

        if (mStatus == Status.STATUS_APP_DOWNLOAD_FAIL) {
            context.onError(-1, "update failed");
            return;
        }

        if (runtime.getContainerManagerModule().getContainerCount() > 1) {
            context.onError(-1, "applyUpdate failed: invoke this api ,container should be only one");
            return;
        }

        IContainerDelegate topContainer = runtime.getContainerManagerModule().getTopContainer();
        if (topContainer == null || topContainer.getActivity().isFinishing() || topContainer.getActivity().isDestroyed()) {
            context.onError(-1, "applyUpdate failed");
            return;
        }

        runtime.setDestroyWhenUnused(true);
        runtime.setDisableReuse(true);

        if (topContainer.isWidget()) {
            ((MSCWidgetFragment) topContainer.getMSCContainer()).notifyReopenWidgetToNative();
            MSCLog.i(TAG, "UpdateManager widget applyUpdate, appId: ", runtime.getApp().getAppId());
        } else {
            topContainer.getIntent().putExtra(MSCParams.DISABLE_REUSE_ANY, true);
            MiniProgramUtil.reopenMiniProgram(topContainer.getActivity());
            MSCLog.i(TAG, "UpdateManager page applyUpdate, appId: ", runtime.getApp().getAppId());
        }

        context.onSuccess(null);
    }
}
