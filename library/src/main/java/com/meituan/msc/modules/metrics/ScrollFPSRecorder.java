package com.meituan.msc.modules.metrics;

import android.text.TextUtils;

import com.meituan.msc.common.utils.MPConcurrentHashMap;
import com.meituan.msc.jse.bridge.UiThreadUtil;
import com.meituan.msc.modules.reporter.MSCLog;

import org.json.JSONException;
import org.json.JSONObject;

import java.util.HashMap;
import java.util.Map;

public class ScrollFPSRecorder {
    protected static final String TAG = "ScrollFPSRecorder";
    public static final String FPS_SCROLL = "fps_scroll";
    public static final String UNKNOWN_ID = "none";

    private static final ScrollFPSRecorder INSTANCE = new ScrollFPSRecorder();

    public static ScrollFPSRecorder getInstance() {
        return INSTANCE;
    }

    private ScrollViewInfo currentScrollingInfo = null;
    private final Map<String, ScrollInfo> scrollInfoMap = new MPConcurrentHashMap<>();
    private final Map<String, ScrollInfo> lastScrollInfoMap = new MPConcurrentHashMap<>();
    private long lastTotalScrollTimeInNs;
    private long lastTotalFrameCount;
    private volatile boolean enableReport = false;

    private ScrollFPSRecorder() {

    }

    public void setEnableReport(boolean enableReport) {
        this.enableReport = enableReport;
    }

    public void onStartToRecordScrollFps(String pageName) {
        // 开始滚动
        MSCLog.d(TAG, "开始滚动, 页面：", pageName);
        if (!enableReport) {
            return;
        }
        UiThreadUtil.runOnUiThreadSafe(new Runnable() {
            @Override
            public void run() {
                onStartToRecordScrollFpsInner(pageName);
            }
        });
    }

    private void onStartToRecordScrollFpsInner(String pageName) {
        if (currentScrollingInfo != null) {
            currentScrollingInfo.startOffset = currentScrollingInfo.curOffset;
            currentScrollingInfo.startIndex = currentScrollingInfo.curIndex;
        }
    }

    public void onStopToRecordScrollFps(String pageName, long scrollTimeInNs, int frameCount) {
        if (!enableReport) {
            return;
        }
        UiThreadUtil.runOnUiThreadSafe(new Runnable() {
            @Override
            public void run() {
                onStopToRecordScrollFpsInner(pageName, scrollTimeInNs, frameCount);
            }
        });
    }

    private void onStopToRecordScrollFpsInner(String pageName, long scrollTimeInNs, int frameCount) {
        if (currentScrollingInfo == null) {
            return;
        }
        boolean isUp;
        if (currentScrollingInfo.curIndex == currentScrollingInfo.startIndex) {
            isUp = currentScrollingInfo.curOffset - currentScrollingInfo.startOffset >= 0;
        } else {
            isUp = currentScrollingInfo.curIndex - currentScrollingInfo.startIndex >= 0;
        }
        // 本次滚动结束
        ScrollInfo scrollInfo = scrollInfoMap.get(currentScrollingInfo.scrollingViewId);
        if (scrollInfo == null) {
            scrollInfo = new ScrollInfo();
            scrollInfoMap.put(currentScrollingInfo.scrollingViewId, scrollInfo);
        }
        if (isUp) {
            scrollInfo.upScrollTimeInNs += scrollTimeInNs;
            scrollInfo.upFrameCount += frameCount;
        } else {
            scrollInfo.downScrollTimeInNs += scrollTimeInNs;
            scrollInfo.downFrameCount += frameCount;
        }
        // 重置滚动的View信息
        if (!currentScrollingInfo.isDrag) {
            this.currentScrollingInfo = null;
        }
        MSCLog.d(TAG, "结束滑动, 页面：", pageName, "，本次帧数：", frameCount, ", 耗时时间: ", scrollTimeInNs, ", 帧率: ", (1e9 / scrollTimeInNs * frameCount));
    }

    public void onComputeAvgScrollFpsOfEntirePage(String pageName, long totalScrollTimeInNs, int totalFrameCount, double avgScrollFps) {
        if (!enableReport) {
            return;
        }
        // 页面滚动结束，开始计算滚动FPS的均值
        lastScrollInfoMap.clear();
        lastScrollInfoMap.putAll(scrollInfoMap);
        lastTotalFrameCount = totalFrameCount;
        lastTotalScrollTimeInNs = totalScrollTimeInNs;
        scrollInfoMap.clear();
        MSCLog.d(TAG, "整个页面的滚动FPS帧率, 页面：", pageName, "，平均帧率：", avgScrollFps);
    }

    // 拖拽开始
    public void onScrollingViewStartDrag(String viewType, String id) {
        if (!enableReport) {
            return;
        }
        id = getId(viewType, id);
        this.currentScrollingInfo = new ScrollViewInfo(id);
    }

    public void onScrollingViewEndDrag(String viewType, String id) {
        if (!enableReport) {
            return;
        }
        if (currentScrollingInfo != null) {
            currentScrollingInfo.isDrag = false;
        }
    }

    public void onScrollingView(String viewType, String id, int offset) {
        onScrollingView(viewType, id, 0, offset);
    }

    // 更新
    public void onScrollingView(String viewType, String id, int index, int offset) {
        if (!enableReport) {
            return;
        }
        id = getId(viewType, id);
        if (currentScrollingInfo != null && TextUtils.equals(currentScrollingInfo.scrollingViewId, id)) {
            currentScrollingInfo.curIndex = index;
            currentScrollingInfo.curOffset = offset;
        }
    }

    private String getId(String viewType, String id) {
        if (TextUtils.isEmpty(id)) {
            id = viewType;
        }
        if (TextUtils.isEmpty(id)) {
            id = UNKNOWN_ID;
        }
        return id;
    }

    public Map<String, Object> getScrollFPSDetailMap() {
        Map<String, Object> detailMap = new HashMap<>();
        if (!enableReport) {
            return detailMap;
        }
        for (Map.Entry<String, ScrollInfo> entry : lastScrollInfoMap.entrySet()) {
            ScrollInfo scrollInfo = entry.getValue();
            JSONObject data = new JSONObject();
            try {
                data.put("frameCount", scrollInfo.upFrameCount + scrollInfo.downFrameCount);
                data.put("scrollTime", (int) ((scrollInfo.upScrollTimeInNs + scrollInfo.downScrollTimeInNs) / 1e6));
                data.put("upFrameCount", scrollInfo.upFrameCount);
                data.put("upScrollTime", (int) (scrollInfo.upScrollTimeInNs / 1e6));
                data.put("downFrameCount", scrollInfo.downFrameCount);
                data.put("downScrollTime", (int) (scrollInfo.downScrollTimeInNs / 1e6));
//                data.put("weight", scrollInfo.scrollTimeInNs); // TODO 计算权重
            } catch (JSONException e) {
                throw new RuntimeException(e);
            }
            detailMap.put(entry.getKey(), data);
        }
        detailMap.put("totalFrameCount", lastTotalFrameCount);
        detailMap.put("totalScrollTime", (int) (lastTotalScrollTimeInNs / 1e6));
        return detailMap;
    }

    private static class ScrollInfo {
        long upScrollTimeInNs = 0;
        int upFrameCount = 0;
        long downScrollTimeInNs = 0;
        int downFrameCount = 0;
//        double scrollSpeed = 0;
    }

    private static class ScrollViewInfo {
        String scrollingViewId;
        boolean isDrag;
        int startIndex;
        int startOffset;
        int curIndex;
        int curOffset;

        public ScrollViewInfo(String scrollingViewId) {
            this.scrollingViewId = scrollingViewId;
            this.isDrag = true;
        }
    }


}
