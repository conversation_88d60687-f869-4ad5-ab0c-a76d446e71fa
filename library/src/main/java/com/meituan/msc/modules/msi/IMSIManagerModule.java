package com.meituan.msc.modules.msi;

import com.meituan.msi.ApiPortal;

/**
 * Created by letty on 2022/1/11.
 **/
public interface IMSIManagerModule {
    /**
     * 同步方法调用
     * @param params
     * @return
     */
    String syncInvoke(String params);

    /**
     * 异步方法调用
     * @param params
     * @return
     */
    String asyncInvoke(String params);

    /**
     * 传递消息到Fe
     * @param name 事件名
     * @param msg 事件信息
     *            msg参考com.meituan.msi.api.network.HeadersReceivedEvent实现，
     *            并添加@MsiSupport注解
     */
    void dispatchEvent(String name, Object msg);

    void updateDefaultValue();

    ApiPortal getApiPortal();
}
