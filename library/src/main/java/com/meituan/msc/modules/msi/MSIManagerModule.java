package com.meituan.msc.modules.msi;

import android.support.annotation.NonNull;
import android.text.TextUtils;

import com.meituan.msc.common.utils.CommonGson;
import com.meituan.msc.jse.bridge.JavaScriptModule;
import com.meituan.msc.modules.api.msi.MsiApisManager;
import com.meituan.msc.modules.manager.MSCMethod;
import com.meituan.msc.modules.manager.MSCNativeCompletableCallback;
import com.meituan.msc.modules.manager.ModuleName;
import com.meituan.msc.modules.page.IPageModule;
import com.meituan.msc.modules.page.render.RendererType;
import com.meituan.msc.modules.page.render.webview.WebViewBridge;
import com.meituan.msc.modules.page.render.webview.WebViewModule;
import com.meituan.msc.modules.reporter.MSCLog;
import com.meituan.msi.ApiPortal;
import com.meituan.msi.api.ApiCallback;
import com.meituan.msi.bean.BroadcastEvent;
import com.meituan.msi.bean.EventType;

import java.util.Map;
import java.util.concurrent.atomic.AtomicLong;

/**
 * MSC MSIManager 模块
 * https://km.sankuai.com/collabpage/1406600878
 * <p>
 * Created by letty on 2022/1/11.
 **/

@ModuleName(name = "MSIManager")
public class MSIManagerModule extends MsiApisManager implements IMSIManagerModule {

    /** 容器整个生命周期内同步api调用次数 */
    private final AtomicLong syncApiInvokeCounts = new AtomicLong(0);

    @MSCMethod(isSync = true)
    public String syncInvoke(String params) {
        syncApiInvokeCounts.incrementAndGet();
        return super.msiSyncInvoke(params);
    }

    @MSCMethod
    public String asyncInvoke(final String params) {
        return super.msiAsyncInvoke(params, new ApiCallback<String>() {
            @Override
            public void onSuccess(String result) {
                msiInvokeBack(result);
            }

            @Override
            public void onFail(String result) {
                msiInvokeBack(result);
            }
        });
    }

    /**
     * 新增 asyncInvokeWithPromise 方法用于支持 MSI 异步调用；
     * <p>
     * 原因：
     * 原 MSIManager.asyncInvoke 方法支持 MSI 异步调用，异步方法回调通过调用前端 Service 层模块 JSMSIBridge.invokeBack 实现；
     * WebView 场景无法将回调正确传递到正确的 WebView 环境中，使用回调形式进行
     * <p>
     * Notice：目前 MSI 事件将直接发送到 Service 层；Page 层无法支持相关事件注册！MSI 支持组件后会支持事件方向指定
     * <p>
     * https://km.sankuai.com/page/1289657718#id-%E6%A8%A1%E5%9D%97%E5%90%8D%EF%BC%9AMSIManager
     */
    @MSCMethod
    public String asyncInvokeWithPromise(String params, MSCNativeCompletableCallback callback) {
//        MSCLog.d("asyncInvokeWithPromise",params);
        return super.msiAsyncInvoke(params, new ApiCallback<String>() {
            @Override
            public void onSuccess(String params) {
                if (callback != null) {
                    callback.onComplete(params);
                }
            }

            @Override
            public void onFail(String params) {
                if (callback != null) {
                    callback.onComplete(params);
                }
            }
        });
    }

    private interface JSMSIBridge extends JavaScriptModule {
        void invokeBack(@NonNull String msg);
    }

    private void msiInvokeBack(String params) {
//        MSCLog.d("MSCMsi", "msiInvokeBack " + params);
        JSMSIBridge bridge = getRuntime().getJSModule(JSMSIBridge.class);
        if (bridge != null) {
            bridge.invokeBack(params);
        } else {
            // fixme Service 未准备好之前 忽略了msi发起的回调信息
            MSCLog.w("MSCMsi", "msiInvokeBack when service is not ready" + params);
        }
    }

    /**
     * msi 消息回传前端
     *
     * @param name
     * @param msg
     */
    @Override
    public void dispatch(EventType eventType, String name, String msg, BroadcastEvent source) {
        if (mMSIEventHandler != null && mMSIEventHandler.onInterceptEvent(name, msg)) {
            return;
        }
        if (handleViewEvent(eventType, msg, source)) {
            return;
        }
        msiInvokeBack(msg);
    }

    private boolean handleViewEvent(EventType eventType, String msg, BroadcastEvent source) {
        //视图层
        if (eventType == EventType.VIEW_EVENT && source != null) {
            Map<String, String> uiDataMap = source.getUiData();
            int pageId = -1;
            if (uiDataMap != null) {
                String strPageId = uiDataMap.get("pageId");
                if (!TextUtils.isEmpty(strPageId)) {
                    try {
                        pageId = Integer.parseInt(strPageId);
                    } catch (NumberFormatException e) {

                    }
                }
            }
            // 临时先这么写 只有webview需要将事件分发过去
            if (pageId >= 0) {
                IPageModule pageModule = getPageById(pageId);
                if (pageModule != null) {
                    if (pageModule.getRendererType() == RendererType.NATIVE) {
                        pageModule.getRenderer().handleViewEvent(eventType, msg, source);
                    } else {
                        WebViewModule webViewModule = pageModule.getSubModule(WebViewModule.class);
                        if (webViewModule != null) {
                            WebViewBridge webViewBridge = webViewModule.getWebViewBridge();
                            if (webViewBridge != null) {
                                webViewBridge.getJSModule(MSIViewModule.class).invokeBack(msg);
                                return true;
                            }
                        }
                    }
                }
            }
            MSCLog.w("MSIManager unhandledViewEvent", pageId, msg);
        }
        return false;
    }

    public interface MSIViewModule extends JavaScriptModule {
        void invokeBack(@NonNull String msg);
    }

    /**
     * 传递消息到Fe
     *
     * @param name 事件名
     * @param msg  事件信息
     *             msg参考com.meituan.msi.api.network.HeadersReceivedEvent实现，
     *             并添加@MsiSupport注解
     */
    public void dispatchEvent(String name, Object msg) {
        apiPortal.dispatchEvent(name, msg);
    }

    /**
     * 传递消息到Fe视图层（后续MSI提供API直接发送视图层事件）
     * @param name 方法名
     * @param msg 参数
     */
    public void dispatchEventToWebView(String name, Object msg, String pageId) {
        BroadcastEvent source = new BroadcastEvent(name, msg);
        source.addUiData("pageId", pageId);
        this.dispatch(EventType.VIEW_EVENT, name, CommonGson.GSON.toJson(source), source);
    }

    @Override
    public void onDestroy() {
        syncApiInvokeCounts.set(0);
        onServiceDestroy();
    }

    /**
     * 获取同步api调用次数
     *
     * @return 调用次数
     */
    public long getSyncApiInvokeCounts() {
        return syncApiInvokeCounts.get();
    }

    /**
     * update when config updated
     */
    public void updateDefaultValue() {
        apiPortal.updateDefaultValue(getRequestDefaultConfig());
    }

    @Override
    public ApiPortal getApiPortal() {
        return apiPortal;
    }
}
