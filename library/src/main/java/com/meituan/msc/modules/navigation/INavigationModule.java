package com.meituan.msc.modules.navigation;

import android.support.annotation.NonNull;

import com.meituan.msc.modules.api.ApiException;
import com.meituan.msc.modules.api.msi.navigation.BizNavigationExtraParams;
import com.meituan.msc.modules.page.IPageManagerModule;
import com.meituan.msc.modules.page.IPageNavigationModule;

/**
 * Created by letty on 2022/1/17.
 **/
public interface INavigationModule extends IPageNavigationModule {
    void launchApp(String url, Integer openSeq, long routeTime, @NonNull BizNavigationExtraParams bizNavigationExtraParams) throws ApiException;
}
