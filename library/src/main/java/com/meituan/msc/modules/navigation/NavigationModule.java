package com.meituan.msc.modules.navigation;

import static com.meituan.msc.lib.interfaces.container.MSCParams.TARGET_PATH;

import android.os.Bundle;
import android.support.annotation.NonNull;

import com.meituan.msc.lib.interfaces.container.MSCParams;
import com.meituan.msc.modules.api.ApiException;
import com.meituan.msc.modules.api.msi.MSCErrorCode;
import com.meituan.msc.modules.api.msi.navigation.BizNavigationExtraParams;
import com.meituan.msc.modules.container.IContainerDelegate;
import com.meituan.msc.modules.container.IContainerManager;
import com.meituan.msc.modules.container.IStartActivityModule;
import com.meituan.msc.modules.manager.MSCModule;
import com.meituan.msc.modules.manager.ModuleName;
import com.meituan.msc.modules.page.IPageManagerModule;
import com.meituan.msc.modules.update.IPageLoadModule;

/**
 * 导航模块，处理导航类事件
 * 协调Service、Page层导航事件触发
 * Created by letty on 2021/12/30.
 **/
@ModuleName(name = "navigation")
public class NavigationModule extends MSCModule implements INavigationModule {
    private IPageManagerModule getTopManagerModule() throws ApiException {
        IPageManagerModule pageManagerModule = getModule(IContainerManager.class).getTopPageManager();
        if (pageManagerModule == null) {
            throw new ApiException(MSCErrorCode.ERROR_CODE_API_COMMON_MEET_EXPECTATIONS, "operation not available for not pageStack");
        }
        return pageManagerModule;
    }

    @Override
    public void launchApp(String url, Integer openSeq, long routeTime, @NonNull BizNavigationExtraParams bizNavigationExtraParams) throws ApiException {
        Bundle extras = new Bundle();
        extras.putString(TARGET_PATH, url);
        if (openSeq != null) {
            extras.putInt(MSCParams.OPEN_SEQ, openSeq);
            extras.putLong(MSCParams.ROUTE_TIME, routeTime);
            extras.putBoolean(MSCParams.DISABLE_PREFETCH, bizNavigationExtraParams.disablePrefetch);
        }
        IContainerDelegate containerDelegate = getModule(IContainerManager.class).getTopContainer();
        if (containerDelegate != null) {
            getModule(IStartActivityModule.class).startActivity(containerDelegate.getMSCContainer().
                    getStartContainerActivityIntent(getRuntime().getAppId(), extras));
        }
    }

    @Override
    public void navigateTo(String url, Integer openSeq, long routeTime, @NonNull BizNavigationExtraParams bizNavigationExtraParams) throws ApiException {
        getTopManagerModule().navigateTo(url, openSeq, routeTime, bizNavigationExtraParams);
    }

    @Override
    public void launch(String url, long routeTime) throws ApiException {
        getTopManagerModule().launch(url, routeTime);
    }

    @Override
    public void reLaunch(String url, long routeTime, @NonNull BizNavigationExtraParams bizNavigationExtraParams) throws ApiException {
        getTopManagerModule().reLaunch(url, routeTime, bizNavigationExtraParams);
    }

    @Override
    public void switchTab(String url, long routeTime, @NonNull BizNavigationExtraParams bizNavigationExtraParams) throws ApiException {
        getTopManagerModule().switchTab(url, routeTime, bizNavigationExtraParams);
    }

    @Override
    public void redirectTo(String url, long routeTime, @NonNull BizNavigationExtraParams bizNavigationExtraParams) throws ApiException {
        getTopManagerModule().redirectTo(url, routeTime,bizNavigationExtraParams);
    }

    @Override
    public void navigateBack(int delta, boolean __mtAllowCloseContainer, long routeTime) throws ApiException {
        getTopManagerModule().navigateBack(delta, __mtAllowCloseContainer, routeTime);
    }

//
//    /**
//     * 通知Service层进行路由操作
//     * 启动过程中在Page层View创建完成时首次调用，将触发小程序App.onLoad
//     *
//     * @param cache 渲染缓存内容，大字符串，使用特殊的快速拼接方式，因此需要单独传递
//     */
//    @MainThread
//    public void onAppRoute(OpenParams openParams, int viewId, int reloadViewId, String cache) {
//
//        String openType = openParams.openType;
//        String path = openParams.url;
//
//        Map<String, Object> params = new HashMap<>();
//        if (mSrcAppID != null) {
//            params = getReferrerInfoParams();
//            params.put(SCENE, getScene());
//            mSrcAppID = null;
//            mSrcExtraData = null;
//        }
//        if (openParams.extraParams != null) {
//            params.putAll(openParams.extraParams);
//        }
////        if (mScene == SceneNumber.OPEN_FROM_WIDGET) {
////            openType = OpenParams.WIDGET_OPEN_APP;
////        }
//        /*
//         * 栈底页面 发送携带lastRemovedPageId的NAVIGATE_BACK事件，用于pop当前页面及以上的，触发当前页面的onUnload，不触发下一个页面的onShow；
//         * 与一般的NAVIGATE_BACK事件事件不同的是，一般的NAVIGATE_BACK事件指返回到当前页面，触发当前页面onShow，被隐藏页面的onUnload；
//         */
//        if (OpenParams.NAVIGATE_BACK_UTIL.equals(openType)) {
//            params.put("lastRemovedPageId", viewId);
//            openType = OpenParams.NAVIGATE_BACK;
//        } else if (path != null && !mAppConfig.hasPage(path)) {
//            params.put("pageNotFound", true);
//        }
//        params.put("openType", openType);
//        params.put("path", path);
//        if (openParams.openSeq != null) {
//            params.put("openSeq", openParams.openSeq);
//        }
//
//        curPageEngineType = "webview";
//        if (mPageManager.getTopPage() != null && mPageManager.getTopPage().getRendererType() == RendererFactory.RendererType.Fluent) {
//            curPageEngineType = "fluent";
//        }
//        params.put("engineType", curPageEngineType);
//        params.put("pageFrameId", "page_" + viewId);
//
//        // 如果是Reload模式，viewId需要传旧的reloadViewId, 并增加一个newPageId参数传递新的viewId
//        if (OpenType.RELOAD.equals(openType) && reloadViewId != View.NO_ID) {
//            params.put("newPageId", viewId);
//            viewId = reloadViewId;
//        }
//
//        if (isWidget()) {
//            MSCWidgetFragment containerFragment = (MSCWidgetFragment) mContainer;
//            params.put("widgetProperties", containerFragment.getWidgetInitialData());
//            if (containerFragment.getWidgetEventListener() != null) {
//                params.put("registerWidgetEvents", containerFragment.getRegisteredWidgetEvents());
//            }
//        }
//
//        JSONObject jsonObject = JsonUtil.parseToJson(params);
//        String paramsString = jsonObject.toString();
//
//        MSCLog.d(TAG, "onAppRoute " + openType + ", to " + paramsString
//                + " with render cache " + FileSizeUtil.formatStringSize(cache));
//
//        // 执行额外事件 添加initialRenderingData数据 向逻辑层发送缓存数据
//        if (!TextUtils.isEmpty(cache)) {
//            try {
//                paramsString = new JsonUtil.FastBuilder(paramsString)
//                        .put("initialRenderingData", cache, false)
//                        .build();
//            } catch (JSONException e) {
//                MSCLog.e(e);
//            }
//        }
//
//        if (!firstRender) {
//            mAppService.injectGlobalField(OpenType.RE_LAUNCH.equals(openType) ? "__appReLaunchStartTime" : "__appLaunchStartTime", String.valueOf
//                    (launchStartTimeCurrentTimeMillis));
//        }
//
//        curPagePath = path;
//        sendOrCacheOnAppRoute(paramsString, viewId);
//        mDevToolsDelegate.onAppRoute(jsonObject);
//
//        // 上报PV时，如果是PageNotFound，报REPORT_PAGE_COUNT_PAGE_NOT_FOUND
//        mReporter.report(params.get("pageNotFound") != null ? ReporterFields.REPORT_PAGE_COUNT_PAGE_NOT_FOUND : ReporterFields
//                        .REPORT_PAGE_COUNT_PAGE_VIEW,
//                HashMapHelper.of("page.path", path));
//        if (!MSCEnvHelper.getEnvInfo().isProdEnv()) {
//            ReportUtils.reportRealTimePageInfo(mReporter, getIntent(), mAppId, getName(), path);
//        }
//    }

}
