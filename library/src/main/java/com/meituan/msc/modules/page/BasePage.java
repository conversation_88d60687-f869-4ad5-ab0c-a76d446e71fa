package com.meituan.msc.modules.page;

import android.content.Context;
import android.widget.RelativeLayout;

import com.meituan.msc.common.framework.interfaces.PageEventListener;
import com.meituan.msc.modules.container.IContainerDelegate;
import com.meituan.msc.modules.engine.MSCRuntime;
import com.meituan.msc.modules.page.reload.PageInfoArray;
import com.meituan.msc.modules.page.reload.PageInfoOne;

/**
 * Created by letty on 2022/3/4.
 **/
public abstract class BasePage extends RelativeLayout {
    final boolean mIsFirstPage;
    final boolean mIsFirstPageV2;
    final MSCRuntime mRuntime;
    final Context mContext; // activity context
    final IContainerDelegate mController;
    final PageEventListener mEventListener;
    final boolean mIsWidget;
    /**
     * 页面路由时间
     */
    protected long routeTime;
    // 是否是半弹层透明Page。
    protected boolean mIsHalfScreenPage;

    public BasePage(MSCRuntime runtime,
                    IContainerDelegate controller,
                    PageEventListener eventListener,
                    boolean isFirstPage,
                    boolean mIsFirstPageV2) {
        super(controller.getActivity());
        this.mIsFirstPageV2 = mIsFirstPageV2;
        this.mContext = getContext();
        this.mRuntime = runtime;
        this.mController = controller;
        this.mEventListener = eventListener;
        this.mIsFirstPage = isFirstPage;
        this.mIsWidget = controller.isWidget();
    }

    public boolean isHalfScreenPage() {
        return mIsHalfScreenPage;
    }

    abstract void onDestroy();

    abstract void onShow(String openType);

    abstract void onHide(int cause);

    abstract String getRoutePath();

    abstract int getViewId();

    public abstract IPageModule getPageModuleById(int id);

    public PageInfoArray getSavedPageInfo(){
        PageInfoArray pageInfo = new PageInfoArray();
        pageInfo.currentId = getViewId();
        pageInfo.currentPath = getRoutePath();
        pageInfo.pageInfos = getPageInfos();
        return pageInfo;
    }

    protected PageInfoOne[] getPageInfos() {
        return new PageInfoOne[]{getPageInfoOne()};
    }

    protected PageInfoOne getPageInfoOne() {
        PageInfoOne pageInfo = new PageInfoOne();
        pageInfo.viewId = getViewId();
        pageInfo.path = getRoutePath();
        return pageInfo;
    }

    // fixme
    abstract Page getPage();

    public void setRouteTime(long routeTime) {
        this.routeTime = routeTime;
    }
}