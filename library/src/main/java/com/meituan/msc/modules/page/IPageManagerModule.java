package com.meituan.msc.modules.page;

import android.support.annotation.NonNull;
import android.view.ViewGroup;

import com.meituan.msc.modules.api.ApiException;
import com.meituan.msc.modules.api.msi.navigation.BizNavigationExtraParams;
import com.meituan.msc.modules.navigation.IPageNotFound;
import com.meituan.msc.modules.page.reload.PageInfoArray;

import java.util.List;

/**
 * Created by letty on 2021/12/30.
 **/
public interface IPageManagerModule extends IPageNavigationModule, IPageNotFound {

    boolean hasPage(int id);

    IPageModule getPage(int id);

    IPageModule getTopPage();

    IPageModule getBottomPage();

    int getPageCount();

    ViewGroup asView();

    void onResume(String openType);

    void onPause(int cause);

    //fixme delete
    @Deprecated
    PageManager getPageManager();

    int getTopPageId();

    void reload(PageInfoArray pageInfoArray, long routeTime, @NonNull BizNavigationExtraParams bizNavigationExtraParams);

    List<Integer> getAllPageId();

    boolean isNeedRemoveTopPage();

    Boolean getIsFullScreenOfLastPage();

    void setIsFullScreenOfLastPage(boolean isTransparent);
}
