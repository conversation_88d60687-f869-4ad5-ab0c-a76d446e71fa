package com.meituan.msc.modules.page;

import com.meituan.msc.modules.manager.MSCMethod;

/**
 * Page 中提供给前端框架调用的方法
 * Created by letty on 2022/1/17.
 **/
public interface IPageMethods {

    @MSCMethod
    void disableScrollBounce(boolean disable);

    /**
     * 开始下拉刷新
     */
    @MSCMethod
    void startPullDownRefresh();

    /**
     * 停止下拉刷新
     */
    @MSCMethod
    void stopPullDownRefresh();

    @MSCMethod
    void setBackgroundColor(int parseColor);

    @MSCMethod
    void setBackgroundTextStyle(boolean isDarkMode);
}


