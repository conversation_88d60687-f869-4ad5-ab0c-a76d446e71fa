package com.meituan.msc.modules.page;

import android.support.annotation.Size;
import android.view.View;

import com.meituan.msc.modules.manager.IMSCModule;
import com.meituan.msc.modules.page.custom.PullLoadingIconConfig;
import com.meituan.msc.modules.page.render.AppPageReporter;
import com.meituan.msc.modules.page.render.BaseRenderer;
import com.meituan.msc.modules.page.render.RendererType;
import com.meituan.msc.modules.page.transition.PageTransitionConfig;
import com.meituan.msi.bean.MsiContext;
import com.meituan.msi.page.IKeyBoardHeightChangeObserver;

import java.util.Map;

import javax.annotation.Nullable;

/**
 * 每个 PageId 对应一个PageModule 对应一个 Renderer
 * 每个 Page 对应一个或多个 PageModule, Tab 情况下会有多个 PageModule
 * Created by letty on 2022/1/14.
 **/
public interface IPageModule extends IPageMethods, IMSCModule, IToast {
    String PAGE_LIFECYCLE_RESUME = "pageResume";
    String PAGE_LIFECYCLE_PAUSE = "pagePause";

    View asView();

    int getId();

    RendererType getRendererType();

    String getPagePath();

    boolean isWidget();

    /**
     * 每个在Tab中的页面可以作为单独页面展示吗？应该不可以吧
     *
     * @return
     */
    boolean isTabPage();

    @Nullable
    ITabPage getTabPage();

    /**
     * 是否拦截页面退出
     *
     * @return
     */
    boolean isEnableBackActionIntercept();

    /**
     * 设置页面退出拦截
     * https://km.sankuai.com/page/604155807
     *
     * @param isEnable true 拦截 false 不拦截
     */
    void setEnableBackActionIntercept(boolean isEnable);

    @Nullable
    int[] getWindowSize();

    IPageNavigationBarMethods getPageNavigationBarMethods();

    AppPageReporter getReporter();

    PageTransitionConfig getPageTransitionConfig();

    PullLoadingIconConfig getPullLoadingIconConfig();

    void setPageTransitionConfig(PageTransitionConfig pageTransitionConfig);

    BaseRenderer getRenderer();

    IMSCViewGroup getIViewGroupImpl();

    void startScroll(int targetY, int duration, MsiContext callback);

    void adjustPosition(int offset, int keyboardHeight, boolean scroll);

    void adjustPosition(View view, String adjustKeyboardTo, int cursorSpacing, int bottomInsetHeight, int delayDur);

    int getKeyboardHeight();

    void getLocationInWindow(@Size(2) int[] outLocation);

    int getContentHeight();

    void registerKeyboardChange(IKeyBoardHeightChangeObserver listener);

    void unregisterKeyboardChange(IKeyBoardHeightChangeObserver listener);

    IWebViewComponentInfo getWebViewComponent();

    boolean isPageShow();

    int getNavigationBarHeight();

    int getWebScrollY();

    void adjustPan(int changedHeight);

    void scrollYEx(int changedHeight);

    int getPan();

    int getHeight();

    Map<String, String> getBizTagsForPage();

    boolean isDestroyed();
}
