package com.meituan.msc.modules.page;

import android.graphics.Rect;

/***
 * Created by letty on 2022/1/17.
 **/
public interface IPageNavigationBarMethods {

    void setNavigationBarTitle(String title);

    /**
     * 设置导航栏颜色
     *
     * @param frontColor      前景颜色值，包括按钮、标题、状态栏的颜色
     * @param backgroundColor 背景颜色值
     */
    void setNavigationBarColor(int frontColor, int backgroundColor);

    /**
     * 显示更多按钮
     * https://km.sankuai.com/page/288014184
     */
    void showShareMenu(boolean callFromBusiness);

    /**
     * 隐藏更多按钮
     * https://km.sankuai.com/page/288014184
     * @param callFromBusiness 是否为业务调用
     */
    void hideShareMenu(boolean callFromBusiness);

    boolean isMenuButtonShown();

    void showNavigationBarLoading();

    void hideNavigationBarLoading();

    Rect getMenuRect();
}
