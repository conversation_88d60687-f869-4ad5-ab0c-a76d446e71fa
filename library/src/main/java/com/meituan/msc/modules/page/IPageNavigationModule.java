package com.meituan.msc.modules.page;

import android.support.annotation.NonNull;

import com.meituan.msc.modules.api.ApiException;
import com.meituan.msc.modules.api.msi.navigation.BizNavigationExtraParams;

/**
 * 导航模块
 * Created by letty on 2021/12/30.
 **/
public interface IPageNavigationModule {

    /**
     * 保留当前页面，跳转到应用内的某个页面。但是不能跳到tabbar页面。小程序中页面栈最多十层。
     *
     * @param url
     * @param routeTime
     * @throws ApiException
     */
    void navigateTo(String url, Integer openSeq, long routeTime, @NonNull BizNavigationExtraParams bizNavigationExtraParams) throws ApiException;

    void launch(String url, long routeTime) throws ApiException;

    /**
     * 跳转到 tabBar 页面，并关闭其他所有非 tabBar 页面
     *
     * @param url
     * @param routeTime
     * @throws ApiException
     */
    void switchTab(String url, long routeTime, @NonNull BizNavigationExtraParams bizNavigationExtraParams) throws ApiException;

    /**
     * 关闭所有页面，打开到应用内的某个页面
     *
     * @param url
     * @param routeTime
     * @throws ApiException
     */
    void reLaunch(String url, long routeTime, @NonNull BizNavigationExtraParams bizNavigationExtraParams) throws ApiException;

    /**
     * 关闭当前页面，跳转到应用内的某个页面。但是不允许跳转到 tabbar 页面。
     *
     * @param url
     * @param routeTime
     * @throws ApiException
     */
    void redirectTo(String url, long routeTime, @NonNull BizNavigationExtraParams bizNavigationExtraParams) throws ApiException;

    /**
     * 关闭当前页面，返回上一页面或多级页面。
     *
     * @param delta
     * @param routeTime
     * @throws ApiException
     */
    void navigateBack(int delta, boolean __mtAllowCloseContainer, long routeTime) throws ApiException;
}
