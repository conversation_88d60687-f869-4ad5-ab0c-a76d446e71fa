package com.meituan.msc.modules.page;

import com.meituan.msc.modules.container.OpenParams;
import com.meituan.msc.modules.page.view.tab.TabBar;

/**
 * Created by letty on 2022/9/21.
 **/
public interface ITabPage {

    boolean isCustomTabPage();

    void switchTab(OpenParams openParams);

    void switchTabByClick(OpenParams openParams);

    void hideTabBar();

    TabBar getTabBar();

    String getCurrentPagePath();
}
