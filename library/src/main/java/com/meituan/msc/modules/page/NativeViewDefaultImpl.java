package com.meituan.msc.modules.page;

import android.text.TextUtils;
import android.view.View;

import com.google.gson.JsonObject;
import com.meituan.msc.common.utils.UIUtil;
import com.meituan.msc.modules.api.msi.components.coverview.CoverUpdateObserver;
import com.meituan.msc.modules.api.msi.components.coverview.ICoverViewUpdateRegistry;
import com.meituan.msc.modules.api.msi.webview.WebViewComponentWrapper;
import com.meituan.msc.modules.engine.MSCHornRollbackConfig;
import com.meituan.msc.modules.page.view.CoverViewWrapper;
import com.meituan.msc.modules.page.view.ViewFinder;
import com.meituan.msc.modules.page.view.coverview.CoverViewRootContainer;
import com.meituan.msc.modules.page.view.coverview.InfoWindowRootContainer;
import com.meituan.msc.modules.page.widget.MultiLayerPage;
import com.meituan.msc.modules.reporter.MSCLog;
import com.meituan.msi.page.IViewListener;

import java.lang.ref.WeakReference;
import java.util.ArrayList;
import java.util.List;
import java.util.concurrent.ConcurrentHashMap;

/**
 * MSI 组件会在 MSC 中统一通过CoverViewWrapper 封装
 * 用于处理样式变化、父子关系层级维护等
 * <p>
 * Created by letty on 2022/9/21.
 **/
public class NativeViewDefaultImpl implements IMSCViewGroup {
    final Page mPage;
    final MultiLayerPage mMultiLayerPage;

    NativeViewDefaultImpl(Page page, MultiLayerPage multiLayerPage) {
        this.mPage = page;
        this.mMultiLayerPage = multiLayerPage;
    }

    @Override
    public void insertView(View view, JsonObject jsonObject) {
        int viewId = view.getId();
        view.setId(View.NO_ID);
        attachApiViewToPage(view, jsonObject);
        //  为何create的时候在需要容器侧setId呢？// 看起来好像是把 id set 给 CoverViewWrapper
        if (view.getParent() instanceof View) {
            ((View) view.getParent()).setId(viewId);
        }
    }

    @Override
    public void updateView(int i, JsonObject jsonObject) {
        findAndUpdateApiViewUI(i, jsonObject);
    }

    @Override
    public void removeView(View view) {
        if (view == null) {
            return;
        }

        View viewParent = (View) view.getParent();
        if (viewParent instanceof CoverViewWrapper) {
            UIUtil.removeFormParentIfNeed(viewParent);
        } else {
            UIUtil.removeFormParentIfNeed(view);
        }
    }

    @Override
    public View findView(int i, int i1) {
        return mPage.findViewByViewId(i, i1);
    }

    @Override
    public View addLayerUpdateListener(String viewId, IViewListener listener) {
        // native
        View markerView = mPage.findViewByMarkerKey(viewId);
        if (markerView != null) {
            if (MSCHornRollbackConfig.enableMsiMapListener() && markerView instanceof IMsiViewDispatcher) {
                ((IMsiViewDispatcher) markerView).setViewListener(listener);
            }
            return markerView;
        }

        // webview
        InfoWindowRootContainer markerInfoWindowRootContainer =
                (InfoWindowRootContainer) mMultiLayerPage.getOrCreateMarkerInfoWindowRootContainer(viewId);
        markerInfoWindowRootContainer.setUpdateUpdateObserver(new CoverUpdateObserver() {
            @Override
            public void onChange() {
                if (listener != null) {
                    listener.onViewChanged(markerInfoWindowRootContainer);
                }
            }
        });
        return markerInfoWindowRootContainer;
    }

    private boolean findAndUpdateApiViewUI(int viewId, JsonObject data) {
        if (!data.has("position")) {//没有坐标的时候返回false
            return false;
        }

        synchronized (mMultiLayerPage) {
            CoverViewWrapper viewWrapper = ViewFinder.findCoverViewWrapper(mMultiLayerPage.getCoverViewContainer(), viewId);
            if (viewWrapper != null) {
                mMultiLayerPage.getCoverViewContainer().updateApiViewUI(viewWrapper, data);
                return true;
            } else if ((viewWrapper = ViewFinder.findCoverViewWrapper(mMultiLayerPage.getUnderCoverViewContainer(), viewId)) != null) {
                mMultiLayerPage.getUnderCoverViewContainer().updateApiViewUI(viewWrapper, data);
                return true;
            }
            //更新视图时，对自定义气泡的容器进行查找通知更新
            viewWrapper = mMultiLayerPage.findCoverViewWrapperInMarkerInfoWindowRootContainer(viewId, -1);
            if (viewWrapper != null) {
                CoverViewRootContainer coverViewRootContainer = mMultiLayerPage.getInfoWindowRootContainerByViewId(viewId);
                if (coverViewRootContainer != null) {
                    coverViewRootContainer.updateApiViewUI(viewWrapper, data);
                    return true;
                }
            }
        }
        return false;
    }

    public boolean attachApiViewToPage(View view, JsonObject data) {
//        MSCLog.d("NativeViewDefaultImpl", "attachApiViewToPage", data);
        MultiLayerPage multiLayerPage = mMultiLayerPage;
        if (multiLayerPage == null || multiLayerPage.getCoverViewContainer() == null || multiLayerPage.getUnderCoverViewContainer() == null) {
            return false;
        }
        if (view instanceof WebViewComponentWrapper) {
            mPage.mCurrentViewWrapper.webViewModuleRef = new WeakReference<>(view);
        }
        //这个状态现在移除不掉噢
        if (data.has("mtSinkMode") && data.get("mtSinkMode").getAsBoolean()) {
            mPage.sinkMode = true;
            multiLayerPage.enableSinkMode(true);
            mPage.setSinkModeBackgroundColor();
            return multiLayerPage.getUnderCoverViewContainer().insertApiViewToContainerAuto(view, data);
        }

        String parentId = data.has("parentId") ? data.get("parentId").getAsString() : null;
        String markerIdKey = multiLayerPage.getMarkIdInMarkerCoverViews(parentId);
        //当前视图或父布局有markerId参数，进行拦截
        if (data.has("markerId") || markerIdKey != null) {
            return interceptInsertApiToContainer(data, markerIdKey, multiLayerPage, view);
        }

        return multiLayerPage.getCoverViewContainer().insertApiViewToContainerAuto(view, data);
    }


    /**
     * 拦截插入的marker coverView，存储到InfoWindow视图容器中
     *
     * @param data
     * @param refreshLayout
     * @param view
     * @return
     */
    private boolean interceptInsertApiToContainer(JsonObject data, String markerIdKey, MultiLayerPage refreshLayout, View view) {
//        MSCLog.d("NativeViewDefaultImpl", "interceptInsertApiToContainer", markerIdKey, data);

        String markerId = "";
        String viewId = data.get("viewId").getAsString();
        if (data.has("markerId")) {
            markerId = data.get("markerId").getAsString();
            //带有markerId的View，是最终的父布局，不在原有coverView中插入，故移除parentId
            data.remove("parentId");
        }
        if (markerIdKey == null && data.has("mapId")) {
            //通过mapId + markerId区分不同地图的CoverViewContainer
            markerIdKey = data.get("mapId").getAsString() + "_" + markerId;
        }

        ConcurrentHashMap<String, List<String>> markerViewIdsMap = refreshLayout.getMarkerViewIdsMap();
        //Cover-View容器视图组添加每一组markerId的视图组
        if (markerIdKey != null) {
            List<String> list = markerViewIdsMap.get(markerIdKey);
            if (list != null) {
                list.add(viewId);
                markerViewIdsMap.put(markerIdKey, list);
            } else {
                if (!TextUtils.isEmpty(markerId)) {
                    list = new ArrayList<>();
                    list.add(viewId);
                    markerViewIdsMap.put(markerIdKey, list);
                }
            }
        }
        if (markerIdKey == null) {
            return false;
        }
        if (view instanceof ICoverViewUpdateRegistry) {
            ((ICoverViewUpdateRegistry) view).setIsCustomCallOutView(true);
        }

        return refreshLayout.getOrCreateMarkerInfoWindowRootContainer(markerIdKey).insertApiViewToContainerAuto(view, data);
    }

}
