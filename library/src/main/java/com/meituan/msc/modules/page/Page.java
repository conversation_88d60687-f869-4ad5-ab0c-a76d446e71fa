package com.meituan.msc.modules.page;

import android.animation.LayoutTransition;
import android.annotation.SuppressLint;
import android.app.Activity;
import android.content.Context;
import android.content.res.Configuration;
import android.graphics.Color;
import android.graphics.drawable.ColorDrawable;
import android.net.Uri;
import android.os.Handler;
import android.os.Looper;
import android.os.SystemClock;
import android.support.annotation.Nullable;
import android.text.TextUtils;
import android.view.MotionEvent;
import android.view.View;
import android.view.ViewConfiguration;
import android.view.ViewGroup;
import android.view.ViewParent;
import android.view.inputmethod.InputMethodManager;
import android.widget.FrameLayout;

import com.meituan.android.degrade.interfaces.resource.ResourceManager;
import com.meituan.msc.common.aov_task.TaskManager;
import com.meituan.msc.common.aov_task.task.ITask;
import com.meituan.msc.common.config.MSCConfig;
import com.meituan.msc.common.executor.MSCExecutors;
import com.meituan.msc.common.framework.MPListenerManager;
import com.meituan.msc.common.framework.interfaces.PageEventListener;
import com.meituan.msc.common.perf.PerfEventConstant;
import com.meituan.msc.common.utils.ColorUtil;
import com.meituan.msc.common.utils.DisplayUtil;
import com.meituan.msc.common.utils.HashMapHelper;
import com.meituan.msc.common.utils.InputMethodUtil;
import com.meituan.msc.common.utils.JsonUtil;
import com.meituan.msc.common.utils.MSCTraceUtil;
import com.meituan.msc.common.utils.PathUtil;
import com.meituan.msc.common.utils.ScreenUtil;
import com.meituan.msc.common.utils.SystemInfoUtils;
import com.meituan.msc.common.utils.ToastUtils;
import com.meituan.msc.common.utils.WhiteScreenUtil;
import com.meituan.msc.extern.MSCEnvHelper;
import com.meituan.msc.modules.api.ApiException;
import com.meituan.msc.modules.api.input.KeyboardHeightObserver;
import com.meituan.msc.modules.api.legacy.appstate.WidgetListener;
import com.meituan.msc.modules.api.msi.api.PullDownRefreshApi;
import com.meituan.msc.modules.api.msi.api.PullDownRefreshParam;
import com.meituan.msc.modules.api.msi.navigation.BizNavigationExtraParams;
import com.meituan.msc.modules.api.msi.webview.IWebFocusDispatcher;
import com.meituan.msc.modules.api.msi.webview.WebViewComponentWrapper;
import com.meituan.msc.modules.api.report.MSCReportBizTagsManager;
import com.meituan.msc.modules.api.web.WebViewConst;
import com.meituan.msc.modules.api.widget.WidgetPreCreateListener;
import com.meituan.msc.modules.apploader.IAppLoader;
import com.meituan.msc.modules.apploader.InstrumentLaunchManager;
import com.meituan.msc.modules.apploader.launchtasks.InjectBuzPkgTask;
import com.meituan.msc.modules.container.ContainerController;
import com.meituan.msc.modules.container.ContainerReporter;
import com.meituan.msc.modules.container.HalfPageUtils;
import com.meituan.msc.modules.container.IContainerDelegate;
import com.meituan.msc.modules.container.IMSCContainer;
import com.meituan.msc.modules.container.OpenParams;
import com.meituan.msc.modules.devtools.IPerformanceManager;
import com.meituan.msc.modules.engine.IRendererManager;
import com.meituan.msc.modules.engine.MSCHornRollbackConfig;
import com.meituan.msc.modules.engine.MSCRuntime;
import com.meituan.msc.modules.engine.ReportUtils;
import com.meituan.msc.modules.engine.RuntimeSource;
import com.meituan.msc.modules.engine.dataprefetch.IDataPrefetchModule;
import com.meituan.msc.modules.manager.MSCEvent;
import com.meituan.msc.modules.page.custom.PullLoadingIconConfig;
import com.meituan.msc.modules.page.render.AppPageReporter;
import com.meituan.msc.modules.page.render.BaseRenderer;
import com.meituan.msc.modules.page.render.IContentScroller;
import com.meituan.msc.modules.page.render.MSCHornPerfConfig;
import com.meituan.msc.modules.page.render.webview.MSCWebViewRenderer;
import com.meituan.msc.modules.page.transition.ITransitionPage;
import com.meituan.msc.modules.page.transition.PageLayoutTransitionHelper;
import com.meituan.msc.modules.page.transition.PageTransitionConfig;
import com.meituan.msc.modules.page.transition.PageTransitionContainer;
import com.meituan.msc.modules.page.transition.SimpleTransitionListener;
import com.meituan.msc.modules.page.view.CoverViewWrapper;
import com.meituan.msc.modules.page.view.CustomNavigationBar;
import com.meituan.msc.modules.page.view.PageViewWrapper;
import com.meituan.msc.modules.page.view.ViewFinder;
import com.meituan.msc.modules.page.view.coverview.CoverViewRootContainer;
import com.meituan.msc.modules.page.view.coverview.IPageLifecycleInterceptor;
import com.meituan.msc.modules.page.widget.MultiLayerPage;
import com.meituan.msc.modules.page.widget.SwipeRefreshLayout;
import com.meituan.msc.modules.preload.MSCHornPreloadConfig;
import com.meituan.msc.modules.reporter.CommonTags;
import com.meituan.msc.modules.reporter.CrashReporterHelper;
import com.meituan.msc.modules.reporter.MSCLog;
import com.meituan.msc.modules.reporter.MSCStartTimeReporter;
import com.meituan.msc.modules.reporter.prexception.AppServiceState;
import com.meituan.msc.modules.reporter.prexception.PageExceptionRecordReporter;
import com.meituan.msc.modules.update.bean.PackageInfoWrapper;
import com.meituan.msc.util.perf.PerfEventRecorder;
import com.meituan.msc.util.perf.PerfTrace;
import com.meituan.msi.api.component.input.MSIBaseInput;
import com.meituan.msi.bean.LifecycleData;
import com.meituan.msi.lifecycle.IPageLifecycleCallback;
import com.meituan.msi.provider.IContainerStageProvider;

import org.json.JSONException;
import org.json.JSONObject;

import java.lang.ref.WeakReference;
import java.util.HashMap;
import java.util.Map;

/**
 * Page层，即小程序view展示层
 * 一个Page对应小程序概念中页面栈的一个层级，在有tab时，平级的多个tab页（PageViewWrapper）均包含在同一个Page中
 * <p>
 * <p>
 * todo refactor page depth
 * 不同层级能力功能分开处理
 */
@SuppressLint("ViewConstructor")
public class Page extends BasePage implements PageViewWrapper.OnHorizontalSwipeListener,
        KeyboardHeightObserver,
        IWebFocusDispatcher, ITransitionPage {
    public static final String CLASS_NAME = "Page";
    public final String TAG = "Page@" + Integer.toHexString(hashCode());

    // 全局
    private String mCurPagePath;

    // View结构
    private ITabPage mTabPage;
    private IPageModule mCurPageModule;
    protected PageViewWrapper mCurrentViewWrapper;

    private int keyboardHeightInLayout; // 已设置进content.getChildAt(0)的高度中的键盘高度，在键盘高度变化时需要用最新值更新已有值
    private boolean isKeyboardShow = false;
    private static int fragmentIdentity;

    // 启动
    /**
     * @deprecated 使用routeTime
     */
    @Deprecated
    long pageStartTimeCurrentTimeMillis;
    private boolean isShow = false;

    // 各种模式
    private boolean launchingHome;
    protected boolean sinkMode;

    private int mClickTitleCount = 0;
    private long mLastClickTitleTime;

    private IPageLifecycleInterceptor pageLifecycleInterceptor;

    private final static Handler MAIN_HANDLER = new Handler(Looper.getMainLooper());
    private CheckWhiteScreenRunnable checkWhiteScreenRunnable;
    private CheckWhiteScreenRunnable StartPageAdvancedCheckWhiteScreenRunnable;
    private HashMap<String, Object> renderProcessGoneState;
    //已经上报秒开
    boolean isReportStartTime;
    private AppPageReporter pageReporter;
    private PageExceptionRecordReporter pageExceptionReporter;
    //路由映射
    private String originPath;
    public BizNavigationExtraParams bizNavigationExtraParams = new BizNavigationExtraParams.Builder().build();

    private PageEventListenerWrapper pageEventListenerWrapper;

    private class PageEventListenerWrapper implements PageEventListener {
        private PageEventListener referPageEventListener;

        public PageEventListenerWrapper(PageEventListener pageEventListener) {
            referPageEventListener = pageEventListener;
        }

        public void setReferPageEventListener(PageEventListener pageEventListener) {
            referPageEventListener = pageEventListener;
        }

        @Override
        public void onPageFirstRender(String path, HashMap<String, Object> paramMap, String openType) {
            if (referPageEventListener != null) {
                referPageEventListener.onPageFirstRender(path, paramMap,openType);
            }
        }

        @Override
        public void onAppRoute(OpenParams openParams, int viewId, int reloadViewId, String cache) {
            if (pageReporter != null) {
                pageReporter.setOnAppRouteStartTime(System.currentTimeMillis());
            }
            if (referPageEventListener != null) {
                referPageEventListener.onAppRoute(openParams, viewId, reloadViewId, cache);
            }
        }

        @Override
        public void notifyServiceSubscribeUIEventHandler(String event, JSONObject params, int viewId) {
            if (referPageEventListener != null) {
                referPageEventListener.notifyServiceSubscribeUIEventHandler(event, params, viewId);
            }
        }
    }

    /**
     * @param reloadViewId 在页面reload时，需要告知service层之前的pageId并更新为新的id，默认是View.NO_ID
     * @param routeTime
     * @param isFirstPage  是本Activity中最靠栈底的一个Page
     */
    public Page(MSCRuntime runtime,
                IContainerDelegate controller,
                PageEventListener eventListener,
                String curPagePath,
                ITabPage tabPage,
                @Nullable int reloadViewId,
                long routeTime,
                boolean isFirstPage,
                String originPath,
                boolean isFirstPageV2) {
        super(runtime, controller, eventListener, isFirstPage, isFirstPageV2);
        if (MSCTraceUtil.TRACE_LEVEL >= MSCTraceUtil.LEVEL_LIGHT) {
            PerfTrace.begin("Page#beforeInit");
        }
        pageStartTimeCurrentTimeMillis = System.currentTimeMillis();
        this.mTabPage = tabPage;
        this.originPath = originPath;
        setRouteTime(routeTime);
        if (MSCHornRollbackConfig.enablePageExitFixMissSceneAndDuration()) {
            pageEventListenerWrapper = new PageEventListenerWrapper(eventListener);
        }

        mCurPagePath = curPagePath;
        if (MSCTraceUtil.TRACE_LEVEL >= MSCTraceUtil.LEVEL_LIGHT) {
            PerfTrace.end("Page#beforeInit");
        }
        init(mContext, curPagePath, reloadViewId);
        if (MSCHornRollbackConfig.enableReportAPIPerformanceStatisticData()) {
            if(getMSILifecycleCallback() != null) {
                getMSILifecycleCallback().onPageCreate(getViewId(), new LifecycleData());
            }
            if (mCurrentViewWrapper.getRenderer() != null) {
                BaseRenderer renderer = mCurrentViewWrapper.getRenderer();
                renderer.pageData.msiContainerStage = IContainerStageProvider.C_ContainerLaunch;
            }
        }
        mCurrentViewWrapper.getRenderer().setPageStartTime(pageStartTimeCurrentTimeMillis);
    }

    public void setContainerReporter(ContainerReporter containerReporter) {
        mCurPageModule.getRenderer().setContainerReporter(containerReporter, this.routeTime);
    }

    private void init(Context context, String url, int reloadId) {
        PageViewWrapper pageViewWrapper = createPageViewWrapper(context, url, reloadId, this.mIsFirstPage);
        MSCLog.i(TAG, "init Page", "view@" + pageViewWrapper.getViewId(), url, reloadId, mIsHalfScreenPage);
        addView(pageViewWrapper, mController.createPageLayoutParams(this));
        ensurePageLifecycleInterceptor();
        if (mIsHalfScreenPage) {
            setOnClickListener(new OnClickListener() {
                @Override
                public void onClick(View v) {
                    mController.getMSCContainer().invokeBackPress();
                }
            });
        }
    }

    public boolean isTabPage() {
        return mTabPage != null;
    }

    private void ensurePageLifecycleInterceptor() {
        if (pageLifecycleInterceptor != null) return;
        pageLifecycleInterceptor = new IPageLifecycleInterceptor() {
            CoverViewRootContainer underCoverViewContainer = null;

            private CoverViewRootContainer getContainer() {
                MultiLayerPage refreshLayout = getSwipeRefreshLayout();
                if (refreshLayout != null) {
                    return refreshLayout.getCoverViewContainer();
                }
                return null;
            }

            private CoverViewRootContainer getUnderContainer() {
                MultiLayerPage refreshLayout = getSwipeRefreshLayout();
                if (refreshLayout != null) {
                    return refreshLayout.getUnderCoverViewContainer();
                }
                return null;
            }

            @Override
            public boolean onInterceptorPageBack() {
                CoverViewRootContainer coverViewContainer = getContainer();
                if (coverViewContainer != null) {
                    boolean handled = false;
                    if (sinkMode && (underCoverViewContainer = getUnderContainer()) != null) {
                        handled = underCoverViewContainer.onBackPressed();
                    }
                    if (handled) {
                        return true;
                    }
                    return coverViewContainer.onBackPressed();
                }
                return false;
            }

            @Override
            public void onPagePause(int cause) {
                CoverViewRootContainer coverViewContainer = getContainer();
                if (coverViewContainer != null) {
                    coverViewContainer.onPagePaused(cause);
                }

                if (sinkMode && (underCoverViewContainer = getUnderContainer()) != null) {
                    underCoverViewContainer.onPagePaused(cause);
                }
            }

            @Override
            public void onPageResume() {
                CoverViewRootContainer coverViewContainer = getContainer();
                if (coverViewContainer != null) {
                    coverViewContainer.onPageResume();
                }
                if (sinkMode && (underCoverViewContainer = getUnderContainer()) != null) {
                    underCoverViewContainer.onPageResume();
                }
            }
        };
    }

    public IContentScroller getContentScroller() {
        return mCurrentViewWrapper.getRenderer().getRendererView();
    }

    public void requestWebLayout() {
        getContentScroller().requestContentLayout();
    }

    public void onSizeChanged() {
        if (mIsWidget) {
            MSCLog.i(TAG, "onWidgetSizeChanged: " + mCurrentViewWrapper.getWidth() + " * " + mCurrentViewWrapper.getHeight());
            JSONObject params = JsonUtil.of(
                    "width", DisplayUtil.toWebValue(mCurrentViewWrapper.getWidth()),
                    "height", DisplayUtil.toWebValue(mCurrentViewWrapper.getHeight()));
            mRuntime.getJSModuleDelegate(WidgetListener.class)
                    .onWidgetSizeChanged(params.toString(), getViewId());
        }
    }

    @Override
    protected void onConfigurationChanged(Configuration newConfig) {
        super.onConfigurationChanged(newConfig);
        // 非展示页面，下次展示的时候再发送 resize事件
        if (!isShow) {
            pendingSendResizeConfiguration = newConfig;
            return;
        }
        MSCExecutors.postOnUiThread(new Runnable() {
            @Override
            public void run() {
                onPageResize(newConfig);
            }
        });
    }

    Configuration pendingSendResizeConfiguration;

    private void onPageResize(Configuration newConfig) {
        if (mCurPageModule == null || mController.getMSCContainer() == null
                || mController.getMSCContainer().getWindow() == null) {
            return;
        }
        // 展示的页面
        int[] widthHeight = ScreenUtil.getScreenWidthAndHeight(mContext instanceof Activity ? (Activity) mContext : null, mController.getMSCContainer().getMPAppId());

        int[] windowInfo = mCurPageModule.getWindowSize();
        mRuntime.getJSModuleDelegate(PageListener.class).onResize(
                JsonUtil.parseToJson(HashMapHelper.of("orientation",
                        newConfig.orientation == Configuration.ORIENTATION_LANDSCAPE ? "landscape" : "portrait",
                        "size", HashMapHelper.of(
                                "windowHeight", (windowInfo != null && windowInfo[1] != 0) ? DisplayUtil.toWebValueRound(windowInfo[1]) : newConfig.screenHeightDp,
                                "windowWidth", (windowInfo != null && windowInfo[0] != 0) ? DisplayUtil.toWebValueRound(windowInfo[0]) : newConfig.screenWidthDp,
                                "screenHeight", DisplayUtil.toWebValueRound(widthHeight[1]),
                                "screenWidth", DisplayUtil.toWebValueRound(widthHeight[0])
                        ))), getViewId());

    }

    private void resendPageResizeIfNeed() {
        if (isShow && pendingSendResizeConfiguration != null) {
            MSCExecutors.postOnUiThread(new Runnable() {
                @Override
                public void run() {
                    if (pendingSendResizeConfiguration != null) {
                        final Configuration config = pendingSendResizeConfiguration;
                        pendingSendResizeConfiguration = null;
                        onPageResize(config);
                    }
                }
            });
        }
    }

    /**
     * 创建封装下拉刷新功能的WebView包装视图
     *
     * @param context 上下文
     * @param url     页面路径
     * @param pageId
     * @return 封装下拉刷新功能的WebView包装视图
     */
    private PageViewWrapper createPageViewWrapper(Context context, String url, int pageId, boolean isFirstTab) {
        final PageViewWrapper pageViewWrapper = new PageViewWrapper(context).setReloadViewId(pageId).setParentPage(this);
        pageViewWrapper.setTag(url);
        mCurrentViewWrapper = pageViewWrapper;

        Map<String, String> bizTagsForPage = getBizTagsForPage(url);
        BaseRenderer renderer = retainRenderer(url, isFirstTab, bizTagsForPage, null);
        triggerResourceControlWhenPreloadUsed(renderer, pageId);
        setUpPageView(url, renderer, pageViewWrapper, bizTagsForPage);
        return pageViewWrapper;
    }

    private void triggerResourceControlWhenPreloadUsed(BaseRenderer renderer, int pageId) {
        if (MSCHornPreloadConfig.enableControlPreloadWebViewPage()) {
            if (renderer.hasTriggerDeepPreloadWebView()) {
                ResourceManager.getInstance().traceWhenPreloadUsed("MSC", "preloadWebViewPageDeep", String.valueOf(pageId));
            }
        }
        if (MSCHornPreloadConfig.enableControlPreloadWebViewBlankPage()) {
            if (renderer.hasTriggerPreloadBlankWebView()) {
                ResourceManager.getInstance().traceWhenPreloadUsed("MSC", "preloadWebViewBlankPage", String.valueOf(pageId));
            }
        }
        if (MSCHornPreloadConfig.enableControlWebViewSegmentPreload()) {
            if (renderer.hasTriggerFourthSegmentPreloadWebView()) {
                ResourceManager.getInstance().traceWhenPreloadUsed("MSC", "webViewSegmentPreload4", "webview");
            }
        }
    }

    /**
     * 创建，或从缓存中获取，并关联至本Page
     */
    public BaseRenderer retainRenderer(String url, boolean isFirstPage, Map<String, String> bizTagsForPage, Boolean isFirstPageV2) {
        if (MSCTraceUtil.TRACE_LEVEL >= MSCTraceUtil.LEVEL_LIGHT) {
            PerfTrace.begin("Page#retainRenderer");
        }
        IRendererManager rendererManager = mRuntime.getModule(IRendererManager.class);
        BaseRenderer renderer = rendererManager.retainRenderer(url);
        renderer.setContainerDelegate(mController);
        // 如果不是首页面，则页面自己创建一个PerfEventRecorder, 有自己独立的sid
        if (!isFirstPage) {
            PerfEventRecorder perfEventRecorder = new PerfEventRecorder(true, true);
            PerfTrace.instant(PerfEventConstant.PERF_SESSION_START);
            renderer.setPerfEventRecorder(perfEventRecorder);
        } else {
            // 如果是首页面，就使用Runtime内的PerfEventRecorder
            renderer.setPerfEventRecorder(mRuntime.getPerfEventRecorder());
        }

        AppPageReporter appPageReporter = AppPageReporter.create(mRuntime, mController, renderer, url,
                this.mIsFirstPage && isFirstPage, mIsWidget, bizTagsForPage, isFirstPageV2 != null ? isFirstPageV2 : this.mIsFirstPageV2);
        appPageReporter.commonTag("originPath", originPath);
        appPageReporter.commonTag("hasRouteMapping", !TextUtils.equals(originPath, mCurPagePath));
        renderer.setAppPageReporter(appPageReporter);
        if (pageEventListenerWrapper != null) {
            renderer.setEventListener(pageEventListenerWrapper);
        } else {
            renderer.setEventListener(mEventListener);
        }
        if (MSCHornPerfConfig.getInstance().enableFPUsePageStartTime()) {
            appPageReporter.onCreatePage(routeTime, mRuntime.getAppId());
        } else {
            appPageReporter.onCreatePage(pageStartTimeCurrentTimeMillis, mRuntime.getAppId());
        }
        appPageReporter.setRouteTime(routeTime);
        this.pageExceptionReporter = new PageExceptionRecordReporter(mRuntime);
        this.pageReporter = appPageReporter;

        //fixme MSC01 performanceManager Module
        IPerformanceManager performanceManager = mRuntime.getModuleWithoutDelegate(IPerformanceManager.class);
        if (performanceManager != null && performanceManager.isPerformanceManagerOpened()) {
            renderer.setPerformanceListener(performanceManager);
        }
        if (MSCTraceUtil.TRACE_LEVEL >= MSCTraceUtil.LEVEL_LIGHT) {
            PerfTrace.end("Page#retainRenderer");
        }
        return renderer;
    }

    @Nullable
    public Map<String, String> getBizTagsForPage(String url) {
        MSCReportBizTagsManager.BizTagsData bizTags = MSCReportBizTagsManager.getInstance().getBizTags(mRuntime.getAppId(), url);
        return bizTags != null ? bizTags.getBizTagsForPage(url) : null;
    }

    public void resetPageStartTime() {
        pageStartTimeCurrentTimeMillis = System.currentTimeMillis();
    }

    private void setUpPageView(String url, BaseRenderer renderer, PageViewWrapper pageViewWrapper, Map<String, String> bizTagsForPage) {
        if (MSCTraceUtil.TRACE_LEVEL >= MSCTraceUtil.LEVEL_LIGHT) {
            PerfTrace.begin("Page#setUpPageView");
        }
        pageViewWrapper.setUpPageViewWrapper(mRuntime,
                renderer, url, mIsWidget, mIsFirstPage, new SwipeRefreshLayout.TouchInterceptor() {
                    @Override
                    public boolean onInterceptTouchEvent(MotionEvent ev) {
                        return Page.this.hasInnerWebViewModule();
                    }
                });
        setPageBackgroundColor(pageViewWrapper, url);

        if (MSCTraceUtil.TRACE_LEVEL >= MSCTraceUtil.LEVEL_LIGHT) {
            PerfTrace.begin("Page#setUpPageView#refreshLayout");
        }
        MultiLayerPage refreshLayout = pageViewWrapper.getRefreshLayout();
        boolean enablePullRefresh = mRuntime.getMSCAppModule().isEnablePullDownRefresh(url);
        pageViewWrapper.setRefreshEnable(enablePullRefresh);
        refreshLayout.setEnabled(enablePullRefresh);
        refreshLayout.setBackgroundTextStyle(mRuntime.getMSCAppModule().isBackgroundTextStyleDark(url));
        refreshLayout.setOnRefreshListener(new SwipeRefreshLayout.OnRefreshListener() {
            @Override
            public void onRefresh() {
                MSCLog.i(TAG, "start onPullDownRefresh");
                onPageEventToMsi(PullDownRefreshApi.MSI_PULL_DOWN_REFRESH_EVENT, new PullDownRefreshParam(getViewId()));
            }
        });
        if (MSCTraceUtil.TRACE_LEVEL >= MSCTraceUtil.LEVEL_LIGHT) {
            PerfTrace.end("Page#setUpPageView#refreshLayout");
        }

        pageViewWrapper.setNavigationBarButtonClickListener(mOnNavigationBarButtonClickListener);

        if (!mIsFirstPage) {
            pageViewWrapper.setSwipeListener(this);
        }

        PageModule pageModule = new PageModule(renderer, pageViewWrapper, mTabPage, url, this)
                .setIsWidget(mIsWidget)
                .setViewGroupImp(new NativeViewDefaultImpl(this, refreshLayout))
                .setBizTags(bizTagsForPage);
        mRuntime.getContainerManagerModule().registerSubModule(pageModule, IPageModule.class);
        mCurPageModule = pageModule;
        // MSCTransparentActivity
        boolean containerIsTransparent = mController.isTransparentContainer();
        PageTransitionConfig pageTransitionConfig = pageModule.getPageTransitionConfig();
        // 上个页面是否是全屏页，isFullScreenOfLastPage为null代表当前Page是首页。
        Boolean isFullScreenOfLastPage = mController.getPageMangerModule().getIsFullScreenOfLastPage();
        // 配置是半屏页的配置：pushStyle=2并且配置了halfPageStyle的pageHeight和pageHeightPercent的任意个有效配置。
        boolean configIsHalfPage = pageTransitionConfig != null && pageTransitionConfig.pushStyle == PageTransitionConfig.TransitionStyle.SLIDE_UP_SLIDE_DOWN && (pageTransitionConfig.pageHeight > 0 || pageTransitionConfig.pageHeightPercent > 0);
        // Page半屏透明条件：非Widget场景；透明容器的Page会全部半屏，非透明容器的Page必须配置了半屏配置以及非首页。
        mIsHalfScreenPage = !mController.isWidget() && (containerIsTransparent || (isFullScreenOfLastPage != null && configIsHalfPage));
        pageViewWrapper.setHalfScreenPage(mIsHalfScreenPage);
        addShadowViewIfNeed();
        if (MSCTraceUtil.TRACE_LEVEL >= MSCTraceUtil.LEVEL_LIGHT) {
            PerfTrace.end("Page#setUpPageView");
        }
    }

    private void addShadowViewIfNeed() {
        if (!needAddShadowViewWhenAddPage()) {
            return;
        }
        getPushTransition().addTransitionListener(new SimpleTransitionListener() {
            @Override
            public void endTransition(LayoutTransition transition, ViewGroup container, View view, int transitionType) {
                getPushTransition().removeTransitionListener(this);
                ColorDrawable shadowDrawable = new ColorDrawable(Color.BLACK);
                // 12.30.200，设计给出透明度规范：0.6
                shadowDrawable.setAlpha(MSCConfig.getHalfDialogShadowAlpha());
                Page.this.setBackground(shadowDrawable);
            }
        });

        getPopTransition().addTransitionListener(new SimpleTransitionListener() {
            @Override
            public void startTransition(LayoutTransition transition, ViewGroup container, View view, int transitionType) {
                getPopTransition().removeTransitionListener(this);
                Page.this.setBackground(null);
            }
        });
        MSCLog.i(TAG, "addShadowViewIfNeed", getPagePath());
    }

    private boolean needAddShadowViewWhenAddPage() {
        if (!mIsHalfScreenPage) {
            return false;
        }
        PageTransitionConfig pageTransitionConfig = getCurPageModule().getPageTransitionConfig();
        if (pageTransitionConfig == null) {
            return false;
        }
        IPageModule topPage = mController.getPageMangerModule().getTopPage();
        if (topPage == null) {
            return false;
        }
        int prePageHeight = topPage.getHeight();
        int curPageHeight = HalfPageUtils.getValidHalfPageSize(getContext(), pageTransitionConfig.pageHeight, pageTransitionConfig.pageHeightPercent).pageHeight;
        MSCLog.i(TAG, "needAddShadowViewWhenAddPage curPageHeight:" + curPageHeight + ", prePageHeight:" + prePageHeight);
        return curPageHeight < prePageHeight;
    }

    /**
     * 设置 Page 或 Widget 背景色
     */
    private void setPageBackgroundColor(PageViewWrapper pageViewWrapper, String url) {
        if (mIsWidget) {
            String widgetBackgroundColorStr = mRuntime.getMSCAppModule().getWidgetBackgroundColor(url);
            if (!TextUtils.isEmpty(widgetBackgroundColorStr)) {
                pageViewWrapper.setWidgetBackgroundColor(ColorUtil.parseColor(widgetBackgroundColorStr, Color.WHITE));
                return;
            } else {
                // WebView删除默认的白色背景，由容器设置
                // https://km.sankuai.com/collabpage/2082418846
                if (!MSCHornRollbackConfig.isRollbackWidgetDefaultBackgroundColor() && !pageViewWrapper.getRenderer().isNativeRender()) {
                    pageViewWrapper.setWidgetBackgroundColor(Color.WHITE);
                    return;
                }
            }
        }
        int color = mRuntime.getMSCAppModule().getBackgroundColor(url);
        if (!mIsHalfScreenPage) {
            pageViewWrapper.setBackgroundColor(color);
        } else {
            MSCLog.i(TAG, "disable setBackgroundColor at transparent container");
        }
    }

    public void setSinkModeBackgroundColor() {
        if (!MSCHornRollbackConfig.isRollbackSinkModeBackgroundColor()) {
            // 沉浸模式场景 支持单独设置 沉浸 native 组件所在层级的背景色
            // https://km.sankuai.com/collabpage/2504612856
            String url = mCurrentViewWrapper.getUrl();
            String sinkModeBackgroundColorStr = mRuntime.getMSCAppModule().getSinkModeBackgroundColor(url);
            if (!TextUtils.isEmpty(sinkModeBackgroundColorStr)) {
                mCurrentViewWrapper.setSinkModeBackgroundColor(ColorUtil.parseColor(sinkModeBackgroundColorStr, Color.WHITE));
            }
        }
    }

    @Override
    protected void onAttachedToWindow() {
        super.onAttachedToWindow();
        mIsDetached = false;
        ensurePageLifecycleInterceptor();
        MSCLog.i(TAG, "onAttachedToWindow() view@", +getViewId());
        //fixme msc
        mController.registerKeyboardListener(this);
        mRuntime.incrementPageCount(1);
    }

    @Override
    protected void onDetachedFromWindow() {
        super.onDetachedFromWindow();
        mIsDetached = true;
        MSCLog.i(TAG, String.format("view@%s onDetachedFromWindow()", getViewId()));
        // widget 场景仅在mark之后的移除才做销毁
        if (!mIsWidget || mMarkDestroy) {
            handleOnDestroy();
        }
        mRuntime.incrementPageCount(-1);
    }

    private boolean mMarkDestroy;
    private boolean mDestroyed;
    private boolean mIsDetached;

    public void onDestroy() {
        mMarkDestroy = true;
        MSCLog.i(TAG, String.format("view@%s markDestroy()", getViewId()));
        // destroy 时机早于从屏幕移除的时机，早于 Detach 移除会有一帧白屏
        if (mIsDetached) {
            handleOnDestroy();
        }
    }

    public void handleOnDestroy() {
        if (mDestroyed) {
            return;
        }
        getMSILifecycleCallback().onPageDestroy(getViewId(), new LifecycleData());
        mDestroyed = true;
        MSCLog.i(TAG, String.format("view@%s handleOnDestroy()", getViewId()));
        if (!MSCHornRollbackConfig.isRemovePageDestroyHideKeyboard(mRuntime.getMSCAppModule().getAppId())) {
            // FIXME 12.18.200仅修复反馈问题的业务（神枪手），12.18.400全量回归出现问题，预期12.19.200新方案全量
            InputMethodUtil.hideSoftInputFromWindow(mContext, getWindowToken(), InputMethodManager.HIDE_NOT_ALWAYS);
        }
        if (mCurrentViewWrapper != null && mCurrentViewWrapper.webViewModuleRef != null) {
            onKeyBoardHide();
        }

        mController.unRegisterKeyboardListener(this);
//        MemoryMonitor.onPageCancel(createMemoryRecord());

        if (mCurrentViewWrapper != null) {
            mCurrentViewWrapper.removeAllViews();
            mCurrentViewWrapper.setTag(null);
            mCurrentViewWrapper.destroy();
        }
        removeAllViews();
        if (mCurPageModule != null) {
            mCurPageModule.destroy();
        }

        if (mCurrentViewWrapper != null && mCurrentViewWrapper.webViewModuleRef != null) {
            View webView = mCurrentViewWrapper.webViewModuleRef.get();
            mCurrentViewWrapper.webViewModuleRef.clear();
            if (webView instanceof WebViewComponentWrapper) {
                ((WebViewComponentWrapper) webView).destroy();
            }
        }
        if (checkWhiteScreenRunnable != null) {
            MAIN_HANDLER.removeCallbacks(checkWhiteScreenRunnable);
        }

        //页面销毁，清空页面数据预拉取
        IDataPrefetchModule dataPrefetchModule = mRuntime.getModule(IDataPrefetchModule.class);
        if (dataPrefetchModule != null) {
            dataPrefetchModule.onPageDestroy(getViewId());
        }

        //清理页面的启动信息
        if (pageReporter != null) {
            InstrumentLaunchManager.getInstance().removeLaunchInfo(String.valueOf(pageReporter.getRouteId()));
        }
    }

    @Override
    public void onKeyboardHeightChanged(int keyboardHeight, int screenOrientation) {
        MSCLog.i(TAG, "[MSCKeyboard]onKeyboardHeightChanged:", keyboardHeight,
                ", isShow:", isShow,
                ", mCurPagePath:", mCurPagePath);
        if (this.isShow) {
            if (keyboardHeight > 0) {
                isKeyboardShow = true;
                onKeyBoardShow(keyboardHeight);
            } else {
                isKeyboardShow = false;
                onKeyBoardHide();
            }
        }
    }

    private void onKeyBoardShow(int keyboardHeight) {
        fragmentIdentity = System.identityHashCode(this);
        if (MSIBaseInput.enableMscFixedKeyboardHeight(mRuntime.getAppId())) {
            if (keyboardHeight != 0) {
                int naviBarHeight = 0;
                if (getContext() instanceof Activity) {
                    naviBarHeight = SystemInfoUtils.getSystemNavigationBarHeight((Activity) getContext());
                }
                keyboardHeight -= naviBarHeight;
            }
        }
        mCurrentViewWrapper.setKeyboardHeight(keyboardHeight);
        mCurrentViewWrapper.processInputAction(true);

        MultiLayerPage pageSwipeRefreshLayout = getSwipeRefreshLayout();
        if (pageSwipeRefreshLayout == null || pageSwipeRefreshLayout.getCoverViewContainer() == null) {
            return;
        }

        pageSwipeRefreshLayout.getCoverViewContainer().onKeyboardShow();
        if (mCurrentViewWrapper.webViewModuleRef != null && mCurrentViewWrapper.webViewModuleRef.get() != null) {
            //fixme https://ones.sankuai.com/ones/product/6246/workItem/defect/detail/5076900
            //fixme https://issuetracker.google.com/issues/36911528
            View innerWebView = mCurrentViewWrapper.webViewModuleRef.get();
            if (innerWebView instanceof WebViewComponentWrapper) {
                ((WebViewComponentWrapper) innerWebView).evaluateJavascript(WebViewConst.SCRIPT_INJECT_INPUT);
            }
        }
    }

    private void onKeyBoardHide() {
        // 多widget场景, 修复fragmentIdentity错位造成的键盘偶现不响应收起 https://km.sankuai.com/collabpage/2308056684
        if (System.identityHashCode(this) == fragmentIdentity) {
            mCurrentViewWrapper.setKeyboardHeight(0);
            mCurrentViewWrapper.processInputAction(false);
            WeakReference<View> viewModuleRef = mCurrentViewWrapper.webViewModuleRef;
            if (viewModuleRef != null) {
                View viewWrapper = viewModuleRef.get();
                if (viewWrapper instanceof WebViewComponentWrapper) {
                    WebViewComponentWrapper tmpWebView = (WebViewComponentWrapper) viewWrapper;
                    //fixme https://ones.sankuai.com/ones/product/6246/workItem/defect/detail/5076900
                    //fixme https://issuetracker.google.com/issues/36911528
                    FrameLayout content = ((Activity) mContext).findViewById(android.R.id.content);
                    View mChildOfContent = content.getChildAt(0);
                    FrameLayout.LayoutParams frameLayoutParams = (FrameLayout.LayoutParams) mChildOfContent.getLayoutParams();
                    if (frameLayoutParams.height != FrameLayout.LayoutParams.MATCH_PARENT) {
                        frameLayoutParams.height = FrameLayout.LayoutParams.MATCH_PARENT;
                        keyboardHeightInLayout = 0;
                        //          mChildOfContent.forceLayout();
                        mChildOfContent.requestLayout();
                        tmpWebView.post(new Runnable() {//patch  调整大小的时候webview不刷新又延迟，模拟上下滚动1个像素
                            @Override
                            public void run() {
                                tmpWebView.scrollBy(0, -1);
                                tmpWebView.scrollBy(0, 1);
                            }
                        });
                    }
                }
            }
            //   mCoverViewContainer.clearFocus();
            adjustPan(0);
        }
    }


    public boolean tabClickable() {
        return mCurrentViewWrapper.isToastMasking();
    }

    private void scheduleDetector(final PageViewWrapper view) {
        // 解决快速操作触发的多次检测问题
        if (checkWhiteScreenRunnable != null) {
            MAIN_HANDLER.removeCallbacks(checkWhiteScreenRunnable);
        }

        checkWhiteScreenRunnable = new CheckWhiteScreenRunnable(view, renderProcessGoneState, false);
        mRuntime.jsErrorRecorder.start();
        MSCLog.i(TAG, "White_Screen_Countdown_Begins", mCurPagePath,
                "PageViewWrapper@", Integer.toHexString(view.hashCode()), renderProcessGoneState);
        boolean executed = MAIN_HANDLER.postDelayed(checkWhiteScreenRunnable, MSCConfig.getCheckWhiteScreenPending() * 1000L);
        if (!executed) {
            MSCLog.i(TAG, "scheduleDetector execute failed");
            reportWhiteScreenCancelCheckReason("scheduleDetector execute failed", false);
        }
    }

    private void scheduleDetectorStartPageAdvanced(final PageViewWrapper view) {
        // 解决快速操作触发的多次检测问题
        if (StartPageAdvancedCheckWhiteScreenRunnable != null) {
            MAIN_HANDLER.removeCallbacks(StartPageAdvancedCheckWhiteScreenRunnable);
        }

        StartPageAdvancedCheckWhiteScreenRunnable = new CheckWhiteScreenRunnable(view, renderProcessGoneState, true);
        mRuntime.jsErrorRecorder.start();
        MSCLog.i(TAG, "New_White_Screen_Countdown_Begins", mCurPagePath,
                "PageViewWrapper@", Integer.toHexString(view.hashCode()), renderProcessGoneState);
        boolean executed = MAIN_HANDLER.postDelayed(StartPageAdvancedCheckWhiteScreenRunnable, MSCConfig.getCheckWhiteScreenPending() * 1000L);
        if (!executed) {
            MSCLog.i(TAG, "scheduleDetectorStartPageAdvanced execute failed");
            reportWhiteScreenCancelCheckReason("scheduleDetectorStartPageAdvanced execute failed", true);
        }
    }

    /**
     * 页面dom内容加载完成
     */
    void onAppRoute(OpenParams openParams, int viewId) {
        // 页面退出时，会发一个navigateback，此时不应该设置approute值。
        if (!OpenParams.NAVIGATE_BACK.equals(openParams.openType)) {
            if (mCurrentViewWrapper != null && mCurrentViewWrapper.getRenderer() != null
                    && mCurrentViewWrapper.getRenderer() instanceof MSCWebViewRenderer) {
                ((MSCWebViewRenderer) mCurrentViewWrapper.getRenderer()).setServiceState(AppServiceState.APP_ROUTE);
            }
        }
        if (mEventListener != null) {
            MSCLog.i(TAG, "onAppRoute, openType=", openParams.openType, "pagePath=", openParams.url, "viewId=", viewId);
            mEventListener.onAppRoute(openParams, viewId, NO_ID, "");
            // 这块暂时不需要初始缓存功能。
        }

        if (pageReporter != null) {
            pageReporter.setOnAppRouteStartTime(System.currentTimeMillis());
        }
    }

    void onPageEventToMsi(String event, Object param) {
        mRuntime.apisManager.dispatchEvent(event, param);
    }

    LayoutTransition popTransition;
    LayoutTransition pushTransition;

    @Override
    public LayoutTransition getPopTransition() {
        if (popTransition == null) {
            popTransition = PageLayoutTransitionHelper
                    .getPopTransition(mCurPageModule != null ? mCurPageModule.getPageTransitionConfig() : null, this);
        }
        return popTransition;
    }

    @Override
    public LayoutTransition getPushTransition() {
        if (pushTransition == null) {
            pushTransition = PageLayoutTransitionHelper
                    .getPushTransition(mCurPageModule != null ? mCurPageModule.getPageTransitionConfig() : null);
        }
        return pushTransition;
    }

    public IPageModule getCurPageModule() {
        return mCurPageModule;
    }

    @Nullable
    public IPageModule getPageModuleById(int viewId) {
        return viewId == getViewId() ? mCurPageModule : null;
    }

    @Override
    Page getPage() {
        return this;
    }

    /**
     * page lifecycle onShow
     */
    public void onShow(String openType) {
        MSCLog.i(TAG, "page lifecycle onShow");
        mRuntime.setIsProcessGone(false);
        mRuntime.setIsExit(false);
        if (!isShow && pageExceptionReporter != null) {
            pageExceptionReporter.setStartTime(System.currentTimeMillis());
        }
        mCurrentViewWrapper.ensureShow();
        isShow = true;
        onPageResume(openType);
        ReportUtils.addBizPkgInfoToMSIContainer(mRuntime);
    }

    /**
     * page lifecycle onHide
     */
    public void onHide(int cause) {
        MSCLog.i(TAG, "page lifecycle onHide");
        // 对齐 com.meituan.mmp.lib.engine.AppPage#onHide 中的异常上报埋点逻辑
        // 若是progressgone触发的，则忽略即可。
        if (isShow && pageExceptionReporter != null) {
            BaseRenderer baseRenderer = mCurrentViewWrapper.getRenderer();
            MSCLog.i(TAG, "onHide,", mRuntime.TAG, baseRenderer.TAG, pageExceptionReporter.TAG);
            AppPageReporter appPageReporter = baseRenderer.pageData.appPageReporter;
            IContainerDelegate containerDelegate = baseRenderer.getContainerDelegate();
            // 兜底设置startTime
            MSCLog.i(TAG, "onHide, routeStartTime:", appPageReporter.getRouteStartTime(),
                    ", startTime:", pageExceptionReporter.getStartTime());
            if (appPageReporter.getRouteStartTime() > pageExceptionReporter.getStartTime()) {
                pageExceptionReporter.setStartTime(appPageReporter.getRouteStartTime());
            }
            if (containerDelegate instanceof ContainerController) {
                ContainerController containerController = (ContainerController) containerDelegate;
                pageExceptionReporter.setIsExit(containerController.isFinishing());
                if (mIsFirstPage) {
                    pageExceptionReporter.setScene(containerController.getScene() + "");
                }
            }
            pageExceptionReporter.setIsWebViewRenderer(isWebViewRenderer());
            pageExceptionReporter.setIsFirstPage(mIsFirstPage);
            pageExceptionReporter.setPkgMode(appPageReporter.getPkgMode(appPageReporter.getMetaInfo()));

                pageExceptionReporter.setPkgModeDetail(appPageReporter.getPkgModeDetail(appPageReporter.getMetaInfo()));

            pageExceptionReporter.setRuntimeSource(RuntimeSource.toReportString(mRuntime.getSource()));
            pageExceptionReporter.setIsWidget(mIsWidget);
            pageExceptionReporter.setIsExit(mRuntime.getIsExit());
            pageExceptionReporter.setPageState(baseRenderer.pageData.pageState);
            pageExceptionReporter.setServiceState(baseRenderer.pageData.serviceState);
            pageExceptionReporter.setNeedReportExceptionDot(!mRuntime.getIsProcessGone());
            boolean enableSetWebViewWhiteForegroundColor = MSCConfig.enableSetWebViewWhiteForegroundColor(mRuntime.getAppId());
            if (baseRenderer instanceof MSCWebViewRenderer && enableSetWebViewWhiteForegroundColor) {
                pageExceptionReporter.setIsWhiteForegroundShow(baseRenderer.pageData.isWhiteForegroundShow);
            }
            if (!TextUtils.isEmpty(baseRenderer.pageData.mPagePath)) {
                String pagePath = baseRenderer.pageData.mPagePath;
                pageExceptionReporter.setPagePath(pagePath);
                PackageInfoWrapper info = mRuntime.getMSCAppModule().getPackageInfoByUrl(pagePath, true);
                if (null != info) {
                    pageExceptionReporter.setPkgInstalled(info.isSourceReady);
                    pageExceptionReporter.setPkgName(info.getPackageName());
                } else {
                    pageExceptionReporter.setPkgInstalled(false);
                }
            }
            pageExceptionReporter.report(cause == IPageLifecycleInterceptor.TYPE_PAGE_PAUSE_CAUSE_ENTER_BACKGROUND_POPPED
                    || cause == IPageLifecycleInterceptor.TYPE_PAGE_PAUSE_CAUSE_NAVIGATE_POP);
        }
        isShow = false;
        onPagePause(cause);
        mCurrentViewWrapper.ensureHide();
        if (isKeyboardShow) {
            isKeyboardShow = false;
            onKeyBoardHide();
//            TextArea.hideOkBar();
        }
    }

    public PageViewWrapper getCurrentViewWrapper() {
        return mCurrentViewWrapper;
    }

    boolean isPaused = true;

    /**
     * inner page lifecycle
     */
    protected void onPagePause(int cause) {
        if (isPaused) {
            return;
        }
        isPaused = true;
        if (pageLifecycleInterceptor != null) {
            pageLifecycleInterceptor.onPagePause(cause);
        } else {
            MSCLog.w("pageLifecycleInterceptor not found!");
        }
        getMSILifecycleCallback().onPagePaused(getViewId(), new LifecycleData(cause));
        mRuntime.publish(new MSCEvent<>(IPageModule.PAGE_LIFECYCLE_PAUSE, mCurPageModule));
        MPListenerManager.getInstance().pageListener.onPagePaused(mRuntime.getMSCAppModule().getAppId(), mCurPagePath, getWindowToken());
        this.pageReporter.onHide();
    }

    /**
     * inner page lifecycle
     */
    protected void onPageResume(String openType) {
        updateLastPageTransparentState();
        renderProcessGoneState = mCurrentViewWrapper.reloadIfRenderProcessGone();

        if (!isPaused) {
            MSCLog.i(TAG, "OnPageResume_Repeat", mCurPagePath);
            return;
        }
        isPaused = false;

        // 每次页面展示均需要触发白屏检测，https://km.sankuai.com/page/816822616
        checkWhiteScreen(openType);

        if (pageLifecycleInterceptor != null) {
            pageLifecycleInterceptor.onPageResume();
        } else {
            MSCLog.w(TAG, "pageLifecycleInterceptor not found!");
        }
        getMSILifecycleCallback().onPageResume(getViewId(), new LifecycleData());
        mRuntime.publish(new MSCEvent(IPageModule.PAGE_LIFECYCLE_RESUME, mCurPageModule));
        MPListenerManager.getInstance().pageListener.onPageResume(mRuntime.getMSCAppModule().getAppId(), mCurPagePath, getWindowToken());
        mCurrentViewWrapper.getRenderer().pageData.appPageReporter.reportPV();
        this.pageReporter.onShow();
        resendPageResizeIfNeed();
    }

    private void checkWhiteScreen(String openType) {
        if (openType == null) {
            openType = mCurrentViewWrapper.getOpenType();
        }
        if (mController.isAppRouteTaskEnabled(openType)) {
            IAppLoader iAppLoader = mRuntime.getModule(IAppLoader.class);
            if (iAppLoader == null) {
                MSCLog.e(TAG, "checkWhiteScreen iAppLoader is null");
                return;
            }
            TaskManager taskManager = iAppLoader.getTaskManager();
            if (taskManager == null) {
                MSCLog.e(TAG, "checkWhiteScreen taskManager is null");
                return;
            }
            ITask<?> injectBuzPkgTask = taskManager.findTaskByClass(InjectBuzPkgTask.class);
            if (injectBuzPkgTask == null) {
                MSCLog.e(TAG, "checkWhiteScreen InjectBuzPkgTask is null");
                return;
            }
            ITask<?> startPageTask = taskManager.findTaskByClass(ContainerController.StartPageTaskOfLaunch.class);
            if (startPageTask == null) {
                MSCLog.e(TAG, "checkWhiteScreen startPageTask is null");
                return;
            }
            long costTime = SystemClock.elapsedRealtime() - taskManager.getExecuteStartTime(startPageTask);
            MSCLog.i(TAG, "StartPage To CheckWhiteScreen costTime: " + costTime);
            taskManager.getExecuteFinishedFuture(injectBuzPkgTask).thenRunAsync(new MSCExecutors.Serialized.SubmitRunnable(() -> {
                scheduleDetectorStartPageAdvanced(mCurrentViewWrapper);
            }, costTime));
        } else {
            scheduleDetector(mCurrentViewWrapper);
        }
    }

    private void updateLastPageTransparentState() {
        // 仅在Page页更新状态，Tab页只能是栈底页面且总是以半屏展示 不需要更新
        mController.getPageMangerModule().setIsFullScreenOfLastPage(!isHalfScreenPage());
    }

    public boolean isPagePause() {
        return isPaused;
    }

    /**
     * 启动主页面
     *
     * @param openParams 启动参数
     */
    public void onLaunchHome(OpenParams openParams) {
        MSCLog.i(TAG, String.format("onLaunchHome(%s) view@%s ", openParams.url, getViewId()));
        launchingHome = true;
        loadUrlWithSwitchTab(openParams);
        launchingHome = false;
    }

    /**
     * 重新启动主页面
     *
     * @param openParams 启动参数
     */
    public void onReLaunch(OpenParams openParams) {
        MSCLog.i(TAG, String.format("view@%s onReLaunch(%s)", getViewId(), openParams.url));
        // 首页面relaunch场景需要重新记录一次Perf_Session_Start
        if (mIsFirstPage) {
            restartPerfLogSession();
        }
        loadUrlWithSwitchTab(openParams);
    }

    public void loadUrlWithSwitchTab(OpenParams openParams) {
        if (mTabPage != null) {
            mTabPage.switchTab(openParams);
            return;
        }
        loadUrl(openParams);
    }

    /**
     * 导航到此页面
     *
     * @param openParams 页面url
     */
    public void onNavigateTo(OpenParams openParams) {
        MSCLog.i(TAG, String.format("onNavigateTo view@%s, url:%s", getViewId(), openParams.url));
        loadUrl(openParams);
    }

    /**
     * 页面恢复，用于窗口因为低内存销毁后的恢复, 此时Activity已经销毁重建
     *
     * @param openParams 启动参数
     */
    public void onReloadTo(OpenParams openParams) {
        loadUrlWithSwitchTab(openParams);
    }

    /**
     * 页面恢复
     * 用于重新跟前端建立通信联系
     *
     * @param renderer
     * @param url
     * @param routeTime
     */
    public void reloadByRenderProcessGone(BaseRenderer renderer, String url, long routeTime) {
        PageModule pageModule = new PageModule(renderer, mCurrentViewWrapper, mTabPage, url, this)
                .setIsWidget(mIsWidget)
                .setViewGroupImp(new NativeViewDefaultImpl(this, mCurrentViewWrapper.getRefreshLayout()));
        mRuntime.getContainerManagerModule().unregisterSubModule((PageModule) mCurPageModule);
        mRuntime.getContainerManagerModule().registerSubModule(pageModule, PageModule.class);
        mCurPageModule = pageModule;
        try {
            OpenParams openParams = new OpenParams.Builder()
                    .setUrl(url)
                    .setOpenType(OpenParams.RELOAD)
                    .setRouteTime(routeTime)
                    .build(mRuntime);
            onReloadTo(openParams);
        } catch (ApiException e) {
            MSCLog.e(TAG, e, "reloadByRenderProcessGone");
            ToastUtils.toast("页面跳转异常");
        }
    }

    /**
     * 重定向此页面的url
     */
    public void onRedirectTo(OpenParams openParams) {
        MSCLog.i(TAG, String.format("onRedirectTo view@%s, url:%s", getViewId(), openParams.url));
        // 首页面redirect场景需要重新记录一次Perf_Session_Start
        if (mIsFirstPage) {
            restartPerfLogSession();
        }
        loadUrl(openParams);
    }

    public void onPageNotFound(OpenParams openParams) {
        MSCLog.i(TAG, String.format("view@%s onPageNotFound(%s)", getViewId(), openParams.url));
        onAppRoute(openParams, getViewId());
    }

    public void setHasLoaded(boolean hasLoaded) {
        this.hasLoaded = hasLoaded;
    }

    boolean hasLoaded;

    public void onSwitchTabTo(OpenParams openParams) {
        if (!hasLoaded) {
            hasLoaded = true;
            // 本tab页之前未加载过内容
            loadUrl(openParams);
        } else {
            // post以保证是否新加载时时序一致，在onNewIntent触发时需要由此保证onAppRoute发生在onResume之后
            MSCExecutors.postOnUiThread(new Runnable() {
                @Override
                public void run() {
                    onAppRoute(openParams, getViewId());
                }
            });
        }
    }

    /**
     * 导航回到此页面
     */
    public void onNavigateBackTo(long routeTime) {
        MSCLog.i(TAG, String.format("onNavigateBack view@%s", getViewId()));

        // 当前端在PageNotFound时NavigateTo跳转到了其他存在的页面时，兜底页内容并没有展示，但再次back回到不存在页面时需要展示兜底内容
        if (!mRuntime.getMSCAppModule().hasPage(mCurPagePath)) {
            showPageNotFoundView();
        }

        //  修复 onAppRoute 中 OpenParams 偶现 NPE
        OpenParams openParams = new OpenParams().setOpenType(OpenParams.NAVIGATE_BACK).setRouteTime(routeTime);
        try {
            openParams = new OpenParams.Builder()
                    .setUrl(mCurrentViewWrapper.getContentUrl())
                    .setOpenType(OpenParams.NAVIGATE_BACK)
                    .setRouteTime(routeTime)
                    .build(mRuntime);
        } catch (ApiException e) {
            // TODO: 2024/3/1 tianbin 页面退出时偶现url为空崩溃，预期不该影响用户使用，待排查
            MSCLog.e(TAG, e, "onNavigateBackTo");
//            if (MSCHornRollbackConfig.get().getConfig().rollbackThrowRuntimeException) {
//                MSCLog.e(TAG, e, "onNavigateBackTo");
//                ToastUtils.toast("页面跳转异常");
//            } else {
//                throw new RuntimeException(e);
//            }
        }
        OpenParams finalOpenParams = openParams;
        MSCExecutors.postOnUiThread(new Runnable() {
            @Override
            public void run() {
                onAppRoute(finalOpenParams, getViewId());
            }
        });
        // 执行navigateBack后，记录新的栈顶页面的PUSH操作
        CrashReporterHelper.pushPage(getPagePath(), mRuntime.getMSCAppModule().getAppId(), OpenParams.NAVIGATE_BACK, mIsWidget);
    }

    private void loadUrl(final OpenParams openParams) {
        bizNavigationExtraParams = openParams.bizNavigationExtraParams;
        PageViewWrapper pageViewWrapper = mCurrentViewWrapper;
        CrashReporterHelper.pushPage(openParams.url, mRuntime.getMSCAppModule().getAppId(), openParams.openType, mIsWidget);
        MSCLog.i(TAG, String.format("loadUrl(%s, %s) view@%s", openParams.url, openParams.openType, getViewId()));
        if (TextUtils.isEmpty(openParams.url) || pageViewWrapper == null) {
            return;
        }

        pageViewWrapper.setContentUrl(openParams.url);
        pageViewWrapper.setOpenType(openParams.openType);

        Runnable runnable = new Runnable() {
            @Override
            public void run() {
                if (mIsWidget) {
                    openParams.addExtraParam("widgetSize", HashMapHelper.of(
                            "width", DisplayUtil.toWebValue(pageViewWrapper.getWidth()),
                            "height", DisplayUtil.toWebValue(pageViewWrapper.getHeight())));
                }
                loadContent(pageViewWrapper, openParams);
            }
        };
        //widget 需布局完成和onAppRoute一起将宽高信息传入
        boolean isMainThread = Thread.currentThread().getId() == Looper.getMainLooper().getThread().getId();
        if (isMainThread && !mIsWidget) {
            runnable.run();
        } else {
            if (launchingHome && !mIsWidget) {
                MSCExecutors.postOnUiThreadFrontOfQueue(runnable);
            } else {
                // onNewIntent先于onResume发生，且内有路由操作
                // 此处post以延后路由，用于保证前端要求的先onEnterForeground后onAppRoute
                // 启动首页时无此考虑，尽量前插至首次绘制之前
                MSCExecutors.postOnUiThread(runnable);
            }
        }
        IMSCContainer mscContainer = mController.getMSCContainer();
        boolean isPreCreate = mscContainer != null && mscContainer.isPreCreate();
        if (!isReportStartTime) {
            if (isPreCreate) {
                mscContainer.setWidgetPreCreateListener(new WidgetPreCreateListener() {
                    @Override
                    public void onWillAppear(long time) {
                        if (isReportStartTime) {
                            return;
                        }
                        // 更新容器启动路由跳转终点，修复TP10耗时数据负值异常 https://km.sankuai.com/collabpage/2710664918
                        setRouteTime(time);
                        MSCStartTimeReporter.report(openParams.url, openParams.openType, mRuntime, mController, (Activity) mContext, pageViewWrapper, time);
                        isReportStartTime = true;
                        pageViewWrapper.getRenderer().startFFPDetect();
                    }
                });
            } else {
                MSCStartTimeReporter.report(openParams.url, openParams.openType, mRuntime, mController, (Activity) mContext, pageViewWrapper, openParams.getRouteTime());
                isReportStartTime = true;
            }
        }
        // 设置 页面加载到达率/页面加载成功率 的起点时刻, 用于 pageDuration 字段上报
        pageReporter.setRenderPageStartTime(System.currentTimeMillis());
        Map<String, Object> tags = new HashMap<>();
        tags.put(CommonTags.TAG_MSC_APP_ID, mRuntime.getMSCAppModule().getAppId());
        tags.put(CommonTags.TAG_WIDGET, String.valueOf(mIsWidget));
        tags.put(CommonTags.TAG_PAGE_PATH, openParams.url);
        tags.put(CommonTags.TAG_PURE_PAGE_PATH, PathUtil.getPath(openParams.url));
        tags.put(UserReporter.TAG_OPEN_TYPE, openParams.openType);
        // pushPage & 秒开检测的起点
        UserReporter.create().reportUserPageStart(tags);
    }

    private void loadContent(final PageViewWrapper pageViewWrapper, final OpenParams openParams) {
        // FIXME: 兜底赋值，可能是并发问题，问题原因较难定位
        //页面退出成功率开启duration上报
        if (pageEventListenerWrapper != null) {
            pageEventListenerWrapper.setReferPageEventListener(mController.getPageEventListener());
            pageViewWrapper.getRenderer().setEventListener(pageEventListenerWrapper);
        } else {
            pageViewWrapper.getRenderer().setEventListener(mController.getPageEventListener());
        }

        pageViewWrapper.loadPage(openParams);

        Uri uri = Uri.parse("msc://www.meituan.com/" + openParams.url);
        String path = uri.getPath().substring(1);//屏蔽参数干扰
        if (!path.endsWith(".html")) {
            path += ".html";
        }
        String pathWithoutSuffix = path.substring(0, path.length() - 5);
        MSCLog.i(TAG, "Page file path :" + path);

        pageViewWrapper.initNavigationBar(pathWithoutSuffix);
        pageViewWrapper.setNavigationBarButtonClickListener(mOnNavigationBarButtonClickListener);

        //设置下拉刷新启用状态
        boolean enablePullRefresh = mRuntime.getMSCAppModule().isEnablePullDownRefresh(pathWithoutSuffix);
        pageViewWrapper.setRefreshEnable(enablePullRefresh);
        SwipeRefreshLayout refreshLayout = pageViewWrapper.getRefreshLayout();
        if (refreshLayout != null) {
            refreshLayout.setEnabled(enablePullRefresh);
            PullLoadingIconConfig pullLoadingIconConfig = getCurPageModule().getPullLoadingIconConfig();
            if (MSCHornRollbackConfig.enableCustomPullLoadingIcon() && pullLoadingIconConfig != null) {
                refreshLayout.setPullLoadingIconConfig(pullLoadingIconConfig, mRuntime.getFileModule());
            }
        }
    }

    /**
     * 获取当前的SwipeRefreshLayout，即当前显示的WebView所在的布局
     *
     * @return
     */
    public MultiLayerPage getSwipeRefreshLayout() {
        return mCurrentViewWrapper.getRefreshLayout();
    }

    public int getViewId() {
        return mCurrentViewWrapper != null ? mCurrentViewWrapper.getViewId() : 0;
    }

    // --------------------------  OnHorizontalSwipeListener  ---------------------------------

    @Override
    public void onHorizontalSwipeMove(float dx) {
        //页面拦截情况下页面不随手势而动
        if (mCurPageModule != null && !mCurPageModule.isEnableBackActionIntercept()) {
            this.scrollBy(-(int) dx, 0);
        }
    }

    @Override
    public void onSwipeTapUp(float x) {
        if (x < getWidth() / 2) {// 回到原位
            this.scrollTo(0, 0);
        } else {// 返回上一层级
            //此处禁用动画是避免页面随手势移动到一半，页面退出动画从屏幕边缘开始问题
            if (mCurPageModule != null && !mCurPageModule.isEnableBackActionIntercept()) {
                changeParentContainerAnimation(false);
            }
            //  this.scrollTo(getWidth(), 0);
//            mCurrentNavigationBar.onBack(getContext());
            ((Activity) mContext).onBackPressed();
        }
    }


    private void changeParentContainerAnimation(boolean enable) {
        ViewParent parent = this.getParent();
        if (parent instanceof PageTransitionContainer) {
            ((PageTransitionContainer) parent).changeAnimation(enable);
        }
    }
    // -------------------------- todo  remove UI component from this  ---------------------------------

    public View findViewByViewId(int viewId, int parentId) {
        BaseRenderer renderer = mCurPageModule.getRenderer();
        if (renderer == null) {
            return null;
        }
        if (renderer.needCoverLayer()) {
            CoverViewWrapper coverViewWrapper = findCoverViewWrapper(viewId, parentId);
            if (coverViewWrapper != null) {
                return ((CoverViewWrapper) coverViewWrapper).getContent();
            }
            return null;
        } else {
            return renderer.findViewById(viewId);
        }
    }

    public View findViewByMarkerKey(String markerKey) {
        BaseRenderer renderer = mCurPageModule.getRenderer();
        if (renderer == null) {
            return null;
        }
        return renderer.findViewByMarkerKey(markerKey);
    }

    public CoverViewWrapper findCoverViewWrapper(int viewId, int parentId) {
        MultiLayerPage refreshLayout = getSwipeRefreshLayout();
        if (refreshLayout == null) {
            return null;
        }
        synchronized (refreshLayout) {
            CoverViewWrapper viewWrapper = ViewFinder.findCoverViewWrapper(refreshLayout.getCoverViewContainer(), viewId);
            if (viewWrapper == null) {
                viewWrapper = ViewFinder.findCoverViewWrapper(refreshLayout.getUnderCoverViewContainer(), viewId);
            }
            if (viewWrapper == null) {
                return refreshLayout.findCoverViewWrapperInMarkerInfoWindowRootContainer(viewId, parentId);
            }
            return viewWrapper;
        }

    }

    /**
     * 标记webView组件实例
     */
    public boolean hasInnerWebViewModule() {
        return mCurrentViewWrapper.hasInnerWebViewModule();
    }

    public IPageLifecycleCallback getMSILifecycleCallback() {
        return mRuntime.apisManager.getApiPortal().getPageLifecycleCallback();
    }

    public IPageLifecycleCallback getNativeRenderLifecycleCallback() {
        if (isNativeRenderer() & mCurPageModule.getRenderer() != null) {
            return mCurPageModule.getRenderer().getPageLifecycleCallback();
        }
        return null;
    }

    /**
     * 如果页面可以back，如里面含有的webview可以back， 则返回true 否则false
     *
     * @return
     */
    public boolean tryToBack() {
        if (pageLifecycleInterceptor != null && pageLifecycleInterceptor.onInterceptorPageBack()) {
            return true;
        }
        if (getMSILifecycleCallback().onBackPressed(getViewId(), new LifecycleData())) {
            return true;
        }

        // TODO 怀疑此判断导致WebView不能响应back问题
        if (getScrollX() != 0) {//如果webview进入多了跳转之后 页面滑动的需要关闭的时候关闭页面，而不是后退
            return false;
        }
        if (hasInnerWebViewModule()) {
            View webView = mCurrentViewWrapper.getWebViewComponent();
            if (webView instanceof WebViewComponentWrapper) {
                return ((WebViewComponentWrapper) webView).tryGoBack();
            }
        }
//        TextArea.hideOkBar();
        return false;
    }

    public String getPagePath() {
        return mCurrentViewWrapper.getContentUrl();
    }


    public String getRoutePath() {
        return mCurPagePath;
    }

    /**
     * 关于布局上推，对整个web布局平移出去，用完后在再平移回来
     *
     * @param panOffset
     */
    public void adjustPan(int panOffset) {
        mCurrentViewWrapper.adjustPan(panOffset);
    }

    /**
     * @param code   1 或者-1 ，1说明找到焦点，如果非1 后面的参数无效
     * @param bottom 输入框底部到顶部的距离
     * @param top    输入框顶部到顶部的距离
     */
    @Override
    public void onElementFocused(int code, float bottom, float top) {
        if (code != 1) return;
        View viewWrapper = mCurrentViewWrapper.getWebViewComponent();
        if (viewWrapper == null) {
            MSCLog.e("webview not ready");
            return;
        }
        int navigationBarHeight = SystemInfoUtils.getSystemNavigationBarHeight(mContext);
        if (SystemInfoUtils.isHuawei()) {
            if (!SystemInfoUtils.isHuaweiNavigationBarShow(mContext)) {
                navigationBarHeight = 0;
            } else {
                navigationBarHeight = SystemInfoUtils.getHuaweiNavigationBarHeight(mContext);
            }
        }
        final WebViewComponentWrapper tmpWebView = (WebViewComponentWrapper) viewWrapper;
        int bottomScale = DisplayUtil.roundWithDevice(bottom);
        int heightWebView = tmpWebView.getWebHeight();
        int keyboardHeight = mCurrentViewWrapper.getKeyboardHeight();
        int deadline;
        if (MSCHornRollbackConfig.enableFixH5KeyboardCoverInput()) {
            deadline = heightWebView - keyboardHeight - navigationBarHeight;
        } else {
            deadline = heightWebView - keyboardHeight;
        }
        if (deadline > bottomScale) {
            return;
        }

        final int scrollOffset = bottomScale - deadline;
        FrameLayout content = ((Activity) mContext).findViewById(android.R.id.content);
        View mChildOfContent = content.getChildAt(0);
        FrameLayout.LayoutParams frameLayoutParams = (FrameLayout.LayoutParams) mChildOfContent.getLayoutParams();
        int resolvedHeight = mChildOfContent.getHeight();

        int heightDifference;
        if (MSCHornRollbackConfig.enableFixH5KeyboardCoverInput()) {
            heightDifference = resolvedHeight + keyboardHeightInLayout - keyboardHeight;//- keyboardHeight-DisplayUtil.fromDPToPix(50);
        } else {
            heightDifference = resolvedHeight + keyboardHeightInLayout - keyboardHeight + navigationBarHeight;//- keyboardHeight-DisplayUtil.fromDPToPix(50);
        }
        keyboardHeightInLayout = keyboardHeight;
        frameLayoutParams.height = heightDifference;    // 更新Page内容区域，以避让弹起的键盘
        mChildOfContent.requestLayout();    //TODO 操作可能是别人的view，不合适吧

        if (scrollOffset > navigationBarHeight) {
            final int finalNavigationBarHeight = navigationBarHeight;
            tmpWebView.postDelayed(new Runnable() {
                @Override
                public void run() {
                    tmpWebView.scrollBy(0, scrollOffset - finalNavigationBarHeight);
                    tmpWebView.invalidate();
                }
            }, 100);
            //  tmpWebView.scrollBy(0, scrollOffset - navigationBarHeight);
        }
    }

    public boolean isWebViewRenderer() {
        return DisplayUtil.isWebViewRenderer(mCurPageModule);
    }

    public boolean isNativeRenderer() {
        return DisplayUtil.isNativeRenderer(mCurPageModule);
    }

    public ITabPage getTabPage() {
        return mTabPage;
    }

    private void reportWhiteScreenCancelCheckReason(String reason, boolean isStartPageAdvanced) {
        WhiteScreenUtil.reportWhiteScreenCancelReason(reason, false, isStartPageAdvanced);
    }

    /**
     * 检测白屏Runnable
     */
    private class CheckWhiteScreenRunnable implements Runnable {

        private WeakReference<PageViewWrapper> pageViewWrapperWeakReference;
        private HashMap<String, Object> renderProcessGoneInfo;

        private final boolean isStartPageAdvanced;

        public CheckWhiteScreenRunnable(PageViewWrapper view, HashMap<String, Object> renderProcessGoneInfo, boolean isStartPageAdvanced) {
            this.pageViewWrapperWeakReference = new WeakReference<>(view);
            this.renderProcessGoneInfo = renderProcessGoneInfo;
            this.isStartPageAdvanced = isStartPageAdvanced;

            // 记录用户视角白屏检测的开始时间
            if (mCurrentViewWrapper != null && mCurrentViewWrapper.getRenderer() != null
                    && mCurrentViewWrapper.getRenderer() instanceof MSCWebViewRenderer) {
                ((MSCWebViewRenderer) mCurrentViewWrapper.getRenderer()).recordWhiteScreenUserPerspectiveDurationStart();
            }
        }

        @Override
        public void run() {
            if (pageViewWrapperWeakReference == null) {
                reportWhiteScreenCancelCheckReason("pageViewWrapper is null", isStartPageAdvanced);
                return;
            }
            PageViewWrapper view = pageViewWrapperWeakReference.get();
            if (view == null) {
                reportWhiteScreenCancelCheckReason("view is null", isStartPageAdvanced);
                return;
            }

            boolean isVisible = (isShow && (view == mCurrentViewWrapper));
            View detectView = view;
            boolean isInnerWebView = false;
            View innerWebivew = null;
            if (view.webViewModuleRef != null && (innerWebivew = view.webViewModuleRef.get()) != null && innerWebivew.isAttachedToWindow()) {
                detectView = innerWebivew;
                isInnerWebView = true;
            }
            if (detectView.isAttachedToWindow() && detectView.isShown()) {
                boolean isContainerShow = true;
                int displayType = WhiteScreenUtil.DISPLAY_TYPE_NORMAL;
                if (MSCHornRollbackConfig.readConfig().enableFixWhiteScreenCheckStrategy) {
                    displayType = WhiteScreenUtil.getCurrViewDisplayType(detectView);
                    isContainerShow = displayType == WhiteScreenUtil.DISPLAY_TYPE_NORMAL;
                }
                // 白屏检测策略调整 https://km.sankuai.com/collabpage/2628235980
                if (!isContainerShow) {
                    MSCLog.i(TAG, "container is invalid displayed, CANCEL WHITE SCREEN CHECK", displayType, mController.getMSCContainer().getMPAppId(), getRoutePath(), getPagePath());
                    reportWhiteScreenCancelCheckReason("container is invalid displayed, type:" + displayType, isStartPageAdvanced);
                } else {
                    String url = "";
                    if (detectView instanceof WebViewComponentWrapper) {
                        url = ((WebViewComponentWrapper) detectView).getUrl();
                    }
                    view.getRenderer().checkWhiteScreenAndReport(
                            detectView,
                            isVisible,
                            isInnerWebView,
                            url,
                            renderProcessGoneInfo,
                            isStartPageAdvanced
                    );
                }
            } else {
                MSCLog.i(TAG, "detectView is not show");
                reportWhiteScreenCancelCheckReason("not attached or not show", isStartPageAdvanced);
            }
            mRuntime.jsErrorRecorder.stop();
        }
    }

    @Override
    public void setRouteTime(long routeTime) {
        super.setRouteTime(routeTime);
        if (pageReporter != null) {
            if (MSCHornPerfConfig.getInstance().enableFPUsePageStartTime()) {
                pageReporter.setPageStartTime(routeTime);
            }
            pageReporter.setRouteTime(routeTime);
        }
    }

    public void setRouteId(long routeId) {
        if (pageReporter != null) {
            pageReporter.setRouteId(routeId);
        }
    }

    /**
     * 判断要加载的目标页面是否存在，如不存在，展示pageNotFound兜底页面
     */
    public void showPageNotFoundView() {
        if (mCurrentViewWrapper == null) return;
        mCurrentViewWrapper.showPageNotFoundView(mCurPagePath);
        mCurrentViewWrapper.setNavigationBarButtonClickListener(mOnNavigationBarButtonClickListener);
    }

    // fixme msc 导航相关挪走吧
    CustomNavigationBar.OnNavigationBarButtonClickListener mOnNavigationBarButtonClickListener = new CustomNavigationBar.OnNavigationBarButtonClickListener() {

        @Override
        public void clickShare() {

            JSONObject object = new JSONObject();
            try {
                // onShareAppMessage 入参 对齐  https://km.sankuai.com/page/830551769
                if (mCurrentViewWrapper != null && mCurrentViewWrapper.webViewModuleRef != null) {
                    View webView = mCurrentViewWrapper.webViewModuleRef.get();
                    if (webView instanceof WebViewComponentWrapper) {
                        String url = ((WebViewComponentWrapper) webView).getUrl();
                        object.put("webViewUrl", url);
                    }
                }
                object.put("from", "menu");
            } catch (JSONException e) {
                e.printStackTrace();
            }
            mRuntime.getJSModuleDelegate(PageListener.class).onShare(object, getViewId());
        }

        @Override
        public void clickBack() {
            ((Activity) mContext).onBackPressed();
        }

        @Override
        public void clickTitleBar() {
            if (mCurPageModule.getRenderer() != null) {
                long now = SystemClock.elapsedRealtime();
                if (now - mLastClickTitleTime <= ViewConfiguration.getDoubleTapTimeout()) {
                    //点击NavigationBar title以及其他空白区域，发送回到顶端的事件给渲染层
                    mCurPageModule.getRenderer().onUserTapBackToTop();
                }
                mLastClickTitleTime = now;
            }
            //点击NavigationBar title以及其他空白区域5次，会弹出版本信息
            if (!MSCEnvHelper.getEnvInfo().isProdEnv()) {
                if (++mClickTitleCount > 4) {
                    mClickTitleCount = 0;
                    ToastUtils.toastWithDuration("小程序版本号:" + mRuntime.getMSCAppModule().getPublishId() +
                            "\n 基础库版本号：" + mRuntime.getMSCAppModule().getBasePkgVersion(), ToastUtils.LENGTH_LONG);
                }
            }
        }
    };


    private void restartPerfLogSession() {
        PerfEventRecorder perfEventRecorder = new PerfEventRecorder(true, true);
        PerfTrace.instant(PerfEventConstant.PERF_SESSION_START);
        if (mCurrentViewWrapper != null && mCurrentViewWrapper.getRenderer() != null)
            mCurrentViewWrapper.getRenderer().setPerfEventRecorder(perfEventRecorder);
    }

    public void reportPageExit() {
        mRuntime.setIsExit(true);
        if (pageReporter != null) {
            pageReporter.onPageExit();
        } else {
            MSCLog.i(TAG, "reportPageExit but pageReporter is null");
        }
    }

    @Override
    public String toString() {
        return super.toString() + ", PageId: " + getViewId();
    }
}
