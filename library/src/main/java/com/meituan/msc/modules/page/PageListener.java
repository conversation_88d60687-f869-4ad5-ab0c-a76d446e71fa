package com.meituan.msc.modules.page;

import com.meituan.msc.jse.bridge.JavaScriptModule;
import com.meituan.msc.jse.bridge.LogMethodInvokeModule;

/**
 * Created by letty on 2022/9/14.
 **/
public interface PageListener extends JavaScriptModule, LogMethodInvokeModule {

    void onTabItemTap(Object params, int pageId);

    void onShare(Object params, int pageId);

    /**
     * 折叠屏适配
     * @param params
     * @param pageId
     */
    void onResize(Object params,int pageId);

    /**
     * 数据预拉取事件
     * @param params
     * @param pageId
     */
    void onPrefetchData(Object params, int pageId);
}