package com.meituan.msc.modules.page;

import android.app.Activity;
import android.content.DialogInterface;
import android.content.Intent;
import android.support.annotation.MainThread;
import android.support.annotation.NonNull;
import android.support.annotation.Nullable;
import android.support.annotation.VisibleForTesting;
import android.text.TextUtils;
import android.view.View;
import android.view.ViewTreeObserver;
import android.widget.FrameLayout;

import com.meituan.msc.common.aov_task.context.ITaskExecuteContext;
import com.meituan.msc.common.aov_task.task.ITask;
import com.meituan.msc.common.config.MSCConfig;
import com.meituan.msc.common.config.SceneNumber;
import com.meituan.msc.common.executor.MSCExecutors;
import com.meituan.msc.common.framework.interfaces.PageEventListener;
import com.meituan.msc.common.perf.PerfEventConstant;
import com.meituan.msc.common.report.MetricsEntry;
import com.meituan.msc.common.support.java.util.concurrent.CompletableFuture;
import com.meituan.msc.common.utils.PathUtil;
import com.meituan.msc.common.utils.ToastUtils;
import com.meituan.msc.common.utils.UIUtil;
import com.meituan.msc.common.utils.VersionUtil;
import com.meituan.msc.extern.MSCEnvHelper;
import com.meituan.msc.lib.R;
import com.meituan.msc.lib.interfaces.container.MSCParams;
import com.meituan.msc.modules.api.ApiException;
import com.meituan.msc.modules.api.msi.MSCErrorCode;
import com.meituan.msc.modules.api.msi.navigation.BizNavigationExtraParams;
import com.meituan.msc.modules.apploader.IAppLoader;
import com.meituan.msc.modules.apploader.LaunchTaskManager;
import com.meituan.msc.modules.apploader.launchtasks.StartPageTask;
import com.meituan.msc.modules.container.ContainerController;
import com.meituan.msc.modules.container.ContainerReporter;
import com.meituan.msc.modules.container.IContainerDelegate;
import com.meituan.msc.modules.container.OpenParams;
import com.meituan.msc.modules.container.fusion.IFusionPageManager;
import com.meituan.msc.modules.container.fusion.MSCFusionActivityMonitor;
import com.meituan.msc.modules.devtools.DebugHelper;
import com.meituan.msc.modules.engine.MSCHornRollbackConfig;
import com.meituan.msc.modules.engine.MSCRuntime;
import com.meituan.msc.modules.engine.dataprefetch.IDataPrefetchModule;
import com.meituan.msc.modules.engine.requestPrefetch.RequestPrefetchManager;
import com.meituan.msc.modules.page.reload.PageInfoArray;
import com.meituan.msc.modules.page.reload.PageStackWatchDog;
import com.meituan.msc.modules.page.render.AppPageReporter;
import com.meituan.msc.modules.page.render.AppRouteParam;
import com.meituan.msc.modules.page.transition.PageTransitionContainer;
import com.meituan.msc.modules.page.view.coverview.IPageLifecycleInterceptor;
import com.meituan.msc.modules.page.widget.LoadingDialog;
import com.meituan.msc.modules.reporter.CommonTags;
import com.meituan.msc.modules.reporter.CrashReporterHelper;
import com.meituan.msc.modules.reporter.MSCLog;
import com.meituan.msc.modules.reporter.ReporterFields;
import com.meituan.msc.util.perf.PerfTrace;

import java.util.ArrayList;
import java.util.List;
import java.util.Map;
import java.util.Stack;
import java.util.concurrent.ConcurrentHashMap;

/**
 * {@link Page}管理类
 */
public class PageManager {
    //do not change
    public static final String CLASS_NAME = "PageManager";
    private final String TAG = "PageManager@" + Integer.toHexString(hashCode());

    private static final int MAX_COUNT = 10;
    public static final int NO_PAGE_ID = -1;

    protected final MSCRuntime mRuntime;
    protected final IRuntimeGetter mRuntimeGetter;
    protected final IContainerDelegate mController;
    protected final PageEventListener mEventListener;
    protected final Activity mActivity;
    protected final PageTransitionContainer mPageContainer;
    protected boolean mShowPageNotFoundPending;
    protected PageStackWatchDog.IPageStackLoader pageStackLoader;
    protected IFusionPageManager mFusionPageManager = MSCEnvHelper.getFusionPageManager();
    public volatile BasePage preCreatePage;
    public volatile boolean hasCreatedPage;
    private LoadingDialog loadingDialog;
    private volatile Runnable delayLoadingRunnable;
    private volatile boolean alreadyCreatePage;
    private volatile boolean mIsLaunch;
    //存放内部页面跳转，还未执行完任务的跳转信息。页面退出时，统计页面退出成功率
    private Map<String, PageRouteInfo> innerPageRouteInfoMap = new ConcurrentHashMap<>();

    public boolean isNeedRemoveTopPage() {
        return needRemoveTopPage;
    }

    private boolean needRemoveTopPage;

    public PageManager(IContainerDelegate controller, MSCRuntime runtime, IRuntimeGetter runtimeGetter) {
        mController = controller;
        mActivity = mController.getActivity();
        mRuntime = runtime;
        mRuntimeGetter = runtimeGetter;
        mPageContainer = new PageTransitionContainer(mActivity).setAttachListener(new PageTransitionContainer.OnAttachListener() {
            final ViewTreeObserver.OnGlobalLayoutListener mOnGlobalLayoutListener = new ViewTreeObserver.OnGlobalLayoutListener() {
                private int lastHeight = 0;
                private int lastWidth = 0;

                @Override
                public void onGlobalLayout() {
                    int nowHeight = mPageContainer.getHeight();
                    int nowWidth = mPageContainer.getWidth();

                    Page topPage = getTopPage();
                    if (lastHeight != 0 && lastWidth != 0 && topPage != null) {    // 排除首次布局
                        if (lastHeight != nowHeight || lastWidth != nowWidth) {
                            topPage.onSizeChanged();
                        }

                        if (lastHeight + 100 < nowHeight) { //键盘收起的时候 把topPage 里面的webView 重新布局
                            topPage.requestWebLayout();
                        }
                    }
                    lastHeight = nowHeight;
                    lastWidth = nowWidth;
                }
            };

            @Override
            public void onDetachedFromWindow() {
                mPageContainer.getViewTreeObserver().removeOnGlobalLayoutListener(mOnGlobalLayoutListener);

            }

            @Override
            public void onAttachedFromWindow() {
                /**
                 * 监听容器高度改变，触发重新布局，调用时要求mPageContainer已attach
                 */
                mPageContainer.getViewTreeObserver().addOnGlobalLayoutListener(mOnGlobalLayoutListener);
            }
        });
        mEventListener = mController.getPageEventListener();
        pageStackLoader = new PageStackWatchDog.IPageStackLoader() {
            @Override
            public void reloadTopOfStack(PageInfoArray pageInfoArray, long routeTime, @NonNull BizNavigationExtraParams bizNavigationExtraParams) {
                PageManager.this.reloadTopOfStack(pageInfoArray, routeTime, bizNavigationExtraParams);
            }
        };
    }

    private MSCRuntime getRuntime() {
        if (mRuntime != null) {
            return mRuntime;
        }
        if (mRuntimeGetter != null) {
            return mRuntimeGetter.getRuntime();
        }
        MSCLog.e(TAG, "runtime use case is too early!");
        return null;
    }

    /**
     * Page destroy 应该在哪些场景调用；
     * 1、Activity/Fragment 销毁
     * 2、页面栈模式下，页面实际退出;
     * (TODO 6.9： 目前Fragment场景下 不支持页面栈，因此暂时未处理，widget需求记得处理完)
     */
    @MainThread
    public void onDestroy() {
        for (int i = 0; i < getPageCount(); i++) {
            View view = mPageContainer.getChildAt(i);
            if (view instanceof BasePage) {
                ((BasePage) view).onDestroy();
            }
        }

        if (preCreatePage != null) {
            preCreatePage.onDestroy();
            preCreatePage = null;
        }
        int runningTaskCount = innerPageRouteInfoMap.size();
        if (runningTaskCount > 0) {
            for (PageRouteInfo pageRouteInfo : innerPageRouteInfoMap.values()) {
                reportPageExitSuccessRate(pageRouteInfo);
            }
        }
        MSCLog.i(TAG, "Inner Route Running Tasks Count: " + runningTaskCount);
    }

    //内部页面跳转，如果还有未执行完成的任务，此时场景对应未创建Page，page不会上报页面退出成功率。
    //此处补充这个场景的页面退出成功率上报
    private void reportPageExitSuccessRate(PageRouteInfo pageRouteInfo) {
        if (pageRouteInfo == null) {
            return;
        }

        if (mRuntime != null) {
            MetricsEntry metricsEntry = mRuntime.getRuntimeReporter().record(ReporterFields.REPORT_PAGE_EXIT_SUCCESS_RATE);
            metricsEntry.tag("errorCode", "7003");
            metricsEntry.tag("clientReadyDuration", System.currentTimeMillis() - pageRouteInfo.routeTime);
            metricsEntry.tag(CommonTags.TAG_PAGE_PATH, pageRouteInfo.path);
            metricsEntry.tag(CommonTags.TAG_PURE_PAGE_PATH, PathUtil.getPath(pageRouteInfo.path));
            metricsEntry.tag(ReporterFields.WIDGET, mController != null ? mController.isWidget() : false);
            metricsEntry.tag(ReporterFields.LAUNCH_START, pageRouteInfo.routeTime);
            metricsEntry.tag(ReporterFields.EXIT_TIME, System.currentTimeMillis());
            metricsEntry.tag(ReporterFields.PAGE_STAY_TIME, System.currentTimeMillis() - pageRouteInfo.routeTime);
            AppPageReporter.appendDDDLoadPackageDetails(mRuntime, pageRouteInfo.routeId, metricsEntry);
            AppPageReporter.appendLaunchMoment(mRuntime, pageRouteInfo.routeId, metricsEntry.getTags());
            metricsEntry.value(0).sendRealTime();

            if (MSCHornRollbackConfig.enableReportTimeoutTaskWhenPageExit()) {
                mRuntime.getRuntimeReporter().reportTaskTimeoutWhenPageExit(pageRouteInfo.path);
            }
        }
    }

    private String getTopPagePath() {
        IPageModule pageModule = getTopPageModule();
        return pageModule != null ? pageModule.getPagePath() : null;
    }

    void reloadTopOfStack(PageInfoArray pageInfoArray, long routeTime, @NonNull BizNavigationExtraParams bizNavigationExtraParams) {
        String path = pageInfoArray.currentPath;
        requestPagePrefetchWhenNavigate(path);
        OpenParams openParams = new OpenParams()
                .setOpenType(OpenParams.RELOAD)
                .setRouteTime(routeTime);
        try {
            openParams = new OpenParams.Builder()
                    .setUrl(path)
                    .setOpenType(OpenParams.RELOAD)
                    .setRouteTime(routeTime)
                    .build(getRuntime());
        } catch (ApiException e) {
            MSCLog.e(TAG, e, "reloadTopOfStack");
            ToastUtils.toast("页面跳转异常");
        }
        OpenParams finalOpenParams = openParams;
        finalOpenParams.setRouteId(finalOpenParams.hashCode());
        Runnable startPageRunnable = new Runnable() {
            @Override
            public void run() {
                Page topPage = getTopPage();
                final Page page = showPage(finalOpenParams, pageInfoArray, null);
                page.onReloadTo(finalOpenParams);
                dataPrefetchAttachToPage(page, finalOpenParams.getRouteId(), page.getViewId());
                // 页面恢复完整 弹出旧页面
                if (topPage != null) {
                    page.onNavigateBackTo(routeTime);
                    mPageContainer.removeView(topPage);
                    MSCLog.i(TAG, "remove page when reloadTopOfStack, page:", topPage, ", PageContainer:", mPageContainer, ", PageManager:", this, ", PageCount:", mPageContainer.getChildCount());
                }
            }
        };
        if (MSCHornRollbackConfig.isRollbackSetRouteMappingPersist()) {
            launchPageByRoute(path, startPageRunnable, false, finalOpenParams.getRouteId(), routeTime, bizNavigationExtraParams);
        } else {
            launchPageByRoute(openParams.url, startPageRunnable, false, finalOpenParams.getRouteId(), routeTime, bizNavigationExtraParams);
        }
    }

    /**
     * dismissLoading  不需要等待firstRender，只等待包加载；
     * 启动流程也不应等待任一页面 firstRender结束；非串行场景下会导致后续页面无法打开
     */
    public void launchPageByRoute(String path, Runnable startPageRunnable, boolean external, int routeId, long routeTime, @NonNull BizNavigationExtraParams bizNavigationExtraParams) {
        MSCLog.i("MSCDynamicDataPrefetch", " launchPageByRoute " + routeId);
        Runnable launchPageRunnable = startPageRunnable;
        if (MSCHornRollbackConfig.enablePageExitFixMissSceneAndDuration()) {
            PageRouteInfo pageRouteInfo = new PageRouteInfo();
            pageRouteInfo.path = path;
            pageRouteInfo.routeTime = routeTime;
            pageRouteInfo.routeId = routeId;

            Runnable runnableWrapper = new Runnable() {
                @Override
                public void run() {
                    startPageRunnable.run();
                    innerPageRouteInfoMap.remove(String.valueOf(routeId));
                }
            };
            innerPageRouteInfoMap.put(String.valueOf(routeId), pageRouteInfo);
            launchPageRunnable = runnableWrapper;
        }

        showLoading(path);
        IAppLoader appLoader = getRuntime().getModule(IAppLoader.class);
        appLoader.launchPage(path, new StartPageTaskOfRoute(LaunchTaskManager.ITaskName.START_PAGE_BY_ROUTE_TASK, mController,
                this, launchPageRunnable), true, external, routeId, routeTime, false, mController.isIgnoreRouteMapping(external), false, bizNavigationExtraParams);
    }

    private void dataPrefetchAttachToPage(Page page, int routeId, int pageId) {
        if (page == null || page.isTabPage()) {
            return;
        }
        MSCLog.i("MSCDynamicDataPrefetch", " dataPrefetchAttachToPage routeId " + routeId);
        IDataPrefetchModule dataPrefetchModule = getRuntime().getModule(IDataPrefetchModule.class);
        if (dataPrefetchModule != null) {
            dataPrefetchModule.attachToPage(routeId, pageId);
        }

        page.setRouteId(routeId);
    }

    public static class StartPageTaskOfRoute extends StartPageTask {
        private final String TAG = "StartPageTaskOfRoute@" + Integer.toHexString(hashCode());

        private PageManager pageManager;
        private Runnable startPageRunnable;

        public StartPageTaskOfRoute(String name, IContainerDelegate containerController,
                                    PageManager pageManager, Runnable startPageRunnable) {
            super(name, containerController);
            this.pageManager = pageManager;
            this.startPageRunnable = startPageRunnable;
        }

        @Override
        protected CompletableFuture<AppRouteParam> doExecuteTaskAsync(@NonNull IContainerDelegate controllerDelegate,
                                                                      ITaskExecuteContext executeContext) {
            if (startPageRunnable == null) {
                MSCLog.i(TAG, "launchPageByRoute cancel,startPageRunnable is null");
                return CompletableFuture.completedFuture(null);
            }
            if (pageManager == null) {
                MSCLog.i(TAG, "launchPageByRoute cancel,mPageContainer is null");
                return CompletableFuture.completedFuture(null);
            }
            MSCExecutors.runOnUiThread(new Runnable() {
                @Override
                public void run() {
                    MSCLog.i(TAG, "StartPageTaskOfRoute doExecuteTaskAsync at UI Thread");
                    if (pageManager != null) {
                        pageManager.dismissLoading();
                    }
                    if (startPageRunnable != null) {
                        startPageRunnable.run();
                    }
                }
            });
            return CompletableFuture.completedFuture(null);
        }

        public void releaseResource() {
            pageManager = null;
            startPageRunnable = null;
        }

        public PageManager getPageManager() {
            return pageManager;
        }
    }

    /**
     * 容器退出时手动释放启动页面任务持有的引用，避免资源泄漏
     */
    public void releaseResourceOfStartPageTask() {
        MSCRuntime runtime = getRuntime();
        if (null == runtime) {
            MSCLog.i(TAG, "releaseResourceOfStartPageTask mRuntime null");
            return;
        }
        IAppLoader appLoader = runtime.getModule(IAppLoader.class);
        if (appLoader == null) {
            MSCLog.i(TAG, "releaseResourceOfStartPageTask appLoader null");
            return;
        }
        List<ITask<AppRouteParam>> startPageTasks = appLoader.findLaunchTasksByClass(StartPageTaskOfRoute.class);
        if (startPageTasks == null) {
            MSCLog.i(TAG, "releaseResourceOfStartPageTask startPageTasks null");
            return;
        }
        for (ITask<?> task : startPageTasks) {
            if (!(task instanceof PageManager.StartPageTaskOfRoute)) {
                continue;
            }
            PageManager.StartPageTaskOfRoute startPageTaskOfRoute = (PageManager.StartPageTaskOfRoute) task;
            PageManager pageManager = startPageTaskOfRoute.getPageManager();
            if (this == pageManager) {
                MSCLog.i(TAG, "releaseResourceOfStartPageTask", task);
                startPageTaskOfRoute.releaseResource();
            }
        }
    }

    /**
     * 获取页面的容器
     *
     * @return 页面容器
     */
    public FrameLayout getPageContainer() {
        return mPageContainer;
    }

    /**
     * 获取顶部页面（即当前显示页面）的id
     *
     * @return 当前显示页面的id
     */
    public int getTopPageId() {
        Page topPage = getTopPage();
        return topPage != null ? topPage.getViewId() : 0;
    }

    /**
     * 返回页面
     */
    public boolean handleBackPress(PageStackWatchDog pageStackWatchDog, long routeTime) {
        if (getPageCount() <= 0) {
            return false;
        }
        Page topPage = getTopPage();
        if (topPage != null && topPage.tryToBack()) {
            return true;
        }
        // 如果是首页，就不能会退了
        if (getPageCount() == 1) {
            // 处理恢复逻辑如果需要的话
            return handleBackForPageRestore(pageStackWatchDog, routeTime, topPage != null ? topPage.bizNavigationExtraParams : new BizNavigationExtraParams.Builder().build());
        }
        // 非首页 走下面的逻辑
        boolean ret = navigateBackPage(1, false, routeTime);
        if (ret) {//回退页面后恢复页面状态
            Page top = getTopPage();
            if (top != null) {
                top.onShow(OpenParams.NAVIGATE_BACK);
            }
        }
        return ret;
    }

    /**
     * 处理页面栈恢复
     *
     * @param pageStackWatchDog PageStackWatchDog
     * @param routeTime
     * @return 如果reload完成返回true 否则返回false
     */
    private boolean handleBackForPageRestore(@Nullable PageStackWatchDog pageStackWatchDog, long routeTime, @NonNull BizNavigationExtraParams bizNavigationExtraParams) {
        if (pageStackWatchDog != null && pageStackWatchDog.canReLoad()) {
            pageStackWatchDog.reloadTopOfStack(pageStackLoader, routeTime, bizNavigationExtraParams);
            return true;
        }
        return false;
    }

    boolean hasPage(int id) {
        return getPageModuleById(id) != null;
    }

    IPageModule getPageModuleById(int id) {
        if (id == NO_PAGE_ID) {
            MSCLog.w("getPageModuleById: id is invalid", id);
            return null;
        }
        int count = getPageCount();
        for (int i = 0; i < count; i++) {
            BasePage page = (BasePage) mPageContainer.getChildAt(i);
            if (page == null) {
                MSCLog.i(TAG, "getPageModuleById, page is null getChildAt", i);
                continue;
            }
            IPageModule pageModule = page.getPageModuleById(id);
            if (pageModule != null) {
                return pageModule;
            }
        }
        MSCLog.w("getPageModuleById: cannot find page by id", id, ", PageContainer:", mPageContainer, ", PageManager:", this, ", PageCount:", mPageContainer.getChildCount());
        return null;
    }

    // private String dumpPageStack() {
    //     StringBuilder stringBuilder = new StringBuilder();
    //     int count = getPageCount();
    //     for (int i = 0; i < count; i++) {
    //         BasePage page = (BasePage) mPageContainer.getChildAt(i);
    //         stringBuilder.append(page);
    //         stringBuilder.append(",");
    //     }
    //     return stringBuilder.toString();
    // }

    public IPageModule getPageModuleOrThrow() throws ApiException {
        return getPageModuleOrThrow(NO_PAGE_ID);
    }

    @NonNull
    public IPageModule getPageModuleOrThrow(int pageId) throws ApiException {
        IPageModule page;
        if (pageId == NO_PAGE_ID) {
            page = getTopPageModule();
        } else {
            page = getPageModuleById(pageId);
        }
        if (page == null) {
            throw new ApiException("no page available");
        }
        return page;
    }

    IPageModule getTopPageModule() {
        Page topPage = getTopPage();
        if (topPage != null) {
            return topPage.getCurPageModule();
        }
        return null;
    }

    IPageModule getBottomPageModule() {
        Page bottomPage = getBottomPage();
        if (bottomPage != null) {
            return bottomPage.getCurPageModule();
        }
        return null;
    }

    /**
     * 获取当前页面数
     *
     * @return 当前页面数
     */
    public int getPageCount() {
        return mPageContainer.getChildCount();
    }

    /**
     * 获取栈底的页面
     *
     * @return 当前显示的页面
     */
    @VisibleForTesting
    Page getBottomPage() {
        int count = getPageCount();
        if (count <= 0) {
            MSCLog.w(TAG, "container have no pages");
            return null;
        }
        return getPageFromView(mPageContainer.getChildAt(0));
    }

    /**
     * 获取顶部的页面，即当前显示的页面
     *
     * @return 当前显示的页面
     */
    public Page getTopPage() {
        int count = getPageCount();
        if (count <= 0) {
            MSCLog.w(TAG, "container have no pages");
            return null;
        }
        return getPageFromView(mPageContainer.getChildAt(count - 1));
    }

    private Page getPageFromView(View view) {
        if (view instanceof TabPage) {
            return ((TabPage) view).getCurPage();
        } else if (view instanceof Page) {
            return (Page) view;
        }
        return null;
    }

    public void onResume(String openType) {
        Page page = getTopPage();
        if (page != null) {
            page.onShow(openType);
            getRuntime().jsErrorRecorder.popToPage(page.getPagePath(), String.valueOf(page.getId()));
        }
    }

    public void onPause(int cause) {
        Page page = getTopPage();
        if (page != null) {
            page.onHide(cause);
        }
    }

    @Nullable
    public Page getPageByViewId(int id) {
        int count = getPageCount();
        for (int i = count - 1; i >= 0; i--) {
            BasePage page = (BasePage) mPageContainer.getChildAt(i);
            if (page == null) {
                MSCLog.i(TAG, "getPageByViewId, page is null getChildAt", i);
                continue;
            }
            if (page instanceof TabPage) {
                Page target = ((TabPage) page).getPageById(id);
                if (target != null) {
                    return target;
                }
            } else if (page.getViewId() == id) {
                return (Page) page;
            }
        }
        return null;
    }

    protected boolean removeAllPages() {
        removePages(getPageCount());
        // fixme msc remove this
        mController.clearRestorePageStack();
        return true;
    }

    /**
     * 移除页面，最多把所有页面移除
     */
    private boolean removePages(int delta) {
        int pageCount = getPageCount();
        MSCLog.i(TAG, "removePage", pageCount, delta);
        delta = Math.min(pageCount, delta);
        int removeUntilIndex = pageCount - delta;
        // 批量移除Page时按添加的逆顺序移除，AppPage复用对此有要求，应保留最靠近移除后所到页面的部分
        for (int i = pageCount - 1; i >= removeUntilIndex; i--) {
            removePage(i);
        }
        return true;
    }

    public void removePage(int index) {
        BasePage page = (BasePage) mPageContainer.getChildAt(index);
        page.onHide(IPageLifecycleInterceptor.TYPE_PAGE_PAUSE_CAUSE_NAVIGATE_POP);
        MSCLog.i(TAG, "remove page: BasePage@", Integer.toHexString(page.hashCode()),
                ", PageContainer@", Integer.toHexString(mPageContainer.hashCode()),
                ", PageManager@", Integer.toHexString(hashCode()), ", PageCount:", mPageContainer.getChildCount());
        mPageContainer.removeViewAt(index);
        getRuntime().jsErrorRecorder.popPage(page.getRoutePath(), String.valueOf(page.getViewId()));
    }

    /**
     * 自顶部向下移除delta个页面，该方法可移除N-1个页面，如果未移除完成，调用方可根据返回值确认后续行为
     * 多级页面返回的场景下，在非仅剩首页的场景下，__mtAllowCloseContainer 为false 将直接认为结束。。。这个逻辑有点绕。。
     *
     * @param delta 移除的页面数，小于等于0为后退一级，大于当前页面数为退至首页，参照微信逻辑
     * @return 是否已完成页面移除，否的话表示还需要移除首页
     */
    private boolean removePagesWithoutFirstPage(int delta, boolean allowCloseContainer) {
        int pageCount = getPageCount();
        MSCLog.i(TAG, "navigateBackPage delta:", delta, " allowCloseContainer:", allowCloseContainer, "pageCount:", pageCount);
        if (pageCount <= 1) {
            mPageContainer.disableAnimation();
            return false;
        }
        boolean result = true;
        if (delta >= pageCount) {
            delta = pageCount - 1;
            if (allowCloseContainer) {
                result = false;
            }
        }
        if (delta <= 0) {
            delta = 1;
        }
        removePages(delta);
        return result;
    }

    private boolean ensurePageCount() {
        // 融合模式暂时不做页面个数限制，三方小程序限制页面栈深度
        if (MSCHornRollbackConfig.enableExternalAppPageDepthLimit()) {
            MSCRuntime mRuntime = getRuntime();
            if (mRuntime != null) {
                boolean isExternalApp = mRuntime.getMSCAppModule().getExternalApp();
                int pageCount = getPageCount();
                if (isExternalApp) {
                    return pageCount < MAX_COUNT;
                }
            }
        }
        return true;
    }

    private Page showPage(OpenParams openParams, @Nullable PageInfoArray pageInfoArray, Boolean isFirstPageV2) {
        return showPage(openParams, pageInfoArray, null, isFirstPageV2);
    }

    /**
     * 创建并添加一个页面
     *
     * @param pageInfoArray 原始页面id，用于页面恢复；默认值为View.NO_ID
     * @return 新创建的页面
     */
    private Page showPage(OpenParams openParams, @Nullable PageInfoArray pageInfoArray, Boolean isHomePage, Boolean isFirstPageV2) {
        boolean isTab = isTab(openParams);
        showPageAnimation(isTab);
        return showPageInner(openParams, pageInfoArray, isHomePage, isFirstPageV2);
    }

    public boolean isTab(OpenParams openParams) {
        return openParams.isTab && (!mController.isWidget() || DebugHelper.navigateInWidget);
    }

    /**
     * 控制showPage的动画效果（如果外部有控制动画效果，建议使用showPageInner)
     *
     * @param isTab
     */
    private void showPageAnimation(boolean isTab) {
        if (isTab) {
            mPageContainer.disableAnimation();
        } else {
            int pageCount = getPageCount();
            if (pageCount == 0) {
                mPageContainer.disableAnimation();
            } else {
                mPageContainer.enableAnimation();
            }
        }
    }

    /**
     * showPage真正添加页面的逻辑
     *
     * @param openParams
     * @param pageInfoArray
     * @param isHomePage
     * @return
     */
    private Page showPageInner(OpenParams openParams, @Nullable PageInfoArray pageInfoArray, Boolean isHomePage, Boolean isFirstPageV2) {
        boolean isTab = isTab(openParams);
        if (isTab) {
            removeAllPages();
        }
        BasePage page = getBasePage(openParams, pageInfoArray, isHomePage, isFirstPageV2);
        // 更新页面的路由时间为当前路由时间
        page.setRouteTime(openParams.getRouteTime());
        addPage(page);
        page.onShow(openParams.openType);
        return page.getPage();
    }

    // 只用于加速创建当前页面
    public void preloadPage(OpenParams openParams, @Nullable PageInfoArray pageInfoArray) {
        if (alreadyCreatePage) {
            MSCLog.i(TAG, "cancel preloadPage already launch");
            return;
        }
        if (!hasCreatedPage && preCreatePage == null) {
            hasCreatedPage = true;
            MSCLog.i(TAG, "preloadPage", openParams.url);
            preCreatePage = createBasePage(openParams, pageInfoArray, null, null);
        }
    }

    private BasePage getBasePage(OpenParams openParams, PageInfoArray pageInfoArray, Boolean isHomePage, Boolean isFirstPageV2) {
        BasePage page = null;
        if (preCreatePage != null) {
            if (TextUtils.equals(preCreatePage.getRoutePath(), openParams.url)) {
                MSCLog.i(TAG, "reuse preCreatePage", preCreatePage);
                page = preCreatePage;
            }
            preCreatePage = null;
        }
        if (page == null) {
            alreadyCreatePage = true;
            page = createBasePage(openParams, pageInfoArray, isHomePage, isFirstPageV2);
        }

        return page;
    }

    private BasePage createBasePage(OpenParams openParams, PageInfoArray pageInfoArray, Boolean isHomePage, Boolean isFirstPageV2) {
        PerfTrace.online().begin(PerfEventConstant.CREATE_PAGE_VIEW).report().arg("url", openParams.url);
        BasePage page;
        //decide which is Tab Page
        openParams.isTab = openParams.isTab && (!mController.isWidget() || DebugHelper.navigateInWidget);
        if (openParams.isTab) {
            page = new TabPage(getRuntime(), mController, mEventListener, openParams.getOriginUrlOrUrl(), openParams.url, pageInfoArray,
                    openParams.getRouteTime(), isHomePage == null ? getPageCount() == 0 : isHomePage, openParams.isTabDerived, isFirstPageV2 == null ? getPageCount() == 0 : isFirstPageV2);
        } else {
            page = new Page(getRuntime(), mController, mEventListener, openParams.url, null, PageInfoArray.getReloadPageId(pageInfoArray),
                    openParams.getRouteTime(), isHomePage == null ? getPageCount() == 0 : isHomePage, openParams.getOriginUrlOrUrl(), isFirstPageV2 == null ? getPageCount() == 0 : isFirstPageV2);
        }
        PerfTrace.online().end(PerfEventConstant.CREATE_PAGE_VIEW).report();
        return page;
    }

    public void navigateBackToPipPage(String url, long routeTime, @NonNull BizNavigationExtraParams bizNavigationExtraParams) throws ApiException {
        int pageCount = getPageCount();
        for (int i = pageCount - 1; i >= 0; i--) {
            BasePage tempPage = (BasePage) mPageContainer.getChildAt(i);
            if (TextUtils.equals(tempPage.getRoutePath(), url)) {
                if (i == pageCount - 1) {
                    //栈顶页面不需要处理
                    return;
                } else {
                    navigateBackPage(pageCount - 1 - i, false, routeTime);
                }
                return;
            }
        }

        navigateToPage(url, null, routeTime, bizNavigationExtraParams);
    }

    protected void addPage(BasePage page) {
        mPageContainer.addView(UIUtil.removeFormParentIfNeed(page), new FrameLayout.LayoutParams(
                FrameLayout.LayoutParams.MATCH_PARENT, FrameLayout.LayoutParams.MATCH_PARENT));
        MSCLog.i(TAG, "added page, page:", page, ", PageContainer:", mPageContainer, ", PageManager:", this, ", PageCount:", mPageContainer.getChildCount());
        getRuntime().jsErrorRecorder.pushPage(page.getRoutePath(), String.valueOf(page.getViewId()));
        getRuntime().incrementHistoryPageCount();
    }

    private ContainerReporter getContainerReporter() {
        return mController instanceof ContainerController ? ((ContainerController) mController).getContainerReporter() : null;
    }

    /**
     * 启动主页面（第一个页面）
     *
     * @param url       页面路径
     * @param routeTime
     */
    public void launchHomePage(final String url, long routeTime, int routeId) {
        navigateHomePage(url, null, true, routeTime, routeId);
    }

    public void navigateHomePage(final String url, Integer openSeq, long routeTime, int routeId) {
        navigateHomePage(url, openSeq, false, routeTime, routeId);
    }

    public boolean getIsLaunch() {
        return mIsLaunch;
    }

    /**
     * todo 确认埋点统一上报 是否算启动
     */
    private void navigateHomePage(final String url, Integer openSeq, boolean isLaunch, long routeTime, int routeId) {
        MSCLog.i(TAG, "navigateHomePage isLaunch:", isLaunch, ", url:", url);
        this.mIsLaunch = isLaunch;
        if (TextUtils.isEmpty(url)) {
            MSCLog.w(TAG, "navigateHomePage failed, url is null");
            return;
        }
        removeAllPages();
        loadPage(url, openSeq, isLaunch, routeTime, routeId);
    }

    private void loadPage(String url, Integer openSeq, boolean isLaunch, long routeTime, int routeId) {
        getRuntime().getPerfEventRecorder().beginDurableEvent(PerfEventConstant.CREATE_VIEW);
        try {
            OpenParams openParams;
            String openType;
            boolean isFirstPageV2 = true;

            // TODO: 2024/1/26 tianbin 代码优化下
            if (isLaunch) {
                // 启动第一个页面，需要走特殊的openType以在前端触发整个小程序的生命周期
                openType = mController.isWidget() ? OpenParams.WIDGET_LAUNCH : OpenParams.APP_LAUNCH;
            } else {
                if (getRuntime().getMSCAppModule().checkIsTabPageIfRouted(url)) {
                    // tab页必须处于页面栈底部，此时融合模式Activity一定已被清除至只剩最接近栈底的一个，视为relaunch
                    openType = OpenParams.RE_LAUNCH;
                } else {
                    // 1.融合模式正在开启非首个Activity 2.保活启动（必须navigateTo，不能relaunch，内存不足场景下openlink跳转会判定为保活启动，发relaunch会使前端页面栈清空，返回会白屏）
                    // https://km.sankuai.com/collabpage/1985759093
                    openType = OpenParams.NAVIGATE_TO;
                    if (getRuntime().getPageCount() > 0) {
                        // 融合模式正在开启非首个Activity
                        isFirstPageV2 = false;
                    }
                }
            }
            openParams = new OpenParams.Builder()
                    .setUrl(url)
                    .setOpenType(openType)
                    .setRouteTime(routeTime)
                    .setRouteId(routeId)
                    .setIsWidget(mController.isWidget())
                    .setAllowInferNavigateToTabPage(true)
                    .setExternalRouter(true)
                    .setIgnoreRouteMapping(mController.isIgnoreRouteMapping(true))
                    .build(getRuntime());

            if (OpenParams.APP_LAUNCH.equals(openType) || OpenParams.WIDGET_LAUNCH.equals(openType)) {
                openParams.isFromLaunch = true;
            }
            Page page = showPage(openParams, null, isFirstPageV2);
            page.setContainerReporter(getContainerReporter());
            getRuntime().getPerfEventRecorder().endDurableEvent(PerfEventConstant.CREATE_VIEW);
            if (isLaunch) {
                // 启动第一个页面，需要走特殊的openType以在前端触发整个小程序的生命周期
                page.onLaunchHome(openParams);
            } else {
                if (openParams.isTab) {
                    // tab页必须处于页面栈底部，此时融合模式Activity一定已被清除至只剩最接近栈底的一个，视为relaunch
                    page.onReLaunch(openParams);
                } else {
                    // 1.融合模式正在开启非首个Activity 2.保活启动（必须navigateTo，不能relaunch，内存不足场景下openlink跳转会判定为保活启动，发relaunch会使前端页面栈清空，返回会白屏）
                    // https://km.sankuai.com/collabpage/1985759093
                    // TODO: 2024/1/26 tianbin03 是否需要调用setAllowInferNavigateToTabPage
                    openParams.setOpenType(OpenParams.NAVIGATE_TO);
                    openParams.openSeq = openSeq;
                    page.onNavigateTo(openParams);
                }
            }

            //Tab页面找到最终page时，从OpenParam中取routeId设置和pageId的关系
            if (getRuntime() != null && !page.isTabPage()) {
                IDataPrefetchModule iDataPrefetchModule = getRuntime().getModule(IDataPrefetchModule.class);
                if (iDataPrefetchModule != null) {
                    iDataPrefetchModule.attachToPage(routeId, page.getViewId());
                }
            }

            //找到页面，设置routeId
            page.setRouteId(routeId);

        } catch (ApiException e) {
            MSCLog.e(TAG, e, "loadPage");
            ToastUtils.toast("页面跳转异常");
        }
    }

    public void navigateToPage(String url, Integer openSeq, long routeTime, @NonNull BizNavigationExtraParams bizNavigationExtraParams) throws ApiException {
        PerfTrace.instant("navigateToPage");
        // TODO: 2024/2/4 tianbin 埋点适配 setRouteMapping
        reportRouteStart("navigateTo", url);

        OpenParams openParams = new OpenParams.Builder()
                .setUrl(url)
                .setOpenType(OpenParams.NAVIGATE_TO)
                .setOpenSeq(openSeq)
                .setBizNavigationExtraParams(bizNavigationExtraParams)
                .setRouteTime(routeTime)
                .build(getRuntime());
        navigateToPage(openParams);
    }

    private void requestPagePrefetchWhenNavigate(String url) {
        if (!MSCHornRollbackConfig.isRollbackNavigateToAppPrefetch() && (!MSCConfig.isEnablePrefetch() || RequestPrefetchManager.isAppLevelPrefetch(getRuntime().getMSCAppModule(), getRuntime().getAppId()))) {
            return;
        }
        getRuntime().resetOrCreateRequestPrefetchManager().startPrefetch(getRuntime().getContainerManagerModule().getTopActivity(), getRuntime().getMSCAppModule().getMetaInfo(), url, SceneNumber.DEFAULT);
    }

    /**
     * 导航到页面
     *
     * @param openParams 页面启动参数
     */
    protected void navigateToPage(final OpenParams openParams) throws ApiException {
        final String path = openParams.url;
        checkPageUrl(path);
        if (MSCHornRollbackConfig.isRollbackSetRouteMappingPersist()) {
            if (openParams.isTab) {
                throw new ApiException(MSCErrorCode.ERROR_CODE_API_COMMON_NOT_MATCH_SCENE, "can not navigateTo tab page");
            }
        } else {
            if (getRuntime().getAppConfigModule().isTabPage(openParams.getOriginUrlOrUrl())) {
                throw new ApiException(MSCErrorCode.ERROR_CODE_API_COMMON_NOT_MATCH_PATH, "can not navigateTo tab page");
            }
        }
        if (!ensurePageCount()) {
            throw new ApiException(MSCErrorCode.ERROR_CODE_API_COMMON_EXTERNAL_APP_PAGE_STACK_OVER_LIMIT, String.format("no more than %s pages allowed", MAX_COUNT));
        }
        requestPagePrefetchWhenNavigate(path);
        openParams.setRouteId(openParams.hashCode());
        Runnable startPageRunnable = new Runnable() {
            @Override
            public void run() {
                Page topPage = getTopPage();
                if (topPage != null) {
                    topPage.onHide(IPageLifecycleInterceptor.TYPE_PAGE_PAUSE_CAUSE_NAVIGATE_PUSH);
                }
                final Page page = showPage(openParams, null, false);
                page.onNavigateTo(openParams);
                dataPrefetchAttachToPage(page, openParams.getRouteId(), page.getViewId());
            }
        };
        launchPageByRoute(path, startPageRunnable, false, openParams.getRouteId(), openParams.getRouteTime(), openParams.bizNavigationExtraParams);
    }

    private void showLoading(String path) {
        if (getRuntime().getMSCAppModule().isPackageLoaded(path)) {
            MSCLog.i(TAG, "showLoading path package is loaded", path);
            return;
        }
        if (delayLoadingRunnable == null) {
            delayLoadingRunnable = new Runnable() {
                @Override
                public void run() {
                    IContainerDelegate container = getRuntime().getContainerManagerModule().getTopContainer();
                    if (container == null) {
                        return;
                    }
                    Activity activity = container.getActivity();
                    if (loadingDialog == null) {
                        loadingDialog = new LoadingDialog(activity, getRuntime().getAppId());
                    }
                    MSCLog.i(TAG, "showLoading loadingDialog", loadingDialog);
                    loadingDialog.setOnCancelListener(new DialogInterface.OnCancelListener() {
                        @Override
                        public void onCancel(DialogInterface dialog) {
                            // 空实现，对齐MMP
                        }
                    });
                    loadingDialog.show(activity.getString(R.string.msc_load_package));
                }
            };
        }
        MSCExecutors.runOnUiThreadDelayed(delayLoadingRunnable, 1000);
    }



    public void dismissLoading() {
        MSCLog.i(TAG, "dismissLoading delayLoadingRunnable", delayLoadingRunnable);
        if (delayLoadingRunnable != null) {
            MSCExecutors.removeUIThreadCallbacks(delayLoadingRunnable);
        }
        MSCExecutors.runOnUiThread(new Runnable() {
            @Override
            public void run() {
                MSCLog.i(TAG, "dismissLoading loadingDialog", loadingDialog);
                if (loadingDialog != null) {
                    loadingDialog.dismiss();
                    loadingDialog = null;
                }
            }
        });
    }

    /**
     * 页面非正常关闭时，缓存页面栈信息
     * <p>
     * fix: merge with previous page stack  销毁重建的情况下，二次销毁，针对当前页面未恢复完全的页面 需要做一遍merge
     *
     * @return 页面栈信息
     */
    public Stack<PageInfoArray> cachePageStackInfos() {
        Stack<PageInfoArray> pageList = new Stack<>();
        for (int i = 0; i < getPageCount(); i++) {
            BasePage page = (BasePage) mPageContainer.getChildAt(i);
            pageList.add(page.getSavedPageInfo());
        }
        return pageList;
    }

    /**
     * 重定向页面url
     *
     * @param path      页面url
     * @param routeTime
     */
    protected void redirectToPage(final String path, long routeTime, @NonNull BizNavigationExtraParams bizNavigationExtraParams) throws ApiException {
        // TODO: 2024/2/4 tianbin 埋点适配 setRouteMapping
        reportRouteStart("redirectTo", path);

        OpenParams openParams = new OpenParams.Builder()
                .setUrl(path)
                .setOpenType(OpenParams.REDIRECT_TO)
                .setRouteTime(routeTime)
                .setBizNavigationExtraParams(bizNavigationExtraParams)
                .build(getRuntime());
        checkPageUrl(openParams.url);
        if (getRuntime().getMSCAppModule().isTabPage(path)) {
            throw new ApiException(MSCErrorCode.ERROR_CODE_API_COMMON_NOT_MATCH_PATH, "can not redirectTo tab page");
        }

        // TODO: 2024/1/25 tianbin 数据预拉取需要适配 setRouteMapping
        requestPagePrefetchWhenNavigate(path);
        openParams.setRouteId(openParams.hashCode());
        Runnable startPageRunnable = new Runnable() {
            @Override
            public void run() {
                boolean isHomePage = getPageCount() - 1 == 0;
                mPageContainer.disableAnimation();
                final Page page;
                // https://tt.sankuai.com/ticket/handle?filter=mine&id=306451127 解决redirectTo先回到上个页面再跳转问题。
                // 先show新页面，再remove原页面
                page = showPageInner(openParams, null, isHomePage, false);
                int pageCount = getPageCount();
                if (pageCount > 1) {
                    removePage(pageCount - 2);
                }
                mPageContainer.enableAnimation();
                page.onRedirectTo(openParams);
                dataPrefetchAttachToPage(page, openParams.getRouteId(), page.getViewId());
            }
        };
        launchPageByRoute(openParams.url, startPageRunnable, false, openParams.getRouteId(), openParams.getRouteTime(), bizNavigationExtraParams);
    }

    /**
     * 带检测的switchTab 前端触发事件
     */
    public void switchTabAction(String url, long routeTime, @NonNull BizNavigationExtraParams bizNavigationExtraParams) throws ApiException {
        // TODO: 2024/2/4 tianbin 埋点适配 setRouteMapping
        reportRouteStart("switchTab", url);

        checkPageUrl(url);
        if (!getRuntime().getMSCAppModule().isTabPage(url)) {
            ToastUtils.toast("页面跳转异常");
            throw new ApiException(MSCErrorCode.ERROR_CODE_API_COMMON_NOT_MATCH_PATH, "can't switchTab to single page");
        }

        // 融合模式 使用FusionPageManager可支持回退至小程序第一个Activity并启动tab页
        // 若无FusionPageManager则仅支持Activity内切换，找不到tab页则失败
        IPageModule bottomPage = getBottomPageModule();
        if (bottomPage != null && !bottomPage.isTabPage()) {
            // 在栈底非tab页，但此Activity为最底下一个Activity，应该起至此activity时，此处会走startActivity-onNewIntent-switchTabPage绕一下
            if (mFusionPageManager != null && mFusionPageManager.switchTab(
                    mActivity, getRuntime().getMSCAppModule().getAppId(), url, mController.getIntent())) {
                return;
            } else {
                throw new ApiException(MSCErrorCode.ERROR_CODE_API_COMMON_MEET_EXPECTATIONS, "can not switchTab in fusionMode while tab is not bottom of stack");
            }
        }
        switchTabPage(url, routeTime, bizNavigationExtraParams);
    }

    /**
     * 打开Tab Bar页面并关闭所有其他非Tab Bar页面
     * tab里的页面必须都在一个package里！—— 现在是只在主包里有tab
     *
     * @param url       页面url，注意参数会被去除
     * @param routeTime
     */
    public void switchTabPage(String url, long routeTime, @NonNull BizNavigationExtraParams bizNavigationExtraParams) throws ApiException {
        checkPageUrl(url);

        if (!getRuntime().getMSCAppModule().isTabPage(url)) {
            throw new ApiException(MSCErrorCode.ERROR_CODE_API_COMMON_NOT_MATCH_PATH, "can't switchTab to single page");
        }
        String trimmedUrl = PathUtil.getPath(url);
        if (getPageCount() > 1) {
            removePagesWithoutFirstPage(Integer.MAX_VALUE, false);
        }

        OpenParams openParams = new OpenParams.Builder()
                .setUrl(trimmedUrl)
                .setOpenType(OpenParams.SWITCH_TAB)
                .setRouteTime(routeTime)
                .setBizNavigationExtraParams(bizNavigationExtraParams)
                .build(getRuntime());
        openParams.setRouteId(openParams.hashCode());
        if (MSCHornRollbackConfig.isRollbackSetRouteMappingPersist()) {
            IDataPrefetchModule dataPrefetchModule = getRuntime().getModule(IDataPrefetchModule.class);
            if (dataPrefetchModule != null) {
                dataPrefetchModule.startDataPrefetch(url, openParams.getRouteId(), openParams.getRouteTime(), mController != null && mController.isWidget());
            }
            realSwitchTab(openParams);
        } else {
            Runnable startPageRunnable = new Runnable() {
                @Override
                public void run() {
                    realSwitchTab(openParams);
                }
            };
            launchPageByRoute(openParams.url, startPageRunnable, false, openParams.getRouteId(), openParams.getRouteTime(), bizNavigationExtraParams);
        }
    }

    private void realSwitchTab(OpenParams openParams) {
        Page page = getTopPage();
        if (page == null || !page.isTabPage()) {
            removeAllPages();
            page = showPage(openParams, null, false);
        } else {
            page.onShow(openParams.openType);
        }
        if (page.getTabPage() != null) {
            page.getTabPage().switchTab(openParams);
        }
    }

    /**
     * 前端触发的reLaunch事件处理
     */
    public void reLaunchAction(final String url, long routeTime, @NonNull BizNavigationExtraParams bizNavigationExtraParams) throws ApiException {
        if (reLaunchActionInFusion(url, mController.getIntent())) {
            return;
        }

        if (MSCHornRollbackConfig.readConfig().rollbackRelaunchReportFix) {
            // TODO: 2024/2/4 tianbin 埋点适配 setRouteMapping
            reportRouteStart("reLaunch", url);
        }
        reLaunchPage(url, routeTime, false, bizNavigationExtraParams);
    }

    /*
     * 融合模式处理relaunch
     */
    public boolean reLaunchActionInFusion(final String url, Intent intent) throws ApiException {
        checkPageUrl(url);
        // 不使用mFusionPageManager处理relaunch至本页面的情况，这属于页面内跳转
        // 其他情况，包含mmp非前台，或有多个Activity（当前Activity一定是顶部的那个），需要通过startActivity跳转至最下面的
        if (mController.isPaused()
                || MSCFusionActivityMonitor.getLivingActivityCount(getRuntime().getMSCAppModule().getAppId()) > 1) {
            if (mFusionPageManager != null && mFusionPageManager.reLaunch(
                    mActivity, getRuntime().getMSCAppModule().getAppId(), url, intent)) {
                return true;
            } else {
                throw new ApiException(MSCErrorCode.ERROR_CODE_API_COMMON_MEET_EXPECTATIONS, "can't relaunch in fusionMode");
            }
        }
        return false;
    }

    /**
     * 关闭所有页面，打开到应用内的某个页面
     *
     * @param path      页面url
     * @param routeTime
     * @return true：成功，否则亦然
     */
    public void reLaunchPage(final String path, long routeTime, boolean external, @NonNull BizNavigationExtraParams bizNavigationExtraParams) {
        if (!MSCHornRollbackConfig.readConfig().rollbackRelaunchReportFix) {
            // TODO: 2024/2/4 tianbin 埋点适配 setRouteMapping
            reportRouteStart("reLaunch", path);
        }
        CrashReporterHelper.getContainerRecorder().popAllPage();
        // TODO: 2024/1/25 tianbin 数据预拉取需要考虑setRouteMapping场景
        requestPagePrefetchWhenNavigate(path);
        try {
            OpenParams openParams = new OpenParams.Builder()
                    .setUrl(path)
                    .setOpenType(OpenParams.RE_LAUNCH)
                    .setRouteTime(routeTime)
                    .setAllowInferNavigateToTabPage(true)
                    .setExternalRouter(external)
                    .setIgnoreRouteMapping(mController.isIgnoreRouteMapping(external))
                    .setBizNavigationExtraParams(bizNavigationExtraParams)
                    .build(getRuntime());
            openParams.setRouteId(openParams.hashCode());
            Runnable startPageRunnable = new Runnable() {
                @Override
                public void run() {
                    mPageContainer.disableAnimation();
                    removeAllPages();   //注意remove和new Page的顺序，先移除的话可以复用AppPage
                    //relaunch之后只有一个页面所以直接指定是首页
                    final Page page;
                    page = showPageInner(openParams, null, true, false);
                    page.onReLaunch(openParams);
                    dataPrefetchAttachToPage(page, openParams.getRouteId(), page.getViewId());
                }
            };
            launchPageByRoute(openParams.url, startPageRunnable, external, openParams.getRouteId(), openParams.getRouteTime(), bizNavigationExtraParams);
        } catch (ApiException e) {
            MSCLog.e(TAG, e, "reLaunchPage");
        }
    }


    /**
     * 启动页面不存在（第一个页面）
     *
     * @param url      页面路径
     * @param openType
     */
    public void onPageNotFound(final String url, String openType, long routeTime) {
        // TODO: 2024/2/4 tianbin 埋点适配 setRouteMapping
        reportRouteStart("onPageNotFound", url);

//         mShowPageNotFoundPending 表示后续可能要展示兜底页，现在还决定不了，要根据js同步执行完onPageNotFound之后的结果，再判断是否展示兜底页
        mShowPageNotFoundPending = true;
        removeAllPages();
        OpenParams openParams = new OpenParams(url, openType);
        openParams.setRouteTime(routeTime);
        final Page page = showPage(openParams, null, true, true);

        if (VersionUtil.compare(getRuntime().getMSCAppModule().getBasePkgVersion(), "1.8.0") < 0) {
            showFallbackIfPageNotFound();
        } else {
            page.onPageNotFound(openParams);
        }
    }

    public void showFallbackIfPageNotFound() {
        Page page = getTopPage();
        if (mShowPageNotFoundPending && page != null) {
            page.showPageNotFoundView();
            // PageNotFound渲染时，也发一个FirstRender, 隐藏掉loading Page
//                mEventListener.onPageFirstRender(page.getPagePath(), null);
        }
    }

    private void reportRouteStart(String routeMode, String url) {
        int pageId = getTopPageId();
        Page topPage = getTopPage();
        String prePagePath = "";
        if (topPage != null) {
            prePagePath = topPage.getPagePath();
        }
        RouteReporter.create(getRuntime()).reportRouteStart(mController.getActivity(), routeMode, pageId, url, prePagePath, mController.isWidget());
    }

    /**
     * 导航返回页面
     * <p>
     * 融合模式下，在首页返回，可返回至上一层页面
     *
     * @param delta     返回的页面层数
     * @param routeTime
     */
    protected boolean navigateBackPage(int delta, boolean __mtAllowCloseContainer, long routeTime) {
        // TODO: 2024/2/4 tianbin 埋点适配 setRouteMapping
        reportRouteStart("navigateBack", "");

        needRemoveTopPage = !removePagesWithoutFirstPage(delta, __mtAllowCloseContainer);
        if (needRemoveTopPage) { // 仅剩栈顶页面
            // 融合模式 栈顶返回直接关闭页面
            MSCLog.i(TAG, "PageManager navigateBackPage");
            mController.handleCloseContainer("navigateBack");
            return true;
        }

        CrashReporterHelper.popPage();
        Page page = getTopPage();
        if (page != null) {
            page.onShow(OpenParams.NAVIGATE_BACK);
            page.onNavigateBackTo(routeTime);
        }
        return true;
    }

    void navigateBackPageForApi(int delta, boolean __mtAllowCloseContainer, long routeTime) throws ApiException {
        if (!navigateBackPage(delta, __mtAllowCloseContainer, routeTime)) {
            throw new ApiException(MSCErrorCode.ERROR_CODE_API_COMMON_MEET_EXPECTATIONS, "cannot navigate back at first page");
        }
    }

    /**
     * 实际抛出的是RuntimeException，此处声明为throws ApiException是为了强制调用方处理，通常此类参数错误并不需要以崩溃的方式应对
     */
    protected void checkPageUrl(String url) throws ApiException {
        if (TextUtils.isEmpty(url)) {
            throw new ApiException(MSCErrorCode.ERROR_CODE_API_COMMON_INVALID_PARAM, "url is empty");
        }
        if (!getRuntime().getMSCAppModule().hasPage(url)) {
            throw new ApiException(MSCErrorCode.ERROR_CODE_API_COMMON_PATH_NOT_EXIST, String.format("page %s is not found", url));
        }
        // 已经处于pageNotFound状态，现在又通过js调api跳转到了存在的页面，则取消pending中的展示兜底页的动作
        mShowPageNotFoundPending = false;
    }

    public List<Integer> getAllPageId() {
        List<Integer> pageIdList = new ArrayList<>();
        for (int i = 0; i <= getPageCount() - 1; i++) {
            pageIdList.add(getPageFromView(mPageContainer.getChildAt(i)).getViewId());
        }
        return pageIdList;
    }
}