package com.meituan.msc.modules.page;

import android.support.annotation.NonNull;
import android.view.ViewGroup;

import com.meituan.msc.modules.api.ApiException;
import com.meituan.msc.modules.api.msi.navigation.BizNavigationExtraParams;
import com.meituan.msc.modules.container.IContainerDelegate;
import com.meituan.msc.modules.engine.MSCRuntime;
import com.meituan.msc.modules.page.reload.PageInfoArray;

import java.util.List;

/**
 * Created by letty on 2021/12/30.
 **/
public class PageManagerModule implements IPageManagerModule {
    PageManager pageManager;
    MSCRuntime mRuntime;
    private Boolean isFullScreenOfLastPage;

    public PageManagerModule(IContainerDelegate controller, MSCRuntime runtime, IRuntimeGetter runtimeGetter) {
        this.mRuntime = runtime;
        pageManager = new PageManager(controller, runtime, runtimeGetter);
    }

    @Override
    public void reload(PageInfoArray pageInfoArray, long routeTime, @NonNull BizNavigationExtraParams bizNavigationExtraParams) {
        pageManager.reloadTopOfStack(pageInfoArray, routeTime, bizNavigationExtraParams);
    }

    @Override
    public void navigateTo(String url, Integer openSeq, long routeTime, @NonNull BizNavigationExtraParams bizNavigationExtraParams) throws ApiException {
        pageManager.navigateToPage(url,openSeq, routeTime, bizNavigationExtraParams);
    }

    @Override
    public void launch(String url, long routeTime) {
        //未找到调用的地方, 取routeTime为id
        pageManager.launchHomePage(url, routeTime, (int) routeTime);
    }

    @Override
    public void reLaunch(String url, long routeTime, @NonNull BizNavigationExtraParams bizNavigationExtraParams) throws ApiException {
        pageManager.reLaunchAction(url, routeTime, bizNavigationExtraParams);
    }

    @Override
    public void switchTab(String url, long routeTime, @NonNull BizNavigationExtraParams bizNavigationExtraParams) throws ApiException {
        pageManager.switchTabAction(url, routeTime, bizNavigationExtraParams);
    }

    @Override
    public void redirectTo(String url, long routeTime, @NonNull BizNavigationExtraParams bizNavigationExtraParams) throws ApiException {
        pageManager.redirectToPage(url, routeTime, bizNavigationExtraParams);
    }

    @Override
    public void navigateBack(int delta, boolean __mtAllowCloseContainer, long routeTime) throws ApiException {
        pageManager.navigateBackPageForApi(delta, __mtAllowCloseContainer, routeTime);
    }

    @Override
    public List<Integer> getAllPageId() {
        return pageManager.getAllPageId();
    }

    @Override
    public boolean isNeedRemoveTopPage() {
        return pageManager.isNeedRemoveTopPage();
    }

    @Override
    public Boolean getIsFullScreenOfLastPage() {
        return isFullScreenOfLastPage;
    }

    @Override
    public void setIsFullScreenOfLastPage(boolean isFullScreen) {
        isFullScreenOfLastPage = isFullScreen;
    }

    @Override
    public void pageNotFound(String url, String openType, long routeTime) {
        pageManager.onPageNotFound(url, openType, routeTime);
    }

    @Override
    public void pageNotFoundCallback() {
        pageManager.showFallbackIfPageNotFound();
    }

    @Override
    public boolean hasPage(int id) {
        return pageManager.hasPage(id);
    }

    @Override
    public IPageModule getPage(int id) {
        return pageManager.getPageModuleById(id);
    }

    @Override
    public IPageModule getTopPage() {
        return pageManager.getTopPageModule();
    }

    @Override
    public int getTopPageId() {
        return getTopPage() != null ? getTopPage().getId() : 0;
    }

    @Override
    public IPageModule getBottomPage() {
        return pageManager.getBottomPageModule();
    }

    @Override
    public int getPageCount() {
        return pageManager.getPageCount();
    }

    @Override
    public ViewGroup asView() {
        return pageManager.getPageContainer();
    }

    @Override
    public void onResume(String openType) {
        pageManager.onResume(openType);
    }

    @Override
    public void onPause(int cause) {
        pageManager.onPause(cause);
    }

    @Override
    public PageManager getPageManager() {
        return pageManager;
    }
}
