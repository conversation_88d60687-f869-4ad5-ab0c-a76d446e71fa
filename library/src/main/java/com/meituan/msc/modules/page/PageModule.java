package com.meituan.msc.modules.page;

import android.support.annotation.Size;
import android.text.TextUtils;
import android.view.View;

import com.meituan.msc.common.executor.MSCExecutors;
import com.meituan.msc.common.utils.MPConcurrentHashMap;
import com.meituan.msc.modules.api.msi.webview.IWebFocusDispatcher;
import com.meituan.msc.modules.engine.MSCHornRollbackConfig;
import com.meituan.msc.modules.engine.MSCRuntime;
import com.meituan.msc.modules.manager.MSCMethod;
import com.meituan.msc.modules.manager.MSCModule;
import com.meituan.msc.modules.page.custom.PullLoadingIconConfig;
import com.meituan.msc.modules.page.render.AppPageReporter;
import com.meituan.msc.modules.page.render.BaseRenderer;
import com.meituan.msc.modules.page.render.RendererType;
import com.meituan.msc.modules.page.transition.PageTransitionConfig;
import com.meituan.msc.modules.page.view.PageViewWrapper;
import com.meituan.msi.bean.MsiContext;
import com.meituan.msi.page.IKeyBoardHeightChangeObserver;
import com.meituan.msi.page.IPage;

import java.util.Map;

/**
 * Created by letty on 2022/1/17.
 * <p>
 * TODO 模块的挂载和取消挂载
 **/
public class PageModule extends MSCModule implements IPageModule {
    //subModule maybe get parentModule
//    final Page mPage;
    BaseRenderer mBaseRenderer;
    PageViewWrapper mPageViewWrapper;
    ITabPage mTabPage;
    boolean enableInterceptBackAction;
    // 缓存Tab页的退出拦截状态
    final MPConcurrentHashMap<String, Boolean> enableInterceptBackActionForPages = new MPConcurrentHashMap<>();
    boolean mIsWidgetPage;
    IPageNavigationBarMethods mPageNavigationBarMethods;
    PageTransitionConfig mPageTransitionConfig;
    PullLoadingIconConfig mPullLoadingIconConfig;
    String mPagePath;
    IMSCViewGroup mViewGroupImp;
    private volatile boolean isDestroyed = false;
    IWebFocusDispatcher webFocusDispatcher;
    private Map<String, String> bizTags;

    PageModule(BaseRenderer baseRenderer, PageViewWrapper pageViewWrapper, ITabPage iTabPage, String url, IWebFocusDispatcher webFocusDispatcher) {
        mBaseRenderer = baseRenderer;
        mPageViewWrapper = pageViewWrapper;
        mTabPage = iTabPage;
        mPageNavigationBarMethods = new PageNavigationBarMethods(pageViewWrapper);
        mPagePath = url;
        this.webFocusDispatcher = webFocusDispatcher;
    }

    PageModule setIsWidget(boolean isWidgetPage) {
        mIsWidgetPage = isWidgetPage;
        return this;
    }

    @Override
    public void onRuntimeAttached(MSCRuntime runtime) {
        super.onRuntimeAttached(runtime);
        registerSubModule(mBaseRenderer.getMSCModules());
        mPageTransitionConfig = PageTransitionConfig.parsePageTransition(mPagePath, runtime.getAppConfigModule());
        boolean enablePullRefresh = runtime.getMSCAppModule().isEnablePullDownRefresh(mPagePath);
        if (MSCHornRollbackConfig.enableCustomPullLoadingIcon() && enablePullRefresh) {
            mPullLoadingIconConfig = PullLoadingIconConfig.parsePullLoadingIcon(mPagePath, runtime.getAppConfigModule());
        }
    }

    public PageModule setViewGroupImp(IMSCViewGroup viewGroupImp) {
        mViewGroupImp = viewGroupImp;
        return this;
    }

    public PageModule setBizTags(Map<String, String> bizTags) {
        this.bizTags = bizTags;
        return this;
    }

    @Override
    public String getName() {
        return String.valueOf(getId());
    }

    @Override
    public int getId() {
        return mBaseRenderer.getViewId();
    }

    @Override
    public int[] getWindowSize() {
        if (mPageViewWrapper != null && mPageViewWrapper.isLaidOut()) {
            int[] rec = new int[2];
            rec[0] = mPageViewWrapper.getWidth();
            rec[1] = mPageViewWrapper.getHeight();
            return rec;
        }
        return null;
    }

    @Override
    public boolean onSubModuleNotFound(String moduleName, String methodName) {
        //覆写该方法，以屏蔽对应UIManager销毁后，触发js异常的逻辑。目前针对已经有报错的方法进行兼容，后续根据线上实际变动，看是否要以submodule形式统一处理或再度补充方法
        if (isDestroyed) {
            if (TextUtils.equals(moduleName, "UIManager")) {
                switch (methodName) {
                    case "manageChildren":
                    case "batchDidComplete":
                    case "batchDidCompleteWithOption":
                    case "removeRootView":
                    case "createView":
                        return true;
                    default:
                        return false;
                }
            } else if (TextUtils.equals(moduleName, "IntersectionObserver")) {
                switch (methodName) {
                    case "disconnect":
                        return true;
                    default:
                        return false;
                }
            }
        }
        return false;
    }


    @Override
    @MSCMethod(isSync = true)
    public void disableScrollBounce(boolean disable) {
        MSCExecutors.runOnUiThread(new Runnable() {
            @Override
            public void run() {
                mPageViewWrapper.disableScrollBounce(disable);
            }
        });
    }

    @Override
    @MSCMethod
    public void startPullDownRefresh() {
        mPageViewWrapper.startPullDownRefresh();
    }

    @Override
    @MSCMethod
    public void stopPullDownRefresh() {
        mPageViewWrapper.stopPullDownRefresh();
    }

    @Override
    public void setBackgroundColor(int parseColor) {
        mPageViewWrapper.setBackgroundColor(parseColor);
    }

    @Override
    public void setBackgroundTextStyle(boolean isDarkMode) {
        mPageViewWrapper.setBackgroundTextStyle(isDarkMode);
    }

    @Override
    public View asView() {
        return mPageViewWrapper;
    }

    @Override
    public RendererType getRendererType() {
        return mBaseRenderer.getType();
    }

    @Override
    public String getPagePath() {
        return mPagePath == null ? "" : mPagePath;
    }

    public boolean isWidget() {
        return mIsWidgetPage;
    }

    @Override
    public boolean isTabPage() {
        return mTabPage != null;
    }

    @Override
    public ITabPage getTabPage() {
        return mTabPage;
    }

    @Override
    public boolean isEnableBackActionIntercept() {
        if (!MSCHornRollbackConfig.readConfig().rollbackInterceptBackFix && isTabPage()) {
            Boolean enable = enableInterceptBackActionForPages.get(mTabPage.getCurrentPagePath());
            return enable != null && enable;
        }
        return enableInterceptBackAction;
    }

    @Override
    public void setEnableBackActionIntercept(boolean isEnable) {
        if (!MSCHornRollbackConfig.readConfig().rollbackInterceptBackFix && isTabPage()) {
            enableInterceptBackActionForPages.put(mTabPage.getCurrentPagePath(), isEnable);
            return;
        }
        enableInterceptBackAction = isEnable;
    }

    @Override
    public void showToast(View view, IPage.ViewParam viewParam) {
        mPageViewWrapper.showMsiToast(view, viewParam);
    }

    @Override
    public View getToast() {
        return mPageViewWrapper.getToastView();
    }

    @Override
    public void hideToast() {
        mPageViewWrapper.hideToast();
    }

    public IPageNavigationBarMethods getPageNavigationBarMethods() {
        return mPageNavigationBarMethods;
    }

    public AppPageReporter getReporter() {
        return mBaseRenderer != null ? mBaseRenderer.pageData.appPageReporter : null;
    }

    @Override
    public PageTransitionConfig getPageTransitionConfig() {
        return mPageTransitionConfig;
    }

    @Override
    public PullLoadingIconConfig getPullLoadingIconConfig() {
        return mPullLoadingIconConfig;
    }

    @Override
    public void setPageTransitionConfig(PageTransitionConfig pageTransitionConfig) {
        mPageTransitionConfig = pageTransitionConfig;
    }

    @Override
    public void onDestroy() {
        super.onDestroy();
        isDestroyed = true;
    }

    public BaseRenderer getRenderer() {
        return mBaseRenderer;
    }

    @Override
    public void startScroll(int targetY, int duration, MsiContext callback) {
        mPageViewWrapper.startScroll(targetY, duration, callback);
    }

    public IMSCViewGroup getIViewGroupImpl() {
        return mViewGroupImp;
    }

    public void adjustPosition(int offset, int keyboardHeight, boolean scroll) {
        mPageViewWrapper.adjustPosition(offset, keyboardHeight, scroll);
    }

    @Override
    public void adjustPosition(View view, String adjustKeyboardTo, int cursorSpacing, int bottomInsetHeight, int delayDur) {
        mPageViewWrapper.adjustPosition(view, adjustKeyboardTo, cursorSpacing, bottomInsetHeight, delayDur);
    }

    public int getKeyboardHeight() {
        return mPageViewWrapper.getKeyboardHeight();
    }

    public void getLocationInWindow(@Size(2) int[] outLocation) {
        mPageViewWrapper.getLocationInWindowInPage(outLocation);
    }

    public int getContentHeight() {
        return mPageViewWrapper.getContentHeight();
    }

    public void registerKeyboardChange(IKeyBoardHeightChangeObserver listener) {
        mPageViewWrapper.registerKeyboardChange(listener);
    }

    @Override
    public void unregisterKeyboardChange(IKeyBoardHeightChangeObserver listener) {
        mPageViewWrapper.unregisterKeyboardChange(listener);
    }

    @Override
    public IWebViewComponentInfo getWebViewComponent() {
        return new IWebViewComponentInfo() {
            @Override
            public boolean hasInnerWebViewComponent() {
                return mPageViewWrapper.hasInnerWebViewModule();
            }

            @Override
            public IWebFocusDispatcher getWebFocusDispatcher() {
                return webFocusDispatcher;
            }

            @Override
            public boolean isPageShow() {
                return PageModule.this.isPageShow();
            }
        };
    }

    @Override
    public boolean isPageShow() {
        return mPageViewWrapper.isPageShow();
    }


    @Override
    public int getNavigationBarHeight() {
        return mPageViewWrapper.getNavigationBarHeight();
    }

    @Override
    public int getWebScrollY() {
        return mPageViewWrapper.getWebScrollY();
    }

    @Override
    public void adjustPan(int changedHeight) {
        mPageViewWrapper.adjustPan(changedHeight);
    }

    @Override
    public void scrollYEx(int changedHeight) {
//        todo scrollWebY
    }

    @Override
    public int getPan() {
        return mPageViewWrapper.getPan();
    }

    @Override
    public int getHeight() {
        return mPageViewWrapper.getHeight();
    }

    @Override
    public Map<String, String> getBizTagsForPage() {
        return bizTags;
    }

    @Override
    public boolean isDestroyed() {
        return isDestroyed;
    }

}
