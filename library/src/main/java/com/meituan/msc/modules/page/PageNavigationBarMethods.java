package com.meituan.msc.modules.page;

import android.graphics.Rect;

import com.meituan.msc.modules.page.view.PageViewWrapper;

/**
 * Created by letty on 2022/2/23.
 **/
public class PageNavigationBarMethods implements IPageNavigationBarMethods {
    PageViewWrapper mPageViewWrapper;

    PageNavigationBarMethods(PageViewWrapper pageViewWrapper) {
        mPageViewWrapper = pageViewWrapper;
    }

    @Override
    public void setNavigationBarTitle(String title) {
        mPageViewWrapper.setNavigationBarTitle(title);
    }

    @Override
    public void setNavigationBarColor(int frontColor, int backgroundColor) {
        mPageViewWrapper.setNavigationBarTextColor(frontColor);
        mPageViewWrapper.setNavigationBarIconColor(frontColor);
        mPageViewWrapper.setNavigationBarBackgroundColor(backgroundColor);
    }

    @Override
    public void showShareMenu(boolean callFromBusiness) {
        mPageViewWrapper.showNavigationBarMoreMenu(callFromBusiness);
    }

    @Override
    public void hideShareMenu(boolean callFromBusiness) {
        mPageViewWrapper.hideNavigationBarMoreMenu(callFromBusiness);
    }

    @Override
    public boolean isMenuButtonShown() {
        return mPageViewWrapper.isMenuButtonShown();
    }

    @Override
    public void showNavigationBarLoading() {
        mPageViewWrapper.showNavigationBarLoading();
    }

    @Override
    public void hideNavigationBarLoading() {
        mPageViewWrapper.hideNavigationBarLoading();
    }

    @Override
    public Rect getMenuRect() {
        return mPageViewWrapper.getMenuRect();
    }

}
