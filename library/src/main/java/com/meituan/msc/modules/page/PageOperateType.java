package com.meituan.msc.modules.page;

import android.support.annotation.StringDef;

import java.lang.annotation.Retention;
import java.lang.annotation.RetentionPolicy;

/**
 * 页面操作类型
 *
 * <AUTHOR>
 * @date 2021/9/2.
 */
@StringDef({
        PageOperateType.LAUNCH_HOME_PAGE,
        PageOperateType.RELAUNCH_PAGE,
        PageOperateType.NAVIGATE_TO_HOME_PAGE,
        PageOperateType.REDIRECT_TO_PAGE,
        PageOperateType.NAVIGATE_TO_PAGE,
        PageOperateType.SWITCH_TAB_PAGE,
        PageOperateType.NAVIGATE_BACK,
        PageOperateType.RELOAD_TOP_OF_STACK})
@Retention(RetentionPolicy.SOURCE)
public @interface PageOperateType {
    String LAUNCH_HOME_PAGE = "launchHomePage";
    String RELAUNCH_PAGE = "reLaunchPage";
    String REDIRECT_TO_PAGE = "redirectToPage";
    String NAVIGATE_TO_PAGE = "navigateToPage";
    String NAVIGATE_TO_HOME_PAGE = "navigateHomePage";
    String RELOAD_TOP_OF_STACK = "reloadTopOfStack";
    String SWITCH_TAB_PAGE = "switchTabPage";
    String NAVIGATE_BACK = "navigateBack";
}