package com.meituan.msc.modules.page;

import com.meituan.msc.modules.service.ServiceInstance;

/**
 * 页面参数类
 */
public class PageParam {
    /**
     * @api
     * 组件标准化注释_标准API
     * 页面地址
     */
    public String mUrl;
    /**
     * jsc指针
     */
    // TODO: 2020/7/22 V8验证通过后改造 
    public ServiceInstance mJSExecutor;

    public static PageParamBuilder createBuilder() {
        return new PageParamBuilder();
    }

    public static final class PageParamBuilder {
        private String url;
        private ServiceInstance jSExecutor;

        private PageParamBuilder() {
        }


        public PageParamBuilder url(String url) {
            this.url = url;
            return this;
        }

        public PageParamBuilder service(ServiceInstance jsExecutor) {
            this.jSExecutor = jsExecutor;
            return this;
        }


        public PageParam build() {
            PageParam pageParam = new PageParam();
            pageParam.mUrl = this.url;
            pageParam.mJSExecutor = this.jSExecutor;
            return pageParam;
        }
    }
}