package com.meituan.msc.modules.page;

import android.app.Activity;
import android.text.TextUtils;

import com.meituan.msc.common.constant.APPIDConstants;
import com.meituan.msc.common.utils.JsonUtil;
import com.meituan.msc.common.utils.PathUtil;
import com.meituan.msc.lib.BuildConfig;
import com.meituan.msc.modules.engine.MSCHornRollbackConfig;
import com.meituan.msc.modules.engine.MSCRuntime;
import com.meituan.msc.modules.reporter.CommonTags;
import com.meituan.msc.modules.reporter.MSCCommonTagReporter;
import com.meituan.msc.modules.reporter.MSCLog;
import com.meituan.msc.modules.reporter.MSCReporter;
import com.meituan.msc.modules.reporter.ReporterFields;
import com.meituan.msc.modules.reporter.StatisticsReporter;

import org.json.JSONObject;

import java.util.HashMap;
import java.util.concurrent.atomic.AtomicInteger;

/**
 * 路由开始、路由异常上报
 * PS：路由成功在AppPageReporter中上报，便于获取对应页面参数
 */
public class RouteReporter extends MSCCommonTagReporter {

    private static final String PAGE_ID = "pageId";
    private static final String PAGE_PATH = "pagePath";
    private static final String PRE_PAGE_PATH = "prePagePath";
    private static final String ORIGIN_URL = "originUrl";
    private static final String PRE_ORIGIN_URL = "preOriginUrl";
    public static final String ROUTE_MODE = "openType";

    // 路由上报异常计算器，用于统计不成对出现的路由上报场景
    public static AtomicInteger routeExceptCount = new AtomicInteger();
    private final MSCRuntime runtime;

    private RouteReporter(MSCRuntime runtime) {
        super(CommonTags.build(runtime));
        this.runtime = runtime;
    }

    public static RouteReporter create(MSCRuntime runtime) {
        return new RouteReporter(runtime);
    }

    public void reportRouteStart(Activity activity, String routeMode, int pageId, String pagePath, String prePagePath, boolean isWidget) {
        String finalPath;
        if (MSCHornRollbackConfig.readConfig().rollbackPageRoutePathChange) {
            finalPath = PathUtil.getPath(pagePath);
        } else {
            finalPath = pagePath;
        }
        record(ReporterFields.REPORT_PAGE_ROUTE_START_COUNT)
                .tag(ROUTE_MODE, routeMode)
                .tag(PAGE_ID, pageId)
                .tag(ReporterFields.WIDGET, isWidget)
                .tag(PAGE_PATH, finalPath)
                .tag(PRE_PAGE_PATH, PathUtil.getPath(prePagePath))
                .tag(ORIGIN_URL, finalPath)
                .tag(PRE_ORIGIN_URL, prePagePath)
                .sendRealTime();


        reportPageRouteSC(activity, runtime.getAppId(), routeMode, finalPath, false, isWidget);
        MSCLog.i(TAG, "reportRouteStart routeExceptCount", RouteReporter.routeExceptCount.incrementAndGet(), runtime, routeMode);
    }

    private static void reportPageRouteSC(Activity activity, String appId, String routeMode, String pagePath, boolean isContainerCreate, boolean isWidget) {
        if (!MSCHornRollbackConfig.readConfig().rollbackStatisticsReporter && TextUtils.equals(appId, APPIDConstants.YOU_XUAN)) {
            HashMap<String, Object> map = new HashMap<>();
            // is_first 暂时按是否为创建容器场景上报
            map.put("is_first", isContainerCreate);
            map.put("route_mode", routeMode);
            map.put("page_type", isWidget ? "widget" : "page");
            map.put("msc_appid", appId);
            map.put("path_url", pagePath);
            StatisticsReporter.reportSC(activity, "b_group_8jwahtn5_sc", map);
        }
    }

    public void reportRouteException(JSONObject params, int viewId) {
        record(ReporterFields.REPORT_PAGE_ROUTE_EXCEPTION_COUNT)
                .tags(JsonUtil.toMap(params))
                .tag("viewId", viewId)
                .tag("routeExceptCount", -1)
                .sendRealTime();
    }

    public void reportRouteExceptCount() {
        record(ReporterFields.REPORT_PAGE_ROUTE_EXCEPTION_COUNT)
                .tag("routeExceptCount", RouteReporter.routeExceptCount)
                .sendRealTime();
    }

    public void reportRouteOpenParamError(String url, String openType, String errorStack) {
        record(ReporterFields.REPORT_PAGE_ROUTE_OPEN_PARAM_ERROR_COUNT)
                .tag("routeUrl", url)
                .tag("openType", openType)
                .tag("errorStack", errorStack == null ? "" : errorStack.substring(0, Math.min(5000, errorStack.length())))
                .sendRealTime();
    }

    public void reportRouteMappingPersistFail(String appId, String targetPath, String originPath, String buildId, boolean isPersist) {
        record(ReporterFields.REPORT_ROUTE_MAPPING_FAIL)
                .tag("mscAppId", appId)
                .tag("targetPath", targetPath)
                .tag("originPath", originPath)
                .tag("buildId", buildId)
                .tag("isPersist", isPersist)
                .sendRealTime();
    }

    public static class CommonReporter extends MSCReporter {

        public static CommonReporter create() {
            return new CommonReporter();
        }

        private CommonReporter() {
            this.commonTag(CommonTags.SDK_VERSION, BuildConfig.AAR_VERSION);
        }

        public void reportRouteStart(Activity activity, String appId, String routeMode, String pagePath, boolean isWidget) {
            String finalPath;
            if (MSCHornRollbackConfig.readConfig().rollbackPageRoutePathChange) {
                finalPath = PathUtil.getPath(pagePath);
            } else {
                finalPath = pagePath;
            }
            record(ReporterFields.REPORT_PAGE_ROUTE_START_COUNT)
                    .tag(CommonTags.TAG_MSC_APP_ID, appId)
                    .tag(ROUTE_MODE, routeMode)
                    .tag("isContainerCreate", true)
                    .tag(ReporterFields.WIDGET, isWidget)
                    .tag(PAGE_PATH, finalPath)
                    .sendRealTime();

            reportPageRouteSC(activity, appId, routeMode, finalPath, true, isWidget);
        }
    }
}
