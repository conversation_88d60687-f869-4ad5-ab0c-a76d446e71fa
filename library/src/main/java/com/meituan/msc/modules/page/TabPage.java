package com.meituan.msc.modules.page;

import android.animation.LayoutTransition;
import android.content.Context;
import android.support.annotation.Nullable;
import android.text.TextUtils;
import android.view.View;
import android.view.ViewGroup;
import android.widget.FrameLayout;
import android.widget.LinearLayout;
import android.widget.RelativeLayout;

import com.meituan.msc.common.executor.MSCExecutors;
import com.meituan.msc.common.framework.interfaces.PageEventListener;
import com.meituan.msc.common.utils.DisplayUtil;
import com.meituan.msc.common.utils.PathUtil;
import com.meituan.msc.lib.R;
import com.meituan.msc.modules.api.ApiException;
import com.meituan.msc.modules.api.msi.navigation.BizNavigationExtraParams;
import com.meituan.msc.modules.container.IContainerDelegate;
import com.meituan.msc.modules.container.OpenParams;
import com.meituan.msc.modules.engine.MSCHornRollbackConfig;
import com.meituan.msc.modules.engine.MSCRuntime;
import com.meituan.msc.modules.engine.dataprefetch.IDataPrefetchModule;
import com.meituan.msc.modules.page.reload.PageInfoArray;
import com.meituan.msc.modules.page.reload.PageInfoOne;
import com.meituan.msc.modules.page.transition.ITransitionPage;
import com.meituan.msc.modules.page.view.coverview.IPageLifecycleInterceptor;
import com.meituan.msc.modules.page.view.tab.TabBar;
import com.meituan.msc.modules.page.view.tab.TabItemInfo;
import com.meituan.msc.modules.reporter.MSCLog;
import com.meituan.msc.util.perf.PerfTrace;

import org.json.JSONException;
import org.json.JSONObject;

import java.util.HashMap;
import java.util.List;
import java.util.Map;

/**
 * Created by letty on 2022/3/4.
 **/
public class TabPage extends BasePage implements ITabPage, TabBar.OnSwitchTabListener, ITransitionPage {
    public static final String TAG = "TabPage";

    private final FrameLayout mWebLayout; //与tabBar等并列，PageViewWrapper的容器
    private Page mCurPage;
    private String mCurPagePath;
    private Page[] mPages = new Page[]{};
    private int openedPageCount = 0;
    /**
     * 存储targetUrl,page映射关系
     */
    private final Map<String, Page> mPathPageMap = new HashMap<>();
    private List<TabItemInfo> mTabList;
    private TabBar mTabBar;
    private PageInfoOne[] pageIdsReuse;
    private boolean mIsCustomTabBar;
    /**
     * @param pageInfoArray 在页面reload时，需要告知service层之前的pageId并更新为新的id，默认是View.NO_ID
     * @param routeTime
     * @param isFirstPage   是本Activity中最靠栈底的一个Page
     */
    public TabPage(MSCRuntime runtime,
                   IContainerDelegate controller,
                   PageEventListener eventListener,
                   String originUrl,
                   String url,
                   @Nullable PageInfoArray pageInfoArray,
                   long routeTime,
                   boolean isFirstPage,
                   Boolean isTabDerived,
                   boolean isFirstPageV2) {
        super(runtime, controller, eventListener, isFirstPage, isFirstPageV2);
        PerfTrace.begin("inflateTabPage");
        inflate(getContext(), R.layout.msc_page, this);
        PerfTrace.end("inflateTabPage");
        mWebLayout = findViewById(R.id.web_layout);
        PerfTrace.begin("initTabPage");
        initTabPage(mContext, originUrl, url, pageInfoArray, routeTime, isTabDerived);
        PerfTrace.end("initTabPage");
        setUpTabBar(findViewById(R.id.top_layout), findViewById(R.id.bottom_layout));
    }

    @Override
    public void switchTab(String url, long routeTime, String index, String text) {
        OpenParams openParams;
        openParams = new OpenParams(url, OpenParams.SWITCH_TAB);
        openParams.isTab = true;
        openParams.setRouteTime(routeTime);
        openParams.setRouteId(openParams.hashCode());

        if (MSCHornRollbackConfig.isRollbackSetRouteMappingPersist()) {
            IDataPrefetchModule dataPrefetchModule = mRuntime != null ? mRuntime.getModule(IDataPrefetchModule.class) : null;
            if (dataPrefetchModule != null) {
                dataPrefetchModule.startDataPrefetch(url, openParams.getRouteId(), routeTime, false);
            }
            switchTabByClick(openParams);
        } else {
            try {
                openParams = new OpenParams.Builder()
                        .setUrl(url)
                        .setOpenType(OpenParams.SWITCH_TAB)
                        .setRouteTime(routeTime)
                        .build(mRuntime);
            } catch (ApiException e) {
                throw new RuntimeException(e);
            }
            if (!openParams.isTab) {
                openParams.url = url;
                openParams.isTab = true;
            }
            OpenParams finalOpenParams = openParams;
            Runnable startPageRunnable = new Runnable() {
                @Override
                public void run() {
                    switchTabByClick(finalOpenParams);
                    if (MSCHornRollbackConfig.enableFixOnTabItemTabAndSwitchTab()) {
                        onTabItemTap(finalOpenParams.url, index, text);
                    }
                }
            };
            mController.getPageManager().launchPageByRoute(finalOpenParams.url, startPageRunnable, false, finalOpenParams.getRouteId(), routeTime, new BizNavigationExtraParams.Builder().build());
        }
    }

    // 如果有Tab页，直接复用；如果没有，则映射创建 或 直接创建
    @Override
    public void switchTab(OpenParams openParams) {
        realSwitchTab(openParams);

        if (mTabBar != null) {
            String originUrlOrUrl = openParams.getOriginUrlOrUrl();
            String url = openParams.url;
            String pagePath;
            // TODO: 8/7/24 linyinong 补充注释
            // 将SwitchTab放到后面，loadUrl在前面，将onAppRoute提前一些
            if (MSCHornRollbackConfig.isRollbackSetRouteMappingPersist()) {
                pagePath = PathUtil.getPath(originUrlOrUrl);
            } else {
                Boolean isTabDerived;
                if (!MSCHornRollbackConfig.isRollbackSetRouteMappingFix()) {
                    isTabDerived = openParams.isTabDerived;
                } else {
                    isTabDerived = mRuntime.getAppConfigModule().isTabDerivedPersist(originUrlOrUrl);
                    if (isTabDerived == null) {
                        isTabDerived = mRuntime.getAppConfigModule().isTabDerived(originUrlOrUrl);
                    }
                }
                if (Boolean.TRUE.equals(isTabDerived)) {
                    pagePath = PathUtil.getPath(originUrlOrUrl);
                } else {
                    pagePath = PathUtil.getPath(url);
                }
            }
            PerfTrace.begin("TabBar.switchTab");
            mTabBar.switchTab(pagePath);
            mTabBar.setVisibility(View.VISIBLE);
            PerfTrace.end("TabBar.switchTab");
        }
    }

    @Override
    public void switchTabByClick(OpenParams openParams) {
        realSwitchTab(openParams);
        String path;
        if (MSCHornRollbackConfig.isRollbackSwitchTabByClick()) {
            path = openParams.getOriginUrlOrUrl();
        } else {
            path = PathUtil.getPath(openParams.getOriginUrlOrUrl());
        }
        PerfTrace.begin("TabBar.switchTab");
        mTabBar.switchTab(path);
        mTabBar.setVisibility(View.VISIBLE);
        PerfTrace.end("TabBar.switchTab");
    }

    private void realSwitchTab(OpenParams openParams) {
        String originUrlOrUrl = openParams.getOriginUrlOrUrl();
        String url = openParams.url;
        Page targetPage = getOrCreatePage(originUrlOrUrl, url, openParams.isTabDerived, openParams.getRouteTime());

        boolean changeTab = (mCurPage != targetPage);
        if (changeTab) {
            mRuntime.jsErrorRecorder.popPage(mCurPagePath, String.valueOf(mCurPage.getViewId()));
            mCurPage.onHide(IPageLifecycleInterceptor.TYPE_PAGE_PAUSE_CAUSE_SWITCH_TAB);
            mCurPage.setVisibility(GONE);
            if (MSCHornRollbackConfig.isRollbackTabPageRouteMappingFix()) {
                mRuntime.jsErrorRecorder.pushPage(mCurPagePath, String.valueOf(mCurPage.getViewId()));
            }
        }
        targetPage.setVisibility(VISIBLE);
        targetPage.onShow(openParams.openType);
        if (!MSCHornRollbackConfig.isRollbackTabPageRouteMappingFix()) {
            openParams.setUrl(targetPage.getRoutePath());
        }
        targetPage.onSwitchTabTo(openParams);
        mCurPagePath = openParams.url;
        mCurPage = targetPage;
        mRuntime.jsErrorRecorder.pushPage(mCurPagePath, String.valueOf(mCurPage.getViewId()));

        if (mCurPage != null) {
            IDataPrefetchModule dataPrefetchModule = mRuntime.getModule(IDataPrefetchModule.class);
            if (dataPrefetchModule != null) {
                dataPrefetchModule.attachToPage(openParams.getRouteId(), mCurPage.getViewId());
            }
            mCurPage.setRouteId(openParams.getRouteId());
        }
    }

    @Override
    public void onTabItemTap(String pagePath, String index, String text) {
        MSCExecutors.postOnUiThread(new Runnable() {
            @Override
            public void run() {
                JSONObject object = new JSONObject();
                try {
                    object.put("pagePath", pagePath);
                    object.put("index", index);
                    object.put("text", text);
                } catch (JSONException e) {
                    e.printStackTrace();
                }
                mRuntime.getJSModuleDelegate(PageListener.class).onTabItemTap(object, mCurPage.getViewId());
            }
        });
    }

    @Override
    public boolean tabClickable() {
        return mCurPage.tabClickable();
    }

    public TabBar getTabBar() {
        return mTabBar;
    }

    @Override
    public String getCurrentPagePath() {
        return mCurPagePath;
    }

    public Page getCurPage() {
        return mCurPage;
    }

    @Override
    public boolean isCustomTabPage() {
        return mIsCustomTabBar;
    }

    /**
     * 添加tab栏
     *
     * @param topLayout
     * @param bottomLayout
     */
    private void setUpTabBar(LinearLayout topLayout, LinearLayout bottomLayout) {
        // 自定义TabBar不展示客户端
        if (mRuntime.getMSCAppModule().isCustomTabBar()) {
            mTabBar = null;
            mIsCustomTabBar = true;
        } else {
            mTabBar = new TabBar(mContext, mRuntime.getMSCAppModule());
            mTabBar.setOnSwitchTabListener(this);
            if (mRuntime.getMSCAppModule().isTopTabBar()) {
                bottomLayout.setVisibility(GONE);
                topLayout.addView(mTabBar, new LayoutParams(LayoutParams.MATCH_PARENT,
                        LayoutParams.WRAP_CONTENT));
                //顶部tab需要留出导航栏+状态栏的高度
                RelativeLayout.LayoutParams lp = (LayoutParams) topLayout.getLayoutParams();
                lp.topMargin = DisplayUtil.getNavigationHeightWithStatusBar();
            } else {
                bottomLayout.setVisibility(VISIBLE);
                bottomLayout.addView(mTabBar, new LayoutParams(LayoutParams.MATCH_PARENT,
                        LayoutParams.WRAP_CONTENT));
            }
        }
    }

    private void initTabPage(Context context, String originUrl, String url, @Nullable PageInfoArray pageInfoArray, long routeTime, Boolean isTabDerived) {
        if (pageInfoArray != null) {
            pageIdsReuse = pageInfoArray.pageInfos;//缓存数据
        }

        //添加web页面
        mTabList = mRuntime.getMSCAppModule().getTabItemList();
        int len = (mTabList == null ? 0 : mTabList.size());
        mPages = new Page[len];

        Page page = getOrCreatePage(originUrl, url, isTabDerived, routeTime);
        mCurPagePath = page.getRoutePath();
        mCurPage = page;
    }

    @Override
    public void hideTabBar() {
        if (mTabBar != null) {
            mTabBar.setVisibility(GONE);
        }
    }

    private int getCurrentOpenedPageCount() {
        return openedPageCount;
    }

    private Page createPage(int index, String pagePath, long routeTime, String originPath) {
        PerfTrace.begin("createPage");
        int resumeId = getReloadViewId(index);
        boolean isFirstPage;
        isFirstPage = this.mIsFirstPage && getCurrentOpenedPageCount() == 0;
        boolean isFirstPageV2 = this.mIsFirstPageV2 && getCurrentOpenedPageCount() == 0;
        Page page = new Page(mRuntime, mController, mEventListener, pagePath, this, resumeId, routeTime, isFirstPage, originPath, isFirstPageV2);
        mPages[index] = page;
        openedPageCount += 1;
        // 缓存页面路径和页面实例映射关系，统一使用实际页面Url
        mPathPageMap.put(PathUtil.getPath(pagePath), page);
        mWebLayout.addView(page, new ViewGroup.LayoutParams(ViewGroup.LayoutParams.MATCH_PARENT,
                ViewGroup.LayoutParams.MATCH_PARENT));
        PerfTrace.end("createPage");
        return page;
    }

    private int getPageIndexWithPath(String url) {
        if (mTabList != null) {
            String pagePath = PathUtil.getPath(url);
            for (int i = 0; i < mTabList.size(); i++) {
                if (TextUtils.equals(mTabList.get(i).pagePath, pagePath)) {
                    return i;
                }
            }
        }
        MSCLog.w(TAG, "getPageIndexWithPath not matched", url, mTabList);
        // not found
        return -1;
    }

    private Page getOrCreatePage(String originUrl, String url, Boolean isTabDerived, long routeTime) {
        Page page;
        if (MSCHornRollbackConfig.isRollbackSetRouteMappingPersist()) {
            page = getPage(originUrl);
        } else {
            page = getPage(url);
        }
        if (page != null) {
            return page;
        }
        if (MSCHornRollbackConfig.isRollbackSetRouteMappingPersist()) {
            String finalUrl = originUrl;
            // 没创建过的页面，是否需要映射
            String pathIfRouted = mRuntime.getAppConfigModule().getRoutePathIfRouted(originUrl);
            if (!TextUtils.isEmpty(pathIfRouted)) {
                finalUrl = pathIfRouted;
                Page pageIfRouted = getPage(pathIfRouted);
                if (pageIfRouted != null) {
                    return pageIfRouted;
                }
            }
            page = createPage(getPageIndexWithPath(originUrl), finalUrl, routeTime, originUrl);
        } else {
            if (MSCHornRollbackConfig.isRollbackSetRouteMappingFix()) {
                isTabDerived = mRuntime.getAppConfigModule().isTabDerivedPersist(originUrl);
                if (isTabDerived == null) {
                    isTabDerived = mRuntime.getAppConfigModule().isTabDerived(originUrl);
                }
            }
            if (Boolean.FALSE.equals(isTabDerived)) {
                page = createPage(getPageIndexWithPath(url), url, routeTime, originUrl);
            } else {
                page = createPage(getPageIndexWithPath(originUrl), url, routeTime, originUrl);
            }
        }
        return page;
    }

    private Page getPage(String url) {
        String pagePath = PathUtil.getPath(url);
        return mPathPageMap.get(pagePath);
    }

    @Override
    public LayoutTransition getPopTransition() {
        return mCurPage.getPopTransition();
    }

    @Override
    public LayoutTransition getPushTransition() {
        return mCurPage.getPushTransition();
    }

    @Override
    public void onDestroy() {
        for (Page page : mPages) {
            if (page != null) {
                page.onDestroy();
            }
        }
    }

    @Override
    public void onShow(String openType) {
        mCurPage.onShow(openType);
    }

    @Override
    public void onHide(int cause) {
        mCurPage.onHide(cause);
    }

    @Override
    String getRoutePath() {
        return mCurPage.getRoutePath();
    }

    @Override
    int getViewId() {
        return mCurPage.getViewId();
    }

    @Override
    public IPageModule getPageModuleById(int id) {
        for (BasePage page : mPages) {
            if (page != null && id == page.getViewId()) {
                return page.getPageModuleById(id);
            }
        }
        return null;
    }

    public Page getPageById(int id) {
        for (Page page : mPages) {
            if (page != null && id == page.getViewId()) {
                return page;
            }
        }
        return null;
    }

    public Page getPage() {
        return getCurPage();
    }

    @Override
    protected PageInfoOne[] getPageInfos() {
        // 需要上次的未恢复完成也保留下来 用于二次恢复 未加载的页面path为空 无法直接从view中取
        PageInfoOne[] array = pageIdsReuse != null ? pageIdsReuse : new PageInfoOne[mPages.length];
        for (int i = 0; i < array.length; i++) {
            // 本次使用过程中未打开过的页面 直接使用上次的 使用过的补充viewId和path信息
            PageInfoOne pageInfo = array[i];
            if (pageInfo == null || pageInfo.viewId == NO_ID || pageInfo.path == null) {
                Page page = mPages[i];
                pageInfo = page != null ? page.getPageInfoOne() : new PageInfoOne();
            }
            if (pageInfo.viewId == getViewId()) {
                pageInfo.top = true;
            }
            array[i] = pageInfo;

        }
        return array;
    }

    /**
     * 一个页面只reload一次  pageIdsReuse中的用完即丢
     *
     * @param i
     * @return
     */
    private int getReloadViewId(int i) {
        if (pageIdsReuse == null) {
            return View.NO_ID;
        }
        if (i >= pageIdsReuse.length) {
            return View.NO_ID;
        }
        int viewId = pageIdsReuse[i].viewId;
//        if (pageIdsReuse[i].top) {
        // 消耗掉即将加载的栈顶页面的id
        pageIdsReuse[i] = null;//top已加载 忽略
//        }
        return viewId;
    }

    @Override
    public void setRouteTime(long routeTime) {
        super.setRouteTime(routeTime);
        mCurPage.setRouteTime(routeTime);
    }
}
