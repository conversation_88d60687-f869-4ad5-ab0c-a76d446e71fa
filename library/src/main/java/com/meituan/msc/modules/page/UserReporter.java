package com.meituan.msc.modules.page;

import android.text.TextUtils;

import com.meituan.msc.common.report.MetricsEntry;
import com.meituan.msc.common.utils.IntentUtil;
import com.meituan.msc.common.utils.PathUtil;
import com.meituan.msc.lib.BuildConfig;
import com.meituan.msc.modules.container.ContainerController;
import com.meituan.msc.modules.engine.MSCRuntime;
import com.meituan.msc.modules.reporter.CommonTags;
import com.meituan.msc.modules.reporter.MSCReporter;
import com.meituan.msc.modules.reporter.ReporterFields;

import java.util.Map;

/**
 * 用户视角监控上报指标
 * https://km.sankuai.com/collabpage/1975250891
 */
public class UserReporter extends MSCReporter {

    public static final String TAG_START_SCENE = "startScene";
    public static final String TAG_LAUNCH_DURATION = "launchDuration";
    public static final String TAG_PAGE_DURATION = "pageDuration";
    public static final String TAG_IS_LAUNCH_PAGE = "isLaunchPage";
    public static final String TAG_SR = "$sr";
    public static final String TAG_FFP_RATE = "ffpRate";
    public static final String TAG_OPEN_TYPE = "openType";
    public static final String TAG_F_TYPE = "fType";
    public static final String CONSTANT_PORTAL = "portal";
    public static final String CONSTANT_NAVIGATE_TO_MINI_PROGRAM = "navigateToMiniProgram";
    public static final String TAG_MMP_TO_MSC_INTERCEPT = "MMPToMSCIntercept";
    public static final String TAG_URI_LENGTH = "uriLength";
    public static final String TAG_WIDGET_INITIAL_DATA_LENGTH = "widgetInitialDataLength";
    public static final String TAG_TARGET_PATH_LENGTH = "targetPathLength";

    public static UserReporter create() {
        return new UserReporter();
    }

    private UserReporter() {
        this.commonTag(CommonTags.SDK_VERSION, BuildConfig.AAR_VERSION);
    }

    public void reportUserLaunchStart(String mscAppId, boolean isWidget, String pagePath, String startScene, boolean isMMPToMSCIntercept, int uriLength, int widgetInitialDataLength, int targetPathLength) {
        this.commonTag(CommonTags.TAG_MSC_APP_ID, mscAppId);
        pagePath = pagePath != null ? pagePath : "";
        String purePath = PathUtil.getPath(pagePath);
        MetricsEntry metricsEntry = record(ReporterFields.REPORT_USER_LAUNCH_START)
                .tag(CommonTags.TAG_WIDGET, String.valueOf(isWidget))
                .tag(CommonTags.TAG_PAGE_PATH, pagePath)
                .tag(CommonTags.TAG_PURE_PAGE_PATH, purePath)
                .tag(TAG_START_SCENE, startScene)
                .tag(TAG_MMP_TO_MSC_INTERCEPT, isMMPToMSCIntercept)
                .tag(TAG_URI_LENGTH, uriLength)
                .tag(TAG_TARGET_PATH_LENGTH, targetPathLength);
        if (widgetInitialDataLength >= 0) {
            metricsEntry.tag(TAG_WIDGET_INITIAL_DATA_LENGTH, widgetInitialDataLength);
        }
        metricsEntry.sendRealTime();
    }

    public void reportUserFoundationLoadSuccess(ContainerController controller) {
        String targetPath = controller.getMPTargetPath();
        MSCRuntime runtime = controller.getRuntime();
        if (TextUtils.isEmpty(targetPath) && runtime != null && runtime.getMSCAppModule() != null
                && runtime.getMSCAppModule().hasMetaInfo() && runtime.getAppConfigModule().hasConfig()) {
            targetPath = runtime.getMSCAppModule().getRootPath();
        }
        String finalTargetPath = targetPath != null ? targetPath : "";
        boolean fromMiniProgramApi = IntentUtil.getBooleanExtra(controller.getIntent(),
                ContainerController.START_FROM_MIN_PROGRAM, false);
        String startScene = fromMiniProgramApi ? UserReporter.CONSTANT_NAVIGATE_TO_MINI_PROGRAM :
                UserReporter.CONSTANT_PORTAL;
        long launchStartTime = controller.getLaunchStartTime();
        long launchDuration = launchStartTime > 0 ? System.currentTimeMillis() - launchStartTime : -1;
        reportUserFoundationLoadSuccess(controller.getAppId(), controller.isWidget(),
                finalTargetPath, startScene, launchDuration);
    }

    public void reportUserFoundationLoadSuccess(String mscAppId, boolean isWidget, String pagePath,
                                                String startScene, long launchDuration) {
        this.commonTag(CommonTags.TAG_MSC_APP_ID, mscAppId);
        pagePath = pagePath != null ? pagePath : "";
        String purePath = PathUtil.getPath(pagePath);
        record(ReporterFields.REPORT_USER_FOUNDATION_LOAD_SUCCESS)
                .tag(CommonTags.TAG_WIDGET, String.valueOf(isWidget))
                .tag(CommonTags.TAG_PAGE_PATH, pagePath)
                .tag(CommonTags.TAG_PURE_PAGE_PATH, purePath)
                .tag(TAG_START_SCENE, startScene)
                .tag(TAG_LAUNCH_DURATION, launchDuration)
                .sendRealTime();
    }

    public void reportUserPageStart(Map<String, Object> commonTags) {
        record(ReporterFields.REPORT_USER_PAGE_START)
                .tags(commonTags)
                .sendRealTime();
    }

    public void reportUserPageLoadSuccess(Map<String, Object> commonTags, String startScene,
                                          long launchDuration, long pageDuration) {
        record(ReporterFields.REPORT_USER_PAGE_LOAD_SUCCESS)
                .tags(commonTags)
                .tag(TAG_START_SCENE, startScene)
                .tag(TAG_LAUNCH_DURATION, launchDuration)
                .tag(TAG_PAGE_DURATION, pageDuration)
                .sendRealTime();
    }

    public void reportUserPageFFP(Map<String, Object> commonTags) {
        record(ReporterFields.REPORT_USER_PAGE_FFP)
                .tags(commonTags)
                .sendRealTime();
    }
}
