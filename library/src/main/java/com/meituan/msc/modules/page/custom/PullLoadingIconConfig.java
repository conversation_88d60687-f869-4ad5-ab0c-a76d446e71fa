package com.meituan.msc.modules.page.custom;

import android.support.annotation.NonNull;
import android.text.TextUtils;

import com.meituan.msc.common.utils.PathUtil;
import com.meituan.msc.modules.update.AppConfigModule;

import org.json.JSONObject;

/**
 * 支持自定义下拉loading图片配置
 * <a href="https://km.sankuai.com/collabpage/2527124100">小象-小程序自定义下拉刷新需求</a>
 * 支持小程序全局配置使用app.json和页面维度配置page.json
 */
public class PullLoadingIconConfig {
    private static final String TAG = "PullLoadingIconConfig";
    // 本地包内gif路径
    public String src;
    // gif渲染尺寸
    public JSONObject sizeObject;
    // 宽度 - 单位pt/dp(逻辑像素)
    public int width;
    // 高度 - 单位pt/dp(逻辑像素)
    public int height;

    /**
     * 配置形式：
     *   "mtCustomPullLoadingIcon": {
     *     	// 本地包内gif路径
     *     	"src": "pathTo/image.gif",
     *       // gif渲染尺寸
     *       "size": {
     *       		// 宽度 - 单位pt
     *       		"width": 96,
     *           // 高度 - 单位pt
     *           "height": 96
     *        }
     *     }
     * @param url 页面路径
     * @param appConfigModule AppConfigModule
     * @return 解析后的 PullLoadingIconConfig
     */
    public static PullLoadingIconConfig parsePullLoadingIcon(String url, AppConfigModule appConfigModule) {
        if (appConfigModule == null) {
            return null;
        }
        JSONObject windowConfig = appConfigModule.getWindowConfig();
        JSONObject pageConfig = appConfigModule.getPages();
        // 应用全局、页面全局均未定义
        if (windowConfig == null && pageConfig == null) {
            return null;
        }
        PullLoadingIconConfig pullLoadingIconConfig;
        if (pageConfig != null) {
            JSONObject item = pageConfig.optJSONObject(PathUtil.getPath(url));
            // 页面级配置粒度更细, 页面配置优先
            pullLoadingIconConfig = parseInner(item);
            if (pullLoadingIconConfig != null) {
                return pullLoadingIconConfig;
            }
        }
        // 使用全局配置
        return windowConfig != null ? parseInner(windowConfig) : null;
    }

    private static PullLoadingIconConfig parseInner(JSONObject item) {
        if (item == null) {
            return null;
        }

        JSONObject mtCustomPullLoadingIcon = item.optJSONObject("mtCustomPullLoadingIcon");
        if (mtCustomPullLoadingIcon == null) {
            return null;
        }

        PullLoadingIconConfig pullLoadingIconConfig = new PullLoadingIconConfig();
        pullLoadingIconConfig.src = mtCustomPullLoadingIcon.optString("src");
        if (TextUtils.isEmpty(pullLoadingIconConfig.src)) {
            return null;
        }
        pullLoadingIconConfig.sizeObject = mtCustomPullLoadingIcon.optJSONObject("size");
        if (pullLoadingIconConfig.sizeObject != null) {
            pullLoadingIconConfig.width = pullLoadingIconConfig.sizeObject.optInt("width");
            pullLoadingIconConfig.height = pullLoadingIconConfig.sizeObject.optInt("height");
        }
        return pullLoadingIconConfig;
    }

    @Override
    public String toString() {
        return "PullLoadingIconConfig, src:" + src + ", width:" + width + ", height:" + height;
    }
}
