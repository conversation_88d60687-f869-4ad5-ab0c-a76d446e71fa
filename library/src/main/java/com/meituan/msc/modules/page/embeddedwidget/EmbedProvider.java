package com.meituan.msc.modules.page.embeddedwidget;

import com.meituan.mtwebkit.internal.hyper.SameLayerClient;
import com.meituan.mtwebkit.internal.hyper.SameLayerManager;
import com.meituan.mtwebkit.internal.hyper.SameLayerWidget;

import java.util.Arrays;
import java.util.HashSet;

/**
 * Created by letty on 2021/8/24.
 **/
public class EmbedProvider implements SameLayerManager.Provider {
//    public static final String[] supportWidgets = new String[]{"map", "video"};
    //同层渲染支持的组件
    public static final String[] supportWidgets = new String[]{"map"};
    public static final HashSet<String> supportWidgetsSet = new HashSet<>(Arrays.asList(supportWidgets));


    @Override
    public SameLayerClient create(SameLayerWidget widget) {
        return MPWidgetFactory.getInstance().createWidgetClient(widget);
    }

    @Override
    public boolean isSupport(String params) {
        //todo
//                return params.contains("map");
        return true;
    }
}
