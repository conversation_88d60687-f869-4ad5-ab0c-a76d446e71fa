package com.meituan.msc.modules.page.embeddedwidget;

import android.view.View;

import com.meituan.msc.common.config.MSCConfig;
import com.meituan.mtwebkit.MTWebView;
import com.meituan.mtwebkit.internal.hyper.SameLayerManager;

/**
 * Created by letty on 2021/7/2.
 **/
public class EmbeddedManager {

    public static void registerEmbed(MTWebView webView) {
        SameLayerManager.bindProvider(webView, new EmbedProvider());
    }

    public static boolean supportEmbed(View webView) {
        if (webView instanceof MTWebView) {
            return MSCConfig.isEnableSameLayerAndroid() && SameLayerManager.checkIfSupportSameLayer((MTWebView) webView);
        }
        return false;
    }

}
