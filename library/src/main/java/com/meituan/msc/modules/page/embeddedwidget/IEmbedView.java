package com.meituan.msc.modules.page.embeddedwidget;

import android.view.MotionEvent;
import android.view.Surface;

/**
 * Created by letty on 2021/8/19.
 **/
public interface IEmbedView extends IEmbed{

    void setSurface(Surface surface);

    void onSizeChanged(Surface surface, int width, int height);

    boolean dispatchTouchEvent(MotionEvent ev);

    void onVisibilityChanged(boolean visibility);

    /**
     * 同层渲染画布销毁
     *
     * 不等于组件销毁
     */
    void onDestroySurface();

}
