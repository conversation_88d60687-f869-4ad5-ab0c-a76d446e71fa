package com.meituan.msc.modules.page.embeddedwidget;

import android.graphics.Rect;
import android.view.MotionEvent;
import android.view.Surface;

import com.meituan.mtwebkit.internal.hyper.SameLayerWidget;

/**
 * Created by letty on 2021/7/5.
 **/
public interface IMPWidget extends SameLayerWidget, IMPInfo {

    IMPInfo getMpInfo();

    void setMpInfo(IMPInfo mpInfo);

    void bindWidgetClient(IMPWidgetClient mpWidgetClient);

    void onCreate(String attributes);

    void onSurfaceCreated(Surface surface);

    void onSizeChanged(int width, int height);

    void onRectChanged(Rect rect);

    void onTouchEvent(MotionEvent event);

    void onVisibilityChanged(boolean visibility);

    void onSurfaceDestroy(Surface surface);

    void onAttributesChanged(String attributes);

    void onDestroy();
}
