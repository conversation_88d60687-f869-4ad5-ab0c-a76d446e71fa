package com.meituan.msc.modules.page.embeddedwidget;


import com.meituan.mtwebkit.internal.hyper.SameLayerClient;
import com.meituan.mtwebkit.internal.hyper.SameLayerWidget;

import java.util.Map;

/**
 * Created by letty on 2021/3/17.
 **/
public interface IMPWidgetClient extends SameLayerClient, IMPInfo, SameLayerWidget {
    Map getAttributes();

    void bindMPWidget(IMPWidget mpWidget);

    boolean hasSurfaceCreated();
}

