package com.meituan.msc.modules.page.embeddedwidget;

import org.json.JSONObject;

import java.util.Map;

/**
 * Created by letty on 2021/7/5.
 **/
public class MPConstant {

    public static final String MP_VIEW_NAME = "mpView_name";
    public static final String MP_VIEW_VIEW_ID = "mpView_viewId";
    public static final String MP_VIEW_PAGE_ID = "mpView_pageId";
    public static final String MP_VIEW_APP_ID = "mpView_appId";
    public static final String MP_VIEW_EMBED_RENDER = "mpView_embed_render";
    public static final String MP_VIEW_EMBED_FIELD_DOWNGRADE = "sameLayerCode";


//    public static final String MP_EMBED_COMPONENT_RENDER = "msc.embed.component.render";
//    public static final String MP_EMBED_RENDER = "msc.embed.render";
//    public static final String MP_EMBED_RENDER_SUCCESS = "msc.embed.render.success";
//
//    public static final String MP_EMBED_RENDER_ERROR = "msc.embed.render.error";
//
    public static final String MP_EMBED_RENDER_BIND = "msc.embed.render.bind";
//
//    public static final String MP_EMBED_WEBVIEW_NOT_SUPPORTED = "msc.embed.webview.not.supported";
//    public static final String MP_FIELD_COMPONENT = "component";

    public static final String MP_FIELD_PREFER_EMBED = "preferEmbed";


    /**
     * 浏览器将所有的key 处理成了小写字母
     *
     * @param attributes
     * @param key
     * @return
     */
    public static String extractFromWebAttributes(Map attributes, String key) {
        return String.valueOf(attributes.get(key.toLowerCase()));
    }

    public static boolean isSameLayerDowngrade(JSONObject jsonObject) {
        return jsonObject != null && jsonObject.has(MP_VIEW_EMBED_FIELD_DOWNGRADE);
    }
}
