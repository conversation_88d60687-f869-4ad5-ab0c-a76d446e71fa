package com.meituan.msc.modules.page.embeddedwidget;

import android.graphics.Rect;
import android.view.MotionEvent;
import android.view.Surface;

import com.meituan.msc.modules.reporter.MSCLog;
import com.meituan.mtwebkit.MTValueCallback;

/**
 * Created by letty on 2021/7/5.
 **/
public class MPWidget implements IMPWidget {

    IMPInfo mIMPInfo;
    IMPWidgetClient mIMPWidgetClient;
    IEmbedView mEmbedView;
    Surface mSurface;
    boolean isWidgetClientReleased;

    public MPWidget() {
    }

    public MPWidget setEmbedView(IEmbedView embedView) {
        mEmbedView = embedView;
        embedView.setMPWidget(this);
        return this;
    }

    public boolean isWidgetClientReady() {
        return mIMPWidgetClient != null;
    }

    @Override
    public IMPInfo getMpInfo() {
        return mIMPInfo;
    }

    @Override
    public void setMpInfo(IMPInfo mpInfo) {
        mIMPInfo = mpInfo;
    }

    @Override
    public void bindWidgetClient(IMPWidgetClient mpWidgetClient) {
        isWidgetClientReleased = false;
        mIMPWidgetClient = mpWidgetClient;
    }

    @Override
    public String getMPViewName() {
        return mIMPInfo != null ? mIMPInfo.getMPViewName() : null;
    }

    @Override
    public String getMPAppId() {
        return mIMPInfo != null ? mIMPInfo.getMPAppId() : null;
    }

    @Override
    public int getMPPageId() {
        return mIMPInfo != null ? mIMPInfo.getMPPageId() : 0;
    }

    @Override
    public String getMPContainerId() {
        return mIMPInfo != null ? mIMPInfo.getMPContainerId() : null;
    }

    @Override
    public void evaluateJavaScript(String script, MTValueCallback<String> callback) {
        if (mIMPWidgetClient != null) {
            mIMPWidgetClient.evaluateJavaScript(script, callback);
        }
    }

    @Override
    public void onCreate(String attributes) {

    }

    @Override
    public void onSurfaceCreated(Surface surface) {
        MSCLog.d("MPMapView onSurfaceCreated ", this, surface, mEmbedView);
        mSurface = surface;
        if (mEmbedView != null) {
            mEmbedView.setSurface(surface);
        }
    }

    @Override
    public void onSizeChanged(int width, int height) {
        if (mEmbedView != null) {
            mEmbedView.onSizeChanged(mSurface, width, height);
        }
    }

    @Override
    public void onRectChanged(Rect rect) {
        //ignore now
    }

    @Override
    public void onTouchEvent(MotionEvent event) {
        if (mEmbedView != null) {
            mEmbedView.dispatchTouchEvent(event);
        }
    }

    @Override
    public void onVisibilityChanged(boolean visibility) {
        mEmbedView.onVisibilityChanged(visibility);
    }

    /**
     * 触发surface销毁回调
     *
     * 此时不做组件销毁，组件销毁仅发生在组件移除（页面移除/前端remove移除）
     * 新的surface/WidgetClient 重新创建，会触发同一组件的二次绑定
     *
     * @param surface
     */
    @Override
    public void onSurfaceDestroy(Surface surface) {
        if (mEmbedView != null) {
            mEmbedView.onDestroySurface();
        }
        mSurface = null;
    }

    @Override
    public void onAttributesChanged(String attributes) {

    }

    @Override
    public void onDestroy() {
        mIMPWidgetClient = null;
        isWidgetClientReleased = true;
    }

    public boolean isWidgetClientReleased() {
        return isWidgetClientReleased;
    }

}
