package com.meituan.msc.modules.page.embeddedwidget;

import android.graphics.Rect;
import android.text.TextUtils;
import android.view.MotionEvent;
import android.view.Surface;

import com.meituan.msc.modules.reporter.MSCLog;
import com.meituan.msc.common.utils.JsonUtil;
import com.meituan.mtwebkit.MTValueCallback;
import com.meituan.mtwebkit.internal.hyper.SameLayerWidget;

import java.util.Map;

/**
 * Created by letty on 2021/7/5.
 **/
public class MPWidgetClient implements IMPWidgetClient {
    private final String TAG = "MPWidgetClient@" + hashCode();
    private String mViewName;
    private String mAppId, mContainerId;
    private int mPageId;
    private Map mAttributes;
    private IMPWidget mMPWidget;
    private Surface mSurface;
    private SameLayerWidget mSameLayerWidget;
    private Rect mRect;
    private int mWidth, mHeight;

    public MPWidgetClient(SameLayerWidget widget) {
        mSameLayerWidget = widget;
    }

    private void log(Object... objects) {
        MSCLog.d(TAG, objects);
    }

    @Override
    public void onCreate(String attributes) {
        log("onCreate attributes:", attributes);

        if (!extractAttributes(attributes)) {
            MSCLog.i(TAG, "onCreate extractAttributes fail! insert widget error");
            return;
        }
        MPWidgetClientManager.onWidgetClientCreate(this);

    }

    private boolean extractAttributes(String attributes) {
        if (TextUtils.isEmpty(attributes)) {
            MSCLog.i(TAG, "extractAttributes empty attributes");
            return false;
        }
        mAttributes = JsonUtil.toMap(attributes);
        if (mAttributes == null) {
            MSCLog.i(TAG, "extractAttributes invalid attributes");
            return false;
        }

        mViewName = MPConstant.extractFromWebAttributes(mAttributes, MPConstant.MP_VIEW_NAME);
        mAppId = MPConstant.extractFromWebAttributes(mAttributes, MPConstant.MP_VIEW_APP_ID);
        try {
            mPageId = Integer.parseInt(MPConstant.extractFromWebAttributes(mAttributes, MPConstant.MP_VIEW_PAGE_ID.toLowerCase()));
        } catch (Exception e) {
            //ignore
        }
        mContainerId = (String) mAttributes.get(MPConstant.MP_VIEW_VIEW_ID.toLowerCase());

        if (TextUtils.isEmpty(mViewName) || TextUtils.isEmpty(mAppId)
                || TextUtils.isEmpty(mContainerId)) {
            MSCLog.i(TAG, "extractAttributes illegal arguments, " +
                    "mViewName:", mViewName, ",mAppId:", mAppId, ",mPageId:", mPageId, ",mContainerId:", mContainerId);
            return false;
        }
        //todo notify insert error
        return true;
    }

    @Override
    public void onSurfaceCreated(Surface surface) {
        log("onSurfaceCreated " + surface);

        mSurface = surface;
        notifySurfaceCreated();
    }

    @Override
    public void onRectChanged(Rect rect) {
        log("onRectChanged ", mRect, rect);

        // 可能在surface创建/组件绑定之前发生，因此需要记录大小
        mRect = rect;
        int width = rect.width();
        int height = rect.height();
        boolean changed = false;
        if (width != mWidth || height != mHeight) {
            mWidth = width;
            mHeight = height;
            changed = true;
        }
        log("onRectChanged ", changed);

        if (mMPWidget != null) {
            mMPWidget.onRectChanged(mRect);
            if (changed) {
                mMPWidget.onSizeChanged(mWidth, mHeight);
            }
        }
    }

    @Override
    public void onTouchEvent(MotionEvent event) {
//        log("onTouchEvent " + event);

        if (mMPWidget != null) {
            mMPWidget.onTouchEvent(event);
        }
    }

    @Override
    public void onVisibilityChanged(boolean visibility) {
        log("onVisibilityChanged " + visibility);

        if (mMPWidget != null) {
            mMPWidget.onVisibilityChanged(visibility);
        }
    }

    /**
     * 这个生命周期surface返回就是个null
     *
     * @param surface
     */
    @Override
    public void onSurfaceDestroy(Surface surface) {
        log("onSurfaceDestroy " + surface);

//        mSurface = surface;
        if (mMPWidget != null) {
            mMPWidget.onSurfaceDestroy(surface);
        }

    }

    @Override
    public void onAttributesChanged(String attributes) {
        log("onAttributesChanged " + attributes);

        if (mMPWidget != null) {
            mMPWidget.onAttributesChanged(attributes);
        }
    }

    @Override
    public void onDestroy() {
        log("onDestroy: " + this);

        mSurface = null;
        mRect = null;
        if (mMPWidget != null) {
            mMPWidget.onDestroy();
            mMPWidget = null;
        }
        MPWidgetClientManager.onWidgetClientDestroy(this);
    }

    @Override
    public String getMPViewName() {
        return mViewName;
    }

    @Override
    public String getMPAppId() {
        return mAppId;
    }

    @Override
    public int getMPPageId() {
        return mPageId;
    }

    @Override
    public String getMPContainerId() {
        return mContainerId;
    }

    @Override
    public Map getAttributes() {
        return mAttributes;
    }

    @Override
    public void bindMPWidget(IMPWidget mpWidget) {
        log("bindMPWidget: " + mpWidget);
        mMPWidget = mpWidget;
        mpWidget.bindWidgetClient(this);
        notifySurfaceCreated();
    }

    private void notifySurfaceCreated() {
        if (mMPWidget != null && mSurface != null) {
            mMPWidget.onSurfaceCreated(mSurface);
            if (mRect != null) {
                mMPWidget.onRectChanged(mRect);
                mMPWidget.onSizeChanged(mWidth, mHeight);
            }
        }
    }

    @Override
    public void evaluateJavaScript(String script, MTValueCallback<String> callback) {
        if (mSameLayerWidget != null) {
            mSameLayerWidget.evaluateJavaScript(script, callback);
        }
    }

    @Override
    public boolean hasSurfaceCreated() {
        return mSurface != null;
    }

}
