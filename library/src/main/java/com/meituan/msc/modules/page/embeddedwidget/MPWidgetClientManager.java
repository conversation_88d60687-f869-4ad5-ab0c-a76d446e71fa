package com.meituan.msc.modules.page.embeddedwidget;

import android.os.Handler;
import android.os.Looper;
import android.support.annotation.Nullable;
import android.text.TextUtils;

import com.meituan.msc.common.utils.HashMapHelper;
import com.meituan.msc.modules.devtools.DebugHelper;
import com.meituan.msc.common.framework.Callback;
import com.meituan.msc.modules.page.render.AppPageReporter;
import com.meituan.msc.modules.reporter.MSCLog;

import java.util.HashMap;
import java.util.Map;
import java.util.concurrent.ConcurrentHashMap;

/**
 * Created by letty on 2021/7/5.
 **/
public class MPWidgetClientManager {

    public static volatile ConcurrentHashMap<String,
            ConcurrentHashMap<Integer, ConcurrentHashMap<String, IMPWidgetClient>>> globalWidgetClients = new ConcurrentHashMap<>();

    //which thread is called
    public static void onWidgetClientCreate(IMPWidgetClient widgetClient) {
        String appId = widgetClient.getMPAppId();
        if (TextUtils.isEmpty(appId)) {
            MSCLog.i("MPWidgetClientManager", "onWidgetClientCreate appId invalid!");
            return;
        }
        // 使用appId获取pageMap
        ConcurrentHashMap<Integer, ConcurrentHashMap<String, IMPWidgetClient>> pageMap = globalWidgetClients.get(appId);
        if (pageMap == null) {
            pageMap = new ConcurrentHashMap<>();
            globalWidgetClients.put(appId, pageMap);
        }
        // 使用pageId获取viewMap
        int pageId = widgetClient.getMPPageId();
        ConcurrentHashMap<String, IMPWidgetClient> viewMap = pageMap.get(pageId);
        if (viewMap == null) {
            viewMap = new ConcurrentHashMap<>();
            pageMap.put(pageId, viewMap);
        }
        String mpContainerId = widgetClient.getMPContainerId();
        if (TextUtils.isEmpty(mpContainerId)) {
            MSCLog.i("MPWidgetClientManager", "onWidgetClientCreate mpContainerId invalid!");
            return;
        }
        viewMap.put(mpContainerId, widgetClient);
    }

    public static void onWidgetClientDestroy(IMPWidgetClient widgetClient) {
        if (widgetClient != null && !TextUtils.isEmpty(widgetClient.getMPAppId())) {
            Map<Integer, ConcurrentHashMap<String, IMPWidgetClient>> pageMap = globalWidgetClients.get(widgetClient.getMPAppId());
            if (pageMap != null) {
                Map<String, IMPWidgetClient> viewMap = pageMap.get(widgetClient.getMPPageId());
                if (viewMap != null && !TextUtils.isEmpty(widgetClient.getMPContainerId())) {
                    viewMap.remove(widgetClient.getMPContainerId());
                }
            }
        }
    }

    public static void getMPWidgetWithClientWithRetry(IMPWidget widget, Callback<Void> callback, AppPageReporter appPageReporter) {
        getMPWidgetWithClientWithRetry(widget, 25, 200, callback, appPageReporter);
    }

    static Handler mHandler = new Handler(Looper.getMainLooper());

    public static void getMPWidgetWithClientWithRetry(IMPWidget widget,
                                                      final int maxTimes,
                                                      final int interval,
                                                      Callback<Void> callback,
                                                      AppPageReporter appPageReporter) {
        if (widget == null) {
            notifyEmbedError(widget, appPageReporter,callback, "empty widget");
            return;
        }
        if (DebugHelper.forceSameLayerErrorDowngrade) {
            notifyEmbedError(widget, appPageReporter,callback, "test forceSameLayerErrorDowngrade");
            return;
        }

        IMPWidgetClient widgetClient = getWidgetClient(widget.getMpInfo());
        if (widgetClient != null && widgetClient.hasSurfaceCreated()) {
            reportEmbedBindEnd(widget, appPageReporter,true, null);
            callback.onSuccess(null);
            widgetClient.bindMPWidget(widget);
        } else if (maxTimes == 0) {
            notifyEmbedError(widget, appPageReporter,callback, "not found embed object for 5s");
        } else {
            mHandler.postDelayed(new Runnable() {
                @Override
                public void run() {
                    getMPWidgetWithClientWithRetry(widget, maxTimes - 1, interval, callback, appPageReporter);
                }
            }, interval);
        }
    }

    public static void reportEmbedBindEnd(@Nullable IMPWidget mpWidget, AppPageReporter appPageReporter, boolean success, String errMsg) {
        if (appPageReporter != null) {
            HashMap<String, Object> map = HashMapHelper.<String, Object>of("err", errMsg,
                    "component", mpWidget != null ? mpWidget.getMpInfo().getMPViewName() : "",
                    "isRebind", mpWidget instanceof MPWidget ? ((MPWidget) mpWidget).isWidgetClientReleased() : "undefine"
            );
            if (success) {
                appPageReporter.record(MPConstant.MP_EMBED_RENDER_BIND).tags(map).tag("state", "success").sendDelay();
            } else {
                appPageReporter.record(MPConstant.MP_EMBED_RENDER_BIND).tags(map).tag("state", "fail").sendDelay();
            }
        }
    }

    public static void notifyEmbedError(@Nullable IMPWidget mpWidget, AppPageReporter appPageReporter, Callback callback,  String errMsg) {
        callback.onFail(errMsg, null);
        reportEmbedBindEnd(mpWidget, appPageReporter,false, errMsg);
    }

    private static IMPWidgetClient getWidgetClient(IMPInfo mpInfo) {
        if (mpInfo != null && !TextUtils.isEmpty(mpInfo.getMPAppId())) {
            Map<Integer, ConcurrentHashMap<String, IMPWidgetClient>> pageMap = globalWidgetClients.get(mpInfo.getMPAppId());
            if (pageMap != null) {
                Map<String, IMPWidgetClient> viewMap = pageMap.get(mpInfo.getMPPageId());
                if (viewMap != null && !TextUtils.isEmpty(mpInfo.getMPContainerId())) {
                    return viewMap.get(mpInfo.getMPContainerId());
                }
            }
        }
        return null;
    }


}
