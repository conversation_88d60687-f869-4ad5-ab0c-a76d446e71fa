package com.meituan.msc.modules.page.embeddedwidget;

import com.meituan.mtwebkit.internal.hyper.SameLayerWidget;

/**
 * Created by letty on 2021/3/18.
 **/
public class MPWidgetFactory implements IEmbeddedWidgetClientFactory {

    private static volatile MPWidgetFactory sInstance;

    public static MPWidgetFactory getInstance() {
        if (sInstance == null) {
            synchronized (MPWidgetFactory.class) {
                if (sInstance == null) {
                    sInstance = new MPWidgetFactory();
                }
            }
        }
        return sInstance;
    }

    private MPWidgetFactory() {

    }

    @Override
    public IMPWidgetClient createWidgetClient(SameLayerWidget widget) {
        return new MPWidgetClient(widget);
    }
}
