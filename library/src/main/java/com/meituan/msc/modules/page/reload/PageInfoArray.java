package com.meituan.msc.modules.page.reload;

import android.view.View;

/**
 * 记录页面信息，用于页面恢复使用
 */
public class PageInfoArray {
//    @Override
//    public String toString() {
//        return "PageInfo{" +
//                "currentPath='" + currentPath + '\'' +
//                ", currentId=" + currentId +
//                ", pageInfos=" + Arrays.toString(pageInfos) +
//                '}';
//    }

    public String currentPath;
    public int currentId;
    public PageInfoOne[] pageInfos;


    public static int getReloadPageId(PageInfoArray array) {
        return array != null ? array.currentId : View.NO_ID;
    }
}
