package com.meituan.msc.modules.page.reload;

import android.text.TextUtils;
import android.util.Pair;

import com.meituan.msc.common.utils.MPConcurrentHashMap;
import com.meituan.msc.modules.page.PageManager;

import java.util.Iterator;
import java.util.Map;
import java.util.Set;
import java.util.Stack;

/**
 * //fixme 重建这块儿逻辑需要重构
 * 页面栈信息内存全局缓存，生命周期与App同步
 */
public class PageStackGlobalCache {
    /**
     * 缓存小程序页面栈信息，Activity重建场景使用
     * <String, Pair<String, Stack<PageInfoArray>>
     * <Activity, Pair<appId, Stack<PageInfoArray>>
     */
    protected final MPConcurrentHashMap<String, Pair<String, Stack<PageInfoArray>>> memCacheGlobal = new MPConcurrentHashMap<>();
    private static final PageStackGlobalCache instance = new PageStackGlobalCache();

    public static PageStackGlobalCache getGlobalInstance() {
        return instance;
    }

    /**
     * cache page stacks for global instance
     *
     * @param mPageManager
     * @param stackSavedFlag
     */
    public void cachePageStackForGlobalInstance(PageStackWatchDog watchDog, String appId,
                                                PageManager mPageManager, String stackSavedFlag) {
        if (mPageManager == null || stackSavedFlag == null) {
            return;
        }
        Stack<PageInfoArray> pageLists = mPageManager.cachePageStackInfos();
        if (watchDog != null) {
            pageLists = watchDog.mergeStack(pageLists);
        }

        Pair<String, Stack<PageInfoArray>> stackPair = new Pair<>(appId, pageLists);
        PageStackGlobalCache.getGlobalInstance().memCacheGlobal.put(stackSavedFlag, stackPair);

    }

    /**
     * only used for Global Instance
     *
     * @param stackSavedFlag clean Global cache
     */
    public void cleanStackForGlobal(String stackSavedFlag) {
        if (stackSavedFlag != null) {
            PageStackGlobalCache.getGlobalInstance().memCacheGlobal.remove(stackSavedFlag);
        }
    }

    /**
     * 引擎销毁时移除缓存的小程序页面栈信息，解决Activity重建场景下JSError page stack is empty问题
     *
     * @param appId appId
     */
    public void cleanStackForGlobalByAppId(String appId) {
        MPConcurrentHashMap<String, Pair<String, Stack<PageInfoArray>>> memCacheGlobal =
                PageStackGlobalCache.getGlobalInstance().memCacheGlobal;
        Set<Map.Entry<String, Pair<String, Stack<PageInfoArray>>>> entries = memCacheGlobal.entrySet();
        Iterator<Map.Entry<String, Pair<String, Stack<PageInfoArray>>>> iterator = entries.iterator();
        while (iterator.hasNext()) {
            Map.Entry<String, Pair<String, Stack<PageInfoArray>>> next = iterator.next();
            Pair<String, Stack<PageInfoArray>> pair = next.getValue();
            if (TextUtils.equals(pair.first, appId)) {
                iterator.remove();
            }
        }
    }
}

