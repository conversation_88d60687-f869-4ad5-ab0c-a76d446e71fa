package com.meituan.msc.modules.page.reload;

import android.support.annotation.NonNull;
import android.text.TextUtils;
import android.util.Pair;

import com.meituan.msc.modules.api.msi.navigation.BizNavigationExtraParams;

import java.util.Stack;

public class PageStackWatchDog {
    private Stack<PageInfoArray> memCache;

    private PageStackWatchDog() {
    }

    /**
     * used for onCreate
     *
     * @param stackSavedFlag
     * @return
     */
    public static PageStackWatchDog newWatchDog(String stackSavedFlag) {
        PageStackWatchDog pageStackWatchDog = new PageStackWatchDog();
        // TODO: 2023/2/22 tianbin stackSavedFlag从代码逻辑看不会为空，先兜底保证不崩溃
        // https://ones.sankuai.com/ones/product/31464/workItem/defect/detail/74693268?activeTabName=first
        if (!TextUtils.isEmpty(stackSavedFlag)) {
            Pair<String, Stack<PageInfoArray>> pair = PageStackGlobalCache.getGlobalInstance().memCacheGlobal.get(stackSavedFlag);
            if (pair != null) {
                pageStackWatchDog.memCache = pair.second;
            }
        }
        return pageStackWatchDog;
    }

    public boolean canReLoad() {
        return memCache != null && !memCache.isEmpty();
    }

    /**
     * switchTab、relaunch 等清栈行为 直接清理任务栈、
     * <p>
     * 两种场景，启动进入/api调用
     */
    public void popAllStack() {
        if (memCache == null) return;
        while (!memCache.empty()) {
            memCache.pop();
        }
    }

    /**
     * 把正在使用的的页面信息和之前保留的页面信息进行合并
     * @param appendCache
     * @return
     */
    public Stack<PageInfoArray> mergeStack(Stack<PageInfoArray> appendCache) {
        if (memCache == null || memCache.empty()) return appendCache;
        if (appendCache == null || appendCache.empty()) return memCache;
        memCache.addAll(appendCache);
        return memCache;
    }

    public void reloadTopOfStack(IPageStackLoader pageStackReLoader, long routeTime, @NonNull BizNavigationExtraParams bizNavigationExtraParams) {
        if (!canReLoad()) return;
        PageInfoArray path = memCache.pop();
        pageStackReLoader.reloadTopOfStack(path, routeTime, bizNavigationExtraParams);
    }

    /**
     * Page re-loader
     */
    public interface IPageStackLoader {
        void reloadTopOfStack(PageInfoArray pageInfoArray, long routeTime, @NonNull BizNavigationExtraParams bizNavigationExtraParams);
    }
}
