package com.meituan.msc.modules.page.render;

import static com.meituan.msc.common.utils.Constants.SG_STORE_PAGE_PATH;
import static com.meituan.msc.modules.reporter.CommonTags.TAG_BIZ_TAGS_FOR_PAGE;
import static com.meituan.msc.modules.reporter.CommonTags.TAG_CHECK_UPDATE_MODE;
import static com.meituan.msc.modules.reporter.CommonTags.TAG_LAUNCH_TASKS_EXECUTE_STATES;
import static com.meituan.msc.modules.reporter.CommonTags.TAG_PAGE_PATH;
import static com.meituan.msc.modules.reporter.CommonTags.TAG_PAGE_START_FROM_APPLICATION_START;
import static com.meituan.msc.modules.reporter.CommonTags.TAG_PKG_MODE;
import static com.meituan.msc.modules.reporter.CommonTags.TAG_PKG_MODE_DETAIL;
import static com.meituan.msc.modules.reporter.CommonTags.TAG_PRELOAD_FROM_APPLICATION_START;
import static com.meituan.msc.modules.reporter.CommonTags.TAG_RENDER_ACTIONS;
import static com.meituan.msc.modules.reporter.CommonTags.TAG_RUNTIME_SOURCE;
import static com.meituan.msc.modules.reporter.CommonTags.TAG_RUNTIME_STATE_BEFORE_LAUNCH;
import static com.meituan.msc.modules.reporter.ReporterFields.MSC_FE_PAGE_FMP;
import static com.meituan.msc.modules.reporter.ReporterFields.MSC_FE_PAGE_FST;
import static com.meituan.msc.modules.reporter.ReporterFields.REPORT_ATTACH_TO_TIME_ATTACH_CALLBACK_DURATION;
import static com.meituan.msc.modules.reporter.ReporterFields.REPORT_FFP_END_TO_ATTACH_CALLBACK_DURATION;

import android.content.Context;
import android.content.Intent;
import android.net.Uri;
import android.support.annotation.NonNull;
import android.text.TextUtils;
import android.view.ViewGroup;

import com.meituan.android.common.weaver.interfaces.ffp.FFPPageInfo;
import com.meituan.android.common.weaver.interfaces.ffp.FFPReportListener;
import com.meituan.android.common.weaver.interfaces.ffp.TimedAttachDataListener;
import com.meituan.met.mercury.load.bean.DDLoadPhaseData;
import com.meituan.msc.common.aov_task.ExecuteStatus;
import com.meituan.msc.common.aov_task.TaskManager;
import com.meituan.msc.common.aov_task.task.ITask;
import com.meituan.msc.common.config.MSCConfig;
import com.meituan.msc.common.executor.MSCExecutors;
import com.meituan.msc.common.perf.PerfEventConstant;
import com.meituan.msc.common.report.MetricsEntry;
import com.meituan.msc.common.support.java.util.concurrent.CompletableFuture;
import com.meituan.msc.common.utils.Constants;
import com.meituan.msc.common.utils.IntentUtil;
import com.meituan.msc.common.utils.JsonUtil;
import com.meituan.msc.common.utils.MSCTraceUtil;
import com.meituan.msc.common.utils.UrlUtils;
import com.meituan.msc.extern.MSCEnvHelper;
import com.meituan.msc.jse.bridge.ConversionUtil;
import com.meituan.msc.jse.bridge.LoadJSCodeCacheCallback;
import com.meituan.msc.lib.interfaces.container.MSCParams;
import com.meituan.msc.modules.api.appLifecycle.MSCAppLifecycleManager;
import com.meituan.msc.modules.api.report.MetricsModule;
import com.meituan.msc.modules.apploader.IAppLoader;
import com.meituan.msc.modules.apploader.InstrumentLaunchManager;
import com.meituan.msc.modules.apploader.MSCAppLoader;
import com.meituan.msc.modules.apploader.PageLaunchInfo;
import com.meituan.msc.modules.apploader.events.AppLoadException;
import com.meituan.msc.modules.container.ContainerController;
import com.meituan.msc.modules.container.ContainerLaunchErrorManager;
import com.meituan.msc.modules.container.ContainerReporter;
import com.meituan.msc.modules.container.IContainerDelegate;
import com.meituan.msc.modules.engine.MSCHornRollbackConfig;
import com.meituan.msc.modules.engine.MSCRuntime;
import com.meituan.msc.modules.engine.MSCRuntimeReporter;
import com.meituan.msc.modules.engine.RuntimeManager;
import com.meituan.msc.modules.engine.RuntimeSource;
import com.meituan.msc.modules.engine.RuntimeStateBeforeLaunch;
import com.meituan.msc.modules.engine.dataprefetch.IDataPrefetchModule;
import com.meituan.msc.modules.engine.dataprefetch.MSCPrefetchPhaseRecord;
import com.meituan.msc.modules.engine.requestPrefetch.RequestPrefetchManager;
import com.meituan.msc.modules.engine.requestPrefetch.TriggerPrefetchDataScene;
import com.meituan.msc.modules.msi.MSIManagerModule;
import com.meituan.msc.modules.page.RouteReporter;
import com.meituan.msc.modules.page.render.webview.BaseWebViewRenderer;
import com.meituan.msc.modules.page.render.webview.IWebView;
import com.meituan.msc.modules.page.render.webview.MSCWebView;
import com.meituan.msc.modules.page.render.webview.MSCWebViewRenderer;
import com.meituan.msc.modules.page.render.webview.WebViewFirstPreloadStateManager;
import com.meituan.msc.modules.page.render.webview.impl.MTWebViewImp;
import com.meituan.msc.modules.preload.MSCHornPreloadConfig;
import com.meituan.msc.modules.reporter.CommonTags;
import com.meituan.msc.modules.reporter.MSCCommonTagReporter;
import com.meituan.msc.modules.reporter.MSCLog;
import com.meituan.msc.modules.reporter.MSCPackageNotHitCacheHelper;
import com.meituan.msc.modules.reporter.ProcessMonitor;
import com.meituan.msc.modules.reporter.ReporterFields;
import com.meituan.msc.modules.reporter.memory.MemoryMonitor;
import com.meituan.msc.modules.reporter.memory.PageMemoryMonitor;
import com.meituan.msc.modules.reporter.preformance.InitRecorder;
import com.meituan.msc.modules.service.codecache.CodeCacheConfig;
import com.meituan.msc.modules.service.codecache.CodeCacheLoadStatusCounter;
import com.meituan.msc.modules.storage.StorageManageUtil;
import com.meituan.msc.modules.update.IPageLoadModule;
import com.meituan.msc.modules.update.MSCAppModule;
import com.meituan.msc.modules.update.bean.AppMetaInfoWrapper;
import com.meituan.msc.modules.update.bean.PackageInfoWrapper;
import com.meituan.msc.modules.update.metainfo.PackageExpirationTimeManager;
import com.meituan.msc.modules.update.pkg.PrefetchPackageManager;
import com.meituan.msc.util.perf.PerfEvent;
import com.meituan.msc.util.perf.PerfEventName;
import com.meituan.msc.util.perf.PerfEventPhase;
import com.meituan.msc.util.perf.PerfEventRecorder;
import com.meituan.msc.util.perf.PerfTimeUtil;
import com.meituan.msc.util.perf.PerfTrace;
import com.meituan.msc.util.perf.TraceEvent;
import com.meituan.msc.util.perf.analyze.ITraceEventFilter;
import com.meituan.msc.util.perf.analyze.PerfTraceAnalyzer;
import com.meituan.msi.metrics.ApiStageStatisticData;
import com.meituan.msi.provider.IContainerStageProvider;
import com.meituan.mtwebkit.internal.optim.StartChromiumStepByStep;

import org.json.JSONArray;
import org.json.JSONException;
import org.json.JSONObject;

import java.lang.ref.WeakReference;
import java.util.Arrays;
import java.util.Collections;
import java.util.HashMap;
import java.util.List;
import java.util.Map;
import java.util.Set;
import java.util.concurrent.CopyOnWriteArrayList;
import java.util.concurrent.TimeUnit;

/**
 * 指标上报细节文档：https://km.sankuai.com/page/**********
 */
public class AppPageReporter extends MSCCommonTagReporter implements PerfEventName {

    private final Intent intent;
    public double cacheLaunchDuration;
    /**
     * 用于解决启动Page但未收到FirstRender场景下页面退出成功率重复上报的问题
     */
    public volatile boolean isReportedPageExit;
    private boolean isLaunchOnError;
    private String launchRefer;

    public static AppPageReporter create(MSCRuntime runtime, IContainerDelegate containerDelegate, BaseRenderer renderer,
                                         String pagePath, Boolean isFirstPage, boolean isWidget,
                                         Map<String, String> bizTags, Boolean isFirstPageV2) {
        PerfTrace.begin("AppPageReporter#create");
        AppPageReporter reporter = new AppPageReporter(renderer, isFirstPage, pagePath,
                containerDelegate, runtime, isWidget, isFirstPageV2);
        reporter.setIsLaunchOnError(containerDelegate.isLaunchOnError());
        if (bizTags != null) {
            reporter.commonTag(TAG_BIZ_TAGS_FOR_PAGE, bizTags);
        }
        reporter.commonTag("isFirstStartAppAfterInstall", MSCEnvHelper.isFirstStartAppAfterInstall());
        reporter.commonTag("pageRecreateType", containerDelegate.getRecreateType());
        PerfTrace.end("AppPageReporter#create");
        return reporter;
    }

    private void setIsLaunchOnError(boolean isLaunchOnError) {
        this.isLaunchOnError = isLaunchOnError;
    }

    private static final String TAG_RUNTIME_PAGE_COUNT = "runtimePageCount";
    /**
     * 运行时历史绑定过的页面数量（包含当前绑定的页面数量），用于识别当前引擎使用的程度，以分析不同场景下的性能、内存等
     */
    private static final String TAG_RUNTIME_HISTORY_PAGE_COUNT = "runtimeHistoryPageCount";
    private static final String TAG_CODE_CACHE = "codecache";
    private static final String TAG_CODE_CACHE_USAGE_LEVEL = "codeCacheLevel";
    private static final String TAG_CODE_CACHE_MIN_JS_FILE_SIZE = "codeCacheMinJSFileSize";
    private static final String TAG_PRELOAD_DURATION_IN_PAGE = "preloadDuration";
    private static final String TAG_OTHER_PRELOAD_APP_ID_IN_PAGE = "otherPreloadAppId";
    private static final String TAG_IS_PENDING_PRELOAD_BIZ = "isPendingPreloadBiz";
    private static final String TAG_ROUTE_TIME = "routeTime";
    private static final String TAG_TRIGGER_PREFETCH_DATA_SCENE = "triggerPrefetchDataScene";
    private static final String TAG_PREFETCH_LOCATION_TYPE = "prefetchLocationType";
    private static final String TAG_PREFETCH_IS_ASYNC = "prefetchIsAsync";
    private static final String TAG_DESTROY_RUNTIME_REASON = "destroyRuntimeReason";
    private static final String TAG_SYNC_API_COUNT = "syncAPICount";
    private static final String TAG_FFP_SAMPLE_TYPE = "ffpSampleType";
    private static final String TAG_STAGE_START_SUFFIX = "_b";
    private static final String TAG_STAGE_END_SUFFIX = "_e";
    private static final int REPORT_STAGES_TIMEOUT = 1000;

    private final WeakReference<BaseRenderer> rendererWeakReference;
    private boolean existError = false;
    private boolean existJSError = false;
    private boolean isLaunchPage = false;
    private final Boolean isFirstPage;
    private boolean isPageLoaded = false;
    private final String pagePath;
    private final MSCRuntimeReporter mscRuntimeReporter;

    private CodeCacheLoadStatusCounter.Snapshot codeCacheLoadStatusCounterSnapshot;
    private int evaluateJsFileCountOnPageCreate = 0;

    private final MSCRuntime mRuntime;
    private ContainerReporter containerReporter;

    private int spendTimeOfFP = -1;
    private long pageStartTime;
    private long renderPageStartTime;
    @Deprecated
    private long routeTime = -1;

    private long routeId = -1;
    private long onAppRouteStartTime = -1;

    private int onShowCount;
    private long lastOnShowTime;
    private boolean isPageOnShow = false;
    private long timeOnPage;

    private String checkUpdateMode;
    private PageMemoryMonitor pageMemoryMonitor;
    private ITraceEventFilter listTraceFilter;
    private ITraceEventFilter listOperatorFilter;
    private final CompletableFuture<Void> fpStagesTrigger = new CompletableFuture<>();
    private final CompletableFuture<Void> ffpStagesTrigger = new CompletableFuture<>();

    private int flushCount = 0;
    private String runtimeSourceWhenCreatePage;
    private final MSCFFPReportListener ffpReportListener;
    private final MSCAttachDataCallback attachDataCallback;
    private final MSCTimedAttachDataCallback timedAttachDataCallback;
    private final MSCFFPLogListener ffpLogListener;
    private IContainerDelegate containerDelegate;
    private final Boolean isFirstPageV2;
    private boolean isSendFFPEnd;
    private long sendFFPEndEventTime;
    private boolean feMetricsTagReported;
    private long attachDataCallBackReceiveTime;
    private long timedAttachDataCallBackReceiveTime;
    private long appRouteLaunchStageTime;
    private long appRouteLaunchStageApiInvokeCounts;
    private long pageRenderLaunchStageTime;
    private long pageRenderLaunchStageApiInvokeCounts;
    private boolean pageRenderReported = false;
    private final CopyOnWriteArrayList<MetricsEntry> launchStagePendingMetrics = new CopyOnWriteArrayList<>();

    // fixme commonTags firstPage 标识不准确 回头统一调整 灰度期间问题修复 暂时不动了
    private AppPageReporter(BaseRenderer renderer, Boolean isFirstPage, String pagePath,
                            IContainerDelegate containerDelegate, MSCRuntime runtime, boolean isWidget, Boolean isFirstPageV2) {
        super(CommonTags.build(runtime, renderer, pagePath, isFirstPage, isWidget, isFirstPageV2));
        this.containerDelegate = containerDelegate;
        this.intent = containerDelegate.getIntent();
        this.pagePath = pagePath;
        this.isFirstPage = isFirstPage;
        this.containerReporter = containerDelegate instanceof ContainerController ? ((ContainerController) containerDelegate).getContainerReporter() : null;
        this.mscRuntimeReporter = runtime != null ? runtime.getRuntimeReporter() : null;
        this.rendererWeakReference = new WeakReference<>(renderer);
        this.mRuntime = runtime;
        this.isFirstPageV2 = isFirstPageV2;
        setPageStartTime(System.currentTimeMillis());
        this.ffpReportListener = new MSCFFPReportListener(this, pagePath, runtime, renderer);
        this.attachDataCallback = new MSCAttachDataCallback(this, pagePath, runtime, renderer);
        this.timedAttachDataCallback = new MSCTimedAttachDataCallback(this, pagePath, runtime, renderer);
        this.ffpLogListener = new MSCFFPLogListener(this, pagePath, runtime, renderer);
        initListFilter();
        launchRefer = IntentUtil.getStringExtra(this.intent, Constants.LAUNCH_REFER);
    }

    @Override
    public MetricsEntry buildEntry(String key, boolean repeatable) {
        MetricsEntry entry = super.buildEntry(key, repeatable)
                .tag(TAG_RUNTIME_PAGE_COUNT, mRuntime != null ? mRuntime.getPageCount() : UNKNOWN_VALUE)
                .tag(TAG_RUNTIME_HISTORY_PAGE_COUNT, mRuntime != null ? mRuntime.getHistoryPageCount() : UNKNOWN_VALUE)
                .tag(TAG_CHECK_UPDATE_MODE, checkUpdateMode)
                .tag(TAG_PKG_MODE, getPkgMode(getMetaInfo()))
                .tag(TAG_PKG_MODE_DETAIL, getPkgModeDetail(getMetaInfo()));
        if (runtimeSourceWhenCreatePage != null) {
            entry.tag(TAG_RUNTIME_SOURCE, runtimeSourceWhenCreatePage);
        }
        return entry;
    }

    public BaseRenderer getRenderer() {
        return rendererWeakReference.get();
    }

    private RendererType getRenderType() {
        BaseRenderer renderer = getRenderer();
        return renderer != null ? renderer.getType() : null;
    }

    public void reportPV() {
        MetricsEntry record = record(ReporterFields.REPORT_PAGE_PAGE_VIEW_COUNT);
        record.tag("useOriginCaptureStrategy", MSCConfig.useOriginCaptureStrategyAtWebViewWhiteScreenCheck());
        appendLaunchRefer(record);
        appendOutLinkMetrics(record, containerDelegate);
        record.sendDelay();
    }

    private void appendLaunchRefer(MetricsEntry entry) {
        boolean isLaunch = containerDelegate.getPageManager().getIsLaunch();
        if (isLaunch && !TextUtils.isEmpty(launchRefer)) {
            entry.tag(Constants.LAUNCH_REFER, launchRefer);
        }
    }


    //todo linyinong pageStartTime改名为routeTime，并删除原routeTime
    public void setPageStartTime(long pageStartTime) {
        if (this.pageStartTime == pageStartTime) {
            return;
        }
        // pageStartTime 取值改为routeTime
        this.pageStartTime = pageStartTime;

        AppMetaInfoWrapper metaInfo = getMetaInfo();
        checkUpdateMode = getCheckUpdateMode(metaInfo);
    }

    @Deprecated
    public void setRouteTime(long routeTime) {
        commonTag(TAG_ROUTE_TIME, routeTime);
        this.routeTime = routeTime;
    }

    public void setRouteId(long routeId) {
        //页面复用场景，routeId变更，清理上一次启动的信息
        if (this.routeId != -1 && this.routeId != routeId) {
            InstrumentLaunchManager.getInstance().removeLaunchInfo(String.valueOf(this.routeId));
        }

        this.routeId = routeId;
    }

    public long getRouteId() {
        return routeId;
    }

    public int getRecreateType() {
        if (containerDelegate != null) {
            return containerDelegate.getRecreateType();
        }
        return 0;
    }

    public void onCreatePage(long pageCreateTime, String appId) {
        PerfTrace.begin("AppPageReporter#onCreatePage");
        this.ffpReportListener.registerListener();
        this.attachDataCallback.registerListener();
        this.timedAttachDataCallback.registerListener();
        this.ffpLogListener.registerListener();
        if (mRuntime != null) {
            runtimeSourceWhenCreatePage = RuntimeSource.toReportString(mRuntime.getSource());
        }

        setPageStartTime(pageCreateTime);

        //fixme
        if (!isFirstPage()) {
            // 如果不是首页，就在创建页面的时候获取一个当前Runtime的CodeCache使用情况
            codeCacheLoadStatusCounterSnapshot = mscRuntimeReporter.getCodeCacheLoadStatusCounter().getSnapshot();
            evaluateJsFileCountOnPageCreate = mscRuntimeReporter.getEvaluateJsFileCount();
        }

        // 上报页面创建指标
        once(ReporterFields.REPORT_PAGE_CREATE_COUNT).sendDelay();

        if (this.containerReporter != null) {
            pageMemoryMonitor = containerReporter.pollPageMemoryMonitorOfFirstPage();
        }
        if (pageMemoryMonitor == null) {
            pageMemoryMonitor = new PageMemoryMonitor(appId);
        }
        pageMemoryMonitor.onCreate();
        PerfTrace.end("AppPageReporter#onCreatePage");
    }

    private long getPreloadDurationInMs() {
        long preloadFinishedTime = mscRuntimeReporter.getPreloadEndTime();
        if (preloadFinishedTime > pageStartTime) {
            return preloadFinishedTime - pageStartTime;
        }
        return 0;
    }

    private String getPreloadAppIdDurationPageLoad() {
        long durationEndTime = System.currentTimeMillis();
        return TextUtils.join(",", RuntimeManager.findPreloadAppIdWithinSpecifiedDuration(pageStartTime, durationEndTime, mRuntime));
    }

    public boolean isFirstPage() {
        return isFirstPage != null && isFirstPage;
    }

    public boolean isFirstPageV2() {
        return isFirstPageV2 != null && isFirstPageV2;
    }

    public AppPageReporter markLaunchPage(boolean isLaunchPage, ContainerReporter reporter, long routeTime) {
        this.isLaunchPage = isLaunchPage;
        this.containerReporter = reporter;
        if (MSCHornPerfConfig.getInstance().enableFPUsePageStartTime()) {
            setPageStartTime(routeTime);
        } else {
            setPageStartTime(isLaunchPage() && containerReporter != null ? containerReporter.getLaunchStartTimeCurrentTimeMillis() : this.pageStartTime);
        }
        return this;
    }

    public long getRouteStartTime() {
        if (MSCHornPerfConfig.getInstance().enableFPUsePageStartTime()) {
            return pageStartTime;
        } else {
            return isLaunchPage() && containerReporter != null ? containerReporter.getLaunchStartTimeCurrentTimeMillis() : pageStartTime;
        }
    }

    public boolean isLaunchPage() {
        return isLaunchPage;
    }

    public void onPageLoadSuccess() {
        if (isPageLoaded) {
            return;
        }
        isPageLoaded = true;
        if (MSCTraceUtil.TRACE_LEVEL == MSCTraceUtil.LEVEL_NONE) {
            // 未使用埋点包时才在FP时刷新前端埋点
            flushJsPerfEvents();
        }
        MetricsEntry entry = once(ReporterFields.REPORT_PAGE_LOAD_SUCCESS_RATE);
        if (isLaunchOnError) {
            entry.tag(CommonTags.TAG_SOURCE_FROM, "reload");
        }
        ContainerLaunchErrorManager.addLaunchErrorCodesToEntry(entry, mRuntime);
        ContainerLaunchErrorManager.getInstance().clear(mRuntime.getAppId());
        String rollbackEfficiencyRateTest = getRollbackEfficiencyRateTest();
        if (!TextUtils.isEmpty(rollbackEfficiencyRateTest)) {
            entry.tag("rollbackEfficiencyRateTest", rollbackEfficiencyRateTest);
        }
        String pageStackStatus = getPageStackStatus();
        if (!TextUtils.isEmpty(pageStackStatus)) {
            entry.tag("pageStackStatus", pageStackStatus);
        }
        //添加包加载各阶段耗时数据
        appendDDDLoadPackageDetails(this.mRuntime, routeId, entry);
        appendOutLinkMetrics(entry, containerDelegate);

        //启动任务执行时机
        appendLaunchMoment(this.mRuntime, routeId, entry.getTags());

        entry.value(1).sendRealTime();
        Map<String, Object> commonTags = null;
        if (isLaunchPage()) {
            if (containerReporter != null) {
                commonTags = containerReporter.getCommonTags();
            }
        }
        // 记录页面开始的时间点
        PerfTrace.online().instant(PAGE_START, pageStartTime).report();
        PerfTrace.instant(FP);
        MetricsEntry metricsEntry = once(ReporterFields.REPORT_PAGE_LOAD_DURATION).durationEnd(pageStartTime);
        metricsEntry.tag("pageStartTime", pageStartTime);
        cacheLaunchDuration = metricsEntry.getValue();
        if (commonTags != null) {
            metricsEntry.tags(commonTags);
        }
        // 添加webview3段开关状态↓↓↓↓↓
        // 只有WebView渲染器才增加下面的维度
        BaseRenderer render = getRenderer();
        if (null != render && !render.isNativeRender()) {
            // 给mtwebview添加
            metricsEntry.tag("kernel", StartChromiumStepByStep.getKernel());
            metricsEntry.tag("total", StartChromiumStepByStep.getTotalTaskCount());
            metricsEntry.tag("runTaskNum", StartChromiumStepByStep.getRealRunTaskNum());
            metricsEntry.tag("strategyName", StartChromiumStepByStep.getStrategyName());
            metricsEntry.tag("index", StartChromiumStepByStep.getSplitStrategyRunTaskIndex());
            metricsEntry.tag("ppso", MSCHornRollbackConfig.isEnablePrePageStartOptimize(mRuntime.getAppId()));
            metricsEntry.tag("oaro", MSCHornRollbackConfig.isEnableOnAppRouteOptimize(mRuntime.getAppId()));
            metricsEntry.tag("piat", MSCHornRollbackConfig.getPageInAdvancedTime());
        }
        // ↑↑↑↑↑↑
        metricsEntry.tag("lifecycleEvent",
                MSCAppLifecycleManager.getInstance().findValidLifecycleList(mRuntime.getAppId()));
        // 启动耗时指标，添加基础库预热错误日志，用于排查新建运行时占比过高问题
        metricsEntry.tag("basePreloadHitControlDetail", mRuntime.getBasePreloadHitControlDetail());
        metricsEntry.tag("bizPreloadHitControlDetail", mRuntime.getBizPreloadHitControlDetail());
        metricsEntry.tag("batchCheckUpdateErrorMsg", mscRuntimeReporter.getBatchCheckUpdateErrorMsg());
        metricsEntry.tag("preDownloadType", getPreDownloadType());
        metricsEntry.tag("checkUpdateBasePackageErrorMsg", mscRuntimeReporter.getCheckUpdateBasePackageErrorMsg());
        metricsEntry.tag("isDependTaskExecutedCheckError", isDependTaskExecutedCheckError());
        if (MSCHornRollbackConfig.enableReportPreloadAndKeepAlive()) {
            metricsEntry.tag("lastEnterKeepAliveTime", RuntimeManager.getLastEnterKeepAliveTime(mRuntime.getAppId()));
            if (Arrays.asList(RuntimeSource.NEW, RuntimeSource.COLD_START, RuntimeSource.BASE_PRELOAD, RuntimeSource.BIZ_PRELOAD).contains(mRuntime.getSource())) {
                // 未命中保活：上报未命中保活归因、业务预热状态
                metricsEntry.tag("keepAliveMissReason", mRuntime.getKeepAliveMissReason());
                metricsEntry.tag("preloadBizErrorMsg", mRuntime.getBizPreloadErrorMsg());
                if (Arrays.asList(RuntimeSource.NEW, RuntimeSource.COLD_START, RuntimeSource.BASE_PRELOAD).contains(mRuntime.getSource())) {
                    // 未命中业务预热：添加未命中业务预热归因、基础库预热状态
                    metricsEntry.tag("preloadBizMissReason", mRuntime.getBizPreloadMissReason());
                    metricsEntry.tag("preloadBaseErrorMsg", mscRuntimeReporter.getPreloadBaseErrorMsg());
                    if (Arrays.asList(RuntimeSource.NEW, RuntimeSource.COLD_START).contains(mRuntime.getSource())) {
                        // 未命中基础库预热：添加未命中基础库预热归因
                        metricsEntry.tag("preloadBaseMissReason", mscRuntimeReporter.getPreloadBaseMissReason());
                    }
                }
            }
            metricsEntry.tag("bizPreloadMaxCount", MSCHornPreloadConfig.get().getConfig().preloadAppLimitCount);
            metricsEntry.tag("keepAliveMaxCount", RuntimeManager.getKeepAliveMaxSize());
            metricsEntry.tag("keepAliveMaxTime", MSCConfig.getEngineKeepAliveTime());
        } else {
            metricsEntry.tag("preloadBaseErrorMsg", mscRuntimeReporter.getPreloadBaseErrorMsg());
            metricsEntry.tag("preloadBizErrorMsg", mRuntime.getBizPreloadErrorMsg());
        }

        metricsEntry.tag(TAG_IS_PENDING_PRELOAD_BIZ, mscRuntimeReporter.isPendingPreloadBiz());
        metricsEntry.tag(TAG_PRELOAD_DURATION_IN_PAGE, getPreloadDurationInMs());
        Boolean isRemoteBasePackageReloadConfigFetched = mscRuntimeReporter.isRemoteBasePackageReloadConfigFetched();
        if (isRemoteBasePackageReloadConfigFetched != null) {
            metricsEntry.tag("isRemoteBasePackageReloadConfigFetched", isRemoteBasePackageReloadConfigFetched);
        }
        metricsEntry.tag("disablePreDownload", StorageManageUtil.hasDisabledPreDownload(mRuntime.getAppId()));

        BaseRenderer baseRenderer = rendererWeakReference.get();
        if (MSCHornPreloadConfig.needPreloadWebView() && baseRenderer instanceof MSCWebViewRenderer) {
            metricsEntry.tag("webViewPreloadState", getPreloadStateStr());
            MSCWebView mscWebView = ((MSCWebViewRenderer) baseRenderer).getMSCWebView();
            metricsEntry.tag("reuseCachedWebViewOnFirstPage", mscWebView.getWebViewCreateScene() == null ? "" : mscWebView.getWebViewCreateScene());
        }
        metricsEntry.tag("isInit", containerDelegate.isMSCInitedAtContainerOnCreate());

        String destroyRuntimeReason = getDestroyRuntimeReason();
        if (!TextUtils.isEmpty(destroyRuntimeReason)) {
            metricsEntry.tag(TAG_DESTROY_RUNTIME_REASON, destroyRuntimeReason);
        }

        //添加包加载各阶段耗时数据
        appendDDDLoadPackageDetails(this.mRuntime, routeId, metricsEntry);
        //启动任务执行时机
        appendLaunchMoment(this.mRuntime, routeId, metricsEntry.getTags());
        appendLaunchTaskInfo(this.mRuntime, routeId, metricsEntry.getTags());

        // append duration detail
        Map<String, Long> durationPoints = getMarkPointMap();
        metricsEntry.tag("durationDetails", createPointDurations(durationPoints, metricsEntry.getValue()));

        long gcTime = MemoryMonitor.getGCTimeCost();
        once(ReporterFields.REPORT_MSC_LAUNCH_PERFORMANCE_GC_TIME).value(gcTime).sendRealTime();
        metricsEntry.tag("gcTime", gcTime);
        metricsEntry.tag(TAG_PAGE_START_FROM_APPLICATION_START, (pageStartTime - InitRecorder.getApplicationStartUnixTime()));
        metricsEntry.lazy(this::addCodeCacheUsageTags);
        appendConfigStatus(metricsEntry);
        appendPackageOfflineReason(metricsEntry);
        addIntentParams(metricsEntry, intent);
        appendPageLoadMetrics(metricsEntry);
        appendPreloadStrategy(metricsEntry);
        appendCacheNotHitReason(metricsEntry);
        appendEngineManageStrategy(metricsEntry);
        ProcessMonitor.getCPUUsageStat(new ProcessMonitor.ICPUStatCallback() {
            @Override
            public void onComplete(double usageRate) {
                if (Double.compare(usageRate, 0) > 0) {
                    once(ReporterFields.REPORT_MSC_LAUNCH_PERFORMANCE_CPU_USAGE).value(usageRate).sendRealTime();
                    metricsEntry.tag("cpuUsageRate", usageRate);
                }
                appendAbnormalFPDetail(metricsEntry);
                metricsEntry.sendDelay();
                reportFPStages(pageStartTime, (long) (pageStartTime + metricsEntry.getValue()), metricsEntry.getTags());
            }
        });
        Boolean isSubPage = isSubPage();
        metricsEntry.tag("isSubPage", isSubPage == null ? "unknown" : isSubPage);
        if (Boolean.TRUE.equals(isSubPage)) {
            appendSubPkgRoot(metricsEntry);
        }
        spendTimeOfFP = (int) metricsEntry.getValue();
        appendHornField(entry);
    }

    private void appendPreloadStrategy(MetricsEntry metricsEntry) {
        String preloadStrategyStr = mRuntime.getPreloadStrategyStr();
        if (!TextUtils.isEmpty(preloadStrategyStr)) {
            metricsEntry.tag("afterT3PreloadStrategy", preloadStrategyStr);
        }
    }

    private void appendEngineManageStrategy(MetricsEntry metricsEntry) {
        metricsEntry.tag("enableAppSharedCountLimit", MSCHornRollbackConfig.enableAppSharedCountLimit());
        metricsEntry.tag("enableLocalAppStackPriority", MSCHornRollbackConfig.enableLocalAppStackPriority());
        metricsEntry.tag("localAppStackPriorityStrategy", MSCHornRollbackConfig.localAppStackPriorityStrategy());
    }

    private String getRollbackEfficiencyRateTest() {
        if (isFirstPage()) {
            return mRuntime.getContainerManagerModule().getRollbackEfficiencyRateTestCachedWhenLaunch();
        }
        return MSCHornRollbackConfig.readConfig().rollbackEfficiencyRateTest;
    }

    private String getPageStackStatus() {
        List<IContainerDelegate> containerDelegates = mRuntime.getContainerManagerModule().getContainerDelegates();
        if (containerDelegates.size() <= 1) {
            if (containerDelegates.size() == 1) {
                IContainerDelegate containerDelegate = containerDelegates.get(0);
                // 上报启动成功时当前页面已被添加到了页面栈，所以需要判断是否大于 1，以识别内部路由场景
                if (containerDelegate.getPageMangerModule().getPageCount() > 1) {
                    return "hasPage";
                }
            }
            return null;
        } else {
            if (containerDelegates.size() == 2) {
                IContainerDelegate firstContainerDelegate = containerDelegates.get(0);
                IContainerDelegate secondContainerDelegate = containerDelegates.get(1);
                if (secondContainerDelegate.getPageMangerModule().getPageCount() > 1) {
                    return "hasPage";
                } else {
                    if (firstContainerDelegate.isWidget()) {
                        return "onlyHasWidget";
                    }
                }
            }
            return "hasPage";
        }
    }

    private void addIntentParams(MetricsEntry metricsEntry, Intent intent) {
        if (intent == null) {
            return;
        }
        Uri data = intent.getData();
        if (data != null) {
            Set<String> params = data.getQueryParameterNames();
            for (String param : params) {
                if (TextUtils.equals(param, MSCParams.TARGET_PATH)
                        || TextUtils.equals(param, MSCParams.APP_ID)
                        || TextUtils.equals(param, MSCParams.APP_NAME)
                        || TextUtils.equals(param, MSCParams.APP_ICON)
                        || TextUtils.equals(param, MSCParams.WIDGET_PATH)) {
                    break;
                }
                String value = data.getQueryParameter(param);
                metricsEntry.tag(param, value);
            }
        }
    }

    private void appendCacheNotHitReason(MetricsEntry metricsEntry) {
        MSCAppModule mscAppModule = mRuntime.getMSCAppModule();
        if (mscAppModule == null) {
            return;
        }
        PackageInfoWrapper basePackage = mscAppModule.getBasePackage();
        // 基础库未命中缓存，且基础库获取时间晚于页面开始时间，说明未命中缓存
        if (basePackage != null && !basePackage.isDownloadedBefore(pageStartTime)) {
            metricsEntry.tag("basePkgNotHitCacheReason", getPkgNotHitCacheReason(basePackage));
        }
        if (mscAppModule.isSubPackagePage(pagePath)) {
            PackageInfoWrapper subPackage = mscAppModule.getSubPackageCachedByPath(pagePath);
            // 子包未命中缓存
            if (subPackage != null && !subPackage.isDownloadedBefore(pageStartTime)) {
                metricsEntry.tag("subPkgNotHitCacheReason", getPkgNotHitCacheReason(subPackage));
            }
        } else {
            PackageInfoWrapper mainPackage = mscAppModule.getMainPackageWrapper();
            // 主包未命中缓存
            if (mainPackage != null && !mainPackage.isDownloadedBefore(pageStartTime)) {
                metricsEntry.tag("mainPkgNotHitCacheReason", getPkgNotHitCacheReason(mainPackage));
            }
        }
    }

    private void appendPackageOfflineReason(MetricsEntry metricsEntry) {
        MSCAppModule mscAppModule = mRuntime.getMSCAppModule();
        if (mscAppModule != null && !TextUtils.isEmpty(mscAppModule.getOfflineBizFailReason())) {
            metricsEntry.tag("offlineBizFailReason", mscAppModule.getOfflineBizFailReason());
        }
        if (mscAppModule != null && !TextUtils.isEmpty(mscAppModule.getOfflineBaseFailReason())) {
            metricsEntry.tag("offlineBaseFailReason", mscAppModule.getOfflineBaseFailReason());
        }
    }

    private void appendSubPkgRoot(MetricsEntry metricsEntry) {
        PackageInfoWrapper subPackageInfo = mRuntime.getMSCAppModule().getSubPackageCachedByPath(pagePath);
        metricsEntry.tag("subPkgName", subPackageInfo == null ? "unknown" : subPackageInfo.getRoot());
    }
    /**
     * 给FP和FFP这种重要的页面启动指标添加一些通用维度
     * 在FP指标上追加launchRefer参数
     * 给FP和FFP追加外投相关字段
     *
     * @param metricsEntry
     */
    private void appendPageLoadMetrics(MetricsEntry metricsEntry) {
        BaseRenderer baseRenderer = rendererWeakReference.get();
        if (baseRenderer instanceof MSCWebViewRenderer) {
            MSCWebViewRenderer webViewRenderer = (MSCWebViewRenderer) baseRenderer;
            /**
             * 页面加载时，使用的渲染缓存类型
             */
            metricsEntry.tag("renderCacheType", webViewRenderer.getRenderCacheType());
            /**
             * 页面加载时，使用的webView是预热、复用还是新建
             */
            metricsEntry.tag("webViewSource", getWebViewSource(webViewRenderer));
            /**
             * 获取页面开始加载时，WebView和Service的状态，用于评估页面启动时的状态。
             * 注意：这些状态只能获取页面启动时的状态，页面加载完成时再获取这些状态就没有意义了
             */
            metricsEntry.tag("webViewInitialState", getWebViewInitialState(webViewRenderer));
            /**
             * 上报启动前视图层已注入了包资源状态
             */
            metricsEntry.tag("webViewPkgInjectState", getWebViewPkgInjectState(webViewRenderer));
            // 上报视图层基础库(page-bootstrap.js + pageframe.js)和业务主包(app-page.js)注入文件大小
            metricsEntry.tag("webviewBasePkgFileSize", webViewRenderer.getBasePackageFileSize());
            metricsEntry.tag("webviewMainPkgFileSize", webViewRenderer.getMainPackageFileSize());

            MSCWebView mscWebView = webViewRenderer.getMSCWebView();
            if (mscWebView != null) {
                IWebView webView = mscWebView.getIWebView();
                if (webView instanceof MTWebViewImp) {
                    // 标记是否是第一个创建的WebView
                    metricsEntry.tag("isFirstCreateWebView", ((MTWebViewImp) webView).isFirstCreateWebView());
                }
            }
        }
        String serviceInitialState = getLastServiceInitialStateBeforeTargetTime(getMetaInfo());
        metricsEntry.tag("serviceInitialState", serviceInitialState);
        metricsEntry.tag("runtimeStatus", getRuntimeStatus(serviceInitialState));
        appendLaunchRefer(metricsEntry);
        appendOutLinkMetrics(metricsEntry, containerDelegate);
        metricsEntry.tag("enableSplitChunks", MSCHornRollbackConfig.enableSplitChunks());
        if (getMetaInfo() != null && getMetaInfo().getAdvanceBuildConfig() != null) {
            metricsEntry.tag("splitChunks", getMetaInfo().getAdvanceBuildConfig().isSplitChunks());
        }
    }

    /**
     * 获取页面启动时的runtime状态。相比老版的运行时来源，指标更准确
     * https://km.sankuai.com/collabpage/2705544276
     */
    private String getRuntimeStatus(String serviceInitialState) {
        int runtimePageCount = mRuntime.getPageCount();
        int runtimeHistoryPageCount = mRuntime.getHistoryPageCount();
        if (serviceInitialState.equals("unknown")) {
            return "unknown";
        } else if (serviceInitialState.equals("biz_sub") && runtimeHistoryPageCount <= 1) {
            return "bizSubInjected";
        } else if (serviceInitialState.equals("biz_sub_without_base") && runtimeHistoryPageCount <= 1) {
            return "bizSubInjectedWithoutBase";
        } else if (serviceInitialState.equals("biz_sub_without_main") && runtimeHistoryPageCount <= 1) {
            return "bizSubInjectedWithoutMain";
        } else if (serviceInitialState.equals("biz_sub_only") && runtimeHistoryPageCount <= 1) {
            return "bizSubInjectedOnly";
        } else if (serviceInitialState.equals("biz_main") && runtimeHistoryPageCount <= 1) {
            return "bizMainInjected";
        } else if (serviceInitialState.equals("biz_main_without_base") && runtimeHistoryPageCount <= 1) {
            return "bizMainInjectedWithoutBase";
        } else if (serviceInitialState.equals("base") && runtimeHistoryPageCount <= 1) {
            return "baseInjected";
        } else if (serviceInitialState.equals("none") && runtimeHistoryPageCount <= 1) {
            return "noneInjected";
        } else if (runtimePageCount <= 1 && runtimeHistoryPageCount > 1) {
            return "keepAlive";
        } else if (runtimePageCount > 1 && runtimeHistoryPageCount > 1) {
            return "secondaryPage";
        }
        return "unknown";
    }

    private String getPkgNotHitCacheReason(PackageInfoWrapper packageInfo) {
        if (packageInfo.isBasePackage()) {
            // 触发了版本强更
            if (mRuntime.hasForceUpdateBasePkg()) {
                return "sdkUpgradeForceUpdate";
            }
        } else {
            // 触发最低版本号强更
            if (getMetaInfo().isFetchedByMinVersionLimit) {
                return "notMatchMinVersionForceUpdate";
            }
            // 如果元信息来自缓存但包来自网络，说明被清理了，不满足此条件，走到!TextUtils.isEmpty(cleanResourceReason)中
            // 如果元信息来自网络但没有上次缓存信息，说明首次请求包，不满足此条件，最终返回default
            // 如果元信息来自网络且有上次缓存信息，说明有启动过但缓存过期，且新元信息中包版本有更新。返回cacheExpiredAndUpdatePkg
            if (!getMetaInfo().isFromCache() && getMetaInfo().hasPreviousExpiredCache()) {
                return "cacheExpiredAndUpdatePkg";
            }
        }
        // 包被DD或存储组件清理场景
        String cleanResourceReason = MSCPackageNotHitCacheHelper.getCleanResourceReason(packageInfo.getDDResourceName());
        if (!TextUtils.isEmpty(cleanResourceReason)) {
            return cleanResourceReason;
        }
        // 未预下载/未启动过
        return "default";
    }

    private String getWebViewInitialState(MSCWebViewRenderer webViewRenderer) {
        BaseWebViewRenderer.LoadStage loadStage = webViewRenderer.getLastWebViewStateBeforeTargetTime(pageStartTime);
        switch (loadStage) {
            case INITIAL:
                return "none";
            case HTML_LOADED:
                return "loadHTML";
            case FIRST_SCRIPT:
                return "firstScript";
            // TODO chdc 这面这几个不好搞，FP这种的在Android上也不会有，先不搞了
            // pageReady
            // firstRender
            // firstInteractiveRender
            default:
                return UNKNOWN_VALUE;
        }
    }

    private String getWebViewPkgInjectState(MSCWebViewRenderer webViewRenderer) {
        if (webViewRenderer == null) {
            return UNKNOWN_VALUE;
        }
        return webViewRenderer.getPkgInjectState();
    }

    private String getLastServiceInitialStateBeforeTargetTime(AppMetaInfoWrapper metaInfo) {
        if (pagePath == null || metaInfo == null) {
            return UNKNOWN_VALUE;
        }
        // 子包
        // 目前业务子包注入依赖主包注入，不存在子包注入了，主包没有注入的情况。为了和其他端对齐，把所有情况都先列了出来
        PackageInfoWrapper subPackage = metaInfo.getOrCreateSubPackageWrapperByPath(pagePath);
        if (subPackage != null && subPackage.isServiceLoadedBefore(pageStartTime)) {
            if (isBasePackageInjectedBeforePageStart() && isBizMainPackageInjectedBeforePageStart(metaInfo)) {
                return "biz_sub";
            } else if (!isBasePackageInjectedBeforePageStart() && isBizMainPackageInjectedBeforePageStart(metaInfo)) {
                return "biz_sub_without_base";
            } else if (isBasePackageInjectedBeforePageStart() && !isBizMainPackageInjectedBeforePageStart(metaInfo)) {
                return "biz_sub_without_main";
            } else if (!isBasePackageInjectedBeforePageStart() && !isBizMainPackageInjectedBeforePageStart(metaInfo)) {
                return "biz_sub_only";
            }
        }
        // 主包
        if (isBizMainPackageInjectedBeforePageStart(metaInfo)) {
            if (isBasePackageInjectedBeforePageStart()) {
                return "biz_main";
            } else {
                return "biz_main_without_base";
            }
        }
        // 基础库包
        if (isBasePackageInjectedBeforePageStart()) {
            return "base";
        }
        return "none";
    }

    private boolean isBasePackageInjectedBeforePageStart() {
        PackageInfoWrapper basePackage = CommonTags.getBasePackage(mRuntime);
        if (basePackage != null && basePackage.isServiceLoadedBefore(pageStartTime)) {
            return true;
        }
        return false;
    }

    private boolean isBizMainPackageInjectedBeforePageStart(AppMetaInfoWrapper metaInfo) {
        if (metaInfo.mainPackageCached != null && metaInfo.mainPackageCached.isServiceLoadedBefore(pageStartTime)) {
            return true;
        }
        return false;
    }

    private String getWebViewSource(@NonNull MSCWebViewRenderer webViewRenderer) {
        long createTime = webViewRenderer.getWebViewCreateTimeMillis();
        String webViewSource;
        if (createTime <= 0) {
            // invalid
            webViewSource = "unknown";
        } else if (createTime >= pageStartTime) {
            // create after page start
            webViewSource = "new";
        } else {
            // create before page start
            if (webViewRenderer.hasRecycle) {
                // 回收复用
                webViewSource = "recycle";
            } else {
                // 预热，非复用
                webViewSource = "precreate";
            }
        }
        return webViewSource;
    }

    private void appendConfigStatus(MetricsEntry metricsEntry) {
        metricsEntry.tag("rollBackBizPreloadWhenDataPrefetch", MSCHornRollbackConfig.get().getConfig().rollBackBizPreloadWhenDataPrefetch);;
        if (mRuntime != null) {
            metricsEntry.tag("isRollbackStartPageAdvanced", MSCHornRollbackConfig.isRollbackStartPageAdvanced(mRuntime.getAppId()));
        }
        metricsEntry.tag("disablePreParseCss", MSCHornPreloadConfig.disablePreParseCss());
        metricsEntry.tag("disablePreParseCssWhenBizPreload", MSCHornPreloadConfig.disablePreParseCssWhenBizPreload());
    }

    private String getPreloadStateStr() {
        BaseRenderer baseRenderer = rendererWeakReference.get();
        if (baseRenderer instanceof MSCWebViewRenderer) {
            MSCWebView mscWebView = ((MSCWebViewRenderer) baseRenderer).getMSCWebView();
            WebViewFirstPreloadStateManager.PreloadState preloadState;
            if (mscWebView != null && (preloadState = mscWebView.getPreloadState()) != null) {
                return preloadState.toString();
            }
        }
        return UNKNOWN_VALUE;
    }

    public void triggerFPStagesReportIfNeed() {
        fpStagesTrigger.complete(null);
    }

    public void triggerFFPStagesReportIfNeed() {
        ffpStagesTrigger.complete(null);
        feMetricsTagReported = true;
        if (MSCHornRollbackConfig.readConfig().enableMSCDimensionReportToFFP && sendFFPEndEventTime != 0) {
            once(ReporterFields.REPORT_FFPEND_EVENT_DURATION).value(System.currentTimeMillis() - sendFFPEndEventTime).sendDelay();
        }
    }

    private void reportFPStages(long pageStartTime, long fpTime, Map<String, Object> extraTags) {
        Map<String, Object> newExtraTags = new HashMap<>(extraTags);
        BaseRenderer renderer = getRenderer();
        if (renderer == null) {
            return;
        }
        PerfEventRecorder recorder = renderer.getPerfEventRecorder();
        if (recorder == null) {
            return;
        }
        List<PerfEvent> perfEvents = recorder.getEvents();
        for (PerfEvent item : perfEvents) {
            newExtraTags.put(item.name + "-" + item.ph, item.unixTs);
        }
        reportStages(ReporterFields.FP_STAGES, fpStagesTrigger, pageStartTime, fpTime, newExtraTags);
    }

    public void reportFFPStages(long ffpStart, long ffpEndTime, Map<String, Object> extraTags) {
        reportStages(ReporterFields.FFP_STAGES, ffpStagesTrigger, ffpStart, ffpEndTime, extraTags);
    }

    private void reportStages(String stage, CompletableFuture<Void> stageTrigger, long stageStartTime, long stageEndTime, Map<String, Object> extraTags) {
        stageTrigger.thenRun(new Runnable() {
            @Override
            public void run() {
                once(stage)
                        .tags(extraTags)
                        .tags(filterOnlineEvents(stageStartTime, stageEndTime, getRenderType(),  true))
                        .value(stageEndTime - stageStartTime)
                        .sendDelay();
            }
        });
        // 如果外部没有在1秒内调用 reportFFPStagesIfNeed() 触发上报，那么就超时自动发出
        MSCExecutors.ioSerialized.schedule(new Runnable() {
            @Override
            public void run() {
                stageTrigger.complete(null);
                feMetricsTagReported = false;
            }
        }, REPORT_STAGES_TIMEOUT, TimeUnit.MILLISECONDS);
    }

    public void reportFFPStagesToFFP(FFPPageInfo pageInfo, TimedAttachDataListener timedListener) {
        ffpStagesTrigger.thenRun(new Runnable() {
            @Override
            public void run() {
                Map<String, Object> containerTags = new HashMap<>();
                // 线上通过查询ffpStageMetricsEvent来确认上报了MSC埋点的比例
                containerTags.put("ffpStageMetricsEvent", true);
                // 线上通过查询feMetricsTagReported来确认上报了前端埋点的比例
                containerTags.put("feMetricsTagReported", feMetricsTagReported);
                //增加重建指标
                containerTags.put("pageRecreateType", getRecreateType());
                timedListener.onTagsReady(containerTags);
                BaseRenderer baseRenderer = rendererWeakReference.get();
                Map<String, Object> renderTags = Collections.emptyMap();
                if (baseRenderer != null) {
                    renderTags = baseRenderer.getFFPTags(pageInfo);
                }
                Map<String, Object> containerDetails = filterOnlineEvents(pageInfo.getStartTimeMills(), pageInfo.getEndTimeMills(), getRenderType(), false);
                containerDetails.putAll(renderTags);
                if (MSCHornRollbackConfig.enableReportAPIDataToFFP()) {
                    Map<String, Object> apiTags = getFFPApiPerformanceTags(containerDetails, pageInfo);
                    containerDetails.putAll(apiTags);
                } else {
                    reportFFPApiPerformance(containerDetails, pageInfo);
                }
                timedListener.onContainerDetailsReady(containerDetails);
            }
        });
        // 如果前端没有在 timedListener的超时时长的一半时间 内将前端的信息发送回来，那么就超时发出客户端部分的埋点
        long listenerTimeout = MSCHornRollbackConfig.enableReportAPIDataToFFP() ? timedListener.getTimeoutMills() - 100 : timedListener.getTimeoutMills() / 2;
        MSCExecutors.ioSerialized.schedule(new Runnable() {
            @Override
            public void run() {
                ffpStagesTrigger.complete(null);
                feMetricsTagReported = false;
            }
        }, listenerTimeout, TimeUnit.MILLISECONDS);
    }

    private static Map<String, Object> filterOnlineEvents(long startTime, long endTime, RendererType rendererType ,boolean keepOriginTimeStamp) {
        Map<String, Object> result = new HashMap<>();
        List<TraceEvent> events = PerfTrace.getOnlineEvents();
        MSCLog.i(TAG, "online events size: ", events.size());
        if (!events.isEmpty()) {
            for (TraceEvent traceEvent : events) {
                PerfEvent event = traceEvent.perfEvent;
                long ts = PerfTimeUtil.convertNanoTimeToUTCTime(event.ts);
                // 用于秒开范围外时间戳上报
                boolean forceReport = false;
                if (event.extra != null) {
                    try {
                        forceReport = event.extra.has("forceReport") && event.extra.getBoolean("forceReport");
                        if (forceReport) {
                            // 仅上报1次，避免每次秒开都上报
                            event.extra.put("forceReport", false);
                        }
                    } catch (JSONException e) {
                    }
                }
                if (event.isShouldReport() && ((ts >= startTime && ts <= endTime) || forceReport)) {
                    // 原始时间戳或者是距离秒开起点的相对时间
                    ts = keepOriginTimeStamp ? ts : ts - startTime;
                    switch (event.ph) {
                        case PerfEventPhase.INSTANT:
                            result.put(event.name, ts);
                            break;
                        case PerfEventPhase.BEGIN:
                            result.put(event.name + TAG_STAGE_START_SUFFIX, ts);
                            break;
                        case PerfEventPhase.END:
                            result.put(event.name + TAG_STAGE_END_SUFFIX, ts);
                            break;
                        case PerfEventPhase.COMPLETE:
                            result.put(event.name + TAG_STAGE_START_SUFFIX, ts);
                            result.put(event.name + TAG_STAGE_END_SUFFIX, ts + event.getDurationInMillis() + ts);
                            break;
                    }
                }
            }
        }
        addCompositeStageTags(result, startTime, rendererType, keepOriginTimeStamp);
        return result;
    }

    // 补充秒开分阶段中需要通过复合计算获取的埋点
    // https://km.sankuai.com/collabpage/2712578204#b-2dec7085f6504f36923e72083f5b37c6
    private static void addCompositeStageTags(Map<String, Object> result, long startTime, RendererType rendererType, boolean keepOriginTimeStamp) {
        if (MSCHornRollbackConfig.readConfig().enableNewStagesDimension) {
            result.put("pkgPrepare_b", getMinNonZeroValue(result, startTime, keepOriginTimeStamp, "FetchMetaInfo_b", "FetchBasePackage_b", "FetchBuzPkgTask_b"));
            result.put("pkgPrepare_e", getMaxNonZeroValue(result, startTime, keepOriginTimeStamp, "FetchBasePackage_e", "FetchBuzPkgTask_e", "FetchSubBuzPkgTask_e"));
            result.put("pkgInject_b", getMinNonZeroValue(result, startTime, keepOriginTimeStamp, "InjectBasePackage_b", "InjectBuzPkgTask_b"));
            result.put("pkgInject_e", getMaxNonZeroValue(result, startTime, keepOriginTimeStamp, "InjectBasePackage_e", "InjectBuzPkgTask_e"));
            result.put("frameLaunch_b", getMaxNonZeroValue(result, startTime, keepOriginTimeStamp, "StartPageTaskOfLaunch_b", "startPageByRoute_b"));
            long frameLaunchEndTS = getMinNonZeroValue(result, startTime, keepOriginTimeStamp, "app_launch_b", "page_launch_b");
            if (rendererType == RendererType.WEBVIEW) {
                Object value = result.get("after_exec_on_page_start");
                if (value instanceof Long && (Long) value > 0) {
                    frameLaunchEndTS = Math.max(frameLaunchEndTS, (Long) value);
                }
            }
            result.put("frameLaunch_e", frameLaunchEndTS);
            result.put("bizLaunch_b", getMinNonZeroValue(result, startTime, keepOriginTimeStamp, "app_launch_b", "page_launch_b"));
            result.put("bizLaunch_e", getMinNonZeroValue(result, startTime, keepOriginTimeStamp, "page_subtree_b"));
        }
    }

    private static long getMinNonZeroValue(Map<String, Object> result, long ffpStartTS, boolean keepOriginTimeStamp, String... keys) {
        if (result == null || keys == null || keys.length == 0) {
            return keepOriginTimeStamp ? ffpStartTS : 0;
        }
        long minValue = Long.MAX_VALUE;
        boolean found = false;

        for (String key : keys) {
            Object value = result.get(key);
            if (value instanceof Long && (Long) value > 0) {
                minValue = Math.min(minValue, (Long) value);
                found = true;
            }
        }

        if (!found) {
            return keepOriginTimeStamp ? ffpStartTS : 0;
        }

        return minValue;
    }

    private static long getMaxNonZeroValue(Map<String, Object> result, long ffpStartTS, boolean keepOriginTimeStamp, String... keys) {
        if (result == null || keys == null || keys.length == 0) {
            return keepOriginTimeStamp ? ffpStartTS : 0;
        }
        long maxValue = Long.MIN_VALUE;
        boolean found = false;

        for (String key : keys) {
            Object value = result.get(key);
            if (value instanceof Long && (Long) value > 0) {
                maxValue = Math.max(maxValue, (Long) value);
                found = true;
            }
        }

        if (!found) {
            return keepOriginTimeStamp ? ffpStartTS : 0;
        }

        return maxValue;
    }

    private void reportFFPApiPerformance(Map<String, Object> ffpTags, FFPPageInfo pageInfo) {
        if (!MSCHornRollbackConfig.enableReportAPIPerformanceStatisticData()) {
            return;
        }
        MSCExecutors.submit(new MSCExecutors.Serialized.SubmitRunnable(new Runnable() {
            @Override
            public void run() {
                // 延迟2s获取MSI数据
                reportFFPApiDetails(getFFPApiPerformanceTags(ffpTags, pageInfo));
            }
        }, MSCConfig.getReportFFPApiDetailsDelayTime()));
    }

    private Map<String, Object> getFFPApiPerformanceTags(Map<String, Object> ffpTags, FFPPageInfo pageInfo) {
        Map<String, Object> apiTags = new HashMap<>();
        addApiPerformanceTags(ffpTags, apiTags, pageInfo);
        String serviceInitialState = getLastServiceInitialStateBeforeTargetTime(getMetaInfo());
        apiTags.put("runtimeStatus", getRuntimeStatus(serviceInitialState));
        return apiTags;
    }

    private void addApiPerformanceTags(Map<String, Object> ffpTags, Map<String, Object> apiTags, FFPPageInfo pageInfo) {
        long sendOnAppRouteTime = getLongValue(ffpTags, PerfEventConstant.SEND_APP_ROUTE, -1);
        // 业务启动阶段数据
        long pageSubTreeTime = getLongValue(ffpTags, "page_subtree_b", -1);
        if (pageSubTreeTime > 0 && sendOnAppRouteTime > 0 && pageInfo != null) {
            addStageApiTags(apiTags, "BizLaunch", sendOnAppRouteTime + pageInfo.getStartTimeMills(), pageSubTreeTime + pageInfo.getStartTimeMills());
        }
        // 页面渲染阶段数据
        if (pageSubTreeTime > 0 && pageInfo != null) {
            addStageApiTags(apiTags, "PageRender", pageSubTreeTime + pageInfo.getStartTimeMills(), pageInfo.getEndTimeMills());
        }
        // 容器启动阶段数据 容器启动阶段终点为after_exec_on_page_start、onAppRoute最晚值，after_exec_on_page_start可能晚于业务启动、页面渲染，API获取数据后会进行清理，因此最后获取API数据不影响其他两个阶段
        long containerLaunchEndTime = sendOnAppRouteTime;
        if (getRenderType() == RendererType.WEBVIEW) {
            containerLaunchEndTime = Math.max(getLongValue(ffpTags, "after_exec_on_page_start", -1), containerLaunchEndTime);
        }
        if (pageInfo != null && containerLaunchEndTime > 0) {
            addStageApiTags(apiTags, "ContainerLaunch", pageInfo.getStartTimeMills(), containerLaunchEndTime + pageInfo.getStartTimeMills());
        }
    }

    private JSONObject getApiDetailsJsonObject(@NonNull ApiStageStatisticData apiStageStatisticData) {
        try {
            String dataStr = ConversionUtil.getGson().toJson(apiStageStatisticData);
            if (!TextUtils.isEmpty(dataStr)) {
                return new JSONObject(dataStr);
            }
            return new JSONObject();
        } catch (Exception e) {
            return new JSONObject();
        }
    }

    private void addStageApiTags(Map<String, Object> apiTags, String stage, long startTime, long endTime) {
        ApiStageStatisticData asyncData = mRuntime.apisManager.getApiStageStatisticData(false, startTime, endTime);
        if (asyncData != null) {
            apiTags.put("asyncAPICountAt" + stage, asyncData.totalCount);
            apiTags.put("asyncAPITotalDuationAt" + stage, asyncData.totalDuration);
            if (asyncData.apiDetails != null) {
                apiTags.put("asyncAPIDetailsAt" + stage, getApiDetailsJsonObject(asyncData));
            }
        }
        ApiStageStatisticData syncData = mRuntime.apisManager.getApiStageStatisticData(true, startTime, endTime);
        if (syncData != null) {
            apiTags.put("syncAPICountAt" + stage, syncData.totalCount);
            apiTags.put("syncAPITotalDuationAt" + stage, syncData.totalDuration);
            if (syncData.apiDetails != null) {
                apiTags.put("syncAPIDetailsAt" + stage, getApiDetailsJsonObject(syncData));
            }
        }
    }

    private long getLongValue(Map<String, Object> map, String key, long defaultValue) {
        if (map == null) {
            return defaultValue;
        }
        Object value = map.get(key);
        if (value == null) {
            return defaultValue;
        }
        if (value instanceof Number) {
            return ((Number) value).longValue();
        } else if (value instanceof String) {
            try {
                return Long.parseLong((String) value);
            } catch (NumberFormatException e) {
                return defaultValue;
            }
        }
        return defaultValue;
    }

    private String getDestroyRuntimeReason() {
        if (mRuntime.getSource() == RuntimeSource.KEEP_ALIVE) {
            return null;
        }
        return RuntimeManager.getDestroyRuntimeReason(mRuntime.getAppId());
    }

    private boolean isDependTaskExecutedCheckError() {
        IAppLoader iAppLoader = mRuntime.getModule(IAppLoader.class);
        if (iAppLoader instanceof MSCAppLoader) {
            MSCAppLoader appLoader = (MSCAppLoader) iAppLoader;
            return appLoader.isDependTaskExecutedCheckError();
        } else {
            return false;
        }
    }

    private void appendAbnormalFPDetail(MetricsEntry metricsEntry) {
        if (MSCHornPerfConfig.getInstance().disableAbnormalFPDetailReport()) {
            return;
        }
        int threshold = MSCHornPerfConfig.getInstance().abnormalFPThreshold();
        if (Double.compare(metricsEntry.getValue(), threshold) < 0) {
            return;
        }
        BaseRenderer renderer = getRenderer();
        if (renderer == null) {
            return;
        }
        PerfEventRecorder recorder = renderer.getPerfEventRecorder();
        if (recorder == null) {
            return;
        }
        List<PerfEvent> perfEvents = recorder.getEvents();
        for (PerfEvent item : perfEvents) {
            metricsEntry.tag(item.name + "-" + item.ph, item.unixTs);
        }
    }

    private String getPreDownloadType() {
        PackageInfoWrapper infoWrapper = mRuntime.getMSCAppModule().getLoadedPackageInfoByPath(pagePath);
        if (PrefetchPackageManager.isPreDownloadPackage(infoWrapper.appId, infoWrapper.getMd5())) {
            return infoWrapper.getPkgTypeString();
        }
        return "no_predownload";
    }

    public String getPkgMode(AppMetaInfoWrapper metaInfo) {
        if (pagePath == null || metaInfo == null) {
            return UNKNOWN_VALUE;
        }
        // 子包
        PackageInfoWrapper subPackage = metaInfo.getOrCreateSubPackageWrapperByPath(pagePath);
        if (subPackage != null && !subPackage.isDownloadedBefore(pageStartTime)) {
            // 子包存在，且下载时间不早于页面加载时间（真实下载时间确实晚于页面加载时间或者下载时间未知），则算是网络来源
            return NETWORK_VALUE;
        }
        // 主包
        if (metaInfo.mainPackageCached == null || !metaInfo.mainPackageCached.isDownloadedBefore(pageStartTime)) {
            // 主包缓存不存在，说明主包还没下载/加载完毕，或者主包的下载时间不早于页面加载时间（真实下载时间确实晚于页面加载时间或者下载时间未知），则算是网络来源
            return NETWORK_VALUE;
        }
        // 基础库包
        PackageInfoWrapper basePackage = CommonTags.getBasePackage(mRuntime);
        if (basePackage == null || !basePackage.isDownloadedBefore(pageStartTime)) {
            // 基础库包缓存不存在，说明基础库包还没下载/加载完毕，或者基础库包的下载时间不早于页面加载时间（真实下载时间确实晚于页面加载时间或者下载时间未知），则算是网络来源
            return NETWORK_VALUE;
        }
        return CACHE_VALUE;
    }

    /**
     * 1代表来自缓存，0代表来自网络
     * 返回值按照 基础库-主包-子包 顺序进行定义（子包不存在时，值为1）
     */
    public String getPkgModeDetail(AppMetaInfoWrapper metaInfo) {
        if (pagePath == null || metaInfo == null) {
            return UNKNOWN_VALUE;
        }
        StringBuilder result = new StringBuilder();
        // 基础库包
        result.append(isBaseFromCache() ? "1" : "0");
        // 主包
        result.append(isMainFromCache(metaInfo) ? "1" : "0");
        // 子包
        result.append(isSubPackageFromCache(metaInfo) ? "1" : "0");
        return result.toString();
    }

    private boolean isSubPackageFromCache(AppMetaInfoWrapper metaInfo) {
        PackageInfoWrapper subPackage = metaInfo.getOrCreateSubPackageWrapperByPath(pagePath);
        if (subPackage != null && !subPackage.isDownloadedBefore(pageStartTime)) {
            // 子包存在，且下载时间不早于页面加载时间（真实下载时间确实晚于页面加载时间或者下载时间未知），则算是网络来源
            return false;
        }
        return true;
    }

    private boolean isMainFromCache(AppMetaInfoWrapper metaInfo) {
        if (metaInfo.mainPackageCached == null || !metaInfo.mainPackageCached.isDownloadedBefore(pageStartTime)) {
            return false;
        }
        return true;
    }

    private boolean isBaseFromCache() {
        PackageInfoWrapper basePackage = CommonTags.getBasePackage(mRuntime);
        if (basePackage == null || !basePackage.isDownloadedBefore(pageStartTime)) {
            // 基础库包缓存不存在，说明基础库包还没下载/加载完毕，或者基础库包的下载时间不早于页面加载时间（真实下载时间确实晚于页面加载时间或者下载时间未知），则算是网络来源
            return false;
        }
        return true;
    }

    private String getCheckUpdateMode(AppMetaInfoWrapper metaInfo) {
        if (metaInfo == null) {
            return UNKNOWN_VALUE;
        }
        // MetaInfo是页面创建之前获取的，则checkUpdateMode是缓存
        if (metaInfo.isFromCache() || metaInfo.getLastUpdateTimeInMs() < pageStartTime) {
            return CACHE_VALUE;
        }
        return NETWORK_VALUE;
    }

    /**
     * 各阶段耗时
     *
     * @param points 入参数组元素需要保证顺序
     * @param value  总耗时
     * @return 各阶段耗时
     */
    public Map<String, Long> createPointDurations(Map<String, Long> points, double value) {
        Map<String, Long> durationDetails = new HashMap<>();
        long lastTimestamp = pageStartTime;

        Set<Map.Entry<String, Long>> entries = points.entrySet();
        int i = 0;
        String lastKey = null;
        for (Map.Entry<String, Long> entry : entries) {
            if (i == entries.size() - 1) {
                lastKey = entry.getKey();
                break;
            }
            if (entry.getValue() == null) {
                continue;
            }
            durationDetails.put(entry.getKey(), entry.getValue() - lastTimestamp);
            lastTimestamp = entry.getValue();

            i++;
        }
        durationDetails.put(lastKey, pageStartTime + (long) value - lastTimestamp);
        return durationDetails;
    }

    public void onPageLoadFail(Context context, AppLoadException exception) {
        if (isPageLoaded) {
            return;
        }
        isPageLoaded = true;
        markError(false);
        recordPageLoadError(exception);
        MetricsEntry entry = once(ReporterFields.REPORT_PAGE_LOAD_SUCCESS_RATE);
        if (isLaunchOnError) {
            entry.tag(CommonTags.TAG_SOURCE_FROM, "reload");
        }
        ContainerLaunchErrorManager.addLaunchErrorCodesToEntry(entry, mRuntime);
        appendDDDLoadPackageDetails(mRuntime, routeId, entry);
        appendOutLinkMetrics(entry, containerDelegate);
        appendLaunchMoment(mRuntime, routeId, entry.getTags());
        entry.tag("errorMessage", exception != null ? exception.getMessage() : null)
                .tag("errorCode", exception != null ? exception.getErrorCode() : null)
                .value(0).sendRealTime();

    }

    public void onPageExit() {
        if (isReportedPageExit) {
            MSCLog.i(TAG, "already reported page exit");
            return;
        }
        isReportedPageExit = true;
        boolean isWhiteScreen = isWhiteScreen();
        MetricsEntry metricsEntry = once(ReporterFields.REPORT_PAGE_EXIT_SUCCESS_RATE);
        metricsEntry.tag("errorCode", getErrorCode(isWhiteScreen));

        BaseRenderer renderer = getRenderer();
        if (renderer != null && renderer.isNativeRender()) {
            renderer.onPageExit();
            renderer.tagRecord(metricsEntry);
        }

        metricsEntry.tag("timeOnPage", timeOnPage);
        metricsEntry.tag("onShowCount", onShowCount);
        long pageTime = System.currentTimeMillis() - pageStartTime;
        metricsEntry.tag(ReporterFields.PAGE_STAY_TIME, pageTime);
        metricsEntry.tag(ReporterFields.LAUNCH_START, pageStartTime);
        metricsEntry.tag(ReporterFields.EXIT_TIME, System.currentTimeMillis());
        MSCLog.i(TAG, "showCount:", onShowCount, "timeOnPage:", timeOnPage, "pageTime:", pageTime);

        if (onAppRouteStartTime > 0 && routeTime > 0) {
            long clientReadyDuration = onAppRouteStartTime - routeTime;
            metricsEntry.tag("clientReadyDuration", clientReadyDuration);
        }

        appendLaunchTaskExecuteStates(metricsEntry);
        //添加包各阶段耗时数据
        appendDDDLoadPackageDetails(this.mRuntime, routeId, metricsEntry);

        appendLaunchMoment(mRuntime, routeId, metricsEntry.getTags());

        metricsEntry.value(isPageLoaded && !existError && !isWhiteScreen ? 1 : 0);
        metricsEntry.sendRealTime();
        pageMemoryMonitor.onPageExit(this);
        this.ffpReportListener.unregisterListener();
        this.attachDataCallback.unregisterListener();
        this.timedAttachDataCallback.unregisterListener();
        this.ffpLogListener.unregisterListener();
    }

    private void appendLaunchTaskExecuteStates(MetricsEntry entry) {
        IAppLoader appLoader = mRuntime.getModule(IAppLoader.class);
        entry.tag(TAG_LAUNCH_TASKS_EXECUTE_STATES, appLoader.getTaskExecuteStateForReport());
    }

    private String getErrorCode(boolean isWhiteScreen) {
        if (!isPageLoaded) {
            if (MSCHornRollbackConfig.enablePageExitFixMissSceneAndDuration()) {
                return onAppRouteStartTime > 0 ? "7000" : "7003";
            } else {
                return "7000";
            }
        }

        if (isWhiteScreen) {
            return "8001";
        }
        if (existJSError) {
            return "4000";
        }
        return existError ? UNKNOWN_VALUE : "0";
    }

    private boolean isWhiteScreen() {
        BaseRenderer renderer = rendererWeakReference.get();
        if (renderer == null) {
            MSCLog.i(TAG, "isWhiteScreen renderer is null");
            return false;
        }
        IRendererView rendererView = renderer.getRendererView();
        if (rendererView.getRendererType() == RendererType.RN
                && rendererView instanceof ViewGroup) {
            // 页面的View树有孩子节点或者没有根View（尚未渲染完毕）
            ViewGroup rnView = (ViewGroup) rendererView;
            return rnView.getChildCount() <= 0;
        }
        // 其他渲染引擎默认都不是白屏
        return false;
    }

    @Override
    public void reportJSError(JSONObject error, MSCRuntime runtime) {
        if (error == null) {
            return;
        }
        // [规则] 当JS Error 是 Fatal 错误的时候，会标记页面加载失败、页面退出失败
        // TODO: 2024/4/17 tianbin Fatal级别的错误一定不可用么
        if (error.optBoolean(TAG_IS_FATAL, true)) {
            markError(true);
            onPageLoadFail(null, null);
        }
        super.reportJSError(error, mRuntime);
    }

    private void markError(boolean isJSError) {
        existError = true;
        if (isJSError) {
            existJSError = true;
        }
    }

    private void addCodeCacheUsageTags(MetricsEntry metricsEntry) {
        JSONObject jsonObject = new JSONObject();
        String codeCacheUsageLevel = "unknown";
        try {
            int totalEvaluateJsFileCount = mscRuntimeReporter.getEvaluateJsFileCount() - evaluateJsFileCountOnPageCreate;
            jsonObject.put("total", totalEvaluateJsFileCount);
            CodeCacheLoadStatusCounter codeCacheLoadStatusCounter = mscRuntimeReporter.getCodeCacheLoadStatusCounter();
            // 如果 codeCacheLoadStatusCounterSnapshot 为null，表明是首页，那么当前的CodeCache使用情况就是当前页面的使用情况
            CodeCacheLoadStatusCounter.Snapshot snapshot = codeCacheLoadStatusCounterSnapshot != null ? codeCacheLoadStatusCounter.minus(codeCacheLoadStatusCounterSnapshot) : codeCacheLoadStatusCounter.getSnapshot();
            for (LoadJSCodeCacheCallback.LoadStatus loadStatus : LoadJSCodeCacheCallback.LoadStatus.values()) {
                jsonObject.put(loadStatus.name(), snapshot.getCount(loadStatus));
            }
            // 映射CodeCache使用率维度
            int loaded = snapshot.getCount(LoadJSCodeCacheCallback.LoadStatus.loaded);
            // 使用率分三个级别：none未使用、全使用、部分使用
            if (loaded == 0) {
                codeCacheUsageLevel = "none";
            } else if (loaded == totalEvaluateJsFileCount) {
                codeCacheUsageLevel = "all";
            } else {
                codeCacheUsageLevel = "part";
            }
            if (MSCHornRollbackConfig.enableFixCodeCacheLaunchReport() && totalEvaluateJsFileCount == 0) {
                codeCacheUsageLevel = "all";
            }
        } catch (JSONException e) {
            MSCLog.e(TAG, e);
        }
        metricsEntry.tag(TAG_CODE_CACHE, jsonObject);
        metricsEntry.tag(TAG_CODE_CACHE_USAGE_LEVEL, codeCacheUsageLevel);
        metricsEntry.tag(TAG_CODE_CACHE_MIN_JS_FILE_SIZE, CodeCacheConfig.INSTANCE.getMinJSFileSize());
    }

    /**
     * 给FFP、FST、FMP等页面加载完成指标添加通用维度
     */
    private void appendTagsToFullPageMetrics(MetricsEntry entry) {
        BaseRenderer renderer = getRenderer();
        entry.tag(TAG_PRELOAD_DURATION_IN_PAGE, getPreloadDurationInMs());
        entry.tag(TAG_IS_PENDING_PRELOAD_BIZ, mRuntime.isPendingPreloadBiz());
        entry.tag(TAG_OTHER_PRELOAD_APP_ID_IN_PAGE, getPreloadAppIdDurationPageLoad());
        entry.tag(TAG_RUNTIME_STATE_BEFORE_LAUNCH, RuntimeStateBeforeLaunch.toReportString(mRuntime.getRuntimeStateBeforeLaunch()));
        entry.tag("lifecycleEvent", MSCAppLifecycleManager.getInstance().findValidLifecycleList(mRuntime.getAppId()));
        entry.tag(TAG_PAGE_START_FROM_APPLICATION_START, (pageStartTime - InitRecorder.getApplicationStartUnixTime()));
        if (mRuntime.getRuntimeReporter() != null) {
            entry.tag(TAG_PRELOAD_FROM_APPLICATION_START, (mRuntime.getRuntimeReporter().getPreloadStartTime() - InitRecorder.getApplicationStartUnixTime()));
        }

        if (renderer != null && renderer.isNativeRender()) {
            int renderActions = renderer.getRenderActions();
//            IRuntimeDelegate.RenderAction.printStringMsg(renderActions);
            entry.tag(TAG_RENDER_ACTIONS, renderActions);
            renderer.putFFPTags(entry);
        }
        if (renderer instanceof MSCWebViewRenderer) {
            entry.tag("loadPageCostTime", ((MSCWebViewRenderer) renderer).loadPageCostTime);
        }
        String destroyRuntimeReason = getDestroyRuntimeReason();
        if (!TextUtils.isEmpty(destroyRuntimeReason)) {
            entry.tag(TAG_DESTROY_RUNTIME_REASON, destroyRuntimeReason);
        }
        appendCacheNotHitReason(entry);
        // TODO @lizhen91 太长了，可能报不上去，暂时缩短，并加开关，线上实验做完后删掉
        if (!MSCHornRollbackConfig.isRollbackFFPReportInFlashSalePage()) {
            addTriggerPrefetchDataScene(entry);
            addCodeCacheUsageTags(entry);
            appendConfigStatus(entry);
        }
        entry.tag("isInit", containerDelegate.isMSCInitedAtContainerOnCreate());
        if (getRenderer() instanceof MSCWebViewRenderer) {
            entry.tag("ppso", MSCHornRollbackConfig.isEnablePrePageStartOptimize(mRuntime.getAppId()));
            entry.tag("oaro", MSCHornRollbackConfig.isEnableOnAppRouteOptimize(mRuntime.getAppId()));
            entry.tag("piat", MSCHornRollbackConfig.getPageInAdvancedTime());
        }
        if (getMetaInfo() != null && !getMetaInfo().isFromCache() && getMetaInfo().hasPreviousExpiredCache()) {
            // 缓存过期场景，上报上个缓存保存的时间。单位ms
            entry.tag("previousExpiredCacheSavedTime", getMetaInfo().getPreviousExpiredCacheSavedTime());
        }
        // 上报单位ms
        entry.tag("metaInfoValidTime", PackageExpirationTimeManager.getInstance().getPackageExpirationTime(mRuntime.getAppId()) * 1000);
        appendPageLoadMetrics(entry);
        appendPackageOfflineReason(entry);
        addWebViewPreloadState(entry);
        entry.tag("preloadBaseErrorMsg", mscRuntimeReporter.getPreloadBaseErrorMsg());
        entry.tag("basePreloadHitControlDetail", mRuntime.getBasePreloadHitControlDetail());
        entry.tag("preloadBizErrorMsg", mRuntime.getBizPreloadErrorMsg());
        entry.tag("bizPreloadHitControlDetail", mRuntime.getBizPreloadHitControlDetail());
        entry.tag("deepPreloadDelayTime", MSCHornPreloadConfig.getDeepPreloadDelayTime());
        Boolean isSubPage = isSubPage();
        entry.tag("isSubPage", isSubPage == null ? "unknown" : isSubPage);
        if (Boolean.TRUE.equals(isSubPage)) {
            appendSubPkgRoot(entry);
        }
        addPrefetchTags(entry);
        addDynamicPrefetchMetrics(entry);
        appendHornField(entry);
    }

    /**
     * 注意：由于FFP线上会采样，所以这个方法不是一定会调用的，除了埋点以外，流程上不要依赖该方法调用
     *
     * @return
     */
    @NonNull
    public Map<String, Object> getFFPTags() {
        long getFFPTagsStart = System.currentTimeMillis();
        PerfTrace.begin(GET_FFP_TAGS);
        BaseRenderer renderer = getRenderer();
        onFFPEnd(renderer != null ? renderer.getViewId() : -1);

        MetricsEntry entry = once("FFP");
        appendTagsToFullPageMetrics(entry);
        appendStageSpendTime(entry, getFFPTagsStart);
        appendPreloadStrategy(entry);
        appendDDDLoadPackageDetails(this.mRuntime, routeId, entry);
        appendEngineManageStrategy(entry);

        //启动任务执行时机及任务执行情况
        appendLaunchMoment(this.mRuntime, routeId, entry.getTags());
        appendLaunchTaskInfo(this.mRuntime, routeId, entry.getTags());

        Map<String, Object> tags = entry.getTags();
        tags.remove(TAG_PAGE_PATH);
        PerfTrace.end(GET_FFP_TAGS);
        MSCLog.i(TAG, "FFP, msc native tags:", tags);
        return tags;
    }

    private Boolean isSubPage() {
        MSCAppModule mscAppModule = mRuntime.getMSCAppModule();
        if (mscAppModule == null) {
            return null;
        }
        return mscAppModule.isSubPackagePage(pagePath);
    }

    private void addWebViewPreloadState(MetricsEntry entry) {
        if (MSCHornPreloadConfig.needPreloadWebView()) {
            BaseRenderer baseRenderer = rendererWeakReference.get();
            if (baseRenderer instanceof MSCWebViewRenderer) {
                entry.tag("webViewPreloadState", getPreloadStateStr());
                MSCWebView mscWebView = ((MSCWebViewRenderer) baseRenderer).getMSCWebView();
                entry.tag("reuseCachedWebViewOnFirstPage", mscWebView.getWebViewCreateScene() == null ? "" : mscWebView.getWebViewCreateScene());
            }
        }
    }

    private void addTriggerPrefetchDataScene(MetricsEntry entry) {
        RequestPrefetchManager requestPrefetchManager = mRuntime.getRequestPrefetchManager();
        if (requestPrefetchManager == null) {
            return;
        }
        TriggerPrefetchDataScene triggerPrefetchDataScene = requestPrefetchManager.getLastTriggerPrefetchDataScene();
        if (triggerPrefetchDataScene != null) {
            entry.tag(TAG_TRIGGER_PREFETCH_DATA_SCENE, triggerPrefetchDataScene.getReportValue());
        }
    }

    private void addPrefetchTags(MetricsEntry entry) {
        RequestPrefetchManager requestPrefetchManager = mRuntime.getRequestPrefetchManager();
        if (requestPrefetchManager == null) {
            return;
        }
        // 值可能为空。如果FFP后，定位仍然未获取到则为空值，不上报。或者该次数据预拉取不需要定位信息，则不上报
        String locationType = requestPrefetchManager.getLastPrefetchLocationType();
        if (!TextUtils.isEmpty(locationType)) {
            entry.tag(TAG_PREFETCH_LOCATION_TYPE, locationType);
        }
        // 值可能为空，如果为空说明未触发
        Boolean lastPrefetchIsAsync = requestPrefetchManager.getLastPrefetchIsAsync();
        if (lastPrefetchIsAsync != null) {
            entry.tag(TAG_PREFETCH_IS_ASYNC, lastPrefetchIsAsync);
        }
    }

    // 埋点信息：https://km.sankuai.com/collabpage/2705244722   3.3.1
    private void addDynamicPrefetchMetrics(MetricsEntry entry) {
        IDataPrefetchModule dataPrefetchModule = mRuntime.getModule(IDataPrefetchModule.class);
        // 共有的记录
        MSCPrefetchPhaseRecord prefetchCommonRecord = dataPrefetchModule.getCommonPrefetchRecord((int) routeId);
        if (prefetchCommonRecord != null) {
            entry.tag("dynamicPrefetchTime", prefetchCommonRecord.startPrefetchTime);
            entry.tag("dynamicPrefetchFetchConfigPackageDuration", prefetchCommonRecord.startParseConfigTime - prefetchCommonRecord.startFetchConfigTime);
            entry.tag("dynamicPrefetchParseConfigDuration", prefetchCommonRecord.startParseValueTime - prefetchCommonRecord.startParseConfigTime);
        }
        // 每个网络请求对应的记录
        Map<String, MSCPrefetchPhaseRecord> prefetchPhaseRecordOfUrl = dataPrefetchModule.getPrefetchPhaseRecordOfUrl((int) routeId);
        if (prefetchPhaseRecordOfUrl == null) {
            return;
        }
        JSONArray urlDurationArray = new JSONArray();
        for (MSCPrefetchPhaseRecord record : prefetchPhaseRecordOfUrl.values()) {
            JSONObject jsonObject = new JSONObject();
            String pureUrl = UrlUtils.getPureUrl(record.requestUrl);
            if (pureUrl == null) {
                pureUrl = "unknown";
            }
            try {
                jsonObject.put("dynamicPrefetchUrl", pureUrl);
                jsonObject.put("dynamicPrefetchResolveValueDuration", record.startMsiRequestTime - record.startParseValueTime);
                jsonObject.put("dynamicPrefetchNetWorkDuration", record.endMsiRequestTime - record.startMsiRequestTime);
                jsonObject.put("dynamicPrefetchSendDataToFeTime", record.sendDataToFeTime);
            } catch (JSONException e) {
                MSCLog.e(TAG, "addDynamicPrefetchMetrics json error", e);
                return;
            }
            urlDurationArray.put(jsonObject);
        }
        entry.tag("dynamicPrefetchUrlDurations", urlDurationArray);
    }

    public void clearFlushCount() {
        flushCount = 0;
    }

    public int getFlushCount() {
        return flushCount;
    }

    private void flushJsPerfEvents() {
        flushCount++;

        // 刷新前端的埋点
        MetricsModule metricsModule = mRuntime.getModule(MetricsModule.class);
        if (metricsModule != null) {
            metricsModule.flushPerfEvents();
        } else {
            MSCLog.i(TAG, "metricsModule is null");
        }
    }

    public void onFFPEnd(int pageId) {
        long ffpEndTime = System.currentTimeMillis();// TODO chdc 秒开改了测量口径了，该值不是秒开结束值了
        if (MSCTraceUtil.TRACE_LEVEL == MSCTraceUtil.LEVEL_NONE) {
            // 未使用埋点包时才在FFP时刷新前端埋点
            flushJsPerfEvents();
        }

        MSCExecutors.submit(() -> {
            // 往Performance中增加秒开结束点
            mRuntime.getPerformanceManager().onFFP(pagePath, pageId, pageStartTime, ffpEndTime);
        });
        pageMemoryMonitor.onFFPEnd();
    }

    /**
     * 分阶段耗时打点，文档：https://km.sankuai.com/collabpage/1447605463
     *
     * @param entry
     */
    private void appendStageSpendTime(MetricsEntry entry, long getFFPTagsStart) {
        // 切出来 FP-当前的所有Trace
        PerfTraceAnalyzer totalEvents = new PerfTraceAnalyzer(PerfTrace.getOnlineEvents());
        PerfTraceAnalyzer ta = totalEvents.sliceStart(pageStartTime);

        long ffpEndTime = System.currentTimeMillis();
        long fpTime = pageStartTime + spendTimeOfFP;
        // 数据预拉取开始时间点可能在 pageStartTime 之前，所以从所有埋点中倒序查找
        long prefetchStartTime = totalEvents.getStartTime(REQUEST_PREFETCH, ffpEndTime, true);
        PerfTraceAnalyzer prefetchTraceAnalyzer;
        if (prefetchStartTime > 0) {
            // 数据预拉取可能在PageStart之前就开启了，所以先找到上一次数据预拉取开始点，然后开始查找数据预拉取相关的埋点，但是不要对于一定出现在pageStartTime之后的埋点使用，因为该PerfTraceAnalyzer可能不准确
            prefetchTraceAnalyzer = totalEvents.sliceStart(prefetchStartTime);
        } else {
            prefetchTraceAnalyzer = ta;
        }
        long prefetchEndTime = prefetchTraceAnalyzer.getEndTime(REQUEST_PREFETCH, ffpEndTime, true);
        long getBackgroundFetchDataStartTime = ta.getStartTime(GET_BACKGROUND_FETCH_DATA);
        long getBackgroundFetchDataEndTime = ta.getEndTime(GET_BACKGROUND_FETCH_DATA);

        long signInterceptorBefore = ta.getEndTime(SIGN_INTERCEPTOR_BEFORE) - ta.getStartTime(SIGN_INTERCEPTOR_BEFORE);
        long signInterceptorAfter = ta.getEndTime(SIGN_INTERCEPTOR_AFTER) - ta.getStartTime(SIGN_INTERCEPTOR_AFTER);

        // 首帧渲染耗时
        entry.tag("FP", spendTimeOfFP);

        entry.tag("signInterceptorBefore", signInterceptorBefore);
        entry.tag("signInterceptorAfter", signInterceptorAfter);

        // 时间点
        entry.tag("pageStartTime", pageStartTime);
        entry.tag("prefetchStartTime", prefetchStartTime);
        entry.tag("prefetchNetworkStartTime", prefetchTraceAnalyzer.getStartTime(REQUEST_PREFETCH_NETWORK));
        entry.tag("prefetchGetBusinessBodyStartTime", prefetchTraceAnalyzer.getStartTime(REQUEST_PREFETCH_GET_BUSINESS_BODY_PARAMS));
        entry.tag("prefetchGetBusinessUrlStartTime", prefetchTraceAnalyzer.getStartTime(REQUEST_PREFETCH_GET_BUSINESS_URL_PARAMS));
        entry.tag("prefetchGetBusinessUrlEndTime", prefetchTraceAnalyzer.getEndTime(REQUEST_PREFETCH_GET_BUSINESS_URL_PARAMS));
        entry.tag("fpTime", fpTime);
        entry.tag(PerfEventConstant.PREFETCH_END_TIME, prefetchEndTime);
        entry.tag("getBackgroundFetchDataStartTime", getBackgroundFetchDataStartTime);
        entry.tag("getBackgroundFetchDataEndTime", getBackgroundFetchDataEndTime);

        BaseRenderer renderer = getRenderer();
        if (renderer != null && renderer.getType() == RendererType.NATIVE) {
            // 【闪购特殊埋点】
            long createListTime = ta.getStartTime(PerfEventName.R_LIST_CREATE, -1, this.listTraceFilter, false);
            renderer.putTags(entry, ta, createListTime, listOperatorFilter);
        }
        entry.tag("getFFPTagsSpend", System.currentTimeMillis() - getFFPTagsStart);
    }

    private void initListFilter() {
        if (pagePath != null && pagePath.startsWith(SG_STORE_PAGE_PATH)) {
            // 闪购商家页只拿指定的长列表的埋点
            this.listTraceFilter = new ITraceEventFilter() {
                @Override
                public boolean test(PerfEvent event) {
                    return event.extra != null && "list-view".equals(event.extra.opt("id"));
                }
            };
        } else {
            this.listTraceFilter = null;
        }
        this.listOperatorFilter = new ITraceEventFilter() {
            @Override
            public boolean test(PerfEvent event) {
                String name = event.name;
                if (name.startsWith(LIST_LOAD) || name.startsWith(R_LIST_DATA_HANDLE)) {
                    return listTraceFilter == null || listTraceFilter.test(event);
                }
                return false;
            }
        };
    }

    public void reportScrollVelocity(List<Integer> velocityArray) {
        if (velocityArray == null || velocityArray.isEmpty()) {
            return;
        }
        StringBuilder sb = new StringBuilder();
        for (int i = 0; i < velocityArray.size() - 1; i++) {
            sb.append(velocityArray.get(i)).append(",");
        }
        sb.append(velocityArray.get(velocityArray.size() - 1));
        record("msc.page.scroll.velocity").tag("velocity", sb.toString()).sendDelay();
    }

    public void reportRouteSucceed(JSONObject params) {
        record(ReporterFields.REPORT_PAGE_ROUTE_END_COUNT)
                .value(ReportValue.SUCCESS)
                .tags(JsonUtil.toMap(params))
                .sendRealTime();

        MSCLog.i(TAG, "reportRouteSucceed routeExceptCount", RouteReporter.routeExceptCount.decrementAndGet(), mRuntime, params);
    }

    public AppMetaInfoWrapper getMetaInfo() {
        AppMetaInfoWrapper metaInfo = null;
        if (mRuntime != null) {
            MSCAppModule mscAppModule = mRuntime.getMSCAppModule();
            if (mscAppModule != null) {
                metaInfo = mscAppModule.getMetaInfo();
            }
        }
        return metaInfo;
    }

    public long getPageStartTime() {
        return pageStartTime;
    }

    public void setRenderPageStartTime(long timestamp) {
        this.renderPageStartTime = timestamp;
    }

    public long getRenderPageStartTime() {
        return renderPageStartTime;
    }

    public void setOnAppRouteStartTime(long timestamp) {
        this.onAppRouteStartTime = timestamp;
    }

    // 预期一个appPageReporter只会发送一次FFPEnd
    public boolean isFirstSendFFPEnd() {
        // 打开回滚开关后，取消此检查。与原有逻辑相同，一直允许发送FFPEnd
        if (!MSCHornRollbackConfig.readConfig().enableFFPEndSendOnce) {
            sendFFPEndEventTime = System.currentTimeMillis();
            return true;
        }
        if (!isSendFFPEnd) {
            isSendFFPEnd = true;
            sendFFPEndEventTime = System.currentTimeMillis();
            return true;
        }
        return false;
    }


    public void onHide() {
        pageMemoryMonitor.onHide();

        if (!isPageOnShow) {
            return;
        }
        isPageOnShow = false;
        onShowCount++;
        timeOnPage += System.currentTimeMillis() - lastOnShowTime;
    }

    /**
     * 对前端要上报的页面相关指标增加页面级通用维度
     *
     * @param type
     * @param value
     * @param tags
     */
    public void reportFeMetrics(String type, long value, Map<String, Object> tags) {
        MetricsEntry metricsEntry = record(type);
        if (MSC_FE_PAGE_FMP.equals(type) || MSC_FE_PAGE_FST.equals(type)) {
            // FST 和 FMP指标和FP、FFP一样增加一些特殊的维度
            appendTagsToFullPageMetrics(metricsEntry);
        }
        metricsEntry
                .tags(tags)
                //https://km.sankuai.com/page/951566560
                .value(value)
                .sendRealTime();
    }

    public void onShow() {
        pageMemoryMonitor.onShow();

        if (isPageOnShow) {
            return;
        }
        isPageOnShow = true;
        lastOnShowTime = System.currentTimeMillis();
    }

    public MSCFFPReportListener getFfpReportListener() {
        return ffpReportListener;
    }

    public static void appendLaunchMoment(MSCRuntime mscRuntime, long routeId, Map<String, Object> tags) {
        if (!MSCHornRollbackConfig.enableLaunchTaskOnRoute()) {
            return;
        }

        if (mscRuntime == null || tags == null) {
            return;
        }

        PageLaunchInfo pageLaunchInfo = InstrumentLaunchManager.getInstance().getLaunchInfo(String.valueOf(routeId));
        if (pageLaunchInfo != null) {
            tags.put("launchMoment", pageLaunchInfo.isLaunchWhenInstrument ? "onRoute" : "onContainer");
            tags.put("useRouteRuntime", pageLaunchInfo.canUseInstrumentRuntime);
        } else {
            tags.put("launchMoment", "onContainer");
            tags.put("useRouteRuntime", false);
        }
    }

    public static void appendLaunchTaskInfo(MSCRuntime mscRuntime, long routeId,  Map<String, Object> tags) {
        if (!MSCHornRollbackConfig.enableLaunchTaskOnRoute() || tags == null) {
            return;
        }

        PageLaunchInfo pageLaunchInfo = InstrumentLaunchManager.getInstance().getLaunchInfo(String.valueOf(routeId));
        if (pageLaunchInfo != null) {
            Map<String, Object> routeTaskInfo = getTasksInfo(mscRuntime, pageLaunchInfo.getInstrumentLaunchTask());
            if (routeTaskInfo != null && !routeTaskInfo.isEmpty()) {
                tags.put("routeTasks", routeTaskInfo);
            }

            Map<String, Object> startPageTaskInfo = getTasksInfo(mscRuntime, pageLaunchInfo.getStartPageTask());
            if (startPageTaskInfo != null && !startPageTaskInfo.isEmpty()) {
                tags.put("launchTasks", startPageTaskInfo);
            }
            tags.put("routeStartTime", pageLaunchInfo.routeStartTime);
            tags.put("launchStartTime", pageLaunchInfo.launchPageStartTime);
        }
    }

    private static Map<String, Object> getTasksInfo(MSCRuntime runtime, ITask<?> targetTask) {
        if (runtime == null) {
            return null;
        }

        IAppLoader appLoader = runtime.getModule(IAppLoader.class);
        TaskManager taskManager = appLoader.getTaskManager();
        if (taskManager == null) {
            return null;
        }
        Map<String, Object> taskInfoMap = new HashMap<>();
        Set<ITask<?>> iTaskSet = taskManager.getAllDependTasks(targetTask);
        if (iTaskSet != null && !iTaskSet.isEmpty()) {
            for (ITask<?> task : iTaskSet) {
                TaskManager.TaskState taskState = taskManager.getTaskState(task);
                if (taskState == null) {
                    continue;
                }
                if (taskState.getExecuteStatus().isFinished() || taskState.getExecuteStatus() == ExecuteStatus.RUNNING) {
                    taskInfoMap.put(task.getName()+"_b", taskState.getExecuteStartPoint());
                    if (taskState.getExecuteStatus().isFinished()) {
                        taskInfoMap.put(task.getName()+"_e", taskState.getExecuteFinishedPoint());
                    }
                }
                taskInfoMap.put(task.getName()+"Status", taskState.getExecuteStatus());
            }
        }

        return taskInfoMap;
    }

    public static void appendDDDLoadPackageDetails(MSCRuntime mscRuntime, long routeId, MetricsEntry metricsEntry) {
        if (mscRuntime == null) {
            return;
        }

        if (MSCHornRollbackConfig.enableAddLoadPackageDetails()) {
            IPageLoadModule pageLoadModule = mscRuntime.getModule(IPageLoadModule.class);
            if (pageLoadModule != null) {
                DDLoadPhaseData mainPackageDetails = pageLoadModule.getLoadPackageDetails(routeId, true);
                DDLoadPhaseData subPackageDetails = pageLoadModule.getLoadPackageDetails(routeId, false);
                if (mainPackageDetails != null) {
                    String mainJsonString = ConversionUtil.getGson().toJson(mainPackageDetails);
                    if (!TextUtils.isEmpty(mainJsonString)) {
                        try {
                            metricsEntry.tag("loadMainPackageDetails", new JSONObject(mainJsonString));
                        } catch (JSONException e) {
                        }
                    }
                }
                if (subPackageDetails != null) {
                    String subJsonString = ConversionUtil.getGson().toJson(subPackageDetails);
                    if (!TextUtils.isEmpty(subJsonString)) {
                        try {
                            metricsEntry.tag("loadSubPackageDetails", new JSONObject(subJsonString));
                        } catch (JSONException e) {
                        }
                    }
                }
            }
        }
    }

    public void onFFPSampleStatusCallBack() {
        if (!MSCHornRollbackConfig.enablePhasedPrimaryMetricsReport()) {
            return;
        }
        for (MetricsEntry metricsEntry : launchStagePendingMetrics) {
            reportLaunchStageMetrics(metricsEntry);
        }
        launchStagePendingMetrics.clear();
    }

    public void onAttachDataCallBack(FFPPageInfo pageInfo) {
        attachDataCallBackReceiveTime = System.currentTimeMillis();
        once(REPORT_FFP_END_TO_ATTACH_CALLBACK_DURATION)
                .tags(pageInfo.getTags())
                .value(attachDataCallBackReceiveTime - pageInfo.getEndTimeMills())
                .sendRealTime();
    }

    public void onTimedAttachDataCallBack(FFPPageInfo pageInfo) {
        timedAttachDataCallBackReceiveTime = System.currentTimeMillis();
        once(REPORT_ATTACH_TO_TIME_ATTACH_CALLBACK_DURATION)
                .tags(pageInfo.getTags())
                .value(timedAttachDataCallBackReceiveTime - attachDataCallBackReceiveTime)
                .sendRealTime();
    }

    public void onFFPReportCallBack(@NonNull FFPReportListener.IReportEvent event) {
        if (!MSCHornRollbackConfig.enablePhasedPrimaryMetricsReport()) {
            return;
        }
        // 获取当前api调用次数
        MSIManagerModule msiManagerModule = mRuntime.getModule(MSIManagerModule.class);
        long curSyncApiInvokeCount = 0;
        if (msiManagerModule != null) {
            curSyncApiInvokeCount = msiManagerModule.getSyncApiInvokeCounts();
        }
        if (pageRenderReported) {
            return;
        }
        pageRenderReported = true;
        // 一级指标: 页面渲染时长
        MetricsEntry metricsEntry = once(ReporterFields.REPORT_PAGE_RENDER_DURATION)
                .tag(TAG_SYNC_API_COUNT, curSyncApiInvokeCount - pageRenderLaunchStageApiInvokeCounts)
                .value(event.endTimeInMs() - pageRenderLaunchStageTime);
        if (!ffpLogListener.isReceived()) {
            launchStagePendingMetrics.add(metricsEntry);
        } else {
            reportLaunchStageMetrics(metricsEntry);
        }
    }

    public void reportFFPApiDetails(Map<String, Object> tags) {
        record(ReporterFields.REPORT_PAGE_FFP_API_DETAILS)
                .tags(tags)
                .sendDelay();
    }

    public void onAppRouteLaunchStage(long timestamp) {
        appRouteLaunchStageTime = timestamp;
        MSIManagerModule msiManagerModule = mRuntime.getModule(MSIManagerModule.class);
        if (msiManagerModule != null) {
            appRouteLaunchStageApiInvokeCounts = msiManagerModule.getSyncApiInvokeCounts();
        }
    }

    public void onLaunchStageChange(String stage, long timestamp) {
        if (!MSCHornRollbackConfig.enablePhasedPrimaryMetricsReport()) {
            return;
        }
        MetricsEntry metricsEntry = null;
        // 获取当前api调用次数
        MSIManagerModule msiManagerModule = mRuntime.getModule(MSIManagerModule.class);
        long curSyncApiInvokeCount = 0;
        if (msiManagerModule != null) {
            curSyncApiInvokeCount = msiManagerModule.getSyncApiInvokeCounts();
        }
        if (TextUtils.equals(stage, BaseRenderer.CONTAINER_LAUNCH_END)) {
            // 一级指标: 容器启动时长
            metricsEntry = once(ReporterFields.REPORT_PAGE_CONTAINER_LAUNCH_DURATION)
                    .value(timestamp);
        } else if (TextUtils.equals(stage, BaseRenderer.PAGE_RENDER_START)) {
            // 一级指标: 业务启动时长
            metricsEntry = once(ReporterFields.REPORT_PAGE_BIZ_LAUNCH_DURATION)
                    .tag(TAG_SYNC_API_COUNT, curSyncApiInvokeCount - appRouteLaunchStageApiInvokeCounts)
                    .value(timestamp - appRouteLaunchStageTime);
            pageRenderLaunchStageTime = timestamp;
            pageRenderLaunchStageApiInvokeCounts = curSyncApiInvokeCount;
        }
        // 处理指标上报
        if (metricsEntry == null) {
            return;
        }
        if (!ffpLogListener.isReceived()) {
            launchStagePendingMetrics.add(metricsEntry);
        } else {
            reportLaunchStageMetrics(metricsEntry);
        }
    }

    /**
     * 上报一级指标
     */
    private void reportLaunchStageMetrics(MetricsEntry metricsEntry) {
        if (metricsEntry == null) {
            return;
        }
        if (!ffpLogListener.isEnable()) {
            return;
        }
        metricsEntry.tag(TAG_FFP_SAMPLE_TYPE, ffpLogListener.getSampleType());
        if (TextUtils.equals(metricsEntry.getKey(), ReporterFields.REPORT_PAGE_CONTAINER_LAUNCH_DURATION)) {
            metricsEntry.value(metricsEntry.getValue() - ffpLogListener.getStartTime());
        }
        metricsEntry.sendDelay();
    }
}