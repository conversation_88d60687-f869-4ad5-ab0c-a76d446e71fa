package com.meituan.msc.modules.page.render;

import com.meituan.msc.modules.container.IContainerDelegate;

public class AppRouteParam {
    String param;
    int viewId;
    IContainerDelegate containerDelegate;
    boolean firstRender;
    long launchStartTimeCurrentTimeMillis;
    String openType;

    public AppRouteParam(String paramsString, int viewId, boolean firstRender, long launchStartTimeCurrentTimeMillis, String openType, IContainerDelegate containerDelegate) {
        this.param = paramsString;
        this.viewId = viewId;
        this.containerDelegate = containerDelegate;
        this.firstRender = firstRender;
        this.launchStartTimeCurrentTimeMillis = launchStartTimeCurrentTimeMillis;
        this.openType = openType;
    }

    public String getOpenType() {
        return openType;
    }

    public boolean isFirstRender() {
        return firstRender;
    }

    public long getLaunchStartTimeCurrentTimeMillis() {
        return launchStartTimeCurrentTimeMillis;
    }

    public String getParam() {
        return param;
    }

    public int getViewId() {
        return viewId;
    }

    public IContainerDelegate getContainerDelegate() {
        return containerDelegate;
    }
}
