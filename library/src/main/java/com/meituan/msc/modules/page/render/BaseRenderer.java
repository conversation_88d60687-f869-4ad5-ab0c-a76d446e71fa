package com.meituan.msc.modules.page.render;

import android.content.Context;
import android.support.annotation.CallSuper;
import android.support.annotation.NonNull;
import android.support.annotation.Nullable;
import android.text.TextUtils;
import android.view.View;
import android.view.Window;

import com.meituan.android.common.weaver.interfaces.ffp.FFPPageInfo;
import com.meituan.android.common.weaver.interfaces.ffp.FFPReportListener;
import com.meituan.msc.common.config.MSCConfig;
import com.meituan.msc.common.executor.MSCExecutors;
import com.meituan.msc.common.framework.MPListenerManager;
import com.meituan.msc.common.framework.interfaces.PageEventListener;
import com.meituan.msc.common.perf.PerfEventConstant;
import com.meituan.msc.common.report.MetricsEntry;
import com.meituan.msc.common.utils.IntentUtil;
import com.meituan.msc.common.utils.PathUtil;
import com.meituan.msc.common.utils.WhiteScreenUtil;
import com.meituan.msc.modules.container.ContainerController;
import com.meituan.msc.modules.container.ContainerReporter;
import com.meituan.msc.modules.container.IContainerDelegate;
import com.meituan.msc.modules.container.IMSCContainer;
import com.meituan.msc.modules.container.OpenParams;
import com.meituan.msc.modules.engine.AppService;
import com.meituan.msc.modules.engine.IRendererManager;
import com.meituan.msc.modules.engine.MSCHornRollbackConfig;
import com.meituan.msc.modules.engine.MSCRuntime;
import com.meituan.msc.modules.engine.RendererPreloadType;
import com.meituan.msc.modules.manager.MSCEvent;
import com.meituan.msc.modules.manager.MSCModule;
import com.meituan.msc.modules.page.UserReporter;
import com.meituan.msc.modules.page.render.webview.MSCWebViewRenderer;
import com.meituan.msc.modules.page.render.webview.OnReloadListener;
import com.meituan.msc.modules.page.render.webview.RenderCacheType;
import com.meituan.msc.modules.reporter.CommonTags;
import com.meituan.msc.modules.reporter.MSCCommonTagReporter;
import com.meituan.msc.modules.reporter.MSCLog;
import com.meituan.msc.modules.reporter.PerformanceListener;
import com.meituan.msc.modules.reporter.ReporterFields;
import com.meituan.msc.modules.reporter.prexception.AppPageState;
import com.meituan.msc.modules.reporter.prexception.AppServiceState;
import com.meituan.msc.modules.reporter.whitescreen.WhiteScreenReasonRecord;
import com.meituan.msc.modules.service.ServiceInstance;
import com.meituan.msc.modules.update.MSCAppModule;
import com.meituan.msc.util.perf.PerfEventRecorder;
import com.meituan.msc.util.perf.PerfTrace;
import com.meituan.msc.util.perf.analyze.ITraceEventFilter;
import com.meituan.msc.util.perf.analyze.PerfTraceAnalyzer;
import com.meituan.msi.provider.IContainerStageProvider;

import java.util.Collections;
import java.util.HashMap;
import java.util.List;
import java.util.Map;
import java.util.Set;

public abstract class BaseRenderer implements IRenderer {
    public final String TAG = "BaseRenderer@" + Integer.toHexString(hashCode());

    protected Context mApplicationContext;
    protected MSCRuntime mRuntime;
    protected MSCAppModule mMSCAppModule;
    protected IContainerDelegate mContainerDelegate;
    private PerfEventRecorder perfEventRecorder;
    private ServiceInstance mServiceInstance;

    public int viewId = View.NO_ID;
    private int reloadViewId = View.NO_ID; //用来记录reload发生之前，原来的viewId，供逻辑层前端在页面栈中查找并替换新的

    public BasePageData pageData = createPageData();

    protected abstract BasePageData createPageData();

    private RendererPreloadType preloadType = RendererPreloadType.NONE;
    /**
     * 是否深度预热
     */
    private boolean hasTriggerDeepPreloadWebView = false;
    /**
     * 是否预热空白页面
     */
    private boolean hasTriggerPreloadBlankWebView = false;
    /**
     * 是否第四段webView预热
     */
    private boolean hasTriggerFourthSegmentPreloadWebView = false;
    private final IWhiteScreenHappenHelper whiteScreenHappenHelper = new WhiteScreenHappenHelper();

    public interface AppPageListener {
        void onPageReload();

        void onFirstRender();

        /**
         * 更新沉浸模式热区数据
         *
         * @param params 热区数据
         */
        void onSinkModeHotZone(@Nullable String params);
    }

    public void setPerfEventRecorder(@NonNull PerfEventRecorder recorder) {
        perfEventRecorder = recorder;
    }

    public PerfEventRecorder getPerfEventRecorder() {
        return perfEventRecorder;
    }

    private boolean needReload;
    public boolean isNeedReload() {
        return needReload;
    }

    public void setNeedReload(boolean needReload) {
        this.needReload = needReload;
    }

    public void setHasTriggerDeepPreloadWebView(boolean hasTriggerDeepPreloadWebView) {
        this.hasTriggerDeepPreloadWebView = hasTriggerDeepPreloadWebView;
    }

    public boolean hasTriggerDeepPreloadWebView() {
        return hasTriggerDeepPreloadWebView;
    }

    public void setHasTriggerPreloadBlankWebView(boolean hasTriggerPreloadBlankWebView) {
        this.hasTriggerPreloadBlankWebView = hasTriggerPreloadBlankWebView;
    }

    public boolean hasTriggerPreloadBlankWebView() {
        return hasTriggerPreloadBlankWebView;
    }

    public void setHasTriggerFourthSegmentPreloadWebView(boolean hasTriggerFourthSegmentPreloadWebView) {
        this.hasTriggerFourthSegmentPreloadWebView = hasTriggerFourthSegmentPreloadWebView;
    }

    public boolean hasTriggerFourthSegmentPreloadWebView() {
        return hasTriggerFourthSegmentPreloadWebView;
    }

    /**
     * 属于单一具体页面（pagePath）的数据，而非与Renderer实例或整个小程序有关，在复用Renderer时应清除
     */
    public static class BasePageData {
        public String mPagePath; //带query
        public volatile PageEventListener mEventListener;
        public AppPageListener mAppPageListener;    //TODO 除onFirstRender之外的方法均为WebView特有

        public ContainerReporter containerReporter;

        public boolean isPageStartReported = false;
        public boolean isPageEndReported = false;  //页面启动结束标识
        public PageStartRecord pageStartRecord = new PageStartRecord();
        public LoadEndRecord pageEndRecord;    //页面加载结果记录
        public AppPageReporter appPageReporter;
        public String webViewPreloadState;
        String curRenderCache = null;   // record last cache for reload use

        public boolean isShow;
        public boolean hasFirstRender; //首次渲染，可能由快照引起，此时不需要前端js启动流程完成

        public long onAppRouteTime;
        public String openType;
        @AppPageState.State
        public volatile String pageState = AppPageState.DEFAULT;
        @AppServiceState.State
        public volatile String serviceState = AppServiceState.DEFAULT;
        public boolean isWhiteForegroundShow;
        // MSI getContainerStage接口返回值，容器启动阶段终点为onAppRoute，不要使用此字段做埋点上报
        public String msiContainerStage = "";
        public boolean hasReceiveOnAppRoute = false;
        public boolean hasReceiveAfterExecOnPageStart = false;
    }

    public final static String CONTAINER_LAUNCH_END = "containerLaunchEnd";
    public final static String PAGE_RENDER_START = "pageRenderStart";

    public static class PageStartRecord {
        public long pageStartTimeCurrentTimeMillis;
    }

    public static class LoadEndRecord {
        public String state;
        public Map<String, Object> params;
    }

    public ServiceInstance getServiceInstance() {
        return mServiceInstance;
    }

    @Override
    @CallSuper
    public void init(Context context, MSCRuntime runtime) {
        mApplicationContext = context.getApplicationContext();
        this.mRuntime = runtime;
        //获取 JS 引擎
        mServiceInstance = mRuntime.getModule(AppService.class).getJsExecutor();
        perfEventRecorder = mRuntime.getPerfEventRecorder();
        mMSCAppModule = runtime.getMSCAppModule();
        whiteScreenHappenHelper.beforeFFP();
    }

    @Override
    public Set<MSCModule> getMSCModules() {
        return Collections.emptySet();
    }

    @Override
    public String getPagePath() {
        return pageData.mPagePath;
    }

    public BaseRenderer setContainerDelegate(IContainerDelegate containerDelegate) {
        mContainerDelegate = containerDelegate;
        return this;
    }

    public IContainerDelegate getContainerDelegate() {
        return mContainerDelegate;
    }

    public Window getWindow() {
        if (mContainerDelegate == null) {
            return null;
        }
        IMSCContainer container = mContainerDelegate.getMSCContainer();
        if (container != null) {
            return container.getWindow();
        }
        return null;
    }

    @CallSuper
    @Override
    public void loadPage(String pagePath, long routeTime) {
        MSCLog.i(TAG, "loadPage: ", pagePath);
        pageData.mPagePath = pagePath;
        reportPageStart();
        if (pageData.mAppPageListener != null) {
            pageData.mAppPageListener.onPageReload();
        }
    }

    @Override
    public void onAttach() {

    }

    /**
     * 在渲染器所在的View结构销毁时需要调用此方法
     * 可能响应为销毁或回收
     */
    @Override
    public void onDetach() {
        MSCLog.i(TAG, "onDetach");
        reportPageCancel();
        IRendererManager rendererManager = mRuntime.getModule(IRendererManager.class);
        pageData.appPageReporter.onPageExit();
        if (rendererManager != null) {
            rendererManager.recycleOrDestroyRenderer(this);
        }
        whiteScreenHappenHelper.exit();
    }

    public MSCCommonTagReporter getReporter() {
        MSCCommonTagReporter reporter = null;
        if (pageData != null && pageData.appPageReporter != null) {
            reporter = pageData.appPageReporter;
        }
        if (reporter == null) {
            reporter = new MSCCommonTagReporter(CommonTags.build(mRuntime, this, null));
        }
        return reporter;
    }

    /**
     * retainRenderer获取render时设置
     *
     * @param listener
     * @return
     */
    public BaseRenderer setEventListener(PageEventListener listener) {
        pageData.mEventListener = listener;
        return this;
    }

    /**
     * 初始化PageViewWrapper时设置
     *
     * @param appPageListener
     * @return
     */
    public BaseRenderer addAppPageListener(AppPageListener appPageListener) {
        pageData.mAppPageListener = appPageListener;
        return this;
    }

    public void setContainerReporter(ContainerReporter containerReporter, long routeTime) {
        pageData.containerReporter = containerReporter;
        pageData.appPageReporter.markLaunchPage(true, containerReporter, routeTime);
    }

    /**
     * retainRenderer获取render时设置
     *
     * @param reporter
     * @return
     */
    public BaseRenderer setAppPageReporter(AppPageReporter reporter) {
        pageData.appPageReporter = reporter;
        pageData.appPageReporter.commonTag("rendererPreloadType", RendererPreloadType.toReportString(preloadType));
        return this;
    }

    public BaseRenderer setPerformanceListener(PerformanceListener performanceListener) {
//        pageData.mPageReporter.addPerformanceListener(performanceListener);
        return this;
    }

    /**
     * 新建Page时设置
     *
     * @param timeCurrentTimeMillis
     */
    public void setPageStartTime(long timeCurrentTimeMillis) {
        pageData.pageStartRecord.pageStartTimeCurrentTimeMillis = timeCurrentTimeMillis;
    }

    protected void reportPageStart() {
        // 避免tab切换重复上报
        if (pageData.isPageStartReported) {
            return;
        }
        pageData.isPageStartReported = true;
        try {
            if (mMSCAppModule.hasMetaInfo()) {
                pageData.appPageReporter.commonTag("foundationVersion", mMSCAppModule.getBasePkgVersion())
                        .commonTag("mscVersion", mMSCAppModule.getPublishId())
                        .commonTag("packageName", mMSCAppModule.getPackageNameByPath(pageData.mPagePath));
            }
            pageData.appPageReporter
                    .once("msc.page.load.start").sendRealTime(); //init的时候webView里还没有contentUrl
        } catch (Exception e) {
            //ignore
        }

//        MemoryMonitor.onPageStart(createMemoryRecord(pageData.mPagePath));
    }

    protected static final String STATUS_SUCCESS = "success";
    protected static final String STATUS_CANCEL = "cancel";
    protected static final String STATUS_FAIL = "fail";

    protected void reportPageCancel() {
        reportPageEnd(STATUS_CANCEL, null);
    }

    /**
     * 如还未上报过结果，立即上报
     */
    void reportPageEnd(String msg, HashMap<String, Object> param) {
        if (pageData.pageEndRecord == null) {    // 仅在本次能作为结果时触发上报
            cachePageEnd(msg, param);
            reportCachedPageEnd();
        }
    }

    /**
     * 仅记录，不立即上报，预加载时需要等页面确定启动才能上报
     */
    protected void cachePageEnd(String msg, HashMap<String, Object> params) {
        if (pageData.pageEndRecord == null) {
            LoadEndRecord record = new LoadEndRecord();
            record.state = msg;
            record.params = params;
            pageData.pageEndRecord = record;
        }
    }

    /**
     * 如有，上报当前已记录的结果
     */
    protected void reportCachedPageEnd() {
        if (pageData.pageEndRecord != null) {
            if (pageData.isPageEndReported || !pageData.isPageStartReported) {
                return;
            }
            pageData.isPageEndReported = true;
//            pageData.appPageReporter.reportDurationFromLaunchStart(ReporterFields.REPORT_PAGE_LOAD_POINT_FIRST_RENDER,
//                    HashMapHelper.merge(HashMapHelper.of("state", pageData.pageEndRecord.state), pageData.pageEndRecord.params));
        }
    }

    protected void onFirstRender() {
        onFirstRender(null);
    }

    //TODO 确保调用
    protected void onFirstRender(HashMap<String, Object> paramsMap) {
        if (pageData.mAppPageListener != null) {
            pageData.mAppPageListener.onFirstRender();
        }

        if (pageData.hasFirstRender) return;

        pageData.hasFirstRender = true;

        pageData.mEventListener.onPageFirstRender(pageData.mPagePath, paramsMap, pageData.openType);
        FirstRenderData data = new FirstRenderData();
        data.path = pageData.mPagePath;
        mRuntime.publish(new MSCEvent<>(EVENT_PAGE_FIRST_RENDER, data));

        if (perfEventRecorder != null) {
            perfEventRecorder.endDurableEvent(PerfEventConstant.RENDER);
            pageData.appPageReporter.commonTag("sid", perfEventRecorder.getSid());
        }
        PerfTrace.end(PerfEventConstant.LAUNCH);
        pageData.appPageReporter.onPageLoadSuccess();
        reportUserPageSuccess();
        mRuntime.getPerformanceManager().onFirstRender(pageData, getViewId());
        // TODO isFirstPage可能不准
        if (pageData.appPageReporter.isFirstPage()) {
            pageData.appPageReporter.commonTag("renderCacheType", getRenderCacheType());
            MPListenerManager.getInstance().launchListener.onFirstRender(mRuntime.getAppId(), pageData.mPagePath, pageData.appPageReporter.getCommonTags());
        }
    }

    protected void reportUserPageSuccess() {
        // 在监控端计算"框架启动成功率V2"指标时 msc.user.page.load.success 点位要筛选 isFirstPage true, 上报时不限制
        Map<String, Object> tags = new HashMap<>();
        String startScene = UserReporter.CONSTANT_PORTAL;
        if (mContainerDelegate != null) {
            tags.put(CommonTags.TAG_WIDGET, mContainerDelegate.isWidget());
            IMSCContainer container = mContainerDelegate.getMSCContainer();
            if (container != null) {
                boolean fromMiniProgramApi = IntentUtil.getBooleanExtra(container.getIntent(),
                        ContainerController.START_FROM_MIN_PROGRAM, false);
                startScene = fromMiniProgramApi ? UserReporter.CONSTANT_NAVIGATE_TO_MINI_PROGRAM :
                        UserReporter.CONSTANT_PORTAL;
                tags.put(CommonTags.TAG_MSC_APP_ID, container.getMPAppId());
                tags.put(CommonTags.TAG_IS_PRE_CREATE, container.isPreCreate());
            }
        }
        String pagePath = pageData.mPagePath != null ? pageData.mPagePath : "";
        tags.put(CommonTags.TAG_PAGE_PATH, pagePath);
        tags.put(CommonTags.TAG_PURE_PAGE_PATH, PathUtil.getPath(pagePath));
        tags.put(CommonTags.TAG_IS_FIRST_PAGE, pageData.appPageReporter.isFirstPage());
        tags.put(CommonTags.TAG_IS_FIRST_PAGE_V2, pageData.appPageReporter.isFirstPageV2());
        tags.put(UserReporter.TAG_IS_LAUNCH_PAGE, pageData.appPageReporter.isLaunchPage());
        long launchStartTime = 0;
        if (pageData.containerReporter != null) {
            launchStartTime = pageData.containerReporter.getLaunchStartTimeCurrentTimeMillis();
        }
        long launchDuration = launchStartTime > 0 ? System.currentTimeMillis() - launchStartTime : -1;
        long pageStartTime = pageData.appPageReporter.getRenderPageStartTime();
        long pageDuration = pageStartTime > 0 ? System.currentTimeMillis() - pageStartTime : -1;
        UserReporter.create().reportUserPageLoadSuccess(tags, startScene, launchDuration, pageDuration);
    }

    public void setReloadViewId(int viewId) {
        this.reloadViewId = viewId;
    }

    //TODO onAppRoute移除后将改为外部生成
    public int getViewId() {
        if (viewId != View.NO_ID) {
            return viewId;
        }
        return hashCode();
    }

    //TODO 基础版onAppRoute，待去除
    public void onAppRoute(OpenParams openParams) {
        pageData.openType = openParams.openType;
        openParams.addExtraParam("routeStartTime",
                getReporter() instanceof AppPageReporter? ((AppPageReporter)getReporter()).getPageStartTime():System.currentTimeMillis());
        MSCLog.i(TAG, String.format("onAppRoute, openType=%s pagePath=%s viewId=%s reloadViewId=%s hasRenderCache=%s",
                openParams.openType, pageData.mPagePath, getViewId(), reloadViewId, !TextUtils.isEmpty(pageData.curRenderCache)));
        pageData.mEventListener.onAppRoute(openParams, getViewId(), reloadViewId, pageData.curRenderCache);
        // reload 功能仅生效一次，在onAppRoute之后发出
        if (reloadViewId != View.NO_ID && !openParams.openType.equals(OpenParams.RELOAD)) {
            pageData.mEventListener.onAppRoute(openParams.setOpenType(OpenParams.RELOAD),
                    getViewId(), reloadViewId, pageData.curRenderCache);
        }
        // 单次使用 用完丢弃
        reloadViewId = View.NO_ID;
        pageData.appPageReporter.commonTag("routeType", openParams.openType);
        pageData.onAppRouteTime = System.currentTimeMillis();
        if (mRuntime != null && mRuntime.enableReportAPIDataFix() && !(TextUtils.equals(openParams.openType, OpenParams.NAVIGATE_BACK) || TextUtils.equals(openParams.openType, OpenParams.NAVIGATE_BACK_UTIL) || TextUtils.equals(openParams.openType, OpenParams.WIDGET_DESTROY))) {
            updateContainerStage("on_app_route");
        }
    }

    public void setRenderCache(String renderCache) {
        if (!TextUtils.isEmpty(renderCache)) {
            pageData.curRenderCache = renderCache;
        }
    }

    //TODO 临时版，自动触发onAppRoute，待去除
    public void loadPage(OpenParams openParams) {
        // 这里的实现是给Native渲染使用的，WebView渲染使用了子类实现
        loadPage(openParams.url, openParams.getRouteTime());
        onAppRoute(openParams);
    }

    @Override
    @CallSuper
    public void onDestroy() {
        // 因为可能会复用，所以可能不会调用。
    }

    @Override
    public void onShow() {
        // [埋点] 上报PV
//        pageData.appPageReporter.reportPV();
        whiteScreenHappenHelper.resume();
    }

    @Override
    public void onHide() {
        whiteScreenHappenHelper.pause();
    }

    public void setPreloadType(RendererPreloadType preloadType) {
        this.preloadType = preloadType;
    }

    public RendererPreloadType getPreloadType() {
        return this.preloadType;
    }

    public void checkWhiteScreenAndReport(View detectView, boolean isVisible, boolean isInnerWebView, String innerUrl, HashMap<String, Object> renderProcessGoneInfo, boolean isStartPageAdvanced) {
        String field = isStartPageAdvanced ? ReporterFields.REPORT_MSC_PAGE_WHITE_SCREEN_COUNT_NEW : ReporterFields.REPORT_MSC_PAGE_WHITE_SCREEN_COUNT;
        if (isVisible) {
            boolean hasFirstRender = pageData.hasFirstRender;
            if (!hasFirstRender) {
                MetricsEntry metricsEntry = getReporter().record(field).tag("isStartPageAdvanced", isStartPageAdvanced);
                int isWhiteScreen = 1;
                if (MSCHornRollbackConfig.isEnableNoFirstRenderCheckWhiteScreen() && MSCConfig.isNeedCheckWhiteScreen(PathUtil.getPath(pageData.mPagePath))) {
                    isWhiteScreen = isWhiteScreen(isInnerWebView, detectView, false, isStartPageAdvanced) ? 1 : 0;
                    checkWebViewBlock();
                }
                addWhiteScreenCountTags(metricsEntry, false, isWhiteScreen, innerUrl, renderProcessGoneInfo);
            } else {
                //切换页面了就不检测了 白屏检测耗时很长
                if (MSCConfig.isNeedCheckWhiteScreen(PathUtil.getPath(pageData.mPagePath))) {
                    MSCLog.i(TAG, "White_Screen_Check_Begin", "detectView@", Integer.toHexString(detectView.hashCode()),
                            isInnerWebView, innerUrl, renderProcessGoneInfo);
                    boolean isWhiteScreen = isWhiteScreen(isInnerWebView, detectView, true, isStartPageAdvanced);
                    checkWebViewBlock();
                    MetricsEntry metricsEntry = getReporter().record(field).tag("isStartPageAdvanced", isStartPageAdvanced);
                    addWhiteScreenCountTags(metricsEntry, true, isWhiteScreen ? 1 : 0, innerUrl, renderProcessGoneInfo);
                } else {
                    WhiteScreenUtil.reportWhiteScreenCancelReason("not need check ", false, isStartPageAdvanced);
                }
            }
        } else {
            MSCLog.i(TAG, "checkWhiteScreen isVisible is false");
            WhiteScreenUtil.reportWhiteScreenCancelReason("invisible", false, isStartPageAdvanced);
        }
    }

    protected void checkWebViewBlock() {
    }

    protected String getConsoleLogErrorMessage() {
        return "";
    }

    protected List<Long> getRenderProcessGoneTimeList() {
        return null;
    }

    private void addWhiteScreenCountTags(MetricsEntry metricsEntry, boolean hasFirstRender, int isWhiteScreen, String innerUrl, HashMap<String, Object> renderProcessGoneInfo) {
        MSCLog.i(TAG, "#addWhiteScreenCountTags,return by isRollbackMscWhiteScreenAddReason = false");
        WhiteScreenReasonRecord whiteScreenReasonRecord = new WhiteScreenReasonRecord.Builder()
                .setPageLoadStatus(getReporter().getPageLoadErrorRecordInfo())
                .setJsError(getReporter().getJsErrorRecordInfo())
                .setWebViewConsoleLogError(getConsoleLogErrorMessage())
                .setRenderProcessGoneInfo(renderProcessGoneInfo, getRenderProcessGoneTimeList())
                .setPageState(pageData.pageState)
                .setServiceStage(pageData.serviceState)
                .setIsUseRenderCacheAndNotRecycle(this instanceof MSCWebViewRenderer ? ((MSCWebViewRenderer) this).isUseRenderCacheAndNotRecycle() : false)
                .build();
        String preloadStrategyStr = mRuntime.getPreloadStrategyStr();
        if (!TextUtils.isEmpty(preloadStrategyStr)) {
            metricsEntry.tag("afterT3PreloadStrategy", preloadStrategyStr);
        }
        boolean enableSetWebViewWhiteForegroundColor = MSCConfig.enableSetWebViewWhiteForegroundColor(mRuntime.getAppId());
        if (this instanceof MSCWebViewRenderer && enableSetWebViewWhiteForegroundColor) {
            metricsEntry.tag("isWhiteForegroundShow", pageData.isWhiteForegroundShow);
        }
        getReporter().appendOutLinkMetrics(metricsEntry, mContainerDelegate);
        metricsEntry
                .tag("hasFirstRender", hasFirstRender ? 1 : 0)
                .tag("isWhiteScreen", isWhiteScreen)
                .tag("openType", pageData.openType)
                .tag("lastStatusEvent", getLastStatusEvent())
                .tag("launchDuration", getLaunchDuration())
//                .tag("pageStack", mRuntime.jsErrorRecorder.getPageStack())
//                .tag("pageNavigation", mRuntime.jsErrorRecorder.getPageNavigationHistory())
//                .tag("jsErrors", mRuntime.jsErrorRecorder.getJSErrors())
                .tag("innerUrl", innerUrl)
                .tag("isRendererGoneReload", renderProcessGoneInfo != null)
                .tag("renderProcessGoneInfo", renderProcessGoneInfo)
                .tag("useOriginCaptureStrategy", useOriginCaptureStrategy())
                .tag("happenTime", whiteScreenHappenHelper.getCurrentStatus())
                .tags(whiteScreenReasonRecord.getWhiteScreenReportMap())
                .tags(renderProcessGoneInfo)
                .sendRealTime();
        getReporter().appendHornField(metricsEntry);
    }

    private double getLaunchDuration() {
        if (pageData == null || pageData.appPageReporter == null) {
            return -1;
        }
        return pageData.appPageReporter.cacheLaunchDuration;
    }

    protected String getLastStatusEvent() {
        return "";
    }

    protected boolean useRenderCache() {
        return false;
    }

    protected RenderCacheType getRenderCacheType() {
        return RenderCacheType.unknown;
    }

    protected abstract boolean useOriginCaptureStrategy();

    public abstract boolean isWhiteScreen(boolean isInnerWebView, View detectView, boolean hasFirstRender, boolean isStartPageAdvanced);


    public boolean hasFirstRender() {
        return pageData.hasFirstRender;
    }

    @NonNull
    public IWhiteScreenHappenHelper getWhiteScreenHappenHelper() {
        return whiteScreenHappenHelper;
    }


    @Override
    public void onUserTapBackToTop() {

    }

    /**
     * 重置pageData
     * 用于回收复用
     */
    public BasePageData resetData() {
        this.pageData = createPageData();
        return this.pageData;
    }

    @Override
    public View findViewById(int id) {
        View view = getRendererView().asView();
        return view == null ? null : view.findViewById(id);
    }

    @Override
    public View findViewByMarkerKey(String markerKey) {
        return null;
    }

    private OnReloadListener mReloadListener;

    public void setOnReloadListener(OnReloadListener onReloadListener) {
        this.mReloadListener = onReloadListener;
    }

    public void reload(HashMap<String, Object> map) {
        if (mReloadListener != null) {
            mReloadListener.onReload(map);
        } else {
            MSCLog.i(TAG, "OnReloadListener is null when reload page");
        }
    }

    public boolean isNativeRender() {
        return getType() == RendererType.NATIVE || getType() == RendererType.RN;
    }

    public int getRenderActions() {
        return 0;
    }

    public void onPageExit() {}

    public void tagRecord(MetricsEntry metricsEntry) {}

    public void putTags(MetricsEntry entry, PerfTraceAnalyzer ta, long createListTime, ITraceEventFilter listOperatorFilter) {}

    public void putFFPTags(MetricsEntry entry) {}

    /**
     * 通知视图层开始检测FFP
     */
    public void startFFPDetect() {
    }

    public boolean isPreCreate() {
        IContainerDelegate containerDelegate = this.mContainerDelegate;
        return containerDelegate != null && containerDelegate.getMSCContainer() != null && containerDelegate.getMSCContainer().isPreCreate();
    }

    public void onFFPReport(@NonNull FFPReportListener.IReportEvent event) {
    }

    public Map<String, Object> getFFPTags(FFPPageInfo pageInfo) {
        return Collections.emptyMap();
    }

    /**
     * TODO 临时方法，灰度上使用的 @nieyihe 需要改造下
     * */
    public String getStatisticTags(FFPReportListener.IReportEvent event) {
        return "{}";
    }

    public long getLastJsTime(FFPReportListener.IReportEvent event) {
        return -1L;
    }

    public int getCssParseSource() {
        return 0;
    }

    public void updateContainerStage(String stage) {
        if (pageData.appPageReporter == null) {
            return;
        }
        long curTimeMillis = System.currentTimeMillis();
        if (MSCHornRollbackConfig.enablePhasedPrimaryMetricsReport()) {
            // 即时上报一级阶段数据
            PerfTrace.online().instant("internal_" + stage, curTimeMillis).report();
        }
        boolean isWebViewRender = getType() == RendererType.WEBVIEW;
        String msiContainerStage = "";
        if ("page_subtree_b".equals(stage)) {
            // 业务启动阶段终点，页面渲染阶段起点
            msiContainerStage = IContainerStageProvider.C_PageRender;
            pageData.appPageReporter.onLaunchStageChange(PAGE_RENDER_START, curTimeMillis);
        } else if ("on_app_route".equals(stage)) {
            // 业务启动阶段起点
            msiContainerStage = IContainerStageProvider.C_BizLaunch;
            pageData.appPageReporter.onAppRouteLaunchStage(curTimeMillis);
            // onAppRoute发送时，传入on_app_route事件
            if (isWebViewRender) {
                pageData.hasReceiveOnAppRoute = true;
                if (pageData.hasReceiveAfterExecOnPageStart) {
                    // 容器启动阶段终点，业务启动阶段起点
                    pageData.appPageReporter.onLaunchStageChange(CONTAINER_LAUNCH_END, curTimeMillis);
                }
            } else {
                // 容器启动阶段终点，业务启动阶段起点
                pageData.appPageReporter.onLaunchStageChange(CONTAINER_LAUNCH_END, curTimeMillis);
            }
        } else if ("after_exec_on_page_start".equals(stage) && isWebViewRender) {
            pageData.hasReceiveAfterExecOnPageStart = true;
            if (pageData.hasReceiveOnAppRoute) {
                // 容器启动阶段终点，非业务启动阶段起点
                pageData.appPageReporter.onLaunchStageChange(CONTAINER_LAUNCH_END, curTimeMillis);
            }
        }
        if (!TextUtils.isEmpty(msiContainerStage)) {
            if (IContainerStageProvider.C_PageRender.equals(msiContainerStage)) {
                MSCExecutors.submit(new MSCExecutors.Serialized.SubmitRunnable(new Runnable() {
                    @Override
                    public void run() {
                        if (pageData != null) {
                            // 秒开采样场景，超时认为完成页面渲染阶段
                            pageData.msiContainerStage = "";
                        }
                    }
                }, 12 * 1000));
            }
        }
    }
}
