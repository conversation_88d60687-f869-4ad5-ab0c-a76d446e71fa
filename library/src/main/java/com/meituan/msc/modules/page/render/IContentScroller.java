package com.meituan.msc.modules.page.render;

import com.meituan.msc.modules.page.render.webview.OnContentScrollChangeListener;

/**
 * 用于控制渲染器View滚动，以处理键盘上推等情况
 * TODO 更改带web的命名
 */
public interface IContentScroller {

    /**
     * @api
     * 组件标准化注释_标准API
     * 申请Web布局
     */
    void requestContentLayout();

    /**
     * @api
     * 组件标准化注释_标准API
     * native驱动滚动渲染器，用于输入法弹起时露出内容
     * @param offset 滚动范围
     */
    void scrollContentY(int offset);

    /**
     * @api
     * 组件标准化注释_标准API
     * 获取滚动坐标
     * @return 滚动坐标
     */
    int getContentScrollY();

    /**
     *
     * @api
     * 组件标准化注释_标准API
     * 获取渲染器内容高度
     * @return 内容高度
     */
    int getContentHeight();

    /**
     * @api
     * 组件标准化注释_标准API
     * 设置滚动监听器
     * @param listener 滚动监听器
     */
    void setOnContentScrollChangeListener(OnContentScrollChangeListener listener);
}
