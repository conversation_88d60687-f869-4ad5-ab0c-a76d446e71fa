package com.meituan.msc.modules.page.render;

import android.support.annotation.IntDef;

import java.lang.annotation.Retention;
import java.lang.annotation.RetentionPolicy;

public interface ICssPreParseManager {

    int STAGE_PRELOAD = 1;
    int STAGE_LAUNCH_PAGE = 2;
    int STAGE_NONE = -1;

    @IntDef({STAGE_PRELOAD, STAGE_LAUNCH_PAGE, STAGE_NONE})
    @Retention(RetentionPolicy.SOURCE)
    public @interface Stage {}

    /**
     * 预解析CSS
     *
     * @param pagePath pagePath
     */
    void preParseCss(String pagePath);

    /**
     * 清除CSS解析结果缓存
     */
    void clearAllCssCache();

    /**
     * 是否已预解析CSS
     *
     * @return 是否已预加载
     */
    boolean isPreParsed();

    /**
     * 设置启动时已执行的预加载页面路径
     *
     * @param pagePath pagePath
     */
    void setParsedCssPagePathAtLaunch(String pagePath);

    /**
     * 与 {@code preParseCss(String)} 的区别是
     * 1. 不判断页面由实验控制
     * 2. pagePath 为空时，根据 App 配置的主路径预加载
     */
    void preCssFileV2(String pagePath, @Stage int stage);

    boolean enablePreParseCssV2();

    /**
     * 清除CSS解析结果缓存
     */
    void clearAllCssCacheV2();

    @Stage int getPreParseCssStage(String pagePath);
}
