package com.meituan.msc.modules.page.render;

import android.content.Context;
import android.view.View;

import com.meituan.msc.modules.engine.MSCRuntime;
import com.meituan.msc.modules.manager.MSCModule;
import com.meituan.msi.bean.BroadcastEvent;
import com.meituan.msi.bean.EventType;
import com.meituan.msi.lifecycle.IPageLifecycleCallback;

import java.util.HashMap;
import java.util.Set;

/**
 * Created by letty on 2021/12/17.
 **/
public interface IRenderer extends IRendererLifecycle, IPageInteraction {

    String EVENT_PAGE_FIRST_RENDER = "pageFirstRender";

    class FirstRenderData {
        public String path;
        public HashMap<String, Object> paramMap;
    }

    /**
     * RenderType
     * @return
     */
    RendererType getType();

    /**
     * 获取Renderer对应的Android View
     * @return
     */
    IRendererView getRendererView();

    /**
     * 初始化，应在创建后立即调用
     */
    void init(Context context, MSCRuntime runtime);

    /**
     * 加载页面
     */
    void loadPage(String pagePath, long routeTime);


    /**
     * 返回当前已加载的页面路径，未确定页面时返回null
     */
    String getPagePath();

    void onAttach();

    /**
     * 在渲染器所在的View结构销毁时需要调用此方法
     * 可能响应为销毁或回收
     */
    void onDetach();

    /**
     * 子模块注册
     * @return
     */
    Set<MSCModule> getMSCModules();

    boolean needCoverLayer();

    View findViewById(int id);

    View findViewByMarkerKey(String markerKey);

    void reload(HashMap<String, Object> map);

    String getRendererUA();

    void handleViewEvent(EventType eventType, String msg, BroadcastEvent source);

    IPageLifecycleCallback getPageLifecycleCallback();
}
