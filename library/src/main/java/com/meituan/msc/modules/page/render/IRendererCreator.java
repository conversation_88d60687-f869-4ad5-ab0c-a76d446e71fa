package com.meituan.msc.modules.page.render;

import com.meituan.msc.common.aov_task.task.ITask;
import com.meituan.msc.modules.apploader.LaunchTaskManager;
import com.meituan.msc.modules.engine.MSCRuntime;

import java.util.List;

/**
 * 渲染器创建器
 * Created by letty on 9/14/23.
 **/
public abstract class IRendererCreator {

    /**
     * 创建渲染器
     * @return
     */
    public abstract IRenderer createRender();

    /**
     * 渲染器类型
     * @return
     */
    public abstract RendererType getType();

    /**
     * 预热
     */
    public void prepare(){}

    /**
     * apploader对应的task
     */
    public abstract void onAppLoaderLaunchPage(LaunchTaskManager taskManager, MSCRuntime runtime, ITask<?> fetchBuzPkgTask, List<ITask<?>> dependList);

    public abstract void addRendererTasks(LaunchTaskManager taskManager, MSCRuntime runtime, ITask<?>... dependList);

    public abstract boolean isAppLoaderPreloadTaskRunning(LaunchTaskManager taskManager);

}
