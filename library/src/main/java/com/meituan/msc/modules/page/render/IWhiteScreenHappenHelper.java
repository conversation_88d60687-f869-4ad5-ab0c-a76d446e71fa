package com.meituan.msc.modules.page.render;

/**
 * 白屏出现时，当前状态搜集
 */
public interface IWhiteScreenHappenHelper {
    /**
     * 当前状态
     * @return
     */
    int getCurrentStatus();

    /**
     * ffp之前
     */
    void beforeFFP();

    /**
     * ffp之后
     */
    void afterFFP();

    /**
     * 页面onPause
     */
    void pause();

    /**
     * 页面resume
     */
    void resume();

    /**
     * 页面destroy
     */
    void exit();
}