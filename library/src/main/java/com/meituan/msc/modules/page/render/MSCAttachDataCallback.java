package com.meituan.msc.modules.page.render;

import android.support.annotation.NonNull;
import android.view.View;

import com.meituan.android.common.weaver.interfaces.Weaver;
import com.meituan.android.common.weaver.interfaces.ffp.AttachDataCallback;
import com.meituan.android.common.weaver.interfaces.ffp.FFPPageInfo;
import com.meituan.msc.modules.api.report.MetricsModule;
import com.meituan.msc.modules.engine.MSCHornRollbackConfig;
import com.meituan.msc.modules.engine.MSCRuntime;
import com.meituan.msc.modules.engine.ReportUtils;

import java.lang.ref.WeakReference;
import java.util.Collections;
import java.util.HashMap;
import java.util.Map;

public class MSCAttachDataCallback implements AttachDataCallback {
    private static final String TAG = "MSCAttachDataCallback";
    private final WeakReference<AppPageReporter> pageReporterWeakReference;
    private final WeakReference<MSCRuntime> runtimeWeakReference;
    private final WeakReference<BaseRenderer> rendererWeakReference;
    // FIXME by chendacai 使用页面跳链配对有风险，后面使用containerId做配对
    private final String pageUrl;

    public MSCAttachDataCallback(AppPageReporter pageReporter, String pageUrl, MSCRuntime runtime, BaseRenderer renderer) {
        this.pageReporterWeakReference = new WeakReference<>(pageReporter);
        this.pageUrl = pageUrl;
        this.runtimeWeakReference = new WeakReference<>(runtime);
        this.rendererWeakReference = new WeakReference<>(renderer);
    }

    public void registerListener() {
        if (MSCHornRollbackConfig.get().getConfig().enableMSCDimensionReportToFFP) {
            Weaver.getWeaver().registerListener(this, AttachDataCallback.class);
        }
    }

    public void unregisterListener() {
        if (MSCHornRollbackConfig.get().getConfig().enableMSCDimensionReportToFFP) {
            Weaver.getWeaver().unregisterListener(this, AttachDataCallback.class);
        }
    }

    @NonNull
    @Override
    public Map<String, Object> attachTags(@NonNull FFPPageInfo pageInfo) {
        Map<String, Object> tags = new HashMap<>();
        if (MSCTimedAttachDataCallback.isSamePage((pageInfo.getPageName()), this.pageUrl)) {
            MSCRuntime runtime = runtimeWeakReference.get();
            ReportUtils.addPkgSizeTagsForFFP(runtime, tags);
            AppPageReporter appPageReporter = pageReporterWeakReference.get();
            if (runtime != null && runtime.enableReportAPIDataFix()) {
                BaseRenderer renderer = rendererWeakReference.get();
                if (renderer != null) {
                    renderer.pageData.msiContainerStage = "";
                }
                MetricsModule metricsModule = runtime.getModule(MetricsModule.class);
                if (metricsModule != null && appPageReporter != null && appPageReporter.isFirstSendFFPEnd()) {
                    long startTimeStamp = pageInfo.getStartTimeMills();
                    long endTimeStamp = pageInfo.getEndTimeMills();
                    String pageId = null;
                    if (renderer != null && renderer.getViewId() != View.NO_ID && renderer.getType() == RendererType.WEBVIEW) {
                        pageId = String.valueOf(renderer.getViewId());
                    }
                    metricsModule.sendFFPEndEvent(pageUrl, startTimeStamp, endTimeStamp, pageId);
                }
            }
            if (appPageReporter != null) {
                appPageReporter.onAttachDataCallBack(pageInfo);
            }
        }
        // 根据pageInfo添加维度信息到tags中
        return tags;
    }

    @Override
    public Map<String, Object> attachBizDetails(@NonNull FFPPageInfo pageInfo) {
        return Collections.emptyMap();
    }

    @Override
    public Map<String, Object> attachContainerDetails(@NonNull FFPPageInfo pageInfo) {
        return Collections.emptyMap();
    }

    @Override
    public Map<String, Object> attachSdkDetails(@NonNull FFPPageInfo pageInfo) {
        return Collections.emptyMap();
    }
}
