package com.meituan.msc.modules.page.render;

import android.support.annotation.NonNull;

import com.meituan.android.common.weaver.interfaces.Weaver;
import com.meituan.android.common.weaver.interfaces.ffp.FFPLogEvent;
import com.meituan.android.common.weaver.interfaces.ffp.FFPLogListener;
import com.meituan.msc.modules.engine.MSCRuntime;
import com.meituan.msc.modules.reporter.MSCLog;

import java.lang.ref.WeakReference;

public class MSCFFPLogListener implements FFPLogListener {

    private final String pageUrl;

    private final WeakReference<AppPageReporter> pageReporterWeakReference;
    // 秒开采样状态
    private volatile FFPLogEvent event = null;

    public MSCFFPLogListener(AppPageReporter pageReporter, String pageUrl, MSCRuntime runtime, BaseRenderer renderer) {
        this.pageReporterWeakReference = new WeakReference<>(pageReporter);
        this.pageUrl = pageUrl;
    }

    public void registerListener() {
        Weaver.getWeaver().registerListener(this, FFPLogListener.class);
    }

    public void unregisterListener() {
        Weaver.getWeaver().unregisterListener(this, FFPLogListener.class);
    }

    @Override
    public void onLogStateDetermined(@NonNull FFPLogEvent event) {
        if (event.startEvent != null && MSCTimedAttachDataCallback.isSamePage(event.startEvent.pageName, pageUrl)) {
            this.event = event;
            AppPageReporter appPageReporter = this.pageReporterWeakReference.get();
            if (appPageReporter != null) {
                appPageReporter.onFFPSampleStatusCallBack();
            }
        }
    }

    /**
     * 是否收到了回调
     */
    public boolean isReceived() {
        return event != null;
    }

    /**
     * 是否命中采样
     *
     * @return 是否命中采样
     */
    public boolean isEnable() {
        if (event == null) {
            return false;
        }
        return event.enableType != 0;
    }

    /**
     * 获取采样类型
     *
     * @return 采样类型
     */
    public int getSampleType() {
        if (event == null) {
            return -1;
        }
        return event.enableType;
    }

    /**
     * 获取秒开起点
     *
     * @return 秒开起点
     */
    public long getStartTime() {
        if (event == null || event.startEvent == null) {
            return 0;
        }
        return event.startEvent.startTime;
    }
}
