package com.meituan.msc.modules.page.render;

import android.support.annotation.NonNull;
import android.support.annotation.Nullable;
import android.support.annotation.VisibleForTesting;
import android.text.TextUtils;
import android.util.Log;
import android.view.View;

import com.meituan.android.common.weaver.interfaces.Weaver;
import com.meituan.android.common.weaver.interfaces.ffp.FFPReportListener;
import com.meituan.msc.jse.bridge.CatalystInstanceImpl;
import com.meituan.msc.modules.api.report.MetricsModule;
import com.meituan.msc.modules.engine.MSCHornRollbackConfig;
import com.meituan.msc.modules.engine.MSCRuntime;
import com.meituan.msc.modules.engine.ReportUtils;
import com.meituan.msc.modules.page.IPageModule;
import com.meituan.msc.modules.page.UserReporter;
import com.meituan.msc.modules.reporter.CommonTags;
import com.meituan.msc.modules.reporter.MSCLog;

import java.lang.ref.WeakReference;
import java.util.HashMap;
import java.util.Map;

public class MSCFFPReportListener implements FFPReportListener {
    private static final String TAG = "MSCFFPReportListener";

    /**
     * CSS 预解析生效阶段
     */
    public static final String TAG_CSS_PRE_PARSE_STAGE = "cssPreParseStage";
    /**
     * CSS 解析来源
     */
    public static final String TAG_CSS_PARSE_SOURCE = "cssParseSource";

    private final WeakReference<AppPageReporter> pageReporterWeakReference;
    private final WeakReference<MSCRuntime> runtimeWeakReference;
    private final WeakReference<BaseRenderer> rendererWeakReference;
    // FIXME by chendacai 使用页面跳链配对有风险，后面使用containerId做配对
    private final String pageUrl;
    private Map<String, Object> commonTags;

    public MSCFFPReportListener(AppPageReporter pageReporter, String pageUrl, MSCRuntime runtime, BaseRenderer renderer) {
        this.pageReporterWeakReference = new WeakReference<>(pageReporter);
        this.pageUrl = pageUrl;
        this.runtimeWeakReference = new WeakReference<>(runtime);
        this.rendererWeakReference = new WeakReference<>(renderer);
    }

    public void registerListener() {
        Weaver.getWeaver().registerListener(this, FFPReportListener.class);
    }

    public void unregisterListener() {
        Weaver.getWeaver().unregisterListener(this, FFPReportListener.class);
    }

    @Override
    public final void onFFPReport(@NonNull IReportEvent event) {
        if (pageReporterWeakReference.get() == null) {
            return;
        }
        if (TextUtils.equals(event.pageUrl(), this.pageUrl)) {
            onMSCPageFFPReport(event);
        } else {
            // FIXME by chendacai 这块逻辑是为了绕过WebView渲染器下FFP的 pageUrl 会带一个?的问题
            // 去掉?之后再对比一下
            if (TextUtils.equals(removeQuery(event.pageUrl()), removeQuery(this.pageUrl))) {
                onMSCPageFFPReport(event);
            }
        }
    }

    public static String removeQuery(String url) {
        if (url != null) {
            int i = url.indexOf('?');
            if (i >= 0) {
                url = url.substring(0, i);
                return url;
            }
        }
        return url;
    }

    private void onMSCPageFFPReport(@NonNull IReportEvent event) {
        MSCLog.i(TAG, "onMSCPageFFPReport");
        AppPageReporter appPageReporter = pageReporterWeakReference.get();
        if (appPageReporter != null) {
            appPageReporter.onFFPReportCallBack(event);
        }
        // 将业务参数存出来，给其他指标用
        Map<String, Object> extraMap = event.extraMap();
        if (extraMap == null || extraMap.isEmpty()) {
            return;
        }

        // 上报渲染阶段耗时
        BaseRenderer renderer = rendererWeakReference.get();
        if (renderer != null) {
            renderer.onFFPReport(event);
        }

        String blinkOnlineTrace;

        final Map<String, Object> commonTags = new HashMap<>();
        // MSC 公共维度
        commonTags.put(CommonTags.TAG_MSC_APP_ID, extraMap.get(CommonTags.TAG_MSC_APP_ID));
        commonTags.put(CommonTags.TAG_MSC_APP_VERSION, extraMap.get(CommonTags.TAG_MSC_APP_VERSION));
        commonTags.put(CommonTags.TAG_BASE_PKG_VERSION, extraMap.get(CommonTags.TAG_BASE_PKG_VERSION));
        commonTags.put(CommonTags.TAG_PUBLISH_ID, extraMap.get(CommonTags.TAG_PUBLISH_ID));
        commonTags.put(CommonTags.TAG_PAGE_PATH, extraMap.get(CommonTags.TAG_PAGE_PATH));
        commonTags.put(CommonTags.TAG_RUNTIME_SOURCE, extraMap.get(CommonTags.TAG_RUNTIME_SOURCE));
        commonTags.put(CommonTags.TAG_IS_FIRST_PAGE, extraMap.get(CommonTags.TAG_IS_FIRST_PAGE));
        commonTags.put(CommonTags.TAG_IS_FIRST_PAGE_V2, extraMap.get(CommonTags.TAG_IS_FIRST_PAGE_V2));
        commonTags.put(CommonTags.TAG_IS_PRE_CREATE, extraMap.get(CommonTags.TAG_IS_PRE_CREATE));
        commonTags.put(CommonTags.TAG_RENDER_TYPE, extraMap.get(CommonTags.TAG_RENDER_TYPE));
        commonTags.put(CommonTags.TAG_NATIVE_DOM_ENABLED, extraMap.get(CommonTags.TAG_NATIVE_DOM_ENABLED));
        if (renderer != null) {
            blinkOnlineTrace = renderer.getStatisticTags(event);
            commonTags.put(CommonTags.TAG_BLINK_ONLINE_TRACE, blinkOnlineTrace);
        } else {
            blinkOnlineTrace = "";
        }
        for (Map.Entry<String, Object> entry : extraMap.entrySet()) {
            // FIXME by chendacai 临时方案，将闪购的业务参数临时存储起来放到其他指标中去
            if (entry.getKey().startsWith("sg_")) {
                commonTags.put(entry.getKey(), entry.getValue());
            }
        }
        this.commonTags = commonTags;
        // 给前端发通知FFP
        MSCRuntime runtime = runtimeWeakReference.get();
        AppPageReporter reporter = pageReporterWeakReference.get();
        if (runtime != null && !runtime.enableReportAPIDataFix()) {
            String pageId = null;
            BaseRenderer baseRenderer = rendererWeakReference.get();
            if (baseRenderer != null) {
                // 秒开结束时间，容器状态设置为空，待和sendFFPEndEvent移至onFFPRenderEnd
                baseRenderer.pageData.msiContainerStage = "";
                if (baseRenderer.getViewId() != View.NO_ID && baseRenderer.getType() == RendererType.WEBVIEW) {
                    pageId = String.valueOf(baseRenderer.getViewId());
                }
            }

            MetricsModule metricsModule = runtime.getModule(MetricsModule.class);
            if (metricsModule != null && reporter != null && reporter.isFirstSendFFPEnd()) {
                long startTimeStamp = event.startTimeInMs();
                long endTimeStamp = event.endTimeInMs();
                metricsModule.sendFFPEndEvent(pageUrl, startTimeStamp, endTimeStamp, pageId);
            }
        }
        if (reporter != null) {
            commonTags.put("pageRecreateType", reporter.getRecreateType());
        }

        // 优先取秒开SDK统计的时长
        long pageDuration = event.ffpInMs();
        if (pageDuration <= 0) {
            long pageStartTime = 0;
            AppPageReporter pageReporter = pageReporterWeakReference.get();
            if (pageReporter != null) {
                pageStartTime = pageReporter.getRenderPageStartTime();
            }
            pageDuration = pageStartTime > 0 ? System.currentTimeMillis() - pageStartTime : -1;
        }
        commonTags.put(UserReporter.TAG_PAGE_DURATION, pageDuration);
        commonTags.put(CommonTags.TAG_WIDGET, extraMap.get(CommonTags.TAG_WIDGET));
        // 秒开检测结果有三种类型，success interact timeout
        commonTags.put(UserReporter.TAG_F_TYPE, extraMap.get(UserReporter.TAG_F_TYPE));
        commonTags.put(UserReporter.TAG_FFP_RATE, extraMap.get(UserReporter.TAG_SR));
        commonTags.put(CommonTags.TAG_PURE_PAGE_PATH, extraMap.get(CommonTags.TAG_PURE_PAGE_PATH));

        int cssParseSource;
        commonTags.put(TAG_CSS_PARSE_SOURCE, cssParseSource = (renderer == null ? -1 : renderer.getCssParseSource()));

        int cssParseStage;
        ICssPreParseManager cssPreParseManager;
        Object purePath = extraMap.get(CommonTags.TAG_PURE_PAGE_PATH);
        if (purePath instanceof String && runtime != null && (cssPreParseManager = runtime.getCssPreParseManagerWithoutDelegate()) != null) {
            commonTags.put(TAG_CSS_PRE_PARSE_STAGE, cssParseStage =  cssPreParseManager.getPreParseCssStage((String) purePath));
            MSCLog.d(TAG, "FFP#css --> cssParserSource=", cssParseSource, ", cssParseStage=", cssParseStage, ", purePath=", purePath);
        }
        commonTags.put(CommonTags.TAG_FFP_START, event.startTimeInMs());


        Runnable reportRunnable = new Runnable() {
            @Override
            public void run() {
                UserReporter.create().reportUserPageFFP(commonTags);

                // 上报FFP分阶段耗时
                if (appPageReporter != null) {
                    Map<String, Object> tags = new HashMap<>(extraMap);
                    tags.put(CommonTags.TAG_BLINK_ONLINE_TRACE, blinkOnlineTrace);
                    tags.put(CommonTags.TAG_JS_DESC, commonTags.get(CommonTags.TAG_JS_DESC));
                    tags.put(CommonTags.TAG_LAST_JS_TIME, commonTags.get(CommonTags.TAG_LAST_JS_TIME));
                    tags.put(CommonTags.TAG_LAST_JS_DESC, commonTags.get(CommonTags.TAG_LAST_JS_DESC));
                    tags.put(CommonTags.TAG_FFP_START, event.startTimeInMs());

                    ReportUtils.addPkgSizeTagsForFFP(runtime, tags);
                    if (event.details() != null) {
                        // 秒开建议直接上报字符串，避免他们后续修改内容导致崩溃
                        tags.put("ffpDetails", event.details());
                    }
                    if (reporter != null) {
                        tags.put("pageRecreateType", reporter.getRecreateType());
                    }

                    appPageReporter.reportFFPStages(event.startTimeInMs(), event.endTimeInMs(), tags);
                }

                trickRenderFFP(runtime);
            }
        };

        if (MSCHornRollbackConfig.enableJSEngineReport() && runtime != null && runtime.mCatalystInstance instanceof CatalystInstanceImpl && renderer != null) {
            CatalystInstanceImpl jsImpl = (CatalystInstanceImpl) runtime.mCatalystInstance;
            jsImpl.runOnJs(new Runnable() {
                @Override
                public void run() {
                    String ffpJsDesc = jsImpl.getSpendTime(event.startTimeInMs(), event.endTimeInMs()); //FFP
                    long ffpLastJsTime = renderer.getLastJsTime(event);
                    String ffpLastJsDesc = "";
                    if (ffpLastJsTime > 0) {
                        ffpLastJsDesc = jsImpl.getSpendTime(event.startTimeInMs(), ffpLastJsTime);
                    }

                    Log.d(TAG, "onMSCPageFFPReport() called with, ffpJSDesc:" + ffpJsDesc + " " + event.startTimeInMs() + "~" + event.endTimeInMs());
                    Log.d(TAG, "onMSCPageFFPReport() called with, lastJsTime:" + ffpLastJsTime + ", lastJsDesc:" + ffpLastJsDesc);

                    commonTags.put(CommonTags.TAG_JS_DESC, ffpJsDesc);
                    commonTags.put(CommonTags.TAG_LAST_JS_DESC, ffpLastJsDesc);
                    commonTags.put(CommonTags.TAG_LAST_JS_TIME, ffpLastJsTime);

                    jsImpl.stopAndClearTracer();
                }
            }, reportRunnable);
        } else {
            reportRunnable.run();
        }
    }

    // 给base render设置ffp成功事件
    @VisibleForTesting
    public void trickRenderFFP(@Nullable MSCRuntime runtime) {
        if (null == runtime) {
            return;
        }
        IPageModule pageModule = runtime.getTopPageModule();
        if (null == pageModule) {
            return;
        }
        BaseRenderer renderer = pageModule.getRenderer();
        if (null == renderer) {
            return;
        }
        renderer.getWhiteScreenHappenHelper().afterFFP();
    }

    public Map<String, Object> getCommonTags() {
        return commonTags;
    }
}
