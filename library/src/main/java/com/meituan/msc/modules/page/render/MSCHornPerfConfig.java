package com.meituan.msc.modules.page.render;

import android.support.annotation.Keep;
import android.text.TextUtils;

import com.meituan.msc.common.utils.MSCHornUtils;
import com.meituan.msc.lib.interfaces.BaseRemoteConfig;
import com.meituan.msc.modules.engine.MSCHornRollbackConfig;

import java.util.ArrayList;
import java.util.Collections;
import java.util.HashMap;
import java.util.Map;
import java.util.Set;

public class MSCHornPerfConfig extends BaseRemoteConfig<MSCHornPerfConfig.Config> {

    private static volatile MSCHornPerfConfig sInstance;

    public static MSCHornPerfConfig getInstance() {
        if (sInstance == null) {
            synchronized (MSCHornPerfConfig.class) {
                if (sInstance == null) {
                    sInstance = new MSCHornPerfConfig();
                }
            }
        }
        return sInstance;
    }

    private MSCHornPerfConfig() {
        super("msc_perf_android_group", Config.class);
    }

    @Override
    protected void onRemoteConfigChanged(String rawConfigString) {
        super.onRemoteConfigChanged(rawConfigString);
        if (TextUtils.isEmpty(rawConfigString)) {
            return;
        }
        Config tempConfig = parseRemoteConfig(rawConfigString);
        if (tempConfig != null && sInstance != null) {
            sInstance.config.instrumentPrefetchAppIds = tempConfig.instrumentPrefetchAppIds;
        }
    }

    public boolean disableAbnormalFPDetailReport() {
        return config.disableAbnormalFPDetailReport;
    }

    public int abnormalFPThreshold() {
        return config.abnormalFPThreshold;
    }

    public boolean useMSCExecutors() {
        return config.useMSCExecutors;
    }

    public boolean useInstrumentPrefetch(String appId) {
        return appId != null && MSCHornUtils.isMatchWhiteConfigRule(config.instrumentPrefetchAppIds, appId);
    }

    public boolean needBuildScriptTrace(String module, String method) {
        Set<String> methods = config.buildScriptTraceModuleMethod.get(module);
        return methods != null && methods.contains(method);
    }

    public boolean enableFPUsePageStartTime() {
        return config.enableFPUsePageStartTime;
    }

    public boolean enableFPMatchFix() {
        return config.enableFPMatchFix;
    }

    @Keep
    public static class Config {

        boolean disableAbnormalFPDetailReport = false;

        int abnormalFPThreshold = 5000;

        boolean useMSCExecutors = false;

        public ArrayList<String> instrumentPrefetchAppIds = new ArrayList<>();

        Map<String, Set<String>> buildScriptTraceModuleMethod = new HashMap<>();
        boolean enableFPMatchFix = true;

        boolean enableFPUsePageStartTime = true;

        public Config() {
            instrumentPrefetchAppIds.add("7122f6e193de47c1");
            buildScriptTraceModuleMethod.put("WebViewPageData", Collections.singleton("onDataChange"));
        }
    }
}
