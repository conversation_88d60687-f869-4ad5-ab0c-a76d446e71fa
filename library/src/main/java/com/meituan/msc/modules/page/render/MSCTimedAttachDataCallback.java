package com.meituan.msc.modules.page.render;

import android.text.TextUtils;
import android.view.View;

import com.meituan.android.common.weaver.interfaces.Weaver;
import com.meituan.android.common.weaver.interfaces.ffp.FFPPageInfo;
import com.meituan.android.common.weaver.interfaces.ffp.TimedAttachDataCallback;
import com.meituan.android.common.weaver.interfaces.ffp.TimedAttachDataListener;
import com.meituan.msc.modules.api.report.MetricsModule;
import com.meituan.msc.modules.engine.MSCHornRollbackConfig;
import com.meituan.msc.modules.engine.MSCRuntime;

import java.lang.ref.WeakReference;

public class MSCTimedAttachDataCallback implements TimedAttachDataCallback {
    private static final String TAG = "MSCTimedAttachDataCallback";
    private final WeakReference<AppPageReporter> pageReporterWeakReference;
    private final WeakReference<MSCRuntime> runtimeWeakReference;
    private final WeakReference<BaseRenderer> rendererWeakReference;
    // FIXME by chendacai 使用页面跳链配对有风险，后面使用containerId做配对
    private final String pageUrl;

    public MSCTimedAttachDataCallback(AppPageReporter pageReporter, String pageUrl, MSCRuntime runtime, BaseRenderer renderer) {
        this.pageReporterWeakReference = new WeakReference<>(pageReporter);
        this.pageUrl = pageUrl;
        this.runtimeWeakReference = new WeakReference<>(runtime);
        this.rendererWeakReference = new WeakReference<>(renderer);
    }

    public void registerListener() {
        if (MSCHornRollbackConfig.get().getConfig().enableMSCDimensionReportToFFP) {
            Weaver.getWeaver().registerListener(this, TimedAttachDataCallback.class);
        }
    }

    public void unregisterListener() {
        if (MSCHornRollbackConfig.get().getConfig().enableMSCDimensionReportToFFP) {
            Weaver.getWeaver().unregisterListener(this, TimedAttachDataCallback.class);
        }
    }

    @Override
    public void timedAttachData(FFPPageInfo pageInfo, TimedAttachDataListener timedListener) {
        if (pageInfo == null || timedListener == null || !isSamePage((pageInfo.getPageName()), this.pageUrl)) {
            return;
        }
        if (pageReporterWeakReference.get() == null) {
            return;
        }
        pageReporterWeakReference.get().onTimedAttachDataCallBack(pageInfo);
        // 把获取到的tags信息回传给秒开
        reportTagsToFFP(pageInfo, timedListener);
    }

    // pageName格式：mscAppId + "/" + pageUrl
    public static boolean isSamePage(String pageName, String pageUrl) {
        if (pageName != null) {
            int i = pageName.indexOf("/");
            if (i >= 0) {
                // 去掉"mscAppId/"字段
                pageName = pageName.substring(i+1);
                if (pageUrl.equals(pageName)) {
                    return true;
                } else {
                    // FIXME by chendacai 这块逻辑是为了绕过WebView渲染器下FFP的 pageUrl 会带一个?的问题
                    // 去掉?之后再对比一下
                    return TextUtils.equals(MSCFFPReportListener.removeQuery(pageName), MSCFFPReportListener.removeQuery(pageUrl));
                }
            }
        }
        return false;
    }

    private void reportTagsToFFP(FFPPageInfo pageInfo, TimedAttachDataListener timedListener) {
        // 给前端发通知FFP
        MSCRuntime runtime = runtimeWeakReference.get();
        AppPageReporter appPageReporter = pageReporterWeakReference.get();
        if (runtime != null && !runtime.enableReportAPIDataFix()) {
            BaseRenderer renderer = rendererWeakReference.get();
            if (renderer != null) {
                renderer.pageData.msiContainerStage = "";
            }
            MetricsModule metricsModule = runtime.getModule(MetricsModule.class);
            if (metricsModule != null && appPageReporter != null && appPageReporter.isFirstSendFFPEnd()) {
                long startTimeStamp = pageInfo.getStartTimeMills();
                long endTimeStamp = pageInfo.getEndTimeMills();
                String pageId = null;
                if (renderer != null && renderer.getViewId() != View.NO_ID && renderer.getType() == RendererType.WEBVIEW) {
                    pageId = String.valueOf(renderer.getViewId());
                }
                metricsModule.sendFFPEndEvent(pageUrl, startTimeStamp, endTimeStamp, pageId);
            }
        }

        if (appPageReporter != null) {
            appPageReporter.reportFFPStagesToFFP(pageInfo, timedListener);
        }
    }
}
