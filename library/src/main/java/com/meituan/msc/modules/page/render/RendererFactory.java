package com.meituan.msc.modules.page.render;

import android.content.Context;

import com.meituan.msc.modules.engine.MSCRuntime;
import com.meituan.msc.modules.manager.MSCRuntimeException;
import com.meituan.msc.modules.page.render.webview.MSCWebViewRenderer;
import com.sankuai.meituan.serviceloader.ServiceLoader;

import java.util.HashMap;
import java.util.List;
import java.util.Map;

/**
 * Created by letty on 2021/12/22.
 **/
public class RendererFactory {

    private static final String TAG = "RendererFactory";
    private static volatile Map<RendererType, IRendererCreator> rendererCreatorMap;

    @SuppressWarnings("unchecked")
    public static <T extends IRenderer> T createRender(RendererType rendererType, Context context, MSCRuntime runtime) {
        T result = null;
        if (rendererType == RendererType.WEBVIEW) {
            result = (T) new MSCWebViewRenderer();
        } else {
            result = (T) getRenderer(rendererType);
        }
        if (result == null) {
            // 经华哥确认，遇到无法识别渲染类型可以直接降级到webView渲染；避免客户端直接抛异常，但是目前看起来不可用 需后续排查；此处直接抛异常方便定位问题
            throw new MSCRuntimeException("not support rendererType" + rendererType.toString());
        }
        if (runtime != null) {
            result.init(context, runtime);
        }
        return result;
    }


    private static IRenderer getRenderer(RendererType rendererType) {
        IRendererCreator rendererCreator = getRendererCreator(rendererType);
        if (rendererCreator != null) {
            return rendererCreator.createRender();
        }
        return null;
    }

    private static IRendererCreator getRendererCreator(RendererType rendererType) {
        return getRendererCreatorMap().get(rendererType);
    }

    private static Map<RendererType, IRendererCreator> getRendererCreatorMap() {
        if (rendererCreatorMap == null) {
            synchronized (RendererFactory.class) {
                if (rendererCreatorMap == null) {
                    rendererCreatorMap = new HashMap<>();
                    List<IRendererCreator> iRendererCreators = ServiceLoader.load(IRendererCreator.class, null);
                    for (IRendererCreator rendererCreator : iRendererCreators) {
                        rendererCreatorMap.put(rendererCreator.getType(), rendererCreator);
                    }
                }
            }
        }
        return rendererCreatorMap;
    }
}
