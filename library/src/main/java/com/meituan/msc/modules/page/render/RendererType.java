package com.meituan.msc.modules.page.render;

/**
 * description
 *
 * <AUTHOR>
 * @Date 3/15/22
 */
public enum RendererType {
    WEBVIEW("mp-webview"),
    RN("react-native"),
    NATIVE("mp-native"),
    FLUENT("Fluent");

    String typeName;

    RendererType(String name) {
        this.typeName = name;
    }

    public static RendererType fromString(String name) {
        for (RendererType type : RendererType.values()) {
            if (type.typeName.equalsIgnoreCase(name)) {
                return type;
            }
        }
        return null;
    }

    @Override
    public String toString() {
        return typeName;
    }
}