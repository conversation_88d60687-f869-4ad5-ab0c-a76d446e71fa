package com.meituan.msc.modules.page.render;

import android.support.annotation.NonNull;
import android.support.annotation.Nullable;

import com.meituan.msc.modules.engine.ResultCallback;
import com.meituan.msc.modules.page.render.webview.OnEngineInitFailedListener;
import com.meituan.msc.modules.update.bean.PackageInfoWrapper;

import java.util.List;
import java.util.Set;

/**
 * 可复用、可预加载的渲染器
 * 本层级仅用于声明接口
 */
public abstract class ReusableRenderer extends BaseRenderer {
    protected boolean recycling;  // 待复用但还未完全进入新页面的状态，此时收到的前端消息均可认为是上一个页面发送的
    protected boolean hasRecycle;
    protected int recycleCount = 0;
    // webview那边是否已经执行完recycle。默认情况是不需要执行，所以是true。
    private volatile boolean isWebViewRecycleCmdFinished = true;

    public boolean isRecycling() {
        return recycling;
    }

    public boolean isWebViewRecycleCmdFinished() {
        return isWebViewRecycleCmdFinished;
    }

    /**
     * 收到前端recycle完成事件
     * @param isWebViewRecycleCmdFinished
     */
    public void setWebViewRecycleCmdFinished(boolean isWebViewRecycleCmdFinished) {
        this.isWebViewRecycleCmdFinished = isWebViewRecycleCmdFinished;
    }

    /**
     * 预加载页面
     * 预加载后此渲染器显示的页面即固定，在不回收的情况下不可再加载其他页面
     * 可不实现
     */
    public abstract void preloadPage(String pagePath, boolean isLaunch);

    /**
     * 是否已加载具体页面，将导致无法被用于转而加载其他页面
     */
    public abstract boolean isPathLoaded();

    /**
     * 预加载资源
     * 可预加载对应多个页面的资源，且预加载资源不会导致渲染器固定用于特定页面
     * 可不实现
     */
    public abstract void preloadResource(@Nullable List<String> paths);


    /**
     * 查询加载过的页面
     * @return
     */
    @NonNull
    public abstract Set<String> getLoadedPaths();

    /**
     * 查询预加载过资源的页面
     */
    @NonNull
    public abstract Set<String> getResourcePaths();

    public abstract void loadWebViewBasePackage(@Nullable final ResultCallback resultCallback);

    public abstract void loadBasicPackages(@Nullable final ResultCallback resultCallback, boolean isLaunch);

    public abstract ReusableRenderer setOnEngineInitFailedListener(OnEngineInitFailedListener onEngineInitFailedListener);

    public abstract boolean isUsedSnapshotTemplate();

    /**
     * 回收渲染器，准备复用（如支持）
     * @return 是否回收成功，如不支持回收/当前状态不可回收，则返回false
     */
    public abstract boolean recycle();
}
