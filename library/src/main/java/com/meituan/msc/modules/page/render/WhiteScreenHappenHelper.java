package com.meituan.msc.modules.page.render;

/**
 * 帮助监控白屏时，happenTime字段获取
 */
class WhiteScreenHappenHelper implements IWhiteScreenHappenHelper {
    /**
     * 默认
     */
    private static final int UNKNOWN = 0;
    /**
     * init
     */
    private static final int BEFORE_FFP = 1;
    /**
     * ffp
     */
    private static final int AFTER_FFP = 2;
    /**
     * hide
     */
    private static final int SUSPEND = 3;
    /**
     * resume
     */
    private static final int RESUME = 4;
    /**
     * exit
     */
    private static final int EXIT = 5;

    private int mCurrentStatus = UNKNOWN;

    WhiteScreenHappenHelper() {
    }

    public int getCurrentStatus() {
        return mCurrentStatus;
    }

    /**
     * 同ios，监听CIPFSPReportResultDict事件
     */
    @Override
    public void beforeFFP() {
        mCurrentStatus = BEFORE_FFP;
    }

    /**
     * 在ffp回调回来时触发
     */
    @Override
    public void afterFFP() {
        mCurrentStatus = AFTER_FFP;
    }

    /**
     * onhide触发
     */
    @Override
    public void pause() {
        mCurrentStatus = SUSPEND;
    }

    /**
     * onshow触发
     */
    @Override
    public void resume() {
        mCurrentStatus = RESUME;
    }

    /**
     * destroy
     */
    @Override
    public void exit() {
        mCurrentStatus = EXIT;
    }


}
