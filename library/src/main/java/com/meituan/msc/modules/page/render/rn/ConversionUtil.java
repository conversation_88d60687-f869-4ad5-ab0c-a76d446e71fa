package com.meituan.msc.modules.page.render.rn;

import com.meituan.msc.jse.bridge.Arguments;
import com.meituan.msc.jse.bridge.ReadableArray;
import com.meituan.msc.jse.bridge.ReadableMap;
import com.meituan.msc.jse.bridge.ReadableMapKeySetIterator;
import com.meituan.msc.jse.bridge.ReadableType;
import com.meituan.msc.jse.bridge.WritableArray;
import com.meituan.msc.jse.bridge.WritableMap;

import org.json.JSONArray;
import org.json.JSONException;
import org.json.JSONObject;

import java.util.ArrayList;
import java.util.HashMap;
import java.util.Iterator;
import java.util.List;
import java.util.Map;

/**
 * Created by letty on 2021/12/22.
 **/
public class ConversionUtil {

    public static List<Object> toListTemp(@javax.annotation.Nullable ReadableArray readableArray) {
        if (readableArray == null) {
            return null;
        } else {
            List<Object> result = new ArrayList(readableArray.size());

            for(int index = 0; index < readableArray.size(); ++index) {
                ReadableType readableType = readableArray.getType(index);
                switch(readableType) {
                    case Null:
                        ((List)result).add((Object)null);
                        break;
                    case Boolean:
                        ((List)result).add(readableArray.getBoolean(index));
                        break;
                    case Number:
                        double tmp = readableArray.getDouble(index);
                        if (tmp == (double)((int)tmp)) {
                            ((List)result).add((int)tmp);
                        } else {
                            ((List)result).add(tmp);
                        }
                        break;
                    case String:
                        ((List)result).add(readableArray.getString(index));
                        break;
                    case Map:
                        ((List)result).add(toMapTemp(readableArray.getMap(index)));
                        break;
                    case Array:
                        ((List)result).add(toListTemp(readableArray.getArray(index)));
                        break;
                    default:
                        throw new IllegalArgumentException("Could not convert object with index: " + index + ".");
                }
            }

            return (List)result;
        }
    }


    public static Object[] toArrayTemp(@javax.annotation.Nullable ReadableArray readableArray) {
            List<Object> list = toListTemp(readableArray);
            return list.toArray();
    }

    public static Object toObjectTemp(@javax.annotation.Nullable ReadableMap readableMap, String key) {
        if (readableMap == null) {
            return null;
        } else {
            ReadableType readableType = readableMap.getType(key);
            Object result;
            switch(readableType) {
                case Null:
                    result = null;
                    break;
                case Boolean:
                    result = readableMap.getBoolean(key);
                    break;
                case Number:
                    try {
                        double tmp = readableMap.getDouble(key);
                        if (tmp == (double)((int)tmp)) {
                            result = (int)tmp;
                        } else if (tmp == (double)((long)tmp)) {
                            result = (long)tmp;
                        } else {
                            result = tmp;
                        }
                    } catch (Exception var6) {
                        result = readableMap.getInt(key);
                    }
                    break;
                case String:
                    result = readableMap.getString(key);
                    break;
                case Map:
                    result = toMapTemp(readableMap.getMap(key));
                    break;
                case Array:
                    result = toListTemp(readableMap.getArray(key));
                    break;
                default:
                    throw new IllegalArgumentException("Could not convert object with key: " + key + ".");
            }

            return result;
        }
    }

    public static Map<String, Object> toMapTemp(@javax.annotation.Nullable ReadableMap readableMap) {
        if (readableMap == null) {
            return null;
        } else {
            ReadableMapKeySetIterator iterator = readableMap.keySetIterator();
            if (!iterator.hasNextKey()) {
                return new HashMap();
            } else {
                HashMap result = new HashMap();

                while(iterator.hasNextKey()) {
                    String key = iterator.nextKey();
                    result.put(key, toObjectTemp(readableMap, key));
                }

                return result;
            }
        }
    }

    /**
     * Json to react writable map.
     *
     * @param jsonObject the json object
     * @return writable map
     * @throws JSONException the json exception
     */
    public static WritableMap jsonToReact(JSONObject jsonObject) throws JSONException {
        if (jsonObject == null) {
            return null;
        }
        WritableMap writableMap = Arguments.createMap();
        Iterator iterator = jsonObject.keys();
        while (iterator.hasNext()) {
            String key = (String) iterator.next();
            Object value = jsonObject.get(key);
            if (value instanceof Number) {
                if (value instanceof Integer) {
                    writableMap.putInt(key, (Integer) value);
                } else {
                    writableMap.putDouble(key, ((Number) value).doubleValue());
                }
            } else if (value instanceof String) {
                writableMap.putString(key, jsonObject.getString(key));
            } else if (value instanceof JSONObject) {
                writableMap.putMap(key, jsonToReact(jsonObject.getJSONObject(key)));
            } else if (value instanceof JSONArray) {
                writableMap.putArray(key, jsonToReact(jsonObject.getJSONArray(key)));
            } else if (value instanceof Boolean) {
                writableMap.putBoolean(key, jsonObject.getBoolean(key));
            } else if (value == JSONObject.NULL) {
                writableMap.putNull(key);
            }
        }

        return writableMap;
    }

    /**
     * Json to react writable array.
     *
     * @param jsonArray the json array
     * @return writable array
     * @throws JSONException the json exception
     */
    public static WritableArray jsonToReact(JSONArray jsonArray) throws JSONException {
        if (jsonArray == null) {
            return null;
        }
        WritableArray writableArray = Arguments.createArray();
        for (int i = 0; i < jsonArray.length(); i++) {
            Object value = jsonArray.get(i);
            if (value instanceof Number) {
                if (value instanceof Integer) {
                    writableArray.pushInt((Integer) value);
                } else {
                    writableArray.pushDouble(((Number) value).doubleValue());
                }
            } else if (value instanceof String) {
                writableArray.pushString(jsonArray.getString(i));
            } else if (value instanceof JSONObject) {
                writableArray.pushMap(jsonToReact(jsonArray.getJSONObject(i)));
            } else if (value instanceof JSONArray) {
                writableArray.pushArray(jsonToReact(jsonArray.getJSONArray(i)));
            } else if (value instanceof Boolean) {
                writableArray.pushBoolean(jsonArray.getBoolean(i));
            } else if (value == JSONObject.NULL) {
                writableArray.pushNull();
            }
        }
        return writableArray;
    }
}
