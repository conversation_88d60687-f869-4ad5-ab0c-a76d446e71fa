package com.meituan.msc.modules.page.render.rn;

import android.support.annotation.Nullable;

import com.meituan.msc.jse.bridge.ReadableArray;
import com.meituan.msc.jse.bridge.ReadableMap;

/**
 * Created by letty on 2021/12/22.
 **/
public interface IUICommand {
    void createView(int tag, String className, int rootViewTag, ReadableMap props);

    void updateView(final int tag, String className, final ReadableMap props);

    void manageChildren(int viewTag, @Nullable ReadableArray moveFrom, @Nullable ReadableArray moveTo, @Nullable ReadableArray addChildTags, @Nullable ReadableArray addAtIndices, @Nullable ReadableArray removeFrom);

    void setChildren(int viewTag, ReadableArray childrenTags);

    void measure(int reactTag, Callback callback);

    void measureInWindow(int reactTag, Callback callback);

    void measureLayout(int tag, int ancestorTag, Callback errorCallback, Callback successCallback);

    void findSubviewIn(int reactTag, ReadableArray point, Callback callback);

}
