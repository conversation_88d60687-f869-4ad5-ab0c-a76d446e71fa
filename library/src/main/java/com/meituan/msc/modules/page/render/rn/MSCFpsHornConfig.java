package com.meituan.msc.modules.page.render.rn;

import android.support.annotation.Keep;
import android.text.TextUtils;

import com.google.gson.annotations.SerializedName;
import com.meituan.msc.lib.interfaces.BaseRemoteConfig;
import com.meituan.msc.modules.engine.MSCHornRollbackConfig;


public class MSCFpsHornConfig extends BaseRemoteConfig<MSCFpsHornConfig.Config> {
    public static final String HORN_CONFIG_FILE_KEY = "msc_fps_android_group";

    private MSCFpsHornConfig(String hornConfigFileKey, Class<? extends Config> configClass) {
        super(hornConfigFileKey, configClass);
    }

    private static MSCFpsHornConfig sInstance;

    public static MSCFpsHornConfig get() {
        if (sInstance == null) {
            synchronized (MSCFpsHornConfig.class) {
                if (sInstance == null) {
                    sInstance = new MSCFpsHornConfig(HORN_CONFIG_FILE_KEY, Config.class);
                }
            }
        }
        return sInstance;
    }

    @Override
    protected void onRemoteConfigChanged(String rawConfigString) {
        super.onRemoteConfigChanged(rawConfigString);
        if (TextUtils.isEmpty(rawConfigString)) {
            return;
        }
        Config tempConfig = parseRemoteConfig(rawConfigString);
        if (tempConfig != null && sInstance != null) {
            sInstance.config.enableFPSMonitor = tempConfig.enableFPSMonitor;
        }
    }

    @Keep
    public static class Config {

        @SerializedName("enableFPSMonitor")
        boolean enableFPSMonitor = false;

        @SerializedName("enableScrollVelocityReport")
        boolean enableScrollVelocityReport;

        @SerializedName("lagConfig")
        LagConfig lagConfig = new LagConfig();

        @Override
        public String toString() {
            return "Config{" +
                    "enableFPSMonitor=" + enableFPSMonitor +
                    ", enableScrollVelocityReport=" + enableScrollVelocityReport +
                    '}';
        }
    }

    @Keep
    public static class LagConfig {

        int maxReportCount = 6;

        //单位：ms
        long threshold = 5000;

        //单位：ms
        long sampleDelay = 128;

        int maxLinePerStackEntry = 10;

        int maxStackEntryCount = 10;

        boolean enablePrinter = true;
    }

    public boolean isEnableFPSMonitor() {
        return config.enableFPSMonitor;
    }

    public long getLagThresholdMillis() {
        return config.lagConfig.threshold;
    }

    public long getLagThresholdNanos() {
        return config.lagConfig.threshold * 1000000;
    }

    public double getLagMaxReportCount() {
        return config.lagConfig.maxReportCount;
    }

    public long getStackTraceSampleDelay() {
        return config.lagConfig.sampleDelay;
    }

    public boolean getEnableScrollVelocityReport() {
        return config.enableScrollVelocityReport;
    }

    public boolean getLagEnablePrinter() {
        return config.lagConfig.enablePrinter;
    }

    public int getLagMaxStackEntryCount() {
        return config.lagConfig.maxStackEntryCount;
    }

    public int getLagMaxLinePerStackEntry() {
        return config.lagConfig.maxLinePerStackEntry;
    }
}
