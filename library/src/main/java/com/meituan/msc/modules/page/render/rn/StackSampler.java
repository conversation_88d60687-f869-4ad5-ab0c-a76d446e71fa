package com.meituan.msc.modules.page.render.rn;

import com.sankuai.android.jarvis.Jarvis;

import java.util.ArrayList;
import java.util.LinkedHashMap;
import java.util.concurrent.ScheduledExecutorService;
import java.util.concurrent.ScheduledFuture;
import java.util.concurrent.TimeUnit;

import static com.meituan.msc.modules.page.render.rn.lag.StackTraceUtil.SEPARATOR;

/**
 * Dumps thread stack.
 */
public class StackSampler {

    private static final LinkedHashMap<Long, String> sStackMap = new LinkedHashMap<>();

    private final int mMaxEntryCount = MSCFpsHornConfig.get().getLagMaxStackEntryCount();
    private final int mMaxLinePerEntry = MSCFpsHornConfig.get().getLagMaxLinePerStackEntry();
    private final Thread mCurrentThread;

    public StackSampler() {
        super();
        mCurrentThread = Thread.currentThread();
    }

    private final ScheduledExecutorService stackSamplerExecutor = Jarvis.newSingleThreadScheduledExecutor("MSCStackSampler");
    private ScheduledFuture<?> stackSampleFuture;

    public ArrayList<String> getThreadStackEntries() {
        ArrayList<String> result = new ArrayList<>();
        synchronized (sStackMap) {
            for (Long entryTime : sStackMap.keySet()) {
                result.add(SEPARATOR + "unixTs:" + entryTime
                        + SEPARATOR
                        + sStackMap.get(entryTime));
            }
        }
        return result;
    }

    public void doSampleDelayed(long delay) {
        if (stackSamplerExecutor.isShutdown()) {
            return;
        }
        stackSampleFuture = stackSamplerExecutor.schedule(new Runnable() {
            @Override
            public void run() {
                doSample();
            }
        }, delay, TimeUnit.MILLISECONDS);
    }

    public void removeAllCallbacks() {
        if (stackSampleFuture != null) {
            stackSampleFuture.cancel(true);
        }
    }

    public void shutDown() {
        removeAllCallbacks();
        stackSamplerExecutor.shutdown();
    }

    private void doSample() {
        StringBuilder stringBuilder = new StringBuilder();

        int count = 0;
        for (StackTraceElement stackTraceElement : mCurrentThread.getStackTrace()) {
            if (count < mMaxLinePerEntry) {
                count++;
                stringBuilder
                        .append(stackTraceElement.toString())
                        .append(SEPARATOR);
            }
        }

        synchronized (sStackMap) {
            if (sStackMap.size() == mMaxEntryCount && mMaxEntryCount > 0) {
                sStackMap.remove(sStackMap.keySet().iterator().next());
            }
            sStackMap.put(System.currentTimeMillis(), stringBuilder.toString());
        }
    }
}