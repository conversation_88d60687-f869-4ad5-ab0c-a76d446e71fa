package com.meituan.msc.modules.page.render.rn.fps;

public class MSCFpsEvent {
    /**
     * 采集过程中的最小FPS
     */
    public double minFps = Integer.MAX_VALUE;
    /**
     * 采集过程中刷新的总帧数
     * 在小于N系统版本上此数据由外部传入，在>=N系统版本上此数据内部计数得到
     */
    public int frameTotalCount = 0;
    /**
     * 采集过程中所有帧累计渲染耗时
     * 在小于N系统版本上此数据由外部传入，在>=N系统版本上此数据内部计数得到
     */
    public long frameTotalCostTime = 0;
    /**
     * 是否正在采集数据。因为对于滚动FPS和自定义FPS来说，整个测量过程可能是不连续的
     */
    public volatile boolean sampleUpdateEnabled;

    private final String type;
    private double avgFps = 0;
    private static final float NANOS_IN_SECOND = 1000000000.0F;

    private int maxFrameCount;
    // todo: 变量更名为scrollingFrameTotalCostTime, scrollingFrameTotalCount
    private long lastFrameTotalCostTime; //滚动fps统计需要，因为一个页面会滚动多次
    private int lastFrameTotalCount;

    /**
     * 初始化函数
     *
     * @param type FPS类型：页面？滚动？自定义？
     */
    public MSCFpsEvent(String type) {
        this.type = type;
    }

    /**
     * 初始化函数
     *
     * @param type        FPS类型：页面？滚动？自定义？
     * @param refreshRate 指定固有刷新频率，观测值的上限。
     */
    public MSCFpsEvent(String type, int refreshRate) {
        this(type);
        this.maxFrameCount = refreshRate;
    }


    /**
     * 获取FPS类型
     *
     * @return FPS类型
     */
    public String getType() {
        return type;
    }

    /**
     * 获取采集过程中的平均FPS
     *
     * @return 平均FPS
     */
    public double getAvgFps() {
        return avgFps;
    }

    /**
     * @param currentFrameTotalCostTime
     * @param currentFrameTotalCount
     * @hide
     */
    public void computeAvgFps(long currentFrameTotalCostTime, int currentFrameTotalCount) {
        long costTime = currentFrameTotalCostTime - frameTotalCostTime;
        int count = currentFrameTotalCount - frameTotalCount;
        if (costTime > 0 && count > 0) {
            avgFps = count * NANOS_IN_SECOND / costTime;
        }
    }

    void computeScrollAvgFps() {
        if (lastFrameTotalCostTime > 0 && lastFrameTotalCount > 0) {
            avgFps = lastFrameTotalCount * NANOS_IN_SECOND / lastFrameTotalCostTime;
        }
    }

    void computeLastTimeAndCount(long currentFrameTotalCostTime, int currentFrameTotalCount) {
        long costTime = currentFrameTotalCostTime - frameTotalCostTime;
        int count = currentFrameTotalCount - frameTotalCount;
        if (costTime > 0 && count > 0) {
            lastFrameTotalCostTime += costTime;
            lastFrameTotalCount += count;
        }
    }

    void reset() {
        lastFrameTotalCostTime = 0;
        lastFrameTotalCount = 0;
    }

    /**
     * 观测值是否有效
     *
     * @return
     */
    public boolean isValid() {
        return !Double.isNaN(avgFps);
    }

    /**
     * 获取平均FPS
     *
     * @return 平均FPS
     */
    public double getMetricValue() {
        if (avgFps > maxFrameCount && maxFrameCount > 0) {
            avgFps = maxFrameCount;
        }
        return avgFps;
    }

}