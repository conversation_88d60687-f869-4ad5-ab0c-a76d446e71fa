package com.meituan.msc.modules.page.render.rn.fps;

import android.annotation.TargetApi;
import android.content.Context;
import android.os.Build;
import android.os.Handler;
import android.os.Looper;
import android.view.Display;
import android.view.MotionEvent;
import android.view.View;
import android.view.ViewTreeObserver;
import android.view.WindowManager;

import com.meituan.metrics.util.TimeUtil;
import com.meituan.msc.modules.reporter.MSCLog;

import static android.view.MotionEvent.ACTION_MOVE;

/**
 * @hide
 */
@TargetApi(Build.VERSION_CODES.JELLY_BEAN)
public class MSCMetricsFpsSamplerImpl {
    private static final String TAG = "MSCMetricsFpsSamplerImpl";
    private static final int MAX_SCROLL_GAP_MS = 80;
    private long frameStartTime = 0;
    private long currentFrameTotalCostTime; //将每帧渲染耗时累加
    private int currentFrameTotalCount; //共渲染了这么多帧
    private MSCFpsEvent pageFpsEvent;
    private MSCFpsEvent scrollFpsEvent;
    private final Handler samplerHandler;
    private final Handler mainHandler;
    private final ViewTreeObserver.OnScrollChangedListener scrollChangedListener;
    private boolean scrollFpsEnabled;

    private static final int DEFAULT_REFRESH_RATE = 60;
    private static int refreshRate = DEFAULT_REFRESH_RATE;
    private boolean isRefreshRateGot;
    private volatile boolean isScrolling;//只会被sampler线程写，但可能被其它线程读
    private volatile boolean userHasOperated;//可能在主线程或者sampler线程写

    private final FPSReportListener mReportListener;

    private final MSCMetricsFrameCallbackManager.MetricsFrameCallback innerCallback = new MetricsFpsFrameCallback();
    private final Looper samplerLooper;

    public MSCMetricsFpsSamplerImpl(Context context, Looper looper, FPSReportListener reportListener) {
        samplerLooper = looper;
        samplerHandler = new Handler(looper);
        mReportListener = reportListener;
        scrollChangedListener = new FpsScrollChangeListener();
        mainHandler = new Handler(Looper.getMainLooper());
        isRefreshRateGot = tryToGetRefreshRate(context);
    }

    private void runOnMainThread(Runnable runnable) {
        if (Looper.myLooper() == Looper.getMainLooper()) {
            runnable.run();
        } else {
            mainHandler.post(runnable);
        }
    }

    private void runOnSamplerThread(Runnable runnable) {
        if (Looper.myLooper() == samplerLooper) {
            runnable.run();
        } else {
            samplerHandler.post(runnable);
        }
    }

    public void pageEnter(View view) {
        runOnSamplerThread(new Runnable() {
            @Override
            public void run() {
                pageEnterInner(view);
            }
        });
    }

    private void pageEnterInner(View view) {
        if (view == null) {
            return;
        }
        if (!isRefreshRateGot && view.getContext() != null) {
            isRefreshRateGot = tryToGetRefreshRate(view.getContext());
        }
        MSCMetricsFrameCallbackManager.getInstance().register(innerCallback);
        startRecordPageFps();
        scrollFpsEvent = new MSCFpsEvent("scroll", refreshRate);
        runOnMainThread(new Runnable() {
            @Override
            public void run() {
                registerGlobalScrollCallback(view);
            }
        });
    }

    private void registerGlobalScrollCallback(View view) {
        try {
            view.getViewTreeObserver().addOnScrollChangedListener(scrollChangedListener);
        } catch (Exception e) {
            MSCLog.i(TAG, "register global scroll listener failed", e);
        }
    }

    public void onUserInteract(MotionEvent motionEvent) {
        if (motionEvent.getAction() == ACTION_MOVE) {
            userHasOperated = true;
        }
    }

    public void pageExit(View view) {
        runOnSamplerThread(new Runnable() {
            @Override
            public void run() {
                pageExitInner(view);
            }
        });
    }

    private void pageExitInner(View view) {
        // 退出页面时重置用户操作状态。
        userHasOperated = false;
        stopRecordPageFps();
        MSCMetricsFrameCallbackManager.getInstance().unregister(innerCallback); //避免内存泄漏
        runOnMainThread(new Runnable() {
            @Override
            public void run() {
                unRegisterGlobalScrollCallback(view);
            }
        });
    }

    private void unRegisterGlobalScrollCallback(View view) {
        if (view == null) {
            return;
        }
        try {
            view.getViewTreeObserver().removeOnScrollChangedListener(scrollChangedListener);
        } catch (Exception e) {
            MSCLog.i(TAG, "unregister global scroll listener failed", e);
        }
    }

    private void startRecordPageFps() {
        pageFpsEvent = new MSCFpsEvent("page", refreshRate);
        pageFpsEvent.sampleUpdateEnabled = true;
        pageFpsEvent.frameTotalCostTime = currentFrameTotalCostTime;
        pageFpsEvent.frameTotalCount = currentFrameTotalCount;
    }

    //离开页面时统计该页面的fps和滚动fps
    private void stopRecordPageFps() {
        double uiFPS = -1;
        double uiScrollFPS = -1;
        if (pageFpsEvent != null) {
            pageFpsEvent.computeAvgFps(currentFrameTotalCostTime, currentFrameTotalCount);
            pageFpsEvent.sampleUpdateEnabled = false;
            if (pageFpsEvent.isValid()) {
                uiFPS = pageFpsEvent.getMetricValue();
            }
            pageFpsEvent = null;
        }
        if (scrollFpsEvent != null && scrollFpsEnabled) {
            scrollFpsEvent.computeScrollAvgFps();
            scrollFpsEvent.sampleUpdateEnabled = false;
            if (scrollFpsEvent.isValid()) {
                uiScrollFPS = scrollFpsEvent.getMetricValue();
            }
            scrollFpsEvent = null;
            scrollFpsEnabled = false;
        }
        mReportListener.reportFPS(uiFPS, uiScrollFPS);
    }

    private void startScrollFPS() {
        if (scrollFpsEvent == null) {
            return;
        }
        scrollFpsEvent.sampleUpdateEnabled = true;
        scrollFpsEnabled = true;
        scrollFpsEvent.frameTotalCostTime = currentFrameTotalCostTime;
        scrollFpsEvent.frameTotalCount = currentFrameTotalCount;

        this.isScrolling = true;
    }

    private void stopScrollFPS() {
        if (scrollFpsEvent == null || !scrollFpsEnabled) {
            return;
        }
        scrollFpsEvent.computeLastTimeAndCount(currentFrameTotalCostTime, currentFrameTotalCount);
        scrollFpsEvent.sampleUpdateEnabled = false;

        this.isScrolling = false;
    }

    public boolean isScrolling() {
        return this.isScrolling;
    }

    private void cancelScrollFPS() {
        if (scrollFpsEvent != null) {
            scrollFpsEvent.sampleUpdateEnabled = false;
        }
    }

    public void reset() {
        runOnSamplerThread(new Runnable() {
            @Override
            public void run() {
                resetInner();
            }
        });
    }

    private void resetInner() {
        frameStartTime = 0;
        currentFrameTotalCostTime = 0;
        currentFrameTotalCount = 0;
        MSCMetricsFrameCallbackManager.getInstance().unregister(innerCallback);
    }

    /**
     * 尝试获取更新率，由于系统原因，有时可能会发生异常
     *
     * @return 是否获取成功
     */
    private static boolean tryToGetRefreshRate(Context context) {
        if (context == null) {
            return false;
        }
        try {
            WindowManager windowManager = (WindowManager) context.getSystemService(Context.WINDOW_SERVICE);
            if (windowManager != null) {
                Display currentDisplay = windowManager.getDefaultDisplay();
                if (currentDisplay != null) {
                    refreshRate = Math.round(currentDisplay.getRefreshRate());
                }
            }
            return true;
        } catch (Throwable e) {
            return false;
        }
    }

    /*
     * start/stop/cancel 方法中都会访问entity对象，需要抛到sampler线程执行
     */
    private final Runnable stopScrollTask = new Runnable() {
        @Override
        public void run() {
            stopScrollFPS();
        }
    };

    private final Runnable cancelScrollTask = new Runnable() {
        @Override
        public void run() {
            cancelScrollFPS();
        }
    };

    private final Runnable startScrollTask = new Runnable() {
        @Override
        public void run() {
            startScrollFPS();
        }
    };

    private class MetricsFpsFrameCallback implements MSCMetricsFrameCallbackManager.MetricsFrameCallback {
        @Override
        public void doFrame(long frameTimeNanos) {
            //渲染耗时累加
            if (frameStartTime > 0) {
                final long timeSpan = frameTimeNanos - frameStartTime;
                currentFrameTotalCostTime += timeSpan;
                currentFrameTotalCount++;
            }
            frameStartTime = frameTimeNanos;
        }
    }

    private class FpsScrollChangeListener implements ViewTreeObserver.OnScrollChangedListener {
        private long scrollStartStamp;
        private int scrollingCount = 0;
        private static final int MIN_SCROLLING_STEPS = 5;
        private boolean scrolling = false;

        private final Runnable scrollStopped = new Runnable() {
            @Override
            public void run() {
                scrolling = false;
                // 过滤程序触发的非用户操作滚动事件，一般持续时间很短，并且只会触发1-2次回调
                if (TimeUtil.elapsedTimeMillis() - scrollStartStamp > MAX_SCROLL_GAP_MS * 2 && scrollingCount >= MIN_SCROLLING_STEPS) {
                    samplerHandler.post(stopScrollTask);
                } else if (TimeUtil.elapsedTimeMillis() - scrollStartStamp > MAX_SCROLL_GAP_MS && scrollingCount > 2) {//快速滑动，scrollview在上面判断抛弃较多&&过滤掉程序触发非用户滚动事件
                    samplerHandler.post(stopScrollTask);
                } else {
                    samplerHandler.post(cancelScrollTask);
                }
            }
        };

        @Override
        public void onScrollChanged() {
            mainHandler.removeCallbacks(scrollStopped);
            if (!scrolling && userHasOperated) {
                scrolling = true;
                scrollingCount = 0;
                scrollStartStamp = TimeUtil.elapsedTimeMillis();
                samplerHandler.post(startScrollTask);
            }
            mainHandler.postDelayed(scrollStopped, MAX_SCROLL_GAP_MS);
            ++scrollingCount;
        }
    }

}
