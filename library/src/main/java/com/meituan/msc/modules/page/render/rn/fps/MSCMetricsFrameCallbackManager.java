package com.meituan.msc.modules.page.render.rn.fps;

import android.view.Choreographer;

import com.sankuai.meituan.Lifecycle.ActivityLifecycleCallbacksSingleton;
import com.sankuai.meituan.Lifecycle.ApplicationSwitchMonitor;

import java.util.List;
import java.util.concurrent.CopyOnWriteArrayList;

public class MSCMetricsFrameCallbackManager implements Choreographer.FrameCallback, ApplicationSwitchMonitor {
    private final Choreographer mChoreographer;
    private final boolean isChoreographerReady;
    private boolean isForeground;
    private final List<MetricsFrameCallback> mCallbacks = new CopyOnWriteArrayList<>();

    public interface MetricsFrameCallback {
        void doFrame(long frameTimeNanos);
    }

    private static final ThreadLocal<MSCMetricsFrameCallbackManager> sThreadInstance = new ThreadLocal<MSCMetricsFrameCallbackManager>() {
        @Override
        protected MSCMetricsFrameCallbackManager initialValue() {
            return new MSCMetricsFrameCallbackManager();
        }
    };

    public static MSCMetricsFrameCallbackManager getInstance() {
        return sThreadInstance.get();
    }

    private MSCMetricsFrameCallbackManager() {
        mChoreographer = Choreographer.getInstance();
        isChoreographerReady = true;
        mChoreographer.postFrameCallback(MSCMetricsFrameCallbackManager.this);
        isForeground = true;
        ActivityLifecycleCallbacksSingleton.getInstance().addApplicationSwitchMonitor(MSCMetricsFrameCallbackManager.this);
    }

    @Override
    public void doFrame(long frameTimeNanos) {
        for (MetricsFrameCallback callback : mCallbacks) {
            callback.doFrame(frameTimeNanos);
        }
        if (isForeground && isChoreographerReady) { //防止从外部直接调用doFrame
            mChoreographer.postFrameCallback(this);
        }
    }

    //避免callback重复，用list是为了保持顺序
    public void register(MetricsFrameCallback callback) {
        if (callback != null && !mCallbacks.contains(callback)) {
            mCallbacks.add(callback);
        }
    }

    public void unregister(MetricsFrameCallback callback) {
        mCallbacks.remove(callback);
    }

    @Override
    public void applicationEnterForeground() {
        isForeground = true;
        if (isChoreographerReady) {
            mChoreographer.postFrameCallback(this);
        }
    }

    @Override
    public void applicationEnterBackground() {
        isForeground = false;
    }

    public boolean isForeground() {
        return isForeground;
    }
}
