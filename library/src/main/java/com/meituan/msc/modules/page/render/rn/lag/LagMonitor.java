package com.meituan.msc.modules.page.render.rn.lag;

import android.os.Debug;
import android.os.Handler;
import android.os.Looper;
import android.os.SystemClock;
import android.util.Printer;

import com.meituan.metrics.looper_logging.LooperLoggingManager;
import com.meituan.msc.modules.page.render.rn.fps.MSCMetricsFrameCallbackManager;

import java.util.ArrayList;
import java.util.Collections;
import java.util.List;

public class LagMonitor implements Printer, MSCMetricsFrameCallbackManager.MetricsFrameCallback {
    private volatile boolean isMonitorBusy = false;
    private final LagCallback lagCallback;
    private volatile long startTimeMillis = 0L;
    private final List<ThreadStackEntry> laggyCheckedSTs = Collections.synchronizedList(new ArrayList<>());
    private final long lagThreshold;
    private final long sampleInterval;
    private final Handler sampleHandler;
    private final Thread thread;
    private final Looper looper;
    private final String targetThreadName;
    private volatile int sampleTimes;
    private boolean isLagReported;
    private int sampleMethod = 0;
    private final Runnable stacktraceSampleTask;
    private final boolean enablePrinter;
    private final int maxStackEntryCount;

    public LagMonitor(int maxStackEntryCount, boolean enablePrinter, long lagThreshold, Looper looper, LagCallback callback) {
        class NamelessClass_1 implements Runnable {
            NamelessClass_1() {
            }

            public void run() {
                if (LagMonitor.this.isMonitorBusy && MSCMetricsFrameCallbackManager.getInstance().isForeground()) {
                    LagMonitor.this.getStack();
                    LagMonitor.this.sampleTimes++;
                    long costs = SystemClock.elapsedRealtime() - LagMonitor.this.startTimeMillis;
                    if (LagMonitor.this.sampleTimes == 1) {
                        LagMonitor.this.isLagReported = false;
                    }

                    if (LagMonitor.this.lagCallback != null && !LagMonitor.this.isLagReported && costs >= LagMonitor.this.lagThreshold && !LagMonitor.this.laggyCheckedSTs.isEmpty()) {

                        LagMonitor.this.lagCallback.onLagEvent(costs, LagMonitor.this.targetThreadName, new ArrayList<>(LagMonitor.this.laggyCheckedSTs));
                        LagMonitor.this.sampleHandler.removeCallbacks(this);
                        LagMonitor.this.isLagReported = true;
                    }

                    if (LagMonitor.this.isMonitorBusy && !LagMonitor.this.isLagReported) {
                        LagMonitor.this.sampleHandler.postDelayed(this, LagMonitor.this.sampleInterval);
                    }
                }

            }
        }

        this.stacktraceSampleTask = new NamelessClass_1();
        this.lagCallback = callback;
        this.lagThreshold = lagThreshold;
        this.sampleInterval = Math.max(lagThreshold / 2L, 1000L);

        this.sampleHandler = new Handler(looper);
        this.looper = looper;
        this.thread = looper.getThread();
        this.targetThreadName = this.thread.getName();
        this.enablePrinter = enablePrinter;
        this.maxStackEntryCount = maxStackEntryCount;
    }

    private void runOnSamplerThread(Runnable runnable) {
        if (Looper.myLooper() == looper) {
            runnable.run();
        } else {
            sampleHandler.post(runnable);
        }
    }

    public void println(String x) {
        if (this.enablePrinter && !Debug.isDebuggerConnected() && x != null && x.length() > 0) {
            boolean loopStarted = x.charAt(0) == '>';
            this.isMonitorBusy = loopStarted;
            if (loopStarted) {
                this.startTimeMillis = SystemClock.elapsedRealtime();
                this.laggyCheckedSTs.clear();
                this.sampleHandler.postDelayed(this.stacktraceSampleTask, this.sampleInterval);
            } else {
                this.sampleTimes = 0;
                this.sampleHandler.removeCallbacks(this.stacktraceSampleTask);
            }
        }
    }

    private void getStack() {
        try {
            if (this.laggyCheckedSTs.size() >= maxStackEntryCount) {
                this.laggyCheckedSTs.remove(this.laggyCheckedSTs.size() - 1);
            }

            long ts = System.currentTimeMillis();
            StackTraceElement[] stackTrace = this.thread.getStackTrace();
            if (stackTrace.length > 0) {
                ThreadStackEntry stackEntry = new ThreadStackEntry(ts, stackTrace);
                this.laggyCheckedSTs.add(stackEntry);
            }
        } catch (Throwable var6) {
            this.laggyCheckedSTs.clear();
        }
    }

    public void register() {
        runOnSamplerThread(new Runnable() {
            @Override
            public void run() {
                registerInner();
            }
        });
    }

    private void registerInner() {
        if (this.sampleMethod == 0) {
            LooperLoggingManager.getInstance().registerLogging(this.looper, this);
            MSCMetricsFrameCallbackManager.getInstance().register(this);
        } else {
            MSCMetricsFrameCallbackManager.getInstance().register(this);
            this.startNewChoreographerSample();
        }
    }

    public void unregister() {
        runOnSamplerThread(new Runnable() {
            @Override
            public void run() {
                unregisterInner();
            }
        });
    }

    private void unregisterInner() {
        if (this.sampleMethod == 0) {
            LooperLoggingManager.getInstance().unRegisterLogging(this.looper, this);
        } else {
            MSCMetricsFrameCallbackManager.getInstance().unregister(this);
        }
    }

    public void doFrame(long frameTimeNanos) {
        if (this.sampleMethod == 0) {
            this.unregister();
            this.sampleMethod = 1;
        }

        this.startNewChoreographerSample();
    }

    private void startNewChoreographerSample() {
        this.sampleHandler.removeCallbacks(this.stacktraceSampleTask);
        this.laggyCheckedSTs.clear();
        this.sampleTimes = 0;
        this.startTimeMillis = SystemClock.elapsedRealtime();
        this.sampleHandler.postDelayed(this.stacktraceSampleTask, this.sampleInterval);
        this.isMonitorBusy = true;
    }
}
