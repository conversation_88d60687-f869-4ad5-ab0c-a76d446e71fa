package com.meituan.msc.modules.page.render.rn.lag;

import java.util.ArrayList;
import java.util.List;

public final class StackTraceUtil {

    public static final String SEPARATOR = System.lineSeparator();

    private StackTraceUtil() {
    }

    public static String stringifyStackTraceElements(StackTraceElement[] stackTraceElements, int maxLine) {
        StringBuilder stringBuilder = new StringBuilder();
        int count = 0;
        for (StackTraceElement stackTraceElement : stackTraceElements) {
            if (count < maxLine) {
                count++;
                stringBuilder.append(stackTraceElement.toString())
                        .append(SEPARATOR);
            }
        }
        return stringBuilder.toString();
    }

    public static ArrayList<String> stringifyStackEntries(List<ThreadStackEntry> entries, int maxLine) {
        ArrayList<String> callstack = new ArrayList<>();
        for (ThreadStackEntry entry : entries) {
            callstack.add(SEPARATOR + "unixTs:" + entry.ts + SEPARATOR + stringifyStackTraceElements(entry.stackTraceElements, maxLine));
        }
        return callstack;
    }
}
