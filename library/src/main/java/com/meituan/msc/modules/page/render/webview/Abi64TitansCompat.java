package com.meituan.msc.modules.page.render.webview;

import android.content.Context;
import android.content.SharedPreferences;
import android.support.annotation.NonNull;
import android.support.annotation.Nullable;
import android.support.v4.content.ContextCompat;
import android.text.TextUtils;

import com.meituan.msc.modules.reporter.MSCLog;
import com.meituan.msc.extern.MSCEnvHelper;
import com.sankuai.common.utils.ProcessUtils;


import java.io.File;

/**
 * 解决宿主App由32位架构升级64位架构后WebView Crash的问题，参考KNB在点评的修复方案
 * https://dev.sankuai.com/code/repo-detail/knb/android-nova-titans/commit/9860a313d129b6327f80ec4a4678d3bfe9120b34?branch=adapter%2F11.20.6_for_dp_target28
 *
 * 判断当前进程是否是64位的方法
 * https://km.sankuai.com/page/997366289
 * <AUTHOR>
 * @date 2021/8/16.
 */
public final class Abi64TitansCompat {

    private static final int MMP_PROCESS_SUFFIX_NUM_MAX= 3;
    private static final String TAG = "Abi64WebViewCompat";

    private static final String CHROMIUM_PREFS_NAME = "WebViewChromiumPrefs";
    private static final String APP_WEB_VIEW_DIR_NAME = "app_webview";
    private static final String MINIAPP = ":miniApp";
    private static final String GPU_CACHE_DIR_NAME = "GPUCache";
    private static final String DEFAULT = "Default";
    private static final String SP_NAME = "delete_webview_gpu_cache";
    private static final String MMP_DELETED_GPU_CACHE_APP_ABI = "deleted_gpu_cache_app_abi";

    public static void obliterate(@Nullable Context context) {
        if (context == null) {
            return;
        }

        if (isAbiNotChanged()) {
            printInfo("obliterate abi is not changed");
            return;
        }

        // 32位升级到64位 or 64位降级到32位，清除缓存，以下为清除逻辑

        try {
            printInfo("delete file start");
            //参考com.android.webview.chromium.WebViewChromiumFactoryProvider
            final Context appContext = context.getApplicationContext();
            final SharedPreferences chromiumPrefs = appContext.getSharedPreferences(
                    CHROMIUM_PREFS_NAME,
                    Context.MODE_PRIVATE
            );
            chromiumPrefs.edit().clear().apply();

            // /app_webview/GPUCache
            final File appWebViewDir = new File(
                    ContextCompat.getDataDir(context) + File.separator
                            + APP_WEB_VIEW_DIR_NAME + File.separator + GPU_CACHE_DIR_NAME);

            // /app_webview/Default/GPUCache
            final File appDefaultWebViewDir = new File(
                    ContextCompat.getDataDir(context) + File.separator
                            + APP_WEB_VIEW_DIR_NAME + File.separator + DEFAULT + File.separator + GPU_CACHE_DIR_NAME);

            if (deleteRecursive(appWebViewDir)
                    && deleteRecursive(appDefaultWebViewDir)
                    && deleteSubProcessWebViewCache(context)
                    && deleteSubProcessWebViewDefaultCache(context)) {
                setFlag();
            }
        } catch (Exception e) {
            printInfo(e.getMessage());
        }
    }

    private static boolean isAbiNotChanged() {
        String deletedGPUCacheAppAbi = getPreferences().getString(MMP_DELETED_GPU_CACHE_APP_ABI, null);
        // 保持32位架构不变，不清除缓存
        if (deletedGPUCacheAppAbi == null && !ProcessUtils.is64Bit()) {
            printInfo("保持32位架构不变，不清除缓存");
            return true;
        }

        // 32位升级到64位架构后，abi保持不变，不清除缓存
        if (TextUtils.equals("64", deletedGPUCacheAppAbi) && ProcessUtils.is64Bit()) {
            printInfo("32位升级到64位架构后，abi保持不变，不清除缓存");
            return true;
        }

        // 64位降级到32位架构后，abi保持不变，不清除缓存
        printInfo("64位降级到32位架构后，abi保持不变，不清除缓存");
        return TextUtils.equals("32", deletedGPUCacheAppAbi) && !ProcessUtils.is64Bit();
    }

    private static SharedPreferences getPreferences() {
        return MSCEnvHelper.getSharedPreferences(SP_NAME);
    }

    private static void setFlag() {
        getPreferences().edit().putString(MMP_DELETED_GPU_CACHE_APP_ABI, ProcessUtils.is64Bit() ? "64" : "32").apply();
    }

    // /app_webview_com.sankuai.meituan:miniApp0/GPUCache
    private static boolean deleteSubProcessWebViewCache(Context context) {
        for (int i = 0; i <= 3; i++) {
            final File appWebViewDirProcess = new File(
                    ContextCompat.getDataDir(context) + File.separator
                            + APP_WEB_VIEW_DIR_NAME + "_" + context.getPackageName() + MINIAPP + i + File.separator + GPU_CACHE_DIR_NAME);
            if (!deleteRecursive(appWebViewDirProcess)) {
                return false;
            }
        }
        return true;
    }

    // /app_webview_com.sankuai.meituan:miniApp0/Default/GPUCache
    private static boolean deleteSubProcessWebViewDefaultCache(Context context) {
        for (int i = 0; i <= MMP_PROCESS_SUFFIX_NUM_MAX; i++) {
            final File appDefaultWebViewDirProcess = new File(
                    ContextCompat.getDataDir(context) + File.separator
                            + APP_WEB_VIEW_DIR_NAME + "_" + context.getPackageName() + MINIAPP + i + File.separator + DEFAULT + File.separator + GPU_CACHE_DIR_NAME);
            if (!deleteRecursive(appDefaultWebViewDirProcess)) {
                return false;
            }
        }
        return true;
    }

    private static boolean deleteRecursive(@NonNull File fileOrDirectory) {
        if (fileOrDirectory.isDirectory()) {
            File[] files = fileOrDirectory.listFiles();
            if (files != null) {
                for (File child : files) {
                    deleteRecursive(child);
                }
            }
        }
        boolean isSuccessDelete = !fileOrDirectory.exists() || fileOrDirectory.delete();
        printInfo("delete isSuccessDelete: " + isSuccessDelete + " fileName: " + fileOrDirectory);
        return isSuccessDelete;
    }

    private static void printInfo(String message) {
        MSCLog.e(TAG, message);
    }
}
