package com.meituan.msc.modules.page.render.webview;

import static com.meituan.msc.common.perf.PerfEventConstant.LOAD_HTML_END;
import static com.meituan.msc.common.perf.PerfEventConstant.READ_WEB_VIEW_BASE_PAGE_BOOTSTRAP;
import static com.meituan.msc.common.perf.PerfEventConstant.READ_WEB_VIEW_MAIN_PAGE_BOOTSTRAP;
import static com.meituan.msc.common.perf.PerfEventConstant.READ_WEB_VIEW_OTHER_PAGE_BOOTSTRAP;

import android.graphics.Bitmap;
import android.os.Handler;
import android.os.Looper;
import android.support.annotation.Keep;
import android.support.annotation.Nullable;
import android.text.TextUtils;
import android.webkit.ValueCallback;

import com.meituan.dio.easy.DioFile;
import com.meituan.msc.common.config.MSCConfig;
import com.meituan.msc.common.perf.PerfEventConstant;
import com.meituan.msc.common.utils.FileUtil;
import com.meituan.msc.common.utils.MSCTraceUtil;
import com.meituan.msc.modules.engine.MSCHornRollbackConfig;
import com.meituan.msc.modules.engine.ResultCallback;
import com.meituan.msc.modules.page.render.ReusableRenderer;
import com.meituan.msc.modules.reporter.MSCLog;
import com.meituan.msc.modules.service.MSCFileUtils;
import com.meituan.msc.modules.update.MSCAppModule;
import com.meituan.msc.modules.update.bean.PackageInfoWrapper;
import com.meituan.msc.util.perf.PerfTrace;
import com.meituan.msi.bean.BroadcastEvent;
import com.meituan.msi.bean.EventType;
import com.meituan.msi.bean.LifecycleData;
import com.meituan.msi.lifecycle.IPageLifecycleCallback;

import java.io.IOException;
import java.util.Queue;
import java.util.concurrent.ConcurrentLinkedQueue;

/**
 * 本层仅关注与WebView及逻辑层的通信，避免涉及具体逻辑
 */
public abstract class BaseWebViewRenderer extends ReusableRenderer implements OnPageFinishedListener {

    public final String TAG = "BaseWebViewRenderer@" + getViewId() + "@" + Integer.toHexString(hashCode());

    private static final int CACHE_LIMIT_SIZE = 20;

    protected final Handler mainHandler = new Handler(Looper.getMainLooper());
    protected MSCWebView webView;

    /**
     * 3个消息缓存队列:
     * 基础包、主包注入在pageEnd/onPageFinished后执行--pendingMessageWaitForPageFinished
     * 所有视图层消息在onFirstScript后执行--pendingMessageWaitForFirstScript
     * 所有webview模块方法调用在onPageStart后执行--pendingMessageWaitForPageStart
     */
    protected final Queue<EvaluateJavascriptInfo> pendingMessageWaitForFirstScript = new ConcurrentLinkedQueue<>();
    protected final Queue<EvaluateJavascriptInfo> pendingMessageWaitForPageFinished = new ConcurrentLinkedQueue<>();
    protected final Queue<EvaluateJavascriptInfo> pendingMessageWaitForPageStart = new ConcurrentLinkedQueue<>();

    protected volatile LoadStage loadStage = LoadStage.INITIAL;
    protected volatile long[] allLoadStageTimeStamps = new long[LoadStage.values().length];

    protected volatile boolean firstScripted = false;

    protected volatile boolean pageFinished = false;

    protected volatile boolean pageStarted = false;
    // 一个Renderer取一次开关，确保页面展示功能一致性
    protected boolean enableOnPageStartQueue = MSCHornRollbackConfig.enableOnPageStartQueue();

    protected volatile long evaluateLaunchStageJavascriptStartTime;

    @Keep
    public enum LoadStage {
        INITIAL,    // 初始状态
        LOAD_TEMPLATE,
        HTML_LOADED,    // 已加载HTML，有HTML后续native才可执行js以继续加载包
        FIRST_SCRIPT,
        WEB_VIEW_PAGE_FINISHED,  //WebView回调HTML加载完毕，此后可evaluateJs
        PAGE_START_SEND;    // 已发送path给前端，加载具体页面

        public boolean isAtLeast(LoadStage other) {
            return compareTo(other) >= 0;
        }
    }

    static class EvaluateJavascriptInfo {
        final WebViewJavaScript script;
        final @Nullable ValueCallback<String> resultCallback;
        final WebViewEvaluateJavascriptListener evaluateJavascriptListener;

        EvaluateJavascriptInfo(WebViewJavaScript script, @Nullable ValueCallback<String> resultCallback, WebViewEvaluateJavascriptListener evaluateJavascriptListener) {
            this.script = script;
            this.resultCallback = resultCallback;
            this.evaluateJavascriptListener = evaluateJavascriptListener;
        }
    }

    protected void raiseLoadStageTo(LoadStage target) {
        MSCLog.d(TAG, "raiseLoadStage from ", loadStage.name(), " to ", target.name());

        if (!loadStage.isAtLeast(target)) {
            loadStage = target;
            allLoadStageTimeStamps[target.ordinal()] = System.currentTimeMillis();
        }
    }

    /**
     * 获取离指定时间戳最近的WebView状态
     * @param targetTime
     * @return
     */
    public LoadStage getLastWebViewStateBeforeTargetTime(long targetTime) {
        for (int i = loadStage.ordinal(); i >= 0; i--) {
            if (allLoadStageTimeStamps[i] > 0 && allLoadStageTimeStamps[i] <= targetTime) {
                return LoadStage.values()[i];
            }
        }
        return LoadStage.INITIAL;
    }

    // 向WebView发送消息，内部逻辑 ----------------------------------------------------

    /**
     * 发送与视图层的交互消息
     * WebView.onPageFinished后才允许执行，任何消息均受此限制
     */
    protected synchronized void evaluateJavascript(WebViewJavaScript script) {
        evaluateJavascript(script, null);
    }

    protected synchronized void evaluateJavascript(WebViewJavaScript script, @Nullable ValueCallback<String> resultCallback) {
        evaluateJavascript(script, resultCallback, null);
    }

    protected synchronized void evaluateJavascript(WebViewJavaScript script, @Nullable ValueCallback<String> resultCallback, WebViewEvaluateJavascriptListener evaluateJavascriptListener) {
        if (pageFinished) {
            if (script.isSupportMessagePort() && mRuntime != null &&
                    !MSCHornRollbackConfig.isRollbackMessagePortWithAppId(mRuntime.getAppId()) &&
                    webView.messagePortReady()) {
                webView.postMessageWithNativeMessagePort(script);
            } else {
                webView.evaluateJavascript(script, resultCallback, evaluateJavascriptListener);
            }
        } else {
            logScriptIfNeeded(script, "Pending_Message_Wait_For_Page_Finished");
            pendingMessageWaitForPageFinished.add(new EvaluateJavascriptInfo(script, resultCallback, evaluateJavascriptListener));
        }
    }

    @Override
    public boolean needCoverLayer() {
        return true;
    }

    @Override
    public void onPageFinished(String url, String source) {
        MSCLog.i(TAG, "onPageFinished view@", getViewId(), pageData.mPagePath, url, ", source:", source);
        if (!MSCHornRollbackConfig.get().getConfig().rollbackOnPageFinishedInAdvanced) {
            if (pageFinished) {
                return;
            }
        }
        raiseLoadStageTo(LoadStage.WEB_VIEW_PAGE_FINISHED);
        pageFinished = true;
        PerfTrace.online().instant(LOAD_HTML_END).arg("url", url).report();
        evaluatePendingEventsForPageFinished();
        evaluatePendingEventsForOnFirstScript();
    }

    public void evaluateBackgroundColorReset() {
        if (!MSCHornRollbackConfig.isRollbackBackgroundColor() && !TextUtils.isEmpty(pageData.mPagePath)) {
            String pageBackgroundColor = mMSCAppModule.getBackgroundColorString(pageData.mPagePath);
            if (!TextUtils.isEmpty(pageBackgroundColor)) {
                String jsString = String.format(TemplateHelper.JAVASCRIPT_BACKGROUND_COLOR_RESET, pageBackgroundColor);
                evaluateJavascript(PureWebViewJavaScript.from(jsString));
            }
        }
    }

    @Override
    public void onPageStarted(String url, Bitmap favicon) {
        MSCLog.i(TAG, "onPageStarted view@", getViewId(), pageData.mPagePath, url);
    }

    private synchronized void evaluatePendingEventsForPageFinished() {
        if (pendingMessageWaitForPageFinished.size() > 0) {
            for (EvaluateJavascriptInfo evaluateJavascriptInfo : pendingMessageWaitForPageFinished) {
                evaluateJavascriptInfo.script.markScriptPendingTime();
                evaluateJavascript(evaluateJavascriptInfo.script, evaluateJavascriptInfo.resultCallback,
                        evaluateJavascriptInfo.evaluateJavascriptListener);
            }
            pendingMessageWaitForPageFinished.clear();
        }
    }

    /**
     * 与WebView的普通交互消息应仅在渲染层活跃时执行（使用{@link #evaluateJavascriptWhenActive}）
     * 使用本方法执行不受限制，应仅用于启动类消息
     */
    public synchronized void evaluateLaunchStageJavascript(WebViewJavaScript script, @Nullable ValueCallback<String> resultCallback) {
        evaluateLaunchStageJavascript(script, resultCallback, null);
    }

    protected synchronized void evaluateLaunchStageJavascript(WebViewJavaScript script, @Nullable ValueCallback<String> resultCallback, WebViewEvaluateJavascriptListener evaluateJavascriptListener) {
        evaluateJavascript(script, resultCallback, evaluateJavascriptListener);
    }

    protected abstract boolean isActiveAndCanEvaluateJavascript();

    public synchronized void onFirstScript() {
        if (evaluateLaunchStageJavascriptStartTime != 0) {
            MSCLog.d(TAG, "evaluateJavascript costTime: ",
                System.currentTimeMillis() - evaluateLaunchStageJavascriptStartTime);
        }
        raiseLoadStageTo(LoadStage.FIRST_SCRIPT);
        firstScripted = true;
        evaluatePendingEventsForOnFirstScript();
    }

    protected abstract void onPageStart(String path);

    @Override
    public boolean recycle() {
        recycleCount++;
        recycling = true;
        pendingMessageWaitForFirstScript.clear();
        pendingMessageWaitForPageFinished.clear();
        // webview复用时会调用recycle,此处清空队列和pageStarted布尔值会确保重新维护下一个页面的缓存队列
        pendingMessageWaitForPageStart.clear();
        pageStarted = false;
        return false;
    }

    /**
     * 向WebView发送普通消息，domLoaded后且页面活跃时才允许执行
     */
    protected synchronized void evaluateJavascriptWhenActive(final WebViewJavaScript script,
                                                             final @Nullable ValueCallback<String> resultCallback,
                                                             final @Nullable WebViewEvaluateJavascriptListener evaluateJavascriptListener) {
        if (firstScripted && hasPageFinished()) {
            // domLoaded后才允许执行
            if (isActiveAndCanEvaluateJavascript()) {
                if (evaluatePendingEventsForOnFirstScript()) {
                    // 正常不应用到此处，兜底，防止错过FIRST_SCRIPT事件
                    MSCLog.e(TAG, "pending events for domLoaded not evaluated when domLoaded publish");
                }
                if (enableOnPageStartQueue && shouldWaitForPageStart(script)) {
                    if (TextUtils.equals(script.getMethodName(), PerfEventConstant.ON_PAGE_START_EVENT)) {
                        evaluateJavascript(script, resultCallback, evaluateJavascriptListener);
                        pageStarted = true;
                        // 收到onPageStart消息时给前端发送onPageStart等待队列中的消息
                        logScriptIfNeeded(script, "Evaluate_Message_Wait_Page_Start");
                        evaluatePendingEventsForOnPageStart();
                    } else {
                        logScriptIfNeeded(script, "Pending_Message_Wait_Page_Start");
                        pendingMessageWaitForPageStart.add(new EvaluateJavascriptInfo(script, resultCallback, evaluateJavascriptListener));
                    }
                } else {
                    logScriptIfNeeded(script, "Evaluate_Message_Wait_First_Script");
                    evaluateJavascript(script, resultCallback, evaluateJavascriptListener);
                }
                return;
            } else if (pendingMessageWaitForFirstScript.size() >= CACHE_LIMIT_SIZE) {
                // 超出队列限制且可执行时，放松限制，执行最老的事件，此处放松的主要是对isShow的限制
                EvaluateJavascriptInfo poll = pendingMessageWaitForFirstScript.poll();
                if (poll != null) {
                    logScriptIfNeeded(poll.script, "Evaluate_Message_When_Over_Cache_Limit");
                    evaluateJavascript(poll.script, poll.resultCallback, poll.evaluateJavascriptListener);
                } else {
                    logScriptIfNeeded(PureWebViewJavaScript.from("EvaluateJavascriptInfo is null"), "Evaluate_Message_When_Over_Cache_Limit");
                }
            }
        }
        if (enableOnPageStartQueue && shouldWaitForPageStart(script)) {
            logScriptIfNeeded(script, "Pending_Message_Wait_Page_Start_And_First_Script");
            if (TextUtils.equals(script.getMethodName(), PerfEventConstant.ON_PAGE_START_EVENT)) {
                // 此时由于 不符合 firstScripted && hasPageFinished() 条件，不能直接执行，将onPageStart的消息转存到onFirstScript消息队列
                pageStarted = true;
                pendingMessageWaitForFirstScript.add(new EvaluateJavascriptInfo(script, resultCallback, evaluateJavascriptListener));
                moveOnPageStartToOnFirstScript();
            } else {
                // 未收到onPageStart，加入onPageStart队列
                pendingMessageWaitForPageStart.add(new EvaluateJavascriptInfo(script, resultCallback, evaluateJavascriptListener));
            }
        } else {
            logScriptIfNeeded(script, "Pending_Message_Wait_First_Script");
            // 新事件未执行，则加入队列
            pendingMessageWaitForFirstScript.add(new EvaluateJavascriptInfo(script, resultCallback, evaluateJavascriptListener));
        }
    }

    private void moveOnPageStartToOnFirstScript() {
        if (!pendingMessageWaitForPageStart.isEmpty()) {
            pendingMessageWaitForFirstScript.addAll(pendingMessageWaitForPageStart);
            pendingMessageWaitForPageStart.clear();
        }
    }

    /**
     * 对 WebViewMethods 中的消息进行排队处理
     * @param script 仅处理 WebViewBridgeInvokeWebViewJavaScript
     * @return 是否等待onPageStart事件
     */
    private boolean shouldWaitForPageStart(WebViewJavaScript script) {
        if (pageStarted || TextUtils.isEmpty(script.getMethodName())) {
            return false;
        }
        String methodName = script.getMethodName();
        return !PerfEventConstant.ON_PAGE_RECYCLE.equals(methodName) && !PerfEventConstant.ON_PAGE_PRELOAD.equals(methodName);
    }


    private void logScriptIfNeeded(WebViewJavaScript script, String logMessage) {
        if (MSCConfig.enableOutputDetailLogForWhiteScreen(mMSCAppModule == null ? null : mMSCAppModule.getAppId())) {
            MSCLog.i(TAG, logMessage, script);
        }
    }

    /**
     * 同Fe通信，已经pageFinished状态
     *
     * @return true pageFinished状态
     */
    boolean hasPageFinished() {
        //云控不需要等PageFinished
        if (!MSCConfig.needWaitForPageFinished()) return true;
        return pageFinished;
    }

    /**
     * @return 是否发生执行
     */
    protected synchronized boolean evaluatePendingEventsForOnFirstScript() {
        if (!pendingMessageWaitForFirstScript.isEmpty() && firstScripted && hasPageFinished() && isActiveAndCanEvaluateJavascript()) {
            MSCLog.v(TAG, "evaluate pending JS when dom loaded: ", pendingMessageWaitForFirstScript.size());
            for (EvaluateJavascriptInfo evaluateJavascriptInfo : pendingMessageWaitForFirstScript) {
                evaluateJavascriptInfo.script.markScriptPendingTime();
                evaluateJavascript(evaluateJavascriptInfo.script, evaluateJavascriptInfo.resultCallback,
                        evaluateJavascriptInfo.evaluateJavascriptListener);
//                 MSCLog.v(TAG, "evaluate pending JS when dom loaded: " + pair.first);
            }
            pendingMessageWaitForFirstScript.clear();
            return true;
        }
        return false;
    }

    protected synchronized boolean evaluatePendingEventsForOnPageStart() {
        if (enableOnPageStartQueue && !pendingMessageWaitForPageStart.isEmpty() && pageStarted) {
            for (EvaluateJavascriptInfo info : pendingMessageWaitForPageStart) {
                info.script.markScriptPendingTime();
                evaluateJavascript(info.script, info.resultCallback, info.evaluateJavascriptListener);
            }
            pendingMessageWaitForPageStart.clear();
            return true;
        }
        return false;
    }

    /**
     * 加载小程序包
     * page-bootstrap.js包
     *
     * @param packageInfo
     */
    protected void injectPackageContent(final PackageInfoWrapper packageInfo, ResultCallback resultCallback, WebViewEvaluateJavascriptListener evaluateJavascriptListener) {
        String packageTraceName = getPackageTraceName(packageInfo);
        PerfTrace.online().begin(packageTraceName).report();
        DioFile bootStrapFile = packageInfo.getPageBootStrapFile();
        PerfTrace.online().end(packageTraceName).report();
        //getPageBootStrapFile可能返回null
        if (bootStrapFile != null && bootStrapFile.exists()) {
            evaluateJsFile(packageInfo, bootStrapFile, resultCallback, evaluateJavascriptListener);
        } else {
            //load发现没文件,把对应package干掉 避免影响下次加载
            if (resultCallback != null) {
                resultCallback.onReceiveFailValue(new RuntimeException("AppPage#loadServicePackage bootStrapFile not exist, " + packageInfo + ", file: " + bootStrapFile.getPath()));
            }
        }
    }

    private String getPackageTraceName(PackageInfoWrapper packageInfo) {
        if (packageInfo == null) {
            return READ_WEB_VIEW_OTHER_PAGE_BOOTSTRAP;
        }
        if (packageInfo.isBasePackage()) {
            return READ_WEB_VIEW_BASE_PAGE_BOOTSTRAP;
        }
        if (packageInfo.isMainPackage()) {
            return READ_WEB_VIEW_MAIN_PAGE_BOOTSTRAP;
        }
        return READ_WEB_VIEW_OTHER_PAGE_BOOTSTRAP;
    }

    protected void evaluateJsFile(PackageInfoWrapper packageInfo, final DioFile file, ResultCallback resultCallback, WebViewEvaluateJavascriptListener evaluateJavascriptListener) {
        if (file != null && file.exists()) {
            String fileContent;
            try {
                PerfTrace.begin("readFileContent").arg("file", file);
                fileContent = FileUtil.readContent(file);
                PerfTrace.end("readFileContent");
            } catch (IOException e) {
                // todo mmp 旧埋点
                //FileUtil.reportReadDioContentFailed(mRuntime.reporter, file.getPath(), e, pageData.mPagePath, mRuntime.getAppId());
                MSCFileUtils.checkMd5AndDeleteIfNeed("loadPage", packageInfo);
                MSCLog.e(e);
                if (resultCallback != null) {
                    resultCallback.onReceiveFailValue(new IOException("AppPage#evaluateJsFile readContent failed" + file, e));
                }
                return;
            }
            MSCLog.d(TAG, "evaluateJsFile: ", file.getName());
            if (MSCTraceUtil.TRACE_LEVEL >= MSCTraceUtil.LEVEL_HEAVY) {
                PerfTrace.begin("evaluateJsFile_async").arg("file", file).arg("size", fileContent.length());
                final ResultCallback originalResultCallback = resultCallback;
                resultCallback = new ResultCallback() {
                    @Override
                    public void onReceiveFailValue(Exception e) {
                        PerfTrace.end("evaluateJsFile_async");
                        if (originalResultCallback != null) {
                            originalResultCallback.onReceiveFailValue(e);
                        }
                    }

                    @Override
                    public void onReceiveValue(String value) {
                        PerfTrace.end("evaluateJsFile_async");
                        if (originalResultCallback != null) {
                            originalResultCallback.onReceiveValue(value);
                        }
                    }
                };
            }
            String filePath = MSCAppModule.PREFIX_FRAMEWORK + PackageInfoWrapper.PACKAGE_PAGE_BOOTSTRAP;
            evaluateLaunchStageJavascriptStartTime = System.currentTimeMillis();
            if (packageInfo != null && packageInfo.isBasePackage() && mRuntime != null
                    && MSCHornRollbackConfig.isBasePackageEvaluateJavascriptWithFilePath(mRuntime.getAppId())) {
                MSCLog.d(TAG,"BasePackageEvaluateJavascriptWithFilePath");
                evaluateLaunchStageJavascript(PureWebViewJavaScript.from(String.format(TemplateHelper.APPEND_SCRIPT_NEW,
                    filePath, false, false)), resultCallback,
                    evaluateJavascriptListener);
            } else {
                evaluateLaunchStageJavascript(PureWebViewJavaScript.from(fileContent), resultCallback,
                    evaluateJavascriptListener);
            }
        }
    }

    @Override
    public String getRendererUA() {
        // 目前为空，后续根据渲染侧的需求增加
        return "";
    }

    @Override
    public void handleViewEvent(EventType eventType, String msg, BroadcastEvent source) {

    }

    @Override
    public IPageLifecycleCallback getPageLifecycleCallback() {
        return new IPageLifecycleCallback() {
            @Override
            public boolean onBackPressed(int pageId, LifecycleData data) {
                return super.onBackPressed(pageId, data);
            }
        };
    }
}