package com.meituan.msc.modules.page.render.webview;

import android.support.v7.widget.RecyclerView;

import com.meituan.msc.modules.reporter.MSCLog;
import com.sankuai.android.jarvis.Jarvis;

import java.util.concurrent.ScheduledExecutorService;
import java.util.concurrent.ScheduledFuture;
import java.util.concurrent.TimeUnit;

public class HomeListScrollListener extends RecyclerView.OnScrollListener {

    private static final String TAG = "HomeListScrollListener";

    private volatile boolean isScrolling = false;

    private boolean isAddToList = false;

    private final ScheduledExecutorService executorService = Jarvis.newSingleThreadScheduledExecutor("MSC-" + hashCode());

    private ScheduledFuture<?> future;

    public void triggerScroll() {
        isScrolling = true;

        // 如果已经有一个任务在等待，就取消那个任务
        if (future != null && !future.isDone()) {
            future.cancel(false);
        }

        // 启动新的任务
        future = executorService.schedule(new Runnable() {
            @Override
            public void run() {
                isScrolling = false;
            }
        }, 1, TimeUnit.SECONDS);
    }

    @Override
    public void onScrollStateChanged(RecyclerView recyclerView, int newState) {
        super.onScrollStateChanged(recyclerView, newState);
        MSCLog.d(TAG, "onScrollStateChanged, newState:" + newState);
    }

    @Override
    public void onScrolled(RecyclerView recyclerView, int dx, int dy) {
        super.onScrolled(recyclerView, dx, dy);
        MSCLog.v(TAG, "onScrolled, dx:", dx, " , dy:", dy);
        triggerScroll();
    }

    public boolean isScrolling() {
        return isScrolling;
    }

    public void setAddToList(boolean addToList) {
        isAddToList = addToList;
    }

    public boolean isAddToList() {
        return isAddToList;
    }
}
