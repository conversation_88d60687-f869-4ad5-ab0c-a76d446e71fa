package com.meituan.msc.modules.page.render.webview;

import android.support.annotation.Nullable;
import android.view.View;
import android.webkit.ValueCallback;

import com.meituan.msc.modules.engine.MSCRuntime;
import com.meituan.msc.modules.manager.ExecutorContext;
import com.meituan.msc.modules.manager.IMSCModule;
import com.meituan.msc.modules.page.render.IContentScroller;
import com.meituan.msc.modules.page.render.IRendererLifecycle;

import java.util.List;

/**
 * @api 组件标准化注释_标准API
 * WebView通用接口
 */
public interface IWebView extends IRendererLifecycle, IContentScroller {

    void init(MSCRuntime runtime);

    /**
     * @param script         脚本
     * @param resultCallback 结果回调对象
     * @api 组件标准化注释_标准API
     * 执行js脚本
     */
    void evaluateJavascript(WebViewJavaScript script, @Nullable ValueCallback<String> resultCallback);

    /**
     * @param url 网页地址
     * @api 组件标准化注释_标准API
     * 加载地址
     */
    void loadUrl(String url);

    /**
     * @param baseUrl  基准网址
     * @param data     数据
     * @param mimeType mime类型
     * @param encoding 编码方式
     * @param failUrl  失败地址
     * @api 组件标准化注释_标准API
     * 加载地址数据
     */
    void loadDataWithBaseURL(String baseUrl, String data,
                             String mimeType, String encoding, String failUrl);

    /**
     * @return 获得地址
     * @api 组件标准化注释_标准API
     * 获得地址
     */
    String getUrl();

    long getCreateTimeMillis();

    /**
     * @param object 接口对象
     * @param name   接口名称
     * @api 组件标准化注释_标准API
     * 添加JS接口
     */
    void addJavascriptInterface(Object object, String name);

    /**
     * @return 引擎标签
     * @api 组件标准化注释_标准API
     * 引擎标签
     */
    String tag();

    /**
     * @return WebView实例
     * @api 组件标准化注释_标准API
     * 获取WebView实例
     */
    View getWebView();

    /**
     * @return UA信息
     * @api 组件标准化注释_标准API
     * 获取UA信息
     */
    String getUserAgentString();

    /**
     * @param userAgentString UA字符串
     * @api 组件标准化注释_标准API
     * 设置UA信息
     */
    void setUserAgentString(String userAgentString);

    /**
     * @api 组件标准化注释_标准API
     * 设置页面停止监听
     */
    void setOnPageFinishedListener(OnPageFinishedListener pageFinishedListener);

    /**
     * @param viewId
     * @api 组件标准化注释_标准API
     * 绑定页面id
     * TODO 仅调试时显示用，可尝试去除
     */
    void bindPageId(int viewId);

    /**
     * @param listener
     * @api 组件标准化注释_标准API
     * 设置全屏监听器
     * TODO 暂不确定非WebView是否需要
     */
    void setOnFullScreenListener(OnWebViewFullScreenListener listener);


    void setOnReloadListener(OnReloadListener listener);

    /**
     * @return WebView初始化时间
     * @api 组件标准化注释_标注API
     * 获取WebView初始化时间
     */
    long getWebViewInitializationDuration();

    /**
     * @return WebView初始化时间
     * @api 组件标准化注释_标注API
     * 获取WebView创建时机
     */
    WebViewCacheManager.WebViewCreateScene getWebViewCreateScene();

    /**
     * @return WebView初始化时间
     * @api 组件标准化注释_标注API
     * 设置WebView创建时机
     */
    void setCreateScene(WebViewCacheManager.WebViewCreateScene createScene);

    /**
     * @return WebView初始化时间
     * @api 组件标准化注释_标注API
     * 获取WebView预加载状态
     */
    WebViewFirstPreloadStateManager.PreloadState getPreloadState();

    /**
     * @return WebView初始化时间
     * @api 组件标准化注释_标注API
     * 设置WebView预加载状态
     */
    void setPreloadState(WebViewFirstPreloadStateManager.PreloadState preloadState);

    void setWebViewBackgroundColor(int color);

    /**
     * @return 是否成功
     * @param mscModule mscModule，用于回调方法中invoke
     * @param executorContext invoke运行的线程
     * 创建message channel
     */
    void createMessagePort(IMSCModule mscModule, ExecutorContext executorContext);

    /**
     * 第一条消息，客户端向前端发送 前端收发消息所用的通信端口
     */
    void transferPortToJavaScript();

    /**
     * 客户端使用messagePort向前端发送消息
     */
    void postMessageWithNativeMessagePort(WebViewJavaScript script);

    /**
     * message port可用，在Horn白名单中，而且已完成初始化
     */
    boolean messagePortReady();

    /**
     * 关闭message port
     */
    void messagePortClose();

    /**
     * 获取最新的ConsoleLog的错误信息,如果没有则返回空字符串
     * @return
     */
    String getConsoleLogErrorMessage();

    /**
     * 获取渲染进程崩溃发生的时间列表
     * @return
     */
    List<Long> getRenderProcessGoneTimeList();
}
