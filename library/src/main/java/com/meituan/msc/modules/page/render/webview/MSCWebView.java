
package com.meituan.msc.modules.page.render.webview;

import static com.meituan.msc.common.perf.PerfEventConstant.INIT_WEB_VIEW_RENDER;
import static com.meituan.msc.common.perf.PerfEventConstant.LOAD_HTML_START;

import android.annotation.SuppressLint;
import android.content.Context;
import android.os.Handler;
import android.os.Looper;
import android.support.annotation.Nullable;
import android.view.View;
import android.webkit.ValueCallback;
import android.widget.LinearLayout;
import android.widget.TextView;

import com.meituan.msc.common.config.MSCConfig;
import com.meituan.msc.common.executor.MSCExecutors;
import com.meituan.msc.common.utils.MSCTraceUtil;
import com.meituan.msc.lib.R;
import com.meituan.msc.modules.api.msi.hook.RequestUserAgent;
import com.meituan.msc.modules.apploader.events.AppLoadException;
import com.meituan.msc.modules.engine.MSCHornRollbackConfig;
import com.meituan.msc.modules.engine.MSCRuntime;
import com.meituan.msc.modules.manager.ExecutorContext;
import com.meituan.msc.modules.manager.IMSCModule;
import com.meituan.msc.modules.page.render.IRendererView;
import com.meituan.msc.modules.page.render.RendererType;
import com.meituan.msc.modules.page.render.webview.impl.EmptyWebViewImpl;
import com.meituan.msc.modules.reporter.MSCLoadErrorConstants;
import com.meituan.msc.modules.reporter.MSCLog;
import com.meituan.msc.util.perf.PerfTrace;

import java.util.List;


@SuppressLint("ViewConstructor")
public class MSCWebView extends LinearLayout implements IWebView, IRendererView {

    public enum SourceType {
        PAGE, COMPONENT
    }

    public static final int TYPE_PAGE = 1;
    public static final int TYPE_SERVICE = 2;
    private final String TAG = "MSCWebView@" + Integer.toHexString(hashCode());
    private final MSCRuntime runtime;
    private final String appId;

    private IWebView mWebView;
    private final int type;
    private final WebViewCacheManager mWebViewCacheManager;
    private volatile boolean isDestroy = false;
    private final Handler handler;
    private final String rendererUA;
    private final ExecutorContext executorContext;
    private final IMSCModule mscModule;
    private OnContentScrollChangeListener mListener;
    private OnPageFinishedListener mOnPageFinishedListener;
    private OnEngineInitFailedListener mOnEngineInitFailedListener;
    private String rendererHashCode;

    public MSCWebView(Context context, MSCRuntime runtime, int type, String rendererUA,
                      ExecutorContext executorContext, IMSCModule mscModule) {
        super(context);
        this.handler = new Handler(Looper.getMainLooper());
        this.mWebViewCacheManager = runtime.webViewCacheManager;
        this.runtime = runtime;
        this.appId = runtime.getAppId();
        this.type = type;
        this.rendererUA = rendererUA;
        this.executorContext = executorContext;
        this.mscModule = mscModule;
    }

    public IWebView getIWebView() {
        return getInnerWebView();
    }

    private IWebView getInnerWebView() {
        if (mWebView == null) {
            initWebView();
        }
        return mWebView;
    }

    private void initWebView() {
        PerfTrace.online().begin(INIT_WEB_VIEW_RENDER).report();
        try {
            if (type == TYPE_PAGE) {
                mWebView = mWebViewCacheManager.getWebViewThroughCache(getContext(), runtime, rendererHashCode);
            } else {
                mWebView = mWebViewCacheManager.getSimpleWebView(getContext(), appId, "mmp_service");
            }
            if (mListener != null) {
                mWebView.setOnContentScrollChangeListener(mListener);
            }
            if (mOnPageFinishedListener != null) {
                mWebView.setOnPageFinishedListener(mOnPageFinishedListener);
            }
            addView(mWebView.getWebView(), LayoutParams.MATCH_PARENT, LayoutParams.MATCH_PARENT);
            RequestUserAgent.getInstance().recordRendererUAWhenInit(RendererType.WEBVIEW, mWebView.getUserAgentString() + rendererUA);

            if (!MSCHornRollbackConfig.isRollbackMessagePort()) {
                createMessagePort(mscModule, executorContext);
            }
        } catch (Exception e) {
            MSCLog.e(TAG, e);
            //WebView初始化失败
            TextView text = new TextView(getContext());
            text.setText(R.string.msc_no_webview_install);
            addView(text, LayoutParams.MATCH_PARENT, LayoutParams.MATCH_PARENT);

            if (mOnEngineInitFailedListener != null) {
                mOnEngineInitFailedListener.onEngineInitFailed(
                        new AppLoadException(MSCLoadErrorConstants.ERROR_CODE_WEBVIEW_INIT_FAILED, e == null ? "" : e.getMessage()));
            }
            mWebView = new EmptyWebViewImpl();
        }
        PerfTrace.online().end(INIT_WEB_VIEW_RENDER).report();
    }

    @Override
    public void createMessagePort(IMSCModule mscModule, ExecutorContext executorContext) {
        getIWebView().createMessagePort(mscModule, executorContext);
    }

    @Override
    public void transferPortToJavaScript() {
        getIWebView().transferPortToJavaScript();
    }

    @Override
    public void postMessageWithNativeMessagePort(WebViewJavaScript script) {
        logEvaluateScriptIfNeeded(script.buildWebMessageString(false), "Post_WebMessage");
        getIWebView().postMessageWithNativeMessagePort(script);
    }


    public void evaluateJavascript(final WebViewJavaScript script, final @Nullable ValueCallback<String> resultCallback, WebViewEvaluateJavascriptListener evaluateJavascriptListener) {
        if (isDestroy) {
            logEvaluateScriptIfNeeded(script.buildJavaScriptString(false), "Cancel_EvaluateJavascript");
            return;
        }
        if (MSCTraceUtil.TRACE_LEVEL >= MSCTraceUtil.LEVEL_HEAVY) {
            PerfTrace.instant("evaluateJavascript_start").arg("script", script).arg("size", script.length());
        }
        Runnable runnable = new Runnable() {
            @Override
            public void run() {
                if (evaluateJavascriptListener != null) {
                    evaluateJavascriptListener.onStart();
                }
                ValueCallback<String> localResultCallback = resultCallback;
                if (MSCTraceUtil.TRACE_LEVEL >= MSCTraceUtil.LEVEL_HEAVY) {
                    long startTime = PerfTrace.currentTime();
                    localResultCallback = new ValueCallback<String>() {
                        @Override
                        public void onReceiveValue(String value) {
                            PerfTrace.duration("evaluateJavascript_js", startTime).arg("script", script).arg("size", script.length());
                            if (resultCallback != null) {
                                resultCallback.onReceiveValue(value);
                            }
                        }
                    };
                }
                logEvaluateScriptIfNeeded(script.buildJavaScriptString(false), "Evaluate_Javascript");
                getInnerWebView().evaluateJavascript(script, localResultCallback);
            }
        };
        if (Looper.getMainLooper() == Looper.myLooper()) {
            runnable.run();
        } else {
            handler.post(runnable);
        }
    }

    private void logEvaluateScriptIfNeeded(String script, String logMessage) {
        if (MSCConfig.enableOutputDetailLogForWhiteScreen(appId)) {
            MSCLog.i(TAG, logMessage, script);
        }
    }

    @Override
    public void init(MSCRuntime runtime) {

    }

    @Override
    public void evaluateJavascript(final WebViewJavaScript script, final @Nullable ValueCallback<String> resultCallback) {
        evaluateJavascript(script, resultCallback, null);
    }

    @Override
    public void onCreate() {
        if (mWebView != null) {
            mWebView.onCreate();
        }
    }

    @Override
    public void onDestroy() {
        if (isDestroy) {
            return;
        }
        isDestroy = true;
        handler.removeCallbacksAndMessages(null);
        if (mWebView != null) {
            mWebView.setOnReloadListener(null);
            MSCExecutors.runOnUiThread(new Runnable() {
                @Override
                public void run() {
                    removeView(mWebView.getWebView());
                    getIWebView().messagePortClose();
                    mWebView.onDestroy();
                }
            });
        } else {
            MSCLog.i(TAG, "onDestroy webview is null");
        }
//        MemoryMonitor.onWebViewDestroy(this);
    }

    @Override
    public void loadUrl(String url) {
        if (MSCTraceUtil.TRACE_LEVEL >= MSCTraceUtil.LEVEL_HEAVY) {
            PerfTrace.begin("loadUrl").arg("url", url);
        }
        getInnerWebView().loadUrl(url);
        if (MSCTraceUtil.TRACE_LEVEL >= MSCTraceUtil.LEVEL_HEAVY) {
            PerfTrace.end("loadUrl");
        }
    }

    @Override
    public void loadDataWithBaseURL(String baseUrl, String data, String mimeType, String encoding, String failUrl) {
        if (MSCTraceUtil.TRACE_LEVEL >= MSCTraceUtil.LEVEL_HEAVY) {
            PerfTrace.instant("loadDataWithBaseURL").arg("url", baseUrl).arg("data", data).arg("size", data != null ? data.length() : 0);
        }
        PerfTrace.online().instant(LOAD_HTML_START).arg("file", baseUrl).arg("size", data != null ? data.length() : 0).report();
        getInnerWebView().loadDataWithBaseURL(baseUrl, data, mimeType, encoding, failUrl);
    }

    @Override
    public String getUrl() {
        return getInnerWebView().getUrl();
    }

    @Override
    public long getCreateTimeMillis() {
        return this.mWebView != null ? mWebView.getCreateTimeMillis() : -1;
    }

    @Override
    public void addJavascriptInterface(Object object, String name) {
        getInnerWebView().addJavascriptInterface(object, name);
    }

    public String tag() {
        return "MSCWebView";
    }

    @Override
    public View getWebView() {
        return getInnerWebView().getWebView();
    }

    @Override
    public void requestContentLayout() {
        getInnerWebView().requestContentLayout();
    }

    @Override
    public void scrollContentY(int offset) {
        getInnerWebView().scrollContentY(offset);
    }

    @Override
    public void setOnContentScrollChangeListener(OnContentScrollChangeListener listener) {
        this.mListener = listener;
        if (mWebView != null) {
            mWebView.setOnContentScrollChangeListener(mListener);
        }
    }

    @Override
    public String getUserAgentString() {
        return mWebView.getUserAgentString();
    }

    @Override
    public void setUserAgentString(String userAgentString) {
        mWebView.setUserAgentString(userAgentString);
    }

    @Override
    public int getContentHeight() {
        return mWebView.getContentHeight();
    }

    @Override
    public int getContentScrollY() {
        return mWebView.getContentScrollY();
    }


    @Override
    public void setOnPageFinishedListener(OnPageFinishedListener onPageFinishedListener) {
        this.mOnPageFinishedListener = onPageFinishedListener;
        if (mWebView != null) {
            mWebView.setOnPageFinishedListener(onPageFinishedListener);
        }
    }

    @Override
    public void onShow() {
        getInnerWebView().onShow();
    }

    @Override
    public void onHide() {
        getInnerWebView().onHide();
    }

    public void bindPageId(int viewId) {
        if (mWebView != null)
            mWebView.bindPageId(viewId);
    }

    public MSCWebView setOnEngineInitFailedListener(OnEngineInitFailedListener onEngineInitFailedListener) {
        mOnEngineInitFailedListener = onEngineInitFailedListener;
        return this;
    }

    @Override
    public void setOnFullScreenListener(OnWebViewFullScreenListener listener) {
        mWebView.setOnFullScreenListener(listener);
    }

    @Override
    public void setOnReloadListener(OnReloadListener listener) {
        mWebView.setOnReloadListener(listener);
    }

    @Override
    public View asView() {
        return this;
    }

    @Override
    public RendererType getRendererType() {
        return RendererType.WEBVIEW;
    }

    @Override
    public long getWebViewInitializationDuration() {
        return 0;
    }

    @Nullable
    @Override
    public WebViewCacheManager.WebViewCreateScene getWebViewCreateScene() {
        return mWebView != null ? mWebView.getWebViewCreateScene() : null;
    }

    @Override
    public void setCreateScene(WebViewCacheManager.WebViewCreateScene createScene) {
        mWebView.setCreateScene(createScene);
    }

    @Override
    public WebViewFirstPreloadStateManager.PreloadState getPreloadState() {
        return mWebView != null ? mWebView.getPreloadState() : null;
    }

    @Override
    public void setPreloadState(WebViewFirstPreloadStateManager.PreloadState preloadState) {
        if (mWebView != null) {
            mWebView.setPreloadState(preloadState);
        } else {
            MSCLog.i(TAG, "mWebView is null");
        }
    }

    @Override
    public void setWebViewBackgroundColor(int color) {
        if (mWebView != null) {
            MSCLog.i(TAG, "setWebViewBackgroundColor", color);
            mWebView.setWebViewBackgroundColor(color);
        }
    }

    @Override
    public boolean messagePortReady() {
        return getIWebView().messagePortReady();
    }


    @Override
    public void messagePortClose() {
        getIWebView().messagePortClose();
    }

    @Override
    public String getConsoleLogErrorMessage() {
        return getInnerWebView().getConsoleLogErrorMessage();
    }

    @Override
    public List<Long> getRenderProcessGoneTimeList() {
        return getInnerWebView().getRenderProcessGoneTimeList();
    }

    public void setRendererHashCode(String rendererHashCode) {
        this.rendererHashCode = rendererHashCode;
    }
}
