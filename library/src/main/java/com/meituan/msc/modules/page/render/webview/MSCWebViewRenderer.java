package com.meituan.msc.modules.page.render.webview;

import static com.meituan.msc.modules.page.render.webview.MSCWebView.TYPE_PAGE;

import android.content.Context;
import android.support.annotation.Keep;
import android.support.annotation.NonNull;
import android.support.annotation.Nullable;
import android.support.annotation.UiThread;
import android.support.annotation.VisibleForTesting;
import android.support.annotation.WorkerThread;
import android.text.TextUtils;
import android.view.View;
import android.webkit.JavascriptInterface;
import android.webkit.ValueCallback;

import com.meituan.android.mercury.msc.adaptor.bean.MSCAppMetaInfo;
import com.meituan.dio.easy.DioFile;
import com.meituan.msc.common.config.MSCConfig;
import com.meituan.msc.common.executor.MSCExecutors;
import com.meituan.msc.common.framework.MPListenerManager;
import com.meituan.msc.common.model.RunOnceRunnable;
import com.meituan.msc.common.perf.PerfEventConstant;
import com.meituan.msc.common.utils.CollectionUtil;
import com.meituan.msc.common.utils.FileSizeUtil;
import com.meituan.msc.common.utils.FileUtil;
import com.meituan.msc.common.utils.JsonUtil;
import com.meituan.msc.common.utils.MSCTraceUtil;
import com.meituan.msc.common.utils.PathUtil;
import com.meituan.msc.common.utils.ToastUtils;
import com.meituan.msc.common.utils.WhiteScreenUtil;
import com.meituan.msc.jse.bridge.ICallFunctionContext;
import com.meituan.msc.jse.bridge.LazyParseJSONArray;
import com.meituan.msc.modules.api.ApiException;
import com.meituan.msc.modules.apploader.LaunchInfo;
import com.meituan.msc.modules.apploader.events.AppLoadException;
import com.meituan.msc.modules.container.OpenParams;
import com.meituan.msc.modules.engine.IRendererManager;
import com.meituan.msc.modules.engine.MSCHornRollbackConfig;
import com.meituan.msc.modules.engine.MSCRuntime;
import com.meituan.msc.modules.engine.ResultCallback;
import com.meituan.msc.modules.manager.ExecutorContext;
import com.meituan.msc.modules.manager.JSONDataParser;
import com.meituan.msc.modules.manager.MSCHandler;
import com.meituan.msc.modules.manager.MSCModule;
import com.meituan.msc.modules.manager.MSCRuntimeException;
import com.meituan.msc.modules.page.render.AppPageReporter;
import com.meituan.msc.modules.page.render.IRendererView;
import com.meituan.msc.modules.page.render.MSCHornPerfConfig;
import com.meituan.msc.modules.page.render.RendererType;
import com.meituan.msc.modules.preload.MSCHornPreloadConfig;
import com.meituan.msc.modules.reporter.MSCCommonTagReporter;
import com.meituan.msc.modules.reporter.MSCLoadErrorConstants;
import com.meituan.msc.modules.reporter.MSCLog;
import com.meituan.msc.modules.reporter.prexception.AppPageState;
import com.meituan.msc.modules.reporter.prexception.AppServiceState;
import com.meituan.msc.modules.service.MSCFileUtils;
import com.meituan.msc.modules.update.MSCAppModule;
import com.meituan.msc.modules.update.bean.AppMetaInfoWrapper;
import com.meituan.msc.modules.update.bean.PackageInfoWrapper;
import com.meituan.msc.modules.update.pkg.MSCLoadPackageScene;
import com.meituan.msc.modules.update.pkg.PackageLoadCallback;
import com.meituan.msc.modules.update.pkg.PackageLoadManager;
import com.meituan.msc.util.perf.PerfTrace;

import org.json.JSONArray;
import org.json.JSONException;
import org.json.JSONObject;

import java.io.IOException;
import java.lang.ref.WeakReference;
import java.util.ArrayList;
import java.util.HashMap;
import java.util.HashSet;
import java.util.Iterator;
import java.util.List;
import java.util.Map;
import java.util.Queue;
import java.util.Set;
import java.util.concurrent.ConcurrentLinkedQueue;
import java.util.concurrent.ConcurrentSkipListSet;

/**
 * 小程序Page层逻辑
 * 每个WebViewRenderer对应小程序一个页面（中的一个tab页，如有底tab的话），关联一个WebView
 * 支持通过与前端的消息驱动进行回收、复用，不支持同一WebView直接再次加载页面
 * <p>
 * Created by letty on 2019/11/15.
 **/
public class MSCWebViewRenderer extends BaseWebViewRenderer implements OnEngineInitFailedListener, PerfEventConstant {
    public final String TAG = "MSCWebViewRenderer@" + Integer.toHexString(hashCode());
    public static final String MP_INFO = "if (typeof __mpInfo === 'undefined') {var __mpInfo = {};}; __mpInfo.appId='%s';";

    private boolean isRenderProcessGone;
    private final Object renderCacheLock = new Object();


    @NonNull
    PageData pageData = (PageData) super.pageData;  //TODO 这个转换很尴尬，如何做到子类用子类的data，需要讨论，泛型是一种办法，但繁琐
    private volatile String cacheSinkModeHotZoneData;
    private boolean useOriginCaptureStrategy;
    private Runnable onFirstRenderListener;

    // 容器启动分阶段 补充指标上报维度 https://km.sankuai.com/collabpage/2709561651
    private volatile String pkgInjectState = PKG_INJECT_STATE_DEFAULT;
    public static final String PKG_INJECT_STATE_DEFAULT = "none";
    public static final String PKG_INJECT_STATE_BASE_INJECT_CURR = "base_inject_curr";
    public static final String PKG_INJECT_STATE_BASE_INJECT_ALREADY = "base_inject_already";
    public static final String PKG_INJECT_STATE_BIZ_MAIN_INJECT_CURR = "biz_main_inject_curr";
    public static final String PKG_INJECT_STATE_BIZ_MAIN_INJECT_ALREADY = "biz_main_inject_already";
    public static final String PKG_INJECT_STATE_BIZ_SUB_INJECT_CURR = "biz_sub_inject_curr";
    public static final String PKG_INJECT_STATE_BIZ_SUB_INJECT_ALREADY = "biz_sub_inject_already";
    public long loadPageCostTime;
    public boolean enablePreSendOnPageStart = false;

    public String getPkgInjectState() {
        return pkgInjectState;
    }

    @Nullable
    public String getCacheSinkModeHotZoneData() {
        return cacheSinkModeHotZoneData;
    }

    @Override
    protected PageData createPageData() {
        return new PageData();
    }

    public void onFatalError(JSONObject data) {
        // maybe do sth
        if (mOnEngineInitFailedListener != null) {
            if (MSCHornRollbackConfig.readConfig().rollbackLoadErrorReportFix) {
                mOnEngineInitFailedListener.onEngineInitFailed(new MSCRuntimeException(data.optString("message")));
            } else {
                AppLoadException exception = new AppLoadException(MSCLoadErrorConstants.ERROR_CODE_FATAL_JS_ERROR, data.optString("message"));
                mOnEngineInitFailedListener.onEngineInitFailed(exception);
            }
        }
    }

    public boolean isBasePackageVersionMatched(PackageInfoWrapper basePackage) {
        synchronized (loadedPackages) {
            for (PackageInfoWrapper pkg : loadedPackages) {
                if (pkg == null) {
                    continue;
                }
                if (TextUtils.equals(basePackage.getDDResourceName(), pkg.getDDResourceName())
                        && TextUtils.equals(basePackage.getMd5(), pkg.getMd5())) {
                    MSCLog.i(TAG, "verifyLoadedPackage, loaded package match current version");
                    return true;
                }
            }
        }
        return false;
    }

    public void setOnFirstRenderListener(Runnable runnable) {
        this.onFirstRenderListener = runnable;
    }

    public void setIsWhiteForegroundShow(boolean isShow) {
        pageData.isWhiteForegroundShow = isShow;
    }

    /**
     * 与AppPage里加载的一个具体页面有关，而非与AppPage实例或整个小程序有关，在复用AppPage时应清除
     */
    static class PageData extends BasePageData {
        PackageInfoWrapper mPackageInfo;    //当前页面所在包
        volatile String preloadedRenderCache;
        volatile boolean pathLoaded;          //加载过一个具体页面，不包含使用template进行的包加载
        volatile boolean pageLaunchStarted;   // 标识已被实际启动（而非预加载）占用，此后不能再开始预加载，但可能距离实际实际收到loadPage还有一段时间
        boolean pageLaunched;        //已实际启动，而不仅限于预加载
        volatile boolean isPreloading;        //首页预加载
        boolean isPreloadRes;        //预加载资源
        volatile boolean hasRenderCacheInit;//保证预热线程先对renderCache初始化，再由加载线程读取
        volatile boolean hasPreloaded; //是否已经开始预热
        volatile String compileTimeRenderCache;
        boolean hasDataInit; //页面是否已经初始化
        public LoadEndRecord domEndRecord;
        boolean useRenderCache;                 //当前是否使用了初始渲染缓存
        RenderCacheType renderCacheType;
        volatile boolean hasSendOnPageStart;
//        long onAppRouteTime; // fixme msc 暂时用不到 回头支持performance相关api加上
    }


    private OnEngineInitFailedListener mOnEngineInitFailedListener;
    private String lastStatusEvent; //记录当前所处阶段，用于上报

    private int viewId = View.NO_ID;

    private final List<PackageInfoWrapper> loadedPackages = new ArrayList<>();    //仅包含基础包、主包，子包会由前端发起加载，不记录在此
    private final Set<String> loadedPaths = new ConcurrentSkipListSet<>();        //曾经加载过的页面
    private final Set<String> loadedResPaths = new ConcurrentSkipListSet<>();     //曾经加载过资源的页面，包含加载过的页面，不含query
    private final Set<String> needPreloadPaths = new ConcurrentSkipListSet<>();   // 需要等待PageReady状态才能预加载资源，此前在此保存
    // 存储当前Renderer已加载过的包名，包含主包和子包，用于埋点上报时区分是否已注入过包资源
    private final Set<String> loadedPkgNames = new ConcurrentSkipListSet<>();
    public volatile boolean isBasePackageLoaded;

    private boolean usedSnapshotTemplate;
    private long basePackageFileSize = 0;
    private long mainPackageFileSize = 0;

    @Override
    public RendererType getType() {
        return RendererType.WEBVIEW;
    }

    @Override
    public void init(Context context, MSCRuntime runtime) {
        MSCLog.i(TAG, "init AppPage, viewId:", getViewId());
        super.init(context, runtime);
        injectAutomatorScript();

        MSCExecutors.runOnUiThread(new Runnable() {
            @Override
            public void run() {
                //  如果runtime已经销毁，则直接返回，否则会导致 MSCRuntime 泄露
                if (runtime.isDestroyed) {
                    runtime.getRuntimeReporter().reportWebViewCreateAfterDestroy();
                    return;
                }
                getMSCWebView(); //抛至主线程执行WebView的创建
            }
        });
    }

    @Override
    public IRendererView getRendererView() {
        return getMSCWebView();
    }

    @UiThread
    public MSCWebView getMSCWebView() {
        if (webView == null) {
            mRuntime.getRuntimeReporter().addStatisticsToMap("Pre_WebView_Create");
            MSCLog.i(TAG, "createMSCWebView");
            createMSCWebView();
            mRuntime.getRuntimeReporter().addStatisticsToMap("After_WebView_Create");
            runPendingRunnableWhenWebViewBridgeReady();
        }
        return webView;
    }

    public long getWebViewCreateTimeMillis() {
        return webView != null ? webView.getCreateTimeMillis() : -1;
    }

    private final WebViewModule mWebViewModule = new WebViewModule().setMSCWebViewRenderer(this);
    private WebViewBridge mWebViewBridge;
    private final Queue<Runnable> pendingRunnableForWebViewBridge = new ConcurrentLinkedQueue<>();

    @Override
    public Set<MSCModule> getMSCModules() {
        return CollectionUtil.asSet(mWebViewModule);
    }

    protected void createMSCWebView() {
        long start = PerfTrace.currentTime();
        mWebViewModule.attachedRuntime(mRuntime);
        mWebViewBridge = createWebViewBridge();
        mWebViewModule.setWebViewBridge(mWebViewBridge);
        ExecutorContext executorContext = new ExecutorContext() {
            @Override
            public void invokeCallback(int callbackID, Object arguments) {
                mWebViewBridge.callBack(callbackID, JSONDataParser.getInstance().serialize(arguments));
            }

            @Override
            public MSCHandler acquireAsyncMethodHandler() {
                return new MSCHandler() {
                    @Override
                    public void handle(Runnable runnable) {
                        // UI指令需要有序执行
                        mRuntime.getScheduledExecutorService().submit(runnable);
                    }
                };
            }
        };
        NativeBridge nativeBridge = new NativeBridge() {
            @Override
            @JavascriptInterface
            public String invoke(String module, String method, String args) {
                Object ob;
                if ("onFirstScript".equals(method)) {
                    MSCLog.i(TAG, "onFirstScript");
                } else if ("onPageRecycleFinished".equalsIgnoreCase(method)) {
                    MSCLog.i(TAG, "onPageRecycleFinished");
                }
                //ignore events from last page while recycling
                if (isRecycling()) {
                    if (!("WebView".equals(module) && "onFirstScript".equals(method))) {
                        return JSONDataParser.getJSONArg(String.format("ignore invoke %s method %s for recycling", module, method)).toString();
                    }
                }
                try {
                    ob = mWebViewModule.invoke(module, method, new LazyParseJSONArray(args), executorContext);
                } catch (Exception e) {
                    mRuntime.getNativeExceptionHandler().handleException(e);
                    return JSONDataParser.getJSONArg(e.getMessage()).toString();
                }
                // 大部分方法调用返回值都是null值，这里做个小优化
                return ob == null ? "[]" : JSONDataParser.getJSONArg(ob).toString();
            }
        };
        webView = new MSCWebView(mApplicationContext, mRuntime, TYPE_PAGE, getRendererUA(), executorContext, mWebViewModule);
        webView.setRendererHashCode(Integer.toHexString(hashCode()));
        webView.addJavascriptInterface(nativeBridge, "NativeBridge");
        webView.setOnEngineInitFailedListener(this);
        webView.setOnPageFinishedListener(this);
        final WeakReference<MSCWebViewRenderer> mscWebViewRendererWeakReference = new WeakReference<>(this);
        webView.setOnReloadListener(new OnReloadListener() {
            @Override
            public void onReload(HashMap<String, Object> map) {
                MSCWebViewRenderer webViewRenderer = mscWebViewRendererWeakReference.get();
                if (webViewRenderer != null) {
                    webViewRenderer.reload(map);
                }
            }
        });
        PerfTrace.duration("createMSCWebView", start);
    }

    private WebViewBridge createWebViewBridge() {
        return new WebViewBridge(getPerfEventRecorder()) {
            @Override
            protected void invokeModuleMethod(ICallFunctionContext context, String module, String method, String args, boolean waitFrameWorkReady, @Nullable ValueCallback<String> resultCallback, WebViewEvaluateJavascriptListener evaluateJavascriptListener) {
                WebViewJavaScript script = new WebViewBridgeInvokeWebViewJavaScript(context, module, method, args);
                context.getTrace().instant("evaluateJavascript");
                // if (method.equals("onAppRoute")) {
                //                    MSCLog.i(TAG, "[MSC_LOG]createWebViewBridge invokeModuleMethod", module, method, ContainerController.TIME_FORMAT.format(new Date(System.currentTimeMillis())));
                //                }
                if (waitFrameWorkReady) {
                    MSCWebViewRenderer.this.evaluateJavascriptWhenActive(script, resultCallback, evaluateJavascriptListener);
                } else {
                    MSCWebViewRenderer.this.evaluateJavascript(script, resultCallback, evaluateJavascriptListener);
                }
            }

            @Override
            public void callBack(int callbackID, JSONArray arguments) {
                evaluateJavascript(String.format("javascript:WebViewBridge.callback('%s', %s)", callbackID, arguments), null);
            }

            @Override
            public void evaluateJavascript(String script, @Nullable ValueCallback<String> resultCallback) {
                MSCWebViewRenderer.this.evaluateJavascript(PureWebViewJavaScript.from(script), resultCallback);
            }
        };
    }

    @Override
    public void onCreate() {
        //TODO 还不确定能在这个生命周期做什么，要执行初始化需要参数输入
    }

    public IWebView getIWebView() {
        if (webView == null) {
            return null;
        } else {
            return webView.getIWebView();
        }
    }

    /**
     * 是否已加载具体页面，将导致无法被用于转而加载其他页面
     */
    public boolean isPathLoaded() {
        return pageData.mPagePath != null;
    }

    public void markPageLaunchStarted() {
        pageData.pageLaunchStarted = true;
    }

    // TODO: 2024/11/1 这个方法应该可以删除，不会走到
    @Override
    public void loadPage(String url, long routeTime) {
        super.loadPage(url, routeTime);
        //TODO onAppRoute重构完成后应不需要这个，传url即可
        try {
            OpenParams openParams = new OpenParams.Builder()
                    .setUrl(url)
                    .setOpenType(OpenParams.APP_LAUNCH)
                    .setRouteTime(routeTime)
                    .build(mRuntime);
            loadPage(openParams);
        } catch (ApiException e) {
            MSCLog.e(TAG, e, "loadPage");
            ToastUtils.toast("页面跳转异常");
        }
    }

    private synchronized void setPathLoaded(boolean pathLoaded) {
        pageData.pathLoaded = pathLoaded;
    }

    private synchronized boolean getHasPreloaded() {
        return pageData.hasPreloaded;
    }

    private synchronized void setHasPreloaded(boolean hasPreloaded) {
        pageData.hasPreloaded = hasPreloaded;
    }


    @UiThread
    @Override
    public void onAppRoute(OpenParams openParams) {
        openParams.addExtraParam("webViewType", getMSCWebView().getIWebView().tag());
        WebViewFirstPreloadStateManager.PreloadState preloadState = getMSCWebView().getPreloadState();
        pageData.webViewPreloadState = preloadState == null ? "" : preloadState.toString();
        super.onAppRoute(openParams);
    }

    @UiThread
    public void loadPage(OpenParams openParams) {
        PerfTrace.begin("AppPage.loadPage");
        long start = System.currentTimeMillis();
        loadPageInner(openParams, false, true);
        setHasPreloaded(false);
        long end = System.currentTimeMillis();
        PerfTrace.end("AppPage.loadPage");
        loadPageCostTime = end - start;
    }

    @Override
    @WorkerThread
    public void preloadPage(String url, boolean isLaunch) {
        synchronized (this) {
            if (pageData.pathLoaded) {
                MSCLog.i(TAG, "canceled preloadPage because some page already loaded");
                return;
            }
            if (pageData.pageLaunchStarted) {
                MSCLog.i(TAG, "canceled preloadPage because page is reserved for launch");
                return;
            }
            MSCLog.i(TAG, "preloadPage:", url);
            // 此处openType及url里的参数等是临时的，将在后续真实加载时被覆盖
            pageData.hasPreloaded = true;
        }
        // TODO: 2024/2/26 tianbin 适配setRouteMapping
        OpenParams openParams = new OpenParams(url, OpenParams.APP_LAUNCH);
        PerfTrace.begin("AppPage.preloadPage");
        loadPageInner(openParams, true, isLaunch);
        PerfTrace.end("AppPage.preloadPage");
    }

    /**
     * 启动页面，对应一个具体的页面路径
     * <p>
     * 此时需保证已下载好对应的package
     * 此方法可在主线程/后台线程调用，且内有耗时操作，需注意状态调整应在耗时操作前进行，并在需要处加synchronized
     * <p>
     * 工作线程负责预热，主线程负责加载
     *
     * @param openParams 加载的原生页面地址
     */
    private void loadPageInner(OpenParams openParams, boolean isPreload, boolean isLaunch) {
        super.loadPage(openParams.url, openParams.getRouteTime());

//        if (needReportLaunch()) {
//            pageData.mLaunchReporter.reportDurationFromLaunchStartLocal("loadPage: " + openParams.url + ", " + preloadState, null);
//        }
        setPathLoaded(true);

        pageData.isPreloading = isPreload;
        pageData.mPagePath = openParams.url; //带query
        pageData.openType = openParams.openType;
        String path = PathUtil.getPath(pageData.mPagePath);   //不带query
        loadedPaths.add(path);
        pageData.mPackageInfo = mRuntime.getMSCAppModule().getLoadedPackageInfoByPath(path); //加载内容
        if (isLaunch || !isPreload) {
            String pkgName = pageData.mPackageInfo != null ? pageData.mPackageInfo.getPackageName() : "";
            if (TextUtils.equals(pkgInjectState, PKG_INJECT_STATE_BASE_INJECT_CURR)) {
                pkgInjectState = PKG_INJECT_STATE_BASE_INJECT_ALREADY;
            }
            if (!TextUtils.isEmpty(pkgName)) {
                if (mRuntime.getMSCAppModule().isSubPackagePage(path)) {
                    if (loadedPkgNames.contains(pkgName)) {
                        pkgInjectState = PKG_INJECT_STATE_BIZ_SUB_INJECT_ALREADY;
                    } else {
                        pkgInjectState = PKG_INJECT_STATE_BIZ_SUB_INJECT_CURR;
                    }
                } else {
                    if (loadedPkgNames.contains(pkgName)) {
                        pkgInjectState = PKG_INJECT_STATE_BIZ_MAIN_INJECT_ALREADY;
                    }
                }
                loadedPkgNames.add(pkgName);
            }
        }

        //fixme crash 为啥这里mainPackage会是null
        if (pageData.mPackageInfo == null) {
            throw new MSCRuntimeException("mPackageInfo error" + mRuntime.isDestroyed + mRuntime.getMSCAppModule()
                    + mRuntime.getMSCAppModule().getMetaInfo() + mRuntime.getMSCAppModule().getMetaInfo().mainPackageCached);
        }
        if (!isPreload) {
            addExtraInfoToAllReporter("lastStatusEventWhenLaunch", lastStatusEvent);
        }

        String appId = mRuntime.getAppId();
        MSCLog.i(TAG, "[LaunchInfo] loadPageInner isPreload:", isPreload,
                ", appId:", appId,
                ", mRuntime:", mRuntime.TAG,
                ", openParams.url:", openParams.url,
                ", openParams.openType:", openParams.openType);
        // 非appLaunch场景下，将视图层提前执行
        boolean isEnableOnAppRouteOptimize = MSCHornRollbackConfig.isEnableOnAppRouteOptimize(appId) && !OpenParams.APP_LAUNCH.equals(openParams.openType);
        if (MSCHornRollbackConfig.isRollbackLoadHTMLOptimize() || isEnableOnAppRouteOptimize) {
            loadPackagesWhenLoadPage(isPreload, isLaunch);
        }

        // reload场景把错误的cache给了非首页
        // 预加载页面后使用预加载的场景，仅需要更新参数并向service层发出onAppRoute事件，不需要重新加载page层
        initRenderCache();

        if (!isPreload) {
//            // 线上有少量NPE，做下防护
//            if (pageData.mAppPageListener != null) {
//                pageData.mAppPageListener.onPageReload();
//            }
            setRenderCache(pageData.preloadedRenderCache);

            //onAppRoute需要在renderCache发送之后再调用，否则会出现renderError

            sendPageCache(pageData.preloadedRenderCache);
            pageData.preloadedRenderCache = null;

            PerfTrace.begin("onAppRoute");
            if (isEnableOnAppRouteOptimize && MSCHornRollbackConfig.getPageInAdvancedTime() > 0) {
                // 优化优选的秒开1.0指标，延后发送onAppRoute，让秒开1.0起点与MMP的秒开1.0起点对齐
                final int viewIdBeforePost = getViewId();
                final PageData pageDataBeforePost = this.pageData;
                MSCExecutors.postDelayOnUiThread(() -> {
                    // 这里的判断是为了避免快进快出场景下抛到主线程的Runnable在页面销毁后还在执行的问题
                    if (viewIdBeforePost != getViewId() || pageDataBeforePost != this.pageData) {
                        MSCLog.i(TAG, "page is destroyed, do not send onAppRoute");
                        return;
                    }
                    if (pageData.mEventListener == null) {
                        return;
                    }
                    onAppRoute(openParams);
                }, MSCHornRollbackConfig.getPageInAdvancedTime());
            } else {
                onAppRoute(openParams);
            }
            PerfTrace.end("onAppRoute");
        }

        if (!MSCHornRollbackConfig.isRollbackLoadHTMLOptimize() && !isEnableOnAppRouteOptimize) {
            // 因为WebView的loadBaseUrl方法比较耗时，先执行onAppRoute再加载HTML
            loadPackagesWhenLoadPage(isPreload, isLaunch);
        }

        if (!isPreload) {
            synchronized (pendingRunnableForLaunch) {
                runPendingRunnableWhenLaunch();
                pageData.pageLaunched = true;
            }
            //注入小程序页面信息
            evaluateLaunchStageJavascript(PureWebViewJavaScript.from(String.format(MP_INFO, mRuntime.getAppId(), pageData.mPagePath)), null);

//            pageData.isRenderCacheInit = false;
//            pageData.isDataInit = false;
        }
    }

    private void loadPackagesWhenLoadPage(boolean isPreload, boolean isLaunch) {
        if (!getHasPreloaded() || isPreload) {
            // preload or launch，总之是第一次执行，需要加载html
            // 支持template，可在确定具体启动query之前通过template预先加载包
            loadBasicPackages(new ResultCallback() {
                @Override
                public void onReceiveFailValue(Exception e) {
                    mRuntime.getModule(IRendererManager.class).releaseRenderer(MSCWebViewRenderer.this);
                    if (mOnEngineInitFailedListener != null) {
                        // TODO: 2024/9/9 tianbin 待确认是否要上报到msc.load.error.count
                        mOnEngineInitFailedListener.onEngineInitFailed(new AppLoadException(MSCLoadErrorConstants.ERROR_CODE_FATAL_JS_ERROR,
                                "load basic packages failed " + pageData.mPagePath, e));
                    }
                }

                @Override
                public void onReceiveValue(String value) {
                    MSCLog.i(TAG, "loadBasicPackages onReceiveValue", value);

                    // TODO: 2022/10/17 tianbin 可以提前到基础包注入后 业务包注入前
                    // 执行基础包注入后，调用onPageStart
                    onPageStart(getPagePath());
                }
            }, isLaunch);
        } else { // if (getHasPreloaded() && !isPreload) {
            if (!MSCHornRollbackConfig.isRollbackLoadHTMLOptimize()) {
                // 当前另一个线程正在预热中，此时主线程没有加载HTML，提前注入HTML
                loadHtmlOnMainThreadInAdvanced();
            }
        }
    }

    private void initRenderCache() {
        if (pageData.hasRenderCacheInit) {
            return;
        }
        PerfTrace.begin("initRenderCache");
        synchronized (renderCacheLock) {
            if (pageData.hasRenderCacheInit) {
                return;
            }
            pageData.preloadedRenderCache = RenderCacheHelper.obtainRenderCacheIfNeed(mRuntime.getMSCAppModule(), pageData.mPagePath, getViewId(), pageData.openType);
            if (pageData.preloadedRenderCache != null) {
                MSCExecutors.runOnUiThread(new Runnable() {
                    @Override
                    public void run() {
                        sendPageCache(pageData.preloadedRenderCache);
                    }
                });
            }
            pageData.hasRenderCacheInit = true;
        }
        PerfTrace.end("initRenderCache");
    }

    /**
     * 预加载页面所需文件至前端，但不触发生命周期事件
     * 将在达到onPageReady状态后实际执行，如已晚于此阶段则无法执行，执行后保持在此状态
     * https://km.sankuai.com/page/898455742
     */
    @Override
    public void preloadResource(@Nullable List<String> paths) {
        if (paths == null || paths.isEmpty()) {
            return;
        }
        pageData.isPreloadRes = true;
        for (String path : paths) {
            path = PathUtil.getPath(path);
            needPreloadPaths.add(path);
        }

        for (String path : needPreloadPaths) {
            PackageInfoWrapper packageInfo = mRuntime.getMSCAppModule().createSubPackageWrapper(path);
            if (packageInfo == null) {
                //不是子包，默认在主包
                packageInfo = mRuntime.getMSCAppModule().getMainPackageWrapper();
            }
            if (packageInfo != null) {
                if (!packageInfo.isSourceReady) {
                    // 页面未下载，下载包，进行深度预热
                    MSCLog.i(TAG, "need download subPackage", packageInfo.getPackageName(), " to preload resource:", path);
                    downloadSubPackageAndInjectPage(packageInfo, path);
                } else {
                    tryPreloadResourceNow();
                }
            }
        }
    }

    /**
     * 下载子包
     * 向Fe发送onPagePreload，后续Fe通过客户端资源拦截加载资源
     *
     * @param packageInfo 包信息
     */
    private void downloadSubPackageAndInjectPage(PackageInfoWrapper packageInfo, String path) {
        PackageLoadManager.getInstance().loadPackageWithInfo(mRuntime.getPerfEventRecorder(), packageInfo,
                true, PackageLoadManager.CheckScene.PRELOAD, MSCLoadPackageScene.LOAD_PACKAGE_TYPE_DEEP_BIZ_PRE_LOAD,
                new PackageLoadCallback<PackageInfoWrapper>() {
                    @Override
                    public void onSuccess(@NonNull PackageInfoWrapper data) {
                        MSCLog.i(TAG, "subPackage download success, continue preload resource:", path);
                        if (MSCHornRollbackConfig.enableFixSubPkgPagePreload()) {
                            // 修复拼好饭WebView深度预加载子包页面功能不生效问题 https://km.sankuai.com/collabpage/2703672078
                            MSCAppModule mscAppModule = mRuntime.getMSCAppModule();
                            data.isSourceReady = true;
                            if (mscAppModule != null) {
                                mscAppModule.cachePackageWrapper(data);
                            }
                        }
                        tryPreloadResourceNow();
                        mRuntime.getPerformanceManager().onDownloadPackage(data);
                    }

                    @Override
                    public void onFail(String errMsg, AppLoadException error) {
                        MSCLog.i(TAG, "subPackage download failed, cancel resource preload: ", path);
                    }
                });
    }

    private void tryPreloadResourceNow() {
        try {
            JSONObject data = new JSONObject();
            JSONArray eventData = new JSONArray();
            for (Iterator<String> it = needPreloadPaths.iterator(); it.hasNext(); ) {
                String path = it.next();
                if (loadedResPaths.contains(path)) {
                    it.remove();
                    continue;
                }
                PackageInfoWrapper packageInfo = mRuntime.getMSCAppModule().getLoadedPackageInfoWithoutDefault(path);
                if (packageInfo == null || !packageInfo.isSourceReady) {
                    MSCLog.i(TAG, "package has not be loaded", path);
                    continue;   //包未下载，忽略，应下载好后再试
                }
                JSONObject item = new JSONObject();
                item.put("pagePath", path);
                item.put("packageName", packageInfo.getPackageName());
                eventData.put(item);

                it.remove();
                loadedResPaths.add(path);
                MSCLog.i(TAG, "onPagePreload: ", path);
            }
            if (eventData.length() > 0) {
                data.put("pages", eventData);
                //深度预加载完成，发送onPagePreload事件
                if (mRuntime != null && MSCHornPreloadConfig.enableBizPreloadMultiPage(mRuntime.getAppId())) {
                    final JSONObject finalData = data;
                    runAfterWebViewBridgeReady(new Runnable() {
                        @Override
                        public void run() {
                            PerfTrace.online().instant("webviewSendOnPagePreload").report();
                            WebViewMethods.onPagePreload(mWebViewBridge, finalData);
                        }
                    });
                } else {
                    PerfTrace.online().instant("webviewSendOnPagePreload").report();
                    WebViewMethods.onPagePreload(mWebViewBridge, data);
                }
            }
        } catch (JSONException e) {
            MSCLog.e(e);
        }
    }

    @Override
    public Set<String> getLoadedPaths() {
        return loadedPaths;
    }

    @Override
    @NonNull
    public Set<String> getResourcePaths() {
        Set<String> result = new HashSet<>(loadedResPaths);
        result.addAll(needPreloadPaths);
        return result;
    }

    @Override
    public synchronized void onPageStart(String pagePath) {
        if (pageData.hasSendOnPageStart) {
            return;
        }
        raiseLoadStageTo(LoadStage.PAGE_START_SEND);
        webView.bindPageId(getViewId());

        PackageInfoWrapper packageInfoWrapper = pageData.mPackageInfo;
        if (packageInfoWrapper == null && enablePreSendOnPageStart) {
            packageInfoWrapper = mRuntime.getMSCAppModule().getLoadedPackageInfoByPath(pagePath);
        }
        injectAccountInfo();
        if (TextUtils.isEmpty(pagePath) || packageInfoWrapper == null) {
            MSCLog.i(TAG, "pagePath or packageInfoWrapper is null, cancel onPageStart ");
            return;
        }
        MSCLog.i(TAG, "onPageStart", pagePath, packageInfoWrapper.getPackageName());
        if (MSCHornPerfConfig.getInstance().enableFPMatchFix()) {
            String script = String.format("javascript:window.__ffpWidgetId = '%s';", getViewId());
            evaluateJavascript(PureWebViewJavaScript.from(script));
        }
        WebViewMethods.onPageStart(mWebViewBridge, pagePath, packageInfoWrapper.getPackageName());
        if (enablePreSendOnPageStart) {
            pageData.hasSendOnPageStart = true;
            // 12.37.200提前发送onPageStart，可能有发送完onPageStart后Page未创建就退出页面的场景
            // 该场景不会有recycle事件清理视图层状态，需要先标记并占用该renderer，否则被其他页面再使用时会由于无onPageStart事件导致白屏。但会造成该renderer不可再复用
            pageData.mPagePath = pagePath;
            pageData.mPackageInfo = packageInfoWrapper;
        }
    }

    @Override
    public void onPageFinished(String url, String source) {
        super.onPageFinished(url, source);
//        if (!MSCHornRollbackConfig.isRollbackExceptionMetrixsReport()) {
//            mRuntime.setPageState(AppPageState.READY);
//        }
    }

    /**
     * 同步渲染缓存至视图层，成功发送后视图层可执行至first render
     * <p>
     */
    @UiThread
    private void sendPageCache(String script) {
        if (pageData.hasDataInit) {
            MSCLog.i(TAG, "page data was initialized before");
            return;
        }
        if (TextUtils.isEmpty(script)) {
            MSCLog.i(TAG, "render cache data is empty, cancel sync");
            return;
        }
        PerfTrace.begin("sendPageCache");
        mRuntime.getRuntimeReporter().addStatisticsToMap("Pre_FirstRender_M");
        pageData.useRenderCache = true;
        // 快照场景下，renderCacheType需要上报成renderCacheTemplate
        if (pageData.renderCacheType != RenderCacheType.renderCacheTemplate) {
            pageData.renderCacheType = RenderCacheType.renderCache;
        }
        MSCLog.i(TAG, "use initial data,", FileSizeUtil.formatStringSize(script));
        MPListenerManager.getInstance().launchEventListener.onEvent("native_send_initial_data_to_page");
        getMSCWebView();
        WebViewMethods.onInitialData(mWebViewBridge, script, isPreCreate());
        pageData.hasDataInit = true;
        mRuntime.getRuntimeReporter().addStatisticsToMap("After_FirstRender_M");
        PerfTrace.end("sendPageCache");
    }

    /**
     * 通过拼接template模版的方式加载视图层包内容
     */
    public void loadBasicPackagesByMerge(@Nullable final ResultCallback resultCallback) {
        if (!MSCHornRollbackConfig.isRollbackLoadHTMLOptimize()) {
            if (loadStage.isAtLeast(LoadStage.LOAD_TEMPLATE)) {
                return;
            }
            MSCLog.i(TAG, "loadBasicPackagesByMerge start");
            PerfTrace.online().begin(LOAD_BASIC_PACKAGES_BY_MERGE).report();
            raiseLoadStageTo(LoadStage.LOAD_TEMPLATE);
        } else {
            if (loadStage.isAtLeast(LoadStage.HTML_LOADED)) {
                return;
            }
            MSCLog.i(TAG, "loadBasicPackagesByMerge start");
            PerfTrace.online().begin(LOAD_BASIC_PACKAGES_BY_MERGE).report();
            raiseLoadStageTo(LoadStage.HTML_LOADED);
        }
        setPageState(AppPageState.LOAD_HTML);

        String template = getTemplateWithBasicPackages();
        if (template == null && resultCallback != null) {
            resultCallback.onReceiveFailValue(new RuntimeException("load basic packages failed"));
        }
        MSCExecutors.runOnUiThread(new Runnable() {
            @Override
            public void run() {
                if (!MSCHornRollbackConfig.isRollbackLoadHTMLOptimize()) {
                    if (loadStage.isAtLeast(LoadStage.HTML_LOADED)) {
                        return;
                    }
                    raiseLoadStageTo(LoadStage.HTML_LOADED);
                }
                PerfTrace.online().begin(LOAD_BASIC_PACKAGES_BY_MERGE_MAIN).report();
                MSCLog.i(TAG, "loadBasicPackagesByMerge runOnUiThread");
                getMSCWebView();
                if (!MSCHornRollbackConfig.get().getConfig().rollbackOnPageFinishedInAdvanced) {
                    pageFinished = false;
                }
                webView.loadDataWithBaseURL("file:///__framework/template.html",
                        template, "text/html", "utf-8", null);
                MSCLog.i(TAG, "loadBasicPackagesByMerge, 数据长度: ", template != null ? template.length() : 0);
                if (!pageData.pageLaunchStarted) {
                    webView.onHide();
                }
                if (resultCallback != null) {
                    resultCallback.onReceiveValue("load basic packages successfully");
                }
                setPageState(AppPageState.PACKAGE_INJECT);
                PerfTrace.online().end(LOAD_BASIC_PACKAGES_BY_MERGE_MAIN).report();
                PerfTrace.online().end(LOAD_BASIC_PACKAGES_BY_MERGE).report();
            }
        });
    }

    public String getTemplateWithBasicPackages() {
        PerfTrace.online().begin(GET_TEMPLATE_WITH_BASIC_PACKAGES).report();
        List<String> scripts = new ArrayList<>();
        String basePkgFilePath = "";
        String mainPackageContent = "";
        // add initial scripts that are to be injected into template
        try {
            JSONObject baseSystemInfo = new JSONObject();
            LaunchInfo.getBaseInfo(baseSystemInfo);
            scripts.add("__systemInfo=" + baseSystemInfo.toString());

            if (isPathLoaded()) {
                JSONObject startPageParam = new JSONObject();
                startPageParam.put("pagePath", pageData.mPagePath);
                startPageParam.put("packageName", pageData.mPackageInfo.getPackageName());
                scripts.add("__startPageParam=" + startPageParam.toString());
            }
            scripts.add(String.format(MP_INFO, mRuntime.getAppId(), pageData.mPagePath));
        } catch (JSONException e) {
            MSCLog.e(e);
        }

        //依次将基础包、主包 bootstrap.js同模版合并
        // add bootstrap.js files to template
        boolean isLoadedBasePackage;
        synchronized (loadedPackages) {
            isLoadedBasePackage = loadedPackages.contains(mMSCAppModule.getBasePackage());
        }
        if (!isLoadedBasePackage) {
            mRuntime.getRuntimeReporter().addStatisticsToMap("Pre_PageYXJS_Load_Disk");
            PerfTrace.online().begin(LOAD_BASIC_PACKAGES_BY_MERGE_READ_BASE_PACKAGE).report();
            boolean isBasePackageExist = checkIsPackageExist(mMSCAppModule.getBasePackage());
            PerfTrace.online().end(LOAD_BASIC_PACKAGES_BY_MERGE_READ_BASE_PACKAGE).report();
            mRuntime.getRuntimeReporter().addStatisticsToMap("After_PageYXJS_Load_Disk");
            if (!isBasePackageExist) {
                return null;
            } else {
                basePkgFilePath = MSCAppModule.PREFIX_FRAMEWORK + PackageInfoWrapper.PACKAGE_PAGE_BOOTSTRAP;
                recordPackageInjectSize(mMSCAppModule.getBasePackage());
            }
        }

        PackageInfoWrapper mainPackage = mMSCAppModule.getMainPackageWrapper();
        boolean isLoadedMainPackage;
        synchronized (loadedPackages) {
            isLoadedMainPackage = loadedPackages.contains(mainPackage);
        }
        if (!isLoadedMainPackage) {
            mRuntime.getRuntimeReporter().addStatisticsToMap("Pre_PageYXJS_Load_Disk");
            PerfTrace.online().begin(LOAD_BASIC_PACKAGES_BY_MERGE_READ_MAIN_PACKAGE).report();
            mainPackageContent = readPackageBootStrap(mainPackage);
            PerfTrace.online().end(LOAD_BASIC_PACKAGES_BY_MERGE_READ_MAIN_PACKAGE).report();
            mRuntime.getRuntimeReporter().addStatisticsToMap("After_PageYXJS_Load_Disk");
            if (mainPackageContent == null) {
                return null;
            } else {
                recordPackageInjectSize(mainPackage);
            }
        }

        // 加载空白模板
        MSCLog.i(TAG, "load blank template view@" + getViewId());


        // 拼接模版
        StringBuilder sb = new StringBuilder();
        sb.append(TemplateHelper.TEMPLATE_HTML_PREFIX);
        // 环境信息注入
        for (int i = 0; i < scripts.size(); i++) {
            sb.append("<script type='text/javascript'>\n").append(scripts.get(i)).append("\n</script>\n");
        }
        // 基础库加载
        if (!TextUtils.isEmpty(basePkgFilePath)) {
            sb.append("<script type='text/javascript' crossorigin='anonymous' async src='").append(basePkgFilePath).append("'></script>\n");
        }
        // 业务主包加载
        if (!TextUtils.isEmpty(mainPackageContent)) {
            sb.append("<script type='text/javascript'>\n").append(mainPackageContent).append("\n</script>\n");
        }
        String templateHtmlSuffix = TemplateHelper.TEMPLATE_HTML_SUFFIX;
        if (MSCHornRollbackConfig.readConfig().enableFixWidgetWhiteBackground) {
            if (!TextUtils.isEmpty(pageData.mPagePath) && pageData.mPagePath.startsWith("/widgets/")) {
                templateHtmlSuffix = TemplateHelper.TEMPLATE_HTML_SUFFIX_WIDGET;
            }
        }
        sb.append(templateHtmlSuffix);
        pageData.renderCacheType = RenderCacheType.normal;
        scripts.clear();
        PerfTrace.online().end(GET_TEMPLATE_WITH_BASIC_PACKAGES).report();
        return sb.toString();
    }

    private boolean checkIsPackageExist(final PackageInfoWrapper packageInfo) {
        if (packageInfo == null) {
            return false;
        }
        synchronized (loadedPackages) {
            if (!loadedPackages.contains(packageInfo)) {
                loadedPackages.add(packageInfo);
            }
        }
        DioFile bootStrapFile = packageInfo.getPageBootStrapFile();
        if (bootStrapFile != null && bootStrapFile.exists()) {
            return true;
        }
        return false;
    }


    public String readPackageBootStrap(final PackageInfoWrapper packageInfo) {
        if (packageInfo == null) {
            return null;
        }
        synchronized (loadedPackages) {
            if (!loadedPackages.contains(packageInfo)) {
                loadedPackages.add(packageInfo);
            }
        }
        //getPageBootStrapFile可能返回null
        DioFile bootStrapFile = packageInfo.getPageBootStrapFile();
        if (bootStrapFile != null && bootStrapFile.exists()) {
            String fileContent;
            try {
                if (MSCTraceUtil.TRACE_LEVEL >= MSCTraceUtil.LEVEL_HEAVY) {
                    PerfTrace.begin(READ_PACKAGE_BOOT_STRAP).arg("file", bootStrapFile);
                }
                fileContent = FileUtil.readContent(bootStrapFile);
                if (MSCTraceUtil.TRACE_LEVEL >= MSCTraceUtil.LEVEL_HEAVY) {
                    PerfTrace.end(READ_PACKAGE_BOOT_STRAP);
                }
            } catch (IOException e) {
                //FileUtil.reportReadDioContentFailed(mRuntime.reporter, bootStrapFile.getPath(), e, pageData.mPagePath, mRuntime.getAppId());
                MSCFileUtils.checkMd5AndDeleteIfNeed("loadBootStrapFile", packageInfo);
                MSCLog.e(e);
                return null;
            }
            return fileContent;
        } else {
            MSCLog.e("MSCWebViewRenderer: DioFile is null or not exist");
        }
        return null;
    }


    private String getSnapshotTemplate() {
        PerfTrace.online().begin(GET_SNAPSHOT_TEMPLATE).report();
        MSCLog.i(TAG, "try getSnapshotTemplate()", getViewId());

        MSCAppModule appModule = mRuntime.getMSCAppModule();
        String template = null;
        String snapshotTemplate = TemplateHelper.getSnapshotTemplate(appModule, pageData.mPagePath);
        if (!TextUtils.isEmpty(snapshotTemplate)) {
            // 加载运行时快照
            MSCLog.i(TAG, "load snapshot template view@" + getViewId());
            template = snapshotTemplate;
        }
        // FIXME by chendacai 为了优化一次页面加载过程中多次调用getSnapshotTemplate多次IO的问题，
        //  将usedSnapshotTemplate变量的赋值放到这里。但是这样做不太合理，get方法一般不能修改变量状态。
        //  后续可以考虑通过任务依赖的方式进行Renderer的加载，这样getSnapshotTemplate就能保证只加载一次了，
        //  usedSnapshotTemplate通过做template变量的实时判空即可。
        this.usedSnapshotTemplate = !TextUtils.isEmpty(template);
        PerfTrace.online().end(GET_SNAPSHOT_TEMPLATE).report();
        return template;
    }

    @Override
    public void onUserTapBackToTop() {
        if (mWebViewBridge == null) return;
        WebViewMethods.onUserTapBackToTop(mWebViewBridge);
    }

    @Override
    public void startFFPDetect() {
        if (mWebViewBridge == null) return;
        MSCLog.i(TAG, "startFFPDetect");
        WebViewMethods.startFFPDetect(mWebViewBridge);
    }

    /**
     * 单纯加载基础包
     * From:com.meituan.msc.modules.apploader.launchtasks.WebViewPreloadBaseTask#injectToRender(com.meituan.msc.modules.update.bean.PackageInfoWrapper, com.meituan.msc.common.support.java.util.concurrent.CompletableFuture)
     * 渲染层webview预热后加载基础库
     *
     * @param resultCallback
     */
    @Override
    public void loadWebViewBasePackage(@NonNull ResultCallback resultCallback) {
        MSCLog.i("webviewInjectBase", "preloadBasePackage step6 start view@" + getViewId() + mMSCAppModule);
        PerfTrace.instant("loadWebViewBasePackage");
        // 加载基础包
        loadPackage(mMSCAppModule.getBasePackage(), new ResultCallback() {
            @Override
            public void onReceiveFailValue(Exception throwable) {
                MSCLog.i("webviewInjectBase", "preloadBasePackage step7 inject failed exit", mRuntime);
                resultCallback.onReceiveFailValue(throwable);
            }

            @Override
            public void onReceiveValue(String value) {
                MSCLog.i("webviewInjectBase", "preloadBasePackage step7 inject success.", mRuntime);
                resultCallback.onReceiveValue(value);
                webView.setPreloadState(WebViewFirstPreloadStateManager.PreloadState.WEBVIEW_PREINJECT);
            }
        }, getSnapshotTemplate(), null, false);
    }

    /**
     * 通过evaluatJavascript的方式注入包内容
     * 主包基础包下载完成后使用，依次加载基础包、主包；适合非小程序冷启动时使用
     */
    @Override
    public void loadBasicPackages(@Nullable final ResultCallback resultCallback, boolean isLaunch) {
        injectHornConfig();
        if (mMSCAppModule.hasMetaInfo()) {
            PerfTrace.instant("loadBasicPackages");
            MSCLog.i(TAG, "loadBasicPackages view@" + getViewId() + mMSCAppModule);

            // 注入元信息到视图层
            injectMetaInfoAdvanceBuildConfig();

            String template = getSnapshotTemplate();
            // 如果没有快照模版，只有空白模版的时候，才会走进此分支
            boolean isLoadedPackagesEmpty;
            synchronized (loadedPackages) {
                isLoadedPackagesEmpty = loadedPackages.isEmpty();
            }
            if (isLoadedPackagesEmpty && MSCHornRollbackConfig.enableLoadBasicPackagesByMerge(mRuntime.getAppId()) && template == null) {
                // horn控制是否走合并bootstrap.js到template的方式启动webview
                loadBasicPackagesByMerge(resultCallback);
            } else {
                loadBasicPackagesByInject(resultCallback, template, isLaunch);
            }
        }
    }

    private void injectMetaInfoAdvanceBuildConfig() {
        if (MSCHornRollbackConfig.readConfig().rollbackInjectAdvanceBuildConfig) {
            MSCLog.i(TAG, "injectMetaInfoConfig rollback");
            return;
        }

        AppMetaInfoWrapper metaInfo = mMSCAppModule.getMetaInfo();
        if (metaInfo == null) {
            MSCLog.i(TAG, "injectMetaInfo metaInfo is null");
            return;
        }

        MSCAppMetaInfo.AdvanceBuildConfig advanceBuildConfig = metaInfo.getAdvanceBuildConfig();
        if (advanceBuildConfig == null) {
            MSCLog.i(TAG, "injectMetaInfo advanceBuildConfig is null");
            return;
        }
        // TODO: 2024/9/19 手动拼接字符串 维护成本太高，主要是考虑前端逻辑层和视图层接收相同类型
        String metaInfoConfigStr = "window.APP_METADATA = {advanceBuildConfig:{asyncSubPkg:" + advanceBuildConfig.isAsyncSubPkg()
                + ", onDemandInjection:" + advanceBuildConfig.isOnDemandInjection() + "}};";
        evaluateLaunchStageJavascript(PureWebViewJavaScript.from(metaInfoConfigStr), null);
    }

    private void injectHornConfig() {
        String appId = mRuntime.getMSCAppModule().getAppId();
        String logMessage = "[LaunchInfo]injectHornConfig,  runtime:" + mRuntime.TAG + ", pagePath:" + pageData.mPagePath + ", appId:" + appId;
        LaunchInfo launchInfo = mRuntime.getModule(LaunchInfo.class);
        if (launchInfo != null) {
            MSCLog.i(TAG, logMessage, " hornConfig ", launchInfo.TAG);
            JSONObject hornConfig = launchInfo.getHornConfig(LaunchInfo.FROM_WEBVIEW_INJECT);
            String jsonString = hornConfig.toString();
            String hornConfigStr = "window.__mmpHornConfig = " + jsonString + ";";
            evaluateLaunchStageJavascript(PureWebViewJavaScript.from(hornConfigStr), null);
        } else {
            MSCLog.w(TAG, logMessage, "getModule(LaunchInfo) failed!");
        }
    }

    private void injectAccountInfo() {
        String appId = mRuntime.getMSCAppModule().getAppId();
        String logMessage = "[LaunchInfo]injectAccountInfo,  runtime:" + mRuntime.TAG + ", pagePath:" + pageData.mPagePath + ", appId:" + appId;
        LaunchInfo launchInfo = mRuntime.getModule(LaunchInfo.class);
        if (launchInfo != null) {
            MSCLog.i(TAG, logMessage, " accountInfo ", launchInfo.TAG);
            JSONObject accountInfo = launchInfo.getAccountInfo(LaunchInfo.FROM_WEBVIEW_INJECT);
            String jsonString = accountInfo.toString();
            String accountInfoStr = "window.__mmpAccountInfo = " + jsonString + ";";
            evaluateLaunchStageJavascript(PureWebViewJavaScript.from(accountInfoStr), null);
        } else {
            MSCLog.w(TAG, logMessage, "getModule(LaunchInfo) failed!");
        }
    }

    @Keep
    public static class MetaInfoConfig {
        public MSCAppMetaInfo.AdvanceBuildConfig advanceBuildConfig;

        public MetaInfoConfig(MSCAppMetaInfo.AdvanceBuildConfig advanceBuildConfig) {
            this.advanceBuildConfig = advanceBuildConfig;
        }
    }

    private void loadBasicPackagesByInject(@Nullable ResultCallback resultCallback, String template, boolean isLaunch) {
        PerfTrace.online().begin(LOAD_BASIC_PACKAGES_BY_INJECT).report();
        final ResultCallback wrapperResultCallback = new ResultCallback() {
            @Override
            public void onReceiveFailValue(Exception e) {
                PerfTrace.online().instant(INJECT_PAGE_BOOTSTRAP_END).arg("forceReport", mRuntime != null && mRuntime.isRuntimeBizPreloading()).report();
                MSCLog.e(TAG, e, "Load_Basic_Packages_By_Inject_Failed");
                if (resultCallback != null) {
                    resultCallback.onReceiveFailValue(e);
                }
            }

            @Override
            public void onReceiveValue(String value) {
                PerfTrace.online().instant(INJECT_PAGE_BOOTSTRAP_END).arg("forceReport", mRuntime != null && mRuntime.isRuntimeBizPreloading()).report();
                MSCLog.i(TAG, "Load_Basic_Packages_By_Inject_Success");
                if (resultCallback != null) {
                    resultCallback.onReceiveValue(value);
                }
            }
        };
        final WebViewEvaluateJavascriptListener evaluateJavascriptListener = new WebViewEvaluateJavascriptListener() {
            @Override
            public void onStart() {
                PerfTrace.online().instant(INJECT_PAGE_BOOTSTRAP_START).report();
            }
        };

        //加载基础包
        loadPackage(mMSCAppModule.getBasePackage(), new ResultCallback() {
            @Override
            public void onReceiveFailValue(Exception throwable) {
                if (wrapperResultCallback != null) {
                    wrapperResultCallback.onReceiveFailValue(throwable);
                }
                PerfTrace.online().end(LOAD_BASIC_PACKAGES_BY_INJECT).report();
            }

            @Override
            public void onReceiveValue(String value) {
                PerfTrace.online().instant(LOAD_BASIC_PACKAGES_BY_INJECT_BASE_PACKAGE).arg("forceReport", mRuntime != null && mRuntime.isRuntimeBizPreloading()).report();
                loadPackage(mMSCAppModule.getMainPackageWrapper(), wrapperResultCallback, template, null, isLaunch);
                PerfTrace.online().end(LOAD_BASIC_PACKAGES_BY_INJECT).report();
            }
        }, template, evaluateJavascriptListener, isLaunch);
    }

    private void loadPackage(final PackageInfoWrapper packageInfo, @Nullable final ResultCallback resultCallback,
                             String template, WebViewEvaluateJavascriptListener evaluateJavascriptListener,
                             boolean isLaunch) {
        if (packageInfo == null) {
            MSCLog.e("AppPage#loadPagePackage", "empty package");
            return;
        }
        PerfTrace.instant("loadPackage").arg("packageType", packageInfo.getPkgTypeString());
        loadTemplateIfNeed(template);
        // TODO packageInfo最好实现hashCode和equals用来比较。
        boolean isLoadedPackage;
        synchronized (loadedPackages) {
            isLoadedPackage = loadedPackages.contains(packageInfo);
            if (!isLoadedPackage) {
                loadedPackages.add(packageInfo);
            }
        }

        if (!isLoadedPackage) {
            MSCLog.i("AppPage#loadPagePackage" + " view@" + getViewId(), packageInfo);

            injectPackageContent(packageInfo, new ResultCallback() {
                @Override
                public void onReceiveFailValue(Exception e) {
                    if (resultCallback != null) {
                        resultCallback.onReceiveFailValue(e);
                    }
                    MSCLog.e("AppPage#loadPackageFailed" + " view@" + getViewId(), e);
                    //todo finish Activity
                }

                @Override
                public void onReceiveValue(String value) {
                    if (resultCallback != null) {
                        resultCallback.onReceiveValue(value);
                    }
                    MSCLog.i("AppPage", "loadPackageSuccess view@", getViewId(), packageInfo);
                    setPageState(AppPageState.PACKAGE_INJECT);
                    if (isLaunch || !pageData.isPreloading) {
                        if (packageInfo.isMainPackage()) {
                            pkgInjectState = PKG_INJECT_STATE_BIZ_MAIN_INJECT_CURR;
                        } else if (packageInfo.isBasePackage()) {
                            pkgInjectState = PKG_INJECT_STATE_BASE_INJECT_CURR;
                        }
                    }
                    recordPackageInjectSize(packageInfo);
                    if (packageInfo.isBasePackage()) {
                        isBasePackageLoaded = true;
                    }
                }
            }, evaluateJavascriptListener);
        } else {
            MSCLog.i("AppPage#loadPagePackage already exist" + " view@" + getViewId(), packageInfo);
            if (resultCallback != null) {
                resultCallback.onReceiveValue(null);
            }
        }
    }

    // 记录视图层基础库(page-bootstrap.js + pageframe.js)和业务主包(app-page.js)注入文件大小
    // https://km.sankuai.com/collabpage/2711001673#b-2840fc7377e24da5900cafda6d83b7d6
    public void recordPackageInjectSize(PackageInfoWrapper packageInfo) {
        if (!MSCHornRollbackConfig.readConfig().enableWebViewPkgSizeReport || packageInfo == null) {
            return;
        }
        if (packageInfo.isBasePackage()) {
            DioFile bootStrapFile = packageInfo.getPageBootStrapFile();
            DioFile pageFrameFile = packageInfo.getSourcePackageDirFile(PackageInfoWrapper.PACKAGE_PAGE_FRAME);
            long bootStrapFileSize = bootStrapFile == null ? 0 : bootStrapFile.length();
            long pageFrameFileSize = pageFrameFile == null ? 0 : pageFrameFile.length();
            basePackageFileSize = bootStrapFileSize + pageFrameFileSize;
        }
        if (packageInfo.isMainPackage()) {
            DioFile appPageFile = packageInfo.getSourcePackageDirFile(PackageInfoWrapper.PACKAGE_APP_PAGE);
            mainPackageFileSize = appPageFile == null ? 0 : appPageFile.length();
        }
    }

    public long getBasePackageFileSize() {
        return basePackageFileSize;
    }

    public long getMainPackageFileSize() {
        return mainPackageFileSize;
    }

    public void preloadWebViewBundle() {
        if (!MSCHornRollbackConfig.enableSplitChunks() || !isBasePackageLoaded) {
            return;
        }
        Map<String, Object> params = new HashMap<>();
        String pagePath = getPagePath();
        if (TextUtils.isEmpty(pagePath)) {
            pagePath = mMSCAppModule.getRootPath();
        }
        String packageName = mMSCAppModule.getPackageNameByPath(pagePath);
        params.put("pagePath", pagePath);
        params.put("packageName", packageName);
        JSONObject jsonObject = JsonUtil.parseToJson(params);
        if (!loadStage.isAtLeast(LoadStage.PAGE_START_SEND)) {
            // 发送过onPageStart无需再发送事件，onPageStart也会执行bundle注入
            PerfTrace.online().instant("webviewSendResourcePreload").arg("forceReport", true).report();
            WebViewMethods.onResourcePreload(mWebViewBridge, jsonObject);
        } else {
            MSCLog.i(TAG, "webview callWebViewBundleLoad after onPageStart");
        }
    }

    private Runnable loadHtmlOnMainThreadRunnable;

    /**
     * 允许预热的包结构
     * 加载一下预热的页面
     * <p>
     * WebView会在这个时机实际创建/或者取已经预热好的WebView
     */
    private boolean loadTemplateIfNeed(String tpl) {
        synchronized (this) {
            if (!MSCHornRollbackConfig.isRollbackLoadHTMLOptimize()) {
                if (loadStage.isAtLeast(LoadStage.LOAD_TEMPLATE)) {
                    return true;
                }
                raiseLoadStageTo(LoadStage.LOAD_TEMPLATE);
            } else {
                if (loadStage.isAtLeast(LoadStage.HTML_LOADED)) {
                    return true;
                }
                raiseLoadStageTo(LoadStage.HTML_LOADED);
            }
            setPageState(AppPageState.LOAD_HTML);

            PerfTrace.online().begin(LOAD_TEMPLATE_IF_NEED).report();

            String template = tpl;
            if (template == null) template = getSnapshotTemplate();
            boolean isSnapshotTemplate = !TextUtils.isEmpty(template);
            if (template == null) {
                // 加载空白模板
                MSCLog.i(TAG, "load blank template view@" + getViewId());
                String templateHtmlSuffix = TemplateHelper.TEMPLATE_HTML_SUFFIX;
                if (MSCHornRollbackConfig.readConfig().enableFixWidgetWhiteBackground) {
                    if (!TextUtils.isEmpty(pageData.mPagePath) && pageData.mPagePath.startsWith("/widgets/")) {
                        templateHtmlSuffix = TemplateHelper.TEMPLATE_HTML_SUFFIX_WIDGET;
                    }
                }
                template = TemplateHelper.TEMPLATE_HTML_PREFIX + templateHtmlSuffix;
            }

            final String finalTemplate = template;
            Runnable runnable = new RunOnceRunnable() {
                @Override
                public void runOnce() {
                    PerfTrace.online().instant(LOAD_TEMPLATE_IF_NEED_MAIN).report();
                    loadHtmlOnMainThread(finalTemplate, isSnapshotTemplate);
                    loadHtmlOnMainThreadRunnable = null;
                    PerfTrace.online().end(LOAD_TEMPLATE_IF_NEED).report();
                }
            };
            loadHtmlOnMainThreadRunnable = runnable;

            String appId = mRuntime == null ? "" : mRuntime.getAppId();
            if (MSCHornRollbackConfig.isEnableAdvanceLoadHtml(appId)) {
                MSCExecutors.runOnUiThreadFrontOfQueue(runnable);
            } else {
                MSCExecutors.runOnUiThread(runnable);
            }

        }
        return true;
    }

    private void loadHtmlOnMainThreadInAdvanced() {
        PerfTrace.begin("loadHtmlOnMainThreadInAdvanced");
        if (loadHtmlOnMainThreadRunnable != null && MSCExecutors.isMainThread()) {
            MSCLog.i(TAG, "loadHtmlOnMainThreadInAdvanced");
            loadHtmlOnMainThreadRunnable.run();
        }
        PerfTrace.end("loadHtmlOnMainThreadInAdvanced");
    }

    private void loadHtmlOnMainThread(String template, boolean isSnapshotTemplate) {
        if (!MSCHornRollbackConfig.isRollbackLoadHTMLOptimize()) {
            if (loadStage.isAtLeast(LoadStage.HTML_LOADED)) {
                return;
            }
            raiseLoadStageTo(LoadStage.HTML_LOADED);
        }

        PerfTrace.begin("loadHtmlOnMainThread").arg("pageId", viewId);
        MSCLog.i(TAG, "loadTemplateIfNeed runOnUiThread");
        getMSCWebView();
        if (!MSCHornRollbackConfig.get().getConfig().rollbackOnPageFinishedInAdvanced) {
            pageFinished = false;
        }
        if (isSnapshotTemplate) {
            pageData.renderCacheType = RenderCacheType.renderCacheTemplate;
        } else if (pageData.renderCacheType != RenderCacheType.renderCache) {
            pageData.renderCacheType = RenderCacheType.normal;
        }
        webView.loadDataWithBaseURL("file:///__framework/template.html",
                template, "text/html", "utf-8", null);
        String appId = mRuntime == null ? "" : mRuntime.getAppId();
        if (!MSCHornRollbackConfig.isEnableRemoveUnusedJSCode(appId)) {
            try {
                // 注入的数据会在webView加载模板时被清除，需要在此之后
                // 前端会在加载包过程中用到，需要在此之前
                JSONObject baseSystemInfo = new JSONObject();
                LaunchInfo.getBaseInfo(baseSystemInfo);
                webView.evaluateJavascript(PureWebViewJavaScript.from("__systemInfo=" + baseSystemInfo), null);
            } catch (JSONException e) {
                MSCLog.e(e);
            }
        }
        if (!pageData.pageLaunchStarted) {
            PerfTrace.begin("webView.onHide");
            webView.onHide();
            PerfTrace.end("webView.onHide");
        }
        PerfTrace.end("loadHtmlOnMainThread");
    }

    public void onShow() {
        super.onShow();
        pageData.isShow = true;
        getMSCWebView().onShow();
        evaluatePendingEventsForOnFirstScript();
    }

    @Override
    protected String getConsoleLogErrorMessage() {
        return webView.getConsoleLogErrorMessage();
    }

    @Override
    protected List<Long> getRenderProcessGoneTimeList() {
        return webView.getRenderProcessGoneTimeList();
    }

    @Override
    protected void checkWebViewBlock() {
        MSCLog.i(TAG, "WebView_Block_Check_Begin");
        webView.evaluateJavascript(PureWebViewJavaScript.from("Date.now()"), new ResultCallback() {
            @Override
            public void onReceiveFailValue(Exception e) {
                MSCLog.e(TAG, e, "WebView_Block_Check_Error");
            }

            @Override
            public void onReceiveValue(String value) {
                MSCLog.i(TAG, "WebView_Block_Check_Success", value);
            }
        });
        MSCLog.i(TAG, "WebView_Block_Check_End");
    }

    @Override
    protected String getLastStatusEvent() {
        return loadStage == null ? "" : loadStage.toString();
    }

    @Override
    public boolean useRenderCache() {
        return pageData.useRenderCache;
    }

    @Override
    public RenderCacheType getRenderCacheType() {
        return pageData.renderCacheType;
    }

    @Override
    protected boolean useOriginCaptureStrategy() {
        // WebView渲染模式下，MSC白屏数据高于MMP，支持回滚开关控制截屏方案对齐MMP
        return useOriginCaptureStrategy;
    }

    public void onHide() {
        super.onHide();
        pageData.isShow = false;
        getMSCWebView().onHide();
    }

    protected boolean isActiveAndCanEvaluateJavascript() {
        boolean isActiveAndCanEvaluateJavascript = pageData.isShow || isPreloadRunning() || recycling;
        if (MSCConfig.enableSendMsgBeforeFirstRender()) {
            //firstRender前允许发送事件给前端；修复页面在启动过程中，被登陆页等情况盖住，不进行启动流程行为，导致启动埋点不准问题
            return isActiveAndCanEvaluateJavascript || !pageData.hasFirstRender;
        }
        return isActiveAndCanEvaluateJavascript;
    }

    /**
     * 平时存在对视图层在小程序进入后台之后活动的限制，对预加载场景，使用渲染缓存时需要允许事件交互，在firstRender前需要放开限制
     */
    private boolean isPreloadRunning() {
        return (pageData.isPreloading || pageData.isPreloadRes) && !pageData.hasFirstRender;
    }

    /**
     * 存储初始渲染缓存
     */
    void saveInitialData(String params) {
        pageData.hasDataInit = true;
        // Service层发送首次渲染数据给Page层，保存作为静态渲染缓存，下次使用
        RenderCacheHelper.saveStaticRenderCache(mMSCAppModule, pageData.mPagePath, params);
    }

    private boolean needReportLaunch() {
        return pageData.containerReporter != null;
    }

    private void addExtraInfoToAllReporter(String key, Object value) {
        if (needReportLaunch()) {
            pageData.containerReporter.commonTag(key, value);
        }
        // TODO: 2022/10/13 tianbin 为啥会为null
        if (pageData.appPageReporter != null) {
            pageData.appPageReporter.commonTag(key, value);
        }
    }

    public void onSinkModeHotZone(String params) {
        if (pageData.mAppPageListener != null) {
            pageData.mAppPageListener.onSinkModeHotZone(params);
        } else {
            cacheSinkModeHotZoneData = params;
        }
    }

    private final Queue<Runnable> pendingRunnableForLaunch = new ConcurrentLinkedQueue<>();

    @Override
    protected void onFirstRender(HashMap<String, Object> paramsMap) {
        runAfterLaunch(new Runnable() {
            @Override
            public void run() {
                MSCWebViewRenderer.super.onFirstRender(paramsMap);
                if (onFirstRenderListener != null) {
                    onFirstRenderListener.run();
                }
            }
        });
    }


    /**
     * 预加载至首页且有渲染缓存时，可以在预加载阶段执行至first render，此时为保证启动流程的打点简单且一致，将部分first render触发的事件重新定义至启动后再执行
     * HeraActivity中的关键事件mmp.launch.full.first.render也有此要求，但已被CachedOnEventListener统一拦截，此处无需处理
     */
    public void runAfterLaunch(Runnable runnable) {
        synchronized (pendingRunnableForLaunch) {
            if (pageData.pageLaunched) {
                runnable.run();
            } else {
                pendingRunnableForLaunch.add(runnable);
            }
        }
    }

    private void runPendingRunnableWhenLaunch() {
        while (true) {
            Runnable runnable = pendingRunnableForLaunch.poll();
            if (runnable == null) {
                break;
            }
            runnable.run();
        }
    }

    private void recordLastStatusEvent(String event) {
        lastStatusEvent = event;
        addExtraInfoToAllReporter("lastStatusEvent", lastStatusEvent);
    }


    public int getViewId() {
        if (viewId != View.NO_ID) return viewId;
        return hashCode() + recycleCount;   // 使用recycleCount错开复用前后的ViewId
    }

    /**
     * 清除前端运行时状态，准备复用WebView
     * 重置状态至FIRST_SCRIPT，并保留已加载的文件，执行时要求当前状态可控，为FirstRender之后
     *
     * @return 是否可复用
     */
    @Override
    public boolean recycle() {
        // 前端达到FIRST_SCRIPT状态后允许复用
        if (!loadStage.isAtLeast(LoadStage.FIRST_SCRIPT)) {
            MSCLog.w(TAG, "cannot recycle AppPage in state " + loadStage);
            return false;
        }
        enablePreSendOnPageStart = false;
        int oldViewId = getViewId();
        MSCLog.i(TAG, "recycle AppPage that was @" + oldViewId + ", " + pageData.mPagePath);
        webView.evaluateJavascript(PureWebViewJavaScript.from("__startPageParam=undefined"),
                null);
        webView.onHide();
        webView.setOnReloadListener(null);

        pageData = (PageData) resetData();
        pageData.appPageReporter = AppPageReporter.create(mRuntime, mContainerDelegate, this,
                null, null, false, null, null);
        viewId = View.NO_ID;
        cacheSinkModeHotZoneData = null;
        super.recycle();

        setWebViewRecycleRecycleCmdStart();
        WebViewMethods.onPageRecycle(mWebViewBridge);
        recycling = false;
        loadStage = LoadStage.FIRST_SCRIPT;
        tryPreloadResourceNow();
        // hasRecycle标记是否复用，执行完recycle()会被放入复用池；recycling设为false不清楚原因
        hasRecycle = true;

        // webview回收时，清空之前保存的数据
        basePackageFileSize = 0;
        mainPackageFileSize = 0;

        MSCLog.i(TAG, "AppPage recycled, @" + oldViewId + " -> @" + getViewId());
        return true;
    }

    /**
     * 判断是否使用渲染缓存而不回收复用场景
     *
     * @return
     */
    public boolean isUseRenderCacheAndNotRecycle() {
        return !hasRecycle && useRenderCache();
    }

    @Override
    public void onDestroy() {
        super.onDestroy();
        if (webView != null) {
            MSCLog.i(TAG, "onDestroy");
            webView.onDestroy();
        } else {
            MSCLog.i(TAG, "onDestroy webview is null");
        }
    }

    private void setWebViewRecycleRecycleCmdStart() {
        // webview回收中，还没执行完。
        setWebViewRecycleCmdFinished(false);
    }

    private void injectStartTime() {
        mainHandler.post(new Runnable() {
            @Override
            public void run() {
                evaluateJavascriptWhenActive(PureWebViewJavaScript.from(String.format("__appLaunchStartTime = %s;__pageNavigationStartTime = %s;",
                        (pageData.containerReporter != null ? pageData.containerReporter.getLaunchStartTimeCurrentTimeMillis() : "undefined"),
                        (pageData.appPageReporter != null ? pageData.appPageReporter.getPageStartTime() : "undefined")
                        // 提交给前端的应该是System.currentTimeMillis(),不能是开机时间
                )), null, null);
            }
        });
    }

//    public void checkWhiteScreen(View detectView, boolean isVisible, boolean isInnerWebview, String innerUrl, HashMap<String, Object> renderProcessGoneInfo) {
//        HashMap<String, Object> hashMap = new HashMap<>();
//        hashMap.put("firstRender", pageData.hasFirstRender);
//        hashMap.put("isWebView", isInnerWebview);
//        hashMap.put("innerUrl", innerUrl);
//        hashMap.put("isVisible", isVisible);
////        hashMap.put("engineType", isFluent ? "fluent" : "webview");
//        hashMap.put("lastStatusEvent", lastStatusEvent);
//
//        if (renderProcessGoneInfo != null) {
//            hashMap.put("isRendererGoneReload", true);
//            hashMap.putAll(renderProcessGoneInfo);
//        }
//        if (!pageData.hasFirstRender) {
//            hashMap.put("pageStack", mRuntime.jsErrorRecorder.getPageStack());
//            hashMap.put("pageNavigation", mRuntime.jsErrorRecorder.getPageNavigationHistory());
//            hashMap.put("jsErrors", mRuntime.jsErrorRecorder.getJSErrors());
//
//            WhiteScreenReporter.create(mRuntime).reportWhiteScreen(false, 1);
//            // todo mmp 旧埋点 白屏上报需要迁移
////            pageData.mPageReporter.reportDurationEnd(ReporterFields.REPORT_STABILITY_WHITE_SCREEN_USER_PERSPECTIVE, hashMap);
////            pageData.mPageReporter.reportDurationFromLaunchStart(ReporterFields.REPORT_STABILITY_WHITE_SCREEN_FIRST_RENDER, hashMap);
//        } else {
//            if (isVisible) {
//                //切换页面了就不检测了 白屏检测耗时很长
//                if (MSCConfig.isNeedCheckWhiteScreen(PathUtil.getPath(pageData.mPagePath))) {
//                    // todo mmp 旧埋点
////                    pageData.mPageReporter.recordDurationStart(ReporterFields.REPORT_STABILITY_WHITE_SCREEN_SCREENSHOT_COST);
////                   由于pageWrapper里面会有titleBar 此处检查里面的webView是否白屏
////                    (PageViewWrapper)detectView).heraWebView
//                    boolean isWhiteScreen = WhiteScreenUtil.isWhiteScreenView(isInnerWebview ? detectView : webView, false);
//                    if (isWhiteScreen) {
//                        hashMap.put("pageStack", mRuntime.jsErrorRecorder.getPageStack());
//                        hashMap.put("pageNavigation", mRuntime.jsErrorRecorder.getPageNavigationHistory());
//                        hashMap.put("jsErrors", mRuntime.jsErrorRecorder.getJSErrors());
//                        // todo mmp 旧埋点
////                        pageData.mPageReporter.reportDurationEnd(ReporterFields.REPORT_STABILITY_WHITE_SCREEN_USER_PERSPECTIVE, hashMap);
//                    }
//                    hashMap.put("isWhiteScreen", isWhiteScreen);
//                    WhiteScreenReporter.create(mRuntime).reportWhiteScreen(true, isWhiteScreen ? 1 : 0);
//                    // todo mmp 旧埋点
////                    pageData.mPageReporter.reportDurationEnd(ReporterFields.REPORT_STABILITY_WHITE_SCREEN_SCREENSHOT_COST, hashMap);
//                }
//            }
//        }
//    }


    @Override
    public boolean isWhiteScreen(boolean isInnerWebview, View detectView, boolean hasFirstRender, boolean isStartPageAdvanced) {
        String appId = mRuntime == null ? "" : mRuntime.getAppId();
        if (MSCConfig.enableCallWebViewOnCheckWhiteScreen(appId, getBasePkgVersion())) {
            WebViewMethods.onCheckWhiteScreen(mWebViewBridge);
        }
        this.useOriginCaptureStrategy = MSCConfig.useOriginCaptureStrategyAtWebViewWhiteScreenCheck();
        return WhiteScreenUtil.isWhiteScreenView(isInnerWebview ? detectView : webView, false, this.useOriginCaptureStrategy, appId, isStartPageAdvanced);
    }

    private String getBasePkgVersion() {
        if (mRuntime == null || mRuntime.getMSCAppModule() == null) {
            return null;
        }
        return mRuntime.getMSCAppModule().getBasePkgVersion();
    }

    public void recordWhiteScreenUserPerspectiveDurationStart() {
// todo mmp 旧埋点
//        pageData.mPageReporter.recordDurationStart(ReporterFields.REPORT_STABILITY_WHITE_SCREEN_USER_PERSPECTIVE);
    }

    @Override
    protected void reportPageStart() {
        super.reportPageStart();
        injectStartTime();
    }

    protected void reportPageCancel() {
        recordLastStatusEvent(STATUS_CANCEL);
        reportDomEnd(STATUS_CANCEL, null);
        super.reportPageCancel();
    }

    private MSCCommonTagReporter getLaunchReporterIfFirstPage() {
        return pageData.containerReporter == null ? pageData.appPageReporter : pageData.containerReporter;
    }

    /**
     * 仅记录，不立即上报，预加载时需要等页面确定启动才能上报
     */
    void cacheDomEnd(String msg, HashMap<String, Object> params) {
        if (pageData.domEndRecord == null) {
            LoadEndRecord record = new LoadEndRecord();
            record.state = msg;
            record.params = params;
            pageData.domEndRecord = record;
        }
    }

    void reportDomEnd(String msg, HashMap<String, Object> param) {
        if (pageData.domEndRecord == null) {    // 仅在本次能作为结果时触发上报
            cachePageEnd(msg, param);
            reportCachedPageEnd();
        }
    }

    void reportCachedDomEnd() {
        // if (pageData.domEndRecord != null) {
        //     if (pageData.isDomLoadReported || !pageData.isPageStartReported) {
        //         return;
        //     }
        //     pageData.isDomLoadReported = true;
        //     getLaunchReporterIfFirstPage().reportDurationFromLaunchStart("msc.page.load.end",
        //             HashMapHelper.merge(HashMapHelper.of("load.status", pageData.domEndRecord.state), pageData.domEndRecord.params));
        // }
    }

    public MSCWebViewRenderer setOnEngineInitFailedListener(OnEngineInitFailedListener onEngineInitFailedListener) {
        mOnEngineInitFailedListener = onEngineInitFailedListener;
        return this;
    }

    @Override
    public void onEngineInitFailed(Exception e) {
        if (mOnEngineInitFailedListener != null) {
            mOnEngineInitFailedListener.onEngineInitFailed(e);
        }
    }


//    /**
//     * 获取navigation数据
//     * entryType=appLaunch 启动数据
//     * entryType=route 前端调用路由Api导航页面数据
//     * 各字段说明https://km.sankuai.com/page/976955294
//     */
//    private JSONObject createNavigationPerformanceData(long endTime) {
//        long startTime = pageData.containerReporter != null
//                ? pageData.containerReporter.getLaunchStartTimeCurrentTimeMillis()
//                : pageData.appPageReporter.getLaunchStartTimeCurrentTimeMillis();
//        PerformanceData performanceData = new PerformanceData()
//                .addEntryType(PerformanceData.ENTRY_TYPE_NAVIGATION)
//                .addName(pageData.containerReporter != null ? "appLaunch" : "route")
//                .addNavigationType(pageData.openType)
//                .addPagePath(pageData.mPagePath)
//                .addStartTime(startTime)
//                .addEndTime(endTime);
//        if (pageData.containerReporter == null) {
//            performanceData.addNavigationStartTime(pageData.onAppRouteTime);
//        }
//        return performanceData;
//    }
//
//    /**
//     * 获取render数据
//     */
//    private JSONObject createRenderPerformanceData(long endTime) {
//        return new PerformanceData()
//                .addEntryType(PerformanceData.ENTRY_TYPE_RENDER)
//                .addName("firstRender")
//                .addPagePath(pageData.mPagePath)
//                .addStartTime(pageData.onAppRouteTime)
//                .addEndTime(endTime);
//    }

//    /**
//     * 通知前端小程序性能相关的信息
//     * https://developers.weixin.qq.com/miniprogram/dev/api/open-api/performance/wx.getPerformance.html
//     */
//    private void onPerformanceDataChange() {
//        long endTime = System.currentTimeMillis();
//        JSONArray jsonArray = new JSONArray();
//        jsonArray.put(createNavigationPerformanceData(endTime));
//        jsonArray.put(createRenderPerformanceData(endTime));
//        JSONObject jsonObject = new JSONObject();
//        try {
//            jsonObject.put("list", jsonArray);
//            onPageEvent("onPerformanceDataChange", jsonObject.toString(), viewId);
//        } catch (JSONException e) {
//            e.printStackTrace();
//        }
//    }

    public boolean isMainPackageLoaded() {
        synchronized (loadedPackages) {
            for (PackageInfoWrapper pkg : loadedPackages) {
                if (pkg == null) {
                    continue;
                }
                if (pkg.isMainPackage()) {
                    MSCLog.i(TAG, "isMainPackageLoaded  true");
                    return true;
                }
            }
        }
        return false;
    }

    private void injectAutomatorScript() {
    }

    @Override
    public boolean isUsedSnapshotTemplate() {
        return usedSnapshotTemplate;
    }


    @Override
    public String toString() {
        return "MSCWebViewRenderer{@" + Integer.toHexString(hashCode()) + ", appId: " + mMSCAppModule.getAppId() + ", path: " + getPagePath() + "}";
    }

    @Override
    public void setOnReloadListener(OnReloadListener onReloadListener) {
        super.setOnReloadListener(onReloadListener);
        // 回滚
        if (getIWebView() != null) {
            MSCExecutors.postOnUiThread(new Runnable() {
                @Override
                public void run() {
                    getIWebView().setOnReloadListener(onReloadListener);
                }
            });
        } else {
            MSCLog.i(TAG, "getIWebView() is null, setOnReloadListener failed");
        }
    }

    @VisibleForTesting
    public void setRuntime(MSCRuntime runtime) {
        this.mRuntime = runtime;
    }

    @VisibleForTesting
    public void setWebView(MSCWebView webView) {
        this.webView = webView;
    }

    public PackageInfoWrapper getPackageInfo() {
        return pageData.mPackageInfo;
    }

    public List<PackageInfoWrapper> getLoadedPackages() {
        return loadedPackages;
    }

    /**
     * 设置渲染层状态
     */
    public void setPageState(@AppPageState.State String state) {
        MSCLog.i(TAG, "setPageState state:", state);
        pageData.pageState = state;
    }

    /**
     * 设置逻辑层状态
     */
    public void setServiceState(@AppServiceState.State String state) {
        MSCLog.i(TAG, "setServiceState state:", state);
        pageData.serviceState = state;
    }

    /**
     * 在WebViewBridge初始化完成后执行任务
     * 如果WebViewBridge已经初始化，则立即执行；否则加入待执行队列
     */
    public void runAfterWebViewBridgeReady(Runnable runnable) {
        synchronized (pendingRunnableForWebViewBridge) {
            if (mWebViewBridge != null) {
                runnable.run();
            } else {
                pendingRunnableForWebViewBridge.add(runnable);
            }
        }
    }

    private void runPendingRunnableWhenWebViewBridgeReady() {
        MSCLog.i(TAG, "runPendingRunnableWhenWebViewBridgeReady");
        if (mRuntime == null || !MSCHornPreloadConfig.enableBizPreloadMultiPage(mRuntime.getAppId())) {
            return;
        }
        synchronized (pendingRunnableForWebViewBridge) {
            while (!pendingRunnableForWebViewBridge.isEmpty()) {
                Runnable runnable = pendingRunnableForWebViewBridge.poll();
                if (runnable == null) {
                    continue;
                }
                runnable.run();
            }
        }
    }
}
