package com.meituan.msc.modules.page.render.webview;

/**
 * @api
 * 组件标准化注释_标准API
 * WebView 滑动接口
 * Created by bunnyblue on 4/19/18.
 */
public interface OnContentScrollChangeListener {
    /**
     * This is called in response to an internal scroll in this view (i.e., the
     * view scrolled its own contents). This is typically as a result of
     * {@link #scrollBy(int, int)} or {@link #scrollTo(int, int)} having been
     * called.
     *
     * @param l Current horizontal scroll origin.
     * @param t Current vertical scroll origin.
     * @param oldl Previous horizontal scroll origin.
     * @param oldt Previous vertical scroll origin.
     */
    void onWebScrollChange(int l, int t, int oldl, int oldt);
}
