package com.meituan.msc.modules.page.render.webview;

import android.view.View;

/**
 * @api
 * 组件标准化注释_标准API
 * WebView全屏通用接口
 */
public interface OnWebViewFullScreenListener {

    /**
     * @api
     * 组件标准化注释_标准API
     * @param view
     * @param callback
     * 视频播放全屏
     */
    void showCustomView(View view, WebChromeClientCustomViewCallback callback);

    /**
     * @api
     * 组件标准化注释_标准API
     * 隐藏视频全屏
     */
    void hideCustomView();
}
