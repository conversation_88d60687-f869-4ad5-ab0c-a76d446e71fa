package com.meituan.msc.modules.page.render.webview;

import android.app.Activity;
import android.app.Application;
import android.content.Context;
import android.os.Build;
import android.os.Bundle;
import android.os.Handler;
import android.os.Looper;
import android.os.MessageQueue;
import android.os.SystemClock;
import android.support.annotation.NonNull;
import android.support.annotation.Nullable;

import com.meituan.android.common.metricx.PreloadInjection;
import com.meituan.android.degrade.interfaces.resource.ExecuteResultCallback;
import com.meituan.android.degrade.interfaces.resource.PreloadBlock;
import com.meituan.android.degrade.interfaces.resource.ResourceManager;
import com.meituan.msc.common.executor.MSCExecutors;
import com.meituan.msc.common.lib.multiplex.MMPPreloadProxy;
import com.meituan.msc.common.lib.multiplex.WebViewCache;
import com.meituan.msc.common.utils.AppWorkloadUtil;
import com.meituan.msc.extern.MSCEnvHelper;
import com.meituan.msc.modules.apploader.IAppLoader;
import com.meituan.msc.modules.engine.MSCRuntime;
import com.meituan.msc.modules.engine.RuntimeManager;
import com.meituan.msc.modules.preload.MMPStrategyUtil;
import com.meituan.msc.modules.preload.MSCHornPreloadConfig;
import com.meituan.msc.modules.reporter.MSCLog;
import com.meituan.mtwebkit.MTWebView;
import com.meituan.mtwebkit.internal.optim.StartChromiumStepByStep;
import com.sankuai.android.jarvis.Jarvis;

import org.json.JSONObject;

import java.util.HashMap;
import java.util.Map;

public class PreloadWebViewManager {

    private static final String TAG = "PreloadWebViewManager";
    /**
     * 预热WebView默认标识，自研内核通过此常量
     */
    public static final String PRELOAD_WEBVIEW = "preload_webview";
    private static volatile PreloadWebViewManager mInstance;

    public static PreloadWebViewManager getInstance() {
        if (mInstance == null) {
            synchronized (PreloadWebViewManager.class) {
                if (mInstance == null) {
                    mInstance = new PreloadWebViewManager();
                }
            }
        }
        return mInstance;
    }

    private boolean isKNBInit;

    //该数值用于两个用途，
    //其一为上报首次idleHandler回调耗时，用于判断当前WebView初始化是否合理，
    //第二个用途为作为延迟的参考值，通过该值观测来退避初始化时间，进一步避免ANR，其配置倍数由horn下发。
    private long addIdleHandleTimeMill = 0L;
    private long backgroundInitTimeMill = 0L;


    private long delayTimes = 0;
    private long getDefaultUserAgentDelayTimes = 0;
    private long lastResumeTime = 0;
    private boolean isActivityPaused;
    private int stepIndex = -1;
    // 后台预热退避次数
    private int backgroundPreloadRetreatCount = 0;
    private boolean hasTriggerFirstSegmentPreload = false;
    private boolean hasTriggerSecondSegmentPreload = false;
    private boolean hasTriggerThirdSegmentPreload = false;

    /**
     * 自研内核分段预热 https://km.sankuai.com/collabpage/1953724680
     * 1.backgroundPreloadWebViewIdleHandler:MTWebViewPreloadManager#preloadForMSCWebViewWarmUp
     * 2.getDefaultUserAgentWebViewIdleHandler:com.meituan.mtwebkit.MTWebSettings#getDefaultUserAgent
     * 3.preCreateWebViewIdleHandler
     * 4.preloadWebViewBasePackage()
     */
    IdleHandlerTask backgroundPreloadWebViewTask = new IdleHandlerTask() {
        @Override
        public boolean queueIdle() {
            WebViewPreloadReporter.getInstance().reportWebViewPreloadIdleDurationStartFromAppLaunch(addIdleHandleTimeMill);
            if (lastResumeTime == 0) {
                return true;
            }
            if (WebViewCacheManager.isBackgroundInited()) {
                MSCLog.i(TAG, "isBackgroundInited", WebViewCacheManager.isBackgroundInited());
                return false;
            }
            if (isActivityPaused) {
                return true;
            }
            long now = System.currentTimeMillis();
            if ((now - lastResumeTime) < MSCHornPreloadConfig.getBackgroundInitWebViewDelayTime()) {
                //此处延迟500毫秒主要为避免以下情况
                //1，页面加载时网络请求及其他计算导致ANR
                //2, Chrome APK首次加载时会更改AssetsManager的内部属性，避免线程冲突带来问题应避免与频繁使用资源时间错开
                return true;
            }
            AppWorkloadUtil.markCpuRateStart();
            //第一阶段预热Webview
            Jarvis.newThread("msc-background-init-webview", new Runnable() {
                @Override
                public void run() {
                    double rate = AppWorkloadUtil.getCpuRateEnd();
                    int threadCount = Thread.activeCount();
                    //首次idleHandler仍然会导致新增万分之3的ANR，因此在idleHandler中要考虑当前CPU损耗及活跃线程数，进行退避
                    int retreatDelayThreshold = MSCHornPreloadConfig.getBackgroundInitWebViewRetreatDelayThreshold();
                    long retreatDelayTime = MSCHornPreloadConfig.getBackgroundInitWebViewRetreatDelayTime();
                    if (rate > MSCHornPreloadConfig.getCPUUsageRatio() || threadCount > MSCHornPreloadConfig.getThreadActiveCount()) {
                        backgroundPreloadRetreatCount++;
                        // 退避次数在阈值（默认20次）以上时，进入延迟退避策略（控制退避频次）。
                        if (backgroundPreloadRetreatCount > retreatDelayThreshold) {
                            try {
                                Thread.sleep(retreatDelayTime);
                            } catch (InterruptedException e) {
                                e.printStackTrace();
                            }
                        }
                        addIdleHandler(backgroundPreloadWebViewTask);
                        return;
                    }
                    // WebView预热 第一段
                    if (MSCHornPreloadConfig.enableControlWebViewSegmentPreload()) {
                        ResourceManager.getInstance().submitPreloadBlock(new PreloadBlock() {
                            @Override
                            public String getBusinessName() {
                                return "MSC";
                            }

                            @Override
                            public String getPreloadType() {
                                return "webViewSegmentPreload1";
                            }

                            @Override
                            public String getBusinessId() {
                                return "webview";
                            }

                            @Override
                            public void onExecute() {
                                MSCLog.i(TAG, "doSegmentPreload by degradeFramework, step:1");
                                doFirstSegmentPreload(retreatDelayThreshold, retreatDelayTime);
                            }
                        }, new ExecuteResultCallback() {
                            @Override
                            public void onExecuteAllow() {

                            }

                            @Override
                            public void onExecuteDenied(String deniedReason, JSONObject adopt) {
                                MSCLog.i(TAG, "doSegmentPreload is rejected by degradeFramework, step:1, reason:" + deniedReason);
                            }
                        });
                    } else {
                        MSCLog.i(TAG, "doSegmentPreload by normal, step:1");
                        doFirstSegmentPreload(retreatDelayThreshold, retreatDelayTime);
                    }
                }
            }).start();
            return false; //不保留循环
        }
    };

    IdleHandlerTask callGetDefaultUAForMTWebViewTask = new IdleHandlerTask() {
        @Override
        public boolean queueIdle() {
            MSCLog.i(TAG, "callGetDefaultUAForMTWebViewTask queueIdle");
            if (isActivityPaused) {
                return true;
            }
            long configDelayTimes = MSCHornPreloadConfig.getDefaultUserAgentDelayTimes();
            if (getDefaultUserAgentDelayTimes > configDelayTimes) {
                MSCLog.i(TAG, "getDefaultUserAgentDelayTimes:", getDefaultUserAgentDelayTimes, "exceed", configDelayTimes);
                return false;
            }
            boolean delay = System.currentTimeMillis() - lastResumeTime < MSCHornPreloadConfig.getDefaultUserAgentDelayTimeMillis();
            double rate = AppWorkloadUtil.getCpuRateEnd();
            int threadCount = Thread.activeCount();
            if (delay || rate > MSCHornPreloadConfig.getCPUUsageRatio() || threadCount > MSCHornPreloadConfig.getThreadActiveCount()) {
                getDefaultUserAgentDelayTimes++;
                return true;
            }
            // 优化策略。
            if (MSCHornPreloadConfig.isHomePageFpsOptimizeByWebViewStep() && StartChromiumStepByStep.isSupported()) {
                // step by step。第二段预热
                boolean hasNextTask = StartChromiumStepByStep.hasNextTask();
                if (MSCHornPreloadConfig.enableControlWebViewSegmentPreload()) {
                    ResourceManager.getInstance().submitPreloadBlock(new PreloadBlock() {
                        @Override
                        public String getBusinessName() {
                            return "MSC";
                        }

                        @Override
                        public String getPreloadType() {
                            return "webViewSegmentPreload2";
                        }

                        @Override
                        public String getBusinessId() {
                            return "webview";
                        }

                        @Override
                        public void onExecute() {
                            MSCLog.i(TAG, "doSegmentPreload by degradeFramework, step:2");
                            doSecondSegmentPreload(hasNextTask);
                        }
                    }, new ExecuteResultCallback() {
                        @Override
                        public void onExecuteAllow() {

                        }

                        @Override
                        public void onExecuteDenied(String deniedReason, JSONObject adopt) {
                            MSCLog.i(TAG, "doSegmentPreload is rejected by degradeFramework, step:2, reason:" + deniedReason);
                        }
                    });
                } else {
                    MSCLog.i(TAG, "doSegmentPreload by normal, step:2");
                    doSecondSegmentPreload(hasNextTask);
                }
                return hasNextTask;
            }

            WebViewCacheManager.getInstance().getDefaultUserAgentForMTWebView();
            MSCLog.i(TAG, "complete getDefaultUserAgent, wait to preCreate");
            addIdleHandler(preCreateWebViewTask);
            return false;
        }
    };

    // 333
    IdleHandlerTask preCreateWebViewTask = new IdleHandlerTask() {
        @Override
        public boolean queueIdle() {
            MSCLog.i(TAG, "preCreateWebViewTask queueIdle");
            //获取当前cpu使用率
            double rate = AppWorkloadUtil.getCpuRateEnd();

            if (isKNBInit) {
                WebViewPreloadReporter.getInstance().reportWebViewPreloadIdleDurationStartFromAppLaunch(addIdleHandleTimeMill);
            }
            if (isActivityPaused) {
                return true;
            }
            long now = System.currentTimeMillis();
            long delayTime = (long) (backgroundInitTimeMill * MSCHornPreloadConfig.getWebViewCreateDelayRatio());
            //https://km.sankuai.com/collabpage/1856476868
            //由线上数据可知，idleHandler的95线在4s左右，当该设备idleHandler执行耗时过长时，在页面繁忙时预热WebView会导致更高的ANR风险
            //因此通过idle指动态设置延迟时长，且延迟超过3次时，自动取消该idleTask。
            int threadCount = Thread.activeCount();
            if (delayTimes > 2) {
                WebViewPreloadReporter.getInstance().preloadPreCreateWebViewDelayTimeOverLimit(now - lastResumeTime,
                        delayTime, rate, MSCHornPreloadConfig.getCPUUsageRatio(),
                        threadCount, MSCHornPreloadConfig.getThreadActiveCount());
                MSCLog.i(TAG, "preCreateWebViewIdleHandler delayTimes", delayTimes);
                return false;
            }
            if ((now - lastResumeTime) < delayTime || rate > MSCHornPreloadConfig.getCPUUsageRatio() || threadCount > MSCHornPreloadConfig.getThreadActiveCount()) {
                //计数，当判定当前执行存在ANR风险时，退避
                delayTimes++;
                return true;
            }
            // WebView预热 第二段
            if (MSCHornPreloadConfig.enableControlWebViewSegmentPreload()) {
                ResourceManager.getInstance().submitPreloadBlock(new PreloadBlock() {
                    @Override
                    public String getBusinessName() {
                        return "MSC";
                    }

                    @Override
                    public String getPreloadType() {
                        return "webViewSegmentPreload3";
                    }

                    @Override
                    public String getBusinessId() {
                        return "webview";
                    }

                    @Override
                    public void onExecute() {
                        MSCLog.i(TAG, "doSegmentPreload by degradeFramework, step:3");
                        doThirdSegmentPreload();
                    }
                }, new ExecuteResultCallback() {
                    @Override
                    public void onExecuteAllow() {

                    }

                    @Override
                    public void onExecuteDenied(String deniedReason, JSONObject adopt) {
                        MSCLog.i(TAG, "doSegmentPreload is rejected by degradeFramework, step:3, reason:" + deniedReason);
                    }
                });
            } else {
                MSCLog.i(TAG, "doSegmentPreload by normal, step:3");
                doThirdSegmentPreload();
            }
            return false;
        }
    };

    public void preloadWebView(boolean isKNBInit, Activity topActivity) {
        if (!MSCHornPreloadConfig.needPreloadWebView()) {
            MSCLog.i(TAG, "no preload WebView", "homePageFpsOptimizeStrategy:", MSCHornPreloadConfig.getHomePageFpsOptimizeStrategy());
            // 首页fps的通参。可以在这里加上维度
            PreloadInjection.notifyPreloadWillStart(getPreloadWebViewType(), "noPreloadWebView");
            PreloadInjection.notifyPreloadWillStart("homePageFpsOptimizeStrategy", MSCHornPreloadConfig.getHomePageFpsOptimizeStrategy() + "");
            WebViewPreloadReporter.getInstance().reportWebViewPreloadStartCount(false, getPreloadWebViewType());
            return;
        }
        WebViewPreloadReporter.getInstance().reportWebViewPreloadStartCount(true, getPreloadWebViewType());
        MSCLog.i(TAG, "preload WebView", "enableScrollRetreatAndSplit:", MSCHornPreloadConfig.enableScrollRetreatAndSplit(), "strategy: C", "homePageFpsOptimizeStrategy", MSCHornPreloadConfig.getHomePageFpsOptimizeStrategy());
        PreloadInjection.notifyPreloadWillStart(getPreloadWebViewType(), "PreloadWebView");
        PreloadInjection.notifyPreloadWillStart("homePageFpsOptimizeStrategy", MSCHornPreloadConfig.getHomePageFpsOptimizeStrategy() + "");
        addIdleHandleTimeMill = SystemClock.elapsedRealtime();
        this.isKNBInit = isKNBInit;
        boolean useMtWebView = WebViewCacheManager.getInstance().useMtWebViewByAppId(PRELOAD_WEBVIEW);
        // 逻辑理论上可以合并，但是为什么没有呢？因为这部分改动属于测试性质，如果有问题还要回滚，到时候就不好拆逻辑。所以先独立一个if分支。
        // TODO 待效果稳定没有问题，可以将条件和else进行融合。 since 12.15.200. horn本地默认打开
        if (MSCHornPreloadConfig.getMtInitEntrePartTwo() && useMtWebView && MTWebView.getMTWebViewIsCreate()) {
            // ab开关；&& 如果是美团自研内核 && 自研内核已经准备好了。-》直接进入二段预热
            enterPart2(true);
        } else if (!this.isKNBInit || useMtWebView) {
            // 生命周期监听注册太晚，首页启动lastResumeTime一直为0
            if (lastResumeTime == 0) {
                lastResumeTime = System.currentTimeMillis();
            }
            // 开始执行WebView后台预热时重置退避次数。
            backgroundPreloadRetreatCount = 0;
            // webview没有被初始化过 || 需要美团的内核webview -》 进入一段预热
            addIdleHandler(backgroundPreloadWebViewTask);
        } else {
            // 其他情况（knb已经init） 进入二段预热
            WebViewPreloadReporter.getInstance().reportWebViewInitStateBeforePreload(false, true);
            WebViewFirstPreloadStateManager.getInstance().raiseStateTo(WebViewFirstPreloadStateManager.PreloadState.BACKGROUND_INIT);
            addIdleHandler(preCreateWebViewTask);
        }
    }

    public void enterPart2(boolean isMtWebViewPreload) {
        MSCLog.i(TAG, "#enterPart2");
        WebViewPreloadReporter.getInstance().reportWebViewInitStateBeforePreload(isMtWebViewPreload, this.isKNBInit);
        WebViewFirstPreloadStateManager.getInstance().raiseStateTo(WebViewFirstPreloadStateManager.PreloadState.BACKGROUND_INIT);
        addIdleHandler(preCreateWebViewTask);
    }

    private void addIdleHandler(IdleHandlerTask task) {
        if (MSCHornPreloadConfig.isHomePageFpsOptimizeByWebViewRetreat()) {
            if (IdleHandlerTaskServiceProvider.getService() != null) {
                IdleHandlerTaskServiceProvider.getService().addIdledHandlerTask(task);
            }
        } else {
            MessageQueue.IdleHandler idleHandler = new MessageQueue.IdleHandler() {
                @Override
                public boolean queueIdle() {
                    return task.queueIdle();
                }
            };
            if (Build.VERSION.SDK_INT < 23) {
                Handler handler = new Handler(Looper.getMainLooper());
                handler.post(new Runnable() {
                    @Override
                    public void run() {
                        Looper.myQueue().addIdleHandler(idleHandler);
                    }
                });
            } else {
                Looper.getMainLooper().getQueue().addIdleHandler(idleHandler);
            }
        }
    }

    public void registerLifecycleListener(Context context) {
        MSCLog.i(TAG, "registerLifecycleListener");
        if (Build.VERSION.SDK_INT >= Build.VERSION_CODES.ICE_CREAM_SANDWICH) {
            ((Application) context.getApplicationContext()).registerActivityLifecycleCallbacks(new Application.ActivityLifecycleCallbacks() {
                @Override
                public void onActivityCreated(@NonNull Activity activity, @Nullable Bundle savedInstanceState) {
                }

                @Override
                public void onActivityStarted(@NonNull Activity activity) {
                }

                @Override
                public void onActivityResumed(@NonNull Activity activity) {
                    MSCLog.i(TAG, "onActivityResumed", activity.toString());
                    lastResumeTime = System.currentTimeMillis();
                    isActivityPaused = false;
                }

                @Override
                public void onActivityPaused(@NonNull Activity activity) {
                    MSCLog.i(TAG, "onActivityPaused", activity.toString());
                    isActivityPaused = true;
                }

                @Override
                public void onActivityStopped(@NonNull Activity activity) {
                }

                @Override
                public void onActivitySaveInstanceState(@NonNull Activity activity, @NonNull Bundle outState) {
                }

                @Override
                public void onActivityDestroyed(@NonNull Activity activity) {
                }
            });
        }
    }

    /**
     * 注入基础库到渲染层。webview预热第四段。
     */
    // 444
    public void preloadWebViewBasePackage() {
        if (!MSCHornPreloadConfig.needPreloadWebView() || !MSCHornPreloadConfig.needWebViewInjectBasePackage()) {
            return;
        }
        MSCExecutors.submit(new Runnable() {
            @Override
            public void run() {
                if (MSCHornPreloadConfig.enableControlWebViewSegmentPreload()) {
                    ResourceManager.getInstance().submitPreloadBlock(new PreloadBlock() {
                        @Override
                        public String getBusinessName() {
                            return "MSC";
                        }

                        @Override
                        public String getPreloadType() {
                            return "webViewSegmentPreload4";
                        }

                        @Override
                        public String getBusinessId() {
                            return "webview";
                        }

                        @Override
                        public void onExecute() {
                            MSCLog.i(TAG, "doSegmentPreload by degradeFramework, step:4");
                            doFourthSegmentPreload();
                        }
                    }, new ExecuteResultCallback() {
                        @Override
                        public void onExecuteAllow() {

                        }

                        @Override
                        public void onExecuteDenied(String deniedReason, JSONObject adopt) {
                            MSCLog.i(TAG, "doSegmentPreload is rejected by degradeFramework, step:4, reason:" + deniedReason);
                        }
                    });
                } else {
                    MSCLog.i(TAG, "doSegmentPreload by normal, step:4");
                    doFourthSegmentPreload();
                }
            }
        });

    }

    private void doFirstSegmentPreload(int retreatDelayThreshold, long retreatDelayTime) {
        hasTriggerFirstSegmentPreload = true;
        MSCLog.i(TAG, "#doFirstSegmentPreload");
        long start = System.currentTimeMillis();
        String preloadWebViewType = getPreloadWebViewType();
        PreloadInjection.notifyPreloadStarted(WebViewFirstPreloadStateManager.PreloadState.BACKGROUND_INIT.name(), preloadWebViewType);
        boolean isInitSuccess = true;
        try {
            WebViewCacheManager.WebViewType webViewType = WebViewCacheManager.getInstance().useMtWebViewByAppId(PRELOAD_WEBVIEW) ?
                    WebViewCacheManager.WebViewType.MT_WEB_VIEW : WebViewCacheManager.WebViewType.CHROME;
            WebViewCacheManager.initOnBackground(MSCEnvHelper.getContext(), webViewType);
        } catch (Throwable e) {
            MSCLog.e(TAG, e, "init on background");
            isInitSuccess = false;
        }
        PreloadInjection.notifyPreloadEnd(WebViewFirstPreloadStateManager.PreloadState.BACKGROUND_INIT.name(), preloadWebViewType);
        backgroundInitTimeMill = System.currentTimeMillis() - start;
        Map<String, Object> tags = new HashMap<>();
        // 有退避策略的额外上报退避策略
        tags.put("retreatCount", backgroundPreloadRetreatCount);
        tags.put("retreatDelayThreshold", retreatDelayThreshold);
        tags.put("retreatDelayTime", retreatDelayTime);
        WebViewPreloadReporter.getInstance().reportWebViewBackgroundInitDuration(backgroundInitTimeMill, isInitSuccess, tags);
        WebViewFirstPreloadStateManager.getInstance().updateStateAfterPreload();

        // 下一个主线程空闲时创建WebView组件
        // 为降低ANR，仅在一阶段初始化耗时阈值低于设置阈值时完成二阶段初始化
        if (backgroundInitTimeMill < MSCHornPreloadConfig.getWebViewCreateDelayThreshold()) {
            AppWorkloadUtil.markCpuRateStart();
            if (MSCHornPreloadConfig.enableScrollRetreatAndSplit() && WebViewCacheManager.getInstance().useMtWebViewByAppId(PRELOAD_WEBVIEW)) {
                // 开关打开 && 自研内核 -> 拆分二段
                addIdleHandler(callGetDefaultUAForMTWebViewTask);
            } else {
                addIdleHandler(preCreateWebViewTask);
            }
        } else {
            WebViewPreloadReporter.getInstance().reportDelayPreloadConfigOfCancelCreateWebView(backgroundInitTimeMill,
                    MSCHornPreloadConfig.getWebViewCreateDelayThreshold());
        }
    }

    private void doSecondSegmentPreload(boolean hasNextTask) {
        hasTriggerSecondSegmentPreload = true;
        MSCLog.i(TAG, "doSecondSegmentPreload hasNextTask:" + hasNextTask);
        if (hasNextTask) {
            stepIndex++;
            String state = MSCHornPreloadConfig.isHomePageFpsOptimizeByWebViewStep() + "," + StartChromiumStepByStep.isSupported();
            PreloadInjection.notifyPreloadStarted("rollbackWebViewStepByStep" + stepIndex, state);
            StartChromiumStepByStep.runNextTask();
            PreloadInjection.notifyPreloadEnd("rollbackWebViewStepByStep" + stepIndex, state);
            WebViewCache.getInstance().putDigExt("Wscurrent", getCurrentStepIndex());
        } else {
            addIdleHandler(preCreateWebViewTask);
        }
    }

    private void doThirdSegmentPreload() {
        hasTriggerThirdSegmentPreload = true;
        MSCLog.i(TAG, "doSegmentWebViewPreload:3 enableControlWebViewSegmentPreload:" + MSCHornPreloadConfig.enableControlWebViewSegmentPreload());
        if (WebViewCacheManager.getInstance().useMtWebViewByAppId(PRELOAD_WEBVIEW) && !MSCHornPreloadConfig.rollbackShareWebView()) {
            // 连续创建2个webview
            WebViewCache.getInstance().create("msc_preload", MSCEnvHelper.getContext());
            if (MMPStrategyUtil.needCreateMMPWebview() && !MMPStrategyUtil.isRouteToMsc()) {
                WebViewCache.getInstance().create("mmp_preload", MSCEnvHelper.getContext());
            }
        }
        MSCLog.i(TAG, "cacheFirstWebView");
        Map<String, Object> tags = new HashMap<>();
        WebViewCacheManager.getInstance().cacheFirstWebView(MSCEnvHelper.getContext(),
                WebViewCacheManager.WebViewCreateScene.PRE_CREATE, PRELOAD_WEBVIEW, tags);
        // 业务预热触发。
        preloadWebViewBasePackage();
        // mmp触发预热
        if (MMPStrategyUtil.needCreateMMPWebview()) {
            MMPPreloadProxy.preload(MSCEnvHelper.getContext());
        }
    }

    private void doFourthSegmentPreload() {
        MSCLog.i(TAG, "doSegmentWebViewPreload:4 enableControlWebViewSegmentPreload:" + MSCHornPreloadConfig.enableControlWebViewSegmentPreload());
        MSCRuntime basePreloadRuntime = RuntimeManager.findBasePreloadRuntime();
        if (null == basePreloadRuntime) {
            return;
        }
        IAppLoader loader = basePreloadRuntime.getModule(IAppLoader.class);
        loader.preloadWebViewBasePackage();
    }


    public boolean isKNBInit() {
        return isKNBInit;
    }

    public String getPreloadWebViewType() {
        WebViewCacheManager.WebViewType webViewType = WebViewCacheManager.getInstance().useMtWebViewByAppId(PRELOAD_WEBVIEW) ?
                WebViewCacheManager.WebViewType.MT_WEB_VIEW : WebViewCacheManager.WebViewType.CHROME;
        String strategy = MSCHornPreloadConfig.enableScrollRetreatAndSplit() ? "_C" : "";
        return "MSC_PRELOAD_" + webViewType.toString() + strategy;
    }

    /**
     * 获取分布预热的当前次数
     */
    public int getCurrentStepIndex() {
        return stepIndex;
    }

    public boolean hasTriggerFirstSegmentPreload() {
        return hasTriggerFirstSegmentPreload;
    }

    public boolean hasTriggerSecondSegmentPreload() {
        return hasTriggerSecondSegmentPreload;
    }

    public boolean hasTriggerThirdSegmentPreload() {
        return hasTriggerThirdSegmentPreload;
    }
}