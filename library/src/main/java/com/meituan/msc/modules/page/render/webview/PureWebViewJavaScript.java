package com.meituan.msc.modules.page.render.webview;

public class PureWeb<PERSON>ie<PERSON>JavaScript extends WebViewJavaScript {
    private final String script;

    private PureWebViewJavaScript(String script) {
        this.script = script;
    }

    @Override
    public String buildWebMessageString(boolean needTrace) {
        return null;
    }

    @Override
    public String buildJavaScriptString(boolean needTrace) {
        return script;
    }

    @Override
    public void markScriptPendingTime() {
    }

    @Override
    public boolean isSupportMessagePort() {
        return false;
    }

    @Override
    public int length() {
        return script.length();
    }

    @Override
    public String getModuleName() {
        return "";
    }

    @Override
    public String getMethodName() {
        return "";
    }

    public static WebViewJavaScript from(String script) {
        return new PureWebViewJavaScript(script);
    }
}
