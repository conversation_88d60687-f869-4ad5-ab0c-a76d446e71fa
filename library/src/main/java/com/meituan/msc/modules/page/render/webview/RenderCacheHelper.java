package com.meituan.msc.modules.page.render.webview;

import android.content.SharedPreferences;
import android.support.annotation.Nullable;
import android.text.TextUtils;
import android.util.Log;

import com.google.gson.JsonElement;
import com.google.gson.JsonObject;
import com.meituan.msc.common.config.MSCConfig;
import com.meituan.msc.common.executor.MSCExecutors;
import com.meituan.msc.common.utils.CommonGson;
import com.meituan.msc.common.utils.FileSizeUtil;
import com.meituan.msc.common.utils.HashMapHelper;
import com.meituan.msc.common.utils.JsonUtil;
import com.meituan.msc.common.utils.MSCSharedPreferences;
import com.meituan.msc.common.utils.PathUtil;
import com.meituan.msc.extern.MSCEnvHelper;
import com.meituan.msc.modules.api.msi.api.InitialCacheApi;
import com.meituan.msc.modules.api.report.MetricsModule;
import com.meituan.msc.modules.engine.MSCHornRollbackConfig;
import com.meituan.msc.modules.engine.MSCRuntime;
import com.meituan.msc.modules.reporter.MSCLog;
import com.meituan.msc.modules.reporter.ReporterFields;
import com.meituan.msc.modules.update.AppConfigModule;
import com.meituan.msc.modules.update.MSCAppModule;

import org.json.JSONException;

import java.io.File;

/**
 * 初始渲染缓存模块
 * 缓存的sync_config数据，此数据允许外界修改。
 * https://km.sankuai.com/page/534799217
 */
public class RenderCacheHelper {

    private static final String TAG = "RenderingCacheModule";
    private static final String MSC = "msc_";
    private static final String NAME = "init_cache";
    // 12.25.400 以后SP存储的文件名优化
    private static final String SP_NEW_PREFIX = "msc_render_cache_";

    public static void init() {
        String[] usingRenderCacheAppIds = MSCHornRollbackConfig.getUsingRenderCacheAppIds();
        if (usingRenderCacheAppIds == null) {
            return;
        }
        if (!MSCHornRollbackConfig.isRenderCacheStorageFix()) {
            // 回滚之后需要清理新版本的渲染缓存。
            for (String usingRenderCacheAppId : usingRenderCacheAppIds) {
                MSCEnvHelper.getSharedPreferences(SP_NEW_PREFIX + usingRenderCacheAppId).edit().clear().apply();
            }
            MSCLog.i(TAG, "init and clear render cache of new version!", usingRenderCacheAppIds);
            return;
        }
        // 存储治理需要清理历史版本的渲染缓存。
        for (String usingRenderCacheAppId : usingRenderCacheAppIds) {
            MSCEnvHelper.getSharedPreferences(MSC + NAME + usingRenderCacheAppId).edit().clear().apply();
        }
        MSCLog.i(TAG, "init and clear render cache of older version!", usingRenderCacheAppIds);
    }

    private static boolean isRenderCacheStorageFix(MSCAppModule mscAppModule) {
        try {
            return mscAppModule.getRuntime().isRenderCacheStorageFix();
        } catch (Exception e) {
            MSCLog.e(TAG, "isRenderCacheStorageFix", e);
        }
        return false;
    }

    /**
     * 供外界配置动态渲染缓存
     *
     * @param param
     */
    public static void saveDynamicInitialRenderCache(InitialCacheApi.InitialCacheParams param, MSCRuntime runtime) {
        MSCExecutors.ioSerialized.submit(new Runnable() {
            @Override
            public void run() {
                updateDynamicRenderCache(param, runtime.getMSCAppModule());
            }
        });
    }

    /**
     * page 存储渲染缓存
     * @param mscAppModule
     * @param pagePath
     * @param staticCache
     */
    public static void saveStaticRenderCache(MSCAppModule mscAppModule, String pagePath, String staticCache) {
        AppConfigModule.InitialRenderingCacheState state = mscAppModule.obtainInitialRenderingCacheState(pagePath);
        if (state == AppConfigModule.InitialRenderingCacheState.NONE || !MSCConfig.isEnableRenderCache()) {
            return;
        }
        MSCExecutors.ioSerialized.submit(new Runnable() {
            @Override
            public void run() {
                // 快照需要依赖同一时间设置的动态渲染缓存，在设置静态渲染缓存时清除快照
                saveSnapshotTemplate(mscAppModule, pagePath, null);
                saveRenderCache(mscAppModule, pagePath, staticCache);
                checkFileSize(mscAppModule);
            }
        });
    }

    /**
     * 获取缓存数据
     */
    @Nullable
    public static String obtainRenderCacheIfNeed(MSCAppModule appModule, String path,
                                                 int viewId, String openType) {
        if (shouldUseRenderCache(appModule,path)) {
            return obtainRenderCache(appModule, path, viewId, openType);
        }
        return null;
    }

    public static boolean shouldUseRenderCache(MSCAppModule appModule, String path) {
        AppConfigModule.InitialRenderingCacheState state = appModule.obtainInitialRenderingCacheState(path);
        return state != AppConfigModule.InitialRenderingCacheState.NONE && MSCConfig.isEnableRenderCache();
    }


    @Nullable
    public static String addParamsToRenderCache(String cache, MSCAppModule mscAppModule, String
            targetPath, int viewId, String openType) {
        try {
            JsonUtil.FastBuilder builder = new JsonUtil.FastBuilder(cache);
            builder.put("id", viewId);
            builder.put("timestamp", System.currentTimeMillis());
            builder.put("navigationType", openType);

            AppConfigModule.InitialRenderingCacheState state = mscAppModule.obtainInitialRenderingCacheState(targetPath);
            if (state != AppConfigModule.InitialRenderingCacheState.NONE) {
                builder.put("initialRenderingCache", state.name().toLowerCase());
            }
            return builder.build();
        } catch (JSONException e) {
            MSCLog.e(e);
        }
        return null;
    }

    public static String obtainSnapshotTemplate(MSCAppModule mscAppModule, String targetPath) {
        String result = "";
        final String cacheKey = obtainSnapshotTemplateKey(mscAppModule, targetPath);
        SharedPreferences sp = obtainCacheHolder(mscAppModule.getAppId(), isRenderCacheStorageFix(mscAppModule));
        if (!sp.contains(cacheKey)) {
            MSCLog.d(TAG, "snapshot template cache not found for ", cacheKey);
        } else {
            result = parseFormatedContent(getString(sp, cacheKey, ""), mscAppModule);
        }
        MSCLog.d(TAG, "obtainSnapshotTemplate: return ", FileSizeUtil.formatStringSize(result));
        return result;
    }

    /**
     * 升级之后删除之前版本的小程序渲染缓存
     * todo 是否可以异步删除？
     * @param appId
     */
    // 清除对应小程序缓存
    public static void clearCache(String appId) {
        MSCLog.d(TAG, "clear rendering cache for appId ", appId);
        obtainCacheHolder(appId, MSCHornRollbackConfig.isRenderCacheStorageFix()).edit().clear().apply();
    }

    private static void updateDynamicRenderCache(InitialCacheApi.InitialCacheParams param, MSCAppModule mscAppModule) {
        final String pageName = param.pageName;

        if (updateDynamicCache(param.cache, mscAppModule, pageName)) return;
        Object cacheTemplate = param.cacheTemplate;
        String cacheTemplateString = cacheTemplate != null ? cacheTemplate.toString() : null;
        saveSnapshotTemplate(mscAppModule, pageName, cacheTemplateString);
        checkFileSize(mscAppModule);
    }

    /**
     * 更新渲染动态缓存数据
     * @param dynamicCache
     * @param mscAppModule
     * @param pageName
     * @return return
     */
    private static boolean updateDynamicCache(JsonElement dynamicCache, MSCAppModule mscAppModule, String pageName) {
        // 拼接缓存关键字
        final String cacheKey = obtainCacheKey(mscAppModule, pageName);
        SharedPreferences sp = obtainCacheHolder(mscAppModule.getAppId(), isRenderCacheStorageFix(mscAppModule));

        if (dynamicCache == null || dynamicCache.isJsonNull()) { // 直接删除
            MSCLog.d(TAG, "received null dynamic cache, clear cache");
            // todo 这里没有移除渲染模块 是否符合预期？
            sp.edit().remove(cacheKey).apply();
            return true;
        }

        // 更新静态渲染缓存，将cache插入
        String containerString = parseFormatedContent(getString(sp, cacheKey, ""), mscAppModule);
        JsonObject jsonObject = CommonGson.GSON.fromJson(containerString, JsonObject.class);
        if (jsonObject == null || jsonObject.size() == 0) {
            MSCLog.d(TAG, "static cache is null, can not add dynamic cache");
            return true;
        }
        // 动态渲染缓存cache、静态渲染缓存config均作为container中的一级字段，两种缓存均为String格式，不要尝试解json
        jsonObject.add("cache", dynamicCache);
        saveRenderCache(mscAppModule, pageName, jsonObject.toString());
        return false;
    }

    /**
     * 存储快照，可直接渲染出页面，与缓存数据需要一起使用
     */
    private static void saveSnapshotTemplate(MSCAppModule mscAppModule, String pagePath, String
            content) {
        final String cacheKey = obtainSnapshotTemplateKey(mscAppModule, pagePath);
        MSCLog.d(TAG, "saveSnapshotTemplate to ", cacheKey, ", ", FileSizeUtil.formatStringSize(content));
        SharedPreferences sp = obtainCacheHolder(mscAppModule.getAppId(), isRenderCacheStorageFix(mscAppModule));
        saveString(sp, cacheKey, formatSavingContent(content, mscAppModule));
    }

    @Nullable
    private static String obtainRenderCache(MSCAppModule mscAppModule, String targetPath,
                                           int viewId, String openType) {  //TODO 编译时快照需要经过此类包装

        final String cacheKey = obtainCacheKey(mscAppModule, targetPath);
        SharedPreferences sp = obtainCacheHolder(mscAppModule.getAppId(), isRenderCacheStorageFix(mscAppModule));
        String cache = parseFormatedContent(getString(sp, cacheKey, ""), mscAppModule);
        if (TextUtils.isEmpty(cache)) {
            MSCLog.d(TAG, "cache not found for ", cacheKey);
        } else {
            cache = addParamsToRenderCache(cache, mscAppModule, targetPath, viewId, openType);
            MSCLog.d(TAG, "obtainRenderCache: return ", FileSizeUtil.formatStringSize(cache));
        }

        return cache;
    }


    /**
     * 存储缓存数据
     *
     * @param pagePath 实际存储时用的key会去除query
     */
    private static void saveRenderCache(MSCAppModule mscAppModule, String pagePath, String
            content) {
        // TODO 这里只有存储新版快照，没有删除老版快照的逻辑，会导致存储占用增大，需要优化
        final String cacheKey = obtainCacheKey(mscAppModule, pagePath);
        MSCLog.d(TAG, "saveRenderCache to ", cacheKey, ", ", FileSizeUtil.formatStringSize(content));
        SharedPreferences sp = obtainCacheHolder(mscAppModule.getAppId(), isRenderCacheStorageFix(mscAppModule));
        saveString(sp, cacheKey, formatSavingContent(content, mscAppModule));
    }
    // SP操作收口

    // 数据key拼接规则 用户ID:页面名称
    private static String obtainCacheKey(MSCAppModule mscAppModule, String pageName) {
        String cachePath = "";
        if (pageName != null) {
            cachePath = PathUtil.getPath(pageName);
        }
        StringBuilder sb = new StringBuilder(MSCEnvHelper.getEnvInfo().getUserID());
        if (isRenderCacheStorageFix(mscAppModule)) {
            // 新版使用"{userId}:{pageName}" 作为存储的key的方式
            sb.append(":").append(cachePath);
        } else {
            sb.append(":").append(MSCEnvHelper.getEnvInfo().getAppID()).append(":").append(mscAppModule.getPublishId()).append(":").append(cachePath);
            // sb.append("_v2");   // 支持缓存中个字段为String类型不解开，不兼容之前的嵌套json形式存储，在此区分
        }
        return sb.toString();
    }

    private static String obtainSnapshotTemplateKey(MSCAppModule mscAppModule, String pageName) {
        return obtainCacheKey(mscAppModule, pageName) + "_template";
    }

    // 获取缓存对象 美团侧自动为Cips实现 优选侧暂时为系统实现
    private static synchronized SharedPreferences obtainCacheHolder(String appId, boolean renderCacheFix) {
        if (renderCacheFix) {
            return MSCEnvHelper.getSharedPreferences(SP_NEW_PREFIX + appId);
        }
        return MSCEnvHelper.getSharedPreferences(MSC + NAME + appId);
    }

    private static File getPreferenceFile(String fileName) {
        String spPath = "shared_prefs/" + fileName + ".xml";
        return new File(MSCEnvHelper.getContext().getApplicationInfo().dataDir, spPath);
    }

    private static long loadPreferenceSize(String fileName) {
        long res = -1;

        if (TextUtils.isEmpty(fileName) || MSCEnvHelper.getContext() == null) {
            Log.w(TAG, "args is invalid");
            return res;
        }

        File spFile = getPreferenceFile(fileName);
        if (spFile.exists()) {
            res = spFile.length();
        } else {
            res = MSCSharedPreferences.getSize(MSCEnvHelper.getContext(), fileName);
        }
        return res;
    }


    private static void checkFileSize(MSCAppModule mscAppModule) {
        boolean isRenderCacheFix = isRenderCacheStorageFix(mscAppModule);
        SharedPreferences sp = obtainCacheHolder(mscAppModule.getAppId(), isRenderCacheFix);
        String fileNamePrefix = isRenderCacheFix ? SP_NEW_PREFIX : MSC + NAME;
        long currentSize = loadPreferenceSize(fileNamePrefix + mscAppModule.getAppId());
        MSCLog.d(TAG, "checkFileSize: ", FileSizeUtil.formatFileSize(currentSize));
        if (currentSize >= MSCHornRollbackConfig.getRenderCacheStorageLimit()) {
            // 超过10M 无脑清空。
            MSCLog.w(TAG, "clear rendering cache because size is over limit");
            MetricsModule.reportMetrics(ReporterFields.REPORT_COUNT_CLEAR_RENDER_CACHE, HashMapHelper.of("mmp.id", mscAppModule.getAppId()));
            sp.edit().clear().apply();
        }
    }

    /**
     * 新版本的渲染缓存和渲染快照内容拼接publishId进行封装。
     * @param result
     * @param mscAppModule
     * @return
     */
    private static String formatSavingContent(String result, MSCAppModule mscAppModule) {
        if (!TextUtils.isEmpty(result) && isRenderCacheStorageFix(mscAppModule)) {
            // 对原数据进行publishId封装：msc_render_cache_123|xxxxxx
            result = getNewVersionContentFlag(mscAppModule) + result;
        }
        return result;
    }

    /**
     * 由于新版本的渲染缓存和渲染快照内容拼接publishId进行封装（msc_render_cache_${publishId}|xxxxxx）,所以内容取出之后需要进行校验。
     * 如果是新版格式的内容数据，则校验publishId，如果一致则可以正常使用，否则无法使用，直接返回null。
     * 如果是老版格式的内容数据，则维持原有逻辑直接返回。
     * @param result
     * @param mscAppModule
     * @return
     */
    private static String parseFormatedContent(String result, MSCAppModule mscAppModule) {
        // 如果取出的数据格式为：msc_render_cache_123|xxxxxx，并且publishId一致，代表是当前版本的缓存，可以使用
        String newVersionFlag = getNewVersionContentFlag(mscAppModule);
        if (result != null && result.startsWith(newVersionFlag)) {
            return result.substring(newVersionFlag.length());
        }
        // 如果取出的数据格式为：msc_render_cache_124|xxxxxx，但publishId不一致，代表不是当前版本的缓存，不可使用（待覆盖）。
        if (result != null && result.startsWith(SP_NEW_PREFIX)) {
            return null;
        }
        // 如果不是新版本的内容格式，则直接返回。
        return result;
    }

    /**
     * 获取新版内容的标识：
     * 12.25.400 以后缓存内容拼接了新格式：msc_render_cache_${publishId}|xxxxxx
     *
     * @param mscAppModule
     * @return
     */
    private static String getNewVersionContentFlag(MSCAppModule mscAppModule) {
        return SP_NEW_PREFIX + mscAppModule.getPublishId() + "|";
    }


    private static void saveString(SharedPreferences sp, String key, String value) {
        if (sp instanceof MSCSharedPreferences) {
            ((MSCSharedPreferences) sp).putStringAsObject(key, value);
        } else {
            sp.edit().putString(key, value).apply();
        }
    }

    private static String getString(SharedPreferences sp, String key, String defaultValue) {
        if (sp instanceof MSCSharedPreferences) {
            return ((MSCSharedPreferences) sp).getStringAsObject(key, defaultValue);
        } else {
            return sp.getString(key, defaultValue);
        }
    }
}
