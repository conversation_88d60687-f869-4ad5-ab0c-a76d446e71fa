package com.meituan.msc.modules.page.render.webview;

import android.util.DisplayMetrics;

import com.meituan.dio.easy.DioFile;
import com.meituan.msc.common.config.MSCConfig;
import com.meituan.msc.common.utils.FileUtil;
import com.meituan.msc.extern.MSCEnvHelper;
import com.meituan.msc.modules.reporter.MSCLog;
import com.meituan.msc.modules.update.MSCAppModule;

import java.io.IOException;
import java.util.regex.Matcher;
import java.util.regex.Pattern;

/**
 * Created by letty on 2022/3/10.
 **/
public class TemplateHelper {
    public static final String TEMPLATE_HTML_PREFIX = "\n" +
            "<!DOCTYPE html>\n" +
            "<html lang=\"zh_CN\">\n" +
            "<head>\n" +
            " <meta charset=\"UTF-8\">\n" +
            " <meta name=\"viewport\" content=\"width=device-width, user-scalable=no, initial-scale=1.0, maximum-scale=1.0, minimum-scale=1.0, " +
            "viewport-fit=cover\">\n" +
            " <script>\n" +
            "   window.__isPagePreloadMode = true\n" +
            " </script>\n" +
            "</head>\n" +
            "<body>\n";

    public static String JAVASCRIPT_BACKGROUND_COLOR_RESET = "document.body.style.backgroundColor = '%s'";

    public static final String TEMPLATE_HTML_SUFFIX = "\n" +
            "<script>\n" +
            "    NativeBridge.invoke('WebView', 'onHTMLLoaded', JSON.stringify(['normal']))\n" +
            "</script>\n" +
            "</body>\n" +
            "</html>";

    public static final String TEMPLATE_HTML_SUFFIX_WIDGET = "\n" +
            "<script>\n" +
            "    document.body.style.backgroundColor = 'transparent';\n" +
            "    NativeBridge.invoke('WebView', 'onHTMLLoaded', JSON.stringify(['normal']))\n" +
            "</script>\n" +
            "</body>\n" +
            "</html>";

    public static final String APPEND_SCRIPT_NEW = "var a = document.createElement('script');\n" +
        "a.src = '%s'; a.async = %s;  a.defer= %s; a.setAttribute('crossorigin', 'anonymous');" +
        "document.body.appendChild(a);";

    public static final String APPEND_SCRIPT = "var a = document.createElement('script');\n" +
            "a.src = '%s'; a.async = %s;" +
            "document.body.appendChild(a);";


    public static boolean shouldUseSnapshotTemplate(MSCAppModule appModule, String path) {
        return RenderCacheHelper.shouldUseRenderCache(appModule,path)
                && MSCConfig.isEnableSnapshotTemplate()
                && appModule.obtainInitialRenderingSnapshotState(path);
    }

    public static String getSnapshotTemplate(MSCAppModule appModule, String path) {
        // 需要与渲染缓存结合使用，因此需要遵守一样的开关控制
        if (shouldUseSnapshotTemplate(appModule, path)) {
            return RenderCacheHelper.obtainSnapshotTemplate(appModule, path);
        }
        return null;
    }

    /**
     * 以真实屏幕宽度缩放编译时渲染出的像素值，形式为<$ 123 $>
     * https://km.sankuai.com/page/864231705
     */
    static String replaceCompileTimeTemplate(String orig) throws NumberFormatException {
        Matcher matcher = Pattern.compile("<\\$.*?\\$>").matcher(orig);
        StringBuffer sb = new StringBuffer();
        String rpx, px;
        while (matcher.find()) {
            rpx = matcher.group();
            rpx = rpx.substring(2, rpx.length() - 2);
            rpx = rpx.trim();
            px = String.valueOf(transform(rpx));
            matcher.appendReplacement(sb, px);
        }
        matcher.appendTail(sb);
        return sb.toString();
    }
    private static DisplayMetrics displayMetrics;

    private static double transform(String rpxString) throws NumberFormatException {
        double rpx = Double.parseDouble(rpxString);
        if (rpx == 0) {
            return 0;
        }
        if (displayMetrics == null) {
            displayMetrics = MSCEnvHelper.getContext().getResources().getDisplayMetrics();
        }
        double px = rpx / 375 * (displayMetrics.widthPixels / displayMetrics.density);  //标准宽带375dp，按本机屏幕宽度进行缩放
        px = px >= 0 ? Math.floor(px + 1e-4) : Math.ceil(px - 1e-4);
        if (px == 0) {
            px = 1;
        }
        return px;
    }

}
