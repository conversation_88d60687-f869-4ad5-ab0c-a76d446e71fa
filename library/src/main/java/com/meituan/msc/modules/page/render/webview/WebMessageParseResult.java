package com.meituan.msc.modules.page.render.webview;
import android.text.TextUtils;
import com.meituan.msc.modules.reporter.MSCLog;
import org.json.JSONArray;
import org.json.JSONException;
import org.json.JSONObject;
public class WebMessageParseResult {
    public static final String TAG = "WebMessageParseResult";
    public String type;
    public String module;
    public String method;
    public JSONArray argsArray;
    public static WebMessageParseResult parseAsyncMethodWebMessage(String jsonString) throws JSONException {
        JSONObject jsonObject = new JSONObject(jsonString);
        WebMessageParseResult parseResult = new WebMessageParseResult();
        String type = jsonObject.optString("type");
        if (!TextUtils.equals(type, "doPublishAsyncMethod")) {
            MSCLog.e(TAG, "messagePort#onMessage unsupported type: " + jsonString);
            return null;
        }
        JSONObject data = jsonObject.optJSONObject("data");
        if (data == null) {
            MSCLog.e(TAG, "messagePort#onMessage data lost: " + jsonString);
            return null;
        }
        String module = data.optString("moduleName");
        String method = data.optString("method");
        String args = data.optString("args");
        JSONArray argsArray = new JSONArray(args);
        parseResult.type = type;
        parseResult.module = module;
        parseResult.method = method;
        parseResult.argsArray = argsArray;
        return parseResult;
    }
}