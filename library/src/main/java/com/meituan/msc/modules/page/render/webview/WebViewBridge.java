package com.meituan.msc.modules.page.render.webview;

import android.support.annotation.Nullable;
import android.webkit.ValueCallback;

import com.meituan.msc.common.perf.PerfEventConstant;
import com.meituan.msc.jse.bridge.CallFunctionContext;
import com.meituan.msc.jse.bridge.ICallFunctionContext;
import com.meituan.msc.jse.bridge.JSFunctionCaller;
import com.meituan.msc.jse.bridge.JavaScriptModule;
import com.meituan.msc.jse.bridge.JavaScriptModuleRegistry;
import com.meituan.msc.modules.reporter.MSCLog;
import com.meituan.msc.util.perf.PerfEventRecorder;
import com.meituan.msc.util.perf.PerfTrace;

import org.json.JSONArray;

import static com.meituan.msc.common.perf.PerfEventConstant.ON_PAGE_START;

/**
 * JS 模块双向通信能力
 * Created by letty on 2022/3/10.
 **/
public abstract class WebViewBridge {

    private final JavaScriptModuleRegistry mJavaScriptModuleRegistry = new JavaScriptModuleRegistry();
    private final PerfEventRecorder perfEventRecorder;

    public WebViewBridge(PerfEventRecorder perfEventRecorder) {
        this.perfEventRecorder = perfEventRecorder;
    }

    private void recordPerfEvent(String module, String method, JSONArray arguments) {
        if ("WebViewPageListener".equals(module)) {
            switch (method) {
                case PerfEventConstant.ON_PAGE_START_EVENT:
                    PerfTrace.online().instant(ON_PAGE_START)
                            .arg("pagePath", arguments != null ? arguments.optString(0) : null)
                            .arg("packageName", arguments != null ? arguments.optString(1) : null)
                            .report();
                    break;
                case PerfEventConstant.ON_PAGE_PRELOAD:
                case PerfEventConstant.ON_PAGE_RECYCLE:
                    perfEventRecorder.recordInstantEvent(method);
                    break;
            }
        }
    }

    /**
     * get specific js module
     * common js function will be invoked after frameworkLoaded
     *
     * @param classOfT
     * @param <T>
     * @return
     */
    public <T extends JavaScriptModule> T getJSModule(Class<T> classOfT) {
        return mJavaScriptModuleRegistry.getJavaScriptModule(new JSFunctionCaller() {
            @Override
            public void callFunction(String module, String method, JSONArray arguments) {
                recordPerfEvent(module, method, arguments);
                MSCLog.i("WebViewBridge", module, method);
                // if (method.equals("onAppRoute")) {
                //                    MSCLog.i("WebViewBridge", "[MSC_LOG]getJSModule invokeMethod", module, method, ContainerController.TIME_FORMAT.format(new Date(System.currentTimeMillis())));
                //                }
                invokeMethod(CallFunctionContext.DO_NOTHING_CONTEXT, module, method, arguments);
            }
        }, classOfT);
    }

    /**
     * invoke method directly, will be invoked after frameworkLoaded
     *
     * @param context
     * @param module
     * @param method
     * @param arguments must be json string
     */
    public void invokeMethod(ICallFunctionContext context, String module, String method, JSONArray arguments) {
        invokeModuleMethod(context, module, method, arguments == null ? null : arguments.toString(), true, null, null);
    }

    public void invokeMethod(String module, String method, JSONArray arguments, @Nullable ValueCallback<String> resultCallback, WebViewEvaluateJavascriptListener evaluateJavascriptListener) {
        invokeModuleMethod(CallFunctionContext.DO_NOTHING_CONTEXT, module, method, arguments == null ? null : arguments.toString(), true, resultCallback, evaluateJavascriptListener);
    }

    protected abstract void invokeModuleMethod(ICallFunctionContext context, String module, String method, String args, boolean waitFrameWorkReady, @Nullable ValueCallback<String> resultCallback, WebViewEvaluateJavascriptListener evaluateJavascriptListener);

    public abstract void callBack(int callbackID, JSONArray arguments);

    public abstract void evaluateJavascript(String script, @Nullable ValueCallback<String> resultCallback);

}
