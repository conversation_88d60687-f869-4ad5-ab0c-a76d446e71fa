package com.meituan.msc.modules.page.render.webview;

import android.text.TextUtils;

import com.meituan.msc.jse.bridge.ICallFunctionContext;
import com.meituan.msc.modules.engine.MSCHornRollbackConfig;
import com.meituan.msc.modules.page.render.MSCHornPerfConfig;
import com.meituan.msc.modules.reporter.MSCLog;

import org.json.JSONObject;

import java.util.HashMap;
import java.util.Map;

public class WebViewBridgeInvokeWebViewJavaScript extends WebViewJavaScript {
    private final ICallFunctionContext context;
    private final String module;
    private final String method;
    private final String args;

    public WebViewBridgeInvokeWebViewJavaScript(ICallFunctionContext context, String module, String method, String args) {
        this.context = context;
        this.module = module;
        this.method = method;
        this.args = args;
    }

    // 为了准确在 setData命令执行前 埋点webViewBuildScript，要求显式调用 构造指令的方法(json或JavaScript)
    @Override
    public String buildWebMessageString(boolean needTrace) {
        Map<String, String> map = new HashMap<>();
        if (MSCHornPerfConfig.getInstance().needBuildScriptTrace(module, method) && needTrace) {
            context.getTrace().instant("webViewBuildScript");
            map.put("extra", context.getTrace().getAllTimeStampsJsonString());
        }
        map.put("module", module);
        map.put("method", method);
        map.put("args", args);
        return new JSONObject(map).toString();
    }

    @Override
    public String buildJavaScriptString(boolean needTrace) {
        String script;
        if (MSCHornPerfConfig.getInstance().needBuildScriptTrace(module, method) && needTrace) {
            context.getTrace().instant("webViewBuildScript");
            script = String.format("javascript:WebViewBridge.invoke('%s','%s', %s, %s)", module, method, args, context.getTrace().getAllTimeStampsJsonString());
        } else {
            script = String.format("javascript:WebViewBridge.invoke('%s','%s', %s)", module, method, args);
        }
        return script;
    }

    @Override
    public boolean isSupportMessagePort() {
        if (MSCHornRollbackConfig.readConfig().enablePageStartSequenceFix) {
            if (TextUtils.equals(module, "WebViewPageListener") && TextUtils.equals(method, "onPageStart")) {
                MSCLog.i("WebViewBridgeInvoke", "[LaunchInfo] let onPageStart run on UI thread");
                return false;
            }
        }
        return true;
    }

    @Override
    public void markScriptPendingTime() {
        if (MSCHornPerfConfig.getInstance().needBuildScriptTrace(module, method)) {
            context.getTrace().instant("beforePendingScriptEvaluateTime");
        }
    }

    @Override
    public int length() {
        return 0;
    }

    @Override
    public String getModuleName() {
        return module;
    }

    @Override
    public String getMethodName() {
        return method;
    }
}
