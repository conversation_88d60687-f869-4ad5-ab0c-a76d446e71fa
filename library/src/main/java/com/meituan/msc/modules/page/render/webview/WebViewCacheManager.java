package com.meituan.msc.modules.page.render.webview;

import android.content.Context;
import android.content.MutableContextWrapper;
import android.os.SystemClock;
import android.support.annotation.WorkerThread;
import android.text.TextUtils;
import android.view.View;
import android.webkit.WebSettings;
import android.webkit.WebView;

import com.meituan.android.common.metricx.PreloadInjection;
import com.meituan.android.degrade.interfaces.resource.ResourceManager;
import com.meituan.msc.common.executor.MSCExecutors;
import com.meituan.msc.common.framework.MPListenerManager;
import com.meituan.msc.common.process.WebViewDirectoryFixer;
import com.meituan.msc.common.utils.CIPStorageFileUtil;
import com.meituan.msc.common.utils.HashMapHelper;
import com.meituan.msc.extern.MSCEnvHelper;
import com.meituan.msc.modules.devtools.DebugHelper;
import com.meituan.msc.modules.engine.MSCHornRollbackConfig;
import com.meituan.msc.modules.engine.MSCRuntime;
import com.meituan.msc.modules.engine.RecentMSCManager;
import com.meituan.msc.modules.page.render.webview.impl.MTWebViewImp;
import com.meituan.msc.modules.page.render.webview.impl.NormalWebView;
import com.meituan.msc.modules.page.render.webview.impl.SimpleMTWebView;
import com.meituan.msc.modules.page.render.webview.impl.SimpleWebView;
import com.meituan.msc.modules.preload.MSCHornPreloadConfig;
import com.meituan.msc.modules.reporter.CommonTags;
import com.meituan.msc.modules.reporter.MSCLog;
import com.meituan.msc.modules.reporter.WebViewInitReporter;
import com.meituan.msc.modules.update.MSCAppModule;
import com.meituan.msc.util.perf.PerfTrace;
import com.meituan.mtwebkit.MTWebSettings;
import com.meituan.mtwebkit.MTWebView;
import com.meituan.mtwebkit.internal.MTWebViewConstants;
import com.meituan.mtwebkit.internal.MTWebViewMSCConfigManager;
import com.meituan.mtwebkit.internal.preload.MTWebViewPreloadManager;

import java.util.HashMap;
import java.util.Map;

import static com.meituan.msc.common.perf.PerfEventConstant.CREATE_WEB_VIEW;
import static com.meituan.msc.common.perf.PerfEventConstant.PRE_CREATE_WEB_VIEW;

/**
 * Created by letty on 2019/8/14.
 **/
public class WebViewCacheManager {

    private static final String TAG = "WebViewCacheManager";  //TODO WebView首次初始化耗时
    private IWebView mCachedWebView;
    private static volatile boolean firstWebViewCreated;
    private static volatile boolean hasGetDefaultUserAgentForMTVebView;
    public final static HashMap<WebViewType, Boolean> sGlobalFirstWebViewCreated = HashMapHelper.of(
            WebViewType.CHROME, false, WebViewType.MT_WEB_VIEW, false,
            WebViewType.MT_WEB_VIEW_SYSTEM, false, WebViewType.X5, false); // 加上 MT_WEB_VIEW_SYSTEM 类型, 以防万一不小心调用到空了

    //用于WebView分段初始化状态记录
    private volatile static boolean sBackgroundInited = false;
    private volatile static String webAppPath = null;

    private static volatile boolean needFallbackToSystemWebView = false;

    public static boolean isNeedFallbackToSystemWebView() {
        return needFallbackToSystemWebView;
    }

    public static void setNeedFallbackToSystemWebView(boolean needFallbackToSystemWebView) {
        WebViewCacheManager.needFallbackToSystemWebView = needFallbackToSystemWebView;
    }

    private final static WebViewCacheManager instance = new WebViewCacheManager();

    public static WebViewCacheManager getInstance() {
        return instance;
    }

    public enum WebViewType {
        CHROME,
        MT_WEB_VIEW,
        MT_WEB_VIEW_SYSTEM,
        X5
    }

    @WorkerThread
    public static void initOnBackground(Context context, WebViewType webViewType){
        if (webViewType == WebViewType.CHROME) {
            if (!sBackgroundInited) {
                // 系统内核原有的一段预热逻辑，getDefaultUserAgent
                WebSettings.getDefaultUserAgent(context.getApplicationContext());
                sBackgroundInited = true;
            }
        } else if (webViewType == WebViewType.MT_WEB_VIEW) {
            //自建引擎初始化逻辑
            if (!sBackgroundInited) {
                if (MSCHornPreloadConfig.enableScrollRetreatAndSplit()) {
                    MTWebViewPreloadManager.preloadForMSCWebViewWarmUp();
                } else {
                    MTWebSettings.getDefaultUserAgent(context.getApplicationContext());
                }
                sBackgroundInited = true;
            }
        } else {
            //X5暂时不优化
        }
        ensureWebViewAppPath(context);
    }


    private static void ensureWebViewAppPath(Context context){
        webAppPath = CIPStorageFileUtil.getFilesDir(context, "webviewcache").getAbsolutePath();
    }


    public static String getWebAppPath(Context context){
        if(webAppPath == null){
            ensureWebViewAppPath(context);
        }
        return webAppPath;
    }


    /**
     * 初始化是否已经完成。
     * @return
     */
    public static boolean isBackgroundInited(){
        return sBackgroundInited;
    }

    /**
     * 若其他途径完成webview创建，则不需要进行三段预热
     * @param inited
     */
    public static void setBackgroundInited(boolean inited) {
        sBackgroundInited = inited;
    }

    public WebViewCacheManager() {
    }

    /**
     * 用于WebViewServiceEngine
     */
    public IWebView getSimpleWebView(Context context, String appId, String mtWebViewTag) {
        IWebView webView;
        if (useMtWebViewByAppId(appId)) {
            webView = new SimpleMTWebView(context, mtWebViewTag);
            if (DebugHelper.isDebugWebView()) {
                MTWebView.setWebContentsDebuggingEnabled(true);
            }
        } else {
            // fix abi架构升级Android7.0以下版本WebViewCrash问题
            Abi64TitansCompat.obliterate(context);

            webView = new SimpleWebView(context);
            if (DebugHelper.isDebugWebView()) {
                WebView.setWebContentsDebuggingEnabled(true);
            }
        }
        setBackgroundInited(true);
        return webView;
    }

    /**
     * WebView创建时产生的异常未处理，交由调用方捕获
     */
    public IWebView getWebViewThroughCache(Context context, MSCRuntime runtime, String rendererHashCode) throws Exception {
        MSCLog.i(TAG, "getWebViewThroughCache", "MSCWebViewRenderer@" + rendererHashCode);
        IWebView result = null;
        // 三方小程序不允许使用预创建的 webview
        if (!MSCHornRollbackConfig.enableExternalAppPrefSourceLimit() || !runtime.getMSCAppModule().getExternalApp()) {
            result = mCachedWebView;
            mCachedWebView = null;
        }

        if (result == null) {
            result = getNewWebView(context, runtime.getAppId());
            result.setCreateScene(WebViewCreateScene.CREATE_AT_NO_CACHE);
        }
        if (!MSCHornRollbackConfig.readConfig().rollbackAppendRendererHashCode) {
            if (result instanceof MTWebViewImp) {
                ((MTWebViewImp) result).setRendererHashCode(rendererHashCode);
            }
        }
        result.init(runtime);
        return result;
    }

    // private boolean mtWebViewReady;

    private IWebView getNewWebView(Context context, String appId) throws Exception {
        String traceName = PreloadWebViewManager.PRELOAD_WEBVIEW.equals(appId) ? PRE_CREATE_WEB_VIEW : CREATE_WEB_VIEW;
        PerfTrace.online().begin(traceName).arg("appId", appId).report();
        long start = SystemClock.elapsedRealtime();
        WebViewType webViewType;
        IWebView result;

        if (!firstWebViewCreated) {
            MPListenerManager.getInstance().launchEventListener.onEvent("native_webview_init_begin");
            WebViewDirectoryFixer.fixMultiProcessWebView(true);
        }
        try {
            // 实际创建WebView
            if (!needFallbackToSystemWebView && useMtWebViewByAppId(appId)) {
                result = new MTWebViewImp(context, appId);
                hasGetDefaultUserAgentForMTVebView = true;
                MTWebView webView = (MTWebView) result.getWebView();
                webViewType = MTWebViewConstants.TYPE_MTWEBVIEW_MT.equals(webView.getMTWebViewType()) ? WebViewType.MT_WEB_VIEW : WebViewType.MT_WEB_VIEW_SYSTEM;
            } else {
                // fix abi架构升级7.0以下版本WebViewCrash问题
                Abi64TitansCompat.obliterate(context);
                webViewType = WebViewType.CHROME;
                result = new NormalWebView(context, appId);
            }
        } catch (Exception e) {
            // webView init error，此处仅记录，继续抛出
//            MSCEnvHelper.getSniffer().smell("WebViewInitError", e.getMessage(), MSCLog.getAllStackInformation(e), null);
            RecentMSCManager.recordWebViewError(context);
            MSCLog.e(e);
            throw e;
        }
        if (DebugHelper.isDebugWebView()) {
            if (webViewType == WebViewType.MT_WEB_VIEW) {
                MTWebView.setWebContentsDebuggingEnabled(true);
            } else if (webViewType == WebViewType.CHROME || webViewType == WebViewType.MT_WEB_VIEW_SYSTEM) {
                WebView.setWebContentsDebuggingEnabled(true);
            }
        }

        triggerResourceControlWhenPreloadUsed(isFirstWebViewCreated(), webViewType);

        if (!firstWebViewCreated) {
            MPListenerManager.getInstance().launchEventListener.onEvent("native_webview_init_end");
        }

        long duration = result.getWebViewInitializationDuration();
        new WebViewInitReporter(appId).report(webViewType, duration, MSCWebView.SourceType.PAGE.toString(), start);

        firstWebViewCreated = true;
        if (webViewType == WebViewType.MT_WEB_VIEW_SYSTEM) {
            // MTWebView 降级成系统 WebView, 此时直接传 CHROME 类型
            sGlobalFirstWebViewCreated.put(WebViewType.CHROME, true);
        } else {
            sGlobalFirstWebViewCreated.put(webViewType, true);
        }

        if (TextUtils.isEmpty(MSCAppModule.WEB_VIEW_VERSION)) {
            MSCAppModule.WEB_VIEW_VERSION = getCurrentWebViewVersion(result.getUserAgentString(), webViewType);
        }
        setGlobalWebViewType(webViewType);
        setBackgroundInited(true);
        PerfTrace.online().end(traceName).report();
        return result;
    }

    private void triggerResourceControlWhenPreloadUsed(boolean isFirstWebView, WebViewType webViewType) {
        // 仅对自研引擎上报分段预热
        if (webViewType != WebViewType.MT_WEB_VIEW) {
            return;
        }
        if (MSCHornPreloadConfig.enableControlWebViewSegmentPreload()) {
            if (PreloadWebViewManager.getInstance().hasTriggerFirstSegmentPreload()) {
                ResourceManager.getInstance().traceWhenPreloadUsed("MSC", "webViewSegmentPreload1", "webview");
            }
            if (PreloadWebViewManager.getInstance().hasTriggerSecondSegmentPreload()) {
                ResourceManager.getInstance().traceWhenPreloadUsed("MSC", "webViewSegmentPreload2", "webview");
            }
            if (PreloadWebViewManager.getInstance().hasTriggerThirdSegmentPreload()) {
                ResourceManager.getInstance().traceWhenPreloadUsed("MSC", "webViewSegmentPreload3", "webview");
            }
        }
    }

    private void setGlobalWebViewType(WebViewType webViewType) {
        if (MSCAppModule.WEB_VIEW_TYPE == null) {
            MSCAppModule.WEB_VIEW_TYPE = webViewType;
            MSCLog.i(TAG, "first set webViewType, webViewType:" + CommonTags.getWebViewType(webViewType));
        } else {
            MSCLog.i(TAG, "set webViewType fail, last webViewType:" + CommonTags.getWebViewType(MSCAppModule.WEB_VIEW_TYPE)
                + " current webViewType:" + CommonTags.getWebViewType(webViewType));
        }
    }

    //TBS/04551

    /**
     * 从webViewUA中获得版本号
     * 例如: Mozilla/5.0 (Linux; Android 10; MI 8 Build/QKQ1.190828.002; wv) AppleWebKit/537.36 (KHTML, like Gecko) Version/4.0 Chrome/80.0.3987.99 Mobile Safari/537.36
     * Chrome 版本号是 80.0.3987.99
     * <p>
     * 获取X5版本号时，内容为X5内核版本号（而非SDK版本号），在UA中这一节形式如 "TBS/043906"
     *
     * @param webViewUA
     * @return
     */
    public final String getCurrentWebViewVersion(String webViewUA, WebViewType webViewType) {
        // 包含chrome和MTWebView，MTWebView目前与chrome格式相同，没有标识
        return getVersionFromUA(webViewUA, "chrome");
    }

    /**
     * 用于获取UA中某一节的版本号
     */
    public final String getVersionFromUA(String webViewUA, String sectionName) {
        if (TextUtils.isEmpty(webViewUA)) {
            return null;
        }
        int startPosition = webViewUA.toLowerCase().indexOf(sectionName);
        if (startPosition != -1) {
            String targetSection = webViewUA.substring(startPosition);
            int endPosition = targetSection.indexOf(' ');
            if (endPosition == -1) { // 如果chrome字段在最后,这里需要特判一下
                endPosition = targetSection.length();
            }
            targetSection = targetSection.substring(0, endPosition);
            String[] targetSectionSplit = targetSection.split("/");
            if (targetSectionSplit.length < 2) {
                return null;
            }
            return targetSectionSplit[1];
        }
        return null;
    }

    public void cacheFirstWebView(final Context context, WebViewCreateScene createScene, String appId) {
        cacheFirstWebView(context, createScene, appId, null);
    }

    /**
     * 仅在本小程序未创建过WebView时缓存一个，页面显示后的WebView缓存将由AppPageManager.cacheOneAppPage()接手
     */
    public void cacheFirstWebView(final Context context, WebViewCreateScene createScene, String appId, Map<String, Object> tags) {
        if (firstWebViewCreated) {
            return;
        }
        MSCExecutors.runOnUiThread(new Runnable() {
            @Override
            public void run() {
                if (firstWebViewCreated) {
                    return;
                }
                if (mCachedWebView == null) {
                    boolean isCreateWebViewSuccess = true;
                    long start = System.currentTimeMillis();
                    if (createScene == WebViewCreateScene.PRE_CREATE) {
                        PreloadInjection.notifyPreloadStarted(WebViewFirstPreloadStateManager.PreloadState.WEBVIEW_PRECREATE.name(),
                                PreloadWebViewManager.getInstance().getPreloadWebViewType());
                    }
                    try {
                        mCachedWebView = getNewWebView(new MutableContextWrapper(context), appId);
                        mCachedWebView.setCreateScene(createScene);
                        WebViewPreloadReporter.getInstance().reportWebViewPartTwoTime();
                    } catch (Exception e) {
                        //只是触发cache，失败不影响运行，忽略
                        isCreateWebViewSuccess = false;
                    }
                    if (createScene == WebViewCreateScene.PRE_CREATE) {
                        PreloadInjection.notifyPreloadEnd(WebViewFirstPreloadStateManager.PreloadState.WEBVIEW_PRECREATE.name(),
                                PreloadWebViewManager.getInstance().getPreloadWebViewType());
                        WebViewPreloadReporter.getInstance().reportWebViewPreCreateDuration(PreloadWebViewManager.getInstance().isKNBInit(),
                                System.currentTimeMillis() - start, isCreateWebViewSuccess, tags);
                        WebViewFirstPreloadStateManager.getInstance().updateStateAfterPreload();
                    }
                }
            }
        });
    }

    /**
     * 分段预热-getDefaultUserAgent
     */
    public void getDefaultUserAgentForMTWebView() {
        if (hasGetDefaultUserAgentForMTVebView) {
            MSCLog.i(TAG, "getDefaultUserAgent done");
            return;
        }
        MSCExecutors.runOnUiThread(new Runnable() {
            @Override
            public void run() {
                long start = System.currentTimeMillis();
                // 现逻辑，只有自研内核添加getDefaultUserAgentWebViewIdleHandler
                MTWebSettings.getDefaultUserAgent(MSCEnvHelper.getContext());
                long duration = System.currentTimeMillis() - start;
                WebViewPreloadReporter.getInstance().reportWebViewPartGetDefaultUserAgentTime(duration);
                hasGetDefaultUserAgentForMTVebView = true;
            }
        });
    }

//    private boolean mRelease = false;

    public void release() {
//        mRelease = true;
        if (mCachedWebView != null) {
            MSCExecutors.runOnUiThread(new Runnable() {
                @Override
                public void run() {
                    if (mCachedWebView != null) {
                        mCachedWebView.onDestroy();
                        mCachedWebView = null;
                    }
                }
            });
        }
//        if (mLastDestroyWebView != null) {
//            mLastDestroyWebView.destroy();
//            mLastDestroyWebView = null;
//        }
    }

    public void releaseWebView(View webView) {
        if (releaseIWebViewWithSpecificWebView(mCachedWebView, webView)) {
            mCachedWebView = null;
        }
    }

    public static boolean releaseIWebViewWithSpecificWebView(IWebView iWebView, View view) {
        if (iWebView != null && iWebView.getWebView() == view) {
            iWebView.onDestroy();
            MSCLog.d(null, "releaseIWebViewIfWebViewCrashed iWebView:", iWebView, ", view: ", view);
            return true;
        }
        return false;
    }

    public boolean isFirstWebViewCreated() {
        return firstWebViewCreated;
    }

    /**
     * MtWebView使用的几种情况，使用前提是当前MtWebView环境已完备
     * （1）小程序Id允许使用MtWebView
     * （2）小于23的版本
     *
     * @return
     */
    public boolean useMtWebViewByAppId(String appId) {
        MSCLog.i(TAG, "useMtWebViewByAppId", appId);
        // 基础库预热时会预创建WebView，此时获取不到appId，使用兜底值控制是否使用自研内核
        String finalAppId = appId == null ? PreloadWebViewManager.PRELOAD_WEBVIEW : appId;
        if (DebugHelper.useMtWebView != null) {
            return DebugHelper.useMtWebView;
        }
        return MTWebViewMSCConfigManager.isEnableMTWebView(finalAppId);
    }

    public enum WebViewCreateScene {
        // todo 来源需要再细分
        PRE_CREATE,
        CREATE_AT_ONCREATE,
        /**
         * Activity onCreate时预创建
         */
        CREATE_AT_PAGE_LAUNCH,
        // todo 来源需要再细分
        CREATE_AT_NO_CACHE,
        /**
         * 业务重写MSCActivity 调用cacheFirstWebView方法触发
         */
        CREATE_BY_USER
    }
}
