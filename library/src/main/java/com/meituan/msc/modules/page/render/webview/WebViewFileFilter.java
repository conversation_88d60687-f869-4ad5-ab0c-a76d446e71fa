package com.meituan.msc.modules.page.render.webview;

import android.annotation.SuppressLint;
import android.content.Context;
import android.net.Uri;
import android.os.Build;
import android.support.annotation.Nullable;
import android.text.TextUtils;
import android.webkit.URLUtil;

import com.meituan.dio.easy.DioFile;
import com.meituan.msc.common.resource.IWebResponseBuild;
import com.meituan.msc.common.utils.FileScope;
import com.meituan.msc.common.utils.FileUtil;
import com.meituan.msc.common.utils.MSCStorageUtil;
import com.meituan.msc.common.utils.MimeHelper;
import com.meituan.msc.common.utils.SequenceInputStream;
import com.meituan.msc.lib.interfaces.IFileModule;
import com.meituan.msc.modules.devtools.DebugHelper;
import com.meituan.msc.common.ensure.ResFilesEnsure;
import com.meituan.msc.modules.engine.MSCHornRollbackConfig;
import com.meituan.msc.modules.manager.MSCModule;
import com.meituan.msc.modules.reporter.MSCLog;
import com.meituan.msc.util.perf.PerfTrace;

import java.io.IOException;
import java.io.InputStream;
import java.io.StringBufferInputStream;
import java.util.ArrayList;
import java.util.Enumeration;
import java.util.HashMap;
import java.util.Iterator;
import java.util.List;
import java.util.Map;

import static com.meituan.msc.common.perf.PerfEventConstant.INTERCEPT_RESOURCE;


/**
 * WebView文件过滤器
 */
public class WebViewFileFilter {
    /**
     * @param context          全局对象
     * @param fileModule     小程序数据module
     * @param url              加载地址
     * @param webResponseBuild 响应接口
     * @return 执行结果
     * @api 组件标准化注释_标准API
     * 拦截WebView资源加载事件
     * 1 网络地址直接返回
     * 2 如果是file：//需要检查文件的scope
     * 3 如果文件是wdfile：//需要检查文件scope
     */
    @SuppressLint("SdCardPath")
    public static Object interceptResource(Context context, IFileModule fileModule,
                                           String url, IWebResponseBuild webResponseBuild,
                                           @Nullable ResFilesEnsure resFilesEnsure, boolean asyncSubPkg) {
        if (URLUtil.isNetworkUrl(url) || TextUtils.isEmpty(url)) return null;//如果路径是网络地址的话 不拦截；
        PerfTrace.begin(INTERCEPT_RESOURCE).arg("url", url);
        try {
            if (DebugHelper.debugWebView) {//用于webview 调试
                if (url.startsWith(MSCStorageUtil.SCHEME_LOCAL_FILE + MSCStorageUtil.getMSCPath(context))) {
                    DioFile res = new DioFile(url.substring(MSCStorageUtil.SCHEME_LOCAL_FILE.length()));
                    return getResourceFromDioFile(fileModule, url, MimeHelper.getMIME(url), res, webResponseBuild);
                }
            }
            // 绝对路径的file域 问题 file:///fix.js
            // 同步ios，android仅处理file开头的文件。
            if (url.startsWith("file://")) {
                return getFileProtocolResource(context, fileModule, url, webResponseBuild, resFilesEnsure, asyncSubPkg);
            }

            String localFile = FileUtil.getResourcePath(url, fileModule);
            if (localFile != null) {
                return getResourceFromResUrl(context, fileModule, url, localFile, webResponseBuild);
            }
            return null;
        } catch (IllegalPathAccessException e) {
            return getResource(403, null, null, webResponseBuild);
        } finally {
            PerfTrace.end(INTERCEPT_RESOURCE);
        }
    }

    /**
     * 获取绝对路径的资源
     *
     * @param context
     * @param fileModule
     * @param url
     * @param webResponseBuild
     * @return
     */
    private static Object getFileProtocolResource(Context context, IFileModule fileModule,
                                                  String url, IWebResponseBuild webResponseBuild,
                                                  @Nullable ResFilesEnsure resFilesEnsure,
                                                  boolean asyncSubPkg) throws IllegalPathAccessException {
        String pathWithoutFile = Uri.parse(url).getPath();
        if (pathWithoutFile == null) {
            return null;
        }

        InputStream is;
        int index = url.indexOf("??");
        if (index > 0) {
            // 多文件
            MSCLog.d("WebViewFileFilter", "load file in combo mode: ", url);
            // combo模式，一次请求返回多个拼接在一起的文件 https://km.sankuai.com/page/847678042
            String tail = url.substring(index + 2);
            String[] files = tail.split(",");
            List<InputStream> isList = new ArrayList<>();

            if (!pathWithoutFile.endsWith("/")) {
                pathWithoutFile += "/";
            }
            // 默认先不开
            if (null != resFilesEnsure) {
                resFilesEnsure.ensure(asyncSubPkg, pathWithoutFile, files);
            }
            for (String file : files) {
                file = file.trim();
                if (file.startsWith("/")) {
                    file = file.substring(1);
                }
                String comboFullPath = pathWithoutFile + file;
                InputStream singleFileStream = getSingleFileProtocolResource(context, fileModule, comboFullPath);
                if (singleFileStream != null) {
                    if (!isList.isEmpty()) {
                        // 与前端的约定，combo文件间以换行分隔
                        isList.add(new StringBufferInputStream("\n"));
                    }
                    isList.add(singleFileStream);
                } else {
                    //TODO 上报错误
                }
            }
            Iterator<InputStream> it = isList.iterator();
            // 将多个输入流合为1个，因SequenceInputStream写法较旧，需要传入Enumeration而非Iterator，作用一致
            is = new SequenceInputStream(new Enumeration<InputStream>() {
                @Override
                public boolean hasMoreElements() {
                    return it.hasNext();
                }

                @Override
                public InputStream nextElement() {
                    return it.next();
                }
            });
        } else {
            // 单文件
            // 默认先不开
            if (null != resFilesEnsure) {
                resFilesEnsure.ensure(asyncSubPkg, null, new String[]{pathWithoutFile});
            }
            is = getSingleFileProtocolResource(context, fileModule, pathWithoutFile);
        }

        return getResource(200, MimeHelper.getMIME(url), is, webResponseBuild);
    }

    @Nullable
    private static InputStream getSingleFileProtocolResource(Context context, IFileModule fileModule, String fullPath)
            throws IllegalPathAccessException {
        try {
            PerfTrace.begin("getFileResource").arg("file", fullPath);
            DioFile res;
            String path;
            boolean isPackagePath = false;
            if (fullPath.startsWith("/data/user/")) {
                res = new DioFile(fullPath);
                if (MSCHornRollbackConfig.enableExternalAppStorageLimit()) {
                    boolean isExternalApp = ((MSCModule) fileModule).getRuntime().getMSCAppModule().getExternalApp();
                    if (isExternalApp) {
                        throw new IllegalPathAccessException("external app can not access local file: " + fullPath);
                    }
                }
            } else if ((path = FileUtil.getUsrStoreAndTempPathWithoutSchema(fullPath.substring(1), fileModule)) != null) {
                res = new DioFile(path);
                if (res == null) {
                    return null;
                }
            } else {
                isPackagePath = true;
                res = fileModule.getPackageResourceFile(fullPath);
            }
            if (res == null) {
                return null;
            }
            // 文件只能位于小程序自身目录或者framework 包内文件不做检测
            if (!isPackagePath && !FileScope.accessFile(res, fileModule.getMiniAppDir())) {
                throw new IllegalPathAccessException();
            }
            if (res.isDirectory()) {
                return null;    //打开一个本地 html 文件的时候就是这个协议，通常用来调试什么的
            }
            if (!res.exists() && !res.isFile()) {
                return null;
            }
            try {
                return res.getInputStream();
//                return new FilterInputStream(res.getInputStream()) {
//                    @Override
//                    public int read() throws IOException {
//                        Trace.beginSection("readFile: " + fullPath);
//                        try {
//                            return super.read();
//                        } finally {
//                            Trace.endSection();
//                        }
//                    }
//
//                    @Override
//                    public int read(byte[] b) throws IOException {
//                        Trace.beginSection("readFile: " + fullPath + ", " + b.length + "/" + res.length());
//                        try {
//                            return super.read(b);
//                        } finally {
//                            Trace.endSection();
//                        }
//                    }
//
//                    @Override
//                    public int read(byte[] b, int off, int len) throws IOException {
//                        Trace.beginSection("readFile: " + fullPath + ", len: " + len + "/" + res.length());
//                        try {
//                            return super.read(b, off, len);
//                        } finally {
//                            Trace.endSection();
//                        }
//                    }
//                };
            } catch (IOException e) {
                // todo mmp 旧埋点
                //FileUtil.reportReadDioContentFailed(null, res.getPath(), e, null, mscAppModule.getAppId());
                MSCLog.e(e);
                return null;
            }
        } finally {
            PerfTrace.end("getFileResource");
        }
    }

    private static Object getResourceFromDioFile(IFileModule fileModule, String url, String mime, DioFile res, IWebResponseBuild webResponseBuild) {
        if (!res.exists() && !res.isFile()) {
            return null;
        }
        InputStream is;
        try {
            is = res.getInputStream();
        } catch (IOException e) {
            // todo mmp 旧埋点
            MSCLog.e(e);
            return null;
        } finally {
        }
        return getResource(200, mime, is, webResponseBuild);
    }

    private static Object getResource(int code, String mine, InputStream is, IWebResponseBuild webResponseBuild) {
        Map<String, String> headers = null;
        if (code == 403 || is == null) {
            mine = "text/html";
            is = new InputStream() {
                @Override
                public int read() throws IOException {
                    return -1;
                }
            };
        }
        if (Build.VERSION.SDK_INT >= Build.VERSION_CODES.LOLLIPOP) {
            headers = new HashMap<>();
            headers.put("Cache-Control", "no-cache, no-store, must-revalidate");
            headers.put("Pragma", "no-cache");
            headers.put("Expires", "0");
            headers.put("Access-Control-Allow-Origin", "*");
        }
        return webResponseBuild.getResource(mine, headers, is);
    }

    /**
     * 获取通过wdfile://协议获取的资源
     *
     * @param context
     * @param fileModule
     * @param url
     * @param webResponseBuild
     * @return
     */
    public static Object getResourceFromResUrl(Context context, IFileModule fileModule,
                                               String url,
                                               String resName,
                                               IWebResponseBuild webResponseBuild) throws IllegalPathAccessException {
        DioFile res = new DioFile(resName);
        if (!FileScope.accessFile(res, fileModule.getMiniAppDir())) {
            throw new IllegalPathAccessException();
        }
        if (!res.exists() && !res.isFile()) {
            return null;
        }
        return getResourceFromDioFile(fileModule, url, "image/*", res, webResponseBuild);
    }

    /**
     * 表示传入了有效但权限不允许的路径，不能由WebView自行处理，需拒绝
     */
    public static class IllegalPathAccessException extends Exception {
        public IllegalPathAccessException(){
            super();
        }

        public IllegalPathAccessException(String msg) {
            super(msg);
        }
    }
}

