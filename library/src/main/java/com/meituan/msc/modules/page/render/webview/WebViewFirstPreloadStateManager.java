package com.meituan.msc.modules.page.render.webview;

import com.meituan.msc.modules.reporter.MSCLog;

/**
 * 用于管理首个预加载的WebView状态，应支持预加载多个WebView？
 */
public class WebViewFirstPreloadStateManager {

    private static final String TAG = "WebViewFirstPreloadStateManager";
    private static volatile WebViewFirstPreloadStateManager mInstance;

    public static WebViewFirstPreloadStateManager getInstance() {
        if (mInstance == null) {
            synchronized (WebViewFirstPreloadStateManager.class) {
                if (mInstance == null) {
                    mInstance = new WebViewFirstPreloadStateManager();
                }
            }
        }
        return mInstance;
    }

    private PreloadState webViewPreloadState = PreloadState.NO_PRELOAD;

    public void updateStateAfterPreload() {
        MSCLog.i(TAG, "updateStateAfterPreload,currentState:", webViewPreloadState);
        switch(webViewPreloadState){
            case NO_PRELOAD:
                webViewPreloadState = PreloadState.BACKGROUND_INIT;
                break;
            case BACKGROUND_INIT:
                webViewPreloadState = PreloadState.WEBVIEW_PRECREATE;
                break;
            // WEBVIEW_PREINJECT状态需要与WebView实例绑定
            default:
                break;
        }
    }

    public void raiseStateTo(PreloadState preloadState) {
        MSCLog.i(TAG, "raiseStateTo", preloadState);
        webViewPreloadState = preloadState;
    }

    public PreloadState getPreloadState() {
        return webViewPreloadState;
    }

    public String getPreloadStateStr() {
        return webViewPreloadState.toString();
    }

    public enum PreloadState {
        NO_PRELOAD,
        BACKGROUND_INIT,
        WEBVIEW_PRECREATE,
        WEBVIEW_PREINJECT
    }
}
