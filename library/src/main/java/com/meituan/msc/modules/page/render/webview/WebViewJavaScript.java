package com.meituan.msc.modules.page.render.webview;
/**
 * 基础库消息转发到WebView数据结构: (1)messagePort发送的json格式，(2)evaluateJavaScript的JS代码。
 * https://km.sankuai.com/collabpage/2043587985
 */
public abstract class WebViewJavaScript {
    /**
     * 转message port支持的json格式字符串，
     * @return json字符串
     */
    public abstract String buildWebMessageString(boolean needTrace);

    /**
     * 转JS字符串
     * @return JS代码字符串
     */
    public abstract String buildJavaScriptString(boolean needTrace);

    /**
     * 排队消息被消费时机埋点
     */
    public abstract void markScriptPendingTime();

    /**
     * @return 当前数据是否支持用messagePort发送
     */
    public abstract boolean isSupportMessagePort();

    public abstract int length();

    /**
     * WebViewBridge 调用JS模块名
     * @return 调用JS模块名
     */
    public abstract String getModuleName();

    /**
     * WebViewBridge 调用JS方法名
     * @return 调用JS方法名
     */
    public abstract String getMethodName();
}
