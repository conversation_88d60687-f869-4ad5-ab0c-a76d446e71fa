package com.meituan.msc.modules.page.render.webview;

import static com.meituan.msc.common.perf.PerfEventConstant.SEND_INITIAL_DATA_TO_PAGE;
import static com.meituan.msc.common.perf.PerfEventConstant.SEND_INITIAL_DATA_TO_PAGE_END;

import android.webkit.ValueCallback;

import com.meituan.msc.jse.bridge.JavaScriptModule;
import com.meituan.msc.util.perf.PerfTrace;

import org.json.JSONArray;
import org.json.JSONObject;

/**
 * Native -> Page
 * Created by letty on 2022/3/11.
 **/
public class WebViewMethods {
    interface WebViewPageListener extends JavaScriptModule {
        /**
         * @api 组件标准化注释_标准API
         * Page 路由启动，，需在PageReady之后发出
         */
        void onPageStart(String pagePath, String packageName);

        /**
         * @api 组件标准化注释_标准API
         * Page 客户端 -> 前端 要求前端清理状态，准备复用，前端将回退至PageReady状态
         * 发送此消息后直至收到FIRST_SCRIPT消息前，接收到的前端消息均可认为是上一页面发出的，应忽略
         */
        void onPageRecycle();

        /**
         * @api 组件标准化注释_标准API
         * Page 向Page层预加载页面相关文件，但不启动具体页面 客户端 -> 前端，需在PageReady之后、onPageStart之前发出
         */
        void onPagePreload(JSONObject data);

        // scrollView 支持点击回到顶部
        void onUserTapBackToTop();

        /**
         * @api 组件标准化注释_标准API
         * Page 客户端 -> 前端 通知前端客户端进行白屏监测时机，前端可进行页面状态检查
         */
        void onCheckWhiteScreen();

        /**
         * 通知视图层开始检测FFP
         */
        void startFFPDetect();

        /**
         * 通知视图层不进行检测FFP
         */
        void disableFFPDetect();

        /**
         * 预创建空白webview场景触发bundle提前注入
         * @param data 参数
         */
        void onResourcePreload(JSONObject data);
    }

    static void onInitialData(WebViewBridge webViewBridge, String data, boolean isPreCreate) {
        if (isPreCreate) {
            disableFFPDetect(webViewBridge);
        }
        JSONArray arguments = new JSONArray();
        arguments.put(data);
        webViewBridge.invokeMethod("WebViewPageData", "onInitialData", arguments, new ValueCallback<String>() {
            @Override
            public void onReceiveValue(String value) {
                PerfTrace.online().instant(SEND_INITIAL_DATA_TO_PAGE_END).report();
            }
        }, new WebViewEvaluateJavascriptListener() {
            @Override
            public void onStart() {
                PerfTrace.online().instant(SEND_INITIAL_DATA_TO_PAGE).report();
            }
        });
    }

    static void onPageStart(WebViewBridge webViewBridge, String pagePath, String packageName) {
        webViewBridge.getJSModule(WebViewPageListener.class).onPageStart(pagePath, packageName);
    }

    static void onPageRecycle(WebViewBridge webViewBridge) {
        webViewBridge.getJSModule(WebViewPageListener.class).onPageRecycle();
    }

    static void onPagePreload(WebViewBridge webViewBridge, JSONObject data) {
        webViewBridge.getJSModule(WebViewPageListener.class).onPagePreload(data);
    }

    static void onUserTapBackToTop(WebViewBridge webViewBridge) {
        webViewBridge.getJSModule(WebViewPageListener.class).onUserTapBackToTop();
    }

    static void onCheckWhiteScreen(WebViewBridge webViewBridge) {
        webViewBridge.getJSModule(WebViewPageListener.class).onCheckWhiteScreen();
    }

    static void startFFPDetect(WebViewBridge webViewBridge) {
        webViewBridge.getJSModule(WebViewPageListener.class).startFFPDetect();
    }

    static void disableFFPDetect(WebViewBridge webViewBridge) {
        webViewBridge.getJSModule(WebViewPageListener.class).disableFFPDetect();
    }

    static void onResourcePreload(WebViewBridge webViewBridge, JSONObject data) {
        webViewBridge.getJSModule(WebViewPageListener.class).onResourcePreload(data);
    }
}
