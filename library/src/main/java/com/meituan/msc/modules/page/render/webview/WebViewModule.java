package com.meituan.msc.modules.page.render.webview;

import static com.meituan.msc.common.perf.PerfEventConstant.RECEIVE_SERVICE_INITIAL_DATA;

import android.support.annotation.Nullable;
import android.webkit.ValueCallback;

import com.meituan.dio.easy.DioFile;
import com.meituan.msc.common.executor.MSCExecutors;
import com.meituan.msc.common.framework.MPListenerManager;
import com.meituan.msc.common.perf.PerfEventConstant;
import com.meituan.msc.common.utils.DisplayUtil;
import com.meituan.msc.common.utils.JsonUtil;
import com.meituan.msc.common.utils.MSCStorageUtil;
import com.meituan.msc.jse.bridge.ICallFunctionContext;
import com.meituan.msc.jse.bridge.LazyParseJSONArray;
import com.meituan.msc.modules.container.IContainerDelegate;
import com.meituan.msc.modules.devtools.DebugHelper;
import com.meituan.msc.modules.engine.MSCHornRollbackConfig;
import com.meituan.msc.modules.engine.async.IBaseEngine;
import com.meituan.msc.modules.exception.ExceptionsInterface;
import com.meituan.msc.modules.exception.MSCExceptionsManagerModule;
import com.meituan.msc.modules.manager.MSCMethod;
import com.meituan.msc.modules.manager.MSCModule;
import com.meituan.msc.modules.manager.MSCRuntimeException;
import com.meituan.msc.modules.manager.ModuleName;
import com.meituan.msc.modules.page.IPageModule;
import com.meituan.msc.modules.page.render.BaseRenderer;
import com.meituan.msc.modules.page.render.RendererType;
import com.meituan.msc.modules.reporter.MSCLog;
import com.meituan.msc.modules.reporter.prexception.AppPageState;
import com.meituan.msc.modules.reporter.prexception.AppServiceState;
import com.meituan.msc.modules.service.IComboEvaluation;
import com.meituan.msc.modules.service.MSCFileUtils;
import com.meituan.msc.util.perf.PerfTrace;
import com.meituan.msi.provider.IContainerStageProvider;

import org.json.JSONArray;
import org.json.JSONException;
import org.json.JSONObject;

import java.util.Collection;

/**
 * Service/Page -> Native
 * https://km.sankuai.com/page/**********
 * Created by letty on 2022/3/7.
 **/
@ModuleName(name = WebViewModule.TAG)
public class WebViewModule extends MSCModule {
    static final String TAG = "WebView";
    private final String logTag = TAG + hashCode();

    private void log(String args) {
        MSCLog.i(logTag, args);
    }

    private WebViewBridge mWebViewBridge;
    private MSCWebViewRenderer mMSCWebViewRenderer;

    public WebViewModule setWebViewBridge(WebViewBridge webViewBridge) {
        mWebViewBridge = webViewBridge;
        return this;
    }

    public WebViewModule setMSCWebViewRenderer(MSCWebViewRenderer MSCWebViewRenderer) {
        mMSCWebViewRenderer = MSCWebViewRenderer;
        return this;
    }

    public WebViewBridge getWebViewBridge() {
        return mWebViewBridge;
    }

    private void recordInstantEvent(String name) {
        mMSCWebViewRenderer.getPerfEventRecorder().recordInstantEvent(name);
    }

    /**
     * Page 开始首帧渲染，前端 -> 客户端，启动时间统计的截止点
     */
    @MSCMethod(isSync = true)
    public void onFirstRender() {
        recordInstantEvent(PerfEventConstant.ON_FIRST_RENDER);
        log("onFirstRender");
        mMSCWebViewRenderer.getReporter().markPoint("first.render");
        mMSCWebViewRenderer.onFirstRender(null);
        mMSCWebViewRenderer.setPageState(AppPageState.FIRST_RENDER);
    }

    @MSCMethod(isSync = true)
    public void receive_sync_config(String data) {
    }

    /**
     * 1、客户端透传 setData Service -> 客户端 -> Page
     * 2、客户端处理初始渲染缓存
     */
    @MSCMethod(isSync = true)
    public void sendInitialData(String data) {
        log("sendInitialData");
        PerfTrace.online().instant(RECEIVE_SERVICE_INITIAL_DATA).report();
        MPListenerManager.getInstance().launchEventListener.onEvent("native_received_first_data_from_service");
        mMSCWebViewRenderer.saveInitialData(data);
        WebViewMethods.onInitialData(mWebViewBridge, data, mMSCWebViewRenderer.isPreCreate());
    }

    /**
     * 使用快照时，firstRender显示的内容不可交互，将在此事件时转为可交互结构，Page -> 客户端
     */
    @MSCMethod(isSync = true)
    public void onPageInteractive() {
        log("onPageInteractive");
        mMSCWebViewRenderer.setPageState(AppPageState.SNAPSHOT_INTERACTIVE);
    }


    /**
     * 获取webview的宽度，
     */
    @MSCMethod(isSync = true)
    public String getWebViewWidth() {
        log("getWebViewWidth");
        MSCWebView webView = mMSCWebViewRenderer.getMSCWebView();
        return String.valueOf(DisplayUtil.toWebValueCeil(webView.getWidth()));
    }

    /**
     * HTML中的body标签处理完毕
     */
    @MSCMethod(isSync = true)
    public void onHTMLLoaded(String type) {
        MSCLog.i(TAG, "onHTMLLoaded");
        if (!MSCHornRollbackConfig.get().getConfig().rollbackOnPageFinishedInAdvanced) {
            mMSCWebViewRenderer.onPageFinished(null, "onHTMLLoaded_" + type);
        }
    }

    /**
     * Page 层执行第一行js代码，前端 -> 客户端，
     */
    @MSCMethod(isSync = true)
    public void onFirstScript(String featureSupportStr) {
        PerfTrace.online().instant(PerfEventConstant.RECEIVE_PAGE_FIRST_SCRIPT).report();
        log("onFirstScript");
        mMSCWebViewRenderer.onFirstScript();
        mMSCWebViewRenderer.isBasePackageLoaded = true;
        mMSCWebViewRenderer.getReporter().markPoint("first.script");
        mMSCWebViewRenderer.setPageState(AppPageState.FIRST_SCRIPT);
        if (!MSCHornRollbackConfig.isRollbackMessagePort()) {
            String appId = "";
            try {
                appId = getRuntime().getAppId();
            } catch (MSCRuntimeException e) {
                MSCLog.e(TAG, e, "runtime is null when onFirstScript");
                getRuntime().getNativeExceptionHandler().handleException(e);
                return;
            }
            if (!MSCHornRollbackConfig.isRollbackMessagePortWithAppId(appId)) {
                try {
                    JSONObject featureSupportJson = new JSONObject(featureSupportStr);
                    boolean isRegistMessageChannelListener = featureSupportJson.optBoolean("isRegistMessageChannelListener");
                    log("messagePort#isRegistMessageChannelListener: " + isRegistMessageChannelListener);
                    if (isRegistMessageChannelListener) {
                        if (android.os.Build.VERSION.SDK_INT >= android.os.Build.VERSION_CODES.M && mMSCWebViewRenderer.webView != null) {
                            MSCLog.i(TAG, "messagePort#transferPort enabled");
                            MSCExecutors.runOnUiThread(new Runnable() {
                                @Override
                                public void run() {
                                    mMSCWebViewRenderer.webView.transferPortToJavaScript();
                                    MSCLog.i(TAG, "messagePort#transferPort executed");
                                }
                            });
                        }
                    } else {
                        MSCLog.i(TAG, "messagePort#transferPort disabled");
                        mMSCWebViewRenderer.webView.messagePortClose();
                    }
                } catch (JSONException e) {
                    MSCLog.e(TAG, "messagePort#onFirstScript " + e);
                    getRuntime().getNativeExceptionHandler().handleException(e);
                }
            }
        }
    }
    /**
     * Page 层执行页面回收完成，前端 -> 客户端
     */
    @MSCMethod(isSync = true)
    public void onPageRecycleFinished() {
        log("onPageRecycleFinished");
        mMSCWebViewRenderer.setWebViewRecycleCmdFinished(true);
    }

    /**
     * Service FST firstScreenTime 秒开率终点，前端 -> 客户端，
     */
    public static final String CUSTOM_EVENT_FIRST_SCREEN = "custom_event_first_screen";

    @MSCMethod(isSync = true)
    public void onFirstScreen(long fstTimestamp) {
        log("onFirstScreen");
        int pageId = mMSCWebViewRenderer.getViewId();
        IContainerDelegate containerDelegate = getRuntime().getContainerManagerModule().getContainerDelegateByPageId(pageId);
        if (containerDelegate != null) {
            containerDelegate.onPageFirstScreen(fstTimestamp, pageId);
        } else {
            log("onPageFirstScreen: containerDelegate is null.");
        }
    }

    /**
     * 透传调用 WebView Page中各模块
     */
    @MSCMethod(isSync = true)
    public void invokeWebViewModule(ICallFunctionContext context, String module, String method, String args) {
        if (mWebViewBridge != null) {
            mWebViewBridge.invokeMethod(context, module, method, new LazyParseJSONArray(args));
        } else {
            throw new RuntimeException("invokeWebViewModule without handler" + module + method);
        }
    }

    @MSCMethod(isSync = true)
    public void onSinkModeHotZone(String params) {
        log("onSinkModeHotZone: " + params);
        mMSCWebViewRenderer.onSinkModeHotZone(params);
    }

    @MSCMethod(isSync = true)
    public String importScripts(JSONArray files, String params) {
        log("importScripts");
        return MSCFileUtils.importScript(JsonUtil.parseToStringArray(files), params, getRuntime(), mIComboEvaluation);
    }

    @MSCMethod
    public void reportException(JSONObject data) {
        log("reportException");
        if (data == null) {
            return;
        }
        if (MSCExceptionsManagerModule.isFatalError(data)) {
            mMSCWebViewRenderer.onFatalError(data);
        }
        IPageModule pageModule = getParentModule() instanceof IPageModule ? (IPageModule) getParentModule() : null;
        getModule(ExceptionsInterface.class).reportException(data, pageModule);
    }

    @MSCMethod(isSync = true)
    public void appLaunch(long timeStamp, boolean isFirstPage) {
        log("appLaunch:" + timeStamp + "," + isFirstPage);
        mMSCWebViewRenderer.setServiceState(AppServiceState.APP_LAUNCH);
    }

    @MSCMethod(isSync = true)
    public void updateContainerStage(String stage) {
        if (MSCHornRollbackConfig.enableReportAPIPerformanceStatisticData()) {
            log("updateContainerStage:" + stage);
            mMSCWebViewRenderer.updateContainerStage(stage);
        }
    }


    private final IBaseEngine mIComboEvaluation = new BaseEngineImpl();

    protected void evaluateJsFilesCombo(Collection<DioFile> files, @Nullable ValueCallback<String> resultCallback) {
        if (files == null) {
            MSCLog.i(TAG, "Cancel_Evaluate_JS_File_Combo_When_Files_Is_Null");
            return;
        }
        if (DebugHelper.debugWebView) {
            //debugWebView 注入script标签是个异步的操作。
            for (DioFile file : files) {
                //document.write 效率低 不建议使用
                mWebViewBridge.evaluateJavascript(String.format(TemplateHelper.APPEND_SCRIPT, MSCStorageUtil.SCHEME_LOCAL_FILE + file.getAbsolutePath(), false),
                        resultCallback);
            }
        } else {
            String finalS = MSCFileUtils.concatComboFileString(files, getRuntime(), resultCallback);
            mWebViewBridge.evaluateJavascript(finalS, resultCallback);
        }
    }

    @Override
    public void onDestroy() {
        super.onDestroy();
    }

    /**
     * 和逻辑层同步的接口。
     */
    private class BaseEngineImpl implements IBaseEngine, IComboEvaluation {
        @Override
        public void evaluateJsFilesCombo(Collection<DioFile> files, String source, @Nullable ValueCallback<String> resultCallback) {
            WebViewModule.this.evaluateJsFilesCombo(files, resultCallback);
        }

        @Override
        public void evaluateJsFilesComboThrow(Collection<DioFile> files, String source, @Nullable ValueCallback<String> resultCallback) {
            // page层异步桥。暂时不需要支持。
        }

        @Override
        public void runOnJSQueueThreadSafe(Runnable runnable) {
            MSCExecutors.runOnUiThread(runnable);
        }
    }
}
