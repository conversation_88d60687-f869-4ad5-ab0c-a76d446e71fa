package com.meituan.msc.modules.page.render.webview;

import static com.meituan.msc.modules.page.render.webview.PreloadWebViewManager.PRELOAD_WEBVIEW;

import android.os.SystemClock;

import com.meituan.msc.common.executor.MSCExecutors;
import com.meituan.msc.lib.BuildConfig;
import com.meituan.msc.modules.preload.MSCHornPreloadConfig;
import com.meituan.msc.modules.reporter.CommonTags;
import com.meituan.msc.modules.reporter.MSCReporter;
import com.meituan.msc.modules.reporter.ReporterFields;

import java.util.Map;

/**
 * WebView预加载埋点
 * https://km.sankuai.com/collabpage/1845788888
 */
public class WebViewPreloadReporter extends MSCReporter {

    private static volatile WebViewPreloadReporter mInstance;
    private volatile boolean hasPartThreeReported = false;
    private volatile boolean hasPartTwoReported = false;
    private volatile boolean hasPartGetDefaultUserAgentReported = false;

    public static WebViewPreloadReporter getInstance() {
        if (mInstance == null) {
            synchronized (WebViewPreloadReporter.class) {
                if (mInstance == null) {
                    mInstance = new WebViewPreloadReporter();
                }
            }
        }
        return mInstance;
    }

    private WebViewPreloadReporter() {
        this.commonTag(CommonTags.SDK_VERSION, BuildConfig.AAR_VERSION);
    }

    public void reportWebViewBackgroundInitDuration(long duration, boolean isInitSuccess, Map<String, Object> tags) {
        WebViewCacheManager.WebViewType webViewType =
                WebViewCacheManager.getInstance().useMtWebViewByAppId(PRELOAD_WEBVIEW) ?
                WebViewCacheManager.WebViewType.MT_WEB_VIEW : WebViewCacheManager.WebViewType.CHROME;
        record(ReporterFields.REPORT_WEBVIEW_BACKGROUND_INIT_DURATION)
                .tag("isInitSuccess", isInitSuccess)
                .tag("webviewType", webViewType.toString())
                .tags(tags)
                .value(duration)
                .sendDelay();
    }

    public void reportWebViewPreCreateDuration(boolean isKNBInit, long duration, boolean isCreateWebViewSuccess
            , Map<String, Object> tags) {
        WebViewCacheManager.WebViewType webViewType =
                WebViewCacheManager.getInstance().useMtWebViewByAppId(PRELOAD_WEBVIEW) ?
                WebViewCacheManager.WebViewType.MT_WEB_VIEW : WebViewCacheManager.WebViewType.CHROME;
        record(ReporterFields.REPORT_WEBVIEW_PRECREATE_DURATION)
                .tag("isKNBInit", isKNBInit)
                .tag("isCreateWebViewSuccess", isCreateWebViewSuccess)
                .tag("webviewType", webViewType)
                .tags(tags)
                .value(duration)
                .sendDelay();
    }


    public void reportWebViewPartTwoTime() {
        MSCExecutors.runOnUiThread(new Runnable() {
            @Override
            public void run() {
                if (hasPartTwoReported) {
                    return;
                }
                hasPartTwoReported = true;
                long time = SystemClock.currentThreadTimeMillis();
                record(ReporterFields.MSC_WEBVIEW_PART2).value(time).sendDelay();
            }
        });

    }

    public void reportWebViewPartThreeTime() {
        MSCExecutors.runOnUiThread(new Runnable() {
            @Override
            public void run() {
                if (hasPartThreeReported) {
                    return;
                }
                hasPartThreeReported = true;
                long time = SystemClock.currentThreadTimeMillis();
                record(ReporterFields.MSC_WEBVIEW_PART3).value(time).sendDelay();
            }
        });

    }

    public void reportWebViewPartGetDefaultUserAgentTime(long duration) {
        MSCExecutors.runOnUiThread(new Runnable() {
            @Override
            public void run() {
                if (hasPartGetDefaultUserAgentReported) {
                    return;
                }
                hasPartGetDefaultUserAgentReported = true;
                long time = SystemClock.currentThreadTimeMillis();
                record(ReporterFields.MSC_WEBVIEW_PART_GET_DEFAULT_USER_AGENT)
                        .tag("duration", duration)
                        .value(time)
                        .sendDelay();
            }
        });
    }

    public void reportWebViewInitStateBeforePreload(boolean isMTWebViewPreload, boolean isKNBInit) {
        record(ReporterFields.REPORT_WEBVIEW_PRELOAD_BEFORE_INIT)
                .tag("isMTWebViewPreload", isMTWebViewPreload)
                .tag("isKNBInit", isKNBInit)
                .sendDelay();
    }

    public void reportWebViewPreloadIdleDurationStartFromAppLaunch(long start) {
        once(ReporterFields.REPORT_WEBVIEW_PRELOAD_IDLE_DURATION)
                .tag("durationFromMSCInit", SystemClock.elapsedRealtime() - start)
                .value(SystemClock.currentThreadTimeMillis())
                .sendDelay();
    }

    public void reportWebViewPreloadStartCount(boolean isPreload, String preloadWebViewType) {
        record(ReporterFields.REPORT_WEBVIEW_PRELOAD_START_COUNT)
                .tag("preloadWebViewType", preloadWebViewType)
                .value(isPreload ? 1 : 0)
                .sendDelay();
    }

    public void reportDelayPreloadConfigOfCancelCreateWebView(long backgroundInitTimeMill,
                                                              long webViewCreateDelayThreshold) {
        record(ReporterFields.REPORT_CANCEL_WEBVIEW_PRECREATE_DELAY_CONFIG)
                .tag("backgroundInitTimeMill", backgroundInitTimeMill)
                .tag("webViewCreateDelayThreshold", webViewCreateDelayThreshold)
                .sendDelay();
    }

    public void preloadPreCreateWebViewDelayTimeOverLimit(long time, long delayTime, double rate, double cpuUsageRatio,
                                                          int threadCount, int threadActiveCount) {
        record(ReporterFields.REPORT_CANCEL_WEBVIEW_PRECREATE_THRESHOLD_CONFIG)
                .tag("currentTime", time)
                .tag("delayTime", delayTime)
                .tag("currentRate", rate)
                .tag("cpuUsageRatio", cpuUsageRatio)
                .tag("threadCount", threadCount)
                .tag("threadActiveCount", threadActiveCount)
                .sendDelay();
    }
}
