package com.meituan.msc.modules.page.render.webview.impl;

import android.support.annotation.Nullable;
import android.view.View;
import android.webkit.ValueCallback;

import com.meituan.msc.modules.engine.MSCRuntime;
import com.meituan.msc.modules.manager.ExecutorContext;
import com.meituan.msc.modules.manager.IMSCModule;
import com.meituan.msc.modules.page.render.webview.IWebView;
import com.meituan.msc.modules.page.render.webview.OnContentScrollChangeListener;
import com.meituan.msc.modules.page.render.webview.OnPageFinishedListener;
import com.meituan.msc.modules.page.render.webview.OnReloadListener;
import com.meituan.msc.modules.page.render.webview.OnWebViewFullScreenListener;
import com.meituan.msc.modules.page.render.webview.WebViewCacheManager;
import com.meituan.msc.modules.page.render.webview.WebViewFirstPreloadStateManager;
import com.meituan.msc.modules.page.render.webview.WebViewJavaScript;

import java.util.List;

/**
 * 一个空实现 用在WebView初始化异常的时候
 * Created by letty on 2019/12/12.
 **/
public class EmptyWebViewImpl implements IWebView {
    @Override
    public void init(MSCRuntime runtime) {

    }

    @Override
    public void evaluateJavascript(WebViewJavaScript script, @Nullable ValueCallback<String> resultCallback) {

    }

    @Override
    public void onCreate() {

    }

    @Override
    public void onDestroy() {

    }

    @Override
    public void loadUrl(String url) {

    }

    @Override
    public void loadDataWithBaseURL(String baseUrl, String data, String mimeType, String encoding, String failUrl) {

    }

    @Override
    public String getUrl() {
        return null;
    }

    @Override
    public long getCreateTimeMillis() {
        return -1;
    }

    @Override
    public void addJavascriptInterface(Object object, String name) {

    }

    @Override
    public String tag() {
        return null;
    }

    @Override
    public View getWebView() {
        return null;
    }

    @Override
    public void requestContentLayout() {

    }

    @Override
    public void scrollContentY(int offset) {

    }

    @Override
    public void setOnContentScrollChangeListener(OnContentScrollChangeListener listener) {

    }

    @Override
    public String getUserAgentString() {
        return null;
    }

    @Override
    public void setUserAgentString(String userAgentString) {

    }

    @Override
    public int getContentHeight() {
        return 0;
    }

    public int getContentScrollY() {
        return 0;
    }

    @Override
    public void setOnPageFinishedListener(OnPageFinishedListener url) {

    }

    @Override
    public void onShow() {

    }

    @Override
    public void onHide() {

    }

    public void bindPageId(int viewId) {

    }

    @Override
    public void setOnFullScreenListener(OnWebViewFullScreenListener listener) {

    }

    @Override
    public void setOnReloadListener(OnReloadListener listener) {

    }

    @Override
    public long getWebViewInitializationDuration() {
        return 0;
    }

    @Override
    public WebViewCacheManager.WebViewCreateScene getWebViewCreateScene() {
        return null;
    }

    @Override
    public void setCreateScene(WebViewCacheManager.WebViewCreateScene createScene) {
    }

    @Override
    public WebViewFirstPreloadStateManager.PreloadState getPreloadState() {
        return null;
    }

    @Override
    public void setPreloadState(WebViewFirstPreloadStateManager.PreloadState preloadState) {

    }

    @Override
    public void setWebViewBackgroundColor(int color) { }

    @Override
    public void createMessagePort(IMSCModule iMSCModule, ExecutorContext executorContext) {
    }

    @Override
    public void transferPortToJavaScript() {
    }

    @Override
    public void postMessageWithNativeMessagePort(WebViewJavaScript script) {
    }

    @Override
    public boolean messagePortReady() {
        return false;
    }

    @Override
    public void messagePortClose() {
    }

    @Override
    public String getConsoleLogErrorMessage() {
        return "";
    }

    @Override
    public List<Long> getRenderProcessGoneTimeList() {
        return null;
    }
}
