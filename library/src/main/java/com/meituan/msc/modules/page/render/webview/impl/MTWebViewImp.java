package com.meituan.msc.modules.page.render.webview.impl;

import static com.meituan.msc.modules.page.render.webview.WebMessageParseResult.parseAsyncMethodWebMessage;
import static com.meituan.mtwebkit.internal.MTWebViewConstants.TYPE_MTWEBVIEW_SYSTEM;

import android.annotation.TargetApi;
import android.content.Context;
import android.graphics.Bitmap;
import android.net.Uri;
import android.os.Build;
import android.os.SystemClock;
import android.support.annotation.Nullable;
import android.util.Log;
import android.util.Pair;
import android.view.View;
import android.view.ViewParent;
import android.view.accessibility.AccessibilityManager;
import android.view.accessibility.AccessibilityNodeProvider;
import android.webkit.ValueCallback;

import com.meituan.msc.common.ensure.ResFilesEnsure;
import com.meituan.msc.common.lib.multiplex.OnWebScrollChangeListener;
import com.meituan.msc.common.lib.multiplex.WebViewCache;
import com.meituan.msc.common.resource.MTWebResponseBuild;
import com.meituan.msc.common.utils.ArrayUtils;
import com.meituan.msc.common.utils.DeviceUtil;
import com.meituan.msc.common.utils.MSCTraceUtil;
import com.meituan.msc.common.utils.ViewUtils;
import com.meituan.msc.modules.api.RenderProcessGoneHandler;
import com.meituan.msc.modules.engine.MSCHornRollbackConfig;
import com.meituan.msc.modules.engine.MSCRuntime;
import com.meituan.msc.modules.manager.ExecutorContext;
import com.meituan.msc.modules.manager.IMSCModule;
import com.meituan.msc.modules.page.embeddedwidget.EmbedProvider;
import com.meituan.msc.modules.page.embeddedwidget.EmbeddedManager;
import com.meituan.msc.modules.page.render.webview.IWebView;
import com.meituan.msc.modules.page.render.webview.OnContentScrollChangeListener;
import com.meituan.msc.modules.page.render.webview.OnPageFinishedListener;
import com.meituan.msc.modules.page.render.webview.OnReloadListener;
import com.meituan.msc.modules.page.render.webview.OnWebViewFullScreenListener;
import com.meituan.msc.modules.page.render.webview.WebChromeClientCustomViewCallback;
import com.meituan.msc.modules.page.render.webview.WebMessageParseResult;
import com.meituan.msc.modules.page.render.webview.WebViewCacheManager;
import com.meituan.msc.modules.page.render.webview.WebViewFileFilter;
import com.meituan.msc.modules.page.render.webview.WebViewFirstPreloadStateManager;
import com.meituan.msc.modules.page.render.webview.WebViewJavaScript;
import com.meituan.msc.modules.reporter.MSCLog;
import com.meituan.msc.util.perf.PerfTrace;
import com.meituan.mtwebkit.MTConsoleMessage;
import com.meituan.mtwebkit.MTRenderProcessGoneDetail;
import com.meituan.mtwebkit.MTValueCallback;
import com.meituan.mtwebkit.MTWebChromeClient;
import com.meituan.mtwebkit.MTWebMessage;
import com.meituan.mtwebkit.MTWebMessagePort;
import com.meituan.mtwebkit.MTWebResourceRequest;
import com.meituan.mtwebkit.MTWebResourceResponse;
import com.meituan.mtwebkit.MTWebSettings;
import com.meituan.mtwebkit.MTWebView;
import com.meituan.mtwebkit.MTWebViewClient;

import org.json.JSONArray;
import org.json.JSONException;

import java.lang.reflect.Field;
import java.lang.reflect.Method;
import java.util.List;
import java.util.concurrent.CopyOnWriteArrayList;

public class MTWebViewImp implements IWebView {

    private final String TAG = "MTWebViewImp@"+Integer.toHexString(hashCode());
    private Boolean mIsAccessibilityEnabledOriginal;
    private MTWebView mWebView;
    private final Context mContext;
    private final String mAppId;
    private MTWebViewClientImp mWebViewClient;
    private OnContentScrollChangeListener mWebScrollChangeListener;
    private int pageId;
    private OnWebViewFullScreenListener mWebViewFullScreenListener;
    private volatile boolean isDestroyed = false;
    private boolean mSupportEmbed = false;
    private MSCRuntime mRuntime;
    private long mMTWebViewInitializationDuration = 0;
    private WebViewCacheManager.WebViewCreateScene createScene;
    private WebViewFirstPreloadStateManager.PreloadState preloadState;
    private ResFilesEnsure mResFilesEnsure;
    private volatile MTWebMessagePort nativePort, jsPort;
    private volatile boolean jsPortTransferred;
    private final Object messagePortLock = new Object();
    private volatile String mConsoleLogErrorMessage = "";
    private volatile List<Long> mRenderProcessGoneTimeList = new CopyOnWriteArrayList<>();
    private static final int MAX_RENDER_PROCESS_GONE_ERROR_SIZE = 3;
    private final long createTimeMillis = System.currentTimeMillis();
    /**
     * 标记当前WebView是否是WebView进程初始化后第一次创建的WebView，因为Android中创建第一个WebView最耗时
     */
    private boolean isFirstCreateWebView = false;
    private String rendererHashCode;

    public MTWebViewImp(Context context, String appId) throws Exception {
        this.mContext = context;
        this.mAppId = appId;
        initSetting("msc_" + appId);
    }

    private MTWebView createMtWebView(String mtWebViewAppTag) {
        isFirstCreateWebView = !MTWebView.getMTWebViewIsCreate();
        // setOnScrollChanged需要api23
        MTWebView cached = WebViewCache.getInstance().consumeMSC(new OnWebScrollChangeListener() {
            @Override
            public void onWebScrollChange(int l, int t, int oldl, int oldt) {
                if (mWebScrollChangeListener != null) {
                    mWebScrollChangeListener.onWebScrollChange(l, t, oldl, oldt);
                }
            }
        });
        if (null != cached) {
            MSCLog.i(TAG, "#createMtWebView，MSC used cache.", mtWebViewAppTag);
            return cached;
        }
        MSCLog.i(TAG, "#createMtWebView，MSC don't find cache.", mtWebViewAppTag);
        // 若没取到，则返回老逻辑。
        return new MTWebView(mtWebViewAppTag, mContext) {
            @Override
            protected void onScrollChanged(int l, int t, int oldl, int oldt) {
                super.onScrollChanged(l, t, oldl, oldt);
                if (mWebScrollChangeListener != null) {
                    mWebScrollChangeListener.onWebScrollChange(l, t, oldl, oldt);
                }
            }

            @Override
            public AccessibilityNodeProvider getAccessibilityNodeProvider() {
                if (MSCHornRollbackConfig.enableCloseWebViewAccessibilityService(mAppId)) {
                    boolean enableAccessibilityService = DeviceUtil.isAccessibilityService(mContext);
                    if (enableAccessibilityService) {
                        return super.getAccessibilityNodeProvider();
                    }
                    MSCLog.i(TAG, "getAccessibilityNodeProvider return null");
                    return null;
                }
                return super.getAccessibilityNodeProvider();
            }
        };
    }

    /**
     * @throws Exception 创建WebView时可能抛异常
     */
    private void initSetting(String mtWebViewAppTag) throws Exception {
        // businessName用于MTWebView侧统计，需要描述业务，及通过mmp调用
        long start = SystemClock.elapsedRealtime();
        mWebView = createMtWebView(mtWebViewAppTag);
        mMTWebViewInitializationDuration = SystemClock.elapsedRealtime() - start;
        mWebView.setOverScrollMode(View.OVER_SCROLL_NEVER);

        fixedAccessibilityInjectorException();
        removeJavaInterface();

        MTWebSettings webSetting = mWebView.getSettings();
        webSetting.setAllowFileAccess(true);
        webSetting.setAllowFileAccessFromFileURLs(true);
        webSetting.setBuiltInZoomControls(true);
        webSetting.setDisplayZoomControls(false);
        webSetting.setSupportMultipleWindows(false);
        webSetting.setAppCacheEnabled(true);
        webSetting.setDomStorageEnabled(true);
//        webSetting.setDatabaseEnabled(true);
        webSetting.setJavaScriptEnabled(true);
        webSetting.setGeolocationEnabled(true);
        webSetting.setUseWideViewPort(true);
        webSetting.setLoadWithOverviewMode(true);

        try {
            webSetting.setMediaPlaybackRequiresUserGesture(false);
        } catch (Exception e) {
            //ignore
        }
//        webSetting.setCacheMode(WebSettings.LOAD_NO_CACHE);
//        String ua = webSetting.getUserAgentString();
//        webSetting.setUserAgentString(String.format("%s Hera(version/%s)", ua, HeraConfig.VERSION));
        mWebView.setVerticalScrollBarEnabled(false);
        mWebView.setHorizontalScrollBarEnabled(false);

        if (Build.VERSION.SDK_INT > Build.VERSION_CODES.ICE_CREAM_SANDWICH) {
            webSetting.setTextZoom(100);
        }

//        webSetting.setDatabasePath(mContext.getFilesDir().getParentFile().getAbsolutePath() + "/databases/");
        webSetting.setAppCacheMaxSize(10 * 1024 * 1024);
        webSetting.setAppCachePath(WebViewCacheManager.getWebAppPath(mContext));

        mWebView.setWebChromeClient(new MTWebChromeClient() {
            private String className = "MTWebViewImp";

            // WebView video组件默认图兜底（问题修复：https://stackoverflow.com/questions/76700882/nullpointer-exception-on-bitmap-getwidth-at-chromium-trichromewebviewgoogle-aa）
            @Override
            public Bitmap getDefaultVideoPoster() {
                return ViewUtils.getDefaultVideoPoster(super.getDefaultVideoPoster());
            }

            @Override
            public boolean onConsoleMessage(MTConsoleMessage consoleMessage) {
                if (consoleMessage.messageLevel() == MTConsoleMessage.MessageLevel.ERROR) {
                    mConsoleLogErrorMessage = consoleMessage.message();
                    MSCLog.e("webview_log_" + className +
                            " [error] " + mConsoleLogErrorMessage +
                            ", sourceId = " + consoleMessage.sourceId() +
                            ", lineNumber = " + consoleMessage.lineNumber());
                } else {
                    Log.i(className + "_log",
                            consoleMessage.message());
                }
                return super.onConsoleMessage(consoleMessage);
            }

            @Override
            public void onReceivedTitle(MTWebView view, String title) {
                super.onReceivedTitle(view, title);
                if (title.startsWith("msc-page:")) {
                    view.evaluateJavascript(String.format("document.title = '%s@page_%s@%s';", mRuntime.getMSCAppModule().getAppId(), pageId, title), null);
                }
            }

            @Override
            public void onShowCustomView(View view, CustomViewCallback callback) {
                super.onShowCustomView(view, callback);
                if (mWebViewFullScreenListener != null) {
                    mWebViewFullScreenListener.showCustomView(view, new WebChromeClientCustomViewCallback() {
                        @Override
                        public void onHideCustomView() {
                            callback.onCustomViewHidden();
                        }
                    });
                }
            }

            @Override
            public void onHideCustomView() {
                if (mWebViewFullScreenListener != null) {
                    mWebViewFullScreenListener.hideCustomView();
                }
            }
        });
//        setDrawDuringWindowsAnimating(mWebView);
        mWebViewClient = new MTWebViewClientImp(mContext);
        mWebView.setWebViewClient(mWebViewClient);
        mSupportEmbed = EmbeddedManager.supportEmbed(mWebView);
        if (mSupportEmbed) {
            MSCLog.d("MTWebView supportEmbed", true);
            EmbeddedManager.registerEmbed(mWebView);
        } else {
            MSCLog.d("MTWebView supportEmbed", false);
        }

    }

    static String embedInjectStr;

    private static void injectEmbedSupport(MTWebView view) {
        if (EmbeddedManager.supportEmbed(view)) {
            if (embedInjectStr == null) {
                embedInjectStr = String.format("if (typeof __mpInfo === 'undefined') {var __mpInfo = {};} __mpInfo.embeddedWidgets = %s;",
                        ArrayUtils.toJSString(EmbedProvider.supportWidgets));
            }
            MSCLog.d("injectEmbedSupport", embedInjectStr);
            view.evaluateJavascript(embedInjectStr, null);
        }
    }

    @Override
    public void init(MSCRuntime runtime) {
        this.mRuntime = runtime;
        int i = runtime.messagePortLeakSize.incrementAndGet();
        MSCLog.i(TAG, "init", "MSCRuntime@" + Integer.toHexString(runtime.hashCode()), i,
                "MSCWebViewRenderer@" + rendererHashCode);
        mResFilesEnsure = new ResFilesEnsure(runtime.getMSCAppModule());
    }

    @Override
    public void evaluateJavascript(WebViewJavaScript script, @Nullable ValueCallback<String> resultCallback) {
        mWebView.evaluateJavascript(script.buildJavaScriptString(true), new MTValueCallback<String>() {
            @Override
            public void onReceiveValue(String value) {
                if (resultCallback != null) {
                    resultCallback.onReceiveValue(value);
                }
            }
        });
    }

    @Override
    public void onCreate() {

    }

    @Override
    public void onDestroy() {
        try {
            if (isDestroyed) {
                MSCLog.d(TAG, "MTWebViewImp is destroyed");
                return;
            }
            isDestroyed = true;
            mWebViewFullScreenListener = null;
            mWebView.setWebChromeClient(null);
            releaseConfigCallback();
            resetAccessibilityEnabled();
            mWebView.destroy();
        } catch (Throwable t) {
            MSCLog.e(tag(), "destroy exception");
        }
    }

    @Override
    public void loadUrl(String url) {
        mWebView.loadUrl(url);
        injectEmbedSupport(mWebView);
    }

    @Override
    public void loadDataWithBaseURL(String baseUrl, String data, String mimeType, String encoding, String failUrl) {
        mWebView.loadDataWithBaseURL(baseUrl, data, mimeType, encoding, failUrl);
        injectEmbedSupport(mWebView);
    }

    @Override
    public String getUrl() {
        return mWebView.getUrl();
    }

    @Override
    public long getCreateTimeMillis() {
        return this.createTimeMillis;
    }

    @SuppressWarnings({"AddJavascriptInterface", "JavascriptInterface"})
    @Override
    public void addJavascriptInterface(Object object, String name) {
        mWebView.addJavascriptInterface(object, name);
    }

    public String tag() {
        return "MTWebView";
    }

    @Override
    public View getWebView() {
        return mWebView;
    }

    @Override
    public void requestContentLayout() {
        mWebView.requestLayout();
    }

    @Override
    public void scrollContentY(int offset) {
        mWebView.scrollBy(0, offset);
    }

    @Override
    public void setOnContentScrollChangeListener(OnContentScrollChangeListener listener) {
        this.mWebScrollChangeListener = listener;
    }

    public String getUserAgentString() {
        return mWebView.getSettings().getUserAgentString();
    }

    @Override
    public void setUserAgentString(String userAgentString) {
        mWebView.getSettings().setUserAgentString(userAgentString);
    }

    @Override
    public int getContentHeight() {
        return (int) (mWebView.getContentHeight() * mWebView.getScale());
    }

    @Override
    public int getContentScrollY() {
        return mWebView.getScrollY();
    }

    @Override
    public void setOnPageFinishedListener(OnPageFinishedListener onPageFinishedListener) {
        this.mWebViewClient.setOnPageFinishedListener(onPageFinishedListener);
    }

    @Override
    public void onShow() {
        this.mWebView.onResume();
    }

    @Override
    public void onHide() {
        this.mWebView.onPause();
    }

    @Override
    public void bindPageId(int viewId) {
        this.pageId = viewId;
    }


    private void removeJavaInterface() {
        try {
            Method removeJavascriptInterface = mWebView.getClass().getMethod("removeJavascriptInterface", String.class);
            if (removeJavascriptInterface != null) {
                removeJavascriptInterface.invoke(mWebView, "searchBoxJavaBridge_");
                removeJavascriptInterface.invoke(mWebView, "accessibility");
                removeJavascriptInterface.invoke(mWebView, "accessibilityTraversal");
            }
        } catch (Exception e) {
            e.printStackTrace();
        }
    }

    private void releaseConfigCallback() {
        if (Build.VERSION.SDK_INT < Build.VERSION_CODES.KITKAT) { // KITKAT
            try {
                Field sConfigCallback = Class.forName("android.webkit.BrowserFrame").getDeclaredField("sConfigCallback");
                if (sConfigCallback != null) {
                    sConfigCallback.setAccessible(true);
                    sConfigCallback.set(null, null);
                }
            } catch (Exception e) {
                e.printStackTrace();
            }
        }
    }

    private void fixedAccessibilityInjectorException() {
        if (Build.VERSION.SDK_INT == Build.VERSION_CODES.JELLY_BEAN_MR1
                && mIsAccessibilityEnabledOriginal == null
                && isAccessibilityEnabled()) {
            mIsAccessibilityEnabledOriginal = true;
            setAccessibilityEnabled(false);
        }
    }

    private boolean isAccessibilityEnabled() {
        AccessibilityManager am = (AccessibilityManager) mContext.getSystemService(Context.ACCESSIBILITY_SERVICE);
        return am.isEnabled();
    }

    private void setAccessibilityEnabled(boolean enabled) {
        AccessibilityManager am = (AccessibilityManager) mContext.getSystemService(Context.ACCESSIBILITY_SERVICE);
        try {
            Method setAccessibilityState = am.getClass().getDeclaredMethod("setAccessibilityState", boolean.class);
            setAccessibilityState.setAccessible(true);
            setAccessibilityState.invoke(am, enabled);
            setAccessibilityState.setAccessible(false);
        } catch (Throwable e) {
            e.printStackTrace();
        }
    }

    private void resetAccessibilityEnabled() {
        if (mIsAccessibilityEnabledOriginal != null) {
            setAccessibilityEnabled(mIsAccessibilityEnabledOriginal);
        }
    }

    public static Pair<Boolean, String> isWebViewPackageException(Throwable e) {
        String messageCause = e.getCause() == null ? e.toString() : e.getCause().toString();
        String trace = Log.getStackTraceString(e);
        if (trace.contains("android.content.pm.PackageManager$NameNotFoundException")
                || trace.contains("java.lang.RuntimeException: Cannot load WebView")
                || trace.contains("android.webkit.WebViewFactory$MissingWebViewPackageException: Failed to load WebView provider: No WebView installed")) {

            MSCLog.e("HeraWebView", "isWebViewPackageException" + e.getMessage());
            return new Pair<>(true, "WebView load failed, " + messageCause);
        }
        return new Pair<>(false, messageCause);
    }

    public class MTWebViewClientImp extends MTWebViewClient {

        private OnPageFinishedListener mOnPageFinishedListener;
        private OnReloadListener mOnReloadListener;

        private Context mContext;
        private MTWebResponseBuild webResponseBuild = new MTWebResponseBuild();

        public MTWebViewClientImp(Context context) {
            mContext = context;
        }

        public MTWebViewClientImp setOnPageFinishedListener(OnPageFinishedListener onPageFinishedListener) {
            mOnPageFinishedListener = onPageFinishedListener;
            return this;
        }

        public MTWebViewClientImp setOnRenderProcessGoneListener(OnReloadListener listener) {
            mOnReloadListener = listener;
            return this;
        }

        @Override
        public void onPageStarted(com.meituan.mtwebkit.MTWebView view, String url, Bitmap favicon) {
            super.onPageStarted(view, url, favicon);
            if (mOnPageFinishedListener != null) {
                mOnPageFinishedListener.onPageStarted(url, favicon);
            }
        }


        /**
         * 处理RenderProcess jni crash 别把app杀掉
         *
         * @param view
         * @param detail
         * @return
         */
        // https://developer.android.com/reference/android/webkit/WebViewClient.html#onRenderProcessGone(android.webkit.WebView,%20android.webkit.RenderProcessGoneDetail)
        @TargetApi(Build.VERSION_CODES.O)
        @Override
        public boolean onRenderProcessGone(com.meituan.mtwebkit.MTWebView view, MTRenderProcessGoneDetail detail) {
            if (mRenderProcessGoneTimeList.size() >= MAX_RENDER_PROCESS_GONE_ERROR_SIZE) {
                mRenderProcessGoneTimeList.remove(0);
            }
            mRenderProcessGoneTimeList.add(System.currentTimeMillis());
            RenderProcessGoneHandler.handleRenderProcessGone(view, detail.didCrash(), detail.rendererPriorityAtExit(), view.getUrl(), mRuntime, mOnReloadListener);
            return true;
        }

        @Override
        public void onPageFinished(com.meituan.mtwebkit.MTWebView view, String url) {
            PerfTrace.instant("WebView#onPageFinished");
            super.onPageFinished(view, url);
            injectEmbedSupport(view);
            if (mOnPageFinishedListener != null) {
                mOnPageFinishedListener.onPageFinished(url, null);
            }
        }

        @TargetApi(Build.VERSION_CODES.LOLLIPOP)
        @Override
        public MTWebResourceResponse shouldInterceptRequest(com.meituan.mtwebkit.MTWebView view, MTWebResourceRequest request) {
            final String url = request.getUrl().toString();
            MTWebResourceResponse resource = (MTWebResourceResponse) WebViewFileFilter.interceptResource(view.getContext(), mRuntime.getFileModule(), url, webResponseBuild, mResFilesEnsure, mRuntime.getMSCAppModule().enableAsyncSubPkg());
            return resource != null ? resource : super.shouldInterceptRequest(view, request);
        }

        @Override
        public MTWebResourceResponse shouldInterceptRequest(com.meituan.mtwebkit.MTWebView view, String url) {
//            HeraTrace.d("InterceptRequest", String.format("url=%s", url));
            MTWebResourceResponse resource = (MTWebResourceResponse) WebViewFileFilter.interceptResource(view.getContext(), mRuntime.getFileModule(), url, webResponseBuild, mResFilesEnsure, mRuntime.getMSCAppModule().enableAsyncSubPkg());
            return resource != null ? resource : super.shouldInterceptRequest(view, url);
        }
    }

    public void setOnFullScreenListener(OnWebViewFullScreenListener listener) {
        this.mWebViewFullScreenListener = listener;
    }

    @Override
    public void setOnReloadListener(OnReloadListener listener) {
        this.mWebViewClient.setOnRenderProcessGoneListener(listener);
    }

    public boolean isSupportEmbed() {
        return mSupportEmbed;
    }

    public Context getContext() {
        return mContext;
    }

    @Override
    public long getWebViewInitializationDuration() {
        return mMTWebViewInitializationDuration;
    }

    @Override
    public WebViewCacheManager.WebViewCreateScene getWebViewCreateScene() {
        return createScene;
    }

    @Override
    public void setCreateScene(WebViewCacheManager.WebViewCreateScene createScene) {
        this.createScene = createScene;
    }

    @Override
    public WebViewFirstPreloadStateManager.PreloadState getPreloadState() {
        return preloadState == null ? WebViewFirstPreloadStateManager.getInstance().getPreloadState() : preloadState;
    }

    @Override
    public void setPreloadState(WebViewFirstPreloadStateManager.PreloadState preloadState) {
        this.preloadState = preloadState;
    }

    @Override
    public void setWebViewBackgroundColor(int color) {
        mWebView.setBackgroundColor(color);
    }

    @Override
    public void createMessagePort(IMSCModule mscModule, ExecutorContext executorContext) {
        // 自研内核降级，在Android 6.0.1和7.1.2有crash，不使用messagePort
        // https://ones.sankuai.com/ones/product/31464/workItem/defect/detail/83696303?activeTabName=first
        if (TYPE_MTWEBVIEW_SYSTEM.equals(((MTWebView) getWebView()).getMTWebViewType()) && android.os.Build.VERSION.SDK_INT <= Build.VERSION_CODES.N_MR1) {
            MSCLog.e(TAG, "messagePort#create abort: TYPE_MTWEBVIEW_SYSTEM");
            return;
        }
        MTWebMessagePort[] messagePort = ((MTWebView) getWebView()).createWebMessageChannel();
        if (messagePort == null || messagePort.length != 2 || messagePort[0] == null || messagePort[1] == null) {
            MSCLog.e(TAG, "messagePort#create fail");
            return;
        }
        nativePort = messagePort[0];
        jsPort = messagePort[1];
        nativePort.setWebMessageCallback(new MTWebMessagePort.WebMessageCallback() {
            @Override
            public void onMessage(MTWebMessagePort port, MTWebMessage message) {
                WebMessageParseResult parseResult;
                try {
                    parseResult = parseAsyncMethodWebMessage(message.getData());
                } catch (JSONException e) {
                    String exceptionMessage = "messagePort#onMessage json parse fail: " + message.getData();
                    MSCLog.e(TAG, exceptionMessage);
                    mRuntime.getNativeExceptionHandler().handleException(new Exception(exceptionMessage));
                    return;
                }
                if (parseResult == null) {
                    String exceptionMessage = "messagePort#onMessage json format error: " + message.getData();
                    MSCLog.e(TAG, exceptionMessage);
                    mRuntime.getNativeExceptionHandler().handleException(new Exception(exceptionMessage));
                    return;
                }
                try {
                    mscModule.invoke(parseResult.module, parseResult.method, parseResult.argsArray, executorContext);
                } catch (Exception e) {
                    MSCLog.e(TAG, "messagePort#onMessage invoke exception: " + e);
                    mRuntime.getNativeExceptionHandler().handleException(e);
                }
            }
        });
        MSCLog.i(TAG, "messagePort#create success");
    }

    @Override
    public void transferPortToJavaScript() {
        String appIdWhiteList = "[]";
        if (MSCHornRollbackConfig.getMessagePortWhiteList() != null) {
            JSONArray jsonArray = new JSONArray(MSCHornRollbackConfig.getMessagePortWhiteList());
            appIdWhiteList = jsonArray.toString();
        }
        MSCLog.i(TAG, "messagePort#appIdWhiteList " + appIdWhiteList);
        if (nativePort != null && jsPort != null) {
            ((MTWebView) getWebView()).postWebMessage(new MTWebMessage(appIdWhiteList,
                    new MTWebMessagePort[]{jsPort}), Uri.parse(""));
            jsPortTransferred = true;
        } else {
            MSCLog.e(TAG, "messagePort#ports not exist");
        }
    }

    @Override
    public void postMessageWithNativeMessagePort(WebViewJavaScript script) {
        MTWebMessage mtWebMessage = new MTWebMessage(script.buildWebMessageString(true));
        if (MSCTraceUtil.TRACE_LEVEL >= MSCTraceUtil.LEVEL_HEAVY) {
            PerfTrace.begin("postWebMessage").arg("content", script.buildJavaScriptString(false));
        }
        if (!MSCHornRollbackConfig.get().getConfig().rollbackMessagePortLock) {
            // https://ones.sankuai.com/ones/product/32979/workItem/defect/detail/84040139?activeTabName=first
            // messagePort销毁与postMessage并发，需要加锁。
            synchronized (messagePortLock) {
                if (messagePortReady()) {
                    nativePort.postMessage(mtWebMessage);
                } else {
                    MSCLog.e(TAG, "messagePort#port closed when postMessage");
                }
            }
        } else {
            nativePort.postMessage(mtWebMessage);
        }
        if (MSCTraceUtil.TRACE_LEVEL >= MSCTraceUtil.LEVEL_HEAVY) {
            PerfTrace.end("postWebMessage").arg("content", script.buildJavaScriptString(false));
        }
    }

    /**
     * message port可用，而且已完成初始化
     */
    @Override
    public boolean messagePortReady() {
        return nativePort != null && jsPortTransferred;
    }

    @Override
    public void messagePortClose() {
        if (nativePort != null && !MSCHornRollbackConfig.isRollbackMessagePort()) {
            if (!MSCHornRollbackConfig.get().getConfig().rollbackMessagePortLock) {
                synchronized (messagePortLock) {
                    int i = mRuntime.messagePortLeakSize.decrementAndGet();
                    MSCLog.i(TAG, "messagePortClose", "MSCRuntime@" + Integer.toHexString(mRuntime.hashCode()), i,
                            "MSCWebViewRenderer@" + rendererHashCode);
                    nativePort.close();
                    // jsPort已经被转移(transferred)，不需要close
                    nativePort = null;
                    jsPort = null;
                }
            } else {
                MSCLog.i(TAG, "messagePortClose", mRuntime);
                nativePort.close();
                // jsPort已经被转移(transferred)，不需要close
                nativePort = null;
                jsPort = null;
            }
        } else {
            int i = mRuntime.messagePortLeakSize.decrementAndGet();
            MSCLog.i(TAG, "messagePortClose nativePort is null", "MSCRuntime@" + Integer.toHexString(mRuntime.hashCode()), i,
                    "MSCWebViewRenderer@" + rendererHashCode);
        }
    }

    @Override
    public String getConsoleLogErrorMessage() {
        return mConsoleLogErrorMessage;
    }

    @Override
    public List<Long> getRenderProcessGoneTimeList() {
        return mRenderProcessGoneTimeList;
    }

    public boolean isFirstCreateWebView() {
        return isFirstCreateWebView;
    }

    public void setRendererHashCode(String rendererHashCode) {
        this.rendererHashCode = rendererHashCode;
    }
}