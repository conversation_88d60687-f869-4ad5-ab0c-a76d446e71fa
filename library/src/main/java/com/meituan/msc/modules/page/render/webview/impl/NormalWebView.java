package com.meituan.msc.modules.page.render.webview.impl;

import android.annotation.TargetApi;
import android.content.Context;
import android.graphics.Bitmap;
import android.graphics.BitmapFactory;
import android.os.Build;
import android.os.SystemClock;
import android.support.annotation.Nullable;
import android.support.annotation.RequiresApi;
import android.util.Log;
import android.util.Pair;
import android.view.View;
import android.view.ViewParent;
import android.view.accessibility.AccessibilityManager;
import android.view.accessibility.AccessibilityNodeProvider;
import android.webkit.ConsoleMessage;
import android.webkit.RenderProcessGoneDetail;
import android.webkit.ValueCallback;
import android.webkit.WebChromeClient;
import android.webkit.WebMessagePort;
import android.webkit.WebResourceRequest;
import android.webkit.WebResourceResponse;
import android.webkit.WebSettings;
import android.webkit.WebView;
import android.webkit.WebViewClient;

import com.meituan.msc.common.ensure.ResFilesEnsure;
import com.meituan.msc.common.resource.DefaultWebResponseBuild;
import com.meituan.msc.common.utils.DeviceUtil;
import com.meituan.msc.common.utils.ToastUtils;
import com.meituan.msc.common.utils.ViewUtils;
import com.meituan.msc.modules.api.RenderProcessGoneHandler;
import com.meituan.msc.modules.engine.MSCHornRollbackConfig;
import com.meituan.msc.modules.engine.MSCRuntime;
import com.meituan.msc.modules.manager.ExecutorContext;
import com.meituan.msc.modules.manager.IMSCModule;
import com.meituan.msc.modules.page.render.webview.IWebView;
import com.meituan.msc.modules.page.render.webview.OnContentScrollChangeListener;
import com.meituan.msc.modules.page.render.webview.OnPageFinishedListener;
import com.meituan.msc.modules.page.render.webview.OnReloadListener;
import com.meituan.msc.modules.page.render.webview.OnWebViewFullScreenListener;
import com.meituan.msc.modules.page.render.webview.WebChromeClientCustomViewCallback;
import com.meituan.msc.modules.page.render.webview.WebViewCacheManager;
import com.meituan.msc.modules.page.render.webview.WebViewFileFilter;
import com.meituan.msc.modules.page.render.webview.WebViewFirstPreloadStateManager;
import com.meituan.msc.modules.page.render.webview.WebViewJavaScript;
import com.meituan.msc.modules.reporter.MSCLog;

import java.io.File;
import java.io.IOException;
import java.lang.reflect.Field;
import java.lang.reflect.Method;
import java.util.List;
import java.util.concurrent.CopyOnWriteArrayList;

import okhttp3.OkHttpClient;
import okhttp3.Request;
import okhttp3.Response;
import okio.BufferedSink;
import okio.Okio;

@TargetApi(Build.VERSION_CODES.KITKAT)
public class NormalWebView implements IWebView {

    private final String TAG = "NormalWebView@" + Integer.toHexString(hashCode());
    private Boolean mIsAccessibilityEnabledOriginal;
    private WebView mWebView;
    private MSCRuntime runtime;
    private final Context mContext;
    private final String mAppId;
    private NormalWebViewClient mWebViewClient;
    private OnContentScrollChangeListener mWebScrollChangeListener;
    private int pageId;

    private OnWebViewFullScreenListener mWebViewFullScreenListener;
    private volatile boolean isDestroyed = false;

    private long mNormalWebViewInitializationDuration = 0;
    private WebViewCacheManager.WebViewCreateScene createScene;
    private WebViewFirstPreloadStateManager.PreloadState preloadState;
    private ResFilesEnsure mResFilesEnsure;

    private volatile WebMessagePort nativePort, jsPort;
    private volatile boolean jsPortTransferred;
    private volatile String mConsoleLogErrorMessage;
    private volatile List<Long> mRenderProcessGoneTimeList = new CopyOnWriteArrayList<>();
    private static final int MAX_RENDER_PROCESS_GONE_ERROR_SIZE = 3;
    private final long createTimeMillis = System.currentTimeMillis();

    public NormalWebView(Context context, String appId) throws Exception {
        mContext = context;
        mAppId = appId;
        initSetting();
    }

    /**
     * @throws Exception 创建WebView时可能抛异常
     */
    private void initSetting() throws Exception {
        long start = SystemClock.elapsedRealtime();
        mWebView = new WebView(mContext) {
            @Override
            protected void onScrollChanged(int l, int t, int oldl, int oldt) {
                super.onScrollChanged(l, t, oldl, oldt);
                if (mWebScrollChangeListener != null) {
                    mWebScrollChangeListener.onWebScrollChange(l, t, oldl, oldt);
                }
            }

            @Override
            public AccessibilityNodeProvider getAccessibilityNodeProvider() {
                if (MSCHornRollbackConfig.enableCloseWebViewAccessibilityService(mAppId)) {
                    boolean enableAccessibilityService = DeviceUtil.isAccessibilityService(mContext);
                    if (enableAccessibilityService) {
                        return super.getAccessibilityNodeProvider();
                    }
                    MSCLog.i(TAG, "getAccessibilityNodeProvider return null");
                    return null;
                }
                return super.getAccessibilityNodeProvider();
            }

//                @Override
//                public void setOverScrollMode(int mode) {
//                    try {
//                        super.setOverScrollMode(mode);
//                    } catch (Throwable e) {
//                        Pair<Boolean, String> pair = isWebViewPackageException(e);
//                        if (pair.first) {
//                            Toast.makeText(getContext(), pair.second, Toast.LENGTH_SHORT).show();
//                            try {
//                                destroy();
//                            } catch (Exception ex) {
//                                //ignore
//                            }
//                        } else {
//                            throw e;
//                        }
//                    }
//                }
        };
        mNormalWebViewInitializationDuration = SystemClock.elapsedRealtime() - start;
        mWebView.setOverScrollMode(View.OVER_SCROLL_NEVER);

        fixedAccessibilityInjectorException();
        removeJavaInterface();

        WebSettings webSetting = mWebView.getSettings();
        webSetting.setAllowFileAccess(true);
        webSetting.setAllowFileAccessFromFileURLs(true);
        webSetting.setBuiltInZoomControls(true);
        webSetting.setDisplayZoomControls(false);
        webSetting.setSupportMultipleWindows(false);
        webSetting.setAppCacheEnabled(true);
        webSetting.setDomStorageEnabled(true);
//        webSetting.setDatabaseEnabled(true);
        webSetting.setJavaScriptEnabled(true);
        webSetting.setGeolocationEnabled(true);
        webSetting.setUseWideViewPort(true);
        webSetting.setLoadWithOverviewMode(true);

        try {
            webSetting.setMediaPlaybackRequiresUserGesture(false);
        } catch (Exception e) {
            //ignore
        }
//        webSetting.setCacheMode(WebSettings.LOAD_NO_CACHE);
//        String ua = webSetting.getUserAgentString();
//        webSetting.setUserAgentString(String.format("%s Hera(version/%s)", ua, HeraConfig.VERSION));
        mWebView.setVerticalScrollBarEnabled(false);
        mWebView.setHorizontalScrollBarEnabled(false);

        if (Build.VERSION.SDK_INT > Build.VERSION_CODES.ICE_CREAM_SANDWICH) {
            webSetting.setTextZoom(100);
        }

//        webSetting.setDatabasePath(mContext.getFilesDir().getParentFile().getAbsolutePath() + "/databases/");
        webSetting.setAppCacheMaxSize(10 * 1024 * 1024);
        webSetting.setAppCachePath(WebViewCacheManager.getWebAppPath(mContext));

//        IX5WebViewExtension x5WebViewExtension = getX5WebViewExtension();
//        if (x5WebViewExtension != null) {
//            x5WebViewExtension.setScrollBarFadingEnabled(false);
//            x5WebViewExtension.setVerticalScrollBarEnabled(false);
//            x5WebViewExtension.setHorizontalScrollBarEnabled(false);
//        }
//
//        getView().setHorizontalScrollBarEnabled(false);
//        getView().setVerticalScrollBarEnabled(false);

        mWebView.setWebChromeClient(new WebChromeClient() {
            private final String className = "NormalWebView";

            // WebView video组件默认图兜底（问题修复：https://stackoverflow.com/questions/76700882/nullpointer-exception-on-bitmap-getwidth-at-chromium-trichromewebviewgoogle-aa）
            @Override
            public Bitmap getDefaultVideoPoster() {
                return ViewUtils.getDefaultVideoPoster(super.getDefaultVideoPoster());
            }

            @Override
            public boolean onConsoleMessage(ConsoleMessage consoleMessage) {
                if (consoleMessage.messageLevel() == ConsoleMessage.MessageLevel.ERROR) {
                    mConsoleLogErrorMessage = consoleMessage.message();
                    MSCLog.e("webview_log_" + className +
                            " [error] " + mConsoleLogErrorMessage +
                            ", sourceId = " + consoleMessage.sourceId() +
                            ", lineNumber = " + consoleMessage.lineNumber());
                } else {
                    Log.d(className + "_log",
                            consoleMessage.message());
                }
                return super.onConsoleMessage(consoleMessage);
            }

            @Override
            public void onReceivedTitle(WebView view, String title) {
                super.onReceivedTitle(view, title);
                if (title.startsWith("msc-page:")) {
                    view.evaluateJavascript(String.format("document.title = '%s@page_%s@%s';", runtime.getMSCAppModule().getAppId(), pageId, title), null);
                }
            }

            @Override
            public void onShowCustomView(View view, CustomViewCallback callback) {
                super.onShowCustomView(view, callback);
                if (mWebViewFullScreenListener != null) {
                    mWebViewFullScreenListener.showCustomView(view, new WebChromeClientCustomViewCallback() {
                        @Override
                        public void onHideCustomView() {
                            callback.onCustomViewHidden();
                        }
                    });
                }
            }

            @Override
            public void onHideCustomView() {
                if (mWebViewFullScreenListener != null) {
                    mWebViewFullScreenListener.hideCustomView();
                }
            }
        });
//        setDrawDuringWindowsAnimating(mWebView);
        mWebViewClient = new NormalWebViewClient(mContext);
        mWebView.setWebViewClient(mWebViewClient);
    }

    public void setOnFullScreenListener(OnWebViewFullScreenListener listener) {
        this.mWebViewFullScreenListener = listener;
    }

    /**
     * 让 activity transition 动画过程中可以正常渲染页面
     */
    private void setDrawDuringWindowsAnimating(View view) {
        if (Build.VERSION.SDK_INT > Build.VERSION_CODES.M
                || Build.VERSION.SDK_INT < Build.VERSION_CODES.JELLY_BEAN_MR1) {
            // 1 android n以上  & android 4.1以下不存在此问题，无须处理
            return;
        }
        // 4.2不存在setDrawDuringWindowsAnimating，需要特殊处理
        if (Build.VERSION.SDK_INT < Build.VERSION_CODES.JELLY_BEAN_MR1) {
            handleDispatchDoneAnimating(view);
            return;
        }
        try {
            // 4.3及以上，反射setDrawDuringWindowsAnimating来实现动画过程中渲染
            ViewParent rootParent = view.getRootView().getParent();
            Method method = rootParent.getClass()
                    .getDeclaredMethod("setDrawDuringWindowsAnimating", boolean.class);
            method.setAccessible(true);
            method.invoke(rootParent, true);
        } catch (Exception e) {
            e.printStackTrace();
        }
    }

    /**
     * android4.2可以反射handleDispatchDoneAnimating来解决
     */
    private void handleDispatchDoneAnimating(View paramView) {
        try {
            ViewParent localViewParent = paramView.getRootView().getParent();
            Class localClass = localViewParent.getClass();
            Method localMethod = localClass.getDeclaredMethod("handleDispatchDoneAnimating");
            localMethod.setAccessible(true);
            localMethod.invoke(localViewParent);
        } catch (Exception localException) {
            localException.printStackTrace();
        }
    }

    @Override
    public void init(MSCRuntime runtime) {
        this.runtime = runtime;
        mResFilesEnsure = new ResFilesEnsure(runtime.getMSCAppModule());
    }

    @Override
    public void evaluateJavascript(WebViewJavaScript script, @Nullable ValueCallback<String> resultCallback) {
        mWebView.evaluateJavascript(script.buildJavaScriptString(true), resultCallback);
    }

    @Override
    public void onCreate() {

    }

    @Override
    public void onDestroy() {
        try {
            if (isDestroyed) {
                MSCLog.d(tag(), "NormalWebView is destroyed");
                return;
            }
            isDestroyed = true;
            mWebViewFullScreenListener = null;
            mWebView.setWebChromeClient(null);
            releaseConfigCallback();
            resetAccessibilityEnabled();
            mWebView.destroy();
        } catch (Throwable t) {
            MSCLog.e(tag(), "destroy exception");
        }
    }

    @Override
    public void loadUrl(String url) {
        mWebView.loadUrl(url);
    }

    @Override
    public void loadDataWithBaseURL(String baseUrl, String data, String mimeType, String encoding, String failUrl) {
        mWebView.loadDataWithBaseURL(baseUrl, data, mimeType, encoding, failUrl);
    }

    @Override
    public String getUrl() {
        return mWebView.getUrl();
    }


    @Override
    public long getCreateTimeMillis() {
        return this.createTimeMillis;
    }

    @SuppressWarnings({"AddJavascriptInterface", "JavascriptInterface"})
    @Override
    public void addJavascriptInterface(Object object, String name) {
        mWebView.addJavascriptInterface(object, name);
    }

    public String tag() {
        return "SystemWebView";
    }

    @Override
    public View getWebView() {
        return mWebView;
    }

    @Override
    public void requestContentLayout() {
        mWebView.requestLayout();
    }

    @Override
    public void scrollContentY(int offset) {
        mWebView.scrollBy(0, offset);
    }

    @Override
    public void setOnContentScrollChangeListener(OnContentScrollChangeListener listener) {
        this.mWebScrollChangeListener = listener;
    }

    public String getUserAgentString() {
        return mWebView.getSettings().getUserAgentString();
    }

    @Override
    public void setUserAgentString(String userAgentString) {
        mWebView.getSettings().setUserAgentString(userAgentString);
    }

    @Override
    public int getContentHeight() {
        return (int) (mWebView.getContentHeight() * mWebView.getScale());
    }

    @Override
    public int getContentScrollY() {
        return mWebView.getScrollY();
    }

    @Override
    public void setOnPageFinishedListener(OnPageFinishedListener onPageFinishedListener) {
        this.mWebViewClient.setOnPageFinishedListener(onPageFinishedListener);
    }

    @Override
    public void onShow() {
        this.mWebView.onResume();
    }

    @Override
    public void onHide() {
        this.mWebView.onPause();
    }

    @Override
    public void setOnReloadListener(OnReloadListener listener) {
        this.mWebViewClient.setOnRenderProcessGoneListener(listener);
    }

    public void bindPageId(int viewId) {
        this.pageId = viewId;
    }

    private void removeJavaInterface() {
        try {
            Method removeJavascriptInterface = mWebView.getClass().getMethod("removeJavascriptInterface", String.class);
            if (removeJavascriptInterface != null) {
                removeJavascriptInterface.invoke(mWebView, "searchBoxJavaBridge_");
                removeJavascriptInterface.invoke(mWebView, "accessibility");
                removeJavascriptInterface.invoke(mWebView, "accessibilityTraversal");
            }
        } catch (Exception e) {
            e.printStackTrace();
        }
    }

    private void releaseConfigCallback() {
        if (Build.VERSION.SDK_INT < Build.VERSION_CODES.KITKAT) { // KITKAT
            try {
                Field sConfigCallback = Class.forName("android.webkit.BrowserFrame").getDeclaredField("sConfigCallback");
                if (sConfigCallback != null) {
                    sConfigCallback.setAccessible(true);
                    sConfigCallback.set(null, null);
                }
            } catch (Exception e) {
                e.printStackTrace();
            }
        }
    }

    private void fixedAccessibilityInjectorException() {
        if (Build.VERSION.SDK_INT == Build.VERSION_CODES.JELLY_BEAN_MR1
                && mIsAccessibilityEnabledOriginal == null
                && isAccessibilityEnabled()) {
            mIsAccessibilityEnabledOriginal = true;
            setAccessibilityEnabled(false);
        }
    }

    private boolean isAccessibilityEnabled() {
        AccessibilityManager am = (AccessibilityManager) mContext.getSystemService(Context.ACCESSIBILITY_SERVICE);
        return am.isEnabled();
    }

    private void setAccessibilityEnabled(boolean enabled) {
        AccessibilityManager am = (AccessibilityManager) mContext.getSystemService(Context.ACCESSIBILITY_SERVICE);
        try {
            Method setAccessibilityState = am.getClass().getDeclaredMethod("setAccessibilityState", boolean.class);
            setAccessibilityState.setAccessible(true);
            setAccessibilityState.invoke(am, enabled);
            setAccessibilityState.setAccessible(false);
        } catch (Throwable e) {
            e.printStackTrace();
        }
    }

    private void resetAccessibilityEnabled() {
        if (mIsAccessibilityEnabledOriginal != null) {
            setAccessibilityEnabled(mIsAccessibilityEnabledOriginal);
        }
    }

    public static Pair<Boolean, String> isWebViewPackageException(Throwable e) {
        String messageCause = e.getCause() == null ? e.toString() : e.getCause().toString();
        String trace = Log.getStackTraceString(e);
        if (trace.contains("android.content.pm.PackageManager$NameNotFoundException")
                || trace.contains("java.lang.RuntimeException: Cannot load WebView")
                || trace.contains("android.webkit.WebViewFactory$MissingWebViewPackageException: Failed to load WebView provider: No WebView installed")) {

            MSCLog.e("HeraWebView", "isWebViewPackageException" + e.getMessage());
            return new Pair<>(true, "WebView load failed, " + messageCause);
        }
        return new Pair<>(false, messageCause);
    }

    @Override
    public long getWebViewInitializationDuration() {
        return mNormalWebViewInitializationDuration;
    }

    @Override
    public WebViewCacheManager.WebViewCreateScene getWebViewCreateScene() {
        return createScene;
    }

    @Override
    public void setCreateScene(WebViewCacheManager.WebViewCreateScene createScene) {
        this.createScene = createScene;
    }

    @Override
    public WebViewFirstPreloadStateManager.PreloadState getPreloadState() {
        return preloadState == null ? WebViewFirstPreloadStateManager.getInstance().getPreloadState() : preloadState;
    }

    @Override
    public void setPreloadState(WebViewFirstPreloadStateManager.PreloadState preloadState) {
        this.preloadState = preloadState;
    }

    @Override
    public void setWebViewBackgroundColor(int color) {
        mWebView.setBackgroundColor(color);
    }

    @RequiresApi(api = Build.VERSION_CODES.M)
    @Override
    public void createMessagePort(IMSCModule mscModule, ExecutorContext executorContext) {
        // 系统webView暂不开启messagePort，Android 6.0.1和7.1.2创建messagePort偶现crash。
        // https://ones.sankuai.com/ones/product/31464/workItem/defect/detail/83709145?activeTabName=first
    }

    // 发送一条初始化消息，初始化消息通道，向前端传业务id白名单
    @RequiresApi(api = Build.VERSION_CODES.M)
    @Override
    public void transferPortToJavaScript() {
        // 系统webView暂不开启messagePort，Android 6.0.1和7.1.2创建messagePort偶现crash。
        // https://ones.sankuai.com/ones/product/31464/workItem/defect/detail/83709145?activeTabName=first
    }

    @RequiresApi(api = Build.VERSION_CODES.M)
    @Override
    public void postMessageWithNativeMessagePort(WebViewJavaScript script) {
        // 系统webView暂不开启messagePort，Android 6.0.1和7.1.2创建messagePort偶现crash。
        // https://ones.sankuai.com/ones/product/31464/workItem/defect/detail/83709145?activeTabName=first
    }

    @Override
    public boolean messagePortReady() {
        return nativePort != null && jsPortTransferred;
    }

    @RequiresApi(api = Build.VERSION_CODES.M)
    @Override
    public void messagePortClose() {
        // 系统webView暂不开启messagePort，Android 6.0.1和7.1.2创建messagePort偶现crash。
        // https://ones.sankuai.com/ones/product/31464/workItem/defect/detail/83709145?activeTabName=first
    }

    private class NormalWebViewClient extends WebViewClient {

        private OnPageFinishedListener mOnPageFinishedListener;
        private OnReloadListener mOnReloadListener;
        private Context mContext;
        private DefaultWebResponseBuild webResponseBuild = new DefaultWebResponseBuild();

        public NormalWebViewClient(Context context) {
            mContext = context;
        }

        public NormalWebViewClient setOnPageFinishedListener(OnPageFinishedListener onPageFinishedListener) {
            mOnPageFinishedListener = onPageFinishedListener;
            return this;
        }

        public NormalWebViewClient setOnRenderProcessGoneListener(OnReloadListener listener) {
            mOnReloadListener = listener;
            return this;
        }

        @Override
        public void onPageStarted(WebView view, String url, Bitmap favicon) {
            super.onPageStarted(view, url, favicon);
            if (mOnPageFinishedListener != null) {
                mOnPageFinishedListener.onPageStarted(url, favicon);
            }
        }

        /**
         * 处理RenderProcess jni crash 别把app杀掉
         *
         * @param view
         * @param detail
         * @return
         */
        // https://developer.android.com/reference/android/webkit/WebViewClient.html#onRenderProcessGone(android.webkit.WebView,%20android.webkit.RenderProcessGoneDetail)
        @TargetApi(Build.VERSION_CODES.O)
        @Override
        public boolean onRenderProcessGone(WebView view, RenderProcessGoneDetail detail) {
            if (mRenderProcessGoneTimeList.size() >= MAX_RENDER_PROCESS_GONE_ERROR_SIZE) {
                mRenderProcessGoneTimeList.remove(0);
            }
            mRenderProcessGoneTimeList.add(System.currentTimeMillis());
            RenderProcessGoneHandler.handleRenderProcessGone(view, detail, "NormalWebView " + view.getUrl(), runtime, mOnReloadListener);
            return true;
        }

        @Override
        public void onPageFinished(WebView view, String url) {
            super.onPageFinished(view, url);
            if (mOnPageFinishedListener != null) {
                mOnPageFinishedListener.onPageFinished(url, null);
            }
        }

        @TargetApi(Build.VERSION_CODES.LOLLIPOP)
        @Override
        public WebResourceResponse shouldInterceptRequest(WebView view, WebResourceRequest request) {
            final String url = request.getUrl().toString();
            WebResourceResponse resource = (WebResourceResponse) WebViewFileFilter.interceptResource(view.getContext(), runtime.getFileModule(), url, webResponseBuild, mResFilesEnsure, runtime.getMSCAppModule().enableAsyncSubPkg());
            return resource != null ? resource : super.shouldInterceptRequest(view, request);
        }

        @Override
        public WebResourceResponse shouldInterceptRequest(WebView view, String url) {
//            HeraTrace.d("InterceptRequest", String.format("url=%s", url));
            WebResourceResponse resource = (WebResourceResponse) WebViewFileFilter.interceptResource(view.getContext(), runtime.getFileModule(), url, webResponseBuild, mResFilesEnsure, runtime.getMSCAppModule().enableAsyncSubPkg());
            return resource != null ? resource : super.shouldInterceptRequest(view, url);
        }

        public static final String HTTPS = "https";
        public static final String HTTP = "http";
        public static final String CONTENT_TYPE = "Content-Type";

        //该方法未被使用
//        private void interceptHttpResource(String url) {
//            if (TextUtils.isEmpty(url)) {
//                return;
//            }
//
//            Uri uri = Uri.parse(url);
//            if (null == uri) {
//                return;
//            }
//
//            if (!HTTPS.equalsIgnoreCase(uri.getScheme())) {
//                return;
//            }
//
//            if (HTTP.equalsIgnoreCase(uri.getScheme())) {
//                Log.e("http-warning", url);
//                return;
//            }
//
//            String filename = stringToMD5(url);
//
//            File downloadedFile = new File(cacheDir, filename);
//
//            downloadResource(url, downloadedFile);
//        }

        private void downloadResource(String url, File downloadedFile) {

            try {
                OkHttpClient okHttpClient = new OkHttpClient();
                Request request = new Request.Builder().url(url).build();
                Response response = okHttpClient.newCall(request).execute();
                String type = response.header(CONTENT_TYPE);

                if (isImageType(type)) {
                    BufferedSink sink = Okio.buffer(Okio.sink(downloadedFile));
                    ;
                    try {
                        sink.writeAll(response.body().source());
                        sink.flush();

                        Bitmap bitmap = BitmapFactory.decodeFile(downloadedFile.getAbsolutePath());
                        int height = bitmap.getHeight();
                        int width = bitmap.getWidth();

                        if (height >= 1000 || width >= 1000) {
                            Log.e("image-warning", "width:" + width + "height" + height);
                            Log.e("image-warning", "url:" + url);
                            Log.e("image-warning", "local-path:" + downloadedFile.getAbsolutePath());
                            ToastUtils.toast("大图警告，详见logcat 过滤image-warning");
                        } else {
                            downloadedFile.delete();
                        }
                    } catch (Exception e) {

                    } finally {
                        if (null != sink) {
                            sink.close();
                        }
                    }
                }
            } catch (IOException e) {
                e.printStackTrace();
            }

        }

        public boolean isImageType(String type) {
            switch (type) {
                case "image/png":
                case "image/gif":
                case "image/jpeg":
                case "image/webp":
                    return true;
            }
            return false;
        }

        public String stringToMD5(String md5) {
            try {
                java.security.MessageDigest md = java.security.MessageDigest.getInstance("MD5");
                byte[] array = md.digest(md5.getBytes());
                StringBuffer sb = new StringBuffer();
                for (int i = 0; i < array.length; ++i) {
                    sb.append(Integer.toHexString((array[i] & 0xFF) | 0x100).substring(1, 3));
                }
                return sb.toString();
            } catch (java.security.NoSuchAlgorithmException e) {
            }
            return null;
        }
    }

    @Override
    public String toString() {
        return "NormalWebView{@" + Integer.toHexString(hashCode()) + "}";
    }

    @Override
    public String getConsoleLogErrorMessage() {
        return mConsoleLogErrorMessage;
    }

    @Override
    public List<Long> getRenderProcessGoneTimeList() {
        return mRenderProcessGoneTimeList;
    }
}
