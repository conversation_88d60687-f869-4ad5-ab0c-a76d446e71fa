package com.meituan.msc.modules.page.render.webview.impl;

import android.annotation.TargetApi;
import android.content.Context;
import android.graphics.Bitmap;
import android.os.Build;
import android.os.SystemClock;
import android.support.annotation.Nullable;
import android.util.Log;
import android.util.Pair;
import android.view.View;
import android.view.accessibility.AccessibilityManager;
import android.webkit.ConsoleMessage;
import android.webkit.RenderProcessGoneDetail;
import android.webkit.ValueCallback;
import android.webkit.WebChromeClient;
import android.webkit.WebResourceRequest;
import android.webkit.WebResourceResponse;
import android.webkit.WebSettings;
import android.webkit.WebView;
import android.webkit.WebViewClient;

import com.meituan.dio.easy.DioFile;
import com.meituan.msc.common.utils.MSCStorageUtil;
import com.meituan.msc.common.utils.MimeHelper;
import com.meituan.msc.common.utils.ViewUtils;
import com.meituan.msc.modules.api.RenderProcessGoneHandler;
import com.meituan.msc.modules.engine.MSCRuntime;
import com.meituan.msc.modules.devtools.DebugHelper;
import com.meituan.msc.modules.manager.ExecutorContext;
import com.meituan.msc.modules.manager.IMSCModule;
import com.meituan.msc.modules.page.render.webview.IWebView;
import com.meituan.msc.modules.page.render.webview.OnContentScrollChangeListener;
import com.meituan.msc.modules.page.render.webview.OnPageFinishedListener;
import com.meituan.msc.modules.page.render.webview.OnReloadListener;
import com.meituan.msc.modules.page.render.webview.OnWebViewFullScreenListener;
import com.meituan.msc.modules.page.render.webview.WebViewCacheManager;
import com.meituan.msc.modules.page.render.webview.WebViewFirstPreloadStateManager;
import com.meituan.msc.modules.page.render.webview.WebViewJavaScript;
import com.meituan.msc.modules.reporter.MSCLog;

import java.io.FileNotFoundException;
import java.io.IOException;
import java.lang.reflect.Field;
import java.lang.reflect.Method;
import java.util.HashMap;
import java.util.List;
import java.util.Map;

@TargetApi(Build.VERSION_CODES.KITKAT)
public class SimpleWebView implements IWebView {

    private Boolean mIsAccessibilityEnabledOriginal;
    private WebView mWebView;
    private final Context mContext;
    private volatile boolean isDestroyed = false;
    private long mSimpleWebViewInitializationDuration = 0;
    private volatile String mConsoleLogErrorMessage = "";
    private final long createTimeMillis = System.currentTimeMillis();

    public SimpleWebView(Context context) {
        mContext = context;
        initSetting();
    }

    private void initSetting() {
        long start = SystemClock.elapsedRealtime();
        mWebView = new WebView(mContext);
        mSimpleWebViewInitializationDuration = SystemClock.elapsedRealtime() - start;
        mWebView.setOverScrollMode(View.OVER_SCROLL_NEVER);

        fixedAccessibilityInjectorException();
        removeJavaInterface();

        WebSettings webSetting = mWebView.getSettings();
        webSetting.setAllowFileAccess(true);
        webSetting.setAllowFileAccessFromFileURLs(true);
        webSetting.setBuiltInZoomControls(true);
        webSetting.setDisplayZoomControls(false);
        webSetting.setSupportMultipleWindows(false);
        webSetting.setAppCacheEnabled(true);
        webSetting.setDomStorageEnabled(true);
//        webSetting.setDatabaseEnabled(true);
        webSetting.setJavaScriptEnabled(true);
        webSetting.setGeolocationEnabled(true);
        webSetting.setUseWideViewPort(true);
        webSetting.setLoadWithOverviewMode(true);
//        webSetting.setCacheMode(WebSettings.LOAD_NO_CACHE);
//        String ua = webSetting.getUserAgentString();
//        webSetting.setUserAgentString(String.format("%s Hera(version/%s)", ua, HeraConfig.VERSION));
        mWebView.setVerticalScrollBarEnabled(false);
        mWebView.setHorizontalScrollBarEnabled(false);

        if (Build.VERSION.SDK_INT > Build.VERSION_CODES.ICE_CREAM_SANDWICH) {
            webSetting.setTextZoom(100);
        }

//        webSetting.setDatabasePath(mContext.getFilesDir().getParentFile().getAbsolutePath() + "/databases/");
        webSetting.setAppCacheMaxSize(10 * 1024 * 1024);
        webSetting.setAppCachePath(WebViewCacheManager.getWebAppPath(mContext));

//        IX5WebViewExtension x5WebViewExtension = getX5WebViewExtension();
//        if (x5WebViewExtension != null) {
//            x5WebViewExtension.setScrollBarFadingEnabled(false);
//            x5WebViewExtension.setVerticalScrollBarEnabled(false);
//            x5WebViewExtension.setHorizontalScrollBarEnabled(false);
//        }
//
//        getView().setHorizontalScrollBarEnabled(false);
//        getView().setVerticalScrollBarEnabled(false);

        mWebView.setWebChromeClient(new WebChromeClient() {
            private String className = SimpleWebView.this.getClass().getSimpleName();

            // WebView video组件默认图兜底（问题修复：https://stackoverflow.com/questions/76700882/nullpointer-exception-on-bitmap-getwidth-at-chromium-trichromewebviewgoogle-aa）
            @Override
            public Bitmap getDefaultVideoPoster() {
                return ViewUtils.getDefaultVideoPoster(super.getDefaultVideoPoster());
            }

            @Override
            public boolean onConsoleMessage(ConsoleMessage consoleMessage) {
                if (consoleMessage.messageLevel() == ConsoleMessage.MessageLevel.ERROR) {
                    mConsoleLogErrorMessage = consoleMessage.message();
                } else {
                    Log.i("webview_log_" + className,
                            consoleMessage.message());
                }
                return super.onConsoleMessage(consoleMessage);
            }
        });

        mWebView.setWebViewClient(new NormalWebViewClient());
    }

    @Override
    public void init(MSCRuntime runtime) {
    }

    @Override
    public void evaluateJavascript(WebViewJavaScript script, @Nullable ValueCallback<String> resultCallback) {
        mWebView.evaluateJavascript(script.buildJavaScriptString(true), resultCallback);
    }

    @Override
    public void onCreate() {

    }

    @Override
    public void onDestroy() {
        try {
            if (isDestroyed) {
                MSCLog.d(tag(), "SimpleWebView is destroyed");
                return;
            }
            isDestroyed = true;

            mWebView.setWebChromeClient(null);
            releaseConfigCallback();
            resetAccessibilityEnabled();
            mWebView.destroy();
        } catch (Throwable t) {
            MSCLog.e(tag(), "destroy exception");
        }
    }

    @Override
    public void loadUrl(String url) {
        mWebView.loadUrl(url);
    }

    @Override
    public void loadDataWithBaseURL(String baseUrl, String data, String mimeType, String encoding, String failUrl) {
        mWebView.loadDataWithBaseURL(baseUrl, data, mimeType, encoding, failUrl);
    }

    @Override
    public String getUrl() {
        return mWebView.getUrl();
    }

    @Override
    public long getCreateTimeMillis() {
        return this.createTimeMillis;
    }

    @SuppressWarnings({"AddJavascriptInterface", "JavascriptInterface"})
    @Override
    public void addJavascriptInterface(Object object, String name) {
        mWebView.addJavascriptInterface(object, name);
    }

    public String tag() {
        return "HeraWebView";
    }

    @Override
    public View getWebView() {
        return mWebView;
    }

    @Override
    public void requestContentLayout() {
        mWebView.requestLayout();
    }

    @Override
    public void scrollContentY(int offset) {
        mWebView.scrollBy(0, offset);
    }

    @Override
    public void setOnContentScrollChangeListener(OnContentScrollChangeListener listener) {
//        this.mWebScrollChangeListener = listener;
    }

    public String getUserAgentString() {
        return mWebView.getSettings().getUserAgentString();
    }

    @Override
    public void setUserAgentString(String userAgentString) {
        mWebView.getSettings().setUserAgentString(userAgentString);
    }

    @Override
    public int getContentHeight() {
        return (int) (mWebView.getContentHeight() * mWebView.getScale());
    }

    @Override
    public int getContentScrollY() {
        return mWebView.getScrollY();
    }

    @Override
    public void setOnPageFinishedListener(OnPageFinishedListener url) {
        throw new RuntimeException("SimpleWebview not support setOnPageFinishedListener!");
    }

    @Override
    public void onShow() {

    }

    @Override
    public void onHide() {

    }

    public void bindPageId(int viewId) {

    }

    @Override
    public void setOnFullScreenListener(OnWebViewFullScreenListener listener) {

    }

    @Override
    public void setOnReloadListener(OnReloadListener listener) {

    }

    private void removeJavaInterface() {
        try {
            Method removeJavascriptInterface = mWebView.getClass().getMethod("removeJavascriptInterface", String.class);
            if (removeJavascriptInterface != null) {
                removeJavascriptInterface.invoke(mWebView, "searchBoxJavaBridge_");
                removeJavascriptInterface.invoke(mWebView, "accessibility");
                removeJavascriptInterface.invoke(mWebView, "accessibilityTraversal");
            }
        } catch (Exception e) {
            e.printStackTrace();
        }
    }

    private void releaseConfigCallback() {
        if (Build.VERSION.SDK_INT < Build.VERSION_CODES.KITKAT) { // KITKAT
            try {
                Field sConfigCallback = Class.forName("android.webkit.BrowserFrame").getDeclaredField("sConfigCallback");
                if (sConfigCallback != null) {
                    sConfigCallback.setAccessible(true);
                    sConfigCallback.set(null, null);
                }
            } catch (Exception e) {
                e.printStackTrace();
            }
        }
    }

    private void fixedAccessibilityInjectorException() {
        if (Build.VERSION.SDK_INT == Build.VERSION_CODES.JELLY_BEAN_MR1
                && mIsAccessibilityEnabledOriginal == null
                && isAccessibilityEnabled()) {
            mIsAccessibilityEnabledOriginal = true;
            setAccessibilityEnabled(false);
        }
    }

    private boolean isAccessibilityEnabled() {
        AccessibilityManager am = (AccessibilityManager) mContext.getSystemService(Context.ACCESSIBILITY_SERVICE);
        return am.isEnabled();
    }

    private void setAccessibilityEnabled(boolean enabled) {
        AccessibilityManager am = (AccessibilityManager) mContext.getSystemService(Context.ACCESSIBILITY_SERVICE);
        try {
            Method setAccessibilityState = am.getClass().getDeclaredMethod("setAccessibilityState", boolean.class);
            setAccessibilityState.setAccessible(true);
            setAccessibilityState.invoke(am, enabled);
            setAccessibilityState.setAccessible(false);
        } catch (Throwable e) {
            e.printStackTrace();
        }
    }

    private void resetAccessibilityEnabled() {
        if (mIsAccessibilityEnabledOriginal != null) {
            setAccessibilityEnabled(mIsAccessibilityEnabledOriginal);
        }
    }

    public static Pair<Boolean, String> isWebViewPackageException(Throwable e) {
        String messageCause = e.getCause() == null ? e.toString() : e.getCause().toString();
        String trace = Log.getStackTraceString(e);
        if (trace.contains("android.content.pm.PackageManager$NameNotFoundException")
                || trace.contains("java.lang.RuntimeException: Cannot load WebView")
                || trace.contains("android.webkit.WebViewFactory$MissingWebViewPackageException: Failed to load WebView provider: No WebView installed")) {

            MSCLog.e("HeraWebView", "isWebViewPackageException" + e.getMessage());
            return new Pair<>(true, "WebView load failed, " + messageCause);
        }
        return new Pair<>(false, messageCause);
    }

    @Override
    public long getWebViewInitializationDuration() {
        return mSimpleWebViewInitializationDuration;
    }

    @Override
    public WebViewCacheManager.WebViewCreateScene getWebViewCreateScene() {
        return null;
    }

    @Override
    public void setCreateScene(WebViewCacheManager.WebViewCreateScene createScene) {
    }

    @Override
    public WebViewFirstPreloadStateManager.PreloadState getPreloadState() {
        return null;
    }

    @Override
    public void setPreloadState(WebViewFirstPreloadStateManager.PreloadState preloadState) {

    }

    @Override
    public void setWebViewBackgroundColor(int color) {
        mWebView.setBackgroundColor(color);
    }

    @Override
    public void createMessagePort(IMSCModule mscModule, ExecutorContext executorContext) {
    }

    @Override
    public void transferPortToJavaScript() {
    }

    @Override
    public void postMessageWithNativeMessagePort(WebViewJavaScript script) {
    }

    @Override
    public boolean messagePortReady() {
        return false;
    }

    @Override
    public void messagePortClose() {
    }

    @Override
    public String getConsoleLogErrorMessage() {
        return mConsoleLogErrorMessage;
    }

    @Override
    public List<Long> getRenderProcessGoneTimeList() {
        return null;
    }

    public static class NormalWebViewClient extends WebViewClient {

        public NormalWebViewClient() {
        }


        @Override
        public void onPageStarted(WebView view, String url, Bitmap favicon) {
            super.onPageStarted(view, url, favicon);
        }


        /**
         * 处理RenderProcess jni crash 别把app杀掉
         *
         * @param view
         * @param detail
         * @return
         */
        // https://developer.android.com/reference/android/webkit/WebViewClient.html#onRenderProcessGone(android.webkit.WebView,%20android.webkit.RenderProcessGoneDetail)
        @TargetApi(Build.VERSION_CODES.O)
        @Override
        public boolean onRenderProcessGone(WebView view, RenderProcessGoneDetail detail) {
            RenderProcessGoneHandler.handleRenderProcessGone(view, detail, "SimpleWebView", null, null);
            return true;
        }

        @Override
        public void onPageFinished(WebView view, String url) {
            super.onPageFinished(view, url);
        }

        @TargetApi(Build.VERSION_CODES.LOLLIPOP)
        @Override
        public WebResourceResponse shouldInterceptRequest(WebView view, WebResourceRequest request) {
            String url = request.getUrl().toString();
//            HeraTrace.d("SimpleWebViewInterceptRequest", String.format("url=%s", url));
            WebResourceResponse resource = interceptResource(view.getContext(), url);
            return resource != null ? resource : super.shouldInterceptRequest(view, request);
        }

        @Override
        public WebResourceResponse shouldInterceptRequest(WebView view, String url) {
//            HeraTrace.d("SimpleWebViewInterceptRequest", String.format("url=%s", url));
            WebResourceResponse resource = interceptResource(view.getContext(), url);
            return resource != null ? resource : super.shouldInterceptRequest(view, url);
        }

//    @Override
//    public WebResourceResponse shouldInterceptRequest(WebView webView, WebResourceRequest webResourceRequest, Bundle bundle) {
//        return super.shouldInterceptRequest(webView, webResourceRequest, bundle);
//    }

        private WebResourceResponse interceptResource(Context context, String url) {
            if (DebugHelper.debugWebView) {
                if (url.startsWith(MSCStorageUtil.SCHEME_LOCAL_FILE + MSCStorageUtil.getMSCPath(context))) {
                    return getResource(MimeHelper.getMIME(url), new DioFile(url.substring(MSCStorageUtil.SCHEME_LOCAL_FILE.length())));
                }
            }
            return null;
        }

        private WebResourceResponse getResource(String mine, DioFile file) {
//            if (file == null) {
//                return null;
//            }
            if (!file.exists() && !file.isFile()) {
                return null;
            }
            try {
                WebResourceResponse response = new WebResourceResponse(mine, "UTF-8", file.getInputStream());
                if (Build.VERSION.SDK_INT >= Build.VERSION_CODES.LOLLIPOP) {
                    Map<String, String> headers = response.getResponseHeaders();
                    if (headers == null) {
                        headers = new HashMap<>();
                    }
//                    Cache-Control: no-cache, no-store, must-revalidate
//                    Pragma: no-cache
//                    Expires: 0
                    headers.put("Cache-Control", "no-cache, no-store, must-revalidate");
                    headers.put("Pragma", "no-cache");
                    headers.put("Expires", "0");
                    response.setResponseHeaders(headers);
                }
                return response;
            } catch (FileNotFoundException e) {
                e.printStackTrace();
            } catch (IOException e) {
                isWebViewPackageException(e);
                e.printStackTrace();
            }
            return null;
        }

    }

}
