//package com.meituan.msc.modules.page.render.webview.impl;
//
//import android.annotation.SuppressLint;
//import android.content.Context;
//import android.graphics.Bitmap;
//import android.os.Build;
//import android.support.annotation.Nullable;
//import android.util.Log;
//import android.util.Pair;
//import android.view.View;
//import android.view.accessibility.AccessibilityManager;
//import android.webkit.ValueCallback;
//
//import com.meituan.msc.modules.api.RenderProcessGoneHandler;
//import com.meituan.msc.modules.engine.MSCRuntime;
//import com.meituan.msc.modules.page.render.webview.IWebView;
//import com.meituan.msc.modules.page.render.webview.OnContentScrollChangeListener;
//import com.meituan.msc.modules.page.render.webview.OnPageFinishedListener;
//import com.meituan.msc.modules.page.render.webview.OnReloadListener;
//import com.meituan.msc.modules.page.render.webview.OnWebViewFullScreenListener;
//import com.meituan.msc.modules.page.render.webview.WebChromeClientCustomViewCallback;
//import com.meituan.msc.modules.page.view.WebViewFileFilter;
//import com.meituan.msc.common.resource.IWebResponseBuild;
//import com.meituan.msc.modules.reporter.MSCLog;
//import com.meituan.msc.common.utils.CIPStorageFileUtil;
//import com.meituan.msc.common.utils.ToastUtils;
//import com.tencent.smtt.export.external.extension.interfaces.IX5WebViewExtension;
//import com.tencent.smtt.export.external.interfaces.ConsoleMessage;
//import com.tencent.smtt.export.external.interfaces.IX5WebChromeClient;
//import com.tencent.smtt.export.external.interfaces.WebResourceRequest;
//import com.tencent.smtt.export.external.interfaces.WebResourceResponse;
//import com.tencent.smtt.sdk.WebChromeClient;
//import com.tencent.smtt.sdk.WebSettings;
//import com.tencent.smtt.sdk.WebView;
//import com.tencent.smtt.sdk.WebViewClient;
//
//import java.io.InputStream;
//import java.lang.reflect.Field;
//import java.lang.reflect.Method;
//import java.util.HashMap;
//import java.util.Map;
//
//public class X5WebView implements IWebView {
//
//    private Boolean mIsAccessibilityEnabledOriginal;
//    private WebView mWebView;
//    private RenderProcessGoneHandler mRenderProcessGoneHandler;
//    private Context mContext;
//    private X5WebViewClient mWebViewClient;
//    private OnContentScrollChangeListener mWebScrollChangeListener;
//    private int pageId;
//    OnWebViewFullScreenListener mWebViewFullScreenListener;
//    private volatile boolean isDestroyed = false;
//    private MSCRuntime runtime;
//
//    public X5WebView(Context context, MSCRuntime runtime) {
//        mContext = context;
//        initSetting();
//    }
//
//    private void initSetting() {
//        mWebView = new WebView(mContext) {
//            @Override
//            protected void onScrollChanged(int l, int t, int oldl, int oldt) {
//                super.onScrollChanged(l, t, oldl, oldt);
//                if (mWebScrollChangeListener != null) {
//                    mWebScrollChangeListener.onWebScrollChange(l, t, oldl, oldt);
//                }
//            }
//
//            @Override
//            public void setOverScrollMode(int mode) {
//                try {
//                    super.setOverScrollMode(mode);
//                } catch (Throwable e) {
//                    Pair<Boolean, String> pair = isWebViewPackageException(e);
//                    if (pair.first) {
//                        ToastUtils.toast(pair.second);
//                        destroy();
//                    } else {
//                        throw e;
//                    }
//                }
//            }
//        };
//
//        fixedAccessibilityInjectorException();
//        removeJavaInterface();
//
//        WebSettings webSetting = mWebView.getSettings();
//        webSetting.setAllowFileAccess(true);
//        webSetting.setAllowFileAccessFromFileURLs(true);
//        webSetting.setBuiltInZoomControls(true);
//        webSetting.setDisplayZoomControls(false);
//        webSetting.setSupportMultipleWindows(false);
//        webSetting.setAppCacheEnabled(true);
//        webSetting.setDomStorageEnabled(true);
////        webSetting.setDatabaseEnabled(true);
//        webSetting.setJavaScriptEnabled(true);
//        webSetting.setGeolocationEnabled(true);
//        webSetting.setUseWideViewPort(true);
//
//        try {
//            webSetting.setMediaPlaybackRequiresUserGesture(false);
//        } catch (Exception e) {
//            //ignore
//        }
////        webSetting.setCacheMode(WebSettings.LOAD_NO_CACHE);
////        String ua = webSetting.getUserAgentString();
////        webSetting.setUserAgentString(String.format("%s Hera(version/%s)", ua, HeraConfig.VERSION));
//        mWebView.setVerticalScrollBarEnabled(false);
//        mWebView.setHorizontalScrollBarEnabled(false);
//
////        webSetting.setDatabasePath(mContext.getFilesDir().getParentFile().getAbsolutePath() + "/databases/");
//        webSetting.setAppCacheMaxSize(10 * 1024 * 1024);
//        webSetting.setAppCachePath(CIPStorageFileUtil.getFilesDir(mContext, "webviewcache").getAbsolutePath());
//
//        IX5WebViewExtension x5WebViewExtension = mWebView.getX5WebViewExtension();
//        if (x5WebViewExtension != null) {
//            x5WebViewExtension.setScrollBarFadingEnabled(false);
//            x5WebViewExtension.setVerticalScrollBarEnabled(false);
//            x5WebViewExtension.setHorizontalScrollBarEnabled(false);
//        }
//
//        mWebView.getView().setHorizontalScrollBarEnabled(false);
//        mWebView.getView().setVerticalScrollBarEnabled(false);
//
//        mWebView.setWebChromeClient(new WebChromeClient() {
//            private String className = X5WebView.this.getClass().getSimpleName();
//
//            @Override
//            public boolean onConsoleMessage(ConsoleMessage consoleMessage) {
//                if (consoleMessage.messageLevel() == ConsoleMessage.MessageLevel.ERROR) {
//                    System.out.println("webview_log_" + className +
//                            " [error] " + consoleMessage.message());
//                    System.out.println("webview_log_" + className +
//                            " [error] sourceId = " + consoleMessage.sourceId());
//                    System.out.println("webview_log_" + className +
//                            " [error] lineNumber = " + consoleMessage.lineNumber());
//                } else {
//                    Log.i("webview_log_" + className,
//                            consoleMessage.message());
//                }
//                return super.onConsoleMessage(consoleMessage);
//            }
//
//            @Override
//            public void onReceivedTitle(WebView webView, String title) {
//                super.onReceivedTitle(webView, title);
//                if (title.startsWith("msc-page:")) {
//                    webView.evaluateJavascript(String.format("document.title = '%s@page_%s@%s';", runtime.getMSCAppModule().getAppId(), pageId, title), null);
//                }
//            }
//
//            @Override
//            public void onShowCustomView(View view, IX5WebChromeClient.CustomViewCallback customViewCallback) {
//                super.onShowCustomView(view, customViewCallback);
//                if (mWebViewFullScreenListener != null) {
//                    mWebViewFullScreenListener.showCustomView(view, new WebChromeClientCustomViewCallback() {
//                        @Override
//                        public void onHideCustomView() {
//                            customViewCallback.onCustomViewHidden();
//                        }
//                    });
//                }
//            }
//
//            @Override
//            public void onHideCustomView() {
//                if (mWebViewFullScreenListener != null) {
//                    mWebViewFullScreenListener.hideCustomView();
//                }
//            }
//        });
//
//        mWebViewClient = new X5WebViewClient(mRenderProcessGoneHandler);
//        mWebViewClient.setWebView(mWebView);
//        mWebView.setWebViewClient(mWebViewClient);
//    }
//
//    @Override
//    public void evaluateJavascript(String script, @Nullable final ValueCallback<String> resultCallback) {
//        mWebView.evaluateJavascript(script, new com.tencent.smtt.sdk.ValueCallback<String>() {
//            @Override
//            public void onReceiveValue(String s) {
//                if (resultCallback != null) {
//                    resultCallback.onReceiveValue(s);
//                }
//            }
//        });
//    }
//
//    @Override
//    public void onCreate() {
//
//    }
//
//    @Override
//    public void onDestroy() {
//        try {
//            if (isDestroyed) {
//                MSCLog.d(tag(), "X5WebView is destroyed");
//                return;
//            }
//            isDestroyed = true;
//
//            mWebView.setWebChromeClient(null);
//            mWebView.removeJavascriptInterface("HeraJSCore");
//            releaseConfigCallback();
//            resetAccessibilityEnabled();
//            mWebView.destroy();
//        } catch (Throwable t) {
//            MSCLog.e(tag(), "destroy exception");
//        }
//    }
//
//    @Override
//    public void loadUrl(String url) {
//        mWebView.loadUrl(url);
//    }
//
//    @Override
//    public void loadDataWithBaseURL(String baseUrl, String data, String mimeType, String encoding, String failUrl) {
//        mWebView.loadDataWithBaseURL(baseUrl, data, mimeType, encoding, failUrl);
//    }
//
//    @Override
//    public String getUrl() {
//        return mWebView.getUrl();
//    }
//
//    @SuppressLint("JavascriptInterface")
//    @Override
//    public void addJavascriptInterface(Object object, String name) {
//        mWebView.addJavascriptInterface(object, name);
//    }
//
//    public String tag() {
//        return "X5WebView";
//    }
//
//    @Override
//    public View getWebView() {
//        return mWebView;
//    }
//
//    @Override
//    public void requestContentLayout() {
//        mWebView.requestLayout();
//    }
//
//    @Override
//    public void scrollContentY(int offset) {
//        mWebView.scrollBy(0, offset);
//    }
//
//    @Override
//    public void setOnContentScrollChangeListener(OnContentScrollChangeListener listener) {
//        this.mWebScrollChangeListener = listener;
//    }
//
//    public String getUserAgentString() {
//        return mWebView.getSettings().getUserAgentString();
//    }
//
//    @Override
//    public void setUserAgentString(String userAgentString) {
//        mWebView.getSettings().setUserAgentString(userAgentString);
//    }
//
//    @Override
//    public int getContentHeight() {
//        return (int) (mWebView.getContentHeight()* mWebView.getScale());
//    }
//
//    @Override
//    public int getContentScrollY() {
//        return mWebView.getScrollY();
//    }
//
//    @Override
//    public void setOnPageFinishedListener(OnPageFinishedListener pageFinishedListener) {
//        this.mWebViewClient.setOnPageFinishedListener(pageFinishedListener);
//    }
//
//    @Override
//    public void onShow() {
//        this.mWebView.onResume();
//    }
//
//    @Override
//    public void onHide() {
//        this.mWebView.onPause();
//    }
//
//    public void bindPageId(int viewId) {
//        pageId = viewId;
//    }
//
//    @Override
//    public void setOnFullScreenListener(OnWebViewFullScreenListener listener) {
//        if (mWebViewFullScreenListener == null) {
//            this.mWebViewFullScreenListener = listener;
//        }
//    }
//
//    @Override
//    public void setOnReloadListener(OnReloadListener listener) {
//        this.mWebViewClient.setOnReloadListener(listener);
//    }
//
//
//    private void removeJavaInterface() {
//        try {
//            Method removeJavascriptInterface = mWebView.getClass().getMethod("removeJavascriptInterface", String.class);
//            if (removeJavascriptInterface != null) {
//                removeJavascriptInterface.invoke(mWebView, "searchBoxJavaBridge_");
//                removeJavascriptInterface.invoke(mWebView, "accessibility");
//                removeJavascriptInterface.invoke(mWebView, "accessibilityTraversal");
//            }
//        } catch (Exception e) {
//            e.printStackTrace();
//        }
//    }
//
//    private void releaseConfigCallback() {
//        if (Build.VERSION.SDK_INT < Build.VERSION_CODES.KITKAT) { // KITKAT
//            try {
//                Field sConfigCallback = Class.forName("android.webkit.BrowserFrame").getDeclaredField("sConfigCallback");
//                if (sConfigCallback != null) {
//                    sConfigCallback.setAccessible(true);
//                    sConfigCallback.set(null, null);
//                }
//            } catch (Exception e) {
//                e.printStackTrace();
//            }
//        }
//    }
//
//    private void fixedAccessibilityInjectorException() {
//        if (Build.VERSION.SDK_INT == Build.VERSION_CODES.JELLY_BEAN_MR1
//                && mIsAccessibilityEnabledOriginal == null
//                && isAccessibilityEnabled()) {
//            mIsAccessibilityEnabledOriginal = true;
//            setAccessibilityEnabled(false);
//        }
//    }
//
//    private boolean isAccessibilityEnabled() {
//        AccessibilityManager am = (AccessibilityManager) mContext.getSystemService(Context.ACCESSIBILITY_SERVICE);
//        return am.isEnabled();
//    }
//
//    private void setAccessibilityEnabled(boolean enabled) {
//        AccessibilityManager am = (AccessibilityManager) mContext.getSystemService(Context.ACCESSIBILITY_SERVICE);
//        try {
//            Method setAccessibilityState = am.getClass().getDeclaredMethod("setAccessibilityState", boolean.class);
//            setAccessibilityState.setAccessible(true);
//            setAccessibilityState.invoke(am, enabled);
//            setAccessibilityState.setAccessible(false);
//        } catch (Throwable e) {
//            e.printStackTrace();
//        }
//    }
//
//    private void resetAccessibilityEnabled() {
//        if (mIsAccessibilityEnabledOriginal != null) {
//            setAccessibilityEnabled(mIsAccessibilityEnabledOriginal);
//        }
//    }
//
//    public static Pair<Boolean, String> isWebViewPackageException(Throwable e) {
//        String messageCause = e.getCause() == null ? e.toString() : e.getCause().toString();
//        String trace = Log.getStackTraceString(e);
//        if (trace.contains("android.content.pm.PackageManager$NameNotFoundException")
//                || trace.contains("java.lang.RuntimeException: Cannot load WebView")
//                || trace.contains("android.webkit.WebViewFactory$MissingWebViewPackageException: Failed to load WebView provider: No WebView installed")) {
//
//            MSCLog.e("HeraWebView", "isWebViewPackageException" + e.getMessage());
//            return new Pair<>(true, "WebView load failed, " + messageCause);
//        }
//        return new Pair<>(false, messageCause);
//    }
//
//    public  class X5WebViewClient extends WebViewClient implements IWebResponseBuild {
//
//        private OnPageFinishedListener mOnPageFinishedListener;
//        private RenderProcessGoneHandler renderProcessGoneHandler;
//        private OnReloadListener mOnRenderProcessGoneListener;
//
//        private WebView mWebView;
//
//        public X5WebViewClient(RenderProcessGoneHandler renderProcessGoneHandler) {
//            this.renderProcessGoneHandler = renderProcessGoneHandler;
//        }
//
//        public X5WebViewClient setWebView(WebView webView) {
//            mWebView = webView;
//            return this;
//        }
//
//        public X5WebViewClient setOnPageFinishedListener(OnPageFinishedListener onPageFinishedListener) {
//            mOnPageFinishedListener = onPageFinishedListener;
//            return this;
//        }
//
//        public X5WebViewClient setOnReloadListener(OnReloadListener listener) {
//            mOnRenderProcessGoneListener = listener;
//            return this;
//        }
//
//        @Override
//        public void onPageStarted(WebView webView, String s, Bitmap bitmap) {
//            super.onPageStarted(webView, s, bitmap);
//            if (mOnPageFinishedListener != null) {
//                mOnPageFinishedListener.onPageStarted(s, bitmap);
//            }
//        }
//
//
//        @Override
//        public boolean onRenderProcessGone(WebView webView, RenderProcessGoneDetail renderProcessGoneDetail) {
//            renderProcessGoneHandler.handleRenderProcessGone(webView, renderProcessGoneDetail.didCrash(),
//                    RenderProcessGoneHandler.RENDERER_PRIORITY_IMPORTANT, // android.webkit.WebView.RENDERER_PRIORITY_IMPORTANT,
//                    webView.getUrl(), runtime, mOnRenderProcessGoneListener);
//            return true;
//        }
//
//
//        @Override
//        public void onPageFinished(WebView view, String url) {
//            super.onPageFinished(view, url);
//            if (mOnPageFinishedListener != null) {
//                mOnPageFinishedListener.onPageFinished(url);
//            }
//        }
//
//        @Override
//        public WebResourceResponse shouldInterceptRequest(WebView view, WebResourceRequest request) {
//            String url = request.getUrl().toString();
////            HeraTrace.d("InterceptRequest", String.format("url=%s", url));
//            WebResourceResponse resource = (WebResourceResponse) WebViewFileFilter.interceptResource(view.getContext(), runtime.getMSCAppModule(), url, this);
//            return resource != null ? resource : super.shouldInterceptRequest(view, request);
//        }
//
//        @Override
//        public WebResourceResponse shouldInterceptRequest(WebView view, String url) {
////            HeraTrace.d("InterceptRequest", String.format("url=%s", url));
//            WebResourceResponse resource = (WebResourceResponse) WebViewFileFilter.interceptResource(view.getContext(), runtime.getMSCAppModule(), url, this);
//            return resource != null ? resource : super.shouldInterceptRequest(view, url);
//        }
//
//        @Override
//        public Object getResource(String mine, Map<String, String> headers, InputStream is) {
//            WebResourceResponse response = new WebResourceResponse(mine, "UTF-8", is);
//            if (headers != null && !headers.isEmpty()) {
//                Map<String, String> allHeaders = response.getResponseHeaders();
//                if (allHeaders == null) {
//                    allHeaders = new HashMap<>();
//                }
//                allHeaders.putAll(headers);
//                response.setResponseHeaders(headers);
//            }
//            return response;
//        }
//    }
//}
