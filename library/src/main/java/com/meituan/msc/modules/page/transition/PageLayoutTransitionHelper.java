package com.meituan.msc.modules.page.transition;

import android.animation.Animator;
import android.animation.AnimatorListenerAdapter;
import android.animation.LayoutTransition;
import android.animation.ObjectAnimator;
import android.animation.PropertyValuesHolder;
import android.support.annotation.NonNull;
import android.util.DisplayMetrics;
import android.view.View;
import android.view.animation.Animation;
import android.view.animation.TranslateAnimation;

import com.meituan.msc.common.config.MSCConfig;
import com.meituan.msc.extern.MSCEnvHelper;
import com.meituan.msc.modules.engine.MSCHornRollbackConfig;
import com.meituan.msc.modules.page.Page;

/**
 * Created by letty on 2022/4/25.
 **/
public class PageLayoutTransitionHelper {

    public static LayoutTransition getPopTransition(PageTransitionConfig config, Page page) {
        if (config != null) {
            if (config.popStyle == PageTransitionConfig.TransitionStyle.FADE_IN_FADE_OUT) {
                return getFadeInFadeOutTransition();
            } else if (config.popStyle == PageTransitionConfig.TransitionStyle.SLIDE_UP_SLIDE_DOWN) {
                return getSlideUpSlideDownTransition();
            } else if (config.popStyle == PageTransitionConfig.TransitionStyle.NONE) {
                return getNoAnimationLayoutTransition();
            } else if (config.popStyle == PageTransitionConfig.TransitionStyle.SHRINK) {
                return getShrinkTransition(config.pivotX, config.pivotY, page);
            }
        }
        return getSlideInSlideOutTransition();
    }

    public static LayoutTransition getPushTransition(PageTransitionConfig config) {
        if (config != null) {
            if (config.pushStyle == PageTransitionConfig.TransitionStyle.FADE_IN_FADE_OUT) {
                return getFadeInFadeOutTransition();
            } else if (config.pushStyle == PageTransitionConfig.TransitionStyle.SLIDE_UP_SLIDE_DOWN) {
                return getSlideUpSlideDownTransition();
            } else if (config.popStyle == PageTransitionConfig.TransitionStyle.NONE) {
                return getNoAnimationLayoutTransition();
            }
        }
        return getSlideInSlideOutTransition();
    }

    private static LayoutTransition getFadeInFadeOutTransition() {
        LayoutTransition fadeInFadeOutTransition = getCommonTransition();
        // View 页面转场动画
        fadeInFadeOutTransition.setAnimator(LayoutTransition.APPEARING, getFadeInAnimation());
        fadeInFadeOutTransition.setAnimator(LayoutTransition.DISAPPEARING, getFadeOutAnimation());
        return fadeInFadeOutTransition;
    }

    private static LayoutTransition getSlideUpSlideDownTransition() {
        LayoutTransition slideUpSlideDownTransition = getSlideVerticalTransition();
        // View 页面转场动画
        DisplayMetrics dm = MSCEnvHelper.getContext().getResources().getDisplayMetrics();
        slideUpSlideDownTransition.setAnimator(LayoutTransition.APPEARING, getSlideUpAnimation(dm.heightPixels));
        slideUpSlideDownTransition.setAnimator(LayoutTransition.DISAPPEARING, getSlideDownAnimation(dm.heightPixels));
        return slideUpSlideDownTransition;
    }

    private static LayoutTransition getSlideVerticalTransition() {
        LayoutTransition slideVerticalTransition = new LayoutTransition();
        // 动画开始延迟
        slideVerticalTransition.setStartDelay(LayoutTransition.APPEARING, 0);
        slideVerticalTransition.setStartDelay(LayoutTransition.DISAPPEARING, 0);
        slideVerticalTransition.setStartDelay(LayoutTransition.CHANGE_APPEARING, 0);
        slideVerticalTransition.setStartDelay(LayoutTransition.CHANGE_DISAPPEARING, 0);
        // 动画执行时间
        slideVerticalTransition.setDuration(LayoutTransition.APPEARING, 280);
        slideVerticalTransition.setDuration(LayoutTransition.DISAPPEARING, 280);
        slideVerticalTransition.setDuration(LayoutTransition.CHANGE_APPEARING, 280);
        slideVerticalTransition.setDuration(LayoutTransition.CHANGE_DISAPPEARING, 280);
        return slideVerticalTransition;
    }

    private static Animator getSlideUpAnimation(int start) {
        return ObjectAnimator.ofFloat(null, "translationY", start, 0);
    }

    private static Animator getSlideDownAnimation(int end) {
        return ObjectAnimator.ofFloat(null, "translationY", 0, end);
    }

    private static LayoutTransition getSlideInSlideOutTransition() {
        LayoutTransition slideInSlideOutTransition = getCommonTransition();
        // View 页面转场动画
        DisplayMetrics dm = MSCEnvHelper.getContext().getResources().getDisplayMetrics();
        slideInSlideOutTransition.setAnimator(LayoutTransition.APPEARING, getSlideInAnimation(dm.widthPixels));
        slideInSlideOutTransition.setAnimator(LayoutTransition.DISAPPEARING, getSileOutAnimation(dm.widthPixels));
        return slideInSlideOutTransition;
    }

    private static LayoutTransition getCommonTransition() {
        LayoutTransition transition = new LayoutTransition();
        // 动画开始延迟
        transition.setStartDelay(LayoutTransition.APPEARING, 0);
        transition.setStartDelay(LayoutTransition.DISAPPEARING, 0);
        transition.setStartDelay(LayoutTransition.CHANGE_APPEARING, 0);
        transition.setStartDelay(LayoutTransition.CHANGE_DISAPPEARING, 0);
        // 动画执行时间
        transition.setDuration(LayoutTransition.APPEARING, 300);
        transition.setDuration(LayoutTransition.DISAPPEARING, 300);
        transition.setDuration(LayoutTransition.CHANGE_APPEARING, 300);
        transition.setDuration(LayoutTransition.CHANGE_DISAPPEARING, 300);
        return transition;
    }

    private static Animator getSlideInAnimation(int start) {
        return ObjectAnimator.ofFloat(null, "translationX", start, 0);
    }

    private static Animator getSileOutAnimation(int end) {
        return ObjectAnimator.ofFloat(null, "translationX", 0, end);
    }


    private static Animator getFadeInAnimation() {
        ObjectAnimator objectAnimator = ObjectAnimator.ofFloat(null, "alpha", 0f, 1f);
//        objectAnimator.setInterpolator(new PathInterpolator(0.4f, 0f, 1f, 1f));
        return objectAnimator;
    }

    private static Animator getFadeOutAnimation() {
        ObjectAnimator objectAnimator = ObjectAnimator.ofFloat(null, "alpha", 1f, 0f);
//        objectAnimator.setInterpolator(new PathInterpolator(0f, 0f, 0.8f, 1f));
        return objectAnimator;
    }

    private static LayoutTransition getNoAnimationLayoutTransition() {
        LayoutTransition transition = new LayoutTransition();
        transition.setAnimator(LayoutTransition.APPEARING, null);
        transition.setAnimator(LayoutTransition.CHANGE_APPEARING, null);
        transition.setAnimator(LayoutTransition.DISAPPEARING, null);
        transition.setAnimator(LayoutTransition.CHANGE_DISAPPEARING, null);
        transition.setAnimator(LayoutTransition.CHANGING, null);
        return transition;
    }

    /**
     * 创建并返回一个LayoutTransition对象，该对象定义了缩小到指定位置的动画。
     * https://km.sankuai.com/collabpage/2119366469
     * @param targetX 目标位置的X坐标
     * @param targetY 目标位置的Y坐标
     * @param view    页面
     * @return 一个带有自定义动画的LayoutTransition对象。
     */
    private static LayoutTransition getShrinkTransition(final float targetX, final float targetY, final View view) {
        LayoutTransition transition = new LayoutTransition();

        // 获取视图宽度和高度的一半
        float halfWidth = view.getWidth() / 2f;
        float halfHeight = view.getHeight() / 2f;

        // 计算视图缩小后的位置偏移量
        float translationXEnd = targetX - halfWidth;
        float translationYEnd = targetY - halfHeight;

        // 设置PropertyValuesHolder以使用计算出的偏移量
        PropertyValuesHolder pvhX = PropertyValuesHolder.ofFloat(View.SCALE_X, 0f);
        PropertyValuesHolder pvhY = PropertyValuesHolder.ofFloat(View.SCALE_Y, 0f);
        PropertyValuesHolder pvhTranslationX = PropertyValuesHolder.ofFloat(View.TRANSLATION_X, translationXEnd);
        PropertyValuesHolder pvhTranslationY = PropertyValuesHolder.ofFloat(View.TRANSLATION_Y, translationYEnd);

        // 创建自定义的ObjectAnimator
        ObjectAnimator shrinkAnimator = ObjectAnimator.ofPropertyValuesHolder(view, pvhX, pvhY, pvhTranslationX, pvhTranslationY);
        shrinkAnimator.setDuration(transition.getDuration(LayoutTransition.DISAPPEARING));

        // 为LayoutTransition设置自定义的动画
        transition.setAnimator(LayoutTransition.DISAPPEARING, shrinkAnimator);

        // 监听动画结束，将视图的缩放和位置还原
        shrinkAnimator.addListener(new AnimatorListenerAdapter() {
            @Override
            public void onAnimationEnd(Animator animation) {
                view.setScaleX(1f);
                view.setScaleY(1f);
                view.setTranslationX(0f);
                view.setTranslationY(0f);
            }
        });

        return transition;
    }


    public static void startSlideUpPushAnimation(@NonNull View view) {
        // 创建并启动从底部滑入的动画
        TranslateAnimation slideIn = new TranslateAnimation(
                Animation.RELATIVE_TO_SELF, 0.0f,
                Animation.RELATIVE_TO_SELF, 0.0f,
                Animation.RELATIVE_TO_SELF, 1.0f,
                Animation.RELATIVE_TO_SELF, 0.0f);
        slideIn.setDuration(MSCConfig.getHalfDialogTransitionDuration());
        view.startAnimation(slideIn);
    }
}
