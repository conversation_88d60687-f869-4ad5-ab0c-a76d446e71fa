package com.meituan.msc.modules.page.transition;

import android.app.Activity;

import com.meituan.msc.common.utils.PathUtil;
import com.meituan.msc.common.utils.ScreenUtil;
import com.meituan.msc.lib.R;
import com.meituan.msc.modules.engine.MSCHornRollbackConfig;
import com.meituan.msc.modules.reporter.MSCLog;
import com.meituan.msc.modules.update.AppConfigModule;

import org.json.JSONObject;

/**
 * Created by letty on 2022/4/21.
 **/
public class PageTransitionConfig {
    private static final String TAG = "PageTransitionConfig";
    private static int[][] exitShrinkAnimations;
    public int pushStyle;
    public int popStyle;
    public boolean overrideContainerPop;
    public float pivotX;
    public float pivotY;
    /**
     * 配置形式：与transitionStyle同一层
     * "halfPageStyle": {
     *   "pageHeightPercent": 0.56,
     *   "pageHeight": 300
     * }
     */
    public float pageHeightPercent;
    public int pageHeight;

    /**
     * 配置形式：
     * transitionStyle:{
     *   pushStyle: 0, //入场动画 枚举 0 默认动画 侧滑动画;  1 淡入; default 0
     *   popStyle: 1, // 出场动画 枚举 0 默认动画 侧滑动画; 1 淡出; default 0
     *   overrideContainerPop: true // 覆盖容器退出动画，以栈底页面为准 default false 默认为侧滑
     * }
     * @param url
     * @param appConfigModule
     * @return
     */
    public static PageTransitionConfig parsePageTransition(String url, AppConfigModule appConfigModule) {
        if (appConfigModule == null || appConfigModule.mPages == null) {
            return null;
        }
        JSONObject item = appConfigModule.mPages.optJSONObject(PathUtil.getPath(url));
        if (item == null) {
            return null;
        }

        JSONObject transitionStyle = item.optJSONObject("transitionStyle");
        PageTransitionConfig pageTransitionConfig = new PageTransitionConfig();
        if (transitionStyle != null) {
            pageTransitionConfig.pushStyle = transitionStyle.optInt("pushStyle");
            pageTransitionConfig.popStyle = transitionStyle.optInt("popStyle");
            pageTransitionConfig.overrideContainerPop = transitionStyle.optBoolean("overrideContainerPop");
        }
        // 12.30.200 新增半弹层相关配置：https://km.sankuai.com/collabpage/2703597599
        JSONObject halfPageStyle = item.optJSONObject("halfPageStyle");
        if (halfPageStyle != null) {
            pageTransitionConfig.pageHeightPercent = (float) halfPageStyle.optDouble("pageHeightPercent", 0);
            pageTransitionConfig.pageHeight = halfPageStyle.optInt("pageHeight", 0);
        }
        return pageTransitionConfig;
    }

    private static int[][] getExitShrinkAnimations() {
        if (exitShrinkAnimations == null) {
            exitShrinkAnimations = new int[][]{
                    {R.anim.msc_shrink_exit_x0_y1d8, R.anim.msc_shrink_exit_x0_y3d8, R.anim.msc_shrink_exit_x0_y5d8, R.anim.msc_shrink_exit_x0_y7d8},
                    {R.anim.msc_shrink_exit_x1_y1d8, R.anim.msc_shrink_exit_x1_y3d8, R.anim.msc_shrink_exit_x1_y5d8, R.anim.msc_shrink_exit_x1_y7d8}
            };
        }
        return exitShrinkAnimations;
    }

    public static void applyContainerPopTransition(Activity activity, PageTransitionConfig config) {
        if (activity != null && config != null && config.overrideContainerPop) {
            MSCLog.i(TAG, "applyContainerPopTransition", config.popStyle);
            if (config.popStyle == TransitionStyle.FADE_IN_FADE_OUT) {
                activity.overridePendingTransition(0, R.anim.msc_fade_out);
            } else if (config.popStyle == TransitionStyle.SLIDE_UP_SLIDE_DOWN) {
                activity.overridePendingTransition(0, R.anim.msc_slide_down);
            } else if (config.popStyle == TransitionStyle.NONE) {
                activity.overridePendingTransition(0, 0);
            } else if (config.popStyle == TransitionStyle.SHRINK) {
                applyContainerPopShrink(activity, config.pivotX, config.pivotY);
            }
        } else {
            MSCLog.i(TAG, "applyContainerPopTransition failed", activity != null ? activity.toString() : null, config);
        }
    }

    private static void applyContainerPopShrink(Activity activity, float pivotX, float pivotY) {
        int[] widthHeight = ScreenUtil.getScreenWidthAndHeight(activity, null);
        int width = widthHeight[0];
        int height = widthHeight[1];
        int partX = Math.min(((int) (pivotX * 2 / width)), 1);
        int partY = Math.min((int) (pivotY * 4 / height), 3);
        activity.overridePendingTransition(0, getExitShrinkAnimations()[partX][partY]);
    }

    public static class TransitionStyle {
        public static final int NONE = -1;
        public static final int DEFAULT = 0;
        public static final int FADE_IN_FADE_OUT = 1;
        public static final int SLIDE_UP_SLIDE_DOWN = 2;
        public static final int SHRINK = 3;
    }

    @Override
    public String toString() {
        return "PageTransitionConfig{" +
                "pushStyle=" + pushStyle +
                ", popStyle=" + popStyle +
                ", overrideContainerPop=" + overrideContainerPop +
                ", pivotX=" + pivotX +
                ", pivotY=" + pivotY +
                ", pageHeightPercent=" + pageHeightPercent +
                ", pageHeight=" + pageHeight +
                '}';
    }
}
