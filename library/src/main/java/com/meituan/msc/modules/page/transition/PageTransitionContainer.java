package com.meituan.msc.modules.page.transition;

import android.content.Context;
import android.support.annotation.NonNull;
import android.view.View;
import android.view.ViewGroup;
import android.widget.FrameLayout;

/**
 * Created by letty on 2022/1/17.
 **/
public class PageTransitionContainer extends FrameLayout {
    private OnAttachListener mAttachListener;
    private boolean enableLayoutTransition = true;

    public PageTransitionContainer(@NonNull Context context) {
        super(context);
    }

    public PageTransitionContainer setAttachListener(OnAttachListener attachListener) {
        this.mAttachListener = attachListener;
        return this;
    }

    @Override
    public void addView(View child, int index, ViewGroup.LayoutParams params) {
        if (enableLayoutTransition && child instanceof ITransitionPage) {
            setLayoutTransition(((ITransitionPage) child).getPushTransition());
        }
        super.addView(child, index, params);
    }

    @Override
    public void removeViewAt(int index) {
        View child = getChildAt(index);
        if (enableLayoutTransition && child instanceof ITransitionPage) {
            setLayoutTransition(((ITransitionPage) child).getPopTransition());
        }
        super.removeViewAt(index);
    }

    public void changeAnimation(boolean enable) {
        if (enable) {
            enableAnimation();
        } else {
            disableAnimation();
        }
    }

    public void enableAnimation() {
        enableLayoutTransition = true;
    }

    public void disableAnimation() {
        enableLayoutTransition = false;
        setLayoutTransition(null);
    }

    protected void onAttachedToWindow() {
        super.onAttachedToWindow();
        if (this.mAttachListener != null) {
            this.mAttachListener.onAttachedFromWindow();
        }
    }

    protected void onDetachedFromWindow() {
        super.onDetachedFromWindow();
        if (this.mAttachListener != null) {
            this.mAttachListener.onDetachedFromWindow();
        }
    }

    public interface OnAttachListener {
        void onDetachedFromWindow();

        void onAttachedFromWindow();
    }

}
