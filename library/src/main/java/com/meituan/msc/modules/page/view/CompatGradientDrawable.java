package com.meituan.msc.modules.page.view;

import android.graphics.Rect;
import android.graphics.drawable.GradientDrawable;
import android.os.Build;

/**
 * 对 setPadding 做版本兼容处理
 *
 * @see GradientDrawable#setPadding
 */
public class CompatGradientDrawable extends GradientDrawable {

    private final Rect paddingCompat;

    public CompatGradientDrawable() {
        paddingCompat = new Rect();
    }

    @Override
    public boolean getPadding(Rect padding) {
        if (Build.VERSION.SDK_INT >= Build.VERSION_CODES.Q) {
            return super.getPadding(padding);
        } else {
            if (paddingCompat != null) {
                padding.set(paddingCompat);
                return true;
            } else {
                padding.set(0, 0, 0, 0);
                return false;
            }
        }
    }

    @Override
    public void setPadding(int left, int top, int right, int bottom) {
        if (Build.VERSION.SDK_INT >= Build.VERSION_CODES.Q) {
            super.setPadding(left, top, right, bottom);
        } else {
            paddingCompat.set(left, top, right, bottom);
            invalidateSelf();
        }
    }
}
