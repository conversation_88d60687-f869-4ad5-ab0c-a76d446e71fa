package com.meituan.msc.modules.page.view;

import android.content.Context;
import android.os.Build;
import android.support.annotation.NonNull;
import android.support.annotation.Nullable;
import android.support.annotation.RequiresApi;
import android.util.AttributeSet;
import android.view.View;

import com.meituan.msc.modules.api.msi.components.coverview.MSCScrollView;
import com.meituan.msc.modules.api.msi.components.coverview.ICoverViewUpdateRegistry;
import com.meituan.msc.modules.page.view.coverview.IPageLifecycleInterceptor;
import com.meituan.msi.view.MsiCoverViewWrapper;

/**
 * borderWidth	边框宽度	Number
 * borderColor	边框颜色	String
 * borderRadius	边框圆角	Number
 * bgColor	背景色
 * Created by bunnyblue on 4/19/18.
 */

public class CoverViewWrapper extends MSCCoverViewWrapper {

    public CoverViewWrapper(@NonNull Context context) {
        super(context);
    }

    public CoverViewWrapper(@NonNull Context context, @Nullable AttributeSet attrs) {
        super(context, attrs);
    }

    public CoverViewWrapper(@NonNull Context context, @Nullable AttributeSet attrs, int defStyleAttr) {
        super(context, attrs, defStyleAttr);
    }

    @RequiresApi(api = Build.VERSION_CODES.LOLLIPOP)
    public CoverViewWrapper(@NonNull Context context, @Nullable AttributeSet attrs, int defStyleAttr, int defStyleRes) {
        super(context, attrs, defStyleAttr, defStyleRes);
    }


    public CoverViewWrapper(Context context, View view) {
        super(context, view);
    }

    @Override
    public void addView(View child, int index) {
        if (view instanceof MSCScrollView){
            ((MSCScrollView) view).addView(child,index);
            return;
        }
        super.addView(child, index);
    }

    @Nullable
    public final IPageLifecycleInterceptor getPageBackInterceptor() {
        if (view instanceof IPageLifecycleInterceptor) {
            return (IPageLifecycleInterceptor) view;
        }
        return null;
    }

    /**
     * 获取实现ICoverViewUpdateRegistry的视图
     * @param coverViewUpdateRegistryClass
     * @return
     */
    public ICoverViewUpdateRegistry getViewInInfoWindowWrapper(Class<ICoverViewUpdateRegistry> coverViewUpdateRegistryClass) {
        try {
            if (coverViewUpdateRegistryClass.isAssignableFrom(this.view.getClass())) {
                return (ICoverViewUpdateRegistry) this.view;
            }
        } catch (Exception e) {
        }
        return null;
    }
}
