package com.meituan.msc.modules.page.view;

import android.content.Context;
import android.graphics.Rect;
import android.support.annotation.Keep;
import android.view.LayoutInflater;
import android.view.View;
import android.widget.RelativeLayout;

import com.meituan.msc.common.utils.DisplayUtil;
import com.meituan.msc.lib.R;

/**
 * 自定义导航栏
 */
@Keep
public abstract class CustomNavigationBar extends RelativeLayout {
    private OnNavigationBarButtonClickListener navigationBarButtonClickListener;
    protected View menuView;
    protected volatile Rect menuRect;

    public CustomNavigationBar(Context context) {
        super(context);
    }

    public interface OnNavigationBarButtonClickListener {
        void clickShare();
        void clickBack();
        void clickTitleBar();
    }

    public final void setNavigationBarButtonClickListener(OnNavigationBarButtonClickListener listener) {
        navigationBarButtonClickListener = listener;
    }

    protected final void ensureNeedClickTitleBar() {
        setOnClickListener(new OnClickListener() {

            @Override
            public void onClick(View v) {
                if (navigationBarButtonClickListener != null) {
                    navigationBarButtonClickListener.clickTitleBar();
                }
            }
        });
    }
    public abstract void disableNavigationBack();

    public abstract void setNavigationBarTitle(CharSequence title);

    public abstract void setNavigationBarTextColor(int colorInt);

    public abstract void setNavigationBarIconColor(int colorInt);

    public abstract void showNavigationBarMoreMenu(boolean callFromBusiness);

    public abstract void hideNavigationBarMoreMenu(boolean callFromBusiness);

    public abstract void showNavigationBarLoading();

    public abstract void hideNavigationBarLoading();

    public abstract boolean isMenuButtonShown();

    public static int getFixedHeight() {
        return DisplayUtil.fromDPToPix(45);
    }

    protected final View attachMenuView() {
        menuView = LayoutInflater.from(getContext()).inflate(R.layout.msc_menu, this, false);
        LayoutParams params = new LayoutParams(LayoutParams.WRAP_CONTENT, LayoutParams.WRAP_CONTENT);
        params.rightMargin = DisplayUtil.fromDPToPix(15);
        params.addRule(RelativeLayout.CENTER_VERTICAL, RelativeLayout.TRUE);
        params.addRule(RelativeLayout.ALIGN_PARENT_RIGHT, RelativeLayout.TRUE);
        addView(menuView, params);
        return menuView;
    }

    @Override
    protected void onLayout(boolean changed, int l, int t, int r, int b) {
        super.onLayout(changed, l, t, r, b);
        menuRect = null;
    }

    public Rect getMenuRect() {
        // menuRect可多线程操作，此处用本地变量保证返回一定不为null，小概率会发生重复计算
        Rect result = menuRect;
        if (result == null) {
            result = new Rect();
            menuView.measure(MeasureSpec.UNSPECIFIED, MeasureSpec.UNSPECIFIED);
            int width = menuView.getMeasuredWidth();
            int height = menuView.getMeasuredHeight();
            //fix 高度计算失败 https://ones.sankuai.com/ones/product/6246/workItem/defect/detail/5023690
            int screenWidth = DisplayUtil.getScreenWidth(getContext());

            LayoutParams params = (LayoutParams) menuView.getLayoutParams();
            result.right = screenWidth - params.rightMargin;
            result.left = result.right - width - params.leftMargin;
            int space = (getFixedHeight() - height) / 2;
            result.top = DisplayUtil.getStatusBarHeight() + space;
            result.bottom = result.top + height;

            menuRect = result;
        }
        return result;
    }

    public final void onUserClickShareIcon() {
        if (navigationBarButtonClickListener != null) {
            navigationBarButtonClickListener.clickShare();
        }
    }

    public final void onUserClickBackIcon() {
        if (navigationBarButtonClickListener != null) {
            navigationBarButtonClickListener.clickBack();
        }
    }
}