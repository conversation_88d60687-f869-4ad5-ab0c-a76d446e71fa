package com.meituan.msc.modules.page.view;

import android.content.Context;
import android.graphics.Canvas;
import android.graphics.Paint;
import android.graphics.Paint.Style;
import android.graphics.Path;
import android.graphics.Path.Direction;
import android.graphics.RectF;
import android.graphics.drawable.Drawable;
import android.support.annotation.NonNull;
import android.support.annotation.Nullable;
import android.support.annotation.RequiresApi;
import android.text.TextUtils;
import android.util.AttributeSet;
import android.view.MotionEvent;
import android.view.View;
import android.view.ViewGroup;
import android.widget.FrameLayout;

import com.google.gson.Gson;
import com.google.gson.JsonObject;
import com.meituan.msc.common.utils.DisplayUtil;
import com.meituan.msc.modules.api.msi.components.coverview.params.MSCStyleParams;
import com.meituan.msi.util.ColorUtil;

public class MSCCoverViewWrapper extends FrameLayout {
    protected View view;
    protected float borderWidth;
    protected float borderRadius;
    protected int borderColor;
    protected int bgColor;
    protected boolean fixed = false;
    protected Paint paint = new Paint();

    public MSCCoverViewWrapper(@NonNull Context context) {
        super(context);
        this.init();
    }

    public MSCCoverViewWrapper(@NonNull Context context, @Nullable AttributeSet attrs) {
        super(context, attrs);
        this.init();
    }

    public MSCCoverViewWrapper(@NonNull Context context, @Nullable AttributeSet attrs, int defStyleAttr) {
        super(context, attrs, defStyleAttr);
    }

    @RequiresApi(
            api = 21
    )
    public MSCCoverViewWrapper(@NonNull Context context, @Nullable AttributeSet attrs, int defStyleAttr, int defStyleRes) {
        super(context, attrs, defStyleAttr, defStyleRes);
    }

    public MSCCoverViewWrapper(Context context, View view) {
        super(context);
        this.view = view;
        this.addView(view, new LayoutParams(-1, -1));
        this.init();
    }

    public void unloadContent() {
        this.removeAllViews();
        this.view = null;
        ((ViewGroup) this.getParent()).removeView(this);
    }

    private void init() {
        this.paint.setStyle(Style.STROKE);
        this.paint.setAntiAlias(true);
        this.setWillNotDraw(false);
    }

    public View getContent() {
        return this.view;
    }

    public void setWillNotDraw(boolean willNotDraw) {
        super.setWillNotDraw(willNotDraw);
    }

    public void draw(Canvas canvas) {
        float f = 0.0F;
        boolean hashBorderRadius = this.borderRadius > 0.0F;
        if (hashBorderRadius) {
            canvas.save();
            Path path = new Path();
            path.addRoundRect(new RectF(0.0F, 0.0F, (float) this.getWidth(), (float) this.getHeight()), this.borderRadius, this.borderRadius, Direction.CW);
            canvas.clipPath(path);
        }

        if (this.bgColor != 0) {
            canvas.drawColor(this.bgColor);
        }

        boolean needResotre;
        if (this.borderWidth > 0.0F) {
            float f2 = this.borderWidth / 2.0F;
            canvas.drawRoundRect(new RectF(f2, f2, (float) this.getWidth() - f2, (float) this.getHeight() - f2), this.borderRadius, this.borderRadius, this.paint);
            if (hashBorderRadius) {
                canvas.restore();
            }

            canvas.save();
            Path path = new Path();
            if (this.borderRadius > 0.0F && this.borderRadius - this.borderWidth > 0.0F) {
                f = this.borderRadius - this.borderWidth;
            }

            path.addRoundRect(new RectF(this.borderWidth, this.borderWidth, (float) this.getWidth() - this.borderWidth, (float) this.getHeight() - this.borderWidth), f, f, Direction.CW);
            canvas.clipPath(path);
            needResotre = true;
        } else {
            needResotre = hashBorderRadius;
        }

        super.draw(canvas);
        if (needResotre) {
            canvas.restore();
        }

    }

    public final <T extends View> T getViewInWrapper(Class<T> cls) {
        try {
            if (cls.isAssignableFrom(this.view.getClass())) {
                return (T) this.view;
            }
        } catch (Exception var3) {
        }

        return null;
    }

    public final void setBorderRadius(float borderRadius) {
        this.borderRadius = borderRadius;
    }

    public final void setBorderColor(int borderColor) {
        this.borderColor = borderColor;
        this.paint.setColor(borderColor);
    }

    public final void setBorderWidth(float borderWidth) {
        this.borderWidth = borderWidth;
        this.paint.setStrokeWidth(borderWidth);
    }

    public final void setBgColor(int bgColor) {
        this.bgColor = bgColor;
    }

    public boolean dispatchTouchEvent(MotionEvent motionEvent) {
        if (motionEvent.getActionMasked() == 0) {
            boolean shouldDispatch = true;
            float x = motionEvent.getX();
            float y = motionEvent.getY();
            if (this.borderRadius > 0.0F) {
                double pow = Math.pow((double) this.borderRadius, 2.0D);
                float width = (float) this.getWidth();
                float height = (float) this.getHeight();
                if (x < this.borderRadius) {
                    if (y < this.borderRadius) {
                        if (Math.pow((double) (this.borderRadius - y), 2.0D) + Math.pow((double) (this.borderRadius - x), 2.0D) > pow) {
                            shouldDispatch = false;
                        }
                    } else if (y > height - this.borderRadius && Math.pow((double) (this.borderRadius + y - height), 2.0D) + Math.pow((double) (this.borderRadius - x), 2.0D) > pow) {
                        shouldDispatch = false;
                    }
                } else if (x > width - this.borderRadius) {
                    if (y < this.borderRadius) {
                        if (Math.pow((double) (this.borderRadius - y), 2.0D) + Math.pow((double) (x + this.borderRadius - width), 2.0D) > pow) {
                            shouldDispatch = false;
                        }
                    } else if (y > height - this.borderRadius && Math.pow((double) (this.borderRadius + y - height), 2.0D) + Math.pow((double) (x + this.borderRadius - width), 2.0D) > pow) {
                        shouldDispatch = false;
                    }
                }
            }

            if (!shouldDispatch) {
                return true;
            }
        }

        return super.dispatchTouchEvent(motionEvent);
    }

    private static final Gson GSON = new Gson();

    public void updateViewStyle(JsonObject params) {
        if (params == null) {
            return;
        }
        MSCStyleParams style = null;
        if (params.has("style")) {
            try {
                style = GSON.fromJson(params.get("style"), MSCStyleParams.class);
            } catch (Exception e) {
                //ignore
            }
        }
        if (style == null) {
            return;
        }
        String bgColor = style.bgColor;
        if (!TextUtils.isEmpty(bgColor)) {
            this.setBackgroundColor(ColorUtil.parsRGBAColor(bgColor));
        }

        String borderColor = style.borderColor;
        if (!TextUtils.isEmpty(borderColor)) {
            this.setBorderColor(ColorUtil.parsRGBAColor(borderColor));
        }

        if (style.borderRadius != null) {
            this.setBorderRadius((float) DisplayUtil.roundWithDevice(style.borderRadius.floatValue()));
        }

        if (style.borderWidth != null) {
            this.setBorderWidth((float) DisplayUtil.roundWithDevice(style.borderWidth.floatValue()));
        }

        boolean invalidate = false;
        if (style.opacity != null) {
            Drawable drawable = this.getBackground();
            if (drawable != null) {
                invalidate = true;
            }

            this.setAlpha(style.opacity.floatValue());
        }

        if (style.padding != null && style.padding.length == 4) {
            this.view.setPadding((int) DisplayUtil.computeValue(style.padding[0]),
                    (int) DisplayUtil.computeValue(style.padding[1]),
                    (int) DisplayUtil.computeValue(style.padding[2]),
                    (int) DisplayUtil.computeValue(style.padding[3]));
        }

        if (style.rotate != null) {
            this.setRotation(style.rotate.floatValue());
            invalidate = true;
        }

//        (float) params.optDouble("scaleX", 1.0D)
        if (style.scaleX != null) {
            this.setScaleX(style.scaleX.floatValue());
            invalidate = true;
        }

        if (style.scaleY != null) {
            this.setScaleY(style.scaleY.floatValue());
            invalidate = true;
        }

        if (invalidate) {
            this.invalidate();
        }
    }


    public boolean isFixed() {
        return this.fixed;
    }

    public void setFixed(boolean fixed) {
        this.fixed = fixed;
    }
}
