package com.meituan.msc.modules.page.view;

import android.annotation.SuppressLint;
import android.content.Context;
import android.content.res.ColorStateList;
import android.graphics.Color;
import android.graphics.Rect;
import android.graphics.Typeface;
import android.graphics.drawable.Drawable;
import android.support.v4.graphics.drawable.DrawableCompat;
import android.view.Gravity;
import android.view.LayoutInflater;
import android.view.View;
import android.widget.ImageView;
import android.widget.LinearLayout;
import android.widget.ProgressBar;
import android.widget.RelativeLayout;
import android.widget.TextView;

import com.meituan.msc.common.utils.CompatibilityUtil;
import com.meituan.msc.common.utils.DisplayUtil;
import com.meituan.msc.common.utils.RomUtil;
import com.meituan.msc.extern.MSCEnvHelper;
import com.meituan.msc.lib.R;
import com.meituan.msc.modules.engine.MSCHornRollbackConfig;
import com.meituan.msc.modules.engine.MSCRuntime;
import com.meituan.msc.modules.reporter.MSCLog;

/**
 * 自定义导航栏
 */
public class OpenPlatformNavigationBar extends CustomNavigationBar implements View.OnClickListener {
    // 导航栏视图组件

    // 右上角菜单组件(分享，MSC暂不支持胶囊)
    private View mMenu;

    // 分享按钮组件 由前端控制按钮显隐接口控制
    private TextView mShare;

    // 导航栏标题组件 标题+进度条
    private LinearLayout mTitleContainer;
    private TextView mTitleText;
    private LayoutParams titleParams;

    // 返回组件
    private ImageView mBackImage;

    // 是否为栈底页面
    private boolean isFirstPage;
    // 是否为自定义导航栏
    private boolean isCustomBar;
    // 分享按钮是否显示
    private boolean isShareShow;
    // loading
    private ProgressBar mProgress;

    private Integer mNavigationBarIconColor;

    public OpenPlatformNavigationBar(Context context, boolean isFirstPage, MSCRuntime runtime, boolean isCustomBar, String url) {
        super(context);
        init(context, isFirstPage, isCustomBar, url);
    }

    @Override
    public void setNavigationBarTitle(CharSequence title) {
        if (null != mTitleText) {
            mTitleText.setText(title);
        }
    }

    @Override
    public void setNavigationBarTextColor(int colorInt) {
        if (null != mTitleText) {
            mTitleText.setTextColor(colorInt);
        }
    }

    @Override
    public void disableNavigationBack() {
        mBackImage.setVisibility(View.GONE);
    }

    @Override
    @SuppressLint("ParseColorDetector")
    public void setNavigationBarIconColor(int colorInt) {

        /**
         * 当该方法被调用的时候，分享按钮有可能因为懒加载的原因，还没有初始化。
         * 因此把colorInt保存一下，当下次控件加载后，再设置分享按钮的颜色。
         */
        this.mNavigationBarIconColor = colorInt;
        if (mBackImage != null && mBackImage.getVisibility() == VISIBLE) {
            mBackImage.setImageDrawable(tintDrawable(mBackImage.getDrawable(), ColorStateList.valueOf(colorInt)));
            CompatibilityUtil.requestInvalidateImageView(mBackImage);
        }
        setShareButtonStyle(colorInt);
    }

    @Override
    public void showNavigationBarMoreMenu(boolean callFromBusiness) {
        // 业务调用，显示分享按钮
        if (callFromBusiness) {
            showShareButton();
        }
    }

    @Override
    public void showNavigationBarLoading() {
        if (mProgress == null) {
            mProgress = new ProgressBar(getContext());
            mProgress.setIndeterminateDrawable(getContext().getDrawable(R.drawable.msc_anim_navigation_loading));
            int size = getContext().getResources().getDimensionPixelSize(R.dimen.msc_navigation_bar_loading_size);
            LinearLayout.LayoutParams layoutParams = new LinearLayout.LayoutParams(size, size);
            layoutParams.gravity = Gravity.CENTER_VERTICAL;
            layoutParams.setMarginStart(-size);
            mTitleContainer.addView(mProgress, layoutParams);
        }
        if (mProgress != null) {
            mProgress.setVisibility(VISIBLE);
        }
    }

    @Override
    public void hideNavigationBarLoading() {
        if (mProgress != null) {
            mProgress.setVisibility(GONE);
        }
    }

    private void showShareButton() {
        isShareShow = true;
        if (this.mMenu == null) {
            this.mMenu = attachMenuView();

            this.mShare = findViewById(R.id.mmp_share);
            this.mShare.setOnClickListener(this);
            // 内部小程序，默认开启融合模式，不显示胶囊按钮
            // 外部小程序，通过isCapsuleShow控制显示
        }

        if (this.mShare == null || this.mShare.getVisibility() == VISIBLE) {//如果已经处理可见状态不重复操作
            return;
        }

        mShare.setVisibility(VISIBLE);
        setShareButtonStyle(mNavigationBarIconColor);
        menuRect = null;    // 显示/隐藏变化时，清空保存的menuRect，下次获取重新计算
        //share显示时埋点
        //分享埋点 https://ones.sankuai.com/ones/product/6246/workItem/task/detail/4252429
        // MSCEnvHelper.getLogger().mgeView(mAppConfig.getAppId(), MeiTuanConstants.MMP_PAGE_CID, "b_group_r1dy9rr4_mv", null);
    }

    @Override
    public void hideNavigationBarMoreMenu(boolean callFromBusiness) {
        // 业务调用，隐藏分享按钮
        if (callFromBusiness) {
            hideShareButton();
        } else {
            // 框架调用，ignore
        }
    }

    @Override
    public boolean isMenuButtonShown() {
        return mShare != null && mShare.getVisibility() == View.VISIBLE;
    }

    private void hideShareButton() {
        isShareShow = false;
        if (this.mShare == null) {
            return;
        }

        this.mShare.setVisibility(GONE);
        menuRect = null;
    }

    private void init(final Context context, boolean isFirstPage, boolean isCustomBar, String url) {
        LayoutInflater.from(context).inflate(R.layout.msc_open_platform_toolbar, this);
        this.mBackImage = findViewById(R.id.img_back);
        this.mBackImage.setContentDescription(getContext().getString(R.string.msc_back));
        this.mTitleContainer = findViewById(R.id.msc_title_container);
        this.mTitleText = findViewById(R.id.title);

        if (!MSCHornRollbackConfig.isRollbackMSCNavigationBarTitleFix()) {
            this.mBackImage.post(new Runnable() {
                @Override
                public void run() {
                    RelativeLayout.LayoutParams mTitleContainerParams = (RelativeLayout.LayoutParams) mTitleContainer.getLayoutParams();
                    LinearLayout.LayoutParams mTitleTextParams = (LinearLayout.LayoutParams) mTitleText.getLayoutParams();
                    mTitleContainerParams.removeRule(RelativeLayout.RIGHT_OF);
                    mTitleContainerParams.addRule(RelativeLayout.CENTER_IN_PARENT, RelativeLayout.TRUE);
                    mTitleTextParams.setMargins(mBackImage.getWidth(), 0, mBackImage.getWidth(), 0);
                    mTitleContainer.setLayoutParams(mTitleContainerParams);
                    mTitleText.setLayoutParams(mTitleTextParams);
                }
            });
        }

        this.titleParams = (LayoutParams) mTitleContainer.getLayoutParams();

        this.isFirstPage = isFirstPage;
        this.isCustomBar = isCustomBar;
        // 内部小程序，默认开启融合模式，不显示胶囊按钮
        // 外部小程序，通过isCapsuleShow控制显示

        backImgLogic();
        titleBarLogic();
        customBarLogic(isCustomBar);
    }

    // 返回图片逻辑
    private void backImgLogic() {
        // 隐藏返回按钮 https://km.sankuai.com/page/532044301
        if (this.isCustomBar) {
            //隐藏返回按钮。自定义导航栏
            hideBackLogic();
        } else if (this.isFirstPage && MSCEnvHelper.needHideFirstPageNavigationBarBackImage()) {
            // 非自定义导航栏、首页、宿主隐藏返回按钮
            hideBackLogic();
            return;
        } else { // 处理其余情况
            this.mBackImage.setOnClickListener(this);
        }
        this.mTitleContainer.setLayoutParams(this.titleParams);
    }

    private void hideBackLogic() {
        this.mBackImage.setVisibility(GONE);
        //非首页导航栏无标题，标题不设置右移
        if (this.isFirstPage) {
            this.mTitleText.setPadding(DisplayUtil.fromDPToPix(15), 0, 0, 0);
        }
        this.titleParams.addRule(RelativeLayout.ALIGN_PARENT_LEFT);
    }

    // 看起来是对于 默认导航栏 需要添加点击标题栏功能
    private void customBarLogic(boolean isCustomBar) {
        if (!isCustomBar) {
            ensureNeedClickTitleBar();
        }
    }

    //如果华为机型，标题不加粗
    private void titleBarLogic() {
        if (RomUtil.isEmui()) {
            mTitleText.setTypeface(Typeface.defaultFromStyle(Typeface.NORMAL));
        } else {
            mTitleText.setTypeface(Typeface.defaultFromStyle(Typeface.BOLD));
        }
    }

    private static Drawable tintDrawable(Drawable drawable, ColorStateList colors) {
        if (drawable == null) {
            MSCLog.w("drawable can't be nil!");
            return null;
        }
        final Drawable wrappedDrawable = DrawableCompat.wrap(drawable).mutate();
        DrawableCompat.setTintList(wrappedDrawable, colors);
        return wrappedDrawable;
    }

    @Override
    public void onClick(View v) {
        int id = v.getId();
        if (id == R.id.img_back) {
            onUserClickBackIcon();
        } else if (id == R.id.mmp_share) {
            //分享
            onUserClickShareIcon();
        }
    }

    // top bottom 全部不变
    // 重点看下left right
    @Override
    public Rect getMenuRect() {
        // menuRect可多线程操作，此处用本地变量保证返回一定不为null，小概率会发生重复计算
        Rect result = menuRect;
        if (result == null) {
            result = new Rect();
            menuView.measure(MeasureSpec.UNSPECIFIED, MeasureSpec.UNSPECIFIED);
            int rightMargin = 0;
            if (this.mShare != null) {
                rightMargin = ((LinearLayout.LayoutParams) this.mShare.getLayoutParams()).rightMargin;
            }
            int width = menuView.getMeasuredWidth() - (isShareShow ? rightMargin : 0);
            int height = menuView.getMeasuredHeight();
            //fix 高度计算失败 https://ones.sankuai.com/ones/product/6246/workItem/defect/detail/5023690
            int screenWidth = DisplayUtil.getScreenWidth(getContext());

            LayoutParams params = (LayoutParams) menuView.getLayoutParams();
            // 这里需要处理下当胶囊按钮隐藏 分享按钮显示时的margin
            result.right = screenWidth - params.rightMargin - (isShareShow ? rightMargin : 0);
            result.left = result.right - width;

            if (height == 0) { // 当不显示时 返回固定值。
                // 固定高度是45 一般height30 所以差值15 （均按dp计算）
                int space = DisplayUtil.fromDPToPix(15) / 2;
                result.top = DisplayUtil.getStatusBarHeight() + space;
                result.bottom = result.top + DisplayUtil.fromDPToPix(30);
            } else {
                int space = (getFixedHeight() - height) / 2;
                result.top = DisplayUtil.getStatusBarHeight() + space;
                result.bottom = result.top + height;
            }
            menuRect = result;
        }
        return result;
    }

    private void setShareButtonStyle(Integer colorInt) {
        if (mShare == null || mShare.getVisibility() != VISIBLE || colorInt == null) {
            return;
        }
        if (colorInt != Color.WHITE) {
            mShare.setBackground(getResources().getDrawable(R.drawable.mmp_toolbar_share_white));
        } else {
            mShare.setBackground(getResources().getDrawable(R.drawable.mmp_toolbar_share_dark));
        }
        mShare.setTextColor(colorInt);
    }
}
