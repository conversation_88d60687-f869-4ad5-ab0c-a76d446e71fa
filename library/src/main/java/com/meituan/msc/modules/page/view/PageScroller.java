package com.meituan.msc.modules.page.view;

import android.animation.Animator;
import android.animation.AnimatorListenerAdapter;
import android.animation.ValueAnimator;
import android.view.ViewPropertyAnimator;
import android.widget.Scroller;

import com.meituan.msc.modules.api.msi.MSCErrorCode;
import com.meituan.msc.modules.reporter.MSCLog;
import com.meituan.msi.api.MSIError;
import com.meituan.msi.bean.MsiContext;

public class PageScroller {
    public static final String TAG = "PageScroller";
    private Scroller scroller;
    private ViewPropertyAnimator animator;
    private int lastScrollY;
    MsiContext runningCallback;

    public void startScroll(PageViewWrapper pageViewWrapper, int targetY, int duration, MsiContext callback){
        //之前可能有正在执行滚动的Scroller，原地停止，开始向新目标滚动
        cancel();
        int startY = getWebScrollY(pageViewWrapper);
        lastScrollY = startY;
        runningCallback = callback;
        scroller = new Scroller(callback.getActivity());
        scroller.startScroll(0, startY, 0, targetY - startY, duration);

        animator = pageViewWrapper.animate();
        animator.setDuration(duration)
                .setUpdateListener(new ValueAnimator.AnimatorUpdateListener() {
                    @Override
                    public void onAnimationUpdate(ValueAnimator animation) {
                        int actualY = getWebScrollY(pageViewWrapper);
                        if (!pageViewWrapper.isAttachedToWindow()) {
                            cancel();
                            return;
                        }
                        if (Math.abs(lastScrollY - actualY) > 1) {
                            // lastScrollY != actualY 说明在本类操纵之外还发生了滚动，可判断为用户操作，此时若不停止将与用户争抢控制，位置上下跳动
                            // 体验更好的处理是在用户触摸（而非滑动）时就停止，但不好监听，放弃
                            // fix：允许有1的差值，在vivo某些机型上发现无操作也会出现1像素变动，下面判断后会打log
                            MSCLog.w(TAG, "lastScrollY ", lastScrollY, " != actualY ", actualY, ", seems user scrolling, cancel auto scroll");
                            cancel();
                            return;
                        }
                        if (lastScrollY != actualY) {
                            MSCLog.w(TAG, "lastScrollY ", lastScrollY, " != actualY ", actualY, ", ignored");
                        }
                        scroller.computeScrollOffset(); // scroller的用途为计算当前时间点应当设置的Y值
                        scrollContentY(pageViewWrapper,scroller.getCurrY() - actualY);
                        lastScrollY = scroller.getCurrY();
                        MSCLog.v(TAG, "currY: ", scroller.getCurrY());
                    }
                }).setListener(new AnimatorListenerAdapter() {
            @Override
            public void onAnimationEnd(Animator animation) {
                if (runningCallback != null) {
                    runningCallback.onSuccess(null);
                    runningCallback = null;
                }
                scroller = null;
                animator = null;
            }
        }).start();
    }


    public void cancel() {
        if (runningCallback != null) {
            runningCallback.onError("cancel", MSIError.getIgnoreError(MSCErrorCode.ERROR_CODE_API_COMMON_MEET_EXPECTATIONS));
            runningCallback = null;
        }
        if (scroller != null) {
            scroller.forceFinished(true);
            scroller = null;
        }
        if (animator != null) {
            animator.cancel();
            animator = null;
        }
    }

    public int getWebScrollY(PageViewWrapper pageViewWrapper) {
        if (pageViewWrapper == null
                || pageViewWrapper.getRenderer() == null
                || pageViewWrapper.getRenderer().getRendererView() == null) {
            return 0;
        }
        return pageViewWrapper.getRenderer().getRendererView().getContentScrollY();
    }


    public void scrollContentY(PageViewWrapper pageViewWrapper, int offset) {
        if (pageViewWrapper == null
                || pageViewWrapper.getRenderer() == null
                || pageViewWrapper.getRenderer().getRendererView() == null) {
            return ;
        }
        pageViewWrapper.getRenderer().getRendererView().scrollContentY(offset);
    }
}
