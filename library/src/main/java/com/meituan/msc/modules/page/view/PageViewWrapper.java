package com.meituan.msc.modules.page.view;

import android.app.Activity;
import android.content.Context;
import android.graphics.Color;
import android.graphics.Rect;
import android.graphics.drawable.ColorDrawable;
import android.support.annotation.NonNull;
import android.support.annotation.Nullable;
import android.support.annotation.Size;
import android.text.TextUtils;
import android.view.GestureDetector;
import android.view.MotionEvent;
import android.view.View;
import android.view.ViewGroup;
import android.view.ViewParent;
import android.view.Window;
import android.widget.FrameLayout;
import android.widget.LinearLayout;
import android.widget.RelativeLayout;
import android.widget.TextView;

import com.meituan.android.common.weaver.interfaces.ffp.FFPTags;
import com.meituan.msc.common.config.MSCConfig;
import com.meituan.msc.common.executor.MSCExecutors;
import com.meituan.msc.common.framework.MPListenerManager;
import com.meituan.msc.common.utils.ColorUtil;
import com.meituan.msc.common.utils.DisplayUtil;
import com.meituan.msc.common.utils.InputMethodUtil;
import com.meituan.msc.common.utils.MSCTraceUtil;
import com.meituan.msc.common.utils.ScreenUtil;
import com.meituan.msc.common.utils.StatusBarUtils;
import com.meituan.msc.common.utils.SystemInfoUtils;
import com.meituan.msc.common.utils.UIUtil;
import com.meituan.msc.lib.R;
import com.meituan.msc.modules.container.OpenParams;
import com.meituan.msc.modules.engine.MSCHornRollbackConfig;
import com.meituan.msc.modules.engine.MSCRuntime;
import com.meituan.msc.modules.engine.RuntimeManager;
import com.meituan.msc.modules.page.Page;
import com.meituan.msc.modules.page.render.BaseRenderer;
import com.meituan.msc.modules.page.render.webview.IWebView;
import com.meituan.msc.modules.page.render.webview.MSCWebView;
import com.meituan.msc.modules.page.render.webview.MSCWebViewRenderer;
import com.meituan.msc.modules.page.render.webview.OnReloadListener;
import com.meituan.msc.modules.page.render.webview.OnWebViewFullScreenListener;
import com.meituan.msc.modules.page.render.webview.WebChromeClientCustomViewCallback;
import com.meituan.msc.modules.page.render.webview.WebViewCacheManager;
import com.meituan.msc.modules.page.view.coverview.InputTouchUtil;
import com.meituan.msc.modules.page.view.reload.ReloadProcessor;
import com.meituan.msc.modules.page.view.reload.ReloadProcessorFactory;
import com.meituan.msc.modules.page.widget.MultiLayerPage;
import com.meituan.msc.modules.page.widget.SwipeRefreshLayout;
import com.meituan.msc.modules.reporter.MSCLog;
import com.meituan.msc.util.perf.PerfTrace;
import com.meituan.msi.api.component.input.MSIBaseInput;
import com.meituan.msi.bean.MsiContext;
import com.meituan.msi.page.IKeyBoardHeightChangeObserver;
import com.meituan.msi.page.IPage;
import com.meituan.msi.view.ToastView;
import com.meituan.mtwebkit.MTWebView;
import com.meituan.mtwebkit.internal.system.MTSystemWebView;

import java.lang.ref.WeakReference;
import java.util.ArrayList;
import java.util.Collections;
import java.util.HashMap;
import java.util.LinkedList;
import java.util.List;
import java.util.Map;


/**
 * Page中的一个tab页，包含单个AppPage/WebView，负责处理其对应的View层面事务，如navigationBar、ToastView
 */
public class PageViewWrapper extends FrameLayout implements OnReloadListener, FFPTags {
    public static final String CLASS_NAME = "PageViewWrapper";
    private final String TAG = "PageViewWrapper@" + Integer.toHexString(hashCode());

    /**
     * @deprecated 兼容ffp低版本会在MSC场景调用MSCActivity的ffpTags接口问题：如果调用了View的ffpTags接口，就不再调用MSCActivity的ffpTags接口了
     * FIXME by chendacai: 这个flag保留几个大版本之后可删掉，从12.6.200版本开始算
     */
    @Deprecated
    public static boolean isEnabledViewFFpTagsFeature = false;

    private boolean isSwiped = false;
    private float mLastTouchX;
    private boolean refreshEnable;
    private String contentUrl, openType;
    private boolean isShow = false;
    private int mBarTextColor = Color.BLACK;
    private int mBgColor = Color.WHITE;
    private Integer mWidgetBgColor = null;

    private OnHorizontalSwipeListener mSwipeListener;
    private @Nullable
    OpenPlatformNavigationBar mNavigationBar;
    private @Nullable
    View mStatusBar;
    private MultiLayerPage mRefreshLayout;
    private BaseRenderer mRenderer;
    private MSCRuntime runtime;
    private boolean isWidget;
    private boolean isCustomBar;    //不显示navigationBar左侧的title、返回键、loading等元素，由前端显示，不影响右侧胶囊按钮
    private boolean mHasLoadUrl;
    private ToastView mToastView;
    public WeakReference<View> webViewModuleRef;
    private int reloadViewId;
    private int detachedViewId = NO_ID;

    private Page parentPage;
    private boolean isHomePage;
    private String url;
    private Runnable mShowToastRunnable;
    private ReloadProcessor mReloadProcessor;
    private boolean mIsHoldKeyboard;
    private boolean mWebViewFocusableFlag;
    private boolean mChildWebViewFocusableFlag;
    private GestureDetector gestureDetector;
    private static final List<Long> mRenderProcessGoneTimeList = new LinkedList<>();
    private boolean mIsHalfScreenPage;

    public PageViewWrapper(Context context) {
        super(context);
    }

    /**
     * 调用此方法完成初始化
     */

    public void setUpPageViewWrapper(MSCRuntime runtime, BaseRenderer renderer, String url, boolean isWidget, boolean isHomePage,
                                     SwipeRefreshLayout.TouchInterceptor innerWebViewStatus) {
        if (MSCTraceUtil.TRACE_LEVEL >= MSCTraceUtil.LEVEL_LIGHT) {
            PerfTrace.begin("PageViewWrapper#setUpPageViewWrapper");
        }
        this.runtime = runtime;
        this.isWidget = isWidget;
        this.isHomePage = isHomePage;
        this.url = url;
        isCustomBar = runtime.getMSCAppModule().isCustomNavigationStyle(url);

        int statusBarHeight = DisplayUtil.getStatusBarHeight();
        // TODO 上面这种认为页面顶上一定有statusBar的推断是错误的，处在分屏的下屏时是没有的
        //  下面注掉的代码能解决这个问题，但直接获取statusBar高度的地方较多，需要保持统一，后续一起更改
        //  微信、MIUI系统应用都处理得较好，但美团内大部分页面都按始终有statusBar处理，部分小程序也会由于写死顶部高度而得到较差的显示效果
//        if (Build.VERSION.SDK_INT >= 23) {
//            statusBarHeight = ((Activity) context).getWindow().getDecorView().getRootWindowInsets().getStableInsetTop();
//        }

        boolean enableSetWebViewWhiteForegroundColor = MSCConfig.enableSetWebViewWhiteForegroundColor(runtime.getAppId());
        // 修复WebView复用闪现上一页面内容问题，显示白色浮层
        if (enableSetWebViewWhiteForegroundColor && renderer.getRendererView() instanceof MSCWebView) {
            setForeground(new ColorDrawable(Color.WHITE));
        }

        if (MSCTraceUtil.TRACE_LEVEL >= MSCTraceUtil.LEVEL_LIGHT) {
            PerfTrace.begin("setUpPageViewWrapper#MultiLayerPage");
        }
        Context context = getContext();
        mRefreshLayout = new MultiLayerPage(context, new SwipeRefreshLayout.TouchInterceptor() {
            @Override
            public boolean onInterceptTouchEvent(MotionEvent event) {
                return innerWebViewStatus.onInterceptTouchEvent(event);
            }
        }, renderer);
        if (MSCTraceUtil.TRACE_LEVEL >= MSCTraceUtil.LEVEL_LIGHT) {
            PerfTrace.end("setUpPageViewWrapper#MultiLayerPage");
        }

        setupAppPage(renderer);
        // mRenderer 在setupAppPage后初始化的

        // 修复WebView复用闪现上一页面内容问题，隐藏白色浮层
        setHideWhiteForegroundColorCallback(enableSetWebViewWhiteForegroundColor);

        LayoutParams params;
        if (isWidget) {
            params = new LayoutParams(LayoutParams.MATCH_PARENT,
                    LayoutParams.MATCH_PARENT);
            addView(mRefreshLayout, params);
        } else {
            if (isCustomBar) {
                params = new LayoutParams(LayoutParams.MATCH_PARENT,
                        LayoutParams.MATCH_PARENT);
                addView(mRefreshLayout, params);
            } else {
                mStatusBar = new View(context);
                addView(mStatusBar, new LayoutParams(LayoutParams.MATCH_PARENT, statusBarHeight));

                params = new LayoutParams(LayoutParams.MATCH_PARENT,
                        LayoutParams.MATCH_PARENT);
                params.topMargin = statusBarHeight;

                FrameLayout frameLayout = new FrameLayout(context);
                addView(frameLayout, params);
                frameLayout.addView(mRefreshLayout, LayoutParams.MATCH_PARENT, LayoutParams.MATCH_PARENT);
            }
        }
        addGestureDetector(context);
        if (MSCTraceUtil.TRACE_LEVEL >= MSCTraceUtil.LEVEL_LIGHT) {
            PerfTrace.end("PageViewWrapper#setUpPageViewWrapper");
        }
    }

    private void setHideWhiteForegroundColorCallback(boolean enableSetWebViewWhiteForegroundColor) {
        if (enableSetWebViewWhiteForegroundColor && mRenderer instanceof MSCWebViewRenderer) {
            MSCWebViewRenderer webViewRenderer = (MSCWebViewRenderer) mRenderer;
            webViewRenderer.setOnFirstRenderListener(new Runnable() {
                @Override
                public void run() {
                    postDelayed(new Runnable() {
                        @Override
                        public void run() {
                            MSCLog.i(TAG, "Resetting the webview foreground color to transparent");
                            setForeground(new ColorDrawable(Color.TRANSPARENT));
                            webViewRenderer.setIsWhiteForegroundShow(false);
                        }
                    }, MSCConfig.getResetWebViewWhiteForegroundColorDelayTimeMillis());
                }
            });
        }
    }

    private void addGestureDetector(Context context) {
        if (MSCTraceUtil.TRACE_LEVEL >= MSCTraceUtil.LEVEL_LIGHT) {
            PerfTrace.begin("PageViewWrapper#addGestureDetector");
        }
        gestureDetector = new GestureDetector(context, new GestureDetector.SimpleOnGestureListener() {
            @Override
            public boolean onScroll(MotionEvent e1, MotionEvent e2, float distanceX, float distanceY) {
                // if条件说明：此手势监听是为了解决holdKeyboard为true且Native渲染，滑动键盘不收起的问题。并且用户在输入框中拖动光标不能收起键盘。
                if (mIsHoldKeyboard && getRenderer().isNativeRender() && !InputTouchUtil.isTouchInput(getContext(), e1)) {
                    // Native渲染滑动收起键盘
                    InputMethodUtil.hideSoftInputFromWindow(getContext(), getWindowToken(), 0);
                }
                return super.onScroll(e1, e2, distanceX, distanceY);
            }
        });
        if (MSCTraceUtil.TRACE_LEVEL >= MSCTraceUtil.LEVEL_LIGHT) {
            PerfTrace.end("PageViewWrapper#addGestureDetector");
        }
    }

    public void setupAppPage(BaseRenderer renderer) {
        if (MSCTraceUtil.TRACE_LEVEL >= MSCTraceUtil.LEVEL_LIGHT) {
            PerfTrace.begin("PageViewWrapper#setupAppPage");
        }
        mRenderer = renderer;

        mReloadProcessor = ReloadProcessorFactory.createReloadProcessor(this);
        mReloadProcessor.init();

        MSCLog.i(TAG, "setupAppPage", renderer, mReloadProcessor);
        mRenderer.setOnReloadListener(this);

        if (mRenderer instanceof MSCWebViewRenderer) {
            MSCWebViewRenderer webViewRenderer = (MSCWebViewRenderer) mRenderer;
            String cacheSinkModeHotZoneData = webViewRenderer.getCacheSinkModeHotZoneData();
            if (cacheSinkModeHotZoneData != null) {
                mRefreshLayout.setRegionData(cacheSinkModeHotZoneData);
            }

        }
        renderer.setReloadViewId(reloadViewId);

        renderer.addAppPageListener(new BaseRenderer.AppPageListener() {
            @Override
            public void onPageReload() {
                mHasLoadUrl = true;
                if (!mIsHalfScreenPage) {
                    // 设置UI只能在主线程。
                    MSCExecutors.runOnUiThread(new Runnable() {
                        @Override
                        public void run() {
                            setBackgroundColorWhenPageReload();
                        }
                    });
                }
            }

            @Override
            public void onFirstRender() {
                MSCExecutors.postOnUiThread(new Runnable() {
                    @Override
                    public void run() {
                        if (runtime.getMSCAppModule() == null) {
                            return;
                        }
                        if (mShowToastRunnable != null) {
                            mShowToastRunnable.run();
                        }
                        // 要求在主线程调用
                        MPListenerManager.getInstance().pageListener.onPageFirstRender(runtime.getMSCAppModule().getAppId(), getContentUrl(), PageViewWrapper.this);
                    }
                });
            }

            @Override
            public void onSinkModeHotZone(String params) {
                if (TextUtils.isEmpty(params)) {
                    MSCLog.e(TAG, "onSinkModeHotZone params is empty");
                    return;
                }
                if (getRefreshLayout() == null) {
                    MSCLog.e(TAG, "getRefreshLayout is null");
                    return;
                }

                getRefreshLayout().setRegionData(params);
            }
        });

        MSCLog.i(TAG, "PageViewWrapper#setupAppPage,reunregisterKeyboardChangemove all native view");
        mRefreshLayout.setContentView(renderer.getRendererView());
        if (MSCTraceUtil.TRACE_LEVEL >= MSCTraceUtil.LEVEL_LIGHT) {
            PerfTrace.end("PageViewWrapper#setupAppPage");
        }
    }


    public PageViewWrapper setReloadViewId(int reloadViewId) {
        this.reloadViewId = reloadViewId;
        return this;
    }

    public PageViewWrapper setParentPage(Page page) {
        this.parentPage = page;
        return this;
    }

    public int getViewId() {
        if (detachedViewId != NO_ID) {
            return detachedViewId;
        }
        return mRenderer == null ? NO_ID : mRenderer.getViewId();
    }

    public String getUrl() {
        return url;
    }

    public void setContentUrl(String url) {
        this.contentUrl = url;
    }

    public void setOpenType(String openType) {
        this.openType = openType;
    }

    public String getContentUrl() {
        return contentUrl;
    }

    public String getOpenType() {
        return openType;
    }

    public void destroy() {
        pageScroller.cancel();
        if (mRenderer != null) {
            /// Renderer 周期要比 PageViewWrapper 长，即时释放注册的资源，避免被依赖
            mRenderer.setOnReloadListener(null);
            if (mRenderer.getRendererView() instanceof MSCWebView) {
                ((MSCWebView) mRenderer.getRendererView()).setOnFullScreenListener(null);
            }
            mRenderer.onDetach();
            detachedViewId = mRenderer.getViewId();  // mAppPage.onDetached()内部可能发生recycle，导致viewId改变，此处事先记录，在退出流程给前端发事件时会用到
        }
        if (mRefreshLayout != null) {
            mRefreshLayout.removeRenderView();
        }
    }

    public void loadPage(OpenParams openParams) {
        mRenderer.loadPage(openParams);  //TODO
    }

    public BaseRenderer getRenderer() {
        return mRenderer;
    }

    public void setProcessGone() {
        // 添加progressgone不上报
        runtime.setIsProcessGone(true);
    }

    @Override
    public boolean dispatchTouchEvent(MotionEvent event) {
        switch (event.getAction()) {
            case MotionEvent.ACTION_DOWN:
                isSwiped = false;
                if (event.getRawX() > 50 || mSwipeListener == null) {
                    isSwiped = false;
                } else {
                    ViewParent parent = getParent().getParent();
                    if (parent instanceof SwipeRefreshLayout) {
                        parent.requestDisallowInterceptTouchEvent(true);
                        ((SwipeRefreshLayout) parent).setEnabled(false);
                    }
                    isSwiped = true;
                    mLastTouchX = event.getRawX();
                    return true;
                }
                break;

            case MotionEvent.ACTION_MOVE:
                if (isSwiped) {
                    float dx = event.getRawX() - mLastTouchX;

                    mSwipeListener.onHorizontalSwipeMove(dx);
                    mLastTouchX = event.getRawX();
                    return true;

                }
                break;
            case MotionEvent.ACTION_CANCEL:
            case MotionEvent.ACTION_UP:
                if (isSwiped) {
                    mSwipeListener.onSwipeTapUp(event.getRawX());
                    return true;
                }
                ViewParent parent = getParent().getParent();
                if (parent != null && parent instanceof SwipeRefreshLayout) {
                    parent.requestDisallowInterceptTouchEvent(false);
                    ((SwipeRefreshLayout) parent).setEnabled(this.refreshEnable);
                }
                break;

        }
        if (gestureDetector != null) {
            return gestureDetector.onTouchEvent(event) || super.dispatchTouchEvent(event);
        }
        return super.dispatchTouchEvent(event);
    }

    public void setSwipeListener(OnHorizontalSwipeListener swipeListener) {
        this.mSwipeListener = swipeListener;
    }

    public void setRefreshEnable(boolean refreshEnable) {
        this.refreshEnable = refreshEnable;
    }

    public boolean isRefreshEnable() {
        return refreshEnable;
    }

    public void initNavigationBar(String url) {
        if (isWidget) return;
        //设置标题栏背景色（通过应用配置信息获取）
        String textColor = runtime.getMSCAppModule().getNavigationBarTextColor(url);
        String bgColor = runtime.getMSCAppModule().getNavigationBarBackgroundColor(url);
//        mCurrentNavigationBar.setTitleTextColor(ColorUtil.parseColor(textColor));
        setNavigationBarTextColor(ColorUtil.parseColor(textColor));
        setNavigationBarIconColor(ColorUtil.parseColor(textColor));
        int naviBgColor = ColorUtil.parseColor(bgColor);
        setNavigationBarBackgroundColor(naviBgColor);

//        Context context = getContext();
//        if (context != null && context instanceof Activity) {
//            UIUtil.setColor((Activity) context, naviBgColor);
//        }
        boolean isDisableNavigationBack = runtime.getMSCAppModule().isDisableNavigationBack(url);

        //更新导航栏返回按钮和标题
        if (isDisableNavigationBack) {
            disableNavigationBack();
        }
        setNavigationBarTitle(runtime.getMSCAppModule().getPageTitle(url));

    }

    public void setNavigationBarTitle(String title) {
        if (isCustomBar) {
            return;
        }

        if (mNavigationBar == null) {
            attachNavigationBar();
        }

        if (mNavigationBar != null) {
            mNavigationBar.setNavigationBarTitle(title);
        }
    }

    public void disableNavigationBack() {
        if (isCustomBar) {
            return;
        }

        if (mNavigationBar == null) {
            attachNavigationBar();
        }

        if (mNavigationBar != null) {
            mNavigationBar.disableNavigationBack();
        }
    }

    private void attachNavigationBar() {
        if (MSCTraceUtil.TRACE_LEVEL >= MSCTraceUtil.LEVEL_LIGHT) {
            PerfTrace.begin("PageViewWrapper#attachNavigationBar");
        }
        int naviBarHeight = CustomNavigationBar.getFixedHeight();
        mNavigationBar = new OpenPlatformNavigationBar(getContext(), isHomePage, runtime, isCustomBar, url);
        int statusBarHeight = DisplayUtil.getStatusBarHeight();
        LayoutParams params;
        if (isCustomBar) {
            params = new LayoutParams(LayoutParams.MATCH_PARENT, naviBarHeight);
            params.topMargin = statusBarHeight;
            addView(mNavigationBar, params);
        } else {
            params = new LayoutParams(LayoutParams.MATCH_PARENT, naviBarHeight);
            params.topMargin = statusBarHeight;
            addView(mNavigationBar, params);

            params = new LayoutParams(LayoutParams.MATCH_PARENT,
                    LayoutParams.MATCH_PARENT);
            params.topMargin = statusBarHeight + naviBarHeight;
            ViewGroup viewGroup = (ViewGroup) mRefreshLayout.getParent();
            viewGroup.setLayoutParams(params);
            viewGroup.requestLayout();
        }
        if (isWidget) {
            mNavigationBar.setVisibility(INVISIBLE);
        }
        if (MSCTraceUtil.TRACE_LEVEL >= MSCTraceUtil.LEVEL_LIGHT) {
            PerfTrace.end("PageViewWrapper#attachNavigationBar");
        }
    }

    public void setNavigationBarTextColor(int frontColor) {
        // 状态栏文字颜色设置
        mBarTextColor = frontColor;
        adjustStatusBarColor();

        // 非自定义导航栏场景，设置状态栏相关逻辑
        if (isCustomBar) {
            return;
        }

        if (mNavigationBar == null) {
            attachNavigationBar();
        }

        if (mNavigationBar != null) {
            mNavigationBar.setNavigationBarTextColor(frontColor);
        }
    }

    public void setNavigationBarIconColor(int iconColor) {

        /**
         * 对其MMP，这个return逻辑去掉。
         * attachNavigationBar（）里面不会因为是否isCustomBar而不能 attach ？
         */
       /* if (isCustomBar) {
            return;
        }*/

        if (mNavigationBar == null) {
            attachNavigationBar();
        }

        if (mNavigationBar != null) {
            mNavigationBar.setNavigationBarIconColor(iconColor);
        }
    }

    public void showNavigationBarMoreMenu(boolean callFromBusiness) {

        if (mNavigationBar == null) {
            attachNavigationBar();
        }
        if (mNavigationBar != null) {
            mNavigationBar.showNavigationBarMoreMenu(callFromBusiness);
        }
    }

    public void hideNavigationBarMoreMenu(boolean callFromBusiness) {
        if (mNavigationBar != null) {
            mNavigationBar.hideNavigationBarMoreMenu(callFromBusiness);
        }
    }

    public void setNavigationBarButtonClickListener(CustomNavigationBar.OnNavigationBarButtonClickListener listener) {
        if (mNavigationBar == null) {
            attachNavigationBar();
        }
        if (mNavigationBar != null) {
            mNavigationBar.setNavigationBarButtonClickListener(listener);
        }
    }

    public void setNavigationBarBackgroundColor(int backgroundColor) {
        if (!isCustomBar) {
            if (mNavigationBar != null) {
                mNavigationBar.setBackgroundColor(backgroundColor);
            }
            if (mStatusBar != null) {
                mStatusBar.setBackgroundColor(backgroundColor);
            }
        }
    }

    public int getNavigationBarHeight() {
        if (mNavigationBar != null) {
            return mNavigationBar.getHeight();
        } else {
            return 0;
        }
    }

    public Rect getMenuRect() {
        if (mNavigationBar != null) {
            return mNavigationBar.getMenuRect();
        } else {
            return new Rect();
        }
    }

    public boolean isMenuButtonShown() {
        return mNavigationBar != null && mNavigationBar.isMenuButtonShown();
    }

    public boolean isToastMasking() {
        return mToastView != null && mToastView.isMasking();
    }

    @Override
    public void onReload(HashMap<String, Object> map) {
        mReloadProcessor.reload(map);
    }

    public void hideToast() {
        if (mToastView != null) {
            mToastView.hide();
        }
        mShowToastRunnable = null;
        mToastView = null;
    }

    public ToastView getToastView() {
        return mToastView;
    }

    public MultiLayerPage getRefreshLayout() {
        return mRefreshLayout;
    }

    @Override
    public void setBackgroundColor(int color) {
        mBgColor = color;
        if (mHasLoadUrl) {
            super.setBackgroundColor(mBgColor);
        }
        if (!isWidget && !MSCHornRollbackConfig.isRollbackBackgroundColor()) {
            if (mRefreshLayout != null) {
                mRefreshLayout.setBackgroundColor(mBgColor);
            }
        }
    }

    /**
     * 设置widget背景色
     */
    public void setWidgetBackgroundColor(int color) {
        mWidgetBgColor = color;
        setBackgroundColor(color);
        if (null != mRefreshLayout && isWidget) {
            mRefreshLayout.setBackgroundColor(color);
        }
    }

    /**
     * 设置webview渲染场景下sinkMode模式native组件层级背景色
     */
    public void setSinkModeBackgroundColor(int color) {
        if (!isWidget) {
            mBgColor = color;
            if (mHasLoadUrl) {
                super.setBackgroundColor(mBgColor);
            }
        }
    }

    /**
     * 页面重新加载时重新设置背景色
     */
    private void setBackgroundColorWhenPageReload() {
        if (isWidget && mWidgetBgColor != null) {
            PageViewWrapper.super.setBackgroundColor(mWidgetBgColor);
            if (mRefreshLayout != null) {
                mRefreshLayout.setBackgroundColor(mWidgetBgColor);
            }
        } else {
            PageViewWrapper.super.setBackgroundColor(mBgColor);
            // 设置 Page 背景色不能影响 Widget transparent 效果
            if (!isWidget && mRenderer instanceof MSCWebViewRenderer) {
                MSCWebViewRenderer webViewRenderer = (MSCWebViewRenderer) mRenderer;
                webViewRenderer.evaluateBackgroundColorReset();
            }
        }
    }

    public void setBackgroundTextStyle(boolean isDarkMode) {
        if (getRefreshLayout() != null) {
            getRefreshLayout().setBackgroundTextStyle(isDarkMode);
        }
    }

    public void showMsiToast(View toastView, IPage.ViewParam viewParam) {
        // 历史逻辑：如果有正在展示的 就不展示新的 ?
        if (mToastView == null) {
            mToastView = (ToastView) toastView;
        }
        ViewGroup parent = null;

        if (viewParam != null && viewParam.relativeToScreen) {
            Window window;
            if (getRenderer() != null && (window = getRenderer().getWindow()) != null) {
                parent = (ViewGroup) window.getDecorView();
            } else {
                MSCLog.i("showMsiToast", "can't find current msc window, downgrade to show toast in current view");
            }
        }
        if (parent == null) {
            parent = PageViewWrapper.this;
        }
        if (mToastView.getParent() != parent) {
            parent.addView(UIUtil.removeFormParentIfNeed(mToastView));
        }
        if (mRenderer.hasFirstRender()) {
            mToastView.setVisibility(VISIBLE);
        } else {
            mToastView.setVisibility(GONE);
            // firstRender前可能调用toast（如在小程序的App生命周期里），会与小程序loading重叠，不允许显示
            mShowToastRunnable = new Runnable() {
                @Override
                public void run() {
                    mToastView.setVisibility(VISIBLE);
                }
            };
        }
    }

    public void showNavigationBarLoading() {
        if (!isCustomBar && mNavigationBar != null) {
            mNavigationBar.showNavigationBarLoading();
        }
    }

    public void hideNavigationBarLoading() {
        if (!isCustomBar && mNavigationBar != null) {
            mNavigationBar.hideNavigationBarLoading();
        }
    }

    public void setHalfScreenPage(boolean halfScreenPage) {
        this.mIsHalfScreenPage = halfScreenPage;
    }

    public interface OnHorizontalSwipeListener {
        void onHorizontalSwipeMove(float dx);

        void onSwipeTapUp(float x);
    }

    /**
     * 发生RenderProcessGone时reload，在前台发生立即调用，否则等到下次onPageResume时调用
     *
     * @return
     */
    public HashMap<String, Object> reloadIfRenderProcessGone() {
        return mReloadProcessor.reloadIfStateSet();
    }

    public final void ensureShow() {
        if (!isShow) {
            onShow();
            isShow = true;
        }
    }

    public final void ensureHide() {
        if (isShow) {
            onHide();
            isShow = false;
        }
    }

    protected void onShow() {
        adjustStatusBarColor();
        mRenderer.onShow();
        if (mRenderer.getRendererView() instanceof MSCWebView) {
            // TODO 暂不确定非WebView是否需要
            addWebViewFullScreenListener(((MSCWebView) mRenderer.getRendererView()));
        }
    }

    protected void onHide() {
        if (mRenderer != null) {
            mRenderer.onHide();
        }
    }

    /**
     * 对齐iOS，widget场景不改变状态栏颜色；可以建议业务使用MSI setStatusBarStyle
     */
    private void adjustStatusBarColor() {
        if (isWidget) return;

        Context context = getContext();
        if (context instanceof Activity) {
            StatusBarUtils.setStatusBarTextColor((Activity) context, mBarTextColor == Color.BLACK);
        }
    }

    private View mWebViewCustomView;
    private int mOriSystemUiVisibility;
    private boolean fixH5VideoFullScreenBottomNaviBarCover = MSCHornRollbackConfig.readConfig().fixH5VideoFullScreenBottomNaviBarCover;

    private void addWebViewFullScreenListener(IWebView webView) {
        webView.setOnFullScreenListener(new OnWebViewFullScreenListener() {
            WebChromeClientCustomViewCallback callback;

            @Override
            public void showCustomView(View view, WebChromeClientCustomViewCallback callback) {
                this.callback = callback;
                if (mWebViewCustomView != null) {
                    callback.onHideCustomView();
                    return;
                }
                showFullCustomView(view);
            }

            @Override
            public void hideCustomView() {
                hideFullCustomView(callback);
            }

        });
    }

    /**
     * 视频播放全屏
     **/
    private void showFullCustomView(View view) {
        if (getContext() instanceof Activity) {
            ViewGroup decor = (ViewGroup) ((Activity) getContext()).getWindow().getDecorView();
            FrameLayout videoViewContainer = new FrameLayout(getContext());
            FrameLayout.LayoutParams params = new FrameLayout.LayoutParams(ViewGroup.LayoutParams.MATCH_PARENT, ViewGroup.LayoutParams.MATCH_PARENT);
            videoViewContainer.addView(view, params);
            videoViewContainer.setBackgroundColor(Color.BLACK);
            decor.addView(videoViewContainer, params);
            mWebViewCustomView = videoViewContainer;
            if (fixH5VideoFullScreenBottomNaviBarCover) {
                mOriSystemUiVisibility = DisplayUtil.enterFullScreen(getContext());
            } else {
                DisplayUtil.setStatusBarVisibility(false, getContext());
            }
        }
    }

    /**
     * 隐藏视频全屏
     */
    private void hideFullCustomView(WebChromeClientCustomViewCallback callback) {
        if (mWebViewCustomView == null || !(getContext() instanceof Activity)) {
            return;
        }
        if (fixH5VideoFullScreenBottomNaviBarCover) {
            DisplayUtil.exitFullScreen(getContext(), mOriSystemUiVisibility);
        } else {
            DisplayUtil.setStatusBarVisibility(true, getContext());
        }
        ViewGroup decor = (ViewGroup) ((Activity) getContext()).getWindow().getDecorView();
        decor.removeView(mWebViewCustomView);
        mWebViewCustomView = null;
        if (callback != null) {
            callback.onHideCustomView();
        }
    }


    public void showPageNotFoundView(String url) {
        LinearLayout pageNotFoundView = findViewById(R.id.page_not_found_view);
        // page not found 兜底页为原生静态页面，直接删掉webview， 避免webview展示vconsole或其他内容
        if (mRefreshLayout != null) {
            mRefreshLayout.setVisibility(GONE);
        }
        initNavigationBar(url);
        if (pageNotFoundView == null) {
            pageNotFoundView = (LinearLayout) inflate(getContext(), R.layout.msc_page_not_found, null);
            if (pageNotFoundView != null) {

                int statusBarHeight = DisplayUtil.getStatusBarHeight();
                int naviBarHeight = CustomNavigationBar.getFixedHeight();
                FrameLayout.LayoutParams params = new LayoutParams(LayoutParams.MATCH_PARENT,
                        LayoutParams.MATCH_PARENT);
                params.topMargin = statusBarHeight + naviBarHeight;
                addView(pageNotFoundView, params);

                TextView textView = findViewById(R.id.page_not_found_msg);
                textView.setText(String.format(getContext().getString(R.string.msc_page_not_found_message), runtime.getMSCAppModule().getAppName()));
            }
        }
    }

    /**
     * 开始下拉刷新，触发下拉刷新动画
     */
    public void startPullDownRefresh() {
        SwipeRefreshLayout refreshLayout = getRefreshLayout();
        if (refreshLayout != null && refreshLayout.isEnabled() && !refreshLayout.isRefreshing()) {
            refreshLayout.setRefreshing(true);
        }
    }

    /**
     * 停止下拉刷新
     */
    public void stopPullDownRefresh() {
        SwipeRefreshLayout refreshLayout = getRefreshLayout();
        if (refreshLayout != null && refreshLayout.isRefreshing()) {
            refreshLayout.setRefreshing(false);
        }
    }

    public void disableScrollBounce(boolean disable) {
        if (isRefreshEnable()) {
            SwipeRefreshLayout refreshLayout = getRefreshLayout();
            if (refreshLayout != null) {
                if (refreshLayout.isEnabled() && disable) {
                    refreshLayout.setEnabled(false);
                } else if (!refreshLayout.isEnabled() && !disable) {
                    refreshLayout.setEnabled(true);
                }
            }
        }
    }

    PageScroller pageScroller = new PageScroller();

    public void startScroll(int targetY, int duration, MsiContext callback) {
        pageScroller.startScroll(this, targetY, duration, callback);
    }

    public void adjustPosition(int diff, int bottomInsetHeight, boolean scroll) {
        if (diff > 0) {
            // 通常为Input被键盘遮挡（offset > keyTop），需要上推
            scrollY(diff, bottomInsetHeight, scroll);
        } else if (diff < 0) {
            // 但也存在短暂获取键盘高度错误，导致键盘顶部留空的情况，需要往回调整
            // 保守起见，目前仅在调整范围能被pan吸收的情况下调整，不进行scroll
            if (getPan() > 0 && getPan() >= -diff) {
                scrollY(diff, bottomInsetHeight, scroll);
            }
        }
    }

    public void adjustPositionForCursor(int diff, int bottomInsetHeight, boolean scroll) {
        if (diff > 0) {
            // 通常为Input被键盘遮挡（offset > keyTop），需要上推
            scrollYForCursor(diff, bottomInsetHeight, scroll);
        } else if (diff < 0) {
            // 但也存在短暂获取键盘高度错误，导致键盘顶部留空的情况，需要往回调整
            // 保守起见，目前仅在调整范围能被pan吸收的情况下调整，不进行scroll
            if (getPan() > 0 && getPan() >= -diff) {
                scrollYForCursor(diff, bottomInsetHeight, scroll);
            }
        }
    }

    /**
     * 背景是为了解决Input/TextArea在键盘弹起时会弹起过高顶上去的问题。修复思路是参考MMP的延迟执行adjustPosition。
     * MSI新增的接口实现，MSI Input/TextArea组件侧有代码级开关控制是否切换到当前新接口调用。
     * 新接口核心是基于MSI侧adjustPosition的前置实现，但是有容器定制的空间，比如延迟执行逻辑。
     *
     * @param view
     * @param adjustKeyboardTo
     * @param cursorSpacing
     * @param bottomInsetHeight
     */
    public void adjustPosition(View view, String adjustKeyboardTo, int cursorSpacing, int bottomInsetHeight, int delayDur) {
        MSCLog.i(TAG, "adjustPosition adjustKeyboardTo:" + adjustKeyboardTo + ", cursorSpacing:" + cursorSpacing + ", bottomInsetHeight:" + bottomInsetHeight + ", delayDur:" + delayDur);
        if (!(view instanceof MSIBaseInput)) {
            MSCLog.w(TAG, "PageViewWrapper adjustPosition exception: target view is not msi input!");
            return;
        }
        MSIBaseInput input = (MSIBaseInput) view;
        String appId = runtime != null ? runtime.getAppId() : "";
        // delayDur没有设置（小于等于0），并且没有命中延时执行白名单，则无延时执行
        if (delayDur <= 0 && !MSCHornRollbackConfig.isAdjustPositionDelay(appId)) {
            if (isAdjustKeyboardToCursorMode(adjustKeyboardTo)) {
                adjustPositionRealRunForCursor(input, cursorSpacing, appId, bottomInsetHeight);
            } else {
                adjustPositionRealRunForBottom(input, cursorSpacing, appId, bottomInsetHeight);
            }
            return;
        }
        // 接下来是delayDur设置了，或者没有设置但是延时执行白名单是命中的，则会对延时时间兜底后进行延时执行。
        if (delayDur <= 0) {
            delayDur = MSCHornRollbackConfig.getAdjustPositionDelayTime();
        }
        postDelayed(new Runnable() {
            @Override
            public void run() {
                if (isAdjustKeyboardToCursorMode(adjustKeyboardTo)) {
                    adjustPositionRealRunForCursor(input, cursorSpacing, appId, bottomInsetHeight);
                } else {
                    adjustPositionRealRunForBottom(input, cursorSpacing, appId, bottomInsetHeight);
                }
            }
        }, delayDur);
    }

    private boolean isAdjustKeyboardToCursorMode(String adjustKeyboardTo) {
        return TextUtils.equals(adjustKeyboardTo, MSIBaseInput.AdjustKeyboardToType.CURSOR);
    }

    private void adjustPositionRealRunForBottom(MSIBaseInput input, int cursorSpacing, String appId, int bottomInsetHeight) {
        if (input == null) {
            return;
        }
        Rect rect = new Rect();
        input.getGlobalVisibleRect(rect);
        int newCursorSpacing = computeCursorSpacing(input, cursorSpacing);
        int offset = rect.bottom + newCursorSpacing;
        Context context = getContext();
        if (context instanceof Activity) {
            DisplayUtil.setDisplayMetrics((Activity) context);
        }
        int keyTop = DisplayUtil.getScreenHeightReal() - bottomInsetHeight;
        boolean scroll = true;
        int totalSize = getContentHeight();
        int diff = offset - keyTop;
        if (offset > totalSize) {
            scroll = false;
        }
        MSCLog.i(TAG, "adjustPositionRealRunForBottom diff:", diff, ", bottomInsetHeight:", bottomInsetHeight,
                ", scroll:", scroll, ", appId:", appId);
        adjustPosition(diff, bottomInsetHeight, scroll);
    }

    protected final int computeCursorSpacing(View view, int cursorSpacing) {
        if (view == null || cursorSpacing < 0) {
            return 0;
        }
        int[] pageLoc = new int[2];//计算页面在窗口的位置
        int[] viewLoc = new int[2];//计算view在窗口的位置
        getLocationInWindowInPage(pageLoc);
        view.getLocationInWindow(viewLoc);
        int bottom = getContentHeight() - (viewLoc[1] - pageLoc[1] + view.getMeasuredHeight());
        //计算view到页面底部的距离，页面总长度-view底部都窗口顶部的距离
        return Math.min(bottom, cursorSpacing);
    }

    private void adjustPositionRealRunForCursor(MSIBaseInput input, int cursorSpacing, String appId, int bottomInsetHeight) {
        if (input == null) {
            return;
        }
        Rect rect = new Rect();
        input.getGlobalVisibleRect(rect);
        int newCursorSpacing = computeCursorSpacingForCursor(input, cursorSpacing);
        int offset = input.getCursorY() + newCursorSpacing;
        MSCLog.i(TAG, "adjustPositionRealRunForCursor input.getCursorY():", input.getCursorY(),
                ", rect.top:", rect.top, ", input.getLocalCursorY():", input.getLocalCursorY());
        Context context = getContext();
        if (context instanceof Activity) {
            DisplayUtil.setDisplayMetrics((Activity) context);
        }
        int keyTop = DisplayUtil.getScreenHeightReal() - bottomInsetHeight - input.getLineHeight();
        boolean scroll = true;
        int totalSize = getContentHeight();
        int diff = offset - keyTop;
        MSCLog.i(TAG, "adjustPositionRealRunForCursor keyTop:", keyTop, ", offset:", offset, ", diff:", diff);
        if (offset > totalSize) {
            scroll = false;
        }
        MSCLog.i(TAG, "adjustPositionRealRunForCursor diff:", diff, ", scroll:", scroll, ", appId:", appId);
        adjustPositionForCursor(diff, bottomInsetHeight, scroll);
    }

    protected final int computeCursorSpacingForCursor(MSIBaseInput view, int cursorSpacing) {
        if (view == null || cursorSpacing < 0) {
            return 0;
        }
        int[] pageLoc = new int[2];//计算页面在窗口的位置
        getLocationInWindowInPage(pageLoc);
        int viewLocY = view.getCursorY();
        int bottom = getContentHeight() - (viewLocY - pageLoc[1] + view.getLineHeight());
        //计算view到页面底部的距离，页面总长度-view底部都窗口顶部的距离
        return Math.min(bottom, cursorSpacing);
    }

    public void scrollY(int diff, int bottomInsetHeight, boolean scroll) {
        if (diff != 0) {
            int panOffsetFixed = Math.min(diff, bottomInsetHeight);
            adjustPan(panOffsetFixed);
            if (scroll) {
                if (diff > bottomInsetHeight) {
                    pageScroller.scrollContentY(this, diff - bottomInsetHeight);
                }
            }
        }
    }

    public void scrollYForCursor(int diff, int bottomInsetHeight, boolean scroll) {
        MSCLog.d(TAG, "scrollYForCursor diff: ", diff, ", bottomInsetHeight: ", bottomInsetHeight);
        if (diff != 0) {
            int deltaY = adjustPanForCursor(diff, bottomInsetHeight);
            MSCLog.d(TAG, "scrollYForCursor adjustPanForCursor deltaY: ", deltaY);
            if (scroll) {
                if (deltaY != 0) {
                    MSCLog.d(TAG, "scrollYForCursor scrollContentY deltaY: ", deltaY);
                    pageScroller.scrollContentY(this, deltaY);
                }
            }
        }
    }

    public void adjustPan(int diff) {
        SwipeRefreshLayout refreshLayout = getRefreshLayout();
        if (refreshLayout == null) {
            return;
        }
        RelativeLayout.MarginLayoutParams marginLayoutParams = (MarginLayoutParams) refreshLayout.getLayoutParams();
        if (marginLayoutParams.bottomMargin == diff) return;
        if (diff == 0) {
            marginLayoutParams.bottomMargin = 0;
            marginLayoutParams.topMargin = 0;
        } else {
            marginLayoutParams.bottomMargin += diff;
            marginLayoutParams.topMargin += (-diff);
        }

        MSCLog.d(TAG, "adjustPan ", pan, " -> ", marginLayoutParams.bottomMargin);
        pan = marginLayoutParams.bottomMargin;

        refreshLayout.setLayoutParams(marginLayoutParams);
    }

    public int adjustPanForCursor(int diff, int bottomInsetHeight) {
        SwipeRefreshLayout refreshLayout = getRefreshLayout();
        if (refreshLayout == null) {
            return 0;
        }
        RelativeLayout.MarginLayoutParams marginLayoutParams = (MarginLayoutParams) refreshLayout.getLayoutParams();
        int navigationBarHeight = SystemInfoUtils.getSystemNavigationBarHeight(getContext());
        int bottomInsetHeight2 = bottomInsetHeight - navigationBarHeight;
        int margin = Math.min(marginLayoutParams.bottomMargin + diff, bottomInsetHeight2);
        if (diff == 0) {
            marginLayoutParams.bottomMargin = 0;
            marginLayoutParams.topMargin = 0;
        } else {
            marginLayoutParams.bottomMargin = margin;
            marginLayoutParams.topMargin = -margin;
        }
        int oldPan = pan;

        MSCLog.d(TAG, "adjustPan2 ", pan, " -> ", marginLayoutParams.bottomMargin);
        pan = marginLayoutParams.bottomMargin;

        refreshLayout.setLayoutParams(marginLayoutParams);
        return oldPan + diff - pan;
    }

    private int pan;

    public int getPan() {
        return pan;
    }

    private int keyboardHeight; // 最新的键盘高度


    public int getKeyboardHeight() {
        return keyboardHeight;
    }

    public void setKeyboardHeight(int keyboardHeight) {
        if (!MSIBaseInput.enableMscFixedKeyboardHeight(runtime.getAppId())) {
            if (MSCHornRollbackConfig.isFixOnKeyBoardHeightChange(runtime.getAppId()) && keyboardHeight != 0) {
                int naviBarHeight = 0;
                if (getContext() instanceof Activity) {
                    naviBarHeight = SystemInfoUtils.getSystemNavigationBarHeight((Activity) getContext());
                }
                keyboardHeight -= naviBarHeight;
            }
        }
        this.keyboardHeight = keyboardHeight;
        for (IKeyBoardHeightChangeObserver keyBoardHeightChangeListener : keyBoardHeightChangeListeners) {
            keyBoardHeightChangeListener.onKeyboardHeightChanged(keyboardHeight);
        }
    }

    /**
     * 在键盘弹出或者收起时，根据Input属性做出相应处理
     *
     * @param isShow 键盘出现/隐藏
     */
    public void processInputAction(boolean isShow) {
        MultiLayerPage pageSwipeRefreshLayout = getRefreshLayout();
        if (pageSwipeRefreshLayout == null || pageSwipeRefreshLayout.getCoverViewContainer() == null) {
            return;
        }
        View view = findFocus();
        MSCLog.i(TAG, "[Keyboard]processInputAction isShow:", isShow, ", view:",
                view == null ? null : Integer.toHexString(view.hashCode()));
        if (view instanceof com.meituan.msi.api.component.input.InputComponent) {
            com.meituan.msi.api.component.input.InputComponent inputComponent = (com.meituan.msi.api.component.input.InputComponent) view;
            // 处理InputComponent接口
            if (inputComponent.hasFocus()) {
                this.mIsHoldKeyboard = inputComponent.isHoldKeyboard();
            } else {
                this.mIsHoldKeyboard = false;
            }
            applyKeyboardHoldingState(isShow);
        }
    }

    /**
     * 处理holdKeyboard逻辑，在holdKeyboard为true时点击WebView空白区域时键盘不收起
     */
    private void applyKeyboardHoldingState(boolean isKeyboardShow) {
        if (mIsHoldKeyboard) {
            if (!getRenderer().isNativeRender() && getRenderer().getRendererView() instanceof MSCWebView) {
                // WebView渲染禁止WebView获取焦点
                View webView = ((MSCWebView) getRenderer().getRendererView()).getWebView();
                MSCLog.i(TAG, "[Keyboard]applyKeyboardHoldingState webView:", webView);
                if (isKeyboardShow) {
                    if (webView.isFocusable()) {
                        webView.setFocusable(false);
                        MSCLog.i(TAG, "[Keyboard]applyKeyboardHoldingState webView.setFocusable(false)");
                        mWebViewFocusableFlag = true;
                    }
                    // 自研内核使用降级模式时, 会通过 SystemWebViewProvider 给 MTWebView 添加一级子View: MTSystemWebView
                    // 美团 App 中该 View 存在时会抢占事件焦点导致 TextArea 被动失去焦点键盘收起.
                    // 这时候 MTWebView 没有抢焦点 故 webView.isFocusable() 是 false, 需要再向下获取一级主动让它失焦.
                    // 什么情况会走到降级模式？1.没有下载到自研内核时, 2.加载自研内核失败时(概率低)
                    if (webView instanceof MTWebView && ((MTWebView) webView).getChildCount() > 0) {
                        View childWebView = ((MTWebView) webView).getChildAt(0);
                        if (childWebView instanceof MTSystemWebView && childWebView.isFocusable()) {
                            childWebView.setFocusable(false);
                            MSCLog.i(TAG, "[Keyboard]applyKeyboardHoldingState childWebView.setFocusable(false)");
                            mChildWebViewFocusableFlag = true;
                        }
                    }
                } else {
                    if (mWebViewFocusableFlag) {
                        webView.setFocusable(true);
                        MSCLog.i(TAG, "[Keyboard]applyKeyboardHoldingState webView.setFocusable(true)");
                        mWebViewFocusableFlag = false;
                    }
                    if (mChildWebViewFocusableFlag && webView instanceof MTWebView && ((MTWebView) webView).getChildCount() > 0) {
                        View systemWebView = ((MTWebView) webView).getChildAt(0);
                        if (systemWebView instanceof MTSystemWebView) {
                            systemWebView.setFocusable(true);
                            MSCLog.i(TAG, "[Keyboard]applyKeyboardHoldingState childWebView.setFocusable(true)");
                            mChildWebViewFocusableFlag = false;
                        }
                    }
                }
            }
        }
    }

    public void getLocationInWindowInPage(@Size(2) int[] outLocation) {
        if (getRefreshLayout() != null) {
            getRefreshLayout().getLocationInWindow(outLocation);
        } else {
            outLocation[0] = outLocation[1] = 0;
        }
    }

    public int getContentHeight() {
        return getRenderer().getRendererView().getContentHeight();
    }

    List<IKeyBoardHeightChangeObserver> keyBoardHeightChangeListeners = new ArrayList<>();

    public void registerKeyboardChange(IKeyBoardHeightChangeObserver listener) {
        keyBoardHeightChangeListeners.add(listener);
    }

    public void unregisterKeyboardChange(IKeyBoardHeightChangeObserver listener) {
        keyBoardHeightChangeListeners.remove(listener);
    }

    public boolean hasInnerWebViewModule() {
        View webView;
        if (webViewModuleRef == null || (webView = webViewModuleRef.get()) == null) {
            return false;
        }
        return webView.isAttachedToWindow();
    }

    @Nullable
    public View getWebViewComponent() {
        if (webViewModuleRef == null) {
            return null;
        }
        return webViewModuleRef.get();
    }

    public boolean isPageShow() {
        return isShow;
    }

    @NonNull
    @Override
    public Map<String, Object> ffpTags() {
        isEnabledViewFFpTagsFeature = true;
        if (mRenderer == null || mRenderer.pageData == null || mRenderer.pageData.appPageReporter == null) {
            return Collections.emptyMap();
        }
        Map<String, Object> tags = mRenderer.pageData.appPageReporter.getFFPTags();
        if (tags == null) {
            tags = Collections.emptyMap();
        }
        return tags;
    }

    public int getWebScrollY() {
        return pageScroller.getWebScrollY(this);
    }

    public MSCRuntime getRuntime() {
        return runtime;
    }

    public boolean isShow() {
        return isShow;
    }

    public Page getParentPage() {
        return parentPage;
    }

    public void checkNeedFallbackToSystemWebView() {
        MSCLog.i(TAG, "needFallbackToSystemWebView:", WebViewCacheManager.isNeedFallbackToSystemWebView());
        if (WebViewCacheManager.isNeedFallbackToSystemWebView()) {
            return;
        }
        boolean isMTWebView = mRenderer != null && (mRenderer instanceof MSCWebViewRenderer);
        boolean isTopPage = false;
        boolean isTopApp = runtime == RuntimeManager.getLatestAttachToContainerRuntime();
        if (runtime != null && runtime.getTopPageModule() != null && runtime.getTopPageModule() == parentPage.getCurPageModule()) {
            isTopPage = true;
        }
        MSCLog.i(TAG, "isMTWebView:", isMTWebView, "isTopPage:", isTopPage, "isTopApp:", isTopApp);
        if (!isMTWebView || !isTopPage || !isTopApp) {
            return;
        }
        int threshold = MSCConfig.getFallbackToSystemWebViewThreshold();
        if (threshold <= 0) {
            MSCLog.e(TAG, "fallbackToSystemWebViewThreshold <= 0");
            return;
        }
        synchronized (mRenderProcessGoneTimeList) {
            if (WebViewCacheManager.isNeedFallbackToSystemWebView()) {
                return;
            }
            mRenderProcessGoneTimeList.add(System.currentTimeMillis());
            MSCLog.i(TAG, "webViewCrashTime:", mRenderProcessGoneTimeList.size());
            if (mRenderProcessGoneTimeList.size() >= threshold) {
                if (mRenderProcessGoneTimeList.get(threshold - 1) - mRenderProcessGoneTimeList.get(0) < 10000) {
                    MSCLog.e(TAG, "ForceFallbackToSystemWebView threshold:", threshold);
                    WebViewCacheManager.setNeedFallbackToSystemWebView(true);
                }
                mRenderProcessGoneTimeList.remove(0);
            }
        }
    }

    public boolean isWidget() {
        return isWidget;
    }
}
