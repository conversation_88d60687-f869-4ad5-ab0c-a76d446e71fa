package com.meituan.msc.modules.page.view;

import android.view.View;

import com.meituan.msc.modules.api.msi.components.coverview.MSCScrollView;
import com.meituan.msc.modules.page.view.coverview.CoverViewRootContainer;
import com.meituan.msc.modules.page.view.coverview.ViewBaseContainer;

public class ViewFinder {
    private static CoverViewWrapper findCoverViewById(CoverViewWrapper view, int id) {
        if (view == null) {
            return null;
        }
        if (view.getId() == id) {
            return view;
        }
        for (int i = 0; i < view.getChildCount(); i++) {
            View viewChild = view.getChildAt(i);
            if (viewChild instanceof CoverViewWrapper) {
                CoverViewWrapper rcView = (CoverViewWrapper) viewChild;
                rcView = findCoverViewById(rcView, id);
                if (rcView != null) {
                    return rcView;
                }
            } else if (viewChild instanceof MSCScrollView) {
                MSCScrollView coverScrollView = (MSCScrollView) viewChild;
                CoverViewWrapper cover = findCoverViewById(coverScrollView, id);
                if (cover != null) {
                    return cover;
                }
            }
        }
        return null;
    }

    private static CoverViewWrapper findCoverViewById(MSCScrollView view, int id) {
        for (int i = 0; i < view.getRootFrame().getChildCount(); i++) {
            View viewChild = view.getRootFrame().getChildAt(i);
            if (viewChild instanceof CoverViewWrapper) {
                CoverViewWrapper rcView = (CoverViewWrapper) viewChild;
                rcView = findCoverViewById(rcView, id);
                if (rcView != null) {
                    return rcView;
                }
            }
        }
        return null;
    }


    public static CoverViewWrapper findCoverViewWrapper(CoverViewRootContainer coverViewContainer, int viewId) {
        if (coverViewContainer == null) {
            return null;
        }
        return coverViewContainer.findCoverViewById(viewId);
    }


    public static CoverViewWrapper findCoverViewWrapper(ViewBaseContainer coverViewContainer, int viewId) {
        for (int i = 0; i < coverViewContainer.getChildCount(); i++) {
            View tmpView = coverViewContainer.getChildAt(i);
            if (tmpView instanceof CoverViewWrapper) {
                CoverViewWrapper coverViewWrapper = (CoverViewWrapper) tmpView;
                CoverViewWrapper cover = findCoverViewById(coverViewWrapper, viewId);
                if (cover != null) {
                    return cover;
                }
            }
        }
        return null;
    }
}
