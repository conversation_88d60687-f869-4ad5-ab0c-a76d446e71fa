package com.meituan.msc.modules.page.view.coverview;

import android.content.Context;
import android.util.AttributeSet;
import android.view.MotionEvent;
import android.view.View;
import android.view.ViewGroup;

import com.meituan.msc.common.utils.InputMethodUtil;

/**
 * CoverViewContainer{包含业务View}>CoverViewWrapper>Page
 * Created by bunnyblue on 4/18/18.
 */
public class CoverViewNormalContainer extends ViewBaseContainer {

    public CoverViewNormalContainer(Context context) {
        super(context);
        init();
    }

    public CoverViewNormalContainer(Context context, AttributeSet attrs) {
        super(context, attrs);
        init();
    }

    public CoverViewNormalContainer(Context context, AttributeSet attrs, int defStyleAttr) {
        super(context, attrs, defStyleAttr);
        init();
    }

    @Override
    public void addView(View child, int index, ViewGroup.LayoutParams params) {
        super.addView(child, index, params);
    }

    @Override
    public void removeView(View view) {
        super.removeView(view);
    }

    @Override
    protected void onAttachedToWindow() {
        super.onAttachedToWindow();
    }

    @Override
    protected void onDetachedFromWindow() {
        super.onDetachedFromWindow();
    }

    @Override
    public boolean onTouchEvent(MotionEvent event) {
        if (event.getAction() == MotionEvent.ACTION_DOWN) {
            boolean isHoldKeyboard = getHoldKeyboard();
            if (!isHoldKeyboard && !InputTouchUtil.isTouchInput(getContext(), event)) {
                InputMethodUtil.hideSoftInputFromWindow(getContext(), getWindowToken(), 0);
            }
        }
        return super.onTouchEvent(event);
    }

    public void init() {

    }
}
