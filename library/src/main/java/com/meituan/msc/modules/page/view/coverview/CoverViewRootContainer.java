package com.meituan.msc.modules.page.view.coverview;

import android.content.Context;
import android.text.TextUtils;
import android.util.AttributeSet;
import android.view.MotionEvent;
import android.view.View;
import android.widget.FrameLayout;

import com.google.gson.JsonObject;
import com.meituan.msc.common.utils.InputMethodUtil;
import com.meituan.msc.modules.page.render.webview.OnContentScrollChangeListener;
import com.meituan.msc.modules.page.view.CoverViewWrapper;
import com.meituan.msc.modules.page.view.ViewFinder;
import com.meituan.msi.api.component.input.MSIBaseInput;
import com.meituan.msi.view.INativeLifecycleInterceptor;

/**
 * CoverViewContainer{包含业务View}>CoverViewWrapper>Page
 * Created by bunnyblue on 4/18/18.
 */

public class CoverViewRootContainer extends FrameLayout implements OnContentScrollChangeListener,
        INativeLifecycleInterceptor {
    //    private final List<INativeLifecycleInterceptor> backInterceptors = new LinkedList<>();
    private FixedViewContainer fixedViewContainer = null;
    private CoverViewNormalContainer coverViewNormalContainer = null;

    public CoverViewRootContainer(Context context) {
        super(context);
        init();
    }

    public int getCoverViewScrollY() {
        return coverViewNormalContainer.getScrollY();
    }

    public CoverViewRootContainer(Context context, AttributeSet attrs) {
        super(context, attrs);
        init();
    }

    public CoverViewRootContainer(Context context, AttributeSet attrs, int defStyleAttr) {
        super(context, attrs, defStyleAttr);
        init();
    }


    public final CoverViewWrapper findCoverViewById(int viewId) {
        CoverViewWrapper coverViewWrapper = ViewFinder.findCoverViewWrapper(fixedViewContainer, viewId);
        if (coverViewWrapper == null) {
            coverViewWrapper = ViewFinder.findCoverViewWrapper(coverViewNormalContainer, viewId);
        }

        return coverViewWrapper;
    }

    public boolean insertApiViewToContainerAuto(View view, JsonObject jsonObject) {
        if (!(view instanceof MSIBaseInput)) {
            if (jsonObject.has("fixed")) {
                if (jsonObject.get("fixed").getAsBoolean()) {
                    jsonObject.remove("parentId");
                    return fixedViewContainer.insertApiViewToContainerAuto(view, jsonObject);
                }
            }
        }
        String pid = null;
        if (jsonObject.has("parentId")){
            pid = jsonObject.get("parentId").getAsString();
        }
        CoverViewWrapper parentCoverView = null;
        if (!TextUtils.isEmpty(pid)) {
            parentCoverView = ViewFinder.findCoverViewWrapper(fixedViewContainer, pid.hashCode());
        }
        if (parentCoverView != null) {
            return fixedViewContainer.insertApiViewToContainerAuto(view, jsonObject);
        }
        return coverViewNormalContainer.insertApiViewToContainerAuto(view, jsonObject);
    }


    public void updateApiViewUI(CoverViewWrapper coverViewWrapper, JsonObject jsonObject) {
        coverViewWrapper.updateViewStyle(jsonObject);
        if (coverViewWrapper.isFixed()) {
            fixedViewContainer.updateApiViewUI(coverViewWrapper, jsonObject);
        } else {
            coverViewNormalContainer.updateApiViewUI(coverViewWrapper, jsonObject);
        }
    }

    @Override
    protected void onAttachedToWindow() {
        super.onAttachedToWindow();
    }

    @Override
    protected void onDetachedFromWindow() {
        // backInterceptors.clear();
        super.onDetachedFromWindow();
    }

    @Override
    public void onWebScrollChange(int l, final int t, final int oldl, int oldt) {
        /** 暂时保留不启用，供native测失去焦点使用
         *         View view = findFocus();
         *         if (view instanceof InputComponent) {
         *             Log.e("inputDebug", "onWebScrollChange scroll offset  " + (t - oldl));
         *             Log.e("inputDebug", "clearFocus and hideSoftKeyboard");
         *             clearFocus();
         *             InputMethodUtil.hideSoftKeyboard((Activity) getContext());
         *         }
         */

        // Log.d("onWebScrollChange",Integer.toString(t));

        coverViewNormalContainer.scrollTo(l, t);
    }

    @Override
    public boolean onTouchEvent(MotionEvent event) {
        if (event.getAction() == MotionEvent.ACTION_DOWN) {
            boolean isHoldKeyboard = coverViewNormalContainer != null && coverViewNormalContainer.getHoldKeyboard();
            if (!isHoldKeyboard && !InputTouchUtil.isTouchInput(getContext(), event)) {
                InputMethodUtil.hideSoftInputFromWindow(getContext(), getWindowToken(), 0);
            }
        }
        return super.onTouchEvent(event);
    }

    public final void onKeyboardShow() {
        fixedViewContainer.onKeyboardShow();
        coverViewNormalContainer.onKeyboardShow();
    }

    public void init() {
        fixedViewContainer = new FixedViewContainer(getContext());
        coverViewNormalContainer = new CoverViewNormalContainer(getContext());
        FrameLayout.LayoutParams layoutParams = new LayoutParams(LayoutParams.MATCH_PARENT, LayoutParams.MATCH_PARENT);
        FrameLayout.LayoutParams layoutParams2 = new LayoutParams(LayoutParams.MATCH_PARENT, LayoutParams.MATCH_PARENT);
        addView(coverViewNormalContainer, layoutParams);
        addView(fixedViewContainer, layoutParams2);
    }


    @Override
    public boolean onSystemDialogClose(String cause) {
        if (fixedViewContainer.onSystemDialogClose(cause)) {
            return true;
        }
        return coverViewNormalContainer.onSystemDialogClose(cause);
    }

    /**
     * @return native view是否消耗back事件
     */
    @Override
    public final boolean onBackPressed() {
        if (fixedViewContainer.onBackPressed()) {
            return true;
        }
        return coverViewNormalContainer.onBackPressed();
    }

    @Override
    public final void onPagePaused(int cause) {
        fixedViewContainer.onPagePaused(cause);
        coverViewNormalContainer.onPagePaused(cause);
    }

    public final void onPageResume() {
        fixedViewContainer.onPageResume();
        coverViewNormalContainer.onPageResume();
    }

    @Override
    public boolean isPipMode() {
        return fixedViewContainer.isPipMode() || coverViewNormalContainer.isPipMode();
    }
}
