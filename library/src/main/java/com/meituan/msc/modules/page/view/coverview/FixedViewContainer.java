package com.meituan.msc.modules.page.view.coverview;

import android.content.Context;
import android.util.AttributeSet;
import android.view.View;
import android.view.ViewGroup;

import org.json.JSONObject;

/**
 * CoverViewContainer{包含业务View}>CoverViewWrapper>Page
 * Created by bunny<PERSON><PERSON> on 4/18/18.
 */

public class FixedViewContainer extends ViewBaseContainer {
    public FixedViewContainer(Context context) {
        super(context);
    }

    public FixedViewContainer(Context context, AttributeSet attrs) {
        super(context, attrs);
    }

    public FixedViewContainer(Context context, AttributeSet attrs, int defStyleAttr) {
        super(context, attrs, defStyleAttr);
    }

    @Override
    public void addView(View child, int index, ViewGroup.LayoutParams params) {
        super.addView(child, index, params);
    }

    @Override
    public final void updateViewLayout(View view, ViewGroup.LayoutParams params) {
        super.updateViewLayout(view, params);
    }

    @Override
    public void removeView(View view) {
        super.removeView(view);
    }


}
