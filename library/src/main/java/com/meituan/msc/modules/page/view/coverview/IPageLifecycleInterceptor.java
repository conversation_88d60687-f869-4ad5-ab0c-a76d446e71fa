package com.meituan.msc.modules.page.view.coverview;


public interface IPageLifecycleInterceptor {
    /**
     * 当前页面暂停 因为打开了新页面 PUSH
     */
    int TYPE_PAGE_PAUSE_CAUSE_NAVIGATE_PUSH = 0x1;
    /**
     * 当前页面暂停 因为打开了新页面 POP
     */
    int TYPE_PAGE_PAUSE_CAUSE_NAVIGATE_POP = 0x2;
    /**
     * 当前页面暂停 因为TAB切换
     */
    int TYPE_PAGE_PAUSE_CAUSE_SWITCH_TAB = 0x3;
    /**
     * 当前页面暂停 因为Activity进入了后台
     */
    int TYPE_PAGE_PAUSE_CAUSE_ENTER_BACKGROUND_OTHERS = 0x10;
    /**
     * 当前页面暂停 因为Activity进入了后台 由于返回关闭等触发的
     */
    int TYPE_PAGE_PAUSE_CAUSE_ENTER_BACKGROUND_POPPED = 0x11;
    /***
     * SDK内部暂停请求
     */
    int TYPE_PAGE_PAUSE_CAUSE_INTERNAL = 0x100;

    boolean onInterceptorPageBack();
    void onPagePause(int cause);
    void onPageResume();
}
