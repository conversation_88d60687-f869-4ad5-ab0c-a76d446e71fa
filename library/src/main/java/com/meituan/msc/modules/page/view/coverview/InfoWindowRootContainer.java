package com.meituan.msc.modules.page.view.coverview;

import android.content.Context;
import android.os.SystemClock;
import android.view.MotionEvent;
import android.view.View;

import com.google.gson.JsonObject;
import com.meituan.msc.common.executor.MSCExecutors;
import com.meituan.msc.modules.api.msi.components.coverview.CoverUpdateObserver;
import com.meituan.msc.modules.api.msi.components.coverview.ICoverViewUpdateRegistry;
import com.meituan.msc.modules.page.view.CoverViewWrapper;
import com.meituan.msc.modules.reporter.MSCLog;

/**
 * marker上展示Cover-view信息视图的容器
 * https://km.sankuai.com/page/992247234
 */
public class InfoWindowRootContainer extends CoverViewRootContainer {
    CoverUpdateObserver coverUpdateObserver;
    Runnable mRunnable;
    CoverUpdateObserver innerCoverUpdateObserver = new CoverUpdateObserver() {
        @Override
        public void onChange() {
            if (mRunnable == null && coverUpdateObserver != null) {
                mRunnable = new Runnable() {
                    @Override
                    public void run() {
                        long start = SystemClock.elapsedRealtime();
                        mRunnable = null;
                        if (coverUpdateObserver != null) {
                            coverUpdateObserver.onChange();
                        }
                        MSCLog.d("InfoWindowRootContainer", "onChange run", this, "cost", SystemClock.elapsedRealtime() - start);
                    }
                };
                // debounce for view update
                MSCExecutors.postDelayOnUiThread(mRunnable, 100);
            }
        }
    };

    //拦截分发给子视图的事件，避免子视图处理后，气泡不能响应事件
    private boolean mInterceptTouchEvent = false;

    //https://km.sankuai.com/collabpage/1537295971 允许响应气泡内子视图cover-view的事件
    //子视图响应后，地图的气泡可能不会响应事件了，业务自己决策使用
    private boolean mEnableCoverViewEvent = false;


    public InfoWindowRootContainer(Context context) {
        super(context);
    }


    public void setInterceptTouchEvent(boolean mInterceptTouchEvent) {
        this.mInterceptTouchEvent = mInterceptTouchEvent;
    }

    /**
     * 拦截点击事件
     *
     * @param ev
     * @return
     */
    @Override
    public boolean onInterceptTouchEvent(MotionEvent ev) {
        if (mInterceptTouchEvent && !mEnableCoverViewEvent) {
            if (ev.getAction() == MotionEvent.ACTION_DOWN) {
                return true;
            }
        }
        return super.onInterceptTouchEvent(ev);
    }

    @Override
    public boolean insertApiViewToContainerAuto(View view, JsonObject jsonObject) {
        if (view instanceof ICoverViewUpdateRegistry) {
            ((ICoverViewUpdateRegistry) view).addUpdateCoverViewObserver(coverUpdateObserver);
            //更新允许气泡内部子View响应点击事件标志状态，气泡视图组中，有任一子视图需要响应cover-view的事件，记录状态，状态不可取消
            mEnableCoverViewEvent = mEnableCoverViewEvent || ((ICoverViewUpdateRegistry) view).enableCoverViewEvent();
        }
        return super.insertApiViewToContainerAuto(view, jsonObject);
    }

    /**
     * 更新视图
     *
     * @param coverViewWrapper
     * @param jsonObject
     */
    @Override
    public void updateApiViewUI(CoverViewWrapper coverViewWrapper, JsonObject jsonObject) {
        super.updateApiViewUI(coverViewWrapper, jsonObject);
        setUpdateUpdateObserverInUpdateApiViewUI(coverViewWrapper);
    }

    public InfoWindowRootContainer setUpdateUpdateObserver(CoverUpdateObserver observer) {
        this.coverUpdateObserver = observer;
        return this;
    }

    /**
     * 更新视图设置监听者
     * 为了更新UI时通知地图刷新
     *
     * @param coverViewWrapper
     */
    private void setUpdateUpdateObserverInUpdateApiViewUI(CoverViewWrapper coverViewWrapper) {
        ICoverViewUpdateRegistry coverViewUpdateRegistry = coverViewWrapper.getViewInInfoWindowWrapper(ICoverViewUpdateRegistry.class);
        if (coverViewUpdateRegistry != null) {
            coverViewUpdateRegistry.addUpdateCoverViewObserver(innerCoverUpdateObserver);
        }
    }
}
