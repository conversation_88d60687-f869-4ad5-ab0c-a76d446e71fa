package com.meituan.msc.modules.page.view.coverview;

import android.app.Activity;
import android.content.Context;
import android.graphics.Rect;
import android.view.MotionEvent;
import android.view.View;
import android.widget.EditText;

/**
 * 统一处理分发touch时，edittext、外层对于down的处理。
 */
public class InputTouchUtil {

    private InputTouchUtil() {

    }

    /**
     * 当前event是否发生在edittext区域
     *
     * @param context
     * @param event
     * @return
     */
    public static boolean isTouchInput(Context context, MotionEvent event) {
        if (!(context instanceof Activity)) {
            return false;
        }
        View view = ((Activity) context).getCurrentFocus();
        if (view instanceof EditText) {
            Rect outRect = new Rect();
            view.getGlobalVisibleRect(outRect);
            return outRect.contains((int) event.getRawX(), (int) event.getRawY());
        }
        return false;
    }
}
