package com.meituan.msc.modules.page.view.coverview;

import android.content.Context;
import android.text.TextUtils;
import android.util.AttributeSet;
import android.view.View;
import android.view.ViewGroup;
import android.widget.AbsoluteLayout;
import android.widget.FrameLayout;

import com.google.gson.JsonObject;
import com.meituan.msc.common.utils.DisplayUtil;
import com.meituan.msc.modules.page.embeddedwidget.MPConstant;
import com.meituan.msc.modules.page.view.CoverViewWrapper;
import com.meituan.msc.modules.page.view.ViewFinder;
import com.meituan.msc.modules.reporter.MSCLog;
import com.meituan.msi.view.INativeLifecycleInterceptor;

import java.util.LinkedList;
import java.util.List;

/**
 * CoverViewContainer{包含业务View}>CoverViewWrapper>Page
 * Created by bunnyblue on 4/18/18.
 */

public class ViewBaseContainer extends AbsoluteLayout implements INativeLifecycleInterceptor {
    private final String TAG = "ViewBaseContainer@" + Integer.toHexString(hashCode());
    private final List<INativeLifecycleInterceptor> backInterceptors = new LinkedList<>();
    private boolean holdKeyboard;

    public ViewBaseContainer(Context context) {
        super(context);

    }

    public ViewBaseContainer(Context context, AttributeSet attrs) {
        super(context, attrs);

    }

    public ViewBaseContainer(Context context, AttributeSet attrs, int defStyleAttr) {
        super(context, attrs, defStyleAttr);

    }


    @Override
    public void addView(View child, int index, ViewGroup.LayoutParams params) {
        if (child instanceof CoverViewWrapper) {
            super.addView(child, index, params);
        } else {
            super.addView(child, index, params);
            MSCLog.e("view not support");
        }
    }

    @Override
    public void removeView(View view) {
        if (view instanceof CoverViewWrapper) {
            View contentView = ((CoverViewWrapper) view).getContent();
            if (contentView instanceof INativeLifecycleInterceptor) {
                synchronized (backInterceptors) {
                    backInterceptors.remove(contentView);
                }
            }
        }
        super.removeView(view);
    }

    private void addNativeLifecycleInterceptor(View view) {
        if (view instanceof INativeLifecycleInterceptor) {
            synchronized (backInterceptors) {
                backInterceptors.add((INativeLifecycleInterceptor) view);
            }
        }
    }

    public boolean insertApiViewToContainerAuto(View view, JsonObject jsonObject) {
        CoverViewWrapper coverViewWrapper = new CoverViewWrapper(getContext(), view);
        if (this instanceof FixedViewContainer) {
            coverViewWrapper.setFixed(true);
        }
        //添加NativeLifecycleInterceptor, 拦截处理UI事件
        addNativeLifecycleInterceptor(view);

        String pid = null;
        if (jsonObject.has("parentId")) {
            pid = jsonObject.get("parentId").getAsString();
        }
        if (!TextUtils.isEmpty(pid)) {
            CoverViewWrapper coverViewWrapperParent = ViewFinder.findCoverViewWrapper(this, pid.hashCode());
            if (coverViewWrapperParent == null) {
                MSCLog.e(TAG, String.format("view(id: %s，pageId: %s)insert failed for parentId %s not found ",
                        jsonObject.get("viewId").getAsString(), jsonObject.get("pageId").getAsString(), pid));
                return false;
            }
            coverViewWrapperParent.addView(coverViewWrapper);
        } else {
            addView(coverViewWrapper);
        }
        updateApiViewUI(coverViewWrapper, jsonObject);
        return true;
    }

    /**
     * 更新view位置 大小等基础布局
     *
     * @param view
     * @param jsonObject
     */
    public void updateApiViewUI(View view, JsonObject jsonObject) {
        CoverViewWrapper coverViewWrapper = (CoverViewWrapper) view;
        boolean isEmbedView = false;
        if (jsonObject.has(MPConstant.MP_VIEW_EMBED_RENDER)) {
            isEmbedView = jsonObject.get(MPConstant.MP_VIEW_EMBED_RENDER).getAsBoolean();
        }

        //同层渲染不做实际布局操作,只有定位 没有宽高
        float[] locs = DisplayUtil.covertAnchor(jsonObject);
        if (locs == null) {
            return;
        }
        if (coverViewWrapper.getParent() == this || coverViewWrapper.getParent() == null) {//只有是一级空间的时候使用绝对布局
            LayoutParams layoutParams = (LayoutParams) coverViewWrapper.getLayoutParams();//
            if (layoutParams == null) {
                layoutParams = new LayoutParams(0, 0, 0, 0);
            }

            JsonObject positionObject = null;
            if (jsonObject.has("position")) {
                positionObject = jsonObject.get("position").getAsJsonObject();
            }
            if (positionObject != null && positionObject.has("left")) {
                layoutParams.x = (int) locs[0];
            }
            if (positionObject != null && positionObject.has("top")) {
                layoutParams.y = (int) locs[1];
            }

            if (!isEmbedView) {
                layoutParams.width = (int) locs[2];
                layoutParams.height = (int) locs[3];
            }
            coverViewWrapper.setLayoutParams(
                    layoutParams
            );
        } else {
            FrameLayout.LayoutParams layoutParams = new FrameLayout.LayoutParams((int) locs[2], (int) locs[3]);//new ViewGroup.LayoutParams((int) locs[2], (int) locs[3]);
//            layoutParams.height = (int) locs[3];
            layoutParams.leftMargin = (int) locs[0];
            layoutParams.topMargin = (int) locs[1];
            if (!isEmbedView) {
                layoutParams.width = (int) locs[2];
                layoutParams.height = (int) locs[3];
            }
            coverViewWrapper.setLayoutParams(
                    layoutParams
            );
        }
        requestLayout();
    }


    @Override
    protected void onAttachedToWindow() {
        super.onAttachedToWindow();
    }

    @Override
    protected void onDetachedFromWindow() {
        backInterceptors.clear();
        super.onDetachedFromWindow();
    }

    public final void onKeyboardShow() {
        // Native渲染在Container中findFocus找不到组件
        View focusView = getRootView().findFocus();
        if (focusView instanceof com.meituan.msi.api.component.input.InputComponent) {
            com.meituan.msi.api.component.input.InputComponent inputComponent = (com.meituan.msi.api.component.input.InputComponent) focusView;
            if (inputComponent.hasFocus()) {
                holdKeyboard = inputComponent.isHoldKeyboard();
                MSCLog.d(TAG, "[onKeyboardShow] holdKeyboard:", holdKeyboard,
                        ", InputComponent@" + Integer.toHexString(inputComponent.hashCode()));
            }
        }
    }


    @Override
    public boolean onSystemDialogClose(String cause) {
        synchronized (backInterceptors) {
            for (INativeLifecycleInterceptor interceptor : backInterceptors) {
                if (interceptor.onSystemDialogClose(cause)) {
                    return true;
                }
            }
        }

        return false;
    }

    /**
     * @return native view是否消耗back事件
     */
    @Override
    public final boolean onBackPressed() {
//        synchronized (backInterceptors) {
//            for (INativeLifecycleInterceptor interceptor : backInterceptors) {
//                if (interceptor.onBackPressed()) {
//                    return true;
//                }
//            }
//        }

        return false;
    }

    @Override
    public final void onPagePaused(int cause) {
//        synchronized (backInterceptors) {
//            for (INativeLifecycleInterceptor interceptor : backInterceptors) {
//                interceptor.onPagePaused(cause);
//            }
//        }

    }

    @Override
    public final void onPageResume() {
//        synchronized (backInterceptors) {
//            for (INativeLifecycleInterceptor interceptor : backInterceptors) {
//                interceptor.onPageResume();
//            }
//        }
    }

    @Override
    public boolean isPipMode() {
        synchronized (backInterceptors) {
            for (INativeLifecycleInterceptor interceptor : backInterceptors) {
                if (interceptor.isPipMode()) {
                    return true;
                }
            }
        }

        return false;
    }

    public boolean getHoldKeyboard() {
        View focusView = getRootView().findFocus();
        if (focusView instanceof com.meituan.msi.api.component.input.InputComponent) {
            com.meituan.msi.api.component.input.InputComponent inputComponent = (com.meituan.msi.api.component.input.InputComponent) focusView;
            if (inputComponent.hasFocus()) {
                holdKeyboard = inputComponent.isHoldKeyboard();
                MSCLog.d(TAG, "[getHoldKeyboard] holdKeyboard:", holdKeyboard,
                        ", InputComponent@" + Integer.toHexString(inputComponent.hashCode()));
            }
        }
        return holdKeyboard;
    }
}
