package com.meituan.msc.modules.page.view.reload;

import java.util.HashMap;

/**
 * 页面重启功能的接口
 */
public interface IReloadProcessor {

    /**
     * 调用该方法，重启页面：
     * 1、如果当前页面在展示，走重启逻辑
     * 2、如果当前页面没有在展示，先标记下来，后续展示的时候再reload
     */
    void reload(HashMap<String, Object> map);

    /**
     * 调用该方法，重启页面：
     * 如果外部被标识为应该重启，才会重启。对应 {@IReloadProcessor#reload(HashMap<String, Object> map}
     * case 2情形
     */
    HashMap<String, Object> reloadIfStateSet();

}
