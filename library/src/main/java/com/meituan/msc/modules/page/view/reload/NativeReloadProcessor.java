package com.meituan.msc.modules.page.view.reload;

import com.meituan.msc.modules.page.view.PageViewWrapper;

import java.util.HashMap;
import java.util.Map;

public class NativeReloadProcessor extends ReloadProcessor {

    public NativeReloadProcessor(PageViewWrapper pageViewWrapper) {
        super(pageViewWrapper);
    }

    @Override
    protected void notifyReload(Map<String, Object> map) {

    }

    @Override
    protected void reportReloadHandler() {

    }

    @Override
    protected void reportReloadPage(HashMap<String, Object> map) {

    }

}
