package com.meituan.msc.modules.page.view.reload;

import android.app.Activity;

import com.meituan.msc.modules.engine.IRendererManager;
import com.meituan.msc.modules.engine.MSCHornRollbackConfig;
import com.meituan.msc.modules.engine.MSCRuntime;
import com.meituan.msc.modules.page.Page;
import com.meituan.msc.modules.page.RouteReporter;
import com.meituan.msc.modules.page.render.BaseRenderer;
import com.meituan.msc.modules.page.view.PageViewWrapper;
import com.meituan.msc.modules.reporter.MSCLog;

import java.util.HashMap;
import java.util.Map;

public abstract class ReloadProcessor implements IReloadProcessor {

    protected PageViewWrapper mPageViewWrapper;
    protected BaseRenderer mRenderer;
    protected MSCRuntime mRuntime;
    protected HashMap<String, Object> reloadState;
    protected static final String TAG = "ReloadProcessor";

    public void init() {
        reloadState = null;
    }

    public ReloadProcessor(PageViewWrapper pageViewWrapper) {
        mPageViewWrapper = pageViewWrapper;
        mRenderer = mPageViewWrapper.getRenderer();
        mRuntime = mPageViewWrapper.getRuntime();
    }

    protected boolean hasCallReloadBefore() {
        return this.reloadState != null;
    }

    protected abstract void notifyReload(Map<String, Object> map);
    protected abstract void reportReloadHandler();
    protected abstract void reportReloadPage(HashMap<String, Object> map);

    public void reload(HashMap<String, Object> map) {
        // 如果当前webview没有在展示，先标记下来，后续展示的时候再reload webview
        if (hasCallReloadBefore()) {
            MSCLog.i(TAG, "already received render process gone, duplicated callback");
            return;
        }
        mPageViewWrapper.checkNeedFallbackToSystemWebView();
        reloadState = map;
        mRenderer.setNeedReload(true);

        reloadState.put("reloadType", isShow() ? "immediate" : "onNextShow");
        notifyReload(map);

        MSCLog.i(TAG, "ReloadProcessor#reload", reloadState);
        // 如果当前webview在展示 且运行时未销毁，直接reload
        if (isShow() && !mRuntime.isDestroyed) {
            reloadIfStateSet();
        }

        reportReloadHandler();
    }

    /**
     * 发生RenderProcessGone时reload，在前台发生立即调用，否则等到下次onPageResume时调用
     *  onPageResume
     * @return
     */
    public HashMap<String, Object> reloadIfStateSet() {
        long routeTime = System.currentTimeMillis();
        MSCLog.i(TAG, "ReloadProcessor#reloadIfStateSet", reloadState, this);
        // 展示前的检查，如果已经renderProcessGone, 需要销毁appPage并重建
        HashMap<String, Object> map = null;
        if (this.reloadState != null) {
            map = new HashMap<>();
            map.putAll(this.reloadState);
        }

        if (hasCallReloadBefore()) {
            MSCLog.i(TAG, "ReloadProcessor#reloadIfStateSet, do release logical start.", reloadState, this);
            // 重建之前设置一下reloadId, 重建的时候会创建新的AppPage, 有新的ViewId, 旧的ViewId则保存在reloadViewId字段中，在发送onAppRoute时候发给前端
            mPageViewWrapper.setReloadViewId(mRenderer.getViewId());

            mPageViewWrapper.setProcessGone();

            // 释放当前挂载的appPage
            releaseAppPage();

            if (!MSCHornRollbackConfig.readConfig().rollbackReloadReportFix) {
                Activity activity = mRenderer.getContainerDelegate() == null ? null : mRenderer.getContainerDelegate().getActivity();
                RouteReporter.create(mRuntime).reportRouteStart(activity, "reload", mPageViewWrapper.getViewId(),
                        mPageViewWrapper.getUrl(), "", mPageViewWrapper.isWidget());
            }

            // 产生一个新的render，并挂载到pageViewWrapper上
            // 注意: 这里的 isFirstPage 用于埋点上报时确定是否是运行时创建或者复用后的第一个页面，reload操作肯定不会是第一个页面，所以这里传 false
            Page parentPage = mPageViewWrapper.getParentPage();
            parentPage.setRouteTime(routeTime);
            mPageViewWrapper.getParentPage().resetPageStartTime();
            // 这里面会将cover层移除旧的，换新的。
            mPageViewWrapper.setupAppPage(mPageViewWrapper.getParentPage().retainRenderer(mPageViewWrapper.getContentUrl(), false,
                    mPageViewWrapper.getParentPage().getBizTagsForPage(mPageViewWrapper.getUrl()), false));
            reportReloadPage(map);

            mPageViewWrapper.ensureShow();
            String reloadPath = mPageViewWrapper.getContentUrl();
            mPageViewWrapper.setContentUrl(null); //清空此变量，否则webview无法重新加载
            mPageViewWrapper.getParentPage().setHasLoaded(false);
            mPageViewWrapper.getParentPage().reloadByRenderProcessGone(mPageViewWrapper.getRenderer(), reloadPath, routeTime);

            MSCLog.i(TAG, "ReloadProcessor#reloadIfStateSet, do release logical end.", reloadState, this);
        }

        return map;
    }

    // 当发生RenderProcessGone, 需要Reload时调用，销毁当前AppPage
    private void releaseAppPage() {

        // 重置一些状态值，reload的时候会对这些值做判断，需要设回初始状态，否则reload执行不下去
        //this.contentUrl = null;
        mPageViewWrapper.ensureHide();

        // 销毁当前挂载的AppPage
        mRuntime.getModule(IRendererManager.class).releaseRenderer(mRenderer);

        // 销毁所有缓存的AppPage，以确保reload后更换的AppPage不会是发生了renderProcessGone
        mRuntime.getModule(IRendererManager.class).clearAllCachedRenderer();
    }

    protected boolean isShow() {
        return mPageViewWrapper.isShow();
    }
}
