package com.meituan.msc.modules.page.view.reload;

import com.meituan.msc.modules.page.render.BaseRenderer;
import com.meituan.msc.modules.page.view.PageViewWrapper;

public class ReloadProcessorFactory {

    public static ReloadProcessor createReloadProcessor(PageViewWrapper pageViewWrapper) {

        if (pageViewWrapper == null) {
            return null;
        }

        BaseRenderer render = pageViewWrapper.getRenderer();

        if (render.isNativeRender()) {
            return new NativeReloadProcessor(pageViewWrapper);
        } else {
            return new WebviewReloadProcessor(pageViewWrapper);
        }
    }
}
