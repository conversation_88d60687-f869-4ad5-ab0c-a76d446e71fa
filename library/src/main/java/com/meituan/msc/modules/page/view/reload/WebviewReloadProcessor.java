package com.meituan.msc.modules.page.view.reload;

import com.meituan.msc.common.lib.IWebViewRenderProcessGone;
import com.meituan.msc.common.lib.SDKType;
import com.meituan.msc.common.utils.HashMapHelper;
import com.meituan.msc.modules.engine.MSCHornRollbackConfig;
import com.meituan.msc.modules.page.view.PageViewWrapper;
import com.meituan.msc.modules.reporter.CommonTags;
import com.meituan.msc.modules.reporter.MSCLog;
import com.meituan.msc.modules.reporter.ReporterFields;
import com.sankuai.meituan.serviceloader.ServiceLoader;

import java.util.HashMap;
import java.util.List;
import java.util.Map;

public class WebviewReloadProcessor extends ReloadProcessor {

    public WebviewReloadProcessor(PageViewWrapper pageViewWrapper) {
        super(pageViewWrapper);
    }

    protected void notifyReload(Map<String, Object> map) {
        if (MSCHornRollbackConfig.get().getConfig().disableNotifyRenderProcessGone) {
            MSCLog.i(TAG, "disableNotifyRenderProcessGone");
            return;
        }
        List<IWebViewRenderProcessGone> callbacks = ServiceLoader.load(IWebViewRenderProcessGone.class, mRuntime.getAppId());
        if (callbacks == null || callbacks.isEmpty()) {
            MSCLog.i(TAG, "notifyRenderProcessGone callbacks empty");
            return;
        }

        for (IWebViewRenderProcessGone callback : callbacks) {
            if (callback == null) {
                MSCLog.i(TAG, "notifyRenderProcessGone callback is null");
                continue;
            }

            Map<String, Object> finalMap = new HashMap<>();
            if (mRenderer.getReporter() != null) {
                CommonTags mscCommonTags = mRenderer.getReporter().getMSCCommonTags();
                if (mscCommonTags != null) {
                    finalMap = HashMapHelper.merge(mscCommonTags.generateCommonTags(), map);
                }
            }
            MSCLog.i(TAG, "notifyRenderProcessGone", finalMap);
            callback.onRenderProcessGone(SDKType.MSC, finalMap);
        }
    }

    @Override
    protected void reportReloadHandler() {
        if (mRenderer != null) {
            mRenderer.getReporter().once(ReporterFields.REPORT_RENDER_PROCESS_GONE_HANDLED).tags(reloadState).sendRealTime();
        }
    }

    @Override
    protected void reportReloadPage(HashMap<String, Object> map) {
        if (mRenderer != null) {
            mRenderer.getReporter().once(ReporterFields.REPORT_RENDER_PROCESS_GONE_RELOAD_PAGE).tags(map).sendRealTime();
        }
    }
}

