package com.meituan.msc.modules.page.view.tab;

import android.content.Context;
import android.content.res.Configuration;
import android.support.annotation.Nullable;
import android.text.TextUtils;
import android.util.DisplayMetrics;
import android.view.View;
import android.view.ViewGroup;
import android.widget.LinearLayout;

import com.meituan.msc.common.utils.ColorUtil;
import com.meituan.msc.common.utils.DisplayUtil;
import com.meituan.msc.modules.engine.MSCHornRollbackConfig;
import com.meituan.msc.modules.update.MSCAppModule;

import java.util.List;

/**
 * 自定义Tab栏视图
 */
public class TabBar extends LinearLayout {

    private LinearLayout mTabItemLayout;
    private OnSwitchTabListener mListener;
    private View border;
    private int topBarHeight;

    public TabBar(Context context, MSCAppModule mscAppModule) {
        super(context);
        init(context, mscAppModule);
    }

    private void init(Context context, MSCAppModule mscAppModule) {
        setOrientation(VERTICAL);
        boolean isTop = mscAppModule.isTopTabBar();
        topBarHeight = DisplayUtil.fromDPToPix(isTop ? 30 : 50);
        String borderColor = mscAppModule.getTabBarBorderColor();
        String backgroundColor = mscAppModule.getTabBarBackgroundColor();
        List<TabItemInfo> list = mscAppModule.getTabItemList();
        if (list == null || list.isEmpty()) {
            return;
        }
        setBackgroundColor(ColorUtil.parseColor(backgroundColor));

        //添加border
        border = new View(context);
        border.setBackgroundColor(ColorUtil.parseColor(borderColor));
        addView(border, new LayoutParams(LayoutParams.MATCH_PARENT, 1));

        //添加item
        mTabItemLayout = new LinearLayout(context);
        //取消基线对齐，解决购物车0->1跳变问题
        mTabItemLayout.setBaselineAligned(false);
        mTabItemLayout.setOrientation(HORIZONTAL);
        DisplayMetrics dm = getResources().getDisplayMetrics();
        int itemWidth = dm.widthPixels / list.size();
        int lrPadding = (dm.widthPixels % list.size()) / 2;
        mTabItemLayout.setPadding(lrPadding, 0, lrPadding, 0);
        addView(mTabItemLayout, new LayoutParams(LayoutParams.MATCH_PARENT,
                LayoutParams.WRAP_CONTENT));

        for (int i = 0; i < list.size(); i++) {
            final TabItemInfo info = list.get(i);
            TabItemView itemView = new TabItemView(context, mscAppModule);
            itemView.setInfo(info);
            itemView.setTop(isTop);
            itemView.setTag(i);
            itemView.setOnClickListener(new OnClickListener() {

                @Override
                public void onClick(View v) {
                    long routeTime = System.currentTimeMillis();
                    if (mListener != null && mListener.tabClickable()) {
                        return;
                    }

                    TabItemView view = (TabItemView) v;
                    if (mListener != null) {
                        String url = view.getPagePath();
                        String index = String.valueOf(v.getTag());
                        // 前端要求onAppRoute之后发送onTabItemTap，请保证switchTab在onTabItemTap前调用
                        if (!view.isSelected()) {
                            mListener.switchTab(url, routeTime, index, info == null ? "" : info.text);
                        } else if (MSCHornRollbackConfig.enableFixOnTabItemTabAndSwitchTab()) {
                            mListener.onTabItemTap(url, index, info == null ? "" : info.text);
                        }
                        if (!MSCHornRollbackConfig.enableFixOnTabItemTabAndSwitchTab()) {
                            mListener.onTabItemTap(url, index, info == null ? "" : info.text);
                        }
                    }
                }
            });
            mTabItemLayout.addView(itemView, new LayoutParams(itemWidth, topBarHeight));
        }

        if (isTop) {
            //添加底部border
            border = new View(context);
            border.setBackgroundColor(ColorUtil.parseColor(borderColor));
            addView(border, new LayoutParams(LayoutParams.MATCH_PARENT, 1));
        }
    }

    @Nullable
    public TabItemView getTabItem(int index) {
        if (index < 0 || mTabItemLayout.getChildCount() <= index) {
            return null;
        }
        return (TabItemView) mTabItemLayout.getChildAt(index);
    }

    public int getTabItemCount() {
        return mTabItemLayout.getChildCount();
    }

    public int getTopBarHeight() {
        return topBarHeight;
    }

    public void setBorderColor(int color) {
        border.setBackgroundColor(color);
    }

    /**
     * 切换Tab item
     *
     * @param url Tab item对应的页面路径
     */
    public void switchTab(String url) {
        int count = mTabItemLayout.getChildCount();
        for (int i = 0; i < count; i++) {
            TabItemView itemView = (TabItemView) mTabItemLayout.getChildAt(i);
            if (TextUtils.equals(url, itemView.getPagePath())) {
                itemView.setSelected(true);
            } else {
                itemView.setSelected(false);
            }
        }
    }

    public void setOnSwitchTabListener(OnSwitchTabListener listener) {
        mListener = listener;
    }

    @Override
    protected void onConfigurationChanged(Configuration newConfig) {
        super.onConfigurationChanged(newConfig);
        if (mTabItemLayout != null) {
            int size = mTabItemLayout.getChildCount();
            DisplayMetrics dm = getResources().getDisplayMetrics();
            int itemWidth = dm.widthPixels / size;
            int lrPadding = (dm.widthPixels % size) / 2;
            mTabItemLayout.setPadding(lrPadding, 0, lrPadding, 0);
            for (int i = 0; i < size; i++) {
                View view = mTabItemLayout.getChildAt(i);
                ViewGroup.LayoutParams layoutParams = view.getLayoutParams();
                layoutParams.width = itemWidth;
                layoutParams.height = topBarHeight;
//                view.setLayoutParams(layoutParams);
            }
        }
    }

    /**
     * Tab切换监听
     */
    public interface OnSwitchTabListener {

        /**
         * 切换tab
         *
         * @param url       tab item的页面路径
         * @param routeTime
         */
        void switchTab(String url, long routeTime, String index, String text);

        /**
         * 点击tab
         *
         * @param pagePath tab item的页面路径，@param index tab item的序号（从0开始），@param text tab item的文字
         */
        void onTabItemTap(String pagePath, String index, String text);

        /**
         * https://ones.sankuai.com/ones/product/6246/workItem/defect/detail/3993253
         *
         * @return
         */
        boolean tabClickable();

    }

}
