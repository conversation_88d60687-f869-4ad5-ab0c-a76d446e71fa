package com.meituan.msc.modules.page.view.tab;

/**
 * https://km.sankuai.com/page/1376814290
 */
public class TabItemInfo {

    //tab 上的文字默认颜色
    public String color;

    //tab 上的文字选中时的颜色
    public String selectedColor;

    //图片路径，当 postion 为 top 时，此参数无效
    public String iconPath;

    //选中时的图片路径，当 postion 为 top 时，此参数无效
    public String selectedIconPath;

    //tab 上按钮文字
    public String text;

    //页面路径，必须在 pages 中先定义
    public String pagePath;

    //如果 isLargerIcon 为 true，则该 tab 项不展示文字，且放大图标。否则展示文字，图标大小正常。默认为 false。
    public boolean isLargerIcon = false;

}
