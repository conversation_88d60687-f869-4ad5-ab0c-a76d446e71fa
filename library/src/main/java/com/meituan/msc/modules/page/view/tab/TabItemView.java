package com.meituan.msc.modules.page.view.tab;

import android.content.Context;
import android.graphics.Paint;
import android.text.TextUtils;
import android.util.TypedValue;
import android.view.Gravity;
import android.view.View;
import android.widget.ImageView;
import android.widget.RelativeLayout;
import android.widget.TextView;

import com.meituan.msc.common.utils.ColorUtil;
import com.meituan.msc.common.utils.FileUtil;
import com.meituan.msc.common.utils.ScreenUtil;
import com.meituan.msc.lib.R;
import com.meituan.msc.lib.interfaces.IFileModule;
import com.meituan.msc.modules.api.msi.tabbar.BadgeStyle;
import com.meituan.msc.modules.engine.MSCHornRollbackConfig;
import com.meituan.msc.modules.page.view.CompatGradientDrawable;
import com.meituan.msc.modules.update.MSCAppModule;
import com.squareup.picasso.RequestCreator;


/**
 * TabView 逻辑目前较分散，数据解析/更新/校验逻辑均分散在多个地方存在不同的逻辑，后续可以收拢一下；
 * com.meituan.msc.modules.update.AppConfigModule / com.meituan.msc.modules.api.msi.tabbar.TabBarApi
 *
 * Tab项视图
 */
public class TabItemView extends RelativeLayout {

    private ImageView mCurrentIcon, mLargeIcon, mSmallIcon;
    private TextView mName, mBadge;
    private TabItemInfo mInfo;
    private MSCAppModule mscAppModule;
    private View mRedDot;
    private final int SMALL_ICON_SIZE = 28, LARGE_ICON_SIZE = 41;

    public TabItemView(Context context, MSCAppModule mscAppModule) {
        super(context);
        init(context);
        this.mscAppModule = mscAppModule;
    }

    private ImageView addIcon(int size) {
        RelativeLayout.LayoutParams params = new LayoutParams(size, size);
        params.topMargin = ScreenUtil.dp2px(3);
        ImageView icon  = new ImageView(getContext());
        params.addRule(CENTER_HORIZONTAL, TRUE);
        icon.setId(View.generateViewId());
        icon.setScaleType(ImageView.ScaleType.CENTER_INSIDE);
        addView(icon, params);
        return icon;
    }

    private void init(Context context) {
        mLargeIcon = addIcon(ScreenUtil.dp2px(LARGE_ICON_SIZE));
        mSmallIcon = addIcon(ScreenUtil.dp2px(SMALL_ICON_SIZE));

        refreshIcon(null);

        LayoutParams params = new LayoutParams(LayoutParams.WRAP_CONTENT, LayoutParams.WRAP_CONTENT);
        mName = new TextView(context);
        params.addRule(CENTER_HORIZONTAL, TRUE);
        params.addRule(BELOW, mCurrentIcon.getId());
        mName.setTextSize(12);
        addView(mName, params);

    }

    public void setInfo(TabItemInfo info) {
        mInfo = info;
        refreshIfIconSizeChange(info);
    }

    private boolean isIconSizeChange(TabItemInfo info) {
        /**
         * 需求：
         * isLargerIcon 为 true  --- tab隐藏文字，放大图标。
         * isLargerIcon 为 false --- tab展示文字，缩小图标。
         * 当从大图标改为小图标，或者小图标改为大图标，则需要刷新UI
         */
        // 已经设置为大图标
        if (info.isLargerIcon && mCurrentIcon == mLargeIcon) {
            return false;
        }
        // 已经设置为小图标
        if (!info.isLargerIcon && mCurrentIcon == mSmallIcon) {
            return false;
        }
        return true;
    }

    private void refreshIcon(TabItemInfo info) {
        if(info == null || !info.isLargerIcon) {
            mSmallIcon.setVisibility(VISIBLE);
            mLargeIcon.setVisibility(GONE);
            mCurrentIcon = mSmallIcon;
        } else {
            mSmallIcon.setVisibility(GONE);
            mLargeIcon.setVisibility(VISIBLE);
            mCurrentIcon = mLargeIcon;
        }
    }

    private void refreshAppName(TabItemInfo info) {
        if (info.isLargerIcon) {
            mName.setVisibility(View.GONE);
        } else {
            mName.setVisibility(VISIBLE);
        }
    }

    private void refreshRedDot(TabItemInfo info) {
        // 要展示的icon不一样了，要重新设置下相对位置。
        if (mRedDot != null) {
            RelativeLayout.LayoutParams redDotParams = (LayoutParams) mRedDot.getLayoutParams();
            redDotParams.addRule(RelativeLayout.ALIGN_LEFT, mCurrentIcon.getId());
            redDotParams.leftMargin = ScreenUtil.dp2px(30);
        }
    }

    private void refreshBadge(TabItemInfo info) {
        if (mBadge != null) {
            RelativeLayout.LayoutParams badgeParams = (LayoutParams) mBadge.getLayoutParams();
            badgeParams.addRule(RelativeLayout.ALIGN_LEFT, mCurrentIcon.getId());
            badgeParams.leftMargin = ScreenUtil.dp2px(20);
        }
    }

    /**
     *  加了快驴icon可以变大变小的需求。
     *  如果icon变大变小，其他元素，也要做一些变化。
     */
    private void refreshIfIconSizeChange(TabItemInfo info) {
        if (!isIconSizeChange(info)) {
            return;
        }
        refreshIcon(info);
        refreshAppName(info);
        refreshRedDot(info);
        refreshBadge(info);
    }




    public TabItemInfo getInfo() {
        return mInfo;
    }

    /**
     * 动态设置 tabBar 某一项的内容
     *
     * @param text
     * @param iconPath
     * @param selectedIconPath
     */
    public void setTabBarItem(String text, String iconPath, String selectedIconPath, boolean isLargerIcon) {
        if (!TextUtils.isEmpty(text)) {
            mInfo.text = text;
        }
        if (!TextUtils.isEmpty(iconPath)) {
            mInfo.iconPath = iconPath;
        }
        if (!TextUtils.isEmpty(selectedIconPath)) {
            mInfo.selectedIconPath = selectedIconPath;
        }
        mInfo.isLargerIcon = isLargerIcon;
        refreshIfIconSizeChange(mInfo);
    }


    public void setTabBarItemStyle(String color,String selectedColor){
        mInfo.color = color;
        mInfo.selectedColor = selectedColor;
    }

    public String getPagePath() {
        return mInfo != null ? mInfo.pagePath : "";
    }

    public void setTop(boolean isTop) {
        int textSize;
//        int padding;
        if (isTop) {
//            padding = getResources().getDimensionPixelSize(R.dimen.tab_bar_padding_l);
            textSize = getResources().getDimensionPixelSize(R.dimen.tab_bar_text_size_l);
            mCurrentIcon.setVisibility(GONE);
        } else {
//            padding = getResources().getDimensionPixelSize(R.dimen.tab_bar_padding_s);
            textSize = getResources().getDimensionPixelSize(R.dimen.tab_bar_text_size_s);
            mCurrentIcon.setVisibility(VISIBLE);
        }
//        setPadding(0, padding, 0, padding);
        if (mName.getVisibility() != VISIBLE) {
            mName.setVisibility(VISIBLE);
        }
        mName.setTextSize(TypedValue.COMPLEX_UNIT_PX, textSize);
    }

    /**
     * 显示 tabBar 某一项的右上角的红点
     */
    public void showTabBarRedDot() {
        removeTabBarBadge();
        if (mRedDot == null) {
            RelativeLayout.LayoutParams params = new LayoutParams(ScreenUtil.dp2px(8), ScreenUtil.dp2px(8));
            params.addRule(RelativeLayout.ALIGN_LEFT, mCurrentIcon.getId());
            params.leftMargin = ScreenUtil.dp2px(30);
            mRedDot = new View(getContext());
            mRedDot.setBackground(getResources().getDrawable(R.drawable.msc_red_dot));
            addView(mRedDot, params);
        }
        mRedDot.setVisibility(View.VISIBLE);
    }

    /**
     * 隐藏 tabBar 某一项的右上角的红点
     */
    public void hideTabBarRedDot() {
        if (mRedDot == null) {
            return;
        }
        if (mRedDot.getVisibility() == View.VISIBLE) {
            mRedDot.setVisibility(GONE);
        }
    }

    /**
     * 为 tabBar 某一项的右上角添加文本
     *
     * @param text
     * @param badgeStyle
     */
    public void setTabBarBadge(String text, BadgeStyle badgeStyle) {
        hideTabBarRedDot();

        if (mBadge == null) {
            mBadge = new TextView(getContext());
            mBadge.setMaxLines(1);
            mBadge.setSingleLine(true);
            addView(mBadge);
        }

        int badgeHeight = badgeStyle.height;
        int badgeWidth = badgeStyle.width;
        int borderSize = badgeStyle.borderSize;
        int borderRadius = badgeStyle.borderRadius;

        /**
         * 单位修改背景：iOS的单位是pt、鸿蒙的单位是vp，pt、vp和安卓的dp类似，为了和另外两端保持一致，安卓也从px修改为dp
         */
        if (MSCHornRollbackConfig.enableTabBarBadgeUnitChange()) {
            if (badgeHeight != LayoutParams.WRAP_CONTENT) {
                badgeHeight = ScreenUtil.dp2px(badgeStyle.height);
            }
            if (badgeWidth != LayoutParams.WRAP_CONTENT) {
                badgeWidth = ScreenUtil.dp2px(badgeStyle.width);
            }
            borderSize = ScreenUtil.dp2px(borderSize);
            borderRadius = ScreenUtil.dp2px(borderRadius);
        }

        final int BADGE_HEIGHT_PADDING = 5;
        /**
         * 需求： height，如果小于fontSize 则取 fontSize 的值，大于 fontSize 时保持文字上下居中.
         * Android: 如果高度只是fontSize的值，显示不全，和iOS保持一致，上下有5dp左右padding
         */
        if (badgeHeight != LayoutParams.WRAP_CONTENT) {
            int fontSize = ScreenUtil.dp2px(badgeStyle.fontSize + BADGE_HEIGHT_PADDING);
            if (badgeHeight < fontSize) {
                badgeHeight = fontSize;
            }
            mBadge.setGravity(Gravity.CENTER);
        } else {
            mBadge.setGravity(Gravity.TOP | Gravity.START);
        }
        Paint paint = new Paint();
        paint.setTextSize(ScreenUtil.dp2px(badgeStyle.fontSize));
        mBadge.setGravity(Gravity.CENTER);
        float textWidth = paint.measureText(text != null ? text : "");
        float textWidthWithPadding = textWidth + 2 * ScreenUtil.dp2px(5);

        if (badgeWidth != LayoutParams.WRAP_CONTENT) {
            if (badgeWidth < textWidth) {
                // 如果badge的宽度小于文字的宽度，则badge的宽度设置为WRAP_CONTENT。
                badgeWidth = LayoutParams.WRAP_CONTENT;
            } else if (badgeWidth < badgeHeight) {
                //宽有效，但是宽度小于高度，则令宽度等于高度
                badgeWidth = badgeHeight;
            }
        } else if (textWidthWithPadding < badgeHeight) {
            //宽为自适应，但整体还是小于高度，则令宽度等于高度
            badgeWidth = badgeHeight;
        }

        RelativeLayout.LayoutParams params = new LayoutParams(badgeWidth, badgeHeight);
        if (badgeStyle.offset == null) {
            params.addRule(RelativeLayout.ALIGN_LEFT, mCurrentIcon.getId());
            params.leftMargin = ScreenUtil.dp2px(20);
        } else {
            int offsetX = badgeStyle.offset.x;
            int offsetY = badgeStyle.offset.y;
            if (MSCHornRollbackConfig.enableTabBarBadgeUnitChange()) {
                offsetX = ScreenUtil.dp2px(offsetX);
                offsetY = ScreenUtil.dp2px(offsetY);
            }
            params.addRule(RelativeLayout.CENTER_IN_PARENT);
            mBadge.setTranslationX(offsetX);    // 水平方向偏移, x > 0 徽标向右移
            mBadge.setTranslationY(offsetY);    // 垂直方向偏移, y > 0 徽标向下移
        }
        mBadge.setLayoutParams(params);

        mBadge.setTextSize(TypedValue.COMPLEX_UNIT_DIP, badgeStyle.fontSize);
        mBadge.setTextColor(ColorUtil.parseColor(badgeStyle.color, ColorUtil.parseColor(BadgeStyle.DEFAULT_COLOR)));

        CompatGradientDrawable badgeBackground = new CompatGradientDrawable();
        int badgeRadius = ScreenUtil.dp2px(10);
        Paint.FontMetrics fontMetrics = paint.getFontMetrics();
        float textHeight = fontMetrics.bottom - fontMetrics.top;
        // 如果没有设置圆角
        if (badgeStyle.borderRadius == BadgeStyle.DEFAULT_NOT_SET_BORDER_RADIUS) {
            if (badgeHeight == LayoutParams.WRAP_CONTENT) {
                // 高度为WRAP_CONTENT，圆角为字体高度的一半
                badgeRadius = (int) (textHeight / 2);
            } else {
                // 高度不为WRAP_CONTENT，圆角为高度的一半
                badgeRadius = badgeHeight / 2;
            }
        } else {
            /**
             * 若业务设置了圆角就使用业务设置的圆角
             * 若圆角半径大于高度的一半，安卓和鸿蒙的系统会按照高度的一半来绘制圆角，iOS需要额外处理这种场景即当业务设置的圆角半径大于高度的一半时，将圆角半径赋值成高度的一半
             */
            badgeRadius = borderRadius;
        }
        badgeBackground.setCornerRadius(badgeRadius);
        if (badgeWidth == LayoutParams.WRAP_CONTENT) {
            badgeBackground.setPadding(ScreenUtil.dp2px(5), 0, ScreenUtil.dp2px(5), 0);
        }
        badgeBackground.setColor(ColorUtil.parseColor(badgeStyle.backgroundColor, ColorUtil.parseColor(BadgeStyle.DEFAULT_BACKGROUND_COLOR)));
        badgeBackground.setStroke(borderSize, ColorUtil.parseColor(badgeStyle.borderColor, ColorUtil.parseColor(BadgeStyle.DEFAULT_BORDER_COLOR)));
        mBadge.setBackground(badgeBackground);

        if ("bold".equals(badgeStyle.fontWeight)) {
            mBadge.getPaint().setFakeBoldText(true);
        } else {
            mBadge.getPaint().setFakeBoldText(false);
        }

        mBadge.setVisibility(View.VISIBLE);

        if (text.length() >= 4) {
            mBadge.setText("···");
        } else {
            mBadge.setText(text);
        }
    }

    /**
     * 移除 tabBar 某一项右上角的文本
     */
    public void removeTabBarBadge() {
        if (mBadge == null) {
            return;
        }
        if (mBadge.getVisibility() == View.VISIBLE) {
            mBadge.setVisibility(GONE);
        }
    }

    @Override
    public void setSelected(boolean selected) {
        super.setSelected(selected);
        if (mInfo == null) {
            return;
        }
        refreshIfIconSizeChange(mInfo);
        String color;
        String icon;
        if (selected) {
            color = mInfo.selectedColor;
            icon = mInfo.selectedIconPath;
            if (TextUtils.isEmpty(color)) {
                color = "#3CC51F";
            }
        } else {
            color = mInfo.color;
            icon = mInfo.iconPath;
            if (TextUtils.isEmpty(color)) {
                color = "#7A7E83";
            }
        }

        if (mInfo.isLargerIcon) {
            mName.setVisibility(INVISIBLE);
        } else {
            mName.setVisibility(VISIBLE);
            mName.setText(mInfo.text);
            mName.setTextColor(ColorUtil.parseColor(color));
        }

        if (TextUtils.isEmpty(icon)) {
            setTop(true);
        }
        if (mCurrentIcon.getVisibility() == VISIBLE) {
            RequestCreator requestCreator = FileUtil.getPicassoRequestCreator(getContext(), icon, mscAppModule.getModule(IFileModule.class));
            if (requestCreator != null) {
                requestCreator.fit().centerInside().into(mCurrentIcon);
            }
        }
    }
}