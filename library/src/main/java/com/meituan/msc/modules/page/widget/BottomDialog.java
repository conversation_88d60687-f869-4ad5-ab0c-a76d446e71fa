package com.meituan.msc.modules.page.widget;

import android.app.Activity;
import android.app.Dialog;
import android.content.Context;
import android.view.Gravity;
import android.view.Window;
import android.view.WindowManager;

import com.meituan.msc.common.utils.ScreenUtil;
import com.meituan.msc.lib.R;

/*
 * 居于底部的dialog
 */
public class BottomDialog extends Dialog {

    private Context mContext;
    public BottomDialog(Context context) {
        super(context, R.style.MSCBackgroundDialog);
        mContext = context;
    }

    @Override
    public void show() {
        setWindowAttributes();
        try {
            if (isActivityValid()) {
                super.show();
            }
        } catch (Exception e) {
            e.printStackTrace();
        }
    }

    public void dismiss() {
        try {
            super.dismiss();
        } catch (Exception e) {
            e.printStackTrace();
        }

    }

    public boolean isActivityValid() {
        if (null != this.mContext && this.mContext instanceof Activity) {
            Activity at = (Activity) this.mContext;
            return !at.isFinishing();
        } else {
            return false;
        }
    }

    /**
     * 设置窗口的属性
     */
    private void setWindowAttributes() {
        Window window = getWindow();
        if (window != null) {
            WindowManager.LayoutParams layoutParams = window.getAttributes();
            layoutParams.width = ScreenUtil.getScreenWidth(mContext instanceof Activity ? (Activity) mContext : null, null);
            layoutParams.gravity = Gravity.BOTTOM;
            window.setAttributes(layoutParams);
        }
    }

}
