package com.meituan.msc.modules.page.widget;

import android.animation.ValueAnimator;
import android.content.Context;
import android.graphics.Color;
import android.graphics.drawable.Drawable;
import android.support.annotation.NonNull;
import android.support.v7.widget.AppCompatImageView;
import android.view.View;
import android.view.animation.LinearInterpolator;

import com.meituan.msc.lib.R;
import com.meituan.msc.modules.reporter.MSCLog;

public class FlowerLoadingIndicator extends AppCompatImageView {
    private final String TAG = "FlowerLoadingIndicator";
    private boolean mIsShowing;
    private ValueAnimator loadingAnimator;

    public FlowerLoadingIndicator(Context context, boolean tintWhite) {
        super(context);
        init(context, tintWhite);
    }

    private void init(Context context, boolean tintWhite) {
        Drawable drawable = context.getResources().getDrawable(R.drawable.icon_loading_frame_0);
        if (tintWhite) {
            drawable.setTint(Color.WHITE);
        }
        setImageDrawable(drawable);
        loadingAnimator = ValueAnimator.ofInt(0, 8);
        loadingAnimator.addUpdateListener(new ValueAnimator.AnimatorUpdateListener() {
            @Override
            public void onAnimationUpdate(ValueAnimator animation) {
                setRotation((int) animation.getAnimatedValue() * 45);
            }
        });
        loadingAnimator.setInterpolator(new LinearInterpolator());
        loadingAnimator.setRepeatCount(ValueAnimator.INFINITE);
        loadingAnimator.setDuration(496);
    }

    /**
     * 添加至Window/Activity stop时均会调用一次
     */
    @Override
    protected void onVisibilityChanged(@NonNull View changedView, int visibility) {
        if (visibility == VISIBLE) {
            show();
        } else {
            hide();
        }
    }

    @Override
    protected void onDetachedFromWindow() {
        super.onDetachedFromWindow();
        if (loadingAnimator != null) {
            loadingAnimator.removeAllUpdateListeners();
        }
    }

    /**
     * 显示加载指示器
     */
    private void show() {
        if (mIsShowing) {
            return;
        }
        MSCLog.i(TAG, "FlowerLoadingIndicator[" + this.hashCode() + "]: FlowerLoadingView show");
        if (loadingAnimator != null) {
            loadingAnimator.start();
        }
        mIsShowing = true;
    }

    /**
     * 隐藏加载指示器
     */
    private void hide() {
        if (!mIsShowing) {
            return;
        }
        MSCLog.i(TAG, "FlowerLoadingIndicator[" + this.hashCode() + "]: FlowerLoadingView hide");
        if (loadingAnimator != null) {
            loadingAnimator.pause();
        }
        mIsShowing = false;
    }
}
