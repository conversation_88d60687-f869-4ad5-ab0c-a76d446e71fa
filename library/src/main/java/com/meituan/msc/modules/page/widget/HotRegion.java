package com.meituan.msc.modules.page.widget;

import android.view.MotionEvent;

import com.meituan.msc.common.utils.DisplayUtil;

/**
 * 沉浸模式热区数据结构
 * https://km.sankuai.com/page/977030423
 *
 * <AUTHOR>
 * @date 2021/7/27.
 */
public class HotRegion {
    // 沉浸模式事件分发消费方向：希望消费水平方向滑动
    public static final String SINK_MODE_EVENT_DIRECTION_HORIZONTAL = "horizontal";
    // 沉浸模式事件分发消费方向：希望消费竖直方向滑动（暂不支持）
    public static final String SINK_MODE_EVENT_DIRECTION_VERTICAL = "vertical";
    // 沉浸模式事件分发消费方向：无（默认）
    public static final String SINK_MODE_EVENT_DIRECTION_NONE = "none";

    private final boolean fixed;
    /**
     * 前端传入的逻辑像素值
     */
    private final float left;
    private final float top;
    private final float width;
    private final float height;
    /**
     * 沉浸模式组件事件消费方向：设置有效值后，事件分发会走到双线分发的优化策略：
     * Native层：Cover&UnderCover层。
     * WebView层：WebView前端View层。
     * 从上至下的View层级是：Cover->WebView->UnderCover。
     * Cover层是所有cover-view，cover-image组件层级，本质是悬浮最上层的Native组件层。
     * UnderCover是沉浸模式Native组件层级，在最底层，配合WebView层级透明化来实现沉浸模式效果。
     * 1. Down事件双线分发给Native&WebView。
     * 2. Move事件根据滑动方向和当前要消费的方向是否匹配来决定是否优先分发，PS：如果当前水平滑动，沉浸模式组件消费的方向也是水平，会优先分发给Native层，否则走默认分发逻辑（Cover->WebView->UnderCover，大部分效果是走到WebView层）
     * 3. Up事件会根据有没有命中前端热区以及是否是点击事件行为的Up事件（没有发生滑动）来决定是否优先分发给Native层。PS：如果是没有命中前端热区并且是点击事件的Up事件，会优先给Native层来消费。
     * 4. Native层内部消费策略：Cover优先于UnderCover，这个是保持原来逻辑。
     */
    private final String mtSinkModeEventDirection;

    public HotRegion(boolean fixed, float left, float top, float width, float height, String mtSinkModeEventDirection) {
        this.fixed = fixed;
        this.left = left * DisplayUtil.getDisplayMetrics().density;
        this.top = top * DisplayUtil.getDisplayMetrics().density;
        this.width = width * DisplayUtil.getDisplayMetrics().density;
        this.height = height * DisplayUtil.getDisplayMetrics().density;
        this.mtSinkModeEventDirection = mtSinkModeEventDirection;
    }


    public boolean isInHotRegion(MotionEvent event, int webViewScrollWidth, int webViewScrollHeight, int hotRegionTopPadding) {
        float x = event.getX();
        float y = event.getY();

        if (width < 0 || height < 0) {
            return false;
        }

        if (fixed) {
            // 前端fixed布局方式，以视窗位置为标准，热区范围由前端返回的x y width height决定
            if (x > left && x < left + width
                    && y > top && y < top + height) {
                return true;
            }
        } else {
            // 前端标准布局方式，以视图组件为标准（视图组件高度可以大于屏幕高度)，热区范围由前端返回的x y width height决定
            // 且前端页面滚动时，热区数据不会变更，但是物理热区有变更，变更大小为WebView内存滑动距离
            float finalLeft = left - webViewScrollWidth;
            float finalRight = left + width - webViewScrollWidth;
            float finalTop = top - webViewScrollHeight + hotRegionTopPadding;
            float finalBottom = top + height - webViewScrollHeight + hotRegionTopPadding;

            if (x > finalLeft && x < finalRight
                    && y > finalTop && y < finalBottom) {
                return true;
            }
        }
        return false;
    }

    public String getMtSinkModeEventDirection() {
        return mtSinkModeEventDirection;
    }

    /**
     * 是否是有效的沉浸模式方向：水平方向/竖直方向（目前仅支持水平方向）
     *
     * @param mtSinkModeEventDirection
     * @return
     */
    public static boolean isSinkModeEventDirectionValid(String mtSinkModeEventDirection) {
        return SINK_MODE_EVENT_DIRECTION_HORIZONTAL.equals(mtSinkModeEventDirection);
    }

    @Override
    public String toString() {
        return "HotRegion{" +
                "fixed=" + fixed +
                ", left=" + left +
                ", top=" + top +
                ", width=" + width +
                ", height=" + height +
                ", mtSinkModeEventDirection='" + mtSinkModeEventDirection + '\'' +
                '}';
    }
}
