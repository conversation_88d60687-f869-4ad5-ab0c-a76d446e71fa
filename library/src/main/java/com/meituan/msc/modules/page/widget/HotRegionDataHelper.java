package com.meituan.msc.modules.page.widget;

import android.support.annotation.NonNull;
import android.support.annotation.Nullable;

import com.meituan.msc.modules.reporter.MSCLog;

import org.json.JSONArray;
import org.json.JSONException;
import org.json.JSONObject;

import java.util.List;

/**
 * 热区数据处理工具类
 *
 * <AUTHOR>
 * @date 2021/7/27.
 */
public final class HotRegionDataHelper {

    private static final String TAG = "HotRegionDataHelper";

    public static void setDataToHotRegionList(@Nullable String params, @Nullable List<HotRegion> hotRegions) {
        if (params == null || hotRegions == null) {
            return;
        }

        JSONArray regionJSONArray = getHotRegionDataArray(params);
        if (regionJSONArray == null) {
            return;
        }
        hotRegions.clear();

        for (int i = 0; i < regionJSONArray.length(); i++) {
            JSONObject jsonObject = regionJSONArray.optJSONObject(i);
            if (jsonObject == null) {
                continue;
            }

            HotRegion hotRegion = createHotRegionData(jsonObject);
            if (hotRegion != null) {
                hotRegions.add(hotRegion);
            }
        }
    }

    @Nullable
    private static JSONArray getHotRegionDataArray(@NonNull String params) {
        JSONObject regionArray = null;
        try {
            regionArray = new JSONObject(params);
        } catch (JSONException e) {
            MSCLog.e(TAG, "getHotRegionDataArray error" + e);
        }
        if (regionArray == null) {
            return null;
        }

        return regionArray.optJSONArray("data");
    }

    @Nullable
    private static HotRegion createHotRegionData(JSONObject jsonObject) {
        HotRegion hotRegion = null;
        try {
            boolean fixed = jsonObject.optBoolean("fixed", false);
            float left = (float) jsonObject.optDouble("left", 0.0f);
            float top = (float) jsonObject.optDouble("top", 0.0f);
            float width = (float) jsonObject.optDouble("width", 0.0f);
            float height = (float) jsonObject.optDouble("height", 0.0f);
            String mtSinkModeEventDirection = jsonObject.optString("mtSinkModeEventDirection", HotRegion.SINK_MODE_EVENT_DIRECTION_NONE);
            hotRegion = new HotRegion(fixed, left, top, width, height, mtSinkModeEventDirection);
        } catch (Throwable e) {
            MSCLog.e(TAG, "createHotRegionData error:" + e);
        }
        return hotRegion;
    }
}
