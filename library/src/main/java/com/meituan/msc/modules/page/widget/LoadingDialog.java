
package com.meituan.msc.modules.page.widget;

import android.app.Dialog;
import android.content.Context;
import android.os.Bundle;
import android.text.TextUtils;
import android.view.View;
import android.view.ViewGroup;
import android.widget.TextView;

import com.meituan.msc.common.utils.ScreenUtil;
import com.meituan.msc.lib.R;
import com.meituan.msc.modules.engine.MSCHornRollbackConfig;
import com.meituan.msc.modules.reporter.MSCLog;

public class LoadingDialog extends Dialog {

    private static final String TAG = "LoadingDialog";

    private TextView mTextView;
    private ViewGroup mLoadingContainer;
    private final boolean enableLoadingViewStyleOpt;

    public LoadingDialog(Context context,String appId) {
        super(context, R.style.MSCTransparentDialog);
        setCancelable(true);
        setCanceledOnTouchOutside(false);
        enableLoadingViewStyleOpt = MSCHornRollbackConfig.enableLoadingViewStyleOpt(appId);
    }


    @Override
    protected void onCreate(Bundle savedInstanceState) {
        super.onCreate(savedInstanceState);
        setContentView(R.layout.msc_loading_dialog);
        mTextView = findViewById(R.id.loading_message);
        mLoadingContainer = findViewById(R.id.loading_container_dialog);
    }

    public void show(String message) {
        show();

        if (mTextView != null) {
            if (TextUtils.isEmpty(message)) {
                mTextView.setText("");
                mTextView.setVisibility(View.GONE);
            } else {
                mTextView.setText(message);
                mTextView.setVisibility(View.VISIBLE);
            }
        } else {
            MSCLog.e(TAG, "mTextView is null");
        }
        if (enableLoadingViewStyleOpt) {
            if (mTextView != null) {
                mTextView.setTextSize(12);
            }
            if (mLoadingContainer == null) {
                return;
            }
            // 移除之前老样式LoadingView，插入新样式LoadingView
            mLoadingContainer.removeViewAt(0);
            mLoadingContainer.addView(new FlowerLoadingIndicator(getContext(), true), 0, new ViewGroup.LayoutParams(ScreenUtil.dp2px(30), ScreenUtil.dp2px(30)));
        }
    }

    @Override
    public void show() {
        try {
            super.show();
        } catch (Exception e) {
            MSCLog.e(TAG, e.getMessage());
        }
    }

    @Override
    public boolean isShowing() {
        try {
            return super.isShowing();
        } catch (Exception e) {
            MSCLog.e(TAG, e.getMessage());
        }

        return false;
    }

    @Override
    public void dismiss() {
        try {
            super.dismiss();
        } catch (Exception e) {
            MSCLog.e(TAG, e.getMessage());
        }
    }
}
