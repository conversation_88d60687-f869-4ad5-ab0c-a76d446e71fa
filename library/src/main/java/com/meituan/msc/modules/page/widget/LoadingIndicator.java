package com.meituan.msc.modules.page.widget;

import android.content.Context;
import android.support.annotation.NonNull;
import android.support.annotation.Nullable;
import android.support.v7.widget.AppCompatImageView;
import android.util.AttributeSet;
import android.view.View;

/**
 * 加载指示视图
 */
public class LoadingIndicator extends AppCompatImageView {
    private IndicatorDrawable mDrawable;

    private boolean mIsShowing;

    public LoadingIndicator(Context context) {
        super(context);
        init(context);
    }

    public LoadingIndicator(Context context, @Nullable AttributeSet attrs) {
        super(context, attrs);
        init(context);
    }

    private void init(Context context) {
        mDrawable = new IndicatorDrawable();
        setImageDrawable(mDrawable);
    }

    /**
     * 添加至Window/Activity stop时均会调用一次
     */
    @Override
    protected void onVisibilityChanged(@NonNull View changedView, int visibility) {
        if (visibility == VISIBLE) {
            show();
        } else {
            hide();
        }
    }

    /**
     * 显示加载指示器
     */
    public void show() {
        if (mIsShowing) {
            return;
        }
        setVisibility(VISIBLE);
        mDrawable.start();
        mIsShowing = true;
    }

    /**
     * 隐藏加载指示器
     */
    public void hide() {
        if (!mIsShowing) {
            return;
        }
        mIsShowing = false;
        setVisibility(GONE);
        mDrawable.stop();
    }
}

