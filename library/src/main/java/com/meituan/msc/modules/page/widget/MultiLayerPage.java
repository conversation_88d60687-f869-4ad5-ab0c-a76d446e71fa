package com.meituan.msc.modules.page.widget;

import android.content.Context;
import android.graphics.Color;
import android.graphics.drawable.ColorDrawable;
import android.support.annotation.NonNull;
import android.support.annotation.WorkerThread;
import android.text.TextUtils;
import android.view.MotionEvent;
import android.view.View;
import android.view.ViewConfiguration;
import android.view.ViewGroup;
import android.widget.FrameLayout;

import com.meituan.msc.modules.engine.MSCHornRollbackConfig;
import com.meituan.msc.modules.page.render.BaseRenderer;
import com.meituan.msc.modules.page.render.IRendererView;
import com.meituan.msc.modules.page.render.webview.MSCWebView;
import com.meituan.msc.modules.page.render.webview.OnContentScrollChangeListener;
import com.meituan.msc.modules.page.view.CoverViewWrapper;
import com.meituan.msc.modules.page.view.ViewFinder;
import com.meituan.msc.modules.page.view.coverview.CoverViewRootContainer;
import com.meituan.msc.modules.page.view.coverview.InfoWindowRootContainer;

import java.util.List;
import java.util.Map;
import java.util.concurrent.ConcurrentHashMap;
import java.util.concurrent.CopyOnWriteArrayList;

/**
 * 处理 CoverView 容器
 */
public class MultiLayerPage extends SwipeRefreshLayout {
    private final static String TAG = "MultiLayerPage";
    private final static int TOUCH_SLOP_FACTOR = 2;
    // Touch事件滑动方向：无方向（默认值）
    private final static int TOUCH_MOVE_DIRECTION_NONE = 0;
    // Touch事件滑动方向：竖直滑动方向
    private final static int TOUCH_MOVE_DIRECTION_VERTICAL = 1;
    // Touch事件滑动方向：水平滑动方向
    private final static int TOUCH_MOVE_DIRECTION_HORIZONTAL = -1;
    // Touch scroll判定阈值（一般大于该阈值以上的Touch move才认为是滚动行为）
    private final int mTouchSlop;
    private IRendererView rendererView;
    private FrameLayout rootView;
    private CoverViewRootContainer mCoverViewContainer = null;
    private CoverViewRootContainer mUnderCoverViewContainer = null;
    private OnContentScrollChangeListener mOnContentScrollChangeListener;

    //Marker上定制化信息窗口View的CoverView根视图容器
    private volatile ConcurrentHashMap<String, CoverViewRootContainer> mMarkerInfoWindowRootContainerMap = new ConcurrentHashMap<>();
    //Maker CoverView视图树容器，MakerId对应多个CoverView
    public volatile ConcurrentHashMap<String,List<String>> mMarkerViewIdsMap = new ConcurrentHashMap<>();

    /**
     * WebView操作热区数据集合，热区内事件需要分发给WebView
     */
    private final List<HotRegion> mWebViewRegionList = new CopyOnWriteArrayList<>();
    /**
     * WebView相对初试状态垂直滚动的距离
     */
    private int webViewScrollWidth = 0;
    /**
     * WebView相对初试状态水平滚动的距离
     */
    private int webViewScrollHeight = 0;

    private final BaseRenderer mRenderer;

    public MultiLayerPage(Context context, TouchInterceptor innerWebViewStatus, BaseRenderer renderer) {
        super(context);
        setTouchInterceptor(innerWebViewStatus);
        mTouchSlop = ViewConfiguration.get(context).getScaledTouchSlop();
        mRenderer = renderer;
    }

    public boolean canChildScrollUp() {
        return mCoverViewContainer.getCoverViewScrollY() != 0;
    }

    public void setContentView(IRendererView pageWebView) {
        if (rootView != null) {
            removeView(rootView);
        }

        rendererView = pageWebView;

        rootView = new FrameLayout(getContext());

        mUnderCoverViewContainer = new CoverViewRootContainer(getContext());
        rootView.addView(mUnderCoverViewContainer, new FrameLayout.LayoutParams(FrameLayout.LayoutParams.MATCH_PARENT,
                FrameLayout.LayoutParams.MATCH_PARENT));

        rootView.addView(rendererView.asView(), new FrameLayout.LayoutParams(FrameLayout.LayoutParams.MATCH_PARENT,
                FrameLayout.LayoutParams.MATCH_PARENT));

        mCoverViewContainer = new CoverViewRootContainer(getContext());
        rootView.addView(mCoverViewContainer, new FrameLayout.LayoutParams(FrameLayout.LayoutParams.MATCH_PARENT,
                FrameLayout.LayoutParams.MATCH_PARENT));

        mOnContentScrollChangeListener = new OnContentScrollChangeListener() {
            @Override
            public void onWebScrollChange(int l, int t, int oldl, int oldt) {
                if (mCoverViewContainer != null) {
                    mCoverViewContainer.onWebScrollChange(l, t, oldl, oldt);
                }
                if (mUnderCoverViewContainer != null) {
                    mUnderCoverViewContainer.onWebScrollChange(l, t, oldl, oldt);
                }

                webViewScrollWidth = l;
                webViewScrollHeight = t;
            }
        };
        rendererView.setOnContentScrollChangeListener(mOnContentScrollChangeListener);
        addView(rootView, new LayoutParams(ViewGroup.LayoutParams.MATCH_PARENT, ViewGroup.LayoutParams.MATCH_PARENT));
    }

    public void enableSinkMode(boolean enable) {
        //设置背景色透明
        if (enable) {
            if (rendererView instanceof MSCWebView) {
                View view = ((MSCWebView) rendererView).getWebView();
                view.setBackgroundColor(Color.TRANSPARENT); // 设置背景色
                //设置图层类型为LAYER_TYPE_HARDWARE，避免透明背景部分机型不生效
                view.setLayerType(View.LAYER_TYPE_HARDWARE, null);
            }
        }
    }

    public CoverViewRootContainer getUnderCoverViewContainer() {
        return mUnderCoverViewContainer;
    }

    public CoverViewRootContainer getCoverViewContainer() {
        return mCoverViewContainer;
    }


    @WorkerThread
    public void setRegionData(@NonNull String params) {
        HotRegionDataHelper.setDataToHotRegionList(params, mWebViewRegionList);
    }

    View lastTouchTarget;
    // 初始化x,y坐标，用于判断事件滑动方向
    float mInitialInterceptorX, mInitialInterceptorY;
    // 事件滑动方向
    int mTouchMoveDirection = TOUCH_MOVE_DIRECTION_NONE;
    // 事件是否发生滑动行为
    boolean mTouchMoved = false;
    // Touch down的时候判断是否命中热区
    boolean mIsHitHotRegionWhenTouchDown = false;
    // 沉浸模式Native组件消费滑动方向
    String mSinkModeConsumeDirection = HotRegion.SINK_MODE_EVENT_DIRECTION_NONE;

    /**
     * 支持沉浸模式
     * https://km.sankuai.com/page/977030423
     */
    @Override
    public boolean dispatchTouchEvent(@NonNull MotionEvent event) {
        int action = event.getActionMasked();
        boolean isActionDown = (event.getActionMasked() == MotionEvent.ACTION_DOWN);
        if (isActionDown) {
            lastTouchTarget = null;
            mInitialInterceptorX = event.getX();
            mInitialInterceptorY = event.getY();
            mTouchMoveDirection = TOUCH_MOVE_DIRECTION_NONE;
            mTouchMoved = false;
            mIsHitHotRegionWhenTouchDown = isInHotRegion(event);
            mSinkModeConsumeDirection = getSinkModeEventDirection();
        }
//        MSCLog.e(TAG, String.format("dispatchTouchEvent mIsHitHotRegionWhenTouchDown:%s, mSinkModeConsumeDirection:%s, mWebViewRegionList:%s", mIsHitHotRegionWhenTouchDown, mSinkModeConsumeDirection, mWebViewRegionList.toString()));
        if (mWebViewRegionList.isEmpty()) {
            return super.dispatchTouchEvent(event);
        } else {
            /**
             * 沉浸模式双线分发的优化策略：
             * WebView渲染从上至下的View层级是：Cover->WebView->UnderCover。
             * Cover层：是所有cover-view，cover-image组件层级，本质是悬浮最上层的Native组件层。
             * WebView层：WebView前端View层。
             * UnderCover层：是沉浸模式Native组件层级，在最底层，配合WebView层级透明化来实现沉浸模式效果。
             * Native层：Cover&UnderCover层。
             * 1. Down事件双线分发给Native&WebView。
             * 2. Move事件根据滑动方向和当前要消费的方向是否匹配来决定是否优先分发，PS：如果当前水平滑动，沉浸模式组件消费的方向也是水平，会优先分发给Native层，否则走默认分发逻辑（Cover->WebView->UnderCover，大部分效果是走到WebView层）
             * 3. Up事件会根据有没有命中前端热区以及是否是点击事件行为的Up事件（没有发生滑动）来决定是否优先分发给Native层。PS：如果是没有命中前端热区并且是点击事件的Up事件，会优先给Native层来消费。
             * 4. Native层内部消费策略：Cover优先于UnderCover，这个是保持原来逻辑。
             */
            // 沉浸模式双线分发的优化策略-水平方向处理逻辑
            boolean rollbackSinkModeEventTwoLineDispatch = MSCHornRollbackConfig.isRollbackSinkModeEventTwoLineDispatch();
            if (!rollbackSinkModeEventTwoLineDispatch && HotRegion.SINK_MODE_EVENT_DIRECTION_HORIZONTAL.equals(mSinkModeConsumeDirection)) {
                if (action == MotionEvent.ACTION_DOWN) {
                    //Down事件分发事件给Native&WebView层
                    boolean coverViewConsumed = mCoverViewContainer.dispatchTouchEvent(event);
                    if (!coverViewConsumed) {
                        mUnderCoverViewContainer.dispatchTouchEvent(event);
                    }
                } else if (action == MotionEvent.ACTION_MOVE) {
                    // 是否发生过Touch move
                    mTouchMoved = true;
                    float x = event.getX();
                    float y = event.getY();
                    // 计算水平或者竖直滑动偏移量
                    float dx = Math.abs(x - mInitialInterceptorX);
                    float dy = Math.abs(y - mInitialInterceptorY);
                    if (lastTouchTarget != null) {
                        return lastTouchTarget.dispatchTouchEvent(event);
                    }
                    if (mTouchMoveDirection == TOUCH_MOVE_DIRECTION_NONE && dy > mTouchSlop * TOUCH_SLOP_FACTOR) {
                        mTouchMoveDirection = TOUCH_MOVE_DIRECTION_VERTICAL;
                    } else if (mTouchMoveDirection != TOUCH_MOVE_DIRECTION_VERTICAL && dx > mTouchSlop) {
                        // 有水平滑动偏移量，优先分发给Native层消费
                        boolean nativeViewConsumed = mCoverViewContainer.dispatchTouchEvent(event);
                        if (!nativeViewConsumed) {
                            nativeViewConsumed = mUnderCoverViewContainer.dispatchTouchEvent(event);
                            if (nativeViewConsumed) {
                                lastTouchTarget = mUnderCoverViewContainer;
                                mTouchMoveDirection = TOUCH_MOVE_DIRECTION_HORIZONTAL;
                            }
                        } else {
                            lastTouchTarget = mCoverViewContainer;
                            mTouchMoveDirection = TOUCH_MOVE_DIRECTION_HORIZONTAL;
                        }
                        // 有Native层组件消费滑动事件时，则不再向下分发给WebView侧，否则在斜向滑动时会导致WebView和Native层组件一起滑动。
                        if (nativeViewConsumed) {
                            return true;
                        }
                    }
                } else if (action == MotionEvent.ACTION_UP) {
                    if (lastTouchTarget != null) {
                        return lastTouchTarget.dispatchTouchEvent(event);
                    }
                    // 没有命中前端热区以及没有发生滑动（点击事件），则优先给Native层消费。
                    if (!mIsHitHotRegionWhenTouchDown && (!mTouchMoved || mTouchMoveDirection == TOUCH_MOVE_DIRECTION_NONE)) {
                        boolean coverViewConsumed = mCoverViewContainer.dispatchTouchEvent(event);
                        if (!coverViewConsumed) {
                            mUnderCoverViewContainer.dispatchTouchEvent(event);
                        }
                    }
                }
                return super.dispatchTouchEvent(event);
            }
            // Down 事件拦截处理后 方可处理其他事件；Down 事件之后不做 target 变更; 多点触碰也以down事件为准
            if (isActionDown) {
                if (!mIsHitHotRegionWhenTouchDown) {
                    // 触摸事件不在热区内，分发事件给CoverView
                    if (mCoverViewContainer.dispatchTouchEvent(event)) {
                        lastTouchTarget = mCoverViewContainer;
                        return true;
                    }

                    // 触摸事件不在热区内，分发事件给原生组件
                    if (mUnderCoverViewContainer.dispatchTouchEvent(event)) {
                        lastTouchTarget = mUnderCoverViewContainer;
                        return true;
                    }
                }
            }
            if (lastTouchTarget != null) {
                return lastTouchTarget.dispatchTouchEvent(event);
            }
            // 分发事件给WebView组件或CoverView
            return super.dispatchTouchEvent(event);
        }
    }

    private boolean isInHotRegion(MotionEvent event) {
        for (HotRegion region : mWebViewRegionList) {
            if (region == null) {
                continue;
            }

            if (region.isInHotRegion(event, webViewScrollWidth, webViewScrollHeight, contentTopPadding)) {
                return true;
            }
        }
        return false;
    }

    /**
     * 从热区数据里找沉浸模式组件事件消费方向
     *
     * @return
     */
    private String getSinkModeEventDirection() {
        for (HotRegion region : mWebViewRegionList) {
            if (region == null) {
                continue;
            }
            if (HotRegion.isSinkModeEventDirectionValid(region.getMtSinkModeEventDirection())) {
                return region.getMtSinkModeEventDirection();
            }
        }
        return HotRegion.SINK_MODE_EVENT_DIRECTION_NONE;
    }

    public CoverViewRootContainer getOrCreateMarkerInfoWindowRootContainer(@NonNull String markerIdKey) {
        if (mMarkerInfoWindowRootContainerMap.containsKey(markerIdKey)) {
            return mMarkerInfoWindowRootContainerMap.get(markerIdKey);
        }
        InfoWindowRootContainer container = new InfoWindowRootContainer(getContext());
        container.setInterceptTouchEvent(true);
        container.setLayoutParams(new LayoutParams(LayoutParams.WRAP_CONTENT, LayoutParams.WRAP_CONTENT));
        mMarkerInfoWindowRootContainerMap.put(markerIdKey, container);
        return container;
    }

    public ConcurrentHashMap<String, CoverViewRootContainer> getMarkerInfoWindowRootContainerMap(){
        return mMarkerInfoWindowRootContainerMap;
    }

    public CoverViewWrapper findCoverViewWrapperInMarkerInfoWindowRootContainer(int viewId, int parentId){
        CoverViewWrapper viewWrapper = null;
        String markerId = getMarkIdInMarkerCoverViews(String.valueOf(parentId));
        if (parentId != -1) {
            CoverViewRootContainer coverViewRootContainer = getOrCreateMarkerInfoWindowRootContainer(String.valueOf(markerId));
            if (coverViewRootContainer != null) {
                viewWrapper = ViewFinder.findCoverViewWrapper(coverViewRootContainer, viewId);
            }
        } else {
            for (Map.Entry entry : getMarkerInfoWindowRootContainerMap().entrySet()) {
                viewWrapper = ViewFinder.findCoverViewWrapper((CoverViewRootContainer) entry.getValue(), viewId);
                if (viewWrapper != null) {
                    return viewWrapper;
                }
            }
        }
        return viewWrapper;
    }

    /**
     * 获取自定义气泡视图容器
     *
     * @param viewId
     * @return
     */
    public CoverViewRootContainer getInfoWindowRootContainerByViewId(int viewId){
        for (Map.Entry entry : getMarkerInfoWindowRootContainerMap().entrySet()) {
            CoverViewWrapper viewWrapper = ViewFinder.findCoverViewWrapper((CoverViewRootContainer) entry.getValue(), viewId);
            if (viewWrapper != null) {
                return (CoverViewRootContainer) entry.getValue();
            }
        }
        return null;
    }

    public String getMarkIdInMarkerCoverViews(String parentId) {
        if (TextUtils.isEmpty(parentId)) {
            return null;
        }
        for (Map.Entry<String, List<String>> entry : getMarkerViewIdsMap().entrySet()) {
            List<String> strings = entry.getValue();
            if (strings == null) {
                continue;
            }
            if (strings.contains(parentId)) {
                return entry.getKey();
            }
        }
        return null;
    }

    public ConcurrentHashMap<String, List<String>> getMarkerViewIdsMap() {
        return mMarkerViewIdsMap;
    }

    public void removeRenderView() {
        if (rootView != null && rendererView != null) {
            rootView.removeView(rendererView.asView());
        }
    }

    @Override
    public void setBackgroundColor(int color) {
        if (rendererView instanceof MSCWebView) {
            ((MSCWebView) rendererView).setWebViewBackgroundColor(color);
        } else {
            super.setBackgroundColor(color);
        }
    }
}
