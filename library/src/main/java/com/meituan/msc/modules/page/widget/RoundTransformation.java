package com.meituan.msc.modules.page.widget;

import android.graphics.Bitmap;

import com.sankuai.common.utils.ImageUtils;
import com.squareup.picasso.Transformation;

public class RoundTransformation implements Transformation {
    private static final RoundTransformation INSTANCE = new RoundTransformation();

    public static RoundTransformation getInstance() {
        return INSTANCE;
    }

    @Override
    public Bitmap transform(Bitmap source) {
        return ImageUtils.getRoundCornerBitmap(source, source.getWidth(), 0);
    }

    @Override
    public String key() {
        return "com.meituan.mmp.lib.widget.RoundTransformation";
    }
}
