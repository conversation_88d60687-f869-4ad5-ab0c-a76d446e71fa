package com.meituan.msc.modules.page.widget;

import android.content.Context;
import android.graphics.drawable.Drawable;
import android.os.Handler;
import android.support.annotation.NonNull;
import android.view.Gravity;
import android.view.MotionEvent;
import android.view.View;
import android.view.ViewGroup;
import android.view.animation.Interpolator;
import android.view.animation.OvershootInterpolator;
import android.widget.ImageView;
import android.widget.LinearLayout;

import com.meituan.msc.common.utils.DisplayUtil;
import com.meituan.msc.common.utils.FileUtil;
import com.meituan.msc.lib.R;
import com.meituan.msc.lib.interfaces.IFileModule;
import com.meituan.msc.modules.page.custom.PullLoadingIconConfig;
import com.meituan.msc.modules.reporter.MSCLog;
import com.squareup.picasso.Picasso;
import com.squareup.picasso.PicassoDrawable;
import com.squareup.picasso.PicassoDrawableTarget;
import com.squareup.picasso.RequestCreator;


/**
 * 处理由下拉刷新引起的页面移动
 */
public class SwipeRefreshLayout extends LinearLayout {
    private static final String TAG = "SwipeRefreshLayout";

    private static final int DEFAULT_HEIGHT = DisplayUtil.fromDPToPix(65);
    private int mHeight = DEFAULT_HEIGHT;
    private static final int TOP_PADDING = DisplayUtil.fromDPToPix(21);
    private static final int BOTTOM_PADDING = DisplayUtil.fromDPToPix(18);
    private static final int mTouchSlop = DisplayUtil.fromDPToPix(3);
    private boolean mIsBeingDragged = false;
    public boolean mIsLoading = false;
    private OnRefreshListener listener;
    private boolean mEnabled = true;
    private PullLoadingIconConfig mPullLoadingIconConfig;
    private String mImageUrl;
    private IFileModule mFileModule;
    private PicassoDrawable mPicassoDrawable;

    protected ImageView loadingView;
    private float mLastMotionX, mLastMotionY, mInitialMotionY;
    private SmoothScrollRunnable mCurrentSmoothScrollRunnable;
    private int mLoadingDrawableResId = R.drawable.msc_page_refresh_loading_1;
    private int mUnLoadingDrawableResId = R.drawable.msc_page_refresh_loading_0;
    private Handler handler = new Handler();
    private TouchInterceptor mTouchInterceptor;

    /**
     * 下拉刷新触发时，内容区域的top-padding值
     */
    protected int contentTopPadding = 0;

    public interface TouchInterceptor {
        boolean onInterceptTouchEvent(MotionEvent ev);
    }

    public SwipeRefreshLayout(Context context) {
        super(context);
        init();
    }

    public SwipeRefreshLayout setTouchInterceptor(TouchInterceptor touchInterceptor) {
        mTouchInterceptor = touchInterceptor;
        return this;
    }

    private void init() {
        setOrientation(VERTICAL);
        setGravity(Gravity.CENTER_HORIZONTAL);

        loadingView = new ImageView(getContext());
        loadingView.setImageDrawable(getResources().getDrawable(mUnLoadingDrawableResId));
        LinearLayout.LayoutParams params = new LayoutParams(DEFAULT_HEIGHT, DEFAULT_HEIGHT - mTouchSlop - BOTTOM_PADDING - TOP_PADDING);
        params.topMargin = TOP_PADDING;
        params.bottomMargin = mTouchSlop + BOTTOM_PADDING;
        addView(loadingView, params);
        setPadding(0, -DEFAULT_HEIGHT, 0, 0);
    }

    public void setContentView(View contentView) {
        addView(contentView, new LayoutParams(ViewGroup.LayoutParams.MATCH_PARENT, ViewGroup.LayoutParams.MATCH_PARENT));
    }

    @Override
    public boolean onInterceptTouchEvent(MotionEvent ev) {
        if (!mEnabled) {
            return false;
        }
        //如果webview禁用下拉刷新
        if (mTouchInterceptor.onInterceptTouchEvent(ev)) {
            return false;
        }

        int action = ev.getAction();
        switch (action) {
            case MotionEvent.ACTION_DOWN:
                mLastMotionY = mInitialMotionY = ev.getY();
                mLastMotionX = ev.getX();
                mIsBeingDragged = false;
                break;

            case MotionEvent.ACTION_MOVE:
                final float y = ev.getY();
                final float dy = y - mLastMotionY;
                final float yDiff = Math.abs(dy);
                final float xDiff = Math.abs(ev.getX() - mLastMotionX);

                if (yDiff > mTouchSlop) {
                    if (dy >= 1f && !canChildScrollUp() && yDiff > xDiff) {
                        if (!mIsBeingDragged) {
                            mInitialMotionY = y;
                        }
                        mLastMotionY = y;
                        mIsBeingDragged = true;
                    }
                }
                break;

            case MotionEvent.ACTION_UP:
            case MotionEvent.ACTION_CANCEL:
                mIsBeingDragged = false;
                break;
        }
        return mIsBeingDragged || mIsLoading;
    }

    @Override
    public boolean onTouchEvent(MotionEvent event) {
        if (!mEnabled) {
            return super.onTouchEvent(event);
        }
        int action = event.getAction();
        switch (action) {
            case MotionEvent.ACTION_DOWN: {
                mLastMotionY = mInitialMotionY = event.getY();
                return true;
            }
            case MotionEvent.ACTION_MOVE:
                if (mIsBeingDragged || mIsLoading) {
                    mLastMotionY = event.getY();
                    pullEvent();
                    return true;
                }
                break;
            case MotionEvent.ACTION_UP:
            case MotionEvent.ACTION_CANCEL:
                mIsBeingDragged = false;
                float scaleY = getScrollY();
                if (scaleY + mHeight < 0f) {
                    smoothScrollTo(Math.round(-mHeight));
                    if (!mIsLoading) {
                        onStartLoading();
                    }
                    mIsLoading = true;
                } else if (scaleY < 0) {
                    smoothScrollTo(0);
                    if (mIsLoading) {
                        onStopLoading();
                    }
                    mIsLoading = false;
                } else {
                    if (mIsLoading) {
                        onStopLoading();
                    }
                    mIsLoading = false;
                }
                return true;
        }
        return super.onTouchEvent(event);
    }

    private boolean pullEvent() {

        final int oldHeight = getScrollY();
        int newHeight = Math.round((mInitialMotionY - mLastMotionY) / 2.0f);
        if (mIsLoading) {
            newHeight -= mHeight;
        }

        if (newHeight <= 0) {
            scrollTo(0, newHeight);
        } else {
            scrollTo(0, 0);
        }
        return oldHeight != newHeight;
    }

    public boolean canChildScrollUp() {
        return true;
    }

    protected final void smoothScrollTo(int y) {
        if (null != mCurrentSmoothScrollRunnable) {
            mCurrentSmoothScrollRunnable.stop();
        }

        if (getScrollY() != y) {
            mCurrentSmoothScrollRunnable = new SmoothScrollRunnable(getScrollY(), y);
            post(mCurrentSmoothScrollRunnable);
        }
    }

    final class SmoothScrollRunnable implements Runnable {

        static final int ANIMATION_DURATION_MS = 300;
        static final int ANIMATION_DELAY = 10;
        static final float ANIMATION_OVERSHOOT_TENSION = 2.0f;

        private final Interpolator mInterpolator;
        private final int mScrollToY;
        private final int mScrollFromY;

        private boolean mContinueRunning = true;
        private long mStartTime = -1;
        private int mCurrentY = -1;

        public SmoothScrollRunnable(int fromY, int toY) {
            mScrollFromY = fromY;
            mScrollToY = toY;
            mInterpolator = new OvershootInterpolator(ANIMATION_OVERSHOOT_TENSION);
        }

        @Override
        public void run() {

            /**
             * Only set mStartTime if this is the first time we're starting,
             * else actually calculate the Y delta
             */
            if (mStartTime == -1) {
                mStartTime = System.currentTimeMillis();
            } else {

                /**
                 * We do do all calculations in long to reduce software float
                 * calculations. We use 1000 as it gives us good accuracy and
                 * small rounding errors
                 */
                long normalizedTime = (1000 * (System.currentTimeMillis() - mStartTime)) / ANIMATION_DURATION_MS;
                normalizedTime = Math.max(Math.min(normalizedTime, 1000), 0);

                final int deltaY = Math.round((mScrollFromY - mScrollToY)
                        * mInterpolator.getInterpolation(normalizedTime / 1000f));
                mCurrentY = mScrollFromY - deltaY;
//                setHeaderScroll(mCurrentY);
                scrollTo(0, mCurrentY);
            }

            // If we're not at the target Y, keep going...
            if (mContinueRunning && mScrollToY != mCurrentY) {
                // if (VERSION.SDK_INT >= VERSION_CODES.JELLY_BEAN) {
                // SDK16.postOnAnimation(PullToRefreshBase.this, this);
                // } else {
                postDelayed(this, ANIMATION_DELAY);
                // }
            }
        }

        public void stop() {
            mContinueRunning = false;
            removeCallbacks(this);
        }
    }

    public void setOnRefreshListener(OnRefreshListener onRefreshListener) {
        listener = onRefreshListener;
    }

    public void setRefreshing(boolean enable) {
        if (enable) {
            smoothScrollTo(Math.round(-mHeight));
            if (!mIsLoading) {
                onStartLoading();
            }
            mIsLoading = true;
        } else {
            smoothScrollTo(0);
            if (mIsLoading) {
                onStopLoading();
            }
            mIsLoading = false;
        }
    }

    @Override
    public void setEnabled(boolean enabled) {
        this.mEnabled = enabled;
        if (!mEnabled) {
            setRefreshing(false);
        }
        super.setEnabled(enabled);
    }

    public void setPullLoadingIconConfig(@NonNull PullLoadingIconConfig pullLoadingIconConfig, IFileModule fileModule) {
        this.mPullLoadingIconConfig = pullLoadingIconConfig;
        this.mImageUrl = mPullLoadingIconConfig.src;
        this.mFileModule = fileModule;
        mHeight = Math.max(DEFAULT_HEIGHT, DisplayUtil.fromDPToPix(mPullLoadingIconConfig.height));
        MSCLog.d(TAG, "setPullLoadingIconConfig mImageUrl:", mImageUrl, ", mPullLoadingIconConfig:", mPullLoadingIconConfig.toString(),
                "DEFAULT_HEIGHT:", DEFAULT_HEIGHT, ", mHeight:", mHeight);
        if (mPullLoadingIconConfig.width > 0 && mPullLoadingIconConfig.height > 0) {
            // 按下发尺寸设置图片大小
            LinearLayout.LayoutParams params = (LinearLayout.LayoutParams) loadingView.getLayoutParams();
            params.width = DisplayUtil.fromDPToPix(mPullLoadingIconConfig.width); // 设置宽度，单位为像素
            params.height = DisplayUtil.fromDPToPix(mPullLoadingIconConfig.height); // 设置高度，单位为像素
            // 当自定义图片高度比DEFAULT_HEIGHT更高时，margin计算值为0，Ref:https://km.sankuai.com/collabpage/2579536942
            params.topMargin = (int) Math.round((mHeight - DisplayUtil.fromDPToPix(mPullLoadingIconConfig.height)) / 2.0);
            params.bottomMargin = params.topMargin;
            loadingView.setLayoutParams(params);
            setPadding(0, -mHeight, 0, 0);
            loadingView.requestLayout();
            startCustomLoading();
        }
    }

    public boolean isRefreshing() {
        return mIsLoading;
    }

    public interface OnRefreshListener {
        void onRefresh();
    }

    private void onStartLoading() {
        if (listener != null) {
            listener.onRefresh();
        }
        if (supportCustomLoadingIcon()) {
            startCustomLoading();
        } else {
            startDefaultLoading();
        }

        contentTopPadding = mHeight;
    }

    /**
     * 是否支持使用自定义下拉loading图标
     */
    private boolean supportCustomLoadingIcon() {
        if (mPullLoadingIconConfig == null) {
            return false;
        }
        return mPullLoadingIconConfig.width > 0 && mPullLoadingIconConfig.height > 0;
    }

    private void startDefaultLoading() {
        loadingView.setScaleType(ImageView.ScaleType.CENTER_INSIDE);
        loadingView.setImageDrawable(getResources().getDrawable(mLoadingDrawableResId));

        handler.postDelayed(new Runnable() {
            int index = 1;

            @Override
            public void run() {
                loadingView.setRotation(30 * index);
                index++;
                handler.postDelayed(this, 60);
            }
        }, 60);
    }

    public void startCustomLoading() {
        if (mPicassoDrawable != null) {
            mPicassoDrawable.start();
        } else {
            // 解析dio文件中的图片路径, 通过图片库加载动图
            RequestCreator requestCreator = FileUtil.getPicassoRequestCreator(getContext().getApplicationContext(), mImageUrl, mFileModule);
            if (requestCreator != null) {
                requestCreator.into(new PicassoDrawableTarget() {
                    @Override
                    public void onResourceReady(PicassoDrawable picassoDrawable, Picasso.LoadedFrom from) {
                        MSCLog.d(TAG, "[LoadingIcon] onResourceReady picassoDrawable:", picassoDrawable);
                        mPicassoDrawable = picassoDrawable;
                        loadingView.setImageDrawable(mPicassoDrawable);
                        loadingView.setScaleType(ImageView.ScaleType.FIT_XY);
                        mPicassoDrawable.start();
                    }

                    @Override
                    public void onLoadFailed(Exception e, Drawable errorDrawable) {
                        MSCLog.d(TAG, "[LoadingIcon] onLoadFailed e:", e);
                        mPicassoDrawable = null;
                        startDefaultLoading();
                    }
                });
            } else {
                startDefaultLoading();
            }
        }
    }

    private void onStopLoading() {
        if (supportCustomLoadingIcon()) {
            stopCustomLoading();
        } else {
            stopDefaultLoading();
        }

        contentTopPadding = 0;
    }

    private void stopDefaultLoading() {
        handler.removeCallbacksAndMessages(null);
        loadingView.setImageDrawable(getResources().getDrawable(mUnLoadingDrawableResId));
    }

    private void stopCustomLoading() {
        if (mPicassoDrawable != null) {
            mPicassoDrawable.stop();
        } else {
            stopDefaultLoading();
        }
    }

    public void setBackgroundTextStyle(boolean isDark) {
        int srcId = mLoadingDrawableResId;
        int srcUnLoadingId = mUnLoadingDrawableResId;
        mLoadingDrawableResId = isDark ? R.drawable.msc_page_refresh_loading_1 : R.drawable.msc_page_refresh_loading_1_white;
        mUnLoadingDrawableResId = isDark ? R.drawable.msc_page_refresh_loading_0 : R.drawable.msc_page_refresh_loading_0_white;
        // 避免 loadingView 的图片样式反复横跳
        if (!supportCustomLoadingIcon()) {
            if (mIsLoading && srcId != mLoadingDrawableResId) {
                loadingView.setImageDrawable(getResources().getDrawable(mLoadingDrawableResId));
            }

            if (!mIsLoading && srcUnLoadingId != mUnLoadingDrawableResId) {
                loadingView.setImageDrawable(getResources().getDrawable(mUnLoadingDrawableResId));
            }
        }
    }

    @Override
    protected void onDetachedFromWindow() {
        if (mIsLoading) {
            onStopLoading();
        }
        mIsLoading = false;
        super.onDetachedFromWindow();
    }
}
