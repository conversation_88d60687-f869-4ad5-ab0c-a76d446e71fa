package com.meituan.msc.modules.preload;

import com.meituan.android.degrade.interfaces.resource.ExecuteResultCallback;
import com.meituan.android.degrade.interfaces.resource.PreloadBlock;
import com.meituan.android.degrade.interfaces.resource.ResourceManager;
import com.meituan.msc.common.perf.PerfEventConstant;
import com.meituan.msc.common.support.java.util.concurrent.CompletableFuture;
import com.meituan.msc.common.support.java.util.function.BiFunction;
import com.meituan.msc.common.utils.MSCResourceWatermarkUtil;
import com.meituan.msc.modules.apploader.IAppLoader;
import com.meituan.msc.modules.apploader.MSCAppLoader;
import com.meituan.msc.modules.container.ContainerStartState;
import com.meituan.msc.modules.engine.MSCHornRollbackConfig;
import com.meituan.msc.modules.engine.MSCRuntime;
import com.meituan.msc.modules.engine.RendererManager;
import com.meituan.msc.modules.engine.RuntimeDestroyReason;
import com.meituan.msc.modules.engine.RuntimeManager;
import com.meituan.msc.modules.engine.RuntimeSource;
import com.meituan.msc.modules.engine.RuntimeStateBeforeLaunch;
import com.meituan.msc.modules.preload.executor.Task;
import com.meituan.msc.modules.preload.executor.TaskExecuteContext;
import com.meituan.msc.modules.reporter.MSCLog;
import com.meituan.msc.modules.service.codecache.CodeCacheManager;
import com.meituan.msc.modules.update.bean.PackageInfoWrapper;
import com.meituan.msc.util.perf.PerfEvent;
import com.meituan.msc.util.perf.PerfEventPhase;
import org.json.JSONObject;


class BasePackagePreloadTask extends Task {

    private static boolean hasCheckCodeCache = false;
    private final CompletableFuture<MSCRuntime> resultFuture = new CompletableFuture<>();
    /**
     * 标识来源，用于日志打印
     */
    private final String source;
    private final String basePkgVersionOfDebug;
    // 预热任务开始执行（非API调用）时Native内存占用
    private long libcMem_b;
    // 预热任务开始执行时Java堆内存
    private long javaMem_b;
    private long startExecuteTime;


    public BasePackagePreloadTask(String source, String basePkgVersionOfDebug) {
        // 这里传入的 id 是固定的，也就意味着同时只能执行一个基础库预加载任务
        super("BasePackagePreload");
        this.source = source;
        this.basePkgVersionOfDebug = basePkgVersionOfDebug;
    }

    public CompletableFuture<MSCRuntime> getResultFuture() {
        return resultFuture;
    }

    public long getLibcMem_b() {
        return libcMem_b;
    }

    public long getJavaMem_b() {
        return javaMem_b;
    }

    public long getStartExecuteTime() {
        return startExecuteTime;
    }

    @Override
    protected void throwException(Throwable e) {
        MSCLog.e(source, e, "[MSC][Preload]preload engine error");
        PreloadManager.getInstance().preloadBaseErrorMsg = "preload engine error:" + e.toString();
        resultFuture.completeExceptionally(e);
    }

    @Override
    protected void execute(TaskExecuteContext taskExecuteContext) {
        libcMem_b = MSCResourceWatermarkUtil.getAppLibcMemByte();
        javaMem_b = MSCResourceWatermarkUtil.getAppUsedJavaMemByte();
        startExecuteTime = System.currentTimeMillis();
        if (ContainerStartState.instance.isContainerLaunching()) {
            MSCLog.i(source, "already launching,cancel preload base");
            PreloadManager.getInstance().preloadBaseErrorMsg = "already launching,cancel preload base";
            resultFuture.complete(null);
            return;
        }
        MSCRuntime mscRuntime = RuntimeManager.createRuntimeIfNotExistBasePreload();
        if (mscRuntime == null) {
            MSCLog.i(source, "already exist base preload");
            PreloadManager.getInstance().setPreloadBaseMetricsInfo("basePreloadExisted", "already exist base preload");
            resultFuture.complete(null);
            return;
        }

        IAppLoader appLoader = mscRuntime.getModule(IAppLoader.class);
        if (!(appLoader instanceof MSCAppLoader)) {
            resultFuture.completeExceptionally(new IllegalArgumentException("AppLoader type error"));
            return;
        }
        if (MSCHornPreloadConfig.enableControlBasePreload()) {
            // 由降级框架接管控制代码执行
            ResourceManager.getInstance().submitPreloadBlock(new PreloadBlock() {
                @Override
                public String getBusinessName() {
                    return "MSC";
                }

                @Override
                public String getPreloadType() {
                    return "basePreload";
                }

                @Override
                public String getBusinessId() {
                    return "mscsdk";
                }

                @Override
                public void onExecute() {
                    MSCLog.i(source, "doBasePackagePreload by degradeFramework");
                    doBasePackagePreload(appLoader, mscRuntime);
                }
            }, new ExecuteResultCallback() {
                @Override
                public void onExecuteAllow() {

                }

                @Override
                public void onExecuteDenied(String deniedReason, JSONObject adopt) {
                    MSCLog.i(source, "doBasePackagePreload is rejected by degradeFramework, reason:" + deniedReason);
                    PreloadManager.getInstance().basePreloadHitControlDetail = adopt.toString();
                    PreloadManager.getInstance().setPreloadBaseMetricsInfo("basePreloadDegradeDenied", deniedReason);
                }
            });
        } else {
            MSCLog.i(source, "doBasePackagePreload by normal");
            doBasePackagePreload(appLoader, mscRuntime);
        }
    }

    private void doBasePackagePreload(IAppLoader appLoader, MSCRuntime mscRuntime) {
        PerfEvent preloadBeginEvent = new PerfEvent(PerfEventConstant.PRELOAD, PerfEventPhase.BEGIN);
        // 自动化测试Log 勿删
        MSCLog.i(source, "[MSC][Preload]preload engine start", mscRuntime, basePkgVersionOfDebug);
        PreloadManager.getInstance().preloadBaseErrorMsg = "base preloading";
        mscRuntime.hasTriggerBasePreload = true;
        mscRuntime.setSource(RuntimeSource.BASE_PRELOAD);
        mscRuntime.setRuntimeStateBeforeLaunch(RuntimeStateBeforeLaunch.BASE_PRELOADING);
        mscRuntime.getMSCAppModule().setBasePkgVersionOfDebugPreload(basePkgVersionOfDebug);
        mscRuntime.setBasePreloadTime(System.currentTimeMillis());
        mscRuntime.getPerfEventRecorder().addEvent(preloadBeginEvent);
        ((MSCAppLoader) appLoader)
                .injectBasePackage(source, basePkgVersionOfDebug)
                .handle(new BiFunction<PackageInfoWrapper, Throwable, Void>() {
                    @Override
                    public Void apply(PackageInfoWrapper packageInfoWrapper, Throwable throwable) {
                        if (throwable != null) {
                            // FIXME: 2023/1/18 启动框架无法抛出异常
                            String preloadBaseErrorMsg = "preload engine fail:"
                                    + (packageInfoWrapper == null ? "null" : packageInfoWrapper.getVersion());
                            PreloadManager.getInstance().setPreloadBaseMetricsInfo("basePreloadExecuteFailed", preloadBaseErrorMsg);
                            MSCLog.i(source, "[MSC][Preload]preload engine fail", mscRuntime);
                            // 清除 WebViewRender 的缓存池，解决 WebView 引用 MSCRuntime导致内存泄露的问题
                            mscRuntime.destroyEngineIfNoCount(RuntimeDestroyReason.toString(RuntimeDestroyReason.BASE_PACKAGE_FAILED));
                            resultFuture.completeExceptionally(throwable);
                        } else {
                            // 预热完成，检查并生成所有的CodeCache数据
                            if (!hasCheckCodeCache) {
                                hasCheckCodeCache = true;
                                // 基础库只检查一次
                                CodeCacheManager.getInstance().createCodeCacheAsync(null, packageInfoWrapper.getVersion(), packageInfoWrapper);
                            }
                            PreloadManager.getInstance().preloadBaseErrorMsg = "preload engine end:" + packageInfoWrapper.getVersion();
                            MSCLog.i(source, "[MSC][Preload]preload engine end:", packageInfoWrapper.getVersion(), mscRuntime);
                            resultFuture.complete(mscRuntime);
                        }
                        return null;
                    }
                });
    }
}
