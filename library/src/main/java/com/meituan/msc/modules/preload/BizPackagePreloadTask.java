package com.meituan.msc.modules.preload;

import android.text.TextUtils;

import com.meituan.android.common.metricx.PreloadInjection;
import com.meituan.android.degrade.interfaces.resource.ExecuteResultCallback;
import com.meituan.android.degrade.interfaces.resource.PreloadBlock;
import com.meituan.android.degrade.interfaces.resource.ResourceManager;
import com.meituan.msc.common.framework.Callback;
import com.meituan.msc.common.perf.PerfEventConstant;
import com.meituan.msc.common.support.java.util.concurrent.CompletableFuture;
import com.meituan.msc.common.support.java.util.function.BiFunction;
import com.meituan.msc.common.utils.MSCResourceWatermarkUtil;
import com.meituan.msc.modules.apploader.IAppLoader;
import com.meituan.msc.modules.apploader.MSCAppLoader;
import com.meituan.msc.modules.apploader.events.AppLoadException;
import com.meituan.msc.modules.container.ContainerStartState;
import com.meituan.msc.modules.engine.MSCHornRollbackConfig;
import com.meituan.msc.modules.engine.MSCRuntime;
import com.meituan.msc.modules.engine.RendererManager;
import com.meituan.msc.modules.engine.RuntimeDestroyReason;
import com.meituan.msc.modules.engine.RuntimeManager;
import com.meituan.msc.modules.engine.RuntimeSource;
import com.meituan.msc.modules.engine.RuntimeStateBeforeLaunch;
import com.meituan.msc.modules.preload.executor.Task;
import com.meituan.msc.modules.preload.executor.TaskExecuteContext;
import com.meituan.msc.modules.reporter.MSCLog;
import com.meituan.msc.util.perf.PerfEvent;
import com.meituan.msc.util.perf.PerfEventPhase;

import org.json.JSONObject;

class BizPackagePreloadTask extends Task {

    private static final String TAG = "BizPackagePreloadTask";
    private final String basePkgVersionOfDebug;
    private final String appId;
    private final String checkUpdateUrl;
    private final String targetPath;
    private final boolean preloadWebViewPage;
    private final String preloadStrategyStr;
    private final Callback<MSCRuntime> callback;

    private final CompletableFuture<MSCRuntime> resultFuture = new CompletableFuture<>();
    // 预热任务开始执行（非API调用）时Native内存占用
    private long libcMem_b;
    // 预热任务开始执行时Java堆内存
    private long javaMem_b;
    // 预热任务开始执行时间
    private long startExecuteTime;
    public long getLibcMem_b() {
        return libcMem_b;
    }

    public long getJavaMem_b() {
        return javaMem_b;
    }

    public long getStartExecuteTime() {
        return startExecuteTime;
    }

    public BizPackagePreloadTask(String basePkgVersionOfDebug, String appId,
                                 String checkUpdateUrl, String targetPath, boolean preloadWebViewPage,
                                 String preloadStrategyStr, Callback<MSCRuntime> callback) {
        super("BizPackagePreloadTask:" + appId);
        this.basePkgVersionOfDebug = basePkgVersionOfDebug;
        this.appId = appId;
        this.checkUpdateUrl = checkUpdateUrl;
        this.targetPath = targetPath;
        this.preloadWebViewPage = preloadWebViewPage;
        this.preloadStrategyStr = preloadStrategyStr;
        this.callback = callback;
    }

    @Override
    protected void execute(TaskExecuteContext taskExecuteContext) {
        libcMem_b = MSCResourceWatermarkUtil.getAppLibcMemByte();
        javaMem_b = MSCResourceWatermarkUtil.getAppUsedJavaMemByte();
        startExecuteTime = System.currentTimeMillis();
        PerfEvent preloadBeginEvent = new PerfEvent(PerfEventConstant.PRELOAD_BIZ, PerfEventPhase.BEGIN);
        // 自动化测试Log 勿删
        MSCLog.i(TAG, "[MSC][Preload]preloadBiz engine start:", appId);

        if (ContainerStartState.instance.isContainerLaunching() && !MSCHornRollbackConfig.get().getConfig().isRollbackPendingPreloadBiz
                && !MSCHornPreloadConfig.inPreloadWithoutPendingList(appId)) {
            // 默认都仅支持jsc恢复。 按照预期，需要在恢复时支持预热webview。
            boolean usePreloadWebViewPage = preloadWebViewPage && !MSCHornRollbackConfig.readConfig().isRollbackPreheatSupportWebView;
            PendingBizPreloadTasksManager.PreloadBizData bizData =
                    new PendingBizPreloadTasksManager.PreloadBizData(appId, targetPath, usePreloadWebViewPage, callback);
            PendingBizPreloadTasksManager.getInstance().addBizPreloadPending(bizData);
            PreloadManager.getInstance().putPreloadBizMetricsInfo(appId, "bizPreloadUnknownError", "biz preload pending");
            resultFuture.complete(null);
            return;
        }

        if (!TextUtils.isEmpty(preloadStrategyStr)) {
            PreloadInjection.notifyPreloadWillStart("After_T3_Preload", preloadStrategyStr);
        }

        MSCRuntime mscRuntime;
        MSCRuntime basePreloadRuntime = RuntimeManager.findBasePreloadRuntimeWithLock();
        // TODO: 2022/5/20 tianbin 添加基础包版本校验逻辑
        if (TextUtils.isEmpty(basePkgVersionOfDebug) && basePreloadRuntime != null) {
            mscRuntime = basePreloadRuntime;
            MSCLog.i(TAG, "reuse runtime:", mscRuntime);
            mscRuntime.setRuntimeStateBeforeLaunch(RuntimeStateBeforeLaunch.BIZ_PRELOADING_FROM_BASE);
            MSCAppLoader mscAppLoader = (MSCAppLoader) mscRuntime.getModule(IAppLoader.class);
            mscAppLoader.setCheckUpdateUrl(checkUpdateUrl);
            mscRuntime.startApp(appId);
            mscRuntime.setIsReuseBasePreload();
            // 消费已预热的基础库，触发再次预热
            long delayMillis = MSCHornPreloadConfig.getBasePreloadDelayWhenUsed() * 1000L;
            PreloadTasksManager.instance.preloadBasePackageAgain(delayMillis);
        } else {
            if (basePreloadRuntime != null) { //基础库预热引擎不满足使用条件，解锁
                basePreloadRuntime.unLock();
            }

            mscRuntime = RuntimeManager.createRuntimeIfNotExist(appId);
            if (mscRuntime == null) {
                resultFuture.completeExceptionally(new AppLoadException(-1, "already exist runtime"));
                PreloadManager.getInstance().putPreloadBizMetricsInfo(appId, "bizPreloadExisted", "biz preload runtime existed");
                return;
            }

            MSCLog.i(TAG, "create runtime:", mscRuntime, "," + basePkgVersionOfDebug);
            mscRuntime.setRuntimeStateBeforeLaunch(RuntimeStateBeforeLaunch.BIZ_PRELOADING_FROM_NEW);
            MSCAppLoader mscAppLoader = (MSCAppLoader) mscRuntime.getModule(IAppLoader.class);
            mscAppLoader.setBasePkgVersionOfDebug(basePkgVersionOfDebug);
            mscAppLoader.setCheckUpdateUrl(checkUpdateUrl);
        }


        IAppLoader appLoader = mscRuntime.getModule(IAppLoader.class);
        // 由降级框架接管业务预热
        if (MSCHornPreloadConfig.enableControlBizPreload()) {
            ResourceManager.getInstance().submitPreloadBlock(new PreloadBlock() {
                @Override
                public String getBusinessName() {
                    return "MSC";
                }

                @Override
                public String getPreloadType() {
                    return "bizPreload";
                }

                @Override
                public String getBusinessId() {
                    return appId;
                }

                @Override
                public void onExecute() {
                    MSCLog.i(TAG, "doBizPackagePreload by degradeFramework");
                    doBizPackagePreload(appLoader, mscRuntime, preloadBeginEvent);
                }
            }, new ExecuteResultCallback() {
                @Override
                public void onExecuteAllow() {
                    // 排除多次预热，首次被管控、第二次未被管控场景
                    PreloadManager.getInstance().bizPreloadHitControlDetail.remove(appId);
                }

                @Override
                public void onExecuteDenied(String deniedReason, JSONObject adopt) {
                    MSCLog.i(TAG, "doBizPackagePreload is rejected by degradeFramework, reason:" + deniedReason);
                    mscRuntime.setRuntimeStateBeforeLaunch(RuntimeStateBeforeLaunch.BIZ_PRELOAD_IS_BLOCK);
                    PreloadManager.getInstance().bizPreloadHitControlDetail.put(appId, adopt.toString());
                    PreloadManager.getInstance().putPreloadBizMetricsInfo(appId, "bizPreloadDegradeDenied", deniedReason);
                }
            });
        } else {
            MSCLog.i(TAG, "doBizPackagePreload by normal");
            doBizPackagePreload(appLoader, mscRuntime, preloadBeginEvent);
        }
    }

    private void doBizPackagePreload(IAppLoader appLoader, MSCRuntime mscRuntime, PerfEvent preloadEvent) {
        mscRuntime.hasTriggerBizPreload = true;
        mscRuntime.setSource(RuntimeSource.BIZ_PRELOAD);
        mscRuntime.setPreloadStrategyStr(preloadStrategyStr);
        mscRuntime.setBasePreloadTime(System.currentTimeMillis());
        mscRuntime.getPerfEventRecorder().addEvent(preloadEvent);
        if (appLoader instanceof MSCAppLoader) {
            ((MSCAppLoader) appLoader).preloadAppPackage(basePkgVersionOfDebug, appId, targetPath, preloadWebViewPage)
                    .handle(new BiFunction<Void, Throwable, Void>() {
                        @Override
                        public Void apply(Void v, Throwable throwable) {
                            if (throwable != null) {
                                MSCLog.i(TAG, "[MSC][Preload]preloadBiz engine fail", appId);
                                PreloadManager.getInstance().putPreloadBizMetricsInfo(appId, "bizPreloadExecuteFailed", "preload engine fail");
                                // 清除 WebViewRender 的缓存池，解决 WebView 引用 MSCRuntime导致内存泄露的问题
                                mscRuntime.destroyEngineIfNoCount(RuntimeDestroyReason.toString(RuntimeDestroyReason.BIZ_PACKAGE_FAILED));
                                resultFuture.completeExceptionally(throwable);
                            } else {
                                MSCLog.i(TAG, "[MSC][Preload]preloadBiz engine end:", appId);
                                PreloadManager.getInstance().preloadBizErrorMsgMap.put(appId, "preload engine end");
                                resultFuture.complete(mscRuntime);
                            }
                            return null;
                        }
                    });
        }
    }
    public CompletableFuture<MSCRuntime> getResultFuture() {
        return resultFuture;
    }
}
