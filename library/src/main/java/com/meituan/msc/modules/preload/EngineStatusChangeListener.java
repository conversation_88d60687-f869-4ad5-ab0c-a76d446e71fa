package com.meituan.msc.modules.preload;

import com.meituan.msc.common.config.MSCConfig;
import com.meituan.msc.common.executor.MSCExecutors;
import com.meituan.msc.common.framework.Callback;
import com.meituan.msc.common.utils.Constants;
import com.meituan.msc.common.utils.DisplayUtil;
import com.meituan.msc.extern.MSCEnvHelper;
import com.meituan.msc.modules.apploader.IAppLoader;
import com.meituan.msc.modules.apploader.IEngineStatusChangeListener;
import com.meituan.msc.modules.engine.MSCHornRollbackConfig;
import com.meituan.msc.modules.engine.MSCRuntime;
import com.meituan.msc.modules.engine.RendererManager;
import com.meituan.msc.modules.engine.ResultCallback;
import com.meituan.msc.modules.reporter.MSCLog;
import com.meituan.msc.modules.update.MSCAppModule;

class EngineStatusChangeListener extends PreloadManagerHolder implements IEngineStatusChangeListener {
    private static final String TAG = "EngineStatusChangeListener";

    public EngineStatusChangeListener(PreloadManager preloadManager) {
        super(preloadManager);
    }

    @Override
    public void onLaunchStatusChanged(MSCRuntime runtime, boolean isLaunched) {
        UnusedPreloadBizAppManager.instance.removePreloadApp(runtime.getAppId());
    }

    @Override
    public void onDestroyed(MSCRuntime runtime, boolean shouldRePreload) {
        if (shouldRePreload) {
            PreloadManager.getInstance().preloadMSCApp(runtime.getAppId(), new Callback<MSCRuntime>() {
                @Override
                public void onSuccess(MSCRuntime data) {
                    MSCLog.i(Constants.PRELOAD_BIZ, "rePreloadBiz after runtime destroy:", data);
                }

                @Override
                public void onFail(String errMsg, Exception error) {
                    MSCLog.e(Constants.PRELOAD_BIZ, error, "rePreloadBiz after runtime destroy error", errMsg);
                }

                @Override
                public void onCancel() {
                }
            });
        } else {
            MSCLog.i(Constants.PRELOAD_BIZ, "EngineStatusChangeListener shouldRePreload is false");
        }
    }

    @Override
    public void onKeepAlive(MSCRuntime runtime) {
        // TODO: 2022/9/13 迁移对应功能时再打开
        long keepAliveTime = MSCConfig.getEngineKeepAliveTime();

        if (MSCHornPreloadConfig.get().shouldPreloadPageWhenKeepAlive() && keepAliveTime > 0) {
            MSCExecutors.runOnUiThreadIdle(new Runnable() {
                @Override
                public void run() {
                    if (!runtime.getModule(IAppLoader.class).isUsable() || runtime.hasContainerAttached()) {
                        return;
                    }
                    MSCLog.i(TAG, "re-preloadPage when engine enter keep alive");

                    RendererManager rendererManager = runtime.getRendererManager();
                    // 进入保活时，全部Activity已销毁，预加载页面以加速再次进入
                    // 为防止在不进首页的情况下错误的预加载首页，检查配置
                    MSCAppModule mscAppModule = runtime.getMSCAppModule();
                    if (!DisplayUtil.isWebViewRender(runtime, mscAppModule.getRootPath())) {
                        MSCLog.i(TAG, "root path is not webview render, don't prelod");
                        return;
                    }

                    if (!(MSCHornRollbackConfig.enableExternalAppPrefSourceLimit() && runtime.getMSCAppModule().getExternalApp())) {
                        rendererManager.preloadHomePage(MSCEnvHelper.getContext(), false);
                    }
                    rendererManager.preloadDefaultResources(MSCEnvHelper.getContext(), new ResultCallback() {
                        @Override
                        public void onReceiveFailValue(Exception e) {
                            MSCLog.i(TAG, "preloadDefaultResources failed:", e != null ? e.toString() : "");
                        }

                        @Override
                        public void onReceiveValue(String value) {
                            MSCLog.i(TAG, "preloadDefaultResources sucessed:", value);
                        }
                    }, false);
                }

            }, 1000);
        }
    }
}
