package com.meituan.msc.modules.preload;

import android.content.Context;
import android.text.TextUtils;

import com.meituan.msc.extern.MSCEnvHelper;
import com.meituan.msc.modules.engine.MSCHornRollbackConfig;
import com.meituan.msc.modules.reporter.MSCLog;
import com.meituan.msc.modules.router.MMPRouterManager;

import org.json.JSONException;
import org.json.JSONObject;

/**
 * 获取mmp那边配置的优选策略
 */
public class MMPStrategyUtil {
    private static final String TAG = "MMPStrategyUtil";
    private static final String SP_PRELOAD_STRATEGY_CONFIG = "sp_msc_preload_strategy_config";
    private static final String KEY_PRELOAD_STRATEGY_CONFIG = "key_preload_strategy_config";
    private static final String KEY_JSON_PRELOAD_STRATEGY_STR = "preloadStrategyStr";
    private static final String YOUXUAN_APP_ID = "gh_84b9766b95bc";

    private volatile static boolean isInited = false;
    private static boolean needCreateMMPWebview = false;
    private static String strategy = "";

    private static void init() {
        if (isInited || !MSCEnvHelper.isInited()) {
            return;
        }
        final Context context = MSCEnvHelper.getContext();
        // 主线程。
        isInited = true;
        String config = MSCEnvHelper.getSharedPreferences(context, SP_PRELOAD_STRATEGY_CONFIG).getString(KEY_PRELOAD_STRATEGY_CONFIG, "");
        if (!TextUtils.isEmpty(config)) {
            try {
                JSONObject obj = new JSONObject(config);
                JSONObject youxuanConfig = obj.optJSONObject(YOUXUAN_APP_ID);
                if (null != youxuanConfig) {
                    strategy = youxuanConfig.optString(KEY_JSON_PRELOAD_STRATEGY_STR);
                    needCreateMMPWebview = "F".equals(strategy);
                }
            } catch (JSONException e) {
                // ignored.
                MSCLog.i(TAG, e.getMessage());
            }

        }
    }

    public static String getStrategy() {
        init();
        return strategy;
    }

    /**
     * 是否需要创建mmp的webview。
     *
     * @return
     */
    public static boolean needCreateMMPWebview() {
        init();
        return needCreateMMPWebview;
    }

    /**
     * 是否迁移到msc
     * @return
     */
    public static boolean isRouteToMsc() {
        return MMPRouterManager.isMMPNeedRouteToMSC(YOUXUAN_APP_ID);
    }

}
