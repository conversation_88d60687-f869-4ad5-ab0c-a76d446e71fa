package com.meituan.msc.modules.preload;

import static com.meituan.msc.common.constant.APPIDConstants.TIAN_XING_JIAN;

import android.os.Build;
import android.support.annotation.Keep;
import android.support.annotation.Nullable;
import android.support.annotation.VisibleForTesting;
import android.text.TextUtils;

import com.google.gson.annotations.SerializedName;
import com.meituan.android.singleton.CityControllerSingleton;
import com.meituan.msc.common.constant.APPIDConstants;
import com.meituan.msc.common.utils.MSCHornUtils;
import com.meituan.msc.extern.MSCEnvHelper;
import com.meituan.msc.lib.interfaces.BaseRemoteConfig;
import com.meituan.msc.modules.api.web.WebViewUtil;
import com.meituan.msc.modules.engine.MSCHornRollbackConfig;

import java.util.Collections;
import java.util.HashMap;
import java.util.Map;
import java.util.Set;

/**
 * 预下载/预加载相关配置
 * https://km.sankuai.com/page/166937023
 */
public class MSCHornPreloadConfig extends BaseRemoteConfig<MSCHornPreloadConfig.Config> {
    private static final String TAG = "MSCHornPreloadConfig";

    @Keep
    public static class Config {
        // 用于gson及其他正常创建的情况
        public Config() {
        }

        @SerializedName("preload_page_to_home")
        public boolean preloadPageToHome = true;  // 预加载page层时加载至首页，有可能不命中

        @SerializedName("preload_page_when_keep_alive")
        public boolean preloadPageWhenKeepAlive = true;  // 保活时预加载页面，可解决空白模板无法使用快照而relaunch较慢的问题，有可能不命中

        @SerializedName("preload_home_skip_app")
        @Nullable
        public String[] preloadHomeSkipApps = new String[]{"mmp_87dffc23944d"};    // 此小程序不预加载首页，用于特殊情况，小程序的主要入口不进入首页，如美团中的团好货

        @SerializedName("enableBlankPagePreload")
        public boolean enableBlankPagePreload = true;  // 预加载含空白模板的WebView，以加快页面启动，将加载主包、基础包，并可能加载页面资源

        @SerializedName("prefetch_earliest_time")
        public long prefetchEarliestTime = 5000;    // 预下载最早允许在距离进程启动此时间长度开始

        // 预热总开关
        @SerializedName("enablePreload")
        public boolean enablePreload = true;

        // 基础库预热开关
        @SerializedName("enableBasePreload")
        public boolean enableBasePreload = true;

        // 业务预热开关
        @SerializedName("enableBizPreload")
        public boolean enableBizPreload = true;

        // 业务预热黑名单
        @SerializedName("preload_black_list")
        public String[] appBlackList = new String[]{};

        // 业务预热重点业务
        @SerializedName("priorityAppList")
        public String[] priorityAppList = null;

        @SerializedName("enablePreCreatePageForLaunch")
        boolean enablePreCreatePageForLaunch = true;
        @SerializedName("disablePreCreatePageForLaunchAppIds")
        public String[] disablePreCreatePageForLaunchAppIds = new String[]{APPIDConstants.XIAO_SHUO};
        /**
         * 基础库被征用后，延迟2s执行再次预热
         */
        public int basePreloadDelayWhenUsed = 2;
        /**
         * 批量检查更新中断场景，在FP之后延迟执行，延迟时间单位s
         */
        public int batchCheckUpdateDelayAfterFP = 5;
        /**
         * 业务预热延迟到启动后执行，在FP之后延迟执行，延迟时间单位s
         */
        public int startPendingPreloadBizTaskAfterFP = 5;
        /**
         * 基础库预热延迟到启动后执行，在FP之后延迟执行，延迟时间单位s
         */
        public int startPreloadBaseTaskAfterFP = 7;
        /**
         * 业务预热不进行pending名单
         */
        public String[] preloadWithoutPendingList = new String[]{"7122f6e193de47c1"};
        /**
         * 是否提前检查MD5，且当资源损坏时删除并重新下载
         */
        public boolean preCheckDDResourceMd5AndRetryDownload = false;
        /**
         * 是否启用前置检查
         */
        public boolean enablePreCheckDDResourceMd5 = false;
        /**
         * 业务预热缓存最大限制个数
         */
        @SerializedName("preload_app_limit_count")
        public int preloadAppLimitCount = 2;
        /**
         * 12.13.400 是否需要预加载WebView
         */
        public boolean enablePreloadWebView;
        /**
         * 12.13.400 是否需要对预加载的WebView注入基础库
         */
        public boolean enableWebviewInjectBasePackage;
        /**
         * 后台初始化WebView延迟时间
         */
        public long backgroundInitWebViewDelayTimeMillis = 500;

        /**
         * 后台初始化WebView退避延迟阈值（超出阈值，将开启延迟退避）
         */
        public int backgroundInitWebViewRetreatDelayThreshold = 100;

        /**
         * 后台初始化WebView退避延迟时间（超出阈值时延迟的时间）
         */
        public long backgroundInitWebViewRetreatDelayTime = 16;

        /**
         * 预热WebView实例延迟时间
         */
        public double webViewCreateDelayTimeRatio = 0.5;

        /**
         * 分段预热-getDefaultUserAgent延迟时间
         */
        public long getDefaultUserAgentDelayTimeMillis = 500;

        /**
         * 分段预热-getDefaultUserAgent延迟次数
         */
        public long getDefaultUserAgentDelayTimes = 3;

        /**
         * 默认delay耗时，基础值暂采用目前独立灰度95线，后续更新
         */
        public long webViewCreateDelayThreshold = 3000;
        /**
         * CPU空闲判定阈值
         */
        public double cpuUsageRatio = 1;

        /**
         * 线程数阈值
         */
        public int threadActiveCount = 300;

        /**
         * 12.13.400 预创建WebView chrome版本号黑名单
         */
        public String[] preloadWebViewChromeVersionBlackList;

        /**
         * 12.15.200 地图在设置Widget的uri时做业务预热
         */
        Set<String> bizPreloadListWhenWidgetSetUri = Collections.singleton(TIAN_XING_JIAN);

        /**
         * 12.15.200 2灰 在preCreateWebViewIfNeed后触发3段
         */
        public boolean invokeAfterPreCreateWebViewIfNeed = true;

        /**
         * 12.15.200 2灰 在OnLaunchParamsCheckFinished后触发3段
         */
        public boolean invokeAfterOnLaunchParamsCheckFinished = true;

        /**
         * 12.15.200 2灰 在业务预热时增加webview创建
         */
        public boolean enableCacheFirstWebViewInBusinessPreload = true;

        /**
         * 12.15.200 2灰 在业务预热时增加webview注入
         */
        public boolean enableWebViewInjectInBusinessPreload = true;

        /**
         * 12.15.200 自研内核初始化完成直接进入第二段
         */
        public boolean mtDisableInitEntrePartTwo = false;
        /**
         * 业务预热场景，预热WebView白名单
         */
        public String[] preloadWebViewWhiteList = new String[]{};

        /**
         * WebView预热 退避滚动+分段预热 总开关
         *
         * 策略字段：preloadScrollRetreatAndSplitStrategy已删除
         * A：预热：退避滚动+四段预热
         * B：预热：退避滚动
         * C：预热：四段预热  --已全量--
         * D：预热
         * E：不预热
         */
        public boolean enableScrollRetreatAndSplit = false;
        /**
         * 12.16.400 是否启用native渲染css文件预解析——总开关
         */
        public boolean disablePreParseCss = true;

        /**
         * 12.16.400 是否启用native渲染css文件预解析——业务预热场景
         */
        public boolean disablePreParseCssWhenBizPreload;


        /**
         * 12.17.400。 共享webview回滚
         */
        public boolean rollbackShareWebView;

        /**
         * 12.17.200 是否禁用FP后的后台检查更新（对齐MMP的热启后台检查更新）
         */
        public boolean disableCheckUpdateAfterFP;

        /**
         * 12.19.400 首页FPS优化开关，涉及3个优化点：CSS预解析、六段预热、滚动退避
         * A 对照组
         * B 预热退避
         * C CSS预解析
         * D 预热退避 + CSS预解析
         * E 六段式预热
         * F 预热退避 + CSS预解析 + 六段式预热
         * G 无WebView预热
         */
        public String homePageFpsOptimizeStrategy;
        /**
         * 12.21.400 业务预热白名单，不被 LRU 清理
         */
        public String[] keepPreloadApps = new String[]{APPIDConstants.YOU_XUAN};

        @SerializedName("enable_external_prefetch_packages")
        public boolean enableExternalPrefetchPackages = false;

        // 12.29.400 指定业务包更新
        public Set<String> enableUpdateBizPackageAppList = Collections.singleton(APPIDConstants.SHAN_GOU);
        // 12.29.200 开启AppRouteTask
        public Set<String> enableAppRouteTaskAppList = Collections.singleton(APPIDConstants.DING_DAN);
        /**
         * 允许管控业务预热
         */
        public boolean enableControlBizPreload = true;
        /**
         * 允许管控基础库预热
         */
        public boolean enableControlBasePreload = true;
        /**
         * 允许管控深度预加载（WebView）
         */
        public boolean enableControlPreloadWebViewPage = true;
        /**
         * 允许预热空白页面（WebView）
         */
        public boolean enableControlPreloadWebViewBlankPage = true;
        /**
         * 允许分段预热（WebView）
         */
        public boolean enableControlWebViewSegmentPreload = true;
        /**
         * 深度预加载延迟时间
         */
        public int deepPreloadDelayTime = 4000;
        /**
         * 业务预热页面限制
         */
        public int bizPreloadPageLimitCount = 5;
        /**
         * 业务预热多页面预热白名单
         */
        public Set<String> bizPreloadMultiPageAppWhiteList = Collections.singleton(APPIDConstants.YI_YAO);
    }

    private static MSCHornPreloadConfig sInstance;

    public static MSCHornPreloadConfig get() {
        if (sInstance == null) {
            synchronized (MSCHornPreloadConfig.class) {
                if (sInstance == null) {
                    sInstance = new MSCHornPreloadConfig();
                }
            }
        }
        return sInstance;
    }

    private MSCHornPreloadConfig() {
        super("msc_preload", Config.class);
    }

    @Override
    protected Map<String, Object> getHornQuery() {
        Map<String, Object> extra = new HashMap<>();
        extra.put("cityId", CityControllerSingleton.getInstance().getCityId());
        if (MSCEnvHelper.isInited()) {
            // 若msc未初始化，也需要让horn配置可用。
            extra.put("chromeVersion", WebViewUtil.getChromeWebViewVersion(MSCEnvHelper.getContext()));
            extra.put("deviceLevel", com.meituan.metrics.util.DeviceUtil.getDeviceLevel(MSCEnvHelper.getContext()).getValue());
        }
        String manufacturer = Build.MANUFACTURER;
        if (!TextUtils.isEmpty(manufacturer)) {
            extra.put("manufacturer", manufacturer);
        }
        return extra;
    }

    @Override
    protected void onRemoteConfigChanged(String rawConfigString) {
        super.onRemoteConfigChanged(rawConfigString);
        // if (startPreload) {
        //                    // TODO by chendacai: 2/25/22 这里的canPreloadMultiTime后面配置为Horn开关，startPreload 和 startPrefetch 也配置为Horn开关，且能在三方App中配置的
        //                    LegacyPreloadManager.preload(false);
        //                }
        Config tempConfig = parseRemoteConfig(rawConfigString, false);
        if (null != tempConfig) {
            this.config.preCheckDDResourceMd5AndRetryDownload = tempConfig.preCheckDDResourceMd5AndRetryDownload;
            this.config.enablePreCheckDDResourceMd5 = tempConfig.enablePreCheckDDResourceMd5;
            this.config.disableCheckUpdateAfterFP = tempConfig.disableCheckUpdateAfterFP;
            this.config.preloadPageToHome = tempConfig.preloadPageToHome;
            this.config.preloadPageWhenKeepAlive = tempConfig.preloadPageWhenKeepAlive;
            this.config.enableBlankPagePreload = tempConfig.enableBlankPagePreload;
            this.config.enablePreload = tempConfig.enablePreload;
            this.config.enableBasePreload = tempConfig.enableBasePreload;
            this.config.enableBizPreload = tempConfig.enableBizPreload;
            this.config.enablePreCreatePageForLaunch = tempConfig.enablePreCreatePageForLaunch;
            this.config.bizPreloadListWhenWidgetSetUri = tempConfig.bizPreloadListWhenWidgetSetUri;
            this.config.mtDisableInitEntrePartTwo = tempConfig.mtDisableInitEntrePartTwo;
            this.config.preloadWebViewWhiteList = tempConfig.preloadWebViewWhiteList;
        }
        // 配置更新，homePageFpsOptimizeStrategy值需保持前后一致，无需更新
    }

    public boolean allowPreloadPageToHome(String appId) {
        return config.preloadPageToHome && !shouldSkipPreloadHomeByAppId(appId);
    }

    public boolean shouldPreloadPageWhenKeepAlive() {
        return config.preloadPageWhenKeepAlive;
    }

    public boolean shouldSkipPreloadHomeByAppId(String appId) {
        if (config.preloadHomeSkipApps != null) {
            for (String skipApp : config.preloadHomeSkipApps) {
                if (TextUtils.equals(appId, skipApp)) {
                    return true;
                }
            }
        }
        return false;
    }

    public boolean enableBlankPagePreload() {
        return config.enableBlankPagePreload;
    }

    public long prefetchEarliestTime() {
        return config.prefetchEarliestTime;
    }

    public static boolean enablePreload() {
        return MSCHornPreloadConfig.get().config.enablePreload;
    }

    public static boolean enableBasePreload() {
        return enablePreload() && MSCHornPreloadConfig.get().config.enableBasePreload;
    }

    public static boolean enableBizPreload() {
        return enablePreload() && MSCHornPreloadConfig.get().config.enableBizPreload;
    }

    @VisibleForTesting
    String[] getAppBlackList() {
        return config.appBlackList;
    }

    public static boolean inPreloadAppBlackList(String appId) {
        return inPreloadAppBlackList(get(), appId);
    }

    @VisibleForTesting
    static boolean inPreloadAppBlackList(MSCHornPreloadConfig config, String appId) {
        String[] appBlackList = config.getAppBlackList();
        if (appBlackList == null || appId == null) {
            return false;
        }

        for (String id : appBlackList) {
            if (appId.equals(id)) {
                return true;
            }
        }
        return false;
    }

    public static String[] getPriorityAppList() {
        return MSCHornPreloadConfig.get().config.priorityAppList;
    }

    public static int getBasePreloadDelayWhenUsed() {
        return MSCHornPreloadConfig.get().config.basePreloadDelayWhenUsed;
    }

    public static boolean enablePreCreatePageForLaunch() {
        return MSCHornPreloadConfig.get().config.enablePreCreatePageForLaunch;
    }

    public static boolean inPreloadWithoutPendingList(String appId) {
        return MSCHornUtils.isMatchWhiteConfigRule(get().config.preloadWithoutPendingList, appId);
    }

    public static boolean preCheckDDResourceMd5AndRetryDownload() {
        return get().config.preCheckDDResourceMd5AndRetryDownload;
    }

    public static boolean enablePreCheckDDResourceMd5() {
        return get().config.enablePreCheckDDResourceMd5;
    }

    public static long getBackgroundInitWebViewDelayTime() {
        return get().config.backgroundInitWebViewDelayTimeMillis;
    }


    public static int getBackgroundInitWebViewRetreatDelayThreshold() {
        return get().config.backgroundInitWebViewRetreatDelayThreshold;
    }


    public static long getBackgroundInitWebViewRetreatDelayTime() {
        return get().config.backgroundInitWebViewRetreatDelayTime;
    }

    public static double getWebViewCreateDelayRatio() {
        return get().config.webViewCreateDelayTimeRatio;
    }

    public static long getWebViewCreateDelayThreshold() {
        return get().config.webViewCreateDelayThreshold;
    }

    public static double getCPUUsageRatio() {
        return get().config.cpuUsageRatio;
    }

    public static int getThreadActiveCount() {
        return get().config.threadActiveCount;
    }

    // 3段预热总开关
    public static boolean needPreloadWebView() {
        return needPreloadWebView(get());
    }

    public static boolean needWebViewInjectBasePackage() {
        return get().config.enableWebviewInjectBasePackage;
    }

    // 12.15.200
    public static boolean needInvokeAfterPreCreateWebViewIfNeed() {
        return get().config.invokeAfterPreCreateWebViewIfNeed;
    }

    // 12.15.200
    public static boolean needInvokeAfterOnLaunchParamsCheckFinished() {
        return get().config.invokeAfterOnLaunchParamsCheckFinished;
    }

    // 12.15.200
    public static boolean enableCacheFirstWebViewInBusinessPreload() {
        return get().config.enableCacheFirstWebViewInBusinessPreload;
    }

    // 12.15.200
    public static boolean enableWebViewInjectInBusinessPreload() {
        return get().config.enableWebViewInjectInBusinessPreload;
    }

    public static boolean getMtInitEntrePartTwo() {
        return !get().config.mtDisableInitEntrePartTwo;
    }

    @VisibleForTesting
    static boolean needPreloadWebView(MSCHornPreloadConfig configParam) {
        if (!configParam.config.enablePreloadWebView || "G".equals(configParam.config.homePageFpsOptimizeStrategy)) {
            return false;
        }
        if (configParam.config.preloadWebViewChromeVersionBlackList != null) {
            String chromeVersion = WebViewUtil.getChromeWebViewVersion(MSCEnvHelper.getContext());
            for (String version : configParam.config.preloadWebViewChromeVersionBlackList) {
                if (TextUtils.equals(version, chromeVersion)) {
                    return false;
                }
            }
        }
        return true;
    }

    public static boolean isInBizPreloadListWhenWidgetSetUri(String appId) {
        return get().config.bizPreloadListWhenWidgetSetUri != null && get().config.bizPreloadListWhenWidgetSetUri.contains(appId);
    }

    public static boolean isPreloadWebViewAtBizPreload(String appId) {
        String[] preloadWebViewWhiteList = get().config.preloadWebViewWhiteList;
        if (preloadWebViewWhiteList == null) {
            return false;
        }
        for (String id : preloadWebViewWhiteList) {
            if (TextUtils.equals(appId, id)) {
                return true;
            }
        }
        return false;
    }

    public static boolean enableScrollRetreatAndSplit() {
        return get().config.enableScrollRetreatAndSplit;
    }

    public static long getDefaultUserAgentDelayTimeMillis() {
        return get().config.getDefaultUserAgentDelayTimeMillis;
    }

    public static long getDefaultUserAgentDelayTimes() {
        return get().config.getDefaultUserAgentDelayTimes;
    }

    public static boolean disablePreParseCss() {
        return get().config.disablePreParseCss;
    }

    public static boolean disablePreParseCssWhenBizPreload() {
        return get().config.disablePreParseCssWhenBizPreload;
    }


    public static boolean rollbackShareWebView() {
        return get().config.rollbackShareWebView;
    }

    public static String getHomePageFpsOptimizeStrategy() {
        return get().config.homePageFpsOptimizeStrategy;
    }

    public static boolean isHomePageFpsOptimizeByWebViewRetreat() {
        // WebView预热退避首页滚动的优化策略
        return "B".equals(getHomePageFpsOptimizeStrategy()) || "D".equals(getHomePageFpsOptimizeStrategy()) || "F".equals(getHomePageFpsOptimizeStrategy());
    }

    public static boolean isHomePageFpsOptimizeByCssRetreat() {
        // CSS预解析退避首页滚动的优化策略
        return "C".equals(getHomePageFpsOptimizeStrategy()) || "D".equals(getHomePageFpsOptimizeStrategy()) || "F".equals(getHomePageFpsOptimizeStrategy());
    }

    public static boolean isHomePageFpsOptimizeByWebViewStep() {
        // 多段预热的优化策略
        return "E".equals(getHomePageFpsOptimizeStrategy()) || "F".equals(getHomePageFpsOptimizeStrategy());
    }

    public boolean disableCleanPreload(String firstPreloadAppId) {
        String[] keepPreloadApps = get().getConfig().keepPreloadApps;
        if (keepPreloadApps == null) {
            return false;
        }
        for (String appId : keepPreloadApps) {
            if (TextUtils.equals(firstPreloadAppId, appId)) {
                return true;
            }
        }
        return false;
    }

    public static boolean disablePreCreatePageForLaunchByAppId(String id) {
        String[] appIds = get().getConfig().disablePreCreatePageForLaunchAppIds;
        if (appIds == null) {
            return false;
        }
        for (String appId : appIds) {
            if (TextUtils.equals(id, appId)) {
                return true;
            }
        }
        return false;
    }

    public static boolean enableExternalPrefetchPackages() {
        return get().config.enableExternalPrefetchPackages;
    }

    public static boolean enableUpdateBizPackageAppList(String appId) {
        return MSCHornUtils.isMatchWhiteConfigRule(get().config.enableUpdateBizPackageAppList, appId);
    }

    public static boolean enableAppRouteTask(String appId) {
        Set<String> enableAppRouteTaskAppList = get().config.enableAppRouteTaskAppList;
        return enableAppRouteTaskAppList != null &&
                (enableAppRouteTaskAppList.contains("__ALL__") || enableAppRouteTaskAppList.contains(appId));
    }

    public static boolean enableControlBizPreload() {
        return get().config.enableControlBizPreload;
    }

    public static boolean enableControlBasePreload() {
        return get().config.enableControlBasePreload;
    }

    public static boolean enableControlPreloadWebViewPage() {
        return get().config.enableControlPreloadWebViewPage;
    }

    public static boolean enableControlPreloadWebViewBlankPage() {
        return get().config.enableControlPreloadWebViewBlankPage;
    }

    public static boolean enableControlWebViewSegmentPreload() {
        return get().config.enableControlWebViewSegmentPreload;
    }

    public static int getDeepPreloadDelayTime() {
        return get().config.deepPreloadDelayTime;
    }

    public static boolean enableBizPreloadMultiPage(String appId) {
        return MSCHornUtils.isMatchWhiteConfigRule(get().config.bizPreloadMultiPageAppWhiteList, appId);
    }

    public static int bizPreloadPageLimitCount() {
        return get().config.bizPreloadPageLimitCount;
    }
}
