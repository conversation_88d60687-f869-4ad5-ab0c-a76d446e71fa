package com.meituan.msc.modules.preload;

import android.content.Context;
import android.support.annotation.Keep;
import android.support.annotation.Nullable;

import com.meituan.msc.common.framework.Callback;
import com.meituan.msc.common.lib.preload.IMSCPreloadBiz;
import com.meituan.msc.common.lib.preload.PreloadMSCBizCallback;
import com.meituan.msc.common.lib.preload.PreloadMSCBizData;
import com.meituan.msc.extern.MSCEnvHelper;
import com.meituan.msc.modules.engine.MSCHornRollbackConfig;
import com.meituan.msc.modules.engine.MSCRuntime;
import com.meituan.msc.modules.reporter.MSCLog;
import com.sankuai.common.utils.ProcessUtils;
import com.sankuai.meituan.serviceloader.annotation.ServiceLoaderInterface;

@Keep
@ServiceLoaderInterface(key = "mscPreloadBiz", interfaceClass = IMSCPreloadBiz.class)
public class MSCPreloadBizImpl implements IMSCPreloadBiz {
    private static final String TAG = "MSCPreloadBizImpl";

    @Override
    public void preloadBiz(Context context, String appId, String targetPath, PreloadMSCBizCallback callback) {
        PreloadManager.getInstance().preloadBiz(context, appId, targetPath, new CallbackWrapper(callback));
    }

    @Override
    public void preloadBiz(Context context, String appId, String targetPath, boolean preloadWebViewPage,
                           String preloadStrategyStr, PreloadMSCBizCallback callback) {
        PreloadManager.getInstance().preloadBiz(context, appId, targetPath, preloadWebViewPage, preloadStrategyStr, new CallbackWrapper(callback));
    }

    @Override
    public void preloadBizWebViewOnly(Context context, final String appId, PreloadMSCBizCallback callback) {
        MSCLog.i(TAG, "#preloadBizWebViewOnly, start");
        if (!ProcessUtils.isMainProcess(context)) {
            String errorMsg = appId + " call preloadBiz at sub process, cancel preloadBizWebViewOnly";
            MSCLog.i(TAG, errorMsg);
            if (callback != null) {
                callback.onCancel();
            }
            return;
        }
        if (!MSCEnvHelper.isInited()) {
            MSCEnvHelper.startHostInit(context);
        }
        final Callback<MSCRuntime> callbackInner = new Callback<MSCRuntime>() {
            @Override
            public void onSuccess(MSCRuntime data) {
                MSCLog.i(TAG, "#preloadBizWebViewOnly, success");
                if (null != callback) {
                    PreloadMSCBizData mscBizData = new PreloadMSCBizData();
                    mscBizData.appId = appId;
                    mscBizData.preloadWebView = true;
                    callback.onSuccess(mscBizData);
                }
            }

            @Override
            public void onFail(String errMsg, Exception error) {
                MSCLog.i(TAG, "#preloadBizWebViewOnly, fail");
                if (null != callback) {
                    callback.onFail(errMsg, error);
                }
            }

            @Override
            public void onCancel() {
                MSCLog.i(TAG, "#preloadBizWebViewOnly, cancel");
                if (null != callback) {
                    callback.onCancel();
                }
            }
        };
        PreloadTasksManager.instance.preloadMSCAppBizWebViewOnly(appId, callbackInner);
    }

    @Override
    public void preloadMSCAppSupportSubProcessBiz(Context context, String appId, String targetPath, boolean preloadWebViewPage, PreloadMSCBizCallback callback) {
        PreloadManager.getInstance().preloadMSCAppSupportSubProcessBiz(context, appId, targetPath, preloadWebViewPage, new CallbackWrapper(callback));
    }

    private static class CallbackWrapper implements Callback<PreloadResultData> {

        private final PreloadMSCBizCallback callback;

        CallbackWrapper(@Nullable PreloadMSCBizCallback callback) {
            this.callback = callback;
        }

        @Override
        public void onSuccess(PreloadResultData data) {
            if (null != callback) {
                callback.onSuccess(convert(data));
            }
        }

        @Override
        public void onFail(String errMsg, Exception error) {
            if (null != callback) {
                callback.onFail(errMsg, error);
            }
        }

        @Override
        public void onCancel() {
            if (null != callback) {
                callback.onCancel();
            }
        }

        private PreloadMSCBizData convert(PreloadResultData data) {
            PreloadMSCBizData ret = new PreloadMSCBizData();
            ret.preloadWebView = data.preloadWebView;
            ret.appId = data.appId;
            ret.targetPath = data.targetPath;
            return ret;
        }
    }
}
