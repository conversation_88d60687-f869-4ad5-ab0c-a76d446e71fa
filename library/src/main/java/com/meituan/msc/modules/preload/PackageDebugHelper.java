package com.meituan.msc.modules.preload;

import android.support.annotation.StringDef;

import com.meituan.msc.extern.MSCEnvHelper;
import com.meituan.msc.modules.container.ContainerDebugLaunchData;
import com.meituan.msc.modules.engine.MSCRuntime;

import java.lang.annotation.Retention;
import java.lang.annotation.RetentionPolicy;

/**
 * 包模块AOP操作辅助类，线上环境返回空，线下环境会进行方法替换
 * com.meituan.msc.dev.aop.PackageDebugAop
 */
public class PackageDebugHelper {

    // AOP处理不支持static方法，所以用了单例
    public static final PackageDebugHelper instance = new PackageDebugHelper();

    public String getBasePkgVersionOfDebug(String mscVersionOfLaunch) {
        if (MSCEnvHelper.getEnvInfo().isProdEnv()) {
            return null;
        }
        return mscVersionOfLaunch;
    }

    public String getCheckUpdateUrlOfIDEPreview(String appId, ContainerDebugLaunchData debugLaunchData) {
        if (MSCEnvHelper.getEnvInfo().isProdEnv()) {
            return null;
        }
        return debugLaunchData.getCheckUpdateUrl();
    }

    @BasePackageEnv
    public String getBasePackageEnv() {
        return BASE_PACKAGE_ENV_PROD;
    }

    public boolean isBasePackageVersionMatchForDebug(ContainerDebugLaunchData launchData, MSCRuntime runtime) {
        return true;
    }

    public static final String BASE_PACKAGE_ENV_TEST = "base_package_env_test";
    public static final String BASE_PACKAGE_ENV_PROD = "base_package_env_prod";

    @StringDef({BASE_PACKAGE_ENV_TEST, BASE_PACKAGE_ENV_PROD})
    @Retention(RetentionPolicy.SOURCE)
    public @interface BasePackageEnv {
    }
}
