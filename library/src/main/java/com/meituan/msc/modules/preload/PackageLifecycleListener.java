//package com.meituan.msc.modules.preload;
//
//class PackageLifecycleListener extends PreloadManagerHolder implements IPackageLifecycleListener {
//
//    public PackageLifecycleListener(PreloadManager preloadManager) {
//        super(preloadManager);
//    }
//
//    public void onPackageUpdated(PackageInfo newPackage, PackageInfo oldPackage) {
//        // [策略] 包更新后，销毁过期的任务，重新创建
//    }
//
//    public void onPackageRemoved(PackageInfo removedPackage) {
//        // [策略] 包删除后，销毁过期的任务，重新创建
//    }
//}
