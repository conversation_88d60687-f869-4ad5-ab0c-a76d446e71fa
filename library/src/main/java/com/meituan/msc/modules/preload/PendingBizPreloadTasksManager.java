package com.meituan.msc.modules.preload;

import android.support.annotation.NonNull;
import android.support.annotation.Nullable;
import android.text.TextUtils;

import com.meituan.msc.common.executor.MSCExecutors;
import com.meituan.msc.common.framework.Callback;
import com.meituan.msc.common.utils.MPConcurrentHashMap;
import com.meituan.msc.modules.api.ApiException;
import com.meituan.msc.modules.container.ContainerStartState;
import com.meituan.msc.modules.engine.MSCHornRollbackConfig;
import com.meituan.msc.modules.engine.MSCRuntime;
import com.meituan.msc.modules.reporter.MSCLog;

import java.util.Iterator;
import java.util.Objects;
import java.util.concurrent.BlockingQueue;
import java.util.concurrent.LinkedBlockingQueue;

public class PendingBizPreloadTasksManager {

    private static final String TAG = "PendingBizPreloadTasksManager";
    private static volatile PendingBizPreloadTasksManager instance;
    // 缓存引擎预热的任务
    private final BlockingQueue<PreloadBizData> bizPreloadPendingQueue = new LinkedBlockingQueue<>();
    // 缓存页面预热的任务
    private final BlockingQueue<PreloadBizData> bizPreloadPagePendingQueue = new LinkedBlockingQueue<>();
    private final MPConcurrentHashMap<String, PreloadBizData> bizPreloadPendingMap = new MPConcurrentHashMap<>();

    public static PendingBizPreloadTasksManager getInstance() {
        if (instance == null) {
            synchronized (PendingBizPreloadTasksManager.class) {
                if (instance == null) {
                    instance = new PendingBizPreloadTasksManager();
                }
            }
        }
        return instance;
    }

    private PendingBizPreloadTasksManager() {
    }

    public void addBizPreloadPending(PreloadBizData bizData) {
        MSCLog.i(TAG, "addBizPreloadPending", bizData);
        bizData.addToQueueTime = System.currentTimeMillis();
        bizPreloadPendingQueue.add(bizData);
        bizPreloadPendingMap.put(bizData.appId, bizData);
        PreloadManager.getInstance().preloadBizErrorMsgMap.put(bizData.appId, "biz preload pending");
    }

    public PreloadBizData getBizDataByAppId(String appId) {
        PreloadBizData preloadBizData = bizPreloadPendingMap.get(appId);
        bizPreloadPendingMap.remove(appId);
        return preloadBizData;
    }

    public void startPreloadPendingBizs() {
        if (MSCHornRollbackConfig.get().getConfig().isRollbackPendingPreloadBiz) {
            MSCLog.i(TAG, "isRollbackPendingPreloadBiz is true");
            return;
        }
        // fixme 预热被启动打断，恢复预热场景时，未保持和之前需求预热一样的需求（若预热需求是jsc+webview，恢复仅包含jsc）。
        MSCExecutors.submit(new MSCExecutors.Serialized.SubmitRunnable(new Runnable() {
            @Override
            public void run() {
                MSCLog.i(TAG, "startPreloadPendingBizs");
                doPreloadPendingBiz(bizPreloadPendingQueue.poll());
            }
        }, MSCHornPreloadConfig.get().getConfig().startPendingPreloadBizTaskAfterFP * 1000L));
    }

    private void doPreloadPendingBiz(@Nullable PreloadBizData bizData) {
        if (bizData == null) {
            MSCLog.i(TAG, "doPreloadPendingBiz finish");
            return;
        }
        MSCLog.i(TAG, "doPreloadPendingBiz", bizData);
        bizData.preloadStartTime = System.currentTimeMillis();
        PreloadManager.getInstance().preloadMSCApp(bizData.appId, bizData.appId, new Callback<MSCRuntime>() {
            @Override
            public void onSuccess(MSCRuntime data) {
                MSCLog.i(TAG, "doPreloadPendingBiz success", bizData);
                if (bizData.callback != null) {
                    bizData.callback.onSuccess(data);
                }
                // 当有容器启动时 暂停执行队列任务
                if (ContainerStartState.instance.isContainerLaunching()) {
                    MSCLog.i(TAG, "cancel doPreloadPendingBiz when container launching", bizData);
                    return;
                }
                doPreloadPendingBiz(bizPreloadPendingQueue.poll());
            }

            @Override
            public void onFail(String errMsg, Exception error) {
                MSCLog.e(TAG, error, "doPreloadPendingBiz", errMsg, bizData);
                if (bizData.callback != null) {
                    bizData.callback.onFail(errMsg, error);
                }
            }

            @Override
            public void onCancel() {
                MSCLog.i(TAG, "doPreloadPendingBiz onCancel", bizData);
                if (bizData.callback != null) {
                    bizData.callback.onCancel();
                }
            }
        }, bizData.preloadWebview);
    }

    public boolean containsTask(@NonNull PreloadBizData preloadBizData) {
        return bizPreloadPendingQueue.contains(preloadBizData);
    }

    public boolean removeTask(String appId) {
        if (appId == null) {
            return false;
        }
        Iterator<PreloadBizData> iterator = bizPreloadPendingQueue.iterator();
        while (iterator.hasNext()) {
            PreloadBizData preloadBizData = iterator.next();
            if (appId.equals(preloadBizData.appId)) {
                iterator.remove();
                return true;
            }
        }
        return false;
    }

    public int getPendingPreloadTaskSize() {
        return bizPreloadPendingQueue.size();
    }

    public void addPagePreloadPendingTask(PreloadBizData preloadBizPageData) {
        if (preloadBizPageData == null) {
            return;
        }
        MSCLog.i(TAG, "addPagePreloadPendingTask", preloadBizPageData.appId);
        bizPreloadPagePendingQueue.add(preloadBizPageData);
    }

    public void executePagePreloadPendingTask(String appId) {
        MSCLog.i(TAG, "executePagePreloadPendingTask", appId);
        if (TextUtils.isEmpty(appId) || !MSCHornPreloadConfig.enableBizPreloadMultiPage(appId)) {
            return;
        }
        Iterator<PreloadBizData> iterator = bizPreloadPagePendingQueue.iterator();
        while (iterator.hasNext()) {
            PreloadBizData preloadBizData = iterator.next();
            if (appId.equals(preloadBizData.appId)) {
                PreloadTasksManager.instance.preloadBizPage(preloadBizData.appId, preloadBizData.targetPath, preloadBizData.preloadWebview, preloadBizData.callback);
                iterator.remove();
            }
        }
    }

    public void removePagePreloadPendingTask(String appId, String errorMsg) {
        MSCLog.i(TAG, "removePagePreloadPendingTask", appId);
        if (TextUtils.isEmpty(appId) || !MSCHornPreloadConfig.enableBizPreloadMultiPage(appId)) {
            return;
        }
        Iterator<PreloadBizData> iterator = bizPreloadPagePendingQueue.iterator();
        while (iterator.hasNext()) {
            PreloadBizData preloadBizData = iterator.next();
            if (appId.equals(preloadBizData.appId)) {
                MSCLog.d(TAG, "cancel preloadPage", preloadBizData.targetPath);
                if (preloadBizData.callback != null) {
                    preloadBizData.callback.onFail(errorMsg, new ApiException(errorMsg));
                }
                iterator.remove();
            }
        }
    }

    public static class PreloadBizData {
        public String appId;
        public String targetPath;
        public boolean preloadWebview;
        public Callback<MSCRuntime> callback;
        public long addToQueueTime;
        public long preloadStartTime;

        public PreloadBizData(String appId, String targetPath, Callback<MSCRuntime> callback) {
            this(appId, targetPath, false, callback);
        }

        public PreloadBizData(String appId, String targetPath, boolean preloadWebview, Callback<MSCRuntime> callback) {
            this.appId = appId;
            this.targetPath = targetPath;
            this.callback = callback;
            this.preloadWebview = preloadWebview;
        }

        @Override
        public boolean equals(Object o) {
            if (this == o) return true;
            if (o == null || getClass() != o.getClass()) return false;
            PreloadBizData that = (PreloadBizData) o;
            return Objects.equals(appId, that.appId);
        }

        @Override
        public int hashCode() {
            return Objects.hash(appId);
        }

        @Override
        public String toString() {
            return "PreloadBizData{" +
                    "appId='" + appId + '\'' +
                    ", targetPath='" + targetPath + '\'' +
                    '}';
        }
    }
}
