package com.meituan.msc.modules.preload;

import android.content.Context;

import com.meituan.msc.common.framework.Callback;
import com.meituan.msc.common.utils.Constants;
import com.meituan.msc.common.utils.MPConcurrentHashMap;
import com.meituan.msc.extern.MSCEnvHelper;
import com.meituan.msc.modules.apploader.IAppLoader;
import com.meituan.msc.modules.engine.MSCHornRollbackConfig;
import com.meituan.msc.modules.engine.MSCRuntime;
import com.meituan.msc.modules.engine.RuntimeManager;
import com.meituan.msc.modules.reporter.MSCLog;
import com.meituan.msc.modules.update.pkg.PackageLoadManager;
import com.sankuai.common.utils.ProcessUtils;

import java.util.Map;


public class PreloadManager {

    private static final PreloadManager INSTANCE = new PreloadManager();
    private static final String TAG = "PreloadManager";
    public String preloadBaseErrorMsg = "no trigger preload base";
    public String preloadBaseMissReason = "basePreloadNoTrigger";
    public String basePreloadHitControlDetail = "";
    public MPConcurrentHashMap<String, String> bizPreloadHitControlDetail = new MPConcurrentHashMap<>();
    public MPConcurrentHashMap<String, String> preloadBizErrorMsgMap = new MPConcurrentHashMap<>();
    public MPConcurrentHashMap<String, String> preloadBizMissReasonMap = new MPConcurrentHashMap<>();

    private Map<String, PreloadStrategy> appPreloadStrategyMap;
    private Map<String, PreloadStrategy> afterT3PreloadStrategyMap;
    // 最近触发页面预热的小程序业务
    private String lastPagePreloadAppId = "";

    /**
     * 预热类型：由应用启动触发，也包括首页启动触发
     * 内部会限制为整个启动过程中允许实际触发一次预热，防止占用资源过多
     */
    public static final int PRELOAD_TYPE_APP_LAUNCH = 1;
    /**
     * 预热类型：由tab切换触发，反复切换tab可多次触发
     * 在上限之内允许预热
     */
    public static final int PRELOAD_TYPE_SWITCH_TAB = 2;
    /**
     * 预热类型：由native页面切换触发，可重复触发
     */
    public static final int PRELOAD_TYPE_ENTER_PAGE = 3;

    public static PreloadManager getInstance() {
        return INSTANCE;
    }

    private PreloadManager() {
    }

    public String getLastPagePreloadAppId() {
        return lastPagePreloadAppId;
    }

    public void setLastPagePreloadAppId(String lastPagePreloadAppId) {
        this.lastPagePreloadAppId = lastPagePreloadAppId;
    }

    public PreloadStrategy getAppPreloadStrategy(String appId) {
        if (appPreloadStrategyMap == null) {
            return null;
        }
        return appPreloadStrategyMap.get(appId);
    }

    public void addAppPreloadStrategy(String appId, PreloadStrategy preloadStrategy) {
        if (appPreloadStrategyMap == null) {
            appPreloadStrategyMap = new MPConcurrentHashMap<>();
        }
        appPreloadStrategyMap.put(appId, preloadStrategy);
    }

    public PreloadStrategy getAfterT3PreloadStrategy(String appId) {
        if (afterT3PreloadStrategyMap == null) {
            return null;
        }
        return afterT3PreloadStrategyMap.get(appId);
    }

    public void addAfterT3PreloadStrategy(String appId, PreloadStrategy preloadStrategy) {
        if (afterT3PreloadStrategyMap == null) {
            afterT3PreloadStrategyMap = new MPConcurrentHashMap<>();
        }
        afterT3PreloadStrategyMap.put(appId, preloadStrategy);
    }

    /**
     * 初始化
     */
    public void init() {
        if (mIsInitialized) {
            return;
        }
        synchronized (this) {
            if (mIsInitialized) {
                return;
            }
            realInit();
            mIsInitialized = true;
        }
    }

    private void realInit() {
        if (!MSCHornPreloadConfig.enablePreload()) {
            // 如果未启用预加载，直接退出
            getInstance().setPreloadBaseMetricsInfo("basePreloadHornRollback", "base preload horn rollback");
            MSCLog.i("Preload", "enablePreload is off");
            return;
        }

        RuntimeManager.init();
        RuntimeManager.setEngineStatusChangeListener(new EngineStatusChangeListener(this));

        PreloadTasksManager.instance.preloadBiz();
        PreloadTasksManager.instance.preloadBase();

        // App 启动触发基础库拉取
        PackageLoadManager.getInstance().checkUpdateLatestBasePackage();
    }

    public void ensureInit() {
        MSCEnvHelper.ensureFullInited();
        init();
    }

    private boolean noInitOrNotMainProcess() {
        if (!MSCEnvHelper.isInited()) {
            MSCLog.i(TAG, "MSC not inited");
            return true;
        }
        if (MSCEnvHelper.getContext() == null) {
            MSCLog.i(TAG, "isMainProcess context is null");
            return true;
        }
        return !ProcessUtils.isMainProcess(MSCEnvHelper.getContext());
    }

    private volatile boolean mIsInitialized = false;

    /**
     * @param context    context
     * @param appId      mscAppId
     * @param targetPath targetPath
     * @param callback   callback
     * @api 业务预热 API，可由业务方直接调用
     */
    public void preloadBiz(Context context, String appId, String targetPath, Callback<PreloadResultData> callback) {
        if (!ProcessUtils.isMainProcess(context)) {
            String errorMsg = appId + " call preloadBiz at sub process, cancel preload";
            PreloadManager.getInstance().putPreloadBizMetricsInfo(appId, "bizPreloadUnknownError", "biz preload process error");
            MSCLog.i(TAG, errorMsg);
            if (callback != null) {
                callback.onCancel();
            }
            return;
        }
        if (!MSCEnvHelper.isInited()) {
            if (context == null) {
                MSCLog.i(TAG, appId, "preloadBiz context is null");
                if (callback != null) {
                    callback.onCancel();
                }
                return;
            }
            MSCEnvHelper.startHostInit(context);
        }
        preloadBiz(appId, targetPath, callback);
    }

    /**
     * 使用 com.meituan.msc.modules.preload.PreloadManager#preloadBiz(android.content.Context,
     * java.lang.String, java.lang.String, com.meituan.msc.common.framework.Callback) 替代
     * @param appId
     * @param targetPath
     * @param callback
     */
    @Deprecated
    public void preloadBiz(String appId, String targetPath, Callback<PreloadResultData> callback) {
        preloadBiz(appId, targetPath, false, callback);
    }

    /**
     * @param context            context
     * @param appId              mscAppId
     * @param targetPath         targetPath
     * @param preloadWebViewPage preloadWebViewPage 是否预热WebView
     * @param callback           callback
     * @api 业务预热 API，可由业务方直接调用
     */
    public void preloadBiz(Context context, String appId, String targetPath, boolean preloadWebViewPage, Callback<PreloadResultData> callback) {
        preloadBiz(context, appId, targetPath, preloadWebViewPage, null, callback);
    }

    /**
     * @param context            context
     * @param appId              mscAppId
     * @param targetPath         targetPath
     * @param preloadWebViewPage preloadWebViewPage 是否预热WebView
     * @param preloadStrategyStr preloadStrategyStr 预热策略，非空时会注入到 matrix 组件埋点系统中，一般用于分析对首页的影响
     * @param callback           callback
     * @api 业务预热 API，可由业务方直接调用
     */
    public void preloadBiz(Context context, String appId, String targetPath, boolean preloadWebViewPage, String preloadStrategyStr, Callback<PreloadResultData> callback) {
        if (!ProcessUtils.isMainProcess(context)) {
            String errorMsg = appId + " call preloadBiz at sub process, cancel preload";
            MSCLog.i(TAG, errorMsg);
            if (callback != null) {
                callback.onCancel();
            }
            return;
        }
        if (!MSCEnvHelper.isInited()) {
            if (context == null) {
                MSCLog.i(TAG, appId, "preloadBiz context is null");
                if (callback != null) {
                    callback.onCancel();
                }
                return;
            }
            MSCEnvHelper.startHostInit(context);
        }
        preloadBiz(appId, targetPath, preloadWebViewPage, preloadStrategyStr, callback);
    }

    /**
     * 使用 com.meituan.msc.modules.preload.PreloadManager#preloadBiz(android.content.Context,
     * java.lang.String, java.lang.String, boolean, com.meituan.msc.common.framework.Callback) 替代
     * @param appId
     * @param targetPath
     * @param preloadWebViewPage
     * @param callback
     */
    @Deprecated
    public void preloadBiz(String appId, String targetPath, boolean preloadWebViewPage, Callback<PreloadResultData> callback) {
        preloadBiz(appId, targetPath, preloadWebViewPage, null, callback);
    }

    public void preloadBiz(String appId, String targetPath, boolean preloadWebViewPage, String preloadStrategyStr, Callback<PreloadResultData> callback) {
        MSCLog.i(TAG, "preloadBiz appId:", appId, "targetPath:", targetPath, "preloadWebViewPage:", preloadWebViewPage);
        if (noInitOrNotMainProcess()) {
            MSCLog.i(Constants.PRELOAD_BIZ, "only main process trigger bizPreload");
            getInstance().putPreloadBizMetricsInfo(appId, "bizPreloadUnknownError", "biz preload process error");
            if (callback != null) {
                callback.onCancel();
            }
            return;
        }
        PreloadTasksManager.instance.preloadMSCApp(appId, targetPath, preloadWebViewPage,
                null, null, preloadStrategyStr, new Callback<MSCRuntime>() {
                    @Override
                    public void onSuccess(MSCRuntime data) {
                        MSCLog.i(TAG, "#preloadBiz, success");
                        if (callback != null) {
                            callback.onSuccess(new PreloadResultData.Builder(appId, targetPath, preloadWebViewPage)
                                    .build());
                        }
                    }

                    @Override
                    public void onFail(String errMsg, Exception error) {
                        MSCLog.i(TAG, "#preloadBiz, fail");
                        if (callback != null) {
                            callback.onFail(errMsg, error);
                        }
                    }

                    @Override
                    public void onCancel() {
                        MSCLog.i(TAG, "#preloadBiz, cancel");
                        if (callback != null) {
                            callback.onCancel();
                        }
                    }
                });
    }

    /**
     * @param context            context
     * @param appId              mscAppId
     * @param targetPath         目标页面
     * @param preloadWebViewPage 预热页面（仅限webview渲染）
     * @param callback           callback
     * @api 业务预热 API，可由业务方直接调用，支持子进程业务调用
     */
    public void preloadMSCAppSupportSubProcessBiz(Context context, String appId, String targetPath, boolean preloadWebViewPage,
                                                  Callback<PreloadResultData> callback) {
        if (!ProcessUtils.isMainProcess(context)) {
            String errorMsg = appId + " call preloadBiz at sub process, cancel preloadMSCAppSupportSubProcessBiz";
            MSCLog.i(TAG, errorMsg);
            if (callback != null) {
                callback.onCancel();
            }
            return;
        }
        if (!MSCEnvHelper.isInited()) {
            if (context == null) {
                MSCLog.i(TAG, appId, "preloadBiz context is null");
                if (callback != null) {
                    callback.onCancel();
                }
                return;
            }
            MSCEnvHelper.startHostInit(context);
        }
        PreloadTasksManager.preloadMSCAppInProcess(appId, targetPath, preloadWebViewPage, null, null, callback);
    }

    /**
     * 使用 com.meituan.msc.modules.preload.PreloadManager#preloadMSCAppSupportSubProcessBiz(android.content.Context,
     * java.lang.String, java.lang.String, boolean, com.meituan.msc.common.framework.Callback) 替代
     * @param appId
     * @param targetPath
     * @param preloadWebViewPage
     * @param callback
     */
    @Deprecated
    public void preloadMSCAppSupportSubProcessBiz(String appId, String targetPath, boolean preloadWebViewPage,
                                                  Callback<PreloadResultData> callback) {
        PreloadTasksManager.preloadMSCAppInProcess(appId, targetPath, preloadWebViewPage, null, null, callback);
    }

    // ------------------------ Deprecated interface ---------------------------------

    /**
     * @param appId    mscAppId
     * @param callback callback
     * @api 业务预热 API，可由业务方直接调用
     */
    @Deprecated
    public void preloadApp(String appId, Callback<IAppLoader> callback) {
        if (noInitOrNotMainProcess()) {
            MSCLog.i(Constants.PRELOAD_BIZ, "only main process trigger bizPreload");
            return;
        }
        this.preloadApp(appId, null, callback);
    }

    /**
     * @param appId      mscAppId
     * @param targetPath 目标页面
     * @param callback   callback
     * @api 业务预热 API，可由业务方直接调用
     */
    @Deprecated
    public void preloadApp(String appId, String targetPath, Callback<IAppLoader> callback) {
        if (noInitOrNotMainProcess()) {
            MSCLog.i(Constants.PRELOAD_BIZ, "only main process trigger bizPreload");
            return;
        }
        PreloadTasksManager.instance.preloadApp(appId, targetPath, null, null, callback);
    }

    /**
     * @param appId
     * @param targetPath
     * @param callback
     * @api 业务预热 API，可由业务方直接调用
     */
    @Deprecated
    public void preloadMSCApp(String appId, String targetPath, Callback<MSCRuntime> callback) {
        preloadMSCApp(appId, targetPath, callback, false);
    }

    /**
     * @param appId
     * @param callback
     * @api 业务预热 API，可由业务方直接调用
     */
    @Deprecated
    public void preloadMSCApp(String appId, Callback<MSCRuntime> callback) {
        if (noInitOrNotMainProcess()) {
            MSCLog.i(Constants.PRELOAD_BIZ, "only main process trigger bizPreload");
            return;
        }
        this.preloadMSCApp(appId, null, callback, false);
    }


    /**
     * @param appId              mscAppId
     * @param targetPath         目标页面
     * @param callback           callback
     * @param preloadWebViewPage 预热页面（仅限webview渲染）
     * @api 业务预热 API，可由业务方直接调用，可以决策WebView渲染是预热WebView
     */
    @Deprecated
    public void preloadMSCApp(String appId, String targetPath, Callback<MSCRuntime> callback, boolean preloadWebViewPage) {
        preloadMSCApp(appId, targetPath, callback, preloadWebViewPage, null);
    }

    @Deprecated
    public void preloadMSCApp(String appId, String targetPath, Callback<MSCRuntime> callback,
                              boolean preloadWebViewPage, String preloadStrategyStr) {
        if (noInitOrNotMainProcess()) {
            MSCLog.i(Constants.PRELOAD_BIZ, "only main process trigger bizPreload");
            return;
        }
        PreloadTasksManager.instance.preloadMSCApp(appId, targetPath, preloadWebViewPage,
                null, null, preloadStrategyStr, callback);
    }

    public void putPreloadBizMetricsInfo(String appId, String preloadBizMissReason, String preloadBizErrorMsg) {
        if (MSCHornRollbackConfig.enableReportPreloadAndKeepAlive()) {
            preloadBizMissReasonMap.put(appId, preloadBizMissReason);
            preloadBizErrorMsgMap.put(appId, preloadBizErrorMsg);
        }
    }

    public void setPreloadBaseMetricsInfo(String preloadBaseMissReason, String preloadBaseErrorMsg) {
        this.preloadBaseErrorMsg = preloadBaseErrorMsg;
        if (MSCHornRollbackConfig.enableReportPreloadAndKeepAlive()) {
            this.preloadBaseMissReason = preloadBaseMissReason;
        }
    }
}
