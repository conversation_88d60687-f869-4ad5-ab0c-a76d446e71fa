package com.meituan.msc.modules.preload;


import android.os.Parcel;
import android.os.Parcelable;

import com.meituan.msi.annotations.MsiSupport;

/**
 * <AUTHOR>
 * 只能使用支持跨进程传递的类型
 */
@MsiSupport
public class PreloadResultData implements Parcelable {
    String appId;
    String targetPath;
    boolean preloadWebView;

    public PreloadResultData(Builder builder) {
        this.appId = builder.appId;
        this.targetPath = builder.targetPath;
        this.preloadWebView = builder.preloadWebView;
    }

    protected PreloadResultData(Parcel in) {
        appId = in.readString();
        targetPath = in.readString();
        preloadWebView = in.readByte() != 0;
    }

    public static final Creator<PreloadResultData> CREATOR = new Creator<PreloadResultData>() {
        @Override
        public PreloadResultData createFromParcel(Parcel in) {
            return new PreloadResultData(in);
        }

        @Override
        public PreloadResultData[] newArray(int size) {
            return new PreloadResultData[size];
        }
    };

    public String getAppId() {
        return appId;
    }

    public String getTargetPath() {
        return targetPath;
    }


    public boolean isPreloadWebView() {
        return preloadWebView;
    }

    @Override
    public int describeContents() {
        return 0;
    }

    @Override
    public void writeToParcel(Parcel dest, int flags) {
        dest.writeString(appId);
        dest.writeString(targetPath);
        dest.writeByte((byte) (preloadWebView ? 1 : 0));
    }

    public static class Builder {
        String appId;
        String targetPath;
        boolean preloadWebView;

        public Builder(String appId,
                       String targetPath,
                       boolean preloadWebView) {
            this.appId = appId;
            this.targetPath = targetPath;
            this.preloadWebView = preloadWebView;
        }

        public Builder setAppId(String appId) {
            this.appId = appId;
            return this;
        }

        public Builder setPreloadWebView(boolean preloadWebView) {
            this.preloadWebView = preloadWebView;
            return this;
        }

        public Builder setTargetPath(String targetPath) {
            this.targetPath = targetPath;
            return this;
        }


        public PreloadResultData build() {
            return new PreloadResultData(this);
        }
    }

    @Override
    public String toString() {
        return "PreloadResultData{" +
                "appId='" + appId + '\'' +
                ", targetPath='" + targetPath + '\'' +
                ", preloadWebView=" + preloadWebView +
                '}';
    }
}
