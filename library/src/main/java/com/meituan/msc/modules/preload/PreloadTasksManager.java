package com.meituan.msc.modules.preload;

import android.os.SystemClock;
import android.support.annotation.Nullable;
import android.support.annotation.RestrictTo;
import android.text.TextUtils;

import com.meituan.android.degrade.interfaces.resource.ExecuteResultCallback;
import com.meituan.android.degrade.interfaces.resource.PreloadBlock;
import com.meituan.android.degrade.interfaces.resource.ResourceManager;
import com.meituan.msc.common.config.MSCMultiProcessConfig;
import com.meituan.msc.common.framework.Callback;
import com.meituan.msc.common.framework.MSCRunningManager;
import com.meituan.msc.common.process.MSCProcess;
import com.meituan.msc.common.process.ipc.IPCInvoke;
import com.meituan.msc.common.support.java.util.concurrent.CompletableFuture;
import com.meituan.msc.common.support.java.util.function.BiFunction;
import com.meituan.msc.common.support.java.util.function.Function;
import com.meituan.msc.common.utils.Constants;
import com.meituan.msc.common.utils.ExceptionHelper;
import com.meituan.msc.common.utils.MSCResourceWatermarkUtil;
import com.meituan.msc.common.utils.ToastUtils;
import com.meituan.msc.extern.MSCEnvHelper;
import com.meituan.msc.modules.api.ApiException;
import com.meituan.msc.modules.api.msi.MSCErrorCode;
import com.meituan.msc.modules.apploader.IAppLoader;
import com.meituan.msc.modules.apploader.MSCAppLoader;
import com.meituan.msc.modules.container.ApplicationLifecycleMonitor;
import com.meituan.msc.modules.engine.MSCHornRollbackConfig;
import com.meituan.msc.modules.engine.MSCRuntime;
import com.meituan.msc.modules.engine.RuntimeDestroyReason;
import com.meituan.msc.modules.engine.RuntimeManager;
import com.meituan.msc.modules.engine.RuntimeStateBeforeLaunch;
import com.meituan.msc.modules.preload.executor.ScheduledTaskExecutor;
import com.meituan.msc.modules.reporter.MSCLog;
import com.meituan.msc.modules.reporter.preformance.InitRecorder;
import com.sankuai.android.jarvis.Jarvis;
import com.sankuai.common.utils.ProcessUtils;

import org.json.JSONObject;

import java.util.concurrent.ScheduledExecutorService;
import java.util.concurrent.locks.Lock;
import java.util.concurrent.locks.ReentrantLock;

/**
 * 预加载操作相关能力，设计文档：https://km.sankuai.com/page/1274538197
 * 业务预热，技术文档：https://km.sankuai.com/page/1314944158
 */
public class PreloadTasksManager {
    public static final PreloadTasksManager instance = new PreloadTasksManager();
    /**
     * 基础库预热 + 业务预热
     */
    private static final int TASK_EXECUTOR_CORE_POOL_SIZE = 2;
    private static final String TAG = "PreloadTasksManager";
    private final ScheduledTaskExecutor mTaskExecutor;
    private final Lock unusedPreloadBizAppLock = new ReentrantLock();

    private PreloadTasksManager() {
        // 创建任务管理器
        ScheduledExecutorService scheduledExecutorService = Jarvis.newScheduledThreadPool("MSCPreloadTaskExecutor", TASK_EXECUTOR_CORE_POOL_SIZE);
        mTaskExecutor = new ScheduledTaskExecutor(scheduledExecutorService);
    }

    public static void forcePreload() {
        // TODO: 2023/3/27 tianbin 调试功能，暂缓支持
    }


    public void preloadBiz() {
        if (MSCHornPreloadConfig.enableBizPreload()) {
            // 启动业务预热
            preloadAppByMSC();
        } else {
            MSCLog.i(Constants.PRELOAD_BIZ, "enableBizPreload is off");
        }
    }

    @RestrictTo(RestrictTo.Scope.LIBRARY_GROUP)
    public void preloadBase() {
        if (MSCHornPreloadConfig.enableBasePreload()) {
            // 启动基础库预热
            preloadBasePackageAsync(Constants.PRELOAD_BASE, 0)
                    .exceptionally(new Function<Throwable, MSCRuntime>() {
                        @Override
                        public MSCRuntime apply(Throwable throwable) {
                            PreloadManager.getInstance().preloadBaseErrorMsg = "enable preloadBase error:" + (throwable != null ? throwable.toString() : "");
                            return null;
                        }
                    });
        } else {
            PreloadManager.getInstance().setPreloadBaseMetricsInfo("basePreloadHornRollback", "enable preloadBase is off");
            MSCLog.i(Constants.PRELOAD_BASE, "enableBasePreload is off");
        }
    }

    /**
     * 预加载基础库
     *
     * @return completable
     */
    private CompletableFuture<MSCRuntime> preloadBasePackageAsync(String source, long delayMillis) {
        if (existAtLeastOneBasePackagePreloadEngine()) {
            PreloadManager.getInstance().setPreloadBaseMetricsInfo("basePreloadExisted", "already exist preload base runtime");
            return CompletableFuture.completedFuture(null);
        }
        RuntimePreloadReporter.CommonReporter.create().reportPreloadCount(RuntimePreloadReporter.BASE_PRELOAD);
        String basePkgVersionOfDebug = PackageDebugHelper.instance.getBasePkgVersionOfDebug(null);
        return realPreloadBasePackage(source, delayMillis, basePkgVersionOfDebug);
    }

    private CompletableFuture<MSCRuntime> realPreloadBasePackage(String source, long delayMillis, String basePkgVersionOfDebug) {
        MSCLog.i(TAG, "realPreloadBasePackage", source, delayMillis, basePkgVersionOfDebug);
        long startTime = System.currentTimeMillis();
        long startTimeOfPreloadBase = SystemClock.elapsedRealtime();
        BasePackagePreloadTask basePackagePreloadTask = new BasePackagePreloadTask(source,  basePkgVersionOfDebug);
        if (delayMillis > 0) {
            mTaskExecutor.addDelayedTask(basePackagePreloadTask, delayMillis);
        } else {
            mTaskExecutor.addTask(basePackagePreloadTask);
        }
        return basePackagePreloadTask.getResultFuture()
                .handle(new BiFunction<MSCRuntime, Throwable, MSCRuntime>() {
                    @Override
                    public MSCRuntime apply(MSCRuntime runtime, Throwable throwable) {
                        if (throwable == null) {
                            reportBasePreloadData(runtime, startTime, startTimeOfPreloadBase, basePackagePreloadTask);
                        } else {
                            RuntimePreloadReporter.CommonReporter.create().reportPreloadFailed(throwable, "preloadBase", null, RuntimePreloadReporter.BASE_PRELOAD);
                        }
                        return runtime;
                    }
                });
    }

    private void reportBasePreloadData(MSCRuntime runtime, long startTime, long startTimeOfPreloadBase, BasePackagePreloadTask basePackagePreloadTask) {
        if (runtime == null) {
            MSCLog.i(TAG, "reportBasePreloadData runtime is null");
            return;
        }
        // 记录完成时间戳
        if (MSCHornRollbackConfig.enableReportPreloadAndKeepAlive()) {
            long libcMem_b = basePackagePreloadTask.getLibcMem_b();
            long libcMem_e = MSCResourceWatermarkUtil.getAppLibcMemByte();
            long javaMem_b = basePackagePreloadTask.getJavaMem_b();
            long javaMem_e = MSCResourceWatermarkUtil.getAppUsedJavaMemByte();
            long preloadTaskFromApplicationStart = basePackagePreloadTask.getStartExecuteTime() - InitRecorder.getApplicationStartUnixTime();
            runtime.getRuntimeReporter().setPreloadStartTime(startTime)
                .setPreloadEndTime(System.currentTimeMillis())
                .setPreloadAppId(runtime.getAppId())
                .setPreloadLibcMemoryByteBegin(libcMem_b)
                .setPreloadLibcMemoryByteEnd(libcMem_e)
                .setPreloadUsedJavaMemoryByteBegin(javaMem_b)
                .setPreloadUsedJavaMemoryByteEnd(javaMem_e)
                .setPreloadTaskFromApplicationStartTime(preloadTaskFromApplicationStart);
        } else {
            runtime.getRuntimeReporter().setPreloadStartTime(startTime)
                .setPreloadEndTime(System.currentTimeMillis())
                .setPreloadAppId(runtime.getAppId());
        }

        // 上报基础库预热数据 仅在启动前预热完毕场景上报
        if (canReportPreloadSuccess(runtime)) {
            // 上报基础库预热成功率
            RuntimePreloadReporter.SuccessReporter.create(runtime)
                    .reportPreloadSuccess(runtime, RuntimePreloadReporter.BASE_PRELOAD);
            // 上报基础库预热耗时
            RuntimePreloadReporter.CommonReporter.create()
                    .reportPreloadDuration(runtime, startTimeOfPreloadBase, RuntimePreloadReporter.BASE_PRELOAD);
        } else {
            MSCLog.i(TAG, "cancel reportBasePreloadData");
        }
    }

    @RestrictTo(RestrictTo.Scope.LIBRARY_GROUP)
    public CompletableFuture<MSCRuntime> preloadBasePackageAsyncForDebug(String source, String basePkgVersionOfDebug, long delayMillis) {
        return realPreloadBasePackage(source, delayMillis, basePkgVersionOfDebug);
    }

    @RestrictTo(RestrictTo.Scope.LIBRARY_GROUP)
    public boolean existAtLeastOneBasePackagePreloadEngine() {
        // 获取未使用的引擎列表
        return RuntimeManager.findBasePreloadRuntime() != null;
    }

    /**
     * 再次触发基础库预热
     * 1. 外链冷启动场景，首次预热基础库动作会被取消
     * 2. 其他启动场景，预热基础库的运行时被征用
     */
    @Nullable
    @RestrictTo(RestrictTo.Scope.LIBRARY_GROUP)
    public CompletableFuture<MSCRuntime> preloadBasePackageAgain(long delayMillis) {
        PreloadManager.getInstance().ensureInit();
        if (!MSCHornPreloadConfig.enableBasePreload()) {
            // 如果未启用预加载，直接退出
            PreloadManager.getInstance().setPreloadBaseMetricsInfo("basePreloadHornRollback", "base preload horn rollback");
            return null;
        }

        // 触发再次预热，重置基础库预热错误原因
        PreloadManager.getInstance().preloadBaseErrorMsg = "preloadBasePackageAgain";
        // 如果当前没有只预加载了基础库包的引擎，就新预加载一个
        // 为了避免影响页面加载时间，延迟一段时间之后再创建新的基础库预热引擎
        return preloadBasePackageAsync(Constants.PRELOAD_AGAIN, delayMillis);
    }

    @RestrictTo(RestrictTo.Scope.LIBRARY_GROUP)
    public void preloadBasePackageAgainAfterFP() {
        MSCLog.i(TAG, "preloadBasePackageAgainAfterFP");
        // 基础库预热的运行时被征用，进行再次预热
        preloadBasePackageAgain(MSCHornPreloadConfig.get().getConfig().startPreloadBaseTaskAfterFP * 1000L);
    }

    /**
     * 内部调试功能使用，禁止业务调用
     */
    @RestrictTo(RestrictTo.Scope.LIBRARY_GROUP)
    public void preloadApp(String appId, String targetPath, String checkUpdateUrl, String mscVersion,
                           @Nullable Callback<IAppLoader> callback) {
        if (notMainProcess()) {
            MSCLog.i(Constants.PRELOAD_BIZ, "only main process trigger bizPreload");
            return;
        }
        this.preloadMSCApp(appId, targetPath, checkUpdateUrl, mscVersion, null, new Callback<MSCRuntime>() {
            @Override
            public void onSuccess(MSCRuntime data) {
                if (callback != null) {
                    callback.onSuccess(data.getModule(IAppLoader.class));
                }
            }

            @Override
            public void onFail(String errMsg, Exception error) {
                if (callback != null) {
                    callback.onFail(errMsg, error);
                }
            }

            @Override
            public void onCancel() {
                if (callback != null) {
                    callback.onCancel();
                }
            }
        });
    }

    private boolean notMainProcess() {
        if (MSCEnvHelper.getContext() == null) {
            MSCLog.i(TAG, "isMainProcess context is null");
            return true;
        }
        return !ProcessUtils.isMainProcess(MSCEnvHelper.getContext());
    }

    @RestrictTo(RestrictTo.Scope.LIBRARY_GROUP)
    public void preloadMSCApp(String appId, String targetPath, String checkUpdateUrl, String mscVersion,
                              String preloadStrategyStr, @Nullable Callback<MSCRuntime> callback) {
        preloadMSCApp(appId, targetPath, false, checkUpdateUrl, mscVersion, preloadStrategyStr, callback);
    }

    /**
     * 业务预热：触发完成逻辑层预热的小程序的视图层预热。
     *
     * @param appId
     * @param callback
     */
    @RestrictTo(RestrictTo.Scope.LIBRARY_GROUP)
    public void preloadMSCAppBizWebViewOnly(String appId, @Nullable Callback<MSCRuntime> callback) {
        // 在非主进程和非小程序进程，执行预热V8有Crash，故有此限制
        if (notMainProcess() && !MSCProcess.STANDARD.isCurrentProcess()) {
            MSCLog.i(Constants.PRELOAD_BIZ, "only main process and msc process trigger bizPreload");
            return;
        }
        PreloadManager.getInstance().ensureInit();
        // 是否支持业务预热
        if (!MSCHornPreloadConfig.enableBizPreload()) {
            String errorMsg = "preloadMSCAppPageOnly is off:" + appId + ",targetPath: null";
            MSCLog.i(Constants.PRELOAD_BIZ, errorMsg);
            if (callback != null) {
                callback.onFail(errorMsg, null);
            }
            return;
        }
        final MSCRuntime runtime = RuntimeManager.getRuntimeWithAppId(appId);
        if (null != runtime) {
            // 找到对应的appid，然后触发webview的业务预热。
            IAppLoader loader = runtime.getModule(IAppLoader.class);
            if (null != loader && !loader.isLaunched() && loader.isUsable()) {
                CompletableFuture result = loader.preloadWebViewBizPackageOnly();
                if (null != result) {
                    result.thenApply(new Function() {
                        @Override
                        public Object apply(Object o) {
                            if (null != callback) {
                                callback.onSuccess(runtime);
                            }
                            return o;
                        }
                    }).exceptionally(new Function<Throwable, Object>() {
                        @Override
                        public Object apply(Throwable throwable) {
                            if (null != callback) {
                                callback.onFail(throwable.getMessage(), null);
                            }
                            return throwable;
                        }
                    });
                    return;
                }
            }
        }
        if (null != callback) {
            callback.onFail("preloadMSCAppBizWebViewOnly not apply.", null);
        }
    }

    @RestrictTo(RestrictTo.Scope.LIBRARY_GROUP)
    public void preloadMSCApp(String appId, String targetPath, boolean preloadWebViewPage,
                              String checkUpdateUrl, String mscVersion, String preloadStrategyStr,
                              @Nullable Callback<MSCRuntime> callback) {
        // 在非主进程和非小程序进程，执行预热V8有Crash，故有此限制
        if (notMainProcess() && !MSCProcess.STANDARD.isCurrentProcess()) {
            MSCLog.i(Constants.PRELOAD_BIZ, "only main process and msc process trigger bizPreload");
            PreloadManager.getInstance().putPreloadBizMetricsInfo(appId, "bizPreloadUnknownError", "biz preload process error");
            if (callback != null) {
                callback.onCancel();
            }
            return;
        }

        PreloadManager.getInstance().ensureInit();
        // 上报预热总调用次数，便于排查预热被调用但未执行且无错误回调的问题
        RuntimePreloadReporter.CommonReporter.create().reportPreloadCount(appId, targetPath, RuntimePreloadReporter.BIZ_PRELOAD);

        // 是否支持业务预热
        if (!MSCHornPreloadConfig.enableBizPreload()) {
            String errorMsg = "enableBizPreload is off:" + appId + ",targetPath:" + targetPath;
            MSCLog.i(Constants.PRELOAD_BIZ, errorMsg);
            RuntimePreloadReporter.CommonReporter.create().reportPreloadFailed(
                    new IllegalStateException(errorMsg), appId, targetPath, RuntimePreloadReporter.BIZ_PRELOAD);
            if (callback != null) {
                callback.onFail(errorMsg, new ApiException(MSCErrorCode.ERROR_CODE_API_COMMON_CLIENT_CLOSE, errorMsg));
            }
            PreloadManager.getInstance().putPreloadBizMetricsInfo(appId, "bizPreloadHornRollback", "enable preload is off");
            return;
        }

        if (MSCHornPreloadConfig.enableBizPreloadMultiPage(appId)) {
            // 允许多次调用业务预热
            doPreloadApp(appId, targetPath, checkUpdateUrl, mscVersion, callback, preloadWebViewPage, preloadStrategyStr);
            return;
        }

        // 已存在，不需要预热
        MSCRuntime runtime = RuntimeManager.getRuntimeWithAppId(appId);
        if ((runtime != null || hasPendingPreloadTask(appId, targetPath, callback))) {
            MSCLog.i(Constants.PRELOAD_BIZ, "runtime is exist:" + appId + ",targetPath:" + targetPath);
            PreloadManager.getInstance().putPreloadBizMetricsInfo(appId, "bizPreloadExisted", "biz preload task repeat");
            if (runtime != null) {
                RuntimePreloadReporter.SuccessReporter.create(runtime)
                        .reportPreloadSuccess(runtime, targetPath, RuntimePreloadReporter.BIZ_PRELOAD);
            }
            if (callback != null) {
                callback.onCancel();
            }
            return;
        }

        doPreloadApp(appId, targetPath, checkUpdateUrl, mscVersion, callback, preloadWebViewPage, preloadStrategyStr);
    }

    static boolean callBackFailIfAtBlackList(String appId, String targetPath, Callback<MSCRuntime> callback) {
        if (MSCHornPreloadConfig.inPreloadAppBlackList(appId)) {
            String errorMsg = appId + " is in preloadAppBlackList";
            MSCLog.i(Constants.PRELOAD_BIZ, errorMsg);
            RuntimePreloadReporter.CommonReporter.create().reportPreloadFailed(
                    new IllegalArgumentException(errorMsg), appId, targetPath, RuntimePreloadReporter.BIZ_PRELOAD);
            if (callback != null) {
                callback.onFail(errorMsg, new ApiException(MSCErrorCode.ERROR_CODE_API_COMMON_CLIENT_CLOSE, errorMsg));
            }
            return true;
        }
        return false;
    }

    private boolean hasPendingPreloadTask(String appId, String targetPath, Callback<MSCRuntime> callback) {
        PendingBizPreloadTasksManager.PreloadBizData preloadBizData =
                new PendingBizPreloadTasksManager.PreloadBizData(appId, targetPath, callback);
        return PendingBizPreloadTasksManager.getInstance().containsTask(preloadBizData);
    }

    private void preloadAppByMSC() {
        // fetch preload appIds
        String[] priorityAppList = MSCHornPreloadConfig.getPriorityAppList();
        if (priorityAppList == null || priorityAppList.length == 0) {
            MSCLog.i(Constants.PRELOAD_BIZ, "priorityAppList is empty");
            return;
        }

        // 后台切到前台后触发
        ApplicationLifecycleMonitor.ALL.addReEnterForegroundListener(new Runnable() {
            @Override
            public void run() {
                ToastUtils.toastIfDebug("app进入前台，尝试再次预加载");
                doPreloadAppByMSC(priorityAppList);
            }
        });

        // 初始化时触发
        doPreloadAppByMSC(priorityAppList);
    }

    private void doPreloadAppByMSC(String[] priorityAppList) {
        MSCLog.i(Constants.PRELOAD_BIZ, "doPreloadAppByMSC");
        for (String appId : priorityAppList) {
            if (TextUtils.isEmpty(appId)) {
                continue;
            }

            // is running or keep alive
            if (RuntimeManager.getRuntimeWithAppId(appId) != null) {
                MSCLog.i(Constants.PRELOAD_BIZ, appId, " app runtime exist");
                PreloadManager.getInstance().putPreloadBizMetricsInfo(appId, "bizPreloadExisted", "bizPreloadExisted");
                continue;
            }

            MSCLog.i(Constants.PRELOAD_BIZ, "preloadAppPackageByMSC start:", appId);
            preloadMSCAppInProcess(appId, null, false, null, null, new Callback<PreloadResultData>() {
                @Override
                public void onSuccess(PreloadResultData data) {
                    // success log
                    MSCLog.i(Constants.PRELOAD_BIZ, "preloadAppPackageByMSC success:", data);
                }

                @Override
                public void onFail(String errMsg, Exception error) {
                    // fail log
                    MSCLog.e(Constants.PRELOAD_BIZ, error, "preloadAppPackageByMSC failed:", appId);
                }

                @Override
                public void onCancel() {
                    // cancel log
                    MSCLog.i(Constants.PRELOAD_BIZ, "preloadAppPackageByMSC cancel:", appId);
                }
            }, false);
        }
    }

    private void doPreloadApp(String appId, String targetPath, @Nullable String checkUpdateUrl, String mscVersion,
                              @Nullable Callback<MSCRuntime> callback, boolean preloadWebViewPage) {
        doPreloadApp(appId, targetPath, checkUpdateUrl, mscVersion, callback, preloadWebViewPage, null);
    }

    private void doPreloadApp(String appId, String targetPath, @Nullable String checkUpdateUrl, String mscVersion,
                              @Nullable Callback<MSCRuntime> callback, boolean preloadWebViewPage, String preloadStrategyStr) {
        long startTime = SystemClock.elapsedRealtime();
        // 在业务预热黑名单中，回调失败
        if (callBackFailIfAtBlackList(appId, targetPath, callback)) {
            PreloadManager.getInstance().putPreloadBizMetricsInfo(appId, "bizPreloadInBlackList", "biz preload in black list");
            return;
        }

        if (TextUtils.isEmpty(appId)) {
            String errorMsg = "appId is null or empty";
            MSCLog.i(Constants.PRELOAD_BIZ, errorMsg);
            RuntimePreloadReporter.CommonReporter.create()
                    .reportPreloadFailed(new IllegalArgumentException(errorMsg), appId, targetPath, RuntimePreloadReporter.BIZ_PRELOAD);
            if (callback != null) {
                callback.onFail(errorMsg, new ApiException(MSCErrorCode.ERROR_CODE_API_COMMON_INVALID_APP_ID, errorMsg));
            }
            return;
        }
        boolean enableBizPreloadMultiPage = MSCHornPreloadConfig.enableBizPreloadMultiPage(appId);
        if (enableBizPreloadMultiPage) {
            MSCRuntime runtime = RuntimeManager.getRuntimeWithAppId(appId);
            boolean isPendingPreload = hasPendingPreloadTask(appId, targetPath, callback);
            // unusedPreloadBizAppLock避免多线程环境下多次执行业务预热
            unusedPreloadBizAppLock.lock();
            if (runtime != null || isPendingPreload || UnusedPreloadBizAppManager.instance.containsPreloadApp(appId)) {
                unusedPreloadBizAppLock.unlock();
                MSCLog.i(Constants.PRELOAD_BIZ, "runtime is exist:" + appId + ",targetPath:" + targetPath);
                if (isPendingPreload || runtime == null || runtime.isRuntimeBizPreloading()) {
                    PendingBizPreloadTasksManager.getInstance().addPagePreloadPendingTask(new PendingBizPreloadTasksManager.PreloadBizData(appId, targetPath, preloadWebViewPage, callback));
                } else {
                    preloadBizPage(appId, targetPath, preloadWebViewPage, callback);
                }
                return;
            }
        }

        // 业务预热缓存/预热队列清除策略
        // 在 NLAS 策略下，若想要预热的小程序权重不够，则不允许预热
        boolean canAddPreload = UnusedPreloadBizAppManager.instance.cleanPreloadAppStrategy(appId);
        if (!canAddPreload) {
            String errorMsg = "NLAS priority is not enough";
            RuntimePreloadReporter.CommonReporter.create()
                .reportPreloadFailed(new ApiException(errorMsg), appId, targetPath, RuntimePreloadReporter.BIZ_PRELOAD);
            if (callback != null) {
                callback.onFail(errorMsg, new ApiException(errorMsg));
            }
            return;
        }
        UnusedPreloadBizAppManager.instance.addPreloadApp(appId);
        if (enableBizPreloadMultiPage) {
            unusedPreloadBizAppLock.unlock();
        }

        String basePkgVersionOfDebug;
        if (TextUtils.isEmpty(mscVersion)) {
            basePkgVersionOfDebug = PackageDebugHelper.instance.getBasePkgVersionOfDebug(null);
        } else {
            basePkgVersionOfDebug = mscVersion;
        }
        BizPackagePreloadTask bizPackagePreloadTask = new BizPackagePreloadTask(basePkgVersionOfDebug,
                appId, checkUpdateUrl, targetPath, preloadWebViewPage, preloadStrategyStr, callback);
        boolean bSuccess = mTaskExecutor.addTask(bizPackagePreloadTask);
        //添加任务不成功 且 开启存在相同任务时返回。补充失败埋点上报，返回
        if (!bSuccess && MSCHornRollbackConfig.enableReturnWhenHasSameBizPreloadTask()){
            String errorMsg = "bizPackagePreloadTask has existed";
            PreloadManager.getInstance().putPreloadBizMetricsInfo(appId, "bizPreloadExisted", errorMsg);
            MSCLog.e(TAG, errorMsg);
            RuntimePreloadReporter.CommonReporter.create()
                    .reportPreloadFailed(new IllegalArgumentException(errorMsg), appId, targetPath, RuntimePreloadReporter.BIZ_PRELOAD);
            if (callback != null) {
                callback.onCancel();
            }
            return;
        }

        bizPackagePreloadTask.getResultFuture()
                .handle(new BiFunction<MSCRuntime, Throwable, Object>() {
                    @Override
                    public Object apply(MSCRuntime mscRuntime, Throwable throwable) {
                        if (throwable == null) {
                            reportBizPreloadData(mscRuntime, targetPath, startTime, bizPackagePreloadTask);
                            if (mscRuntime != null) {
                                mscRuntime.addLoadedTargetPath(targetPath, false);
                                if (callback != null) {
                                    callback.onSuccess(mscRuntime);
                                }
                                PendingBizPreloadTasksManager.getInstance().executePagePreloadPendingTask(appId);
                            } else {
                                PendingBizPreloadTasksManager.getInstance().removePagePreloadPendingTask(appId, "preloadBiz runtime is null");
                                MSCLog.i(TAG, "preloadBiz runtime is null");
                            }
                        } else {
                            MSCLog.e(Constants.PRELOAD_BIZ, throwable, "doPreloadAppPackage error",
                                    appId, targetPath, ExceptionHelper.readStack(throwable));
                            RuntimePreloadReporter.CommonReporter.create()
                                    .reportPreloadFailed(throwable, appId, targetPath, RuntimePreloadReporter.BIZ_PRELOAD);
                            UnusedPreloadBizAppManager.instance.removePreloadApp(appId);
                            PendingBizPreloadTasksManager.getInstance().removePagePreloadPendingTask(appId, throwable.getMessage());
                            if (callback != null) {
                                callback.onFail(throwable.getMessage(), new Exception(throwable));
                            }
                        }
                        return null;
                    }
                });
    }

    public void preloadBizPage(String appId, String targetPath, boolean preloadWebViewPage, @Nullable Callback<MSCRuntime> callback) {
        MSCRuntime runtime = RuntimeManager.getRuntimeWithAppId(appId);
        // 检查是否为不同的targetPath
        if (!TextUtils.isEmpty(targetPath) && runtime != null && !runtime.isTargetPathLoaded(targetPath) && runtime.canPreloadTargetPath()) {
            MSCLog.i(Constants.PRELOAD_BIZ, "runtime exists but targetPath not preloaded:" + appId + ",targetPath:" + targetPath);
            runtime.addLoadedTargetPath(targetPath, false);
            if (MSCHornPreloadConfig.enableControlBizPreload()) {
                ResourceManager.getInstance().submitPreloadBlock(new PreloadBlock() {
                    @Override
                    public String getBusinessName() {
                        return "MSC";
                    }

                    @Override
                    public String getPreloadType() {
                        return "bizPreload";
                    }

                    @Override
                    public String getBusinessId() {
                        return appId;
                    }

                    @Override
                    public void onExecute() {
                        MSCLog.i(TAG, "preload targetPath by degradeFramework");
                        doPreloadTargetPathResource(appId, targetPath, preloadWebViewPage, callback, runtime);
                    }
                }, new ExecuteResultCallback() {
                    @Override
                    public void onExecuteAllow() {
                    }

                    @Override
                    public void onExecuteDenied(String deniedReason, JSONObject adopt) {
                        MSCLog.i(TAG, "preload targetPath is rejected by degradeFramework, reason:" + deniedReason);
                        if (callback != null) {
                            callback.onFail("rejected by degradeFramework", new ApiException("rejected by degradeFramework"));
                        }
                    }
                });
            } else {
                MSCLog.i(TAG, "preload targetPath by normal");
                doPreloadTargetPathResource(appId, targetPath, preloadWebViewPage, callback, runtime);
            }
        } else {
            MSCLog.i(Constants.PRELOAD_BIZ, "runtime exists and targetPath cannot preload:" + appId + ",targetPath:" + targetPath);
            if (callback != null) {
                callback.onFail("preloaded or count limit", new ApiException("preloaded or count limit"));
            }
        }
    }

    private static void doPreloadTargetPathResource(String appId, String targetPath, boolean preloadWebViewPage, Callback<MSCRuntime> callback, MSCRuntime runtime) {
        // 已存在的runtime预加载targetPath
        IAppLoader appLoader = runtime.getModule(IAppLoader.class);
        if (appLoader instanceof MSCAppLoader) {
            ((MSCAppLoader) appLoader).preloadTargetPathForExistingRuntime(targetPath, preloadWebViewPage).handle(new BiFunction<Void, Throwable, Void>() {
                @Override
                public Void apply(Void result, Throwable throwable) {
                    if (throwable != null) {
                        MSCLog.e(Constants.PRELOAD_BIZ, throwable, "preload targetPath failed:", appId, targetPath);
                        runtime.removeLoadedTargetPath(targetPath);
                        RuntimePreloadReporter.CommonReporter.create().reportPreloadFailed(throwable, appId, targetPath, RuntimePreloadReporter.BIZ_PRELOAD);
                        if (callback != null) {
                            callback.onFail("preload targetPath failed", new ApiException("preload targetPath failed"));
                        }
                    } else {
                        MSCLog.i(Constants.PRELOAD_BIZ, "preload targetPath success:", appId, targetPath);
                        RuntimePreloadReporter.SuccessReporter.create(runtime).reportPreloadSuccess(runtime, targetPath, RuntimePreloadReporter.BIZ_PRELOAD);
                        if (callback != null) {
                            callback.onSuccess(runtime);
                        }
                    }
                    return null;
                }
            });
            return;
        }
    }

    private void reportBizPreloadData(MSCRuntime runtime, String targetPath, long startTime, BizPackagePreloadTask bizPackagePreloadTask) {
        if (runtime == null) {
            MSCLog.i(TAG, "reportBizPreloadData runtime is null");
            return;
        }
        if (MSCHornRollbackConfig.enableReportPreloadAndKeepAlive()) {
            long libcMem_b = bizPackagePreloadTask.getLibcMem_b();
            long libcMem_e = MSCResourceWatermarkUtil.getAppLibcMemByte();
            long javaMem_b = bizPackagePreloadTask.getJavaMem_b();
            long javaMem_e = MSCResourceWatermarkUtil.getAppUsedJavaMemByte();
            long preloadTaskFromApplicationStart = bizPackagePreloadTask.getStartExecuteTime() - InitRecorder.getApplicationStartUnixTime();
            // 记录时间戳
            runtime.getRuntimeReporter().setPreloadStartTime(startTime)
                    .setPreloadEndTime(System.currentTimeMillis())
                    .setPreloadAppId(runtime.getAppId())
                    .setPreloadLibcMemoryByteBegin(libcMem_b)
                    .setPreloadLibcMemoryByteEnd(libcMem_e)
                    .setPreloadUsedJavaMemoryByteBegin(javaMem_b)
                    .setPreloadUsedJavaMemoryByteEnd(javaMem_e)
                    .setPreloadTaskFromApplicationStartTime(preloadTaskFromApplicationStart);
        } else {
            runtime.getRuntimeReporter().setPreloadStartTime(startTime)
                .setPreloadEndTime(System.currentTimeMillis())
                .setPreloadAppId(runtime.getAppId());
        }

        // 上报业务预热数据 仅在启动前预热完毕场景上报
        if (canReportPreloadSuccess(runtime)) {
            // 上报业务预热成功率
            RuntimePreloadReporter.SuccessReporter.create(runtime)
                    .reportPreloadSuccess(runtime, targetPath, RuntimePreloadReporter.BIZ_PRELOAD);

            // 上报业务预热耗时，如果已被征用，则不再上报
            RuntimePreloadReporter.CommonReporter.create()
                    .reportPreloadDuration(runtime, startTime, targetPath, RuntimePreloadReporter.BIZ_PRELOAD);
        } else {
            MSCLog.i(TAG, "cancel reportBizPreloadData");
        }
    }

    private boolean canReportPreloadSuccess(MSCRuntime runtime) {
        IAppLoader appLoader = runtime.getModule(IAppLoader.class);
        if (appLoader == null) {
            return false;
        }
        // 仅在启动前预热完毕场景上报
        return !appLoader.isLaunched();
    }

    @RestrictTo(RestrictTo.Scope.LIBRARY_GROUP)
    public void cleanPreloadApp(String appId, @Nullable Callback<Void> callback) {
        MSCEnvHelper.ensureFullInited();

        if (MSCHornPreloadConfig.inPreloadAppBlackList(appId)) {
            String errorMsg = appId + " is in preloadAppBlackList";
            MSCLog.i(Constants.PRELOAD_BIZ, errorMsg);
            if (callback != null) {
                callback.onFail(errorMsg, new IllegalArgumentException(errorMsg));
            }
            return;
        }

        MSCLog.i(Constants.PRELOAD_BIZ, "cleanPreloadApp start:", appId);
        MSCRuntime bizPreloadRuntime = RuntimeManager.findBizPreloadRuntime(appId);
        if (bizPreloadRuntime != null) {
            String reason = RuntimeDestroyReason.toString(RuntimeDestroyReason.CLEAN_PRELOAD_APP);
            bizPreloadRuntime.destroyEngineIfNoCountWithCallback(reason, new Callback<Void>() {
                @Override
                public void onSuccess(Void data) {
                    MSCLog.i(Constants.PRELOAD_BIZ, "cleanPreloadApp success:", appId);
                    UnusedPreloadBizAppManager.instance.removePreloadApp(appId);
                    if (callback != null) {
                        callback.onSuccess(null);
                    }
                }

                @Override
                public void onFail(String errMsg, Exception error) {
                    MSCLog.i(Constants.PRELOAD_BIZ, "cleanPreloadApp failed:", appId);
                    if (callback != null) {
                        callback.onFail("", new IllegalStateException());
                    }
                }

                @Override
                public void onCancel() {
                }
            });
        } else {
            MSCLog.i(Constants.PRELOAD_BIZ, "runtime not exist,cleanPreloadApp end:", appId);
            if (callback != null) {
                callback.onSuccess(null);
            }
        }
    }

    @RestrictTo(RestrictTo.Scope.LIBRARY_GROUP)
    public void startAllTasks() {
        mTaskExecutor.resume();
    }

    @RestrictTo(RestrictTo.Scope.LIBRARY_GROUP)
    public void stopAllTasks() {
        mTaskExecutor.pause();
    }

    @RestrictTo(RestrictTo.Scope.LIBRARY_GROUP)
    public void destroyAllTasks() {
        if (mTaskExecutor == null) {
            return;
        }
        mTaskExecutor.clear();
    }

    @RestrictTo(RestrictTo.Scope.LIBRARY_GROUP)
    public void onLowMemory() {
        destroyAllTasks();
    }

    private static void preloadMSCAppInProcess(String appId, String targetPath, boolean preloadWebViewPage,
                                               String checkUpdateUrl, String mscVersion,
                                               Callback<PreloadResultData> callback, boolean checkPreloadPermission) {
        if (MSCRunningManager.isMultiRunningApp(appId)) {
            MSCLog.i(Constants.PRELOAD_BIZ, "The same appId app has preloaded!");
            PreloadManager.getInstance().putPreloadBizMetricsInfo(appId, "bizPreloadExisted", "bizPreloadExisted");
            callback.onCancel();
            return;
        }
        if (MSCMultiProcessConfig.get().inMultiProcessWhiteList(appId) && !MSCProcess.STANDARD.isCurrentProcess()) {
            //当前小程序在多进程白名单中，不是在子进程中调用的预热，切换子进程调用，子进程中默认预热WebView
            ((IIPCTask) IPCInvoke.getInvokeProxy(IPCTask.class, MSCProcess.STANDARD))
                    .preload(appId, targetPath, true, checkUpdateUrl, mscVersion, callback);
            return;
        }

        Callback<MSCRuntime> finalCallback = new Callback<MSCRuntime>() {
            @Override
            public void onSuccess(MSCRuntime data) {
                MSCLog.i(Constants.PRELOAD_BIZ, "preload success", data);
                if (callback != null) {
                    PreloadResultData resultData = new PreloadResultData.Builder(data.getAppId(), targetPath, preloadWebViewPage).build();
                    callback.onSuccess(resultData);
                }
            }

            @Override
            public void onFail(String errMsg, Exception error) {
                MSCLog.i(Constants.PRELOAD_BIZ, "preload fail", appId, targetPath, preloadWebViewPage, "errMsg:", errMsg);
                if (callback != null) {
                    callback.onFail(errMsg, error);
                }
            }

            @Override
            public void onCancel() {
                MSCLog.i(Constants.PRELOAD_BIZ, "preload cancel", appId, targetPath, preloadWebViewPage);
                if (callback != null) {
                    callback.onCancel();
                }
            }
        };
        if (checkPreloadPermission) {
            instance.preloadMSCApp(appId, targetPath, preloadWebViewPage, checkUpdateUrl, mscVersion, null, finalCallback);
        } else {
            instance.doPreloadApp(appId, targetPath, checkUpdateUrl, mscVersion, finalCallback, preloadWebViewPage);
        }
    }

    private interface IIPCTask {
        void preload(String appId, String targetPath, boolean preloadWebViewPage, String checkUpdateUrl, String mscVersion, Callback<PreloadResultData> callback);
    }

    private static class IPCTask implements IIPCTask {

        @Override
        public void preload(String appId, String targetPath, boolean preloadWebViewPage, String checkUpdateUrl, String mscVersion, Callback<PreloadResultData> callback) {
            preloadMSCAppInProcess(appId, targetPath, preloadWebViewPage, checkUpdateUrl, mscVersion, callback);
        }
    }

    /**
     * 决策切换进程、并可设置预热WebView
     *
     * @param appId              mscAppId
     * @param targetPath         目标页面
     * @param preloadWebViewPage 预热页面（仅限webview渲染）
     * @param callback           callback
     */
    @RestrictTo(RestrictTo.Scope.LIBRARY_GROUP)
    public static void preloadMSCAppInProcess(String appId, String targetPath, boolean preloadWebViewPage,
                                              String checkUpdateUrl, String mscVersion,
                                              Callback<PreloadResultData> callback) {
        // 业务通过预热接口在主进程触发业务预热，业务需要在子进程启动，故在子进程执行预热，且需要检查是否允许预热
        preloadMSCAppInProcess(appId, targetPath, preloadWebViewPage, checkUpdateUrl, mscVersion, callback, true);
    }
}
