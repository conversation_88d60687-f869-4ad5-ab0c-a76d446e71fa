package com.meituan.msc.modules.preload;

import android.os.SystemClock;
import android.support.annotation.StringDef;

import com.meituan.msc.common.report.MetricsEntry;
import com.meituan.msc.common.utils.ExceptionHelper;
import com.meituan.msc.common.utils.MSCResourceWatermarkUtil;
import com.meituan.msc.lib.BuildConfig;
import com.meituan.msc.modules.engine.MSCHornRollbackConfig;
import com.meituan.msc.modules.engine.MSCRuntime;
import com.meituan.msc.modules.engine.MSCRuntimeReporter;
import com.meituan.msc.modules.engine.RuntimeManager;
import com.meituan.msc.modules.reporter.CommonTags;
import com.meituan.msc.modules.reporter.MSCCommonTagReporter;
import com.meituan.msc.modules.reporter.MSCLog;
import com.meituan.msc.modules.reporter.MSCReporter;
import com.meituan.msc.modules.reporter.ReporterFields;

import java.lang.annotation.Retention;
import java.lang.annotation.RetentionPolicy;
import java.util.List;

/**
 * https://km.sankuai.com/page/1440213974
 */
public class RuntimePreloadReporter {

    public static final String PRELOAD_TYPE = "preloadType";
    public static final String IS_REUSE_BASE_PRELOAD = "isReuseBasePreload";

    /**
     * 预热成功率上报
     */
    public static class SuccessReporter extends MSCCommonTagReporter {

        private final MSCRuntime runtime;

        public static SuccessReporter create(MSCRuntime runtime) {
            return new SuccessReporter(runtime);
        }

        public SuccessReporter(MSCRuntime runtime) {
            super(CommonTags.build(runtime));
            this.runtime = runtime;
        }


        public void reportPreloadSuccess(MSCRuntime runtime, @PreloadType String preloadType) {
            this.reportPreloadSuccess(runtime, null, preloadType);
        }

        /**
         * 上报预热成功
         *
         * @param targetPath  targetPath
         * @param preloadType 预热类型
         */
        public void reportPreloadSuccess(MSCRuntime runtime, String targetPath, @PreloadType String preloadType) {
            long libcMem_e = MSCResourceWatermarkUtil.getAppLibcMemByte();
            long javaMem_e = MSCResourceWatermarkUtil.getAppUsedJavaMemByte();
            MSCRuntimeReporter runtimeReporter = runtime.getRuntimeReporter();
            MetricsEntry metricsEntry = record(ReporterFields.REPORT_MSC_RUNTIME_PRELOAD_SUCCESS_RATE)
                    .value(ReportValue.SUCCESS)
                    .tag(CommonTags.TAG_PAGE_PATH, targetPath)
                    .tag(PRELOAD_TYPE, preloadType)
                    .tag(CommonTags.TAG_LOAD_TYPE, runtime.getMSCAppModule().getBasePackageLoadType())
                    .tag(IS_REUSE_BASE_PRELOAD, runtime.isReuseBasePreloadWhenPreloadBiz());
            if (MSCHornRollbackConfig.enableReportPreloadAndKeepAlive()) {
                metricsEntry.tag(IS_REUSE_BASE_PRELOAD, runtime.isReuseBasePreloadWhenPreloadBiz())
                    .tag(CommonTags.TAG_LIBC_MEMORY_BEGIN, runtimeReporter.getPreloadLibcMemoryByteBegin())
                    .tag(CommonTags.TAG_LIBC_MEMORY_END, libcMem_e)
                    .tag(CommonTags.TAG_JAVA_MEMORY_BEGIN, runtimeReporter.getPreloadUsedJavaMemoryByteBegin())
                    .tag(CommonTags.TAG_JAVA_MEMORY_END, javaMem_e)
                    .tag(CommonTags.TAG_PRELOAD_TASK_FROM_APP_START, runtimeReporter.getPreloadTaskFromApplicationStartTime())
                    .tag(CommonTags.TAG_CURRENT_BIZ_PRELOAD_COUNT, RuntimeManager.getPreloadBizAppIds().size())
                    .tag(CommonTags.TAG_CURRENT_KEEP_ALIVE_COUNT, RuntimeManager.getKeepAliveAppSize())
                    .tag(CommonTags.TAG_BIZ_PRELOAD_MAX_COUNT, MSCHornPreloadConfig.get().getConfig().preloadAppLimitCount)
                    .tag(CommonTags.TAG_KEEP_ALIVE_MAX_COUNT, RuntimeManager.getKeepAliveMaxSize());

                if (BIZ_PRELOAD.equals(preloadType)) {
                    PendingBizPreloadTasksManager.PreloadBizData preloadBizData = PendingBizPreloadTasksManager.getInstance().getBizDataByAppId(runtime.getAppId());
                    if (preloadBizData != null) {
                        long pendingDuration = preloadBizData.preloadStartTime - preloadBizData.addToQueueTime;
                        metricsEntry.tag(CommonTags.TAG_PENDING_DURATION, pendingDuration);
                    }
                }
            }
            if (RuntimePreloadReporter.BASE_PRELOAD.equals(preloadType)) {
                // 基础库预热，Runtime中无AppId，手动在tag中添加
                metricsEntry.tag(CommonTags.TAG_MSC_APP_ID, "preloadBase");
            }
            metricsEntry.sendDelay();
        }
    }

    /**
     * 预热计数、预热耗时 上报
     */
    public static class CommonReporter extends MSCReporter {

        public static CommonReporter create() {
            return new CommonReporter();
        }

        private CommonReporter() {
            this.commonTag(CommonTags.SDK_VERSION, BuildConfig.AAR_VERSION);
        }

        /**
         * 上报预热耗时
         *
         * @param preloadType 预热类型
         */
        public void reportPreloadDuration(MSCRuntime runtime, long startTime, @PreloadType String preloadType) {
            this.reportPreloadDuration(runtime, startTime, "", preloadType);
        }

        /**
         * 上报预热耗时
         *
         * @param preloadType 预热类型
         */
        public void reportPreloadDuration(MSCRuntime runtime, long startTime, String targetPath, @PreloadType String preloadType) {
            long endTime = SystemClock.elapsedRealtime();
            long duration = endTime - startTime;
            if (duration <= 0) {
                MSCLog.i(runtime.TAG, "reportPreloadDurationError, startTime:", startTime, "endTime:", endTime, "duration:", duration);
            }
            List<String> preloadBizAppIds = RuntimeManager.getPreloadBizAppIds();
            record(ReporterFields.REPORT_MSC_RUNTIME_PRELOAD_DURATION)
                    .tags(CommonTags.build(runtime).generateCommonTags())
                    .tag(PRELOAD_TYPE, preloadType)
                    .tag(CommonTags.TAG_PAGE_PATH, targetPath)
                    .tag(IS_REUSE_BASE_PRELOAD, runtime.isReuseBasePreloadWhenPreloadBiz())
                    .tag("preloadBizAppIdsCount", preloadBizAppIds.size())
                    .tag("preloadBizAppIds", preloadBizAppIds)
                    .tag("totalMemorySizeOfV8", RuntimeManager.getAllPreloadRuntimeTotalMemorySize())
                    .tag("memorySizeOfV8", runtime.getLastJSMemoryUsageAfterPackageLoaded())
                    .value(duration)
                    .sendDelay();
        }

        /**
         * 上报预热调用总次数
         *
         * @param preloadType 预热类型
         */
        public void reportPreloadCount(@PreloadType String preloadType) {
            record(ReporterFields.REPORT_MSC_RUNTIME_PRELOAD_COUNT)
                    .tag(PRELOAD_TYPE, preloadType)
                    .sendDelay();
        }

        public void reportPreloadCount(String appId, String targetPath, @PreloadType String preloadType) {
            record(ReporterFields.REPORT_MSC_RUNTIME_PRELOAD_COUNT)
                    .tag(PRELOAD_TYPE, preloadType)
                    .tag(CommonTags.TAG_MSC_APP_ID, appId)
                    .tag(CommonTags.TAG_PAGE_PATH, targetPath)
                    .sendDelay();
        }

        /**
         * 上报预热失败
         *
         * @param throwable   throwable
         * @param appId       appId
         * @param targetPath  targetPath
         * @param preloadType 预热类型
         */
        public void reportPreloadFailed(Throwable throwable, String appId, String targetPath, @PreloadType String preloadType) {
            MetricsEntry metricsEntry = record(ReporterFields.REPORT_MSC_RUNTIME_PRELOAD_SUCCESS_RATE)
                    .value(ReportValue.FAILED)
                    .tag(CommonTags.TAG_MSC_APP_ID, appId)
                    .tag(CommonTags.TAG_PAGE_PATH, targetPath)
                    .tag(PRELOAD_TYPE, preloadType)
                    .tag(CommonTags.TAG_ERROR_CODE, ExceptionHelper.getErrorCode(throwable))
                    .tag(CommonTags.TAG_ERROR_MSG, ExceptionHelper.readStack(throwable));
            if (MSCHornRollbackConfig.enableReportPreloadAndKeepAlive()) {
                long libcMem_e = MSCResourceWatermarkUtil.getAppLibcMemByte();
                long javaMem_e = MSCResourceWatermarkUtil.getAppUsedJavaMemByte();
                metricsEntry.tag(CommonTags.TAG_LIBC_MEMORY_END, libcMem_e)
                    .tag(CommonTags.TAG_JAVA_MEMORY_END, javaMem_e)
                    .tag(CommonTags.TAG_CURRENT_BIZ_PRELOAD_COUNT, RuntimeManager.getPreloadBizAppIds().size())
                    .tag(CommonTags.TAG_CURRENT_KEEP_ALIVE_COUNT, RuntimeManager.getKeepAliveAppSize())
                    .tag(CommonTags.TAG_BIZ_PRELOAD_MAX_COUNT, MSCHornPreloadConfig.get().getConfig().preloadAppLimitCount)
                    .tag(CommonTags.TAG_KEEP_ALIVE_MAX_COUNT, RuntimeManager.getKeepAliveMaxSize());
                MSCRuntime runtime = RuntimeManager.getRuntimeWithAppId(appId);
                if (runtime != null) {
                    MSCRuntimeReporter runtimeReporter = runtime.getRuntimeReporter();
                    metricsEntry.tag(CommonTags.TAG_LIBC_MEMORY_BEGIN, runtimeReporter.getPreloadLibcMemoryByteBegin())
                        .tag(CommonTags.TAG_JAVA_MEMORY_BEGIN, runtimeReporter.getPreloadUsedJavaMemoryByteBegin())
                        .tag(CommonTags.TAG_PRELOAD_TASK_FROM_APP_START, runtimeReporter.getPreloadTaskFromApplicationStartTime());
                }
            }
            metricsEntry.sendDelay();
        }
    }

    public static final String BASE_PRELOAD = "basePreload";
    public static final String BIZ_PRELOAD = "bizPreload";

    @StringDef({BASE_PRELOAD, BIZ_PRELOAD})
    @Retention(RetentionPolicy.SOURCE)
    public @interface PreloadType {
    }
}
