package com.meituan.msc.modules.preload;

import android.support.annotation.Nullable;
import android.support.annotation.VisibleForTesting;

import com.meituan.msc.modules.engine.MSCHornRollbackConfig;
import com.meituan.msc.modules.engine.MSCLocalAppStackHelper;
import com.meituan.msc.modules.engine.RuntimeManager;

import java.util.Arrays;
import java.util.Collections;
import java.util.HashSet;
import java.util.LinkedHashSet;
import java.util.Set;

/**
 * 未使用业务预热管理类，超过最大预热限制后执行LRU清除策略
 */
public class UnusedPreloadBizAppManager {

    public static final UnusedPreloadBizAppManager instance = new UnusedPreloadBizAppManager();
    private static final Set<String> preloadedAppIdSet = Collections.synchronizedSet(new LinkedHashSet<>());
    // 引擎优先级策略种类标识：https://km.sankuai.com/collabpage/2707197355
    private static final Set<String> TAG_LRU_PRELOAD_STRATEGY = new HashSet<>(Arrays.asList("B", "D"));
    private static final Set<String> TAG_LAS_PRELOAD_STRATEGY = new HashSet<>(Arrays.asList("C", "E"));
    private UnusedPreloadBizAppManager() {
    }

    public void addPreloadApp(String appId) {
        if (isLRUPreloadStrategy()) {
            preloadedAppIdSet.remove(appId);
        }
        preloadedAppIdSet.add(appId);
    }

    public void removePreloadApp(String appId) {
        preloadedAppIdSet.remove(appId);
    }

    public boolean containsPreloadApp(String appId) {
        return preloadedAppIdSet.contains(appId);
    }

    public boolean cleanPreloadAppStrategy(String appId) {
        if (MSCHornRollbackConfig.enableAppSharedCountLimit()) {
            // 引擎数量阈值共享：https://km.sankuai.com/collabpage/2707197355
            int curRuntimeCount = RuntimeManager.getKeepAliveAppSize() + getAppSizePreloadedAndPending();
            if (curRuntimeCount >= RuntimeManager.MAX_PRELOAD_AND_KEEP_ALIVE_SHARE_ENGINE_COUNT) {
                if (getAppSizePreloadedAndPending() >= MSCHornPreloadConfig.get().getConfig().preloadAppLimitCount) {
                    return doCleanPreloadAppStrategy(PreloadTasksManager.instance, appId);
                } else {
                    RuntimeManager.destroyEarliestKeepAliveEngine(null);
                    return true;
                }
            }
        } else if (getAppSizePreloadedAndPending() >= MSCHornPreloadConfig.get().getConfig().preloadAppLimitCount) {
            return doCleanPreloadAppStrategy(PreloadTasksManager.instance, appId);
        }
        return true;
    }

    @VisibleForTesting
    static boolean doCleanPreloadAppStrategy(PreloadTasksManager preloadTasksManager, String appId) {
        if (preloadedAppIdSet.isEmpty()) {
            return true;
        }
        String toBeRemovedAppId;
        if (MSCHornRollbackConfig.readConfig().rollbackKeepPreloadApps) {
            toBeRemovedAppId = (String) preloadedAppIdSet.toArray()[0];
        } else {
            toBeRemovedAppId = findFirstPreloadAppIdAndCanRemove(appId);
        }
        if (toBeRemovedAppId == null) {
            return false;
        }
        // 先尝试清除等待队列，如未清除成功再检查缓存池
        boolean removeSuccess = PendingBizPreloadTasksManager.getInstance().removeTask(toBeRemovedAppId);
        if (!removeSuccess) {
            preloadTasksManager.cleanPreloadApp(toBeRemovedAppId, null);
        }
        return true;
    }

    @Nullable
    private static String findFirstPreloadAppIdAndCanRemove(String preloadAppId) {
        if (isLASPreloadStrategy()) {
            return findFirstRemovePreloadAppByLAS(preloadAppId);
        } else {
            synchronized (preloadedAppIdSet) {
                for (String appId : preloadedAppIdSet) {
                    if (!MSCHornPreloadConfig.get().disableCleanPreload(appId)) {
                        return appId;
                    }
                }
            }
        }
        return null;
    }

    @Nullable
    private static String findFirstRemovePreloadAppByLAS(String preloadAppId) {
        int minWeight = Integer.MAX_VALUE;
        synchronized (preloadedAppIdSet) {
            // 先找出所有可清理的 appId 及其权重
            for (String appId : preloadedAppIdSet) {
                if (MSCHornPreloadConfig.get().disableCleanPreload(appId)) {
                    continue;
                }
                int weight = MSCLocalAppStackHelper.getAppIdBizPreloadWeight(appId);
                if (weight < minWeight) {
                    minWeight = weight;
                }
            }
            // 如果有即将预热的appId，且其权重低于队列所有appId，则不进行预热
            if (preloadAppId != null) {
                int newAppWeight = MSCLocalAppStackHelper.getAppIdBizPreloadWeight(preloadAppId);
                if (newAppWeight < minWeight) {
                    return null;
                }
            }
            // 再次遍历，找到第一个权重等于 minWeight 的 appId（即最早的）
            for (String appId : preloadedAppIdSet) {
                if (MSCHornPreloadConfig.get().disableCleanPreload(appId)) {
                    continue;
                }
                int weight = MSCLocalAppStackHelper.getAppIdBizPreloadWeight(appId);
                if (weight == minWeight) {
                    return appId;
                }
            }
        }
        return null;
    }

    public int getAppSizePreloadedAndPending() {
        return RuntimeManager.getPreloadBizAppIds().size() + PendingBizPreloadTasksManager.getInstance().getPendingPreloadTaskSize();
    }

    private static boolean isLRUPreloadStrategy() {
        return TAG_LRU_PRELOAD_STRATEGY.contains(MSCHornRollbackConfig.localAppStackPriorityStrategy());
    }

    private static boolean isLASPreloadStrategy() {
        return TAG_LAS_PRELOAD_STRATEGY.contains(MSCHornRollbackConfig.localAppStackPriorityStrategy());
    }
}
