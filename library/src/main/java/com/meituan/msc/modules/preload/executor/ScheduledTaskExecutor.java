package com.meituan.msc.modules.preload.executor;

import android.support.annotation.NonNull;

import com.sankuai.android.jarvis.Jarvis;

import java.util.concurrent.ExecutorService;
import java.util.concurrent.ScheduledExecutorService;
import java.util.concurrent.TimeUnit;

/**
 * 内部使用ScheduledExecutorService实现的工作线程
 */
public class ScheduledTaskExecutor extends TaskExecutor {
    private final boolean mOwnExecutorService;
    private final ScheduledExecutorService mExecutorService;

    public ScheduledTaskExecutor(String name) {
        mExecutorService = Jarvis.newScheduledThreadPool(name, 1);
        mOwnExecutorService = true;
    }

    public ScheduledTaskExecutor(@NonNull ScheduledExecutorService executorService) {
        mExecutorService = executorService;
        mOwnExecutorService = false;
    }

    @Override
    public void addDelayedTask(@NonNull Task task, long delayMillis) {
        mExecutorService.schedule(new Runnable() {
            @Override
            public void run() {
                ScheduledTaskExecutor.this.addTask(task);
            }
        }, delayMillis, TimeUnit.MILLISECONDS);
    }

    @Override
    protected void executeTask(Task task) {
        mExecutorService.execute(new Runnable() {
            @Override
            public void run() {
                ScheduledTaskExecutor.super.executeTask(task);
            }
        });
    }

    @Override
    public void shutDown() {
        super.shutDown();
        if (mOwnExecutorService) {
            mExecutorService.shutdown();
        }
    }

    public ExecutorService getExecutor(){
        return mExecutorService;
    }
}
