package com.meituan.msc.modules.preload.executor;

import android.support.annotation.NonNull;
import android.support.annotation.Nullable;

import com.meituan.msc.modules.reporter.MSCLog;

public abstract class Task implements Comparable<Task> {

    public static final int DEFAULT_PRIORITY = 0;
    private static final String TAG = "Task";

    private final String mId;
    private TaskState mState;
    private long mAddTime;
    private int mPriority;

    public Task(@NonNull String id) {
        this(id, DEFAULT_PRIORITY);
    }

    public Task(@NonNull String id, int priority) {
        mId = id;
        mPriority = priority;
    }

    public boolean onPause() {
        return false;
    }

    public boolean onResume() {
        return false;
    }

    public boolean onCancel() {
        return true;
    }

    protected void throwException(Throwable e) {
        if (!(e instanceof RuntimeException)) {
            e = new RuntimeException(e);
        }
        MSCLog.eSync(TAG, e, "Task throwException");
        throw (RuntimeException)e;
    }

    protected void onTaskExisted(Task existedTask) {
        // ignore
    }

    /**
     * 值越大越有可能先被处理
     * @return
     */
    public int getPriority() {
        return mPriority;
    }

    @Nullable
    public String getId() {
        return mId;
    }

    public TaskState getState() {
        return mState;
    }

    public void setState(TaskState state) {
        this.mState = state;
    }

    public long getAddTime() {
        return mAddTime;
    }

    public void setAddTime(long addTime) {
        this.mAddTime = addTime;
    }

    @Override
    public int compareTo(Task o) {
        int r = o.getPriority() - getPriority();
        if (r != 0) {
            return r;
        }
        return (int)(mAddTime - o.mAddTime);
    }

    protected void setPriority(int priority) {
        this.mPriority = priority;
    }

    protected abstract void execute(TaskExecuteContext taskExecuteContext);

    /**
     * 异步执行任务
     */
    protected void executeAsync(TaskExecuteContext taskExecuteContext, Callback callback) {
        try {
            execute(taskExecuteContext);
            callback.onSuccess();
        } catch (Throwable e) {
            callback.onFailure(e);
        }
    }

    @Override
    public String toString() {
        return mId;
    }
}
