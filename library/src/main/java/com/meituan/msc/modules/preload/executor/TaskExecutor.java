package com.meituan.msc.modules.preload.executor;

import android.support.annotation.GuardedBy;
import android.support.annotation.NonNull;

import java.util.Collection;
import java.util.List;
import java.util.PriorityQueue;
import java.util.concurrent.CopyOnWriteArrayList;

/**
 * 任务执行器
 * - 支持暂停和恢复
 * - 支持重复任务检测
 * - 支持插队
 * - 支持取消
 * - 支持延时任务
 */
public abstract class TaskExecutor {

    // 安装优先级排序的列表，如果优先级相同，则后插入的在后面
    @GuardedBy("mWaitingTasks")
    // 排队中的任务列表
    protected final PriorityQueue<Task> mWaitingTasks = new PriorityQueue<>();
    // 正在执行中的任务列表
    protected final List<Task> mExecutingTasks = new CopyOnWriteArrayList<>();

    protected boolean mIsPaused = false;

    public TaskExecutor() {

    }

    /**
     * 恢复暂停状态
     */
    public synchronized void resume() {
        if (!mIsPaused) {
            return;
        }
        mIsPaused = false;
        for (Task task : mExecutingTasks) {
            resumeTask(task);
        }
    }

    /**
     * 暂停执行
     */
    public synchronized void pause() {
        if (mIsPaused) {
            return;
        }
        mIsPaused = true;
        // 暂停执行中的任务
        for (Task task : mExecutingTasks) {
            pauseTask(task);
        }
    }

    public synchronized void clear() {
        pause();
        clearTasks(mExecutingTasks);
        clearTasks(mWaitingTasks);
    }

    private void clearTasks(@NonNull Collection<Task> tasks) {
        synchronized (tasks) {
            for (Task task : tasks) {
                cancelTask(task, false);
            }
            tasks.clear();
        }
    }

    public void cancelTask(String taskKey) {
        Task task = getTaskByKey(taskKey);
        cancelTask(task, true);
    }

    protected void pauseTask(Task task) {
        if (task == null) {
            return;
        }
        TaskState state = task.getState();
        if (state == TaskState.PAUSED) {
            return;
        }
        if (state == TaskState.EXECUTING && task.onPause()) {
            task.setState(TaskState.PAUSED);
        }
    }

    protected void resumeTask(Task task) {
        if (task == null) {
            return;
        }
        if (task.getState() != TaskState.PAUSED) {
            return;
        }
        if (task.onResume()) {
            task.setState(TaskState.EXECUTING);
        }
    }

    protected void cancelTask(Task task, boolean removeTask) {
        if (task == null) {
            return;
        }
        if (removeTask) {
            synchronized (mWaitingTasks) {
                mWaitingTasks.remove(task);
            }
            mExecutingTasks.remove(task);
        }
        if (task.getState() == TaskState.CANCELED) {
            return;
        }
        if (task.onCancel()) {
            task.setState(TaskState.CANCELED);
        }
    }

    public Task getTaskByKey(String taskKey) {
        if (taskKey == null) {
            return null;
        }
        for (Task task : mExecutingTasks) {
            if (taskKey.equals(task.getId())) {
                return task;
            }
        }
        synchronized (mWaitingTasks) {
            for (Task task : mWaitingTasks) {
                if (taskKey.equals(task.getId())) {
                    return task;
                }
            }
        }
        return null;
    }

    public boolean existsTask(String taskKey) {
        return getTaskByKey(taskKey) != null;
    }

    public synchronized boolean addTask(@NonNull Task task) {
        // 判断是否有重复任务存在
        Task existedTask = getTaskByKey(task.getId());
        if (existedTask != null) {
            onTaskExisted(task, existedTask);
            return false;
        }
        long now = getCurrentTimeMillis();
        task.setAddTime(now);
        task.setState(TaskState.IN_QUEUE);
        synchronized (mWaitingTasks) {
            mWaitingTasks.offer(task);
        }
        onAddTask(task);
        return true;
    }

    public void addDelayedTask(@NonNull Task task, long delayMillis) {
        throw new UnsupportedOperationException("Not implemented");
    }

    protected long getCurrentTimeMillis() {
        return System.currentTimeMillis();
    }

    protected void onAddTask(Task task) {
        pollTaskToExecute();
    }

    protected void onTaskSuccess(Task task) {
        task.setState(TaskState.FINISHED);
        mExecutingTasks.remove(task);
        pollTaskToExecute();
    }

    protected void pollTaskToExecute() {
        if (!canPollTaskToExecute()) {
            return;
        }

        Task task = pollTask();
        if (task != null) {
            TaskState state = task.getState();
            if (state == TaskState.EXECUTING) {
                throw new IllegalStateException(String.format("The task %s is already executed.", task));
            }
            task.setState(TaskState.EXECUTING);
            mExecutingTasks.add(task);
            executeTask(task);
        }
    }

    protected boolean canPollTaskToExecute() {
        // 已经暂停了不执行
        return !mIsPaused && mExecutingTasks.isEmpty();
    }

    protected void onTaskThrowException(Task task, Throwable e) {
        task.throwException(e);
        task.setState(TaskState.FAILED);
    }

    protected void onTaskExisted(Task task, Task existedTask) {
        task.onTaskExisted(existedTask);
        task.setState(TaskState.FAILED);
    }

    protected void executeTask(Task task) {
        task.executeAsync(new TaskExecuteContext(this), new Callback() {
            @Override
            public void onSuccess() {
                onTaskSuccess(task);
            }

            @Override
            public void onFailure(Throwable throwable) {
                onTaskThrowException(task, throwable);
            }
        });
    }

    protected Task pollTask() {
        synchronized (mWaitingTasks) {
            return mWaitingTasks.poll();
        }
    }

    /**
     * 获取当前的所有任务里权重最大的任务的权重值，可用于添加最优先的任务
     * @return
     */
    public int getCurrentMaxPriority() {
        if (mWaitingTasks.isEmpty()) {
            return 0;
        }
        return mWaitingTasks.peek().getPriority();
    }

    public void shutDown() {
        clear();
    }
}
