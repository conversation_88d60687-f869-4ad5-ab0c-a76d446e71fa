package com.meituan.msc.modules.reporter;

import static com.meituan.msc.modules.reporter.MSCCommonTagReporter.CACHE_VALUE;
import static com.meituan.msc.modules.reporter.MSCCommonTagReporter.DEV_VALUE;
import static com.meituan.msc.modules.reporter.MSCCommonTagReporter.NETWORK_VALUE;
import static com.meituan.msc.modules.reporter.MSCCommonTagReporter.UNKNOWN_VALUE;

import android.support.annotation.Nullable;
import android.text.TextUtils;

import com.meituan.android.cipstorage.CIPSStrategy;
import com.meituan.android.mercury.msc.adaptor.bean.MSCAppMetaInfo;
import com.meituan.msc.common.process.MSCProcess;
import com.meituan.msc.common.utils.TimeUtil;
import com.meituan.msc.extern.MSCEnvHelper;
import com.meituan.msc.lib.BuildConfig;
import com.meituan.msc.modules.api.report.MSCReportBizTagsManager;
import com.meituan.msc.modules.engine.MSCApp;
import com.meituan.msc.modules.engine.MSCRuntime;
import com.meituan.msc.modules.engine.RuntimeSource;
import com.meituan.msc.modules.engine.RuntimeStateBeforeLaunch;
import com.meituan.msc.modules.page.render.BaseRenderer;
import com.meituan.msc.modules.page.render.webview.IWebView;
import com.meituan.msc.modules.page.render.webview.MSCWebViewRenderer;
import com.meituan.msc.modules.page.render.webview.WebViewCacheManager;
import com.meituan.msc.modules.page.render.webview.impl.MTWebViewImp;
import com.meituan.msc.modules.page.render.webview.impl.NormalWebView;
import com.meituan.msc.modules.preload.MSCHornPreloadConfig;
import com.meituan.msc.modules.storage.StorageManageUtil;
import com.meituan.msc.modules.update.MSCAppModule;
import com.meituan.msc.modules.update.bean.AppMetaInfoWrapper;
import com.meituan.msc.modules.update.bean.PackageInfoWrapper;
import com.meituan.mtwebkit.MTWebView;
import com.meituan.mtwebkit.internal.MTWebViewConstants;

import java.lang.ref.WeakReference;
import java.util.HashMap;
import java.util.Map;

public class CommonTags {
    public static final String TAG_MSC_APP_ID = "mscAppId";
    public static final String TAG_MSC_APP_VERSION = "mscAppVersion";
    public static final String TAG_APP_STATE = "appState";
    public static final String TAG_MSC_ENV = "env";
    /**
     * 基础库版本号
     */
    public static final String TAG_BASE_PKG_VERSION = "basePkgVersion";
    public static final String TAG_PUBLISH_ID = "publishId";
    public static final String TAG_BUILD_ID = "buildId";
    public static final String TAG_PAGE_PATH = "pagePath";
    public static final String TAG_PURE_PAGE_PATH = "purePath";
    public static final String TAG_WIDGET = "widget";
    /**
     * 运行时是新创建的还是用的预热的，但是对于基础库预热过程中的指标上报，该值应该是 new
     */
    public static final String TAG_RUNTIME_SOURCE = "runtimeSource";
    public static final String TAG_SERVICE_PRE_INIT = "servicePreInit";
    public static final String TAG_RUNTIME_STATE_BEFORE_LAUNCH = "runtimeStateBeforeLaunch";
    public static final String TAG_PAGE_START_FROM_APPLICATION_START = "pageStartFromApplicationStart";
    public static final String TAG_ORIGIN_RUNTIME_SOURCE = "originRuntimeSource";
    public static final String TAG_PRELOAD_FROM_APPLICATION_START = "preloadFromApplicationStart";
    /**
     * 判断在页面加载开始到当前打点结束之间，当前页面所使用的包（包含基础库包）是否都是从缓存来的。
     */
    public static final String TAG_PKG_MODE = "pkgMode";
    /**
     * 更加细致的判断每个包是从缓存中加载还是从网络加载
     */
    public static final String TAG_PKG_MODE_DETAIL = "pkgModeDetail";
    /**
     * 创建新引擎或者使用基础库预热的引擎时，根据是否检查来设置该字段；
     * 其他情况时，该值一定是cache；
     * 基础库如果是从网络拿的，该值也是cache。
     */
    public static final String TAG_CHECK_UPDATE_MODE = "checkUpdateMode";
    /**
     * 是否是打开的第一个页面（旧版，redirectTo、relaunch、switchTab为true不合理）
     */
    public static final String TAG_IS_FIRST_PAGE = "isFirstPage";
    /**
     * 是否是打开的第一个页面（新版）
     */
    public static final String TAG_IS_FIRST_PAGE_V2 = "isFirstPageV2";
    /**
     * 启动任务执行状态
     */
    public static final String TAG_LAUNCH_TASKS_EXECUTE_STATES = "launchTasksExecuteStates";
    /**
     * 容器类型
     */
    public static final String TAG_RENDER_TYPE = "renderType";
    /**
     * 当容器为NativeRenderer时，在FFP时获取渲染过程已记录的关键动作
     */
    public static final String TAG_RENDER_ACTIONS = "renderActions";
    /**
     * 容器版本号
     */
    public static final String SDK_VERSION = "sdkVersion";
    /**
     * WebView版本号
     */
    public static final String TAG_CHROME_VERSION = "chrome";
    /**
     * WebView类型
     */
    public static final String TAG_WEB_VIEW_TYPE = "webViewType";
    /**
     * 业务公共维度数据（所有页面均上报）
     */
    public static final String TAG_BIZ_TAGS_FOR_APPID = "bizTagsForAppId";
    /**
     * 业务指定页面维度数据（指定页面上报）
     */
    public static final String TAG_BIZ_TAGS_FOR_PAGE = "bizTagsForPage";
    /**
     * 场景来源
     */
    public static final String TAG_SOURCE_FROM = "sourceFrom";
    /**
     * 包加载来源
     */
    public static final String TAG_LOAD_TYPE = "loadType";
    /**
     * 包类型
     */
    public static final String TAG_PKG_TYPE = "pkgType";
    /**
     * 包名
     */
    public static final String TAG_PKG_NAME = "pkgName";

    public static final String TAG_LOAD_PKG_Details = "loadPackageDetails";
    /**
     * 错误码
     */
    public static final String TAG_ERROR_CODE = "errorCode";
    /**
     * 错误描述
     */
    public static final String TAG_ERROR_MSG = "errorMsg";
    /**
     * 进程名
     */
    private static final String TAG_PROCESS_NAME = "processName";
    /**
     * 是否是预创建
     */
    public static final String TAG_IS_PRE_CREATE = "isPreCreate";
    /**
     * 是否开启startPage提前
     */
    public static final String TAG_START_PAGE_ADVANCE = "startPageAdvance";

    /**
     * 元信息是否来源S3降级
     */
    public static final String TAG_IS_META_FROM_DEGRADE = "isMetaFromDegrade";

    /**
     * 是否三方小程序
     */
    public static final String TAG_IS_EXTERNAL_APP = "externalApp";

    /**
     * JS 性能埋点(FFP 结束时，起点～FFP 结束时)
     */
    public static final String TAG_JS_DESC = "js_desc";

    /**
     * JS 性能埋点(FFP结束时，起点～最后一批 JS Batch 时)
     */
    public static final String TAG_LAST_JS_DESC = "last_js_desc";
    /**
     * JS 性能埋点(FFP结束时，起点～最后一批 JS Batch 时)
     */
    public static final String TAG_LAST_JS_TIME = "last_js_time";

    public static final String TAG_LIBC_MEMORY_BEGIN = "libcMem_b";
    public static final String TAG_LIBC_MEMORY_END = "libcMem_e";
    public static final String TAG_JAVA_MEMORY_BEGIN = "JavaMem_b";
    public static final String TAG_JAVA_MEMORY_END = "JavaMem_e";
    public static final String TAG_PENDING_DURATION = "pendingDuration";
    public static final String TAG_PRELOAD_TASK_FROM_APP_START = "preloadTaskFromApplicationStart";
    public static final String TAG_CURRENT_BIZ_PRELOAD_COUNT = "currentBizPreloadCount";
    public static final String TAG_CURRENT_KEEP_ALIVE_COUNT = "currentKeepAliveCount";
    public static final String TAG_BIZ_PRELOAD_MAX_COUNT = "bizPreloadMaxCount";
    public static final String TAG_KEEP_ALIVE_MAX_COUNT = "keepAliveMaxCount";
    public static final String TAG_KEEP_ALIVE_MAX_TIME = "keepAliveMaxTime";
    public static final String TAG_BLINK_ONLINE_TRACE = "blink_online_trace";
    public static final String TAG_FFP_START = "ffpStartTS";

    public static final String TAG_NATIVE_DOM_ENABLED = "native_dom_enabled";

    public static CommonTags build(MSCRuntime runtime) {
        return build(runtime, null, null);
    }

    public static CommonTags build(MSCRuntime runtime, BaseRenderer renderer, Boolean isFirstPage) {
        return new CommonTags(runtime, renderer, null, isFirstPage, null, null);
    }

    public static CommonTags build(MSCRuntime runtime, BaseRenderer renderer, String pagePath, Boolean isFirstPage,
                                   Boolean isWidget, Boolean isFirstPageV2) {
        return new CommonTags(runtime, renderer, pagePath, isFirstPage, isWidget, isFirstPageV2);
    }

    private final WeakReference<MSCRuntime> runtimeWeakReference;
    private final WeakReference<BaseRenderer> rendererWeakReference;
    private final String pagePath;
    private final Boolean isWidget;
    private final Boolean isFirstPage;
    private final Boolean isFirstPageV2;

    protected CommonTags(MSCRuntime runtime, BaseRenderer renderer, String pagePath, Boolean isFirstPage, Boolean isWidget, Boolean isFirstPageV2) {
        this.runtimeWeakReference = new WeakReference<>(runtime);
        this.rendererWeakReference = new WeakReference<>(renderer);
        this.pagePath = pagePath;
        this.isFirstPage = isFirstPage;
        this.isWidget = isWidget;
        this.isFirstPageV2 = isFirstPageV2;
    }

    public Map<String, Object> generateCommonTags() {
        MSCRuntime runtime = runtimeWeakReference.get();
        BaseRenderer renderer = rendererWeakReference.get();
        Map<String, Object> commonTags = new HashMap<>();
        commonTags.put(SDK_VERSION, BuildConfig.AAR_VERSION);
        // TODO by chendacai: 2022/3/3 获取顶部Activity中的 MSCRuntime
        AppMetaInfoWrapper metaInfo = null;
        if (runtime != null) {
            MSCAppModule mscAppModule = runtime.getMSCAppModule();
            if (mscAppModule != null) {
                metaInfo = mscAppModule.getMetaInfo();
            }
            commonTags.put("runtime", runtime.TAG);
        }
        String pagePath = fixTagValue(this.pagePath != null ? this.pagePath : (renderer != null ? renderer.pageData.mPagePath : null));
        String appId = getAppId(runtime);
        String appVersion = metaInfo != null ? metaInfo.getVersion() : UNKNOWN_VALUE;
        String basePkgVersion = getBasePkgVersion(runtime);
        String publishId = metaInfo != null ? metaInfo.getPublishId() : UNKNOWN_VALUE;
        String buildId = metaInfo != null ? metaInfo.getBuildId() : UNKNOWN_VALUE;
        String purePath = pagePath != null ? removePathQuery(pagePath) : UNKNOWN_VALUE;
        String runtimeSource = runtime != null ? RuntimeSource.toReportString(runtime.getSource()) : UNKNOWN_VALUE;
        String runtimeStateBeforeLaunch = runtime != null ? RuntimeStateBeforeLaunch.toReportString(runtime.getRuntimeStateBeforeLaunch()) : UNKNOWN_VALUE;
        String originSource = runtime != null ? RuntimeSource.toReportString(runtime.getOriginSource()) : UNKNOWN_VALUE;
        String pkgMode = getPkgMode(pagePath, runtime, metaInfo);
        String updateMode = metaInfo != null ? (metaInfo.isFromCache() ? CACHE_VALUE : NETWORK_VALUE) : UNKNOWN_VALUE;
        String renderType = getRenderType(renderer);
        boolean servicePreInit = runtime != null ? runtime.isServicePreInit() : false;
        boolean externalApp = metaInfo != null ? metaInfo.getExternalApp() : false;

        if (metaInfo != null) {
            boolean isMetaFromDegrade = false;
            if (metaInfo.getMetaFrom() == MSCAppMetaInfo.META_FROM_S3) {
                isMetaFromDegrade = true;
            }
            commonTags.put(TAG_IS_META_FROM_DEGRADE, isMetaFromDegrade);
        }

        if (!MSCEnvHelper.getEnvInfo().isProdEnv()) {
            // 非线上环境，处理下线下的时间戳参数值，避免污染线上指标
            if (TimeUtil.isUnixTime(publishId)) {
                publishId = DEV_VALUE;
            }
            if (TimeUtil.isUnixTime(basePkgVersion)) {
                basePkgVersion = DEV_VALUE;
            }
            if (appId != null && appId.startsWith("app_")) {
                appId = DEV_VALUE;
            }
            if (TimeUtil.isUnixTime(appVersion)) {
                appVersion = DEV_VALUE;
            }
        }

        commonTags.put(TAG_MSC_APP_ID, appId);
        commonTags.put(TAG_MSC_APP_VERSION, appVersion);
        commonTags.put(TAG_BASE_PKG_VERSION, basePkgVersion);
        commonTags.put(TAG_PUBLISH_ID, publishId);
        commonTags.put(TAG_BUILD_ID, buildId);
        commonTags.put(TAG_PAGE_PATH, pagePath != null ? pagePath : UNKNOWN_VALUE);
        commonTags.put(TAG_WIDGET, isWidget != null ? String.valueOf(isWidget) : UNKNOWN_VALUE);
        // FIXME by chendacai: 由于异动归因不支持对维度值进行处理，临时增加去掉页面路径中的query部分的维度，后面可删掉
        commonTags.put(TAG_PURE_PAGE_PATH, purePath);
        commonTags.put(TAG_RUNTIME_SOURCE, runtimeSource);
        commonTags.put(TAG_SERVICE_PRE_INIT, servicePreInit);
        commonTags.put(TAG_RUNTIME_STATE_BEFORE_LAUNCH, runtimeStateBeforeLaunch);
        if (!TextUtils.equals(originSource, UNKNOWN_VALUE)) {
            commonTags.put(TAG_ORIGIN_RUNTIME_SOURCE, originSource);
        }

        commonTags.put(TAG_PKG_MODE, pkgMode);
        commonTags.put(TAG_PKG_MODE_DETAIL, getPkgModeDetail(pagePath, runtime, metaInfo));
        commonTags.put(TAG_CHECK_UPDATE_MODE, updateMode);
        commonTags.put(TAG_IS_FIRST_PAGE, isFirstPage != null ? String.valueOf(isFirstPage) : UNKNOWN_VALUE);
        commonTags.put(TAG_IS_FIRST_PAGE_V2, isFirstPageV2 != null ? String.valueOf(isFirstPageV2) : UNKNOWN_VALUE);
        commonTags.put(TAG_IS_PRE_CREATE, renderer != null && renderer.isPreCreate());
        commonTags.put(TAG_RENDER_TYPE, renderType);
        commonTags.put(TAG_MSC_ENV, MSCEnvHelper.getEnvInfo().isProdEnv() ? "prod" : "test");
        if (MSCAppModule.WEB_VIEW_VERSION != null) {
            commonTags.put(TAG_CHROME_VERSION, MSCAppModule.WEB_VIEW_VERSION);
        }
        String webViewType = getWebViewType(getWebViewTypeFromRenderer());
        String appState = getAppState(runtime);
        commonTags.put(TAG_WEB_VIEW_TYPE, webViewType);
        commonTags.put(TAG_APP_STATE, appState);
        commonTags.put(TAG_PROCESS_NAME, MSCProcess.getCurrentProcessName());
        commonTags.put(TAG_IS_EXTERNAL_APP, externalApp);

        // 存储优化相关维度
        CIPSStrategy.LRUConfig lruConfig = StorageManageUtil.getLRUConfigWithFramework();
        commonTags.put("storageUserType", StorageManageUtil.getStorageType());
        commonTags.put("cleanStrategy", lruConfig.strategy);
        commonTags.put("currentLRUSize", lruConfig.maxSize);
        commonTags.put("currentLRUDuration", lruConfig.duration);
        commonTags.put("autoCleanABTestKey", StorageManageUtil.getStorageTestStrategy());

        // 上报业务公共维度 https://km.sankuai.com/collabpage/1573692797
        MSCReportBizTagsManager.BizTagsData bizTags = MSCReportBizTagsManager.getInstance().getBizTags(appId, null);
        if (bizTags != null) {
            commonTags.put(TAG_BIZ_TAGS_FOR_APPID, bizTags.getBizTagsForAppId());
        }
        commonTags.put(TAG_START_PAGE_ADVANCE, MSCHornPreloadConfig.enableAppRouteTask(appId));
        return commonTags;
    }

    private String removePathQuery(String path) {
        if (path == null) {
            return null;
        }
        int i = path.indexOf('?');
        if (i < 0) {
            return path;
        }
        return path.substring(0, i);
    }

    private WebViewCacheManager.WebViewType getWebViewTypeFromRenderer() {
        BaseRenderer renderer = rendererWeakReference.get();
        return getWebViewTypeFromRenderer(renderer);
    }

    public static WebViewCacheManager.WebViewType getWebViewTypeFromRenderer(BaseRenderer renderer) {
        if (renderer instanceof MSCWebViewRenderer) {
            IWebView iWebView = ((MSCWebViewRenderer) renderer).getIWebView();
            if (iWebView instanceof MTWebViewImp) {
                MTWebView mtWebView = (MTWebView) iWebView.getWebView();
                if (mtWebView != null) {
                    return MTWebViewConstants.TYPE_MTWEBVIEW_MT.equals(mtWebView.getMTWebViewType())
                            ? WebViewCacheManager.WebViewType.MT_WEB_VIEW : WebViewCacheManager.WebViewType.MT_WEB_VIEW_SYSTEM;
                }
            } else if (iWebView instanceof NormalWebView) {
                return WebViewCacheManager.WebViewType.CHROME;
            }
        }
        return MSCAppModule.WEB_VIEW_TYPE;
    }

    public static String getWebViewType(@Nullable WebViewCacheManager.WebViewType webViewType) {
        if (webViewType == null) {
            return "";
        }
        switch (webViewType) {
            case CHROME:
                return "chrome";
            case X5:
                return "x5";
            case MT_WEB_VIEW:
                return "mtwebview";
            case MT_WEB_VIEW_SYSTEM:
                return "mtwebview_system";
        }
        return "";
    }

    private String getBasePkgVersion(MSCRuntime runtime) {
        if (runtime == null || runtime.getMSCAppModule() == null) {
            return UNKNOWN_VALUE;
        }
        return runtime.getMSCAppModule().getBasePkgVersion();
    }

    private static String getRenderType(BaseRenderer renderer) {
        if (renderer == null || renderer.getType() == null) {
            return UNKNOWN_VALUE;
        }
        return renderer.getType().toString();
    }

    public static String getAppId(MSCRuntime runtime) {
        return runtime == null ? UNKNOWN_VALUE : runtime.getAppId();
    }

    public String getPagePath() {
        return this.pagePath;
    }

    private static String getAppState(MSCRuntime runtime) {
        if (runtime == null) {
            return UNKNOWN_VALUE;
        }
        MSCApp app = runtime.getApp();
        if (app == null) {
            return "background";
        }
        return app.isAppInForeground() ? "foreground" : "background";
    }

    private static String fixTagValue(String value) {
        return value != null ? value : UNKNOWN_VALUE;
    }

    private static String getPkgMode(String pagePath, MSCRuntime runtime, AppMetaInfoWrapper metaInfo) {
        if (pagePath == null || metaInfo == null) {
            return UNKNOWN_VALUE;
        }
        PackageInfoWrapper subPackage = metaInfo.getOrCreateSubPackageWrapperByPath(pagePath);
        // 主包、子包、基础库包都是从缓存中获取的，那么 pkgMode 才是 cache
        boolean isFromCache = isFromCache(subPackage) &&
                isFromCache(metaInfo.mainPackageCached) && isFromCache(getBasePackage(runtime));
        return isFromCache ? CACHE_VALUE : NETWORK_VALUE;
    }

    /**
     * 1代表来自缓存，0代表来自网络
     * 返回值按照 基础库-主包-子包 顺序进行定义（子包不存在时，值为1）
     */
    private static String getPkgModeDetail(String pagePath, MSCRuntime runtime, AppMetaInfoWrapper metaInfo) {
        if (pagePath == null || metaInfo == null) {
            return UNKNOWN_VALUE;
        }
        StringBuilder result = new StringBuilder();
        // 基础库包
        result.append(isFromCache(getBasePackage(runtime)) ? "1" : "0");
        // 主包
        result.append(isFromCache(metaInfo.mainPackageCached) ? "1" : "0");
        // 子包
        PackageInfoWrapper subPackage = metaInfo.getOrCreateSubPackageWrapperByPath(pagePath);
        result.append(isFromCache(subPackage) ? "1" : "0");
        return result.toString();
    }

    public static PackageInfoWrapper getBasePackage(MSCRuntime runtime) {
        if (runtime.getMSCAppModule() == null) {
            return null;
        }
        return runtime.getMSCAppModule().getBasePackage();
    }

    private static boolean isFromCache(PackageInfoWrapper pkg) {
        return pkg == null || !pkg.isFromNet();
    }
}
