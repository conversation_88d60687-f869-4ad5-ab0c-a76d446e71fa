package com.meituan.msc.modules.reporter;

import android.app.Activity;
import android.arch.lifecycle.Lifecycle;
import android.net.Uri;
import android.support.annotation.Nullable;
import android.support.v4.app.Fragment;
import android.support.v4.app.FragmentActivity;
import android.support.v4.app.FragmentManager;

import com.meituan.crashreporter.CrashReporter;
import com.meituan.crashreporter.container.ContainerRecorder;
import com.meituan.msc.modules.api.report.MSCReportBizTagsManager;
import com.meituan.msc.modules.container.ApplicationLifecycleMonitor;
import com.meituan.msc.modules.container.IMSCContainer;
import com.meituan.msc.modules.container.MSCActivity;
import com.meituan.msc.modules.container.MSCWidgetFragment;

import java.util.List;
import java.util.Map;

/**
 * https://km.sankuai.com/page/1050541162
 * Created by letty on 2019/12/30.
 **/
public class CrashReporterHelper {


    private static final String TAG = "CrashReporterHelper";

    public static ContainerRecorder getContainerRecorder() {
        return CrashReporter.obtainContainerRecorder("msc");
    }

    public static void pushPage(String url, String appId, String openType, boolean isWidget) {
        String path = new Uri.Builder()
                .scheme("msc")
                .authority("msc")
                .appendQueryParameter("targetPath", url)
                .appendQueryParameter("appId", appId)
                .appendQueryParameter("openType", openType)
                .appendQueryParameter("isWidget", String.valueOf(isWidget))
                .build().toString();
        //MSCLog.d("pushPage",path);
        CrashReporterHelper.getContainerRecorder().pushPage(path);
    }

    public static void popPage() {
        CrashReporterHelper.getContainerRecorder().popPage();
    }

    /**
     * 获取栈顶小程序页面 appId
     * 如果栈顶页面非小程序页面，则返回null
     *
     * @return appId
     */
    @Nullable
    public static String getCurrentMSCForegroundAppId(boolean isGetWidgetContainer) {
        if (ApplicationLifecycleMonitor.MSC.getState().isAtLeast(Lifecycle.State.CREATED)) {
            Activity lastActivity = ApplicationLifecycleMonitor.ALL.getLastActivity();
            IMSCContainer topMSCContainer = getTopMSCContainer(lastActivity, isGetWidgetContainer);
            if (topMSCContainer != null) {
                return topMSCContainer.getMPAppId();
            }
            return null;
        }
        return null;
    }

    /**
     * 获取栈顶小程序页面 appVersion
     * 如果栈顶页面非小程序页面，则返回null
     *
     * @return appVersion
     */
    @Nullable
    public static String getCurrentMSCForegroundAppVersion(boolean isGetWidgetContainer) {
        if (ApplicationLifecycleMonitor.MSC.getState().isAtLeast(Lifecycle.State.CREATED)) {
            Activity lastActivity = ApplicationLifecycleMonitor.ALL.getLastActivity();
            IMSCContainer topMSCContainer = getTopMSCContainer(lastActivity, isGetWidgetContainer);
            if (topMSCContainer != null) {
                return topMSCContainer.getMPAppVersion();
            }
            return null;
        }
        return null;
    }

    @Nullable
    private static IMSCContainer getTopMSCContainer(Activity lastActivity, boolean isGetWidgetContainer) {
        if (isGetWidgetContainer) {
            return getTopMSCWidgetFragment(lastActivity);
        } else {
            if (lastActivity instanceof MSCActivity) {
                return ((IMSCContainer) lastActivity);
            }
        }
        return null;
    }

    /**
     * 获取栈顶小程序页面 pagePath
     * 如果栈顶页面非小程序页面，则返回null
     *
     * @return pagePath
     */
    @Nullable
    public static String getCurrentMSCForegroundPagePath(boolean isGetWidgetContainer) {
        if (ApplicationLifecycleMonitor.MSC.getState().isAtLeast(Lifecycle.State.CREATED)) {
            Activity lastActivity = ApplicationLifecycleMonitor.ALL.getLastActivity();
            IMSCContainer topMSCContainer = getTopMSCContainer(lastActivity, isGetWidgetContainer);
            if (topMSCContainer != null) {
                return topMSCContainer.getTopPagePath();
            }
            return null;
        }
        return null;
    }

    /**
     * 获取栈顶小程序页面 业务维度数据
     * 如果栈顶页面非小程序页面，则返回null
     *
     * @return pagePath
     */
    @Nullable
    public static Map<String, String> getCurrentMSCForegroundPageBizTags(boolean isGetWidgetContainer) {
        if (ApplicationLifecycleMonitor.MSC.getState().isAtLeast(Lifecycle.State.CREATED)) {
            Activity lastActivity = ApplicationLifecycleMonitor.ALL.getLastActivity();
            IMSCContainer topMSCContainer = getTopMSCContainer(lastActivity, isGetWidgetContainer);
            if (topMSCContainer != null) {
                return topMSCContainer.getTopPageBizTags();
            }
            return null;
        }
        return null;
    }

    @Nullable
    private static IMSCContainer getTopMSCWidgetFragment(Activity lastActivity) {
        if (lastActivity instanceof FragmentActivity) {
            FragmentActivity activity = (FragmentActivity) lastActivity;
            FragmentManager supportFragmentManager = activity.getSupportFragmentManager();
            if (supportFragmentManager == null) {
                return null;
            }
            List<Fragment> fragments = supportFragmentManager.getFragments();
            for (Fragment fragment : fragments) {
                if (fragment == null || !fragment.isVisible()) {
                    continue;
                }
                // TODO: 2023/3/16 tianbin 目前仅取第一个，多个Widget均在前台的场景暂缓处理
                if (fragment instanceof MSCWidgetFragment) {
                    return (IMSCContainer) fragment;
                }
                // 闪购、医药业务Widget接入使用ChildFragmentManager添加，这里多取一层
                // 递归遍历可能会影响Crash正常上报，暂不支持
                return getIMSCContainerFromChildFragmentManager(fragment);
            }
        }
        return null;
    }

    @Nullable
    private static IMSCContainer getIMSCContainerFromChildFragmentManager(Fragment fragment) {
        FragmentManager fragmentManager = fragment.getChildFragmentManager();
        List<Fragment> fragments = fragmentManager.getFragments();
        for (Fragment childFragment : fragments) {
            if (childFragment == null || !childFragment.isVisible()) {
                continue;
            }
            // TODO: 2023/3/16 tianbin 目前仅取第一个，多个Widget均在前台的场景暂缓处理
            if (childFragment instanceof MSCWidgetFragment) {
                return (IMSCContainer) childFragment;
            }
        }
        return null;
    }

    @Nullable
    public static Map<String, String> getCurrentMSCForegroundBizTagsForAppId(String appId) {
        MSCReportBizTagsManager.BizTagsData bizTags = MSCReportBizTagsManager.getInstance().getBizTags(appId, null);
        return bizTags != null ? bizTags.getBizTagsForAppId() : null;
    }
}
