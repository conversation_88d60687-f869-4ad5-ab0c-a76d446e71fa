package com.meituan.msc.modules.reporter;

import com.meituan.msc.modules.engine.MSCRuntimeReporter;

import java.util.Map;

public interface IRenderReporter {

    void reportJSSourceError(MSCRuntimeReporter reporter, long importScriptStartTimeNs, String jsFile);

    void reportFileError(MSCRuntimeReporter reporter, long importScriptStartTimeNs, String jsFile);

    void reportEvaluateError(MSCRuntimeReporter reporter, long importScriptStartTimeNs, String jsFile, Exception e);

    void reportRealTime(MSCRuntimeReporter reporter, long importScriptStartTimeNs, String jsFile);

    Map<String, Object> getRenderTags(int pageId, String appId, String path);

    boolean enableReportRenderMessage();

    boolean disableReport(String message);
}
