package com.meituan.msc.modules.reporter;

import android.text.TextUtils;

import java.util.LinkedList;
import java.util.List;
import java.util.Stack;
import java.util.concurrent.CopyOnWriteArrayList;


/**
 * 小程序启动相关埋点上报
 * <p>
 * todo  System.currentTimeMillis() => SystemClock.elapsedRealtime();
 * Created by letty on 2019/8/16.
 **/
public class JSErrorRecorder {

    private static final String TAG = "JSErrorRecorder";
    private Stack<PageRecord> mPageStack = new Stack<>();
    private LinkedList<String> mPageNavigationHistory = new LinkedList<>();
    private List<String> jSErrors = new CopyOnWriteArrayList<>();


    private static final int MAX_NAVIGATION_HISTORY_SIZE = 10;
    private static final int MAX_PAGE_STACK_SIZE = 20;
    private static final int MAX_NAVIGATION_RECORD_LENGTH = 128;


    /**
     * 记录页面打开
     * @param pagePath
     * @param pageId
     * @return
     */
    public JSErrorRecorder pushPage(String pagePath, String pageId) {
        // 超过最大数量，需要先移除最老的记录
        if (mPageStack.size() >= MAX_PAGE_STACK_SIZE){
            mPageStack.remove(0);
        }
        mPageStack.push(new PageRecord(pageId, pagePath));

        // 超过最大数量，需要先移除最老的记录
        if (mPageNavigationHistory.size() >= MAX_NAVIGATION_HISTORY_SIZE) {
            mPageNavigationHistory.remove(0);
            MSCLog.i(TAG, "pushPage remove first");
        }
        mPageNavigationHistory.add("push id=" + pageId + ",path=" + pagePath + "\\n");
        MSCLog.i(TAG, "pushPage", "push id=" + pageId + ",path=" + pagePath);
        return this;
    }

    /**
     * 记录页面退出
     * @param pagePath
     * @param pageId
     * @return
     */
    public JSErrorRecorder popPage(String pagePath, String pageId) {
        int index = mPageStack.search(new PageRecord(pageId, pagePath));
        popPages(index);
        return this;
    }

    /**
     * 记录页面退出
     * @param pagePath
     * @param pageId
     * @return
     */
    public JSErrorRecorder popToPage(String pagePath, String pageId) {
        int index = mPageStack.search(new PageRecord(pageId, pagePath));
        popPages(index-1);
        return this;
    }


    private void popPages(int index){
        for (int i = 1; i <= index; i++) {
            PageRecord record = mPageStack.pop();
            if (mPageNavigationHistory.size() >= MAX_NAVIGATION_HISTORY_SIZE) {
                mPageNavigationHistory.remove(0);
            }
            mPageNavigationHistory.add("pop id=" + record.getId() + ",path=" + record.getPath() + "\\n");
        }
    }

    /**
     * 获取当前页面栈，用户JSError和白屏时上报
     * @return
     */
    public String getPageStack() {
        StringBuilder sb = new StringBuilder();
        for (int i = mPageStack.size() - 1; i >= 0; i--) {
            PageRecord record = mPageStack.elementAt(i);
            if (record!=null){
                sb.append(getTrimedPageRecord(record.toString()));
            }
        }
        return sb.toString();
    }

    /**
     * 获取当页面跳转历史记录，最多10条，用户JSError和白屏时上报
     * @return
     */
    public String getPageNavigationHistory() {
        StringBuilder sb = new StringBuilder();
        try {
            for (int i = mPageNavigationHistory.size() - 1; i >= 0; i--) {
                sb.append(getTrimedPageRecord(mPageNavigationHistory.get(i)));
            }
        } catch (NullPointerException e) {
            MSCLog.e(TAG, e, "getPageNavigationHistory");
            return "unknow";
        }
        return sb.toString();
    }

    private String getTrimedPageRecord(String record){
        if(TextUtils.isEmpty(record)) return "";
        else {
            if (record.length()>MAX_NAVIGATION_RECORD_LENGTH){
                return record.substring(0, MAX_NAVIGATION_RECORD_LENGTH);
            } else {
                return record;
            }
        }
    }



    public void start(){
        jSErrors.clear();
    }

    public void stop(){
        jSErrors.clear();
    }

    public String getJSErrors(){
        StringBuilder sb = new StringBuilder();
        for (String s : jSErrors) {
            if (s!=null){
                sb.append(getTrimedPageRecord(s)+"\\n");
            }
        }
        return sb.toString();
    }
}
