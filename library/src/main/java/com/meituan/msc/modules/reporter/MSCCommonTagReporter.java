package com.meituan.msc.modules.reporter;

import android.support.annotation.NonNull;
import android.support.annotation.Nullable;
import android.text.TextUtils;

import com.meituan.msc.common.executor.MSCExecutors;
import com.meituan.msc.common.report.MetricsEntry;
import com.meituan.msc.common.utils.Constants;
import com.meituan.msc.modules.apploader.events.AppLoadException;
import com.meituan.msc.modules.container.ContainerController;
import com.meituan.msc.modules.container.IContainerDelegate;
import com.meituan.msc.modules.container.IContainerManager;
import com.meituan.msc.modules.engine.MSCHornRollbackConfig;
import com.meituan.msc.modules.engine.MSCRuntime;
import com.meituan.msc.modules.page.IPageModule;
import com.meituan.msc.modules.page.PageModule;
import com.meituan.msc.modules.page.render.AppPageReporter;
import com.meituan.msc.modules.page.render.MSCHornPerfConfig;
import com.meituan.msc.modules.reporter.whitescreen.ErrorRecordInfo;
import com.meituan.msc.modules.update.MSCAppModule;

import org.json.JSONArray;
import org.json.JSONException;
import org.json.JSONObject;

import java.util.Iterator;
import java.util.Map;
import java.util.concurrent.ConcurrentHashMap;

/**
 * 上报维度中携带公共维度的Reporter
 * 一般一个模块一个Reporter
 * <p>
 * 方法命名规则：
 * - 如果是直接在当前模块的某个生命周期或者事件回调中，可以叫 onXxx
 * - 如果是模块外部埋点，可以叫 reportXxx
 * - 添加公共维度的两种方式：可以在 CommonTags 中添加；或者重写 once() 方法追加
 */
public class MSCCommonTagReporter extends MSCReporter {
    public static final String UNKNOWN_VALUE = "unknown";
    public static final String NETWORK_VALUE = "network";
    public static final String CACHE_VALUE = "cache";
    public static final String DEV_VALUE = "dev";
    protected static final String TAG_IS_FATAL = "isFatal";
    private static final String TAG_IS_USER_ERROR = "isUser"; // 区分业务异常 & 框架异常 https://km.sankuai.com/collabpage/1482702155
    private volatile ErrorRecordInfo.PageLoadErrorRecordInfo pageLoadErrorRecordInfo;
    private volatile ErrorRecordInfo.JsErrorRecordInfo jsErrorRecordInfo;
    private IRenderReporter renderReporter;
    private final Object removeMapLock = new Object();

    private static final long REPORT_JS_ERROR_GAP = 1000L;

    //JS上报来源
    public static final String REPORT_JS_FROM_KEY = "from";

    @NonNull
    protected CommonTags mscCommonTags;
    public MSCCommonTagReporter(CommonTags commonTags) {
        this.mscCommonTags = commonTags;
    }

    @Override
    public MetricsEntry buildEntry(String key, boolean repeatable) {
        // 如果这里耗时，可以考虑使用缓存池
        return super.buildEntry(key, repeatable)
                .tags(mscCommonTags.generateCommonTags());
    }


    public void reportJSError(final JSONObject error, final MSCRuntime runtime) {
        recordJSError(error);
        if (MSCHornRollbackConfig.enableJSErrorReportStrategy() && error != null && !error.optBoolean(TAG_IS_USER_ERROR, false) && runtime != null) {
            // 只对基础库的错误去重，用户上报的不去重
            MSCExecutors.submit(new Runnable() {
                @Override
                public void run() {
                    String errorMessage = error.optString("message");
                    long currentTime = System.currentTimeMillis();
                    ConcurrentHashMap<String, Long> recentErrors = runtime.getModule(MSCAppModule.class).getRecentError();
                    // 在设定的时间间隔内上报过
                    if (recentErrors.containsKey(errorMessage) && currentTime - recentErrors.get(errorMessage) < REPORT_JS_ERROR_GAP) {
                        reportError(error, ReporterFields.REPORT_JS_ERROR_INNER_COUNT, runtime);
                    } else {
                        recentErrors.put(errorMessage, currentTime);
                        reportError(error, ReporterFields.REPORT_JS_ERROR_COUNT, runtime);
                    }
                    removeOldErrors(currentTime, recentErrors);
                }
            });
        } else {
            reportError(error, ReporterFields.REPORT_JS_ERROR_COUNT, runtime);
        }
    }

    /**
     * @param currentTime 处理时刻
     * @param recentErrors 最近上报的错误
     * 删除距离处理时刻超过1s的上报错误，避免无效内存占用
     * 例如一个具有很长的message的error在出现一次之后再未出现，就会在小程序运行生命周期内一致占用这部分空间
     */
    private void removeOldErrors(long currentTime, ConcurrentHashMap<String, Long> recentErrors) {
         synchronized (removeMapLock) {
            Iterator<Map.Entry<String, Long>> iterator = recentErrors.entrySet().iterator();
            while (iterator.hasNext()) {
                Map.Entry<String, Long> entry = iterator.next();
                if ((currentTime - entry.getValue()) >= REPORT_JS_ERROR_GAP) {
                    iterator.remove();
                }
            }
        }
    }

    private void recordJSError(JSONObject error) {
        String errorMessage = "";
        if (error != null && error.has("message")) {
            errorMessage = error.optString("message");
        }
        jsErrorRecordInfo = new ErrorRecordInfo.JsErrorRecordInfo(errorMessage);
    }

    public ErrorRecordInfo.JsErrorRecordInfo getJsErrorRecordInfo() {
        return jsErrorRecordInfo;
    }

    public void recordPageLoadError(Exception error) {
        int errorCode = 0;
        if (error != null && error instanceof AppLoadException) {
            errorCode = ((AppLoadException) error).getErrorCode();
        }
        String errorMessage = error != null ? error.getMessage() : "";
        pageLoadErrorRecordInfo = new ErrorRecordInfo.PageLoadErrorRecordInfo(errorMessage, errorCode);
    }

    public ErrorRecordInfo.PageLoadErrorRecordInfo getPageLoadErrorRecordInfo() {
        return pageLoadErrorRecordInfo;
    }

    public void reportRenderError(JSONObject error, String errorKey, MSCRuntime runtime) {
        // 统一迁移到新的指标点上，做指标管理
        // 未设置错误码，则统一为：ERROR_CODE_UNKNOWN
        try {
            if (error != null && !error.has("errorCode")) {
                error.put("errorCode", -1000);
            }
        } catch (JSONException e) {
            MSCLog.e(TAG, e);
        }
        reportError(error, errorKey, runtime);
    }

    public void reportError(JSONObject error, String errorKey, MSCRuntime runtime) {
        if (error == null) {
            return;
        }
        boolean isFatal = error.optBoolean(TAG_IS_FATAL, true);
        boolean isUser = error.optBoolean(TAG_IS_USER_ERROR, false);
        try {
            error.put(TAG_IS_FATAL, String.valueOf(isFatal));
            error.put(TAG_IS_USER_ERROR, String.valueOf(isUser));
        } catch (JSONException ignored) {

        }

        // extraData.rawStack通过stack字段上报，不需要上报extraData到Perf
        String rawStack = getStack(error);

        MetricsEntry entry = record(errorKey);
        Iterator<String> iterator = error.keys();
        while (iterator.hasNext()) {
            String key = iterator.next();
            Object value = error.opt(key);
            // 将 JSON 和 Array 转换为字符串
            if (value instanceof JSONObject || value instanceof JSONArray) {
                value = value.toString();
            }

            // 前端异常
            if (TextUtils.equals(key, "extraData")) {
                continue;
            }

            entry.tag(key, value);
        }

        // 前端异常
        if (!TextUtils.isEmpty(rawStack)) {
            entry.tag("stack", rawStack);
        }

        IContainerDelegate containerDelegate = null;
        if (runtime != null && runtime.getContainerManagerModule() != null) {
            containerDelegate = runtime.getContainerManagerModule().getTopContainer();
        }
        appendOutLinkMetrics(entry, containerDelegate);
        entry.sendRealTime();
    }

    public void appendOutLinkMetrics(MetricsEntry entry, IContainerDelegate containerDelegate) {
        if (MSCHornRollbackConfig.readConfig().enableOutLinkParamsReport) {
            if (containerDelegate != null) {
                entry.tag(Constants.OUT_LINK_COLD_LAUNCH, containerDelegate.isOutLinkColdLaunch());
                if (containerDelegate.isOutLinkColdLaunch() && containerDelegate.getOutLinkMap() != null) {
                    entry.tag(Constants.OUT_LINK_LCH, containerDelegate.getOutLinkMap().get(Constants.OUT_LINK_LCH));
                    entry.tag(Constants.OUT_LINK_REFER, containerDelegate.getOutLinkMap().get(Constants.OUT_LINK_REFER));
                    entry.tag(Constants.DSP_TYPE, containerDelegate.getOutLinkMap().get(Constants.DSP_TYPE));
                }
            }
        }
    }

    /**
     * FP、FFP、白屏等指标上报时，添加horn字段配置，用于放量监控
     */
    public void appendHornField(MetricsEntry entry) {
        // 预创建webview场景提前发送onPageStart
        entry.tag("enablePreSendOnPageStart", MSCHornRollbackConfig.enablePreSendOnPageStart());
        // 修复业务预热场景重复创建webview
        entry.tag("enableFixMultiCreateWebView", MSCHornRollbackConfig.get().getConfig().prePageStartOptimizeWhiteList);
        // 包注入方式由inject切换为merge
        entry.tag("enableSwitchToMergeInject", MSCHornRollbackConfig.get().getConfig().enableLoadBasicPackagesByMergeAppList);
        // 旧版数据预拉取提前到路由时机
        entry.tag("instrumentPrefetchAppIds", MSCHornPerfConfig.getInstance().getConfig().instrumentPrefetchAppIds);
        // startPage提前
        entry.tag("startPageAdvancedWhiteList", MSCHornRollbackConfig.get().getConfig().startPageAdvancedWhiteList);
    }

    @Nullable
    private String getStack(JSONObject error) {
        JSONObject extraData = error.optJSONObject("extraData");
        String stack = null;
        if (extraData != null) {
            stack = extraData.optString("rawStack");
        }
        return stack;
    }

    protected static IPageModule getTopPage(MSCRuntime runtime) {
        if (runtime == null) {
            return null;
        }
        IContainerManager containerManager = runtime.getContainerManagerModule();
        if (containerManager == null) {
            return null;
        }
        return containerManager.getTopPage();
    }

    protected static AppPageReporter getTopAppPageReporter(MSCRuntime runtime) {
        IPageModule pageModule = getTopPage(runtime);
        if (pageModule instanceof PageModule) {
            return ((PageModule) pageModule).getReporter();
        }
        return null;
    }

    public CommonTags getMSCCommonTags() {
        return this.mscCommonTags;
    }

    public void setRenderReporter(IRenderReporter renderReporter) {
        this.renderReporter = renderReporter;
    }

    public IRenderReporter getRenderReporter() {
        return renderReporter;
    }
}
