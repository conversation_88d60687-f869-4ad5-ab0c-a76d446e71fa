package com.meituan.msc.modules.reporter;

/**
 * 容器加载异常错误码
 * https://km.sankuai.com/collabpage/1723214225
 *
 * Created by t<PERSON><PERSON> on 2023/06/19.
 */
public class MSCLoadErrorConstants {

    /**
     * JS引擎创建异常
     */
    public static final int ERROR_CREATE_JS_ENGINE_ERROR = 102000;
    /**
     * JS引擎初始化异常
     */
    public static final int ERROR_INIT_JS_ENGINE_ERROR = 102001;
    /**
     * DDD下载基础库接口返回失败
     */
    public static final int ERROR_DOWNLOAD_BASE_FAILED = 104001;
    /**
     * DDD下载基础库接口返回成功，但无数据
     */
    public static final int ERROR_DOWNLOAD_BASE_EMPTY_DATA = 104998;
    /**
     * DDD下载基础库失败，但无异常信息
     */
    public static final int ERROR_DOWNLOAD_BASE_FAILED_EXCEPTION_EMPTY = 104999;
    /**
     * 注入基础库失败，文件不存在
     */
    public static final int ERROR_INJECT_BASE_FILE_NOT_EXIST = 105000;
    /**
     * 注入基础库失败，文件损坏
     */
    public static final int ERROR_INJECT_BASE_FILE_INVALID = 105001;
    /**
     * 注入基础库失败，js预注入到webview渲染器
     */
    public static final int ERROR_INJECT_BASE_JS_WEBVIEW = 105002;
    /**
     * 注入基础库失败
     */
    public static final int ERROR_INJECT_BASE_FAILED = 105003;
    /**
     * 获取元信息接口返回失败，106001 + DDLoaderException.ErrorCode
     */
    public static final int ERROR_FETCH_METAINFO_FAILED = 106001;
    /**
     * 获取元信息返回失败，appId错误
     */
    public static final int ERROR_CODE_WRONG_APP_ID = 106002;
    /**
     * 获取缓存的元信息返回成功，但不满足最低版本号
     */
    public static final int ERROR_FETCH_METAINFO_NOT_MATCH_MIN_VERSION = 106997;
    /**
     * 获取元信息接口返回成功，但无数据
     */
    public static final int ERROR_FETCH_METAINFO_RESULT_EMPTY = 106998;
    /**
     * 获取元信息接口失败，但无异常信息
     */
    public static final int ERROR_FETCH_METAINFO_FAILED_EXCEPTION_EMPTY = 106999;
    /**
     * DDD加载业务主包，取消
     */
    public static final int ERROR_DOWNLOAD_MAIN_PACKAGE_CANCEL = 107000;
    /**
     * DDD加载业务主包返回失败
     */
    public static final int ERROR_DOWNLOAD_MAIN_PACKAGE_FAILED = 107001;
    /**
     * DDD加载业务主包返回成功，但无数据
     */
    public static final int ERROR_DOWNLOAD_MAIN_PACKAGE_EMPTY_DATA = 107998;
    /**
     * DDD加载业务主包异常，但无异常信息
     */
    public static final int ERROR_DOWNLOAD_MAIN_PACKAGE_EXCEPTION_EMPTY = 107999;
    /**
     * DDD加载业务子包返回失败
     */
    public static final int ERROR_DOWNLOAD_SUB_PACKAGE_FAILED = 108001;
    /**
     * DDD加载业务子包返回成功，但无数据
     */
    public static final int ERROR_DOWNLOAD_SUB_PACKAGE_EMPTY_DATA = 108998;
    /**
     * DDD加载业务子包异常，但无异常信息
     */
    public static final int ERROR_DOWNLOAD_SUB_PACKAGE_EXCEPTION_EMPTY = 108999;
    /**
     * 主包config.json文件解析结果异常
     */
    public static final int ERROR_PARSE_CONFIG_FILE_ERROR = 109000;
    /**
     * 注入主包失败，文件不存在
     */
    public static final int ERROR_INJECT_MAIN_PACKAGE_FILE_NOT_EXIST = 110000;
    /**
     * 注入主包失败，文件损坏
     */
    public static final int ERROR_INJECT_MAIN_PACKAGE_FILE_INVALID = 110001;
    /**
     * 注入子包失败，文件不存在
     */
    public static final int ERROR_INJECT_SUB_PACKAGE_FILE_NOT_EXIST = 110002;
    /**
     * 注入子包失败，文件损坏
     */
    public static final int ERROR_INJECT_SUB_PACKAGE_FILE_INVALID = 110003;
    /**
     * 注入主包失败
     */
    public static final int ERROR_INJECT_MAIN_PACKAGE_FAILED = 110004;
    /**
     * 注入子包失败
     */
    public static final int ERROR_INJECT_SUB_PACKAGE_FAILED = 110005;
    /**
     * combo注入模式下，未找到对应的包
     */
    public static final int ERROR_INJECT_PACKAGE_INFO_NOT_EXIST = 110999;
    /**
     * 启动页面，主包config.json文件解析结果检查结果异常
     */
    public static final int ERROR_START_PAGE_CHECK_CONFIG_RESULT_INVALID = 110000;
    /**
     * 长列表配置预解析，主包config.json文件解析结果检查结果异常
     */
    public static final int ERROR_PERF_LIST_CHECK_CONFIG_RESULT_INVALID = 111000;
    /**
     * 长列表配置预解析，长列表模版配置Json解析异常
     */
    public static final int ERROR_PERF_LIST_CONFIG_JSON_PARSE_EXCEPTION = 111001;
    /**
     * 长列表配置预解析，长列表属性读取异常
     */
    public static final int ERROR_PERF_LIST_READ_PROPERY_EXCEPTION = 111002;
    /**
     * 页面找不到异常
     */
    public static final int ERROR_PERF_PAGE_NOT_FOUND_EXCEPTION = 112000;
    /**
     * 出现 Fatal 级别的JS异常
     */
    public static final int ERROR_CODE_FATAL_JS_ERROR = 112001;
    /**
     * 基础库版本不匹配，需要升级宿主app
     */
    public static final int ERROR_CODE_NEED_UPGRADE_HOST_APP = 112002;
    /**
     * 启动过程中，内存不足
     */
    public static final int ERROR_CODE_OOM = 112003;
    /**
     * 任务不存在
     */
    public static final int ERROR_TASK_NOT_EXIST = 112004;
    /**
     * WebView初始化失败
     */
    public static final int ERROR_CODE_WEBVIEW_INIT_FAILED = 113000;
    /**
     * AppRoute发送失败（AppRoute参数为空）
     */
    public static final int ERROR_CODE_APP_ROUTE_FAILED = 114000;

    // 加载基础库超时
    public static final int ERROR_CODE_LOAD_BASE_TASK_TIMEOUT = 104099;

    // 加载主包超时
    public static final int ERROR_CODE_LOAD_MAIN_TASK_TIMEOUT = 107099;

    // 加载子包超时
    public static final int ERROR_CODE_LOAD_SUB_TASK_TIMEOUT = 108099;

    // 加载元信息超时
    public static final int ERROR_CODE_LOAD_META_INFO_TASK_TIMEOUT = 106099;

    // 加载其他任务超时
    public static final int ERROR_CODE_LOAD_OTHER_TASK_TIMEOUT = 109099;
}
