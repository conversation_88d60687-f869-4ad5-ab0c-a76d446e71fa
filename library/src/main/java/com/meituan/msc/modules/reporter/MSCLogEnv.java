package com.meituan.msc.modules.reporter;

import com.meituan.msc.common.config.MSCConfig;
import com.meituan.msc.common.process.MSCProcess;
import com.meituan.msc.extern.MSCEnvHelper;
import com.meituan.msc.modules.devtools.DebugHelper;

/**
 * Log需要的环境信息
 */
public class MSCLogEnv implements ILogEnv {
    private String processTag;

    @Override
    public boolean isDebug() {
        return DebugHelper.isDebug();
    }

    @Override
    public boolean enableMSCLogSync() {
        return MSCConfig.enableMSCLogSync();
    }

    @Override
    public String getProcessTag() {
        if (processTag != null) {
            return processTag;
        }
        if (!MSCEnvHelper.isInited()) {
            return "";
        }
        MSCProcess process = MSCProcess.getCurrentProcess();
        processTag = "[" + (process != null ? process.getShortName() : "?") + "]";
        return processTag;
    }
}
