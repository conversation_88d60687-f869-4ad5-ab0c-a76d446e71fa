package com.meituan.msc.modules.reporter;

import android.content.SharedPreferences;
import android.support.annotation.Keep;

import com.google.gson.Gson;
import com.google.gson.reflect.TypeToken;
import com.meituan.android.cipstorage.CIPSStrategy;
import com.meituan.met.mercury.load.bean.DDResourceCleanData;
import com.meituan.met.mercury.load.bean.DDResourceCleanStrategy;
import com.meituan.msc.extern.MSCEnvHelper;

import java.util.List;
import java.util.Map;
import java.util.concurrent.ConcurrentHashMap;

public final class MSCPackageNotHitCacheHelper {
    private static final String CLEAN_RESOURCES_SP_KEY = "clean_resources_infos";
    private static final int DIO_CLEAN_STRATEGY = 20; // 对应存储组件dio清理dioCleanStrategy
    private static final int PREDOWNLOAD_CACHE_CLEAN_STRATEGY = 21; // 对应存储组件cache清理cacheCleanStrategy，涉及msc预下载
    private static final Map<String, CleanCacheInfo> sCleanCacheInfoMap = new ConcurrentHashMap<>();
    private static final Gson gson = new Gson();
    private static volatile boolean isCacheLoaded = false;
    private static final Object cacheLock = new Object(); // 锁对象
    @Keep
    static class CleanCacheInfo {
        int cleanStrategy;
        String md5;
        long timestamp;

        public CleanCacheInfo(int cleanStrategy, String md5, long timestamp) {
            this.cleanStrategy = cleanStrategy;
            this.md5 = md5;
            this.timestamp = timestamp;
        }
    }

    private static void loadCleanCacheInfoFromSp() {
        if (!isCacheLoaded) {
            synchronized (cacheLock) {
                if (!isCacheLoaded) {
                    String jsonString = MSCEnvHelper.getDefaultSharedPreferences().getString(CLEAN_RESOURCES_SP_KEY, null);
                    if (jsonString != null) {
                        Map<String, CleanCacheInfo> loadedMap = gson.fromJson(jsonString, new TypeToken<Map<String, CleanCacheInfo>>(){}.getType());
                        sCleanCacheInfoMap.putAll(loadedMap);
                    }
                    isCacheLoaded = true; // 标记缓存已加载
                }
            }
        }
    }

    /**
     * 记录DD回调资源清理策略
     * @param delResources 清理的缓存列表
     */
    public static void recordCleanResourceStrategy(List<DDResourceCleanData> delResources) {
        // 首先将持久化存储的数据读取到内存
        loadCleanCacheInfoFromSp();
        SharedPreferences.Editor edit = MSCEnvHelper.getDefaultSharedPreferences().edit();
        for (DDResourceCleanData ddResourceCleanData : delResources) {
            if (ddResourceCleanData == null) {
                continue;
            }
            CleanCacheInfo cleanCacheInfo = new CleanCacheInfo(ddResourceCleanData.getCleanStrategy(), ddResourceCleanData.getResourceVersion(), ddResourceCleanData.getDelTimestamp());
            sCleanCacheInfoMap.put(ddResourceCleanData.getResourceName(), cleanCacheInfo);
        }
        edit.putString(CLEAN_RESOURCES_SP_KEY, gson.toJson(sCleanCacheInfoMap));
        edit.apply();
    }

    /**
     * 记录存储组件清理策略
     * @param cleanStrategy 清理策略
     * @param removedFiles 清理的文件列表
     */
    public static void recordCleanResourceStrategy(String cleanStrategy, List<CIPSStrategy.SingleFileCleanData> removedFiles) {
        // 首先将持久化存储的数据读取到内存
        loadCleanCacheInfoFromSp();
        SharedPreferences.Editor edit = MSCEnvHelper.getDefaultSharedPreferences().edit();
        for (CIPSStrategy.SingleFileCleanData singleFileCleanData : removedFiles) {
            if (singleFileCleanData == null) {
                continue;
            }
            if ("dioCleanStrategy".equals(cleanStrategy)) {
                CleanCacheInfo cleanCacheInfo = new CleanCacheInfo(DIO_CLEAN_STRATEGY, singleFileCleanData.fileVersion, System.currentTimeMillis());
                String fileName = singleFileCleanData.fileName;
                // fileName格式为：/data/user/0/com.sankuai.meituan/files/cips/common/ddload/assets/msc/d783efcbad0f4cd9.delete
                if (fileName != null) {
                    int lastIndex = fileName.lastIndexOf('/');
                    if (lastIndex != -1 && lastIndex < fileName.length() - 1) {
                        String fileNameOnly = fileName.substring(lastIndex + 1);
                        int dotIndex = fileNameOnly.indexOf('.');
                        if (dotIndex != -1) {
                            String bundleName = fileNameOnly.substring(0, dotIndex);
                            sCleanCacheInfoMap.put(bundleName, cleanCacheInfo);
                        }
                    }
                }
            } else if ("cacheCleanStrategy".equals(cleanStrategy)) {
                CleanCacheInfo cleanCacheInfo = new CleanCacheInfo(PREDOWNLOAD_CACHE_CLEAN_STRATEGY, singleFileCleanData.fileVersion, System.currentTimeMillis());
                String fileName = singleFileCleanData.fileName;
                // fileName格式为：...../preload/br/msc/${bundle_name}/${bundle_version}/69759_0ba6ba5f9be69628c8a41e5f55a0c68d.br"
                if (fileName != null) {
                    int mscIndex = fileName.indexOf("msc/");
                    if (mscIndex != -1) {
                        int startIndex = mscIndex + "msc/".length();
                        int bundleNameEndIndex = fileName.indexOf('/', startIndex);
                        if (bundleNameEndIndex != -1) {
                            String bundleName = fileName.substring(startIndex, bundleNameEndIndex);
                            int bundleVersionIndex = bundleNameEndIndex + 1;
                            int bundleVersionEndIndex = fileName.indexOf('/', bundleVersionIndex);
                            String bundleVersion = "unknown";
                            if (bundleVersionEndIndex != -1) {
                                bundleVersion = fileName.substring(bundleVersionIndex, bundleVersionEndIndex);
                            }
                            cleanCacheInfo.md5 = bundleVersion;
                            sCleanCacheInfoMap.put(bundleName, cleanCacheInfo);
                        }
                    }
                }
            } else {
                MSCLog.w("MSCPackageNotHitCacheHelper", "unknown cleanStrategy of storage: " + cleanStrategy);
            }
        }
        edit.putString(CLEAN_RESOURCES_SP_KEY, gson.toJson(sCleanCacheInfoMap));
        edit.apply();
    }

    /**
     * 获取资源清理原因
     * @param resourceName 资源名称
     * @return
     */
    public static String getCleanResourceReason(String resourceName) {
        // todo 需要确认指标上报是否会在主线程运行
        // 确保已读取存储到内存，之后可直接读取cleanCacheInfoMap
        loadCleanCacheInfoFromSp();
        CleanCacheInfo cleanCacheInfo = sCleanCacheInfoMap.get(resourceName);
        if (cleanCacheInfo != null) {
            switch (cleanCacheInfo.cleanStrategy) {
                case DIO_CLEAN_STRATEGY: // 存储组件直接DIO清理，存储超阈值
                    return "storageLRUClean";
                case PREDOWNLOAD_CACHE_CLEAN_STRATEGY: // 存储组件预下载缓存清理
                    return "storageCleanPreloadCache";
                case DDResourceCleanStrategy.APP_START_CLEAN_STRATEGY: // DD冷启动时清理
                    return "ddColdStartClean";
                case DDResourceCleanStrategy.LRU_MAX_SIZE_CLEAN_STRATEGY: // DD内LRU存储超阈值清理
                    return "ddLRUMaxSizeClean";
                case DDResourceCleanStrategy.LRU_DURATION_CLEAN_STRATEGY: // DD内LRU长时间未使用清理
                    return "ddLRUDurationClean";
                case DDResourceCleanStrategy.BUSINESS_CLEAN_ALL_STRATEGY: // DD内，业务主动调用接口清理白名单外所有缓存
                    return "ddBizCleanAllCache";
                case DDResourceCleanStrategy.BUSINESS_CLEAN_SPECIFIC_STRATEGY: // DD内，业务主动调用接口清理指定缓存
                    return "ddBizCleanSpecificCache";
                case DDResourceCleanStrategy.VERSION_MULTI_CLEAN_STRATEGY: // DD多版本清理
                    return "ddMultiVersionClean";
                case DDResourceCleanStrategy.EXPIRE_CLEAN_STRATEGY: // DD内资源过期清理
                    return "ddExpireClean";
                case DDResourceCleanStrategy.ZOMBIE_FILE_CLEAN_STRATEGY: // DD内僵尸文件清理（有文件但没有索引）
                    return "ddZombieFileClean";
                default:
                    return null;
            }
        }
        return null;
    }
}
