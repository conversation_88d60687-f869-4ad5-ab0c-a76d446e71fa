package com.meituan.msc.modules.reporter;

import android.os.Build;
import android.support.annotation.IntDef;

import com.meituan.android.common.babel.Babel;
import com.meituan.android.common.kitefly.Log;
import com.meituan.msc.common.report.BaseMetricsReporter;
import com.meituan.msc.common.report.MetricsEntry;

import java.lang.annotation.Retention;
import java.lang.annotation.RetentionPolicy;
import java.util.ArrayList;
import java.util.List;

/**
 * 底层使用 Babel 上报埋点数据的 Reporter，上报维度中不带公共维度
 */
public class MSCReporter extends BaseMetricsReporter {
    protected static final String TAG = "MSCReporter";
    // FIXME by chendacai: 3/4/22 等Perf准备好后，将通道改为 p5
    private static final String CHANNEL = "prism-report-mmp";

    private static Log buildBabelLog(MetricsEntry entry) {
        // FIXME: 2022/7/18 让子类主动调用该方法不太好，后面优化下
        entry.performLazyActions();
        Log.Builder builder = new Log.Builder(null);
        builder.tag(entry.getKey());
        builder.value(entry.getValue());
        builder.optional(entry.getTags());
        builder.reportChannel(CHANNEL);
//        builder.raw(entry.getExtra());
        builder.lv4LocalStatus(true);
        return builder.build();
    }

    // 只有高于m的安卓版本才可以发实时日志
    private boolean canLogRT() {
        return Build.VERSION.SDK_INT > Build.VERSION_CODES.M;
    }

    @Override
    protected void realSendEntry(MetricsEntry entry, boolean realTime) {
        Log log = buildBabelLog(entry);
        if (realTime && canLogRT()) {
            Babel.logRT(log);
        } else {
            Babel.log(log);
        }
        printMetricsEntry(entry);
    }

    @Override
    protected void realSendBatchEntry(List<MetricsEntry> entries, boolean realTime) {
        List<Log> logs = new ArrayList<>();
        for (MetricsEntry entry : entries) {
            logs.add(buildBabelLog(entry));
            printMetricsEntry(entry);
        }
        if (realTime && canLogRT()) {
            Babel.logRT(logs);
        } else {
            for (Log log : logs) {
                Babel.log(log);
            }
        }
    }

    private void printMetricsEntry(MetricsEntry entry) {
        MSCLog.i(TAG, entry);
    }

    @IntDef({ReportValue.SUCCESS, ReportValue.FAILED})
    @Retention(RetentionPolicy.SOURCE)
    public @interface ReportValue {
        int SUCCESS = 1;
        int FAILED = 0;
    }
}
