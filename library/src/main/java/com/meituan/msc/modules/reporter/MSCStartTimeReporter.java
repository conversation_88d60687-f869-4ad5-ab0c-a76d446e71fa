package com.meituan.msc.modules.reporter;

import static com.meituan.msc.modules.reporter.MSCCommonTagReporter.DEV_VALUE;

import android.app.Activity;

import com.meituan.msc.common.utils.TimeUtil;
import com.meituan.msc.extern.MSCEnvHelper;
import com.meituan.msc.lib.interfaces.IMSCNavigationReporter;
import com.meituan.msc.lib.interfaces.StartTimeParam;
import com.meituan.msc.modules.container.IContainerDelegate;
import com.meituan.msc.modules.engine.MSCHornRollbackConfig;
import com.meituan.msc.modules.engine.MSCRuntime;
import com.meituan.msc.modules.page.render.MSCHornPerfConfig;
import com.meituan.msc.modules.page.render.RendererType;
import com.meituan.msc.modules.page.view.PageViewWrapper;
import com.meituan.msc.modules.update.MSCAppModule;
import com.sankuai.meituan.serviceloader.ServiceLoader;

import java.util.List;

public class MSCStartTimeReporter {

    private static IMSCNavigationReporter mMscNavigationReporter;

    //已经加载过IMSCNavigationReporter
    private static boolean loaded;

    public static IMSCNavigationReporter getMscNavigationReporter() {
        if (mMscNavigationReporter == null && !loaded) {
            List<IMSCNavigationReporter> reporters = ServiceLoader.load(IMSCNavigationReporter.class,
                    IMSCNavigationReporter.MSC_NAVIGATION_START_TIME_REPORTER);
            if (reporters != null && reporters.size() > 0) {
                mMscNavigationReporter = reporters.get(0);
            }
            loaded = true;
        }
        return mMscNavigationReporter;
    }

    public static void report(String path, String type, MSCRuntime runtime, IContainerDelegate container, Activity activity, PageViewWrapper pageViewWrapper,
                              long routeTime) {
        if (mMscNavigationReporter == null) {
            getMscNavigationReporter();
        }
        if (mMscNavigationReporter != null) {
            String appId = runtime.getAppId();
            MSCAppModule mscAppModule = runtime.getMSCAppModule();
            String appName = mscAppModule.getAppName();
            String baseSdkVersion = mscAppModule.getBasePkgVersion();
            int containerId = container.getContainerId();
            RendererType renderType = mscAppModule.getRendererTypeForPage(path);

            if (!MSCEnvHelper.getEnvInfo().isProdEnv()) {
                // 非线上环境，处理下线下的时间戳参数，避免污染线上指标
                if (TimeUtil.isUnixTime(baseSdkVersion)) {
                    baseSdkVersion = DEV_VALUE;
                }
                if (appId != null && appId.startsWith("app_")) {
                    appId = DEV_VALUE;
                }
            }

            StartTimeParam.Builder builder = new StartTimeParam.Builder(path, appId, appName)
                    .setActivity(activity)
                    .setMscVersion(baseSdkVersion)
                    .setContainerId(containerId)
                    .setType(type)
                    .setRenderType(renderType.toString())
                    .setWidget(container.isWidget())
                    .setPageViewWrapper(pageViewWrapper);
            if (MSCHornRollbackConfig.readConfig().enableFFPReporterAppendRouteTimeFix) {
                builder.setStartFSPMonitorTime(routeTime);
            }
            if (MSCHornPerfConfig.getInstance().enableFPMatchFix() && container.isWidget()) {
                builder.setFfpWidgetId(String.valueOf(pageViewWrapper.getRenderer().getViewId()));
            }
            StartTimeParam param = builder.build();
            mMscNavigationReporter.navigationStartTime(param);
            MSCLog.i("MSCStartTimeReporter", param.toString());
        }
    }
}
