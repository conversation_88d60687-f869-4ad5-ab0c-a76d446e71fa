package com.meituan.msc.modules.reporter;

import android.text.TextUtils;

import com.meituan.msc.common.utils.PathUtil;

import java.util.Objects;

/**
 * <AUTHOR>
 * @since 2021/11/17.
 */

public class PageRecord {

    public static final String ID_NATIVE = "native";


    private String path;
    private String id;

    PageRecord(String id, String path) {
        this.id = id;
        this.path = PathUtil.getPath(path);
        if (TextUtils.isEmpty(this.path)){
            this.path =  "path_not_found";
        }
    }

    @Override
    public String toString() {
        return "id=" + id + ",path=" + path + "\\n";
    }

    @Override
    public boolean equals(Object o) {
        if (this == o) return true;
        if (o == null || getClass() != o.getClass()) return false;
        PageRecord record = (PageRecord) o;
        return Objects.equals(id, record.id) &&
                Objects.equals(path, record.path);
    }

    @Override
    public int hashCode() {
        return Objects.hash(path, id);
    }

    public String getPath() {
        return path;
    }

    public String getId() {
        return id;
    }
}
