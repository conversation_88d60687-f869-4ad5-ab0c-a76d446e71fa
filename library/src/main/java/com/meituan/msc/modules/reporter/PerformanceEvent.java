package com.meituan.msc.modules.reporter;

import java.util.ArrayList;
import java.util.Collection;
import java.util.Collections;
import java.util.Comparator;
import java.util.List;

public abstract class PerformanceEvent {

    public String name;
    public long time;   //事件发生时间，在duration中表示endTime

    public PerformanceEvent(String name, long time) {
        this.name = name;
        this.time = time;
    }

    public long getStartTime() {
        return time;
    }

    /**
     * 用于上报的时间，point报（距离启动的）发生时间，duration报时长
     */
    public long getReportTime(long launchStartTime) {
        return time - launchStartTime;
    }

    public String toString(long launchStartTime) {
        long diff = time - launchStartTime;
        return name + ": " + (diff > 0 ? "+" : "") + diff + "ms";
    }

    public static class Point extends PerformanceEvent {

        public Point(String name, long time) {
            super(name, time);
        }

        public Point(String name) {
            this(name, System.currentTimeMillis());
        }
    }

    public static class Duration extends PerformanceEvent {
        public long duration;

        public Duration(String name, long endTime, long duration) {
            super(name, endTime);
            this.duration = duration;
        }

        public Duration(String name, long duration) {
            this(name, System.currentTimeMillis(), duration);
        }

        @Override
        public long getStartTime() {
            return time - duration;
        }

        @Override
        public long getReportTime(long launchStartTime) {
            return duration;
        }

        @Override
        public String toString(long launchStartTime) {
            return super.toString(launchStartTime) + ", duration " + duration + "ms";
        }

        /**
         * 文件加载，内容形式与duration类似，前端展示时需要区分
         */
        public static class Load extends Duration {

            public Load(String filePath, long endTime, long duration) {
                super(filePath, endTime, duration);
            }

            public Load(String filePath, long duration) {
                super(filePath, duration);
            }
        }
    }

    /**
     * 以结束时间排序，合并两个事件流
     */
    public static List<PerformanceEvent> merge(Collection<PerformanceEvent> a, Collection<PerformanceEvent> b) {
        List<PerformanceEvent> result = new ArrayList<>(a.size() + b.size());
        result.addAll(a);
        result.addAll(b);
        Collections.sort(result, new Comparator<PerformanceEvent>() {
            @Override
            public int compare(PerformanceEvent o1, PerformanceEvent o2) {
                return Long.compare(o1.time, o2.time);
            }
        });
        return result;
    }
}
