package com.meituan.msc.modules.reporter;

/**
 * Created by letty on 2019/8/29.
 **/
public class ReporterFields {
    public static final String REPORT_LAUNCH_DURATION_WEB_VIEW_INIT = "msc.launch.duration.webview.init";//首次创建WebView耗时

    public static final String REPORT_PAGE_DURATION_PAGE_START_FIRST_RENDER = "msc.page.duration.page.start.first.render";//事件驱动阶段业务执行,onPageStart 事件,custom_event_H5_FIRST_RENDER 事件,page.path，当前页面路径,
    public static final String REPORT_PAGE_LOAD_POINT_FIRST_RENDER = "msc.page.load.point.first.render";//page加载时间,Page init，FirstRender,page.path，当前页面路径,

    //    基于起始时间点的埋点
    public static final String REPORT_LAUNCH_POINT_FULL_FIRST_RENDER = "msc.launch.point.full.first.render";//一致,启动总耗时,启动小程序,custom_event_H5_FIRST_RENDER 事件,mode、pkgSource,

    public static final String REPORT_LAUNCH_POINT_FAILED = "msc.launch.point.failed";//启动到失败的耗时

    public static final String REPORT_ACTIVITY_RECREATE_RATE = "msc.activity.recreate.rate";    //页面重新创建埋点
    public static final String REPORT_START_ACTIVITY_REUSE = "msc.activity.start.reuse";    //页面复用埋点
    public static final String REPORT_UNCAUGHT_ERROR = "msc.uncaught.js.error";    //客户端捕获异常

    // https://km.sankuai.com/collabpage/1716055429
    public static final String MSC_WEBVIEW_COMPONENT_URL_PORTAL = "msc.webview.component.url.portal";

    // https://km.sankuai.com/collabpage/1723214225
    public static final String REPORT_MSC_LOAD_ERROR_COUNT = "msc.load.error.count";

    public static final String REPORT_MSC_TASK_TIMEOUT_COUNT = "msc.load.task.timeout";

    //请求预拉取
    /**
     * 预拉取成功率（成功/失败/取消）
     */
    public static final String REPORT_LAUNCH_POINT_REQUEST_PREFETCH_RATE = "msc.launch.point.request.prefetch.rate";
    /**
     * 数据预拉取耗时，包含准备耗时、网络请求耗时、getBackgroundFetchData/onBackgroundFetchData 的耗时
     */
    public static final String REPORT_DURATION_REQUEST_PREFETCH_TOTAL = "msc.duration.request.prefetch.total";
    /**
     * 数据预拉取请求耗时，不包含msi和msc的数据包装耗时
     */
    public static final String REPORT_DURATION_REQUEST_PREFETCH_REQUEST = "msc.duration.request.prefetch.request";
    /**
     * 数据预拉取准备耗时，包含定位和获取业务通用参数
     */
    public static final String REPORT_DURATION_REQUEST_PREFETCH_PREPARE = "msc.duration.request.prefetch.prepare";

    //页面加载
    public static final String REPORT_PAGE_PAGE_VIEW_COUNT = "msc.page.view.count";//pv
    public static final String REPORT_PAGE_CREATE_COUNT = "msc.page.create.count";
    public static final String REPORT_PAGE_LOAD_SUCCESS_RATE = "msc.page.load.success.rate";
    public static final String REPORT_PAGE_EXIT_SUCCESS_RATE = "msc.page.exit.success.rate";
    public static final String REPORT_PAGE_TASK_NOT_FINISH = "msc.page.task.not.finish";
    public static final String REPORT_PAGE_LOAD_DURATION = "msc.page.create.to.load.duration";
    public static final String REPORT_PAGE_NOT_FOUND_ROUTE_COUNT = "msc.page.notfound.route.count"; // 页面找不到
    public static final String REPORT_PAGE_MEMORY_USAGE = "msc.page.memory"; // 页面内存占用

    public static final String REPORT_LAUNCH_TO_APP_ROUTE_COUNT = "msc.launch.to.app.route.count";

    // 路由成功率 https://km.sankuai.com/page/1356814043
    public static final String REPORT_PAGE_ROUTE_START_COUNT = "msc.page.route.start.count";
    public static final String REPORT_PAGE_ROUTE_END_COUNT = "msc.page.route.end.count";
    public static final String REPORT_PAGE_ROUTE_EXCEPTION_COUNT = "msc.page.route.exception.count";
    public static final String REPORT_PAGE_ROUTE_OPEN_PARAM_ERROR_COUNT = "msc.page.route.open.param.error.count";

    public static final String REPORT_COUNT_CLEAR_RENDER_CACHE = "msc.count.clear.render.cache";

    public static final String REPORT_WEBVIEW_PRELOAD_START_COUNT = "msc.webview.preload.start.count";
    public static final String REPORT_WEBVIEW_BACKGROUND_INIT_DURATION = "msc.webview.backgroundinit.duration";
    public static final String REPORT_WEBVIEW_PRECREATE_DURATION = "msc.webview.precreate.duration";
    public static final String REPORT_WEBVIEW_PRELOAD_BEFORE_INIT = "msc.webview.preload.before.init";
    public static final String REPORT_WEBVIEW_PRELOAD_IDLE_DURATION = "msc.webview.preload.idle.duration";
    public static final String REPORT_CANCEL_WEBVIEW_PRECREATE_DELAY_CONFIG = "msc.cancel.webview.precreate.delay.config";
    public static final String REPORT_CANCEL_WEBVIEW_PRECREATE_THRESHOLD_CONFIG = "msc.cancel.webview.precreate.threshold.config";

    //用户视角监控指标 https://km.sankuai.com/collabpage/1975250891
    //需求文档 https://km.sankuai.com/collabpage/1952020429
    public static final String REPORT_USER_LAUNCH_START = "msc.user.launch.start";
    public static final String REPORT_USER_FOUNDATION_LOAD_SUCCESS = "msc.user.foundation.load.success";
    public static final String REPORT_USER_PAGE_START = "msc.user.page.start";
    public static final String REPORT_USER_PAGE_LOAD_SUCCESS = "msc.user.page.load.success";
    public static final String REPORT_USER_PAGE_FFP = "msc.user.page.ffp";

    // App加载
    public static final String REPORT_APP_LOAD_FAIL = "msc.app.load.fail";

    //包模块 https://km.sankuai.com/page/1287876075
    public static final String REPORT_MSC_METAINFO_LOAD_SUCCESS_RATE = "msc.metainfo.load.success.rate"; //元信息获取成功率
    // 元信息加载耗时
    public static final String REPORT_MSC_METAINFO_LOAD_DURATION = "msc.metainfo.load.duration";
    public static final String REPORT_MSC_PACKAGE_LOAD_SUCCESS_RATE = "msc.package.load.success.rate";  //包获取成功率
    // 包加载耗时
    public static final String REPORT_MSC_PACKAGE_LOAD_DURATION = "msc.package.load.duration";
    public static final String REPORT_MSC_PACKAGE_LOAD_FAIL_COUNT = "msc.package.load.fail.count";  //包获取失败数目,避免包获取成功率采样后丢失业务量小的失败页面数据
    public static final String REPORT_MSC_PACKAGE_INJECT_SUCCESS_RATE = "msc.package.inject.success.rate";  //包注入成功率
    public static final String REPORT_MSC_PACKAGE_INJECT_FAIL_COUNT = "msc.package.inject.fail.count";  //包注入失败数目,避免包注入成功率采样后丢失业务量小的失败页面数据
    public static final String REPORT_MSC_BASE_PACKAGE_VERSION_ERROR_COUNT = "msc.base.package.version.error.count"; //用户指定的基础库版本数据
    public static final String REPORT_MSC_BASE_PACKAGE_VERSION_ILLEGAL_COUNT = "msc.base.package.version.illegal.count"; //获取到的基础库版本在强制重拉名单中
    public static final String REPORT_MSC_JS_RESOURCE_NOT_EXIST_COUNT = "msc.js.resource.not.exist.count";  //js资源文件不存在数据上报

    public static final String REPORT_MSC_PACKAGE_INVALID_COUNT = "msc.package.invalid";  //检测包资源不可用
    public static final String REPORT_MSC_LAUNCH_COMPLETABLEFUTURE_NPE = "msc.launch.completablefuture.npe";  //检测包资源不可用
    public static final String REPORT_MSC_PACKAGE_PRE_CHECK_INVALID_COUNT = "msc.package.pre.check.invalid";
    public static final String REPORT_MSC_PACKAGE_PRE_CHECK_FILE_NOT_EXIST_COUNT = "msc.package.pre.check.file.not.exist";
    public static final String REPORT_MSC_PACKAGE_INVALID_USING_RUNTIME_COUNT = "msc.package.invalid.using.runtime.count";  //检测包资源不可用时，启动过并挂载页面的个数
    public static final String REPORT_MSC_RESOURCE_CHECK_RESULT_COUNT = "msc.resource.check.result.count";

    // 最低版本号内置文件读取异常
    public static final String REPORT_MSC_LOAD_MINVERSION_ERROR_COUNT = "msc.load.minversion.error.count";

    public static final String REPORT_MSC_PACKAGE_MD5_CHECK_DURATION = "msc.package.md5.check.duration";
    public static final String REPORT_MSC_BASE_PACKAGE_RELOAD_CONFIG_FETCH_DURATION = "msc.package.base.reload.config.fetch.duration";

    public static final String REPORT_MSC_LAUNCH_NEW_INTENT_ERROR = "msc.launch.new.intent.error";  //onNewIntent error

    //preload 运行时预加载 https://km.sankuai.com/page/1440213974
    public static final String REPORT_MSC_RUNTIME_PRELOAD_COUNT = "msc.runtime.preload.start.count";
    public static final String REPORT_MSC_RUNTIME_PRELOAD_SUCCESS_RATE = "msc.runtime.preload.success.rate";
    public static final String REPORT_MSC_RUNTIME_PRELOAD_DURATION = "msc.runtime.preload.duration";

    // 启动加载过程指标
    public static final String REPORT_MSC_LAUNCH_PERFORMANCE_GC_TIME = "msc.launch.performance.gc.time";
    public static final String REPORT_MSC_LAUNCH_PERFORMANCE_CPU_USAGE = "msc.launch.cpu.usage.rate";
    public static final String REPORT_MSC_BASE_PRELOAD_USAGE_RATE = "msc.base.preload.usage.rate";
    public static final String REPORT_MSC_BIZ_PRELOAD_USAGE_RATE = "msc.biz.preload.usage.rate";

    public static final String MSC_RUNTIME_LEAK = "msc.runtime.memory.leak.count";
    public static final String REPORT_MSC_DESTROY_RUNTIME_COUNT = "msc.runtime.destroy.count";
    public static final String REPORT_MSC_WEBVIEW_MESSAGEPORT_LEAK_COUNT = "msc.webview.messageport.leak.count";


    //stability
    public static final String REPORT_STABILITY_MEMORY_WARNING = "msc.stability.count.memory.warning";
    public static final String REPORT_STABILITY_MEMORY_WARNING_V2 = "msc.stability.count.memory.warning.V2";
    public static final String REPORT_JS_ERROR_COUNT = "msc.js.error.count";
    // 替换 msc.render.error.count 埋点，做统一指标管理
    public static final String REPORT_RENDER_NATIVE_ERROR_COUNT = "msc.render.native.uicmd.error.count";
    public static final String REPORT_RENDER_NATIVE_MESSAGE_COUNT = "msc.render.native.uicmd.message.count";
    public static final String REPORT_TEMP_FILE_CLEAN_COUNT = "msc.temp.file.clean.count";

    //白屏检测耗时 https://km.sankuai.com/page/1319064946
    public static final String REPORT_MSC_PAGE_WHITE_SCREEN_COUNT = "msc.page.white.screen.count";
    public static final String REPORT_MSC_PAGE_WHITE_SCREEN_CANCEL_COUNT = "msc.page.white.screen.cancel.count";
    // 新增白屏检测耗时新指标，修复startPage提前导致的白屏率上涨 https://km.sankuai.com/collabpage/2706150966
    public static final String REPORT_MSC_PAGE_WHITE_SCREEN_COUNT_NEW = "msc.page.white.screen.count.new";
    public static final String REPORT_MSC_PAGE_WHITE_SCREEN_CANCEL_COUNT_NEW = "msc.page.white.screen.cancel.count.new";
    // 白屏异常感知 https://km.sankuai.com/collabpage/2111790230
    public static final String REPORT_PAGE_EXCEPTION_RECORD_COUNT = "msc.page.exception.record.count";

    // API调用埋点上报 https://km.sankuai.com/collabpage/2704593151
    public static final String REPORT_API_METHOD_COUNT = "msc.api.method.count";

    public static final String NETWORK = "network";
    public static final String WEB_VIEW_TYPE = "webViewType";
    public static final String APP_ID = "msc.id";
    public static final String WIDGET = "widget";
    public static final String PAGE_STAY_TIME = "pageStayTime";

    // renderProcessGone相关埋点
    public static final String REPORT_RENDER_PROCESS_GONE = "msc.render.process.gone"; // 发生render process gone次数
    public static final String REPORT_RENDER_PROCESS_GONE_HANDLED = "msc.render.process.gone.handled"; // 对render process gone回调进行了接收并处理的次数
    public static final String REPORT_RENDER_PROCESS_GONE_RELOAD_PAGE = "msc.render.process.gone.reload.page"; // render process gone之后reload页面

    //启动阶段拦截二次页面启动上报
    public static final String MSC_LAUNCH_MULTI_SKIP = "msc.launch.multi.skip";

    public static final String FP_STAGES = "msc.page.create.to.first.render.stages";
    public static final String FFP_STAGES = "msc.ffp.stages";

    public static final String CODE_CACHE_CREATE = "msc.codecache.create";
    public static final String LAUNCH_START = "launchStartTime";
    public static final String EXIT_TIME = "exitTime";

    public static final String LIFECYCLE_COST_TIME = "msc.observer.lifecycle.cost.time";

    public static final String MSC_EXE_POOL_ERROR_RATE = "msc.exe.pool.error.rate";
    public static final String MSC_BIKE_FIRST_TIME = "msc.bikeFirstTime";
    public static final String MSC_WEBVIEW_PART2 = "msc.bikeWebView.part2.time";
    public static final String MSC_WEBVIEW_PART3 = "msc.bikeWebView.part3.time";
    public static final String MSC_WEBVIEW_PART_GET_DEFAULT_USER_AGENT = "msc.bikeWebView.part.getDefaultUserAgent.time";

    //msc page 级配置的生效率统计
    public static final String MSC_PAGE_CONFIG_ERROR_RATE = "msc.page.config.error.rate";
    public static final String MSC_PAGE_CONFIG_UPDATE_COUNT = "msc.page.config.update.count";
    public static final String MSC_FE_PAGE_FMP = "msc.fe.duration.app.launch.fmp";
    public static final String MSC_FE_PAGE_FST = "msc.fe.page.fst";
    public static final String REPORT_WEBVIEW_CREATE_AFTER_DESTROY_COUNT = "msc.webview.create.after.destroy.count";

    //兜底页指标
    public static final String MSC_OFFLINE_MMP_LAUNCH_COUNT = "msc.offline.mmp.launch.count";

    // 路由映射失败埋点
    public static final String REPORT_ROUTE_MAPPING_FAIL = "msc.route.mapping.route.fail";

    // 重复JSError上报指标
    public static final String REPORT_JS_ERROR_INNER_COUNT = "msc.js.error.inner.count";

    // 容器停留时长 https://km.sankuai.com/collabpage/2518723690
    public static final String REPORT_MSC_CONTAINER_STAY_DURATION = "msc.container.stay.duration";

    //数据预拉取
    public static final String REPORT_DYNAMIC_PARSE_PREFETCH_SUCCESS_RATE = "msc.dynamic.parse.prefetch.success.rate";
    public static final String REPORT_DYNAMIC_PARSE_PREFETCH_DURATION = "msc.dynamic.parse.prefetch.duration";
    public static final String REPORT_DYNAMIC_PARSE_CONFIG_SUCCESS_RATE = "msc.dynamic.parse.config.success.rate";

    // 保活
    public static final String REPORT_KEEP_ALIVE_COUNT = "msc.app.enter.keepAlive.count";
    // ffpEnd事件从发送到接收的耗时
    public static final String REPORT_FFPEND_EVENT_DURATION = "msc.ffpend.event.duration";
    // 秒开终点到MSCAttachDataCallback回调触发的间隔
    public static final String REPORT_FFP_END_TO_ATTACH_CALLBACK_DURATION = "msc.ffp.end.to.attach.callback.duration";
    // MSCAttachDataCallback回调触发到MSCTimedAttachDataCallback回调触发的间隔
    public static final String REPORT_ATTACH_TO_TIME_ATTACH_CALLBACK_DURATION = "msc.attach.to.time.attach.callback.duration";
    // 秒开阶段API调用数据
    public static final String REPORT_PAGE_FFP_API_DETAILS = "msc.page.ffp.api.details";

    // 分阶段一级指标（容器启动时长）
    public static final String REPORT_PAGE_CONTAINER_LAUNCH_DURATION = "msc.page.container.launch.duration";
    // 分阶段一级指标（业务启动时长）
    public static final String REPORT_PAGE_BIZ_LAUNCH_DURATION = "msc.page.biz.launch.duration";
    // 分阶段一级指标（页面渲染时长）
    public static final String REPORT_PAGE_RENDER_DURATION = "msc.page.render.duration";
    // 首个 ABTest，分布指标
    public static final String REPORT_FIRST_ABTEST = "msc.first.abtest";
}
