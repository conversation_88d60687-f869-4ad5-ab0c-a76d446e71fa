package com.meituan.msc.modules.reporter;

import android.app.Activity;

import com.meituan.android.common.statistics.Statistics;
import com.meituan.android.common.statistics.utils.AppUtil;

import java.util.Map;

public class StatisticsReporter {

    private static final String TAG = "StatisticsReporter";
    // TODO: 2024/10/11 tianbin 暂时优选都上报到美团通道，后续有其他业务使用时再进行适配
    public static final String CATEGORY = "group";
    public static final String VAL_CID = "c_group_lrimotms";

    public static void reportSC(Activity activity, String val_bid, Map<String, Object> val_lab) {
        MSCLog.i(TAG, "reportSC", activity != null ? activity.toString() : null, val_bid, val_lab);
        Statistics.getChannel(CATEGORY).writeSystemCheck(AppUtil.generatePageInfoKey(activity), val_bid, val_lab, VAL_CID);
    }
}
