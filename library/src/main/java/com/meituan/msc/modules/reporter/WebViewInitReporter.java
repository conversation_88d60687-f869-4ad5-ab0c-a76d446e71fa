package com.meituan.msc.modules.reporter;

import android.os.SystemClock;

import com.meituan.msc.common.process.MSCProcess;
import com.meituan.msc.common.utils.HashMapHelper;
import com.meituan.msc.lib.BuildConfig;
import com.meituan.msc.modules.container.ApplicationLifecycleMonitor;
import com.meituan.msc.modules.page.render.webview.WebViewCacheManager;
import com.meituan.mtwebkit.internal.preload.MTWebViewPreloadInstance;

public class WebViewInitReporter extends MSCReporter {

    public WebViewInitReporter(String mscAppId) {
        commonTag(CommonTags.SDK_VERSION, BuildConfig.AAR_VERSION)
                .commonTag(CommonTags.TAG_MSC_APP_ID, mscAppId);
    }

    public void report(WebViewCacheManager.WebViewType webViewType, long duration, String usage, long startTime) {
        // 因为降级也是使用系统 WebView, 这里需要和不使用 MTWebView 的情况保持统一
        boolean isFirstInit;
        if (webViewType == WebViewCacheManager.WebViewType.MT_WEB_VIEW_SYSTEM) {
            isFirstInit = !WebViewCacheManager.sGlobalFirstWebViewCreated.get(WebViewCacheManager.WebViewType.CHROME);
        } else {
            isFirstInit = !WebViewCacheManager.sGlobalFirstWebViewCreated.get(webViewType);
        }
        MSCProcess currentProcess = MSCProcess.getCurrentProcess();
        String webViewTypeStr = CommonTags.getWebViewType(webViewType);
        boolean isMTWebViewPreload = MTWebViewPreloadInstance.getInstance().getIsPreload();
        record(ReporterFields.REPORT_LAUNCH_DURATION_WEB_VIEW_INIT)
                .tags(HashMapHelper.of(
                        "process", currentProcess == null ? "" : currentProcess.getLogName(),
                        ReporterFields.WEB_VIEW_TYPE, webViewTypeStr,
                        "isPreload", !ApplicationLifecycleMonitor.MSC.isFirstActivityCreated(),
                        "isMTWebViewPreload", isMTWebViewPreload,
                        "firstInit", isFirstInit,
                        "appForeground", ApplicationLifecycleMonitor.ALL.isForeground(),
                        "realInitDuration", duration,
                        "usage", usage
                        )
                )
                .value(SystemClock.elapsedRealtime() - startTime)
                .sendDelay();
    }

}
