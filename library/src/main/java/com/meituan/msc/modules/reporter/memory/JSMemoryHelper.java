package com.meituan.msc.modules.reporter.memory;

import android.net.Uri;
import android.support.annotation.NonNull;
import android.support.annotation.Nullable;
import android.text.TextUtils;

import com.meituan.crashreporter.CrashInfoProvider;
import com.meituan.crashreporter.CrashReporter;
import com.meituan.msc.common.framework.MSCRunningManager;
import com.meituan.msc.common.constant.APPIDConstants;
import com.meituan.msc.modules.reporter.MSCLog;

import java.util.ArrayList;
import java.util.HashMap;
import java.util.List;
import java.util.Map;
import java.util.concurrent.ConcurrentHashMap;

/**
 * Crash增加MMP JS内存信息
 * https://km.sankuai.com/page/**********
 *
 * <AUTHOR>
 * @date 2021/9/4.
 */
public final class JSMemoryHelper {

    private static final String TAG = "JSMemoryHelper";
    /**
     * 页面内存大小榜单中页面个数
     */
    private static final int TOP_SIZE = 5;
    /**
     * 存储所有新建的页面及其JS内存占用
     */
    @NonNull
    private static final ConcurrentHashMap<String, List<PageJSMemData>> memInfoMap = new ConcurrentHashMap<>();
    /**
     * 按页面JS内存大小来存储榜单信息
     */
    @NonNull
    private static final ConcurrentHashMap<String, List<PageJSMemData>> memInfoTopNMap = new ConcurrentHashMap<>();
    /**
     * 小程序维度缓存最近一次JS总内存占用
     */
    @NonNull
    private static final ConcurrentHashMap<String, Long> lastJSTotalSize = new ConcurrentHashMap<>();

    public static void addLastJSTotalSize(@Nullable String appId, @NonNull Long lastJSTotalSize) {
        if (appId == null || TextUtils.isEmpty(appId)) {
            MSCLog.d(TAG, "appId is null");
            return;
        }

        JSMemoryHelper.lastJSTotalSize.put(appId, lastJSTotalSize);
    }

    /**
     * 缓存对应小程序的页面地址及内存信息
     */
    public static void addPageMemInfo(@Nullable String appId, @Nullable String pageUrl, long jsMemSize) {
        if (appId == null || TextUtils.isEmpty(appId)
                || TextUtils.isEmpty(pageUrl) || TextUtils.equals(pageUrl, "null")) {
            MSCLog.d(TAG, "appId or pageUrl is null");
            return;
        }
        if (jsMemSize <= 0) {
            MSCLog.d(TAG, pageUrl, ", jsMemSize <= 0");
            return;
        }

        try {
            // 团好货部分页面path中参数value没有进行encode处理，导致Uri解析异常，比如商详页
            if (APPIDConstants.TUAN_HAO_HUO.equalsIgnoreCase(appId)) {
                if (pageUrl.contains("?")) {
                    pageUrl = pageUrl.substring(0, pageUrl.indexOf("?"));
                }
            }

            Uri uri = Uri.parse(pageUrl);
            final String pagePath = uri.getPath();
            List<PageJSMemData> memDataList = JSMemoryHelper.memInfoMap.get(appId);
            if (memDataList == null) {
                memDataList = new ArrayList<>();
                JSMemoryHelper.memInfoMap.put(appId, memDataList);
            }
            memDataList.add(new PageJSMemData(pagePath, jsMemSize));

            // 输出全量页面内存数据到Logan，协助排查内存问题
            MSCLog.d(TAG, "addPageMemInfo, ", pagePath, ":", jsMemSize);

            // 缓存内存占用Top5的页面
            addPageMemInfoToTopNMap(appId, pagePath, jsMemSize, TOP_SIZE);
        } catch (Throwable e) {
            MSCLog.e(TAG, e);
        }
    }

    private static void addPageMemInfoToTopNMap(String appId, String pageUrl, long jsMemSize, int topLimit) {
        List<PageJSMemData> memDataList = JSMemoryHelper.memInfoTopNMap.get(appId);
        if (memDataList == null) {
            memDataList = new ArrayList<>();
            memDataList.add(new PageJSMemData(pageUrl, jsMemSize));

            JSMemoryHelper.memInfoTopNMap.put(appId, memDataList);
        } else {
            final int size = memDataList.size();

            // 数据从大到小存储
            int insertIndex = -1;
            for (int i = 0; i < size; i++) {
                PageJSMemData info = memDataList.get(i);
                if (jsMemSize > info.jsMemKB) {
                    insertIndex = i;
                    break;
                }
            }

            if (insertIndex >= 0) {
                memDataList.add(insertIndex, new PageJSMemData(pageUrl, jsMemSize));
            } else {
                memDataList.add(new PageJSMemData(pageUrl, jsMemSize));
            }

            int finalSize = memDataList.size();
            if (finalSize > topLimit) {
                memDataList.remove(finalSize - 1);
            }
        }
    }

    /**
     * 上报前台小程序的平均页面内存占用大小
     */
    static void setPageAverageJSSize(@Nullable Map<String, Object> map) {
        if (map == null) {
            return;
        }
        try {
            String appId = MSCRunningManager.getCurrentForegroundAppIdInThisProcess();
            if (appId == null) {
                return;
            }

            List<PageJSMemData> memDataList = memInfoMap.get(appId);
            if (memDataList == null || memDataList.isEmpty()) {
                return;
            }

            int size = memDataList.size();
            long totalJSSize = 0;
            for (int i = 0; i < size; i++) {
                totalJSSize += memDataList.get(i).jsMemKB;
            }

            map.put("mmpPageJSSizeAVG", totalJSSize / size);
        } catch (Throwable e) {
            MSCLog.e(TAG, null, "setPageAverageJSSize error:", e);
        }
    }

    /**
     * 上报前台小程序内存占用最大的5个页面
     */
    static void setTop5PageMemInfo(@Nullable Map<String, Object> map, int lengthLimit) {
        if (map == null) {
            return;
        }

        try {
            String appId = MSCRunningManager.getCurrentForegroundAppIdInThisProcess();
            if (appId == null) {
                return;
            }

            List<PageJSMemData> memDataList = memInfoTopNMap.get(appId);
            if (memDataList == null) {
                return;
            }

            ArrayList<Map<String, Object>> pageMemDataList = new ArrayList<>();

            // 这里再转一次格式的目的是为了减少上报字符串的长度
            // 最终上报格式为
            // [
            //  {
            //    "/packages/shopping-cart-inner/index/shopping-cart":12000 //key为小程序页面路径，value为当前小程序页面占用的js内存，单位KB
            //  }
            //]

            int contentLength = 0;
            for (PageJSMemData info : memDataList) {
                Map<String, Object> tempMap = new HashMap<>();
                tempMap.put(info.pagePath, info.jsMemKB);

                contentLength += tempMap.toString().length();
                if (contentLength > lengthLimit) {
                    break;
                }

                pageMemDataList.add(tempMap);
            }

            map.put("mmpPageTop", pageMemDataList);
        } catch (Throwable e) {
            MSCLog.e(TAG, null, "setTop5PageMemInfo error:", e);
        }
    }

    /**
     * 上报前台小程序操作页面栈中最后 N 个页面的内存数据
     * N 由上报字符串长度限制计算得出
     */
    static void setLastNPageMemInfo(@Nullable Map<String, Object> map, int lengthLimit) {
        if (map == null) {
            return;
        }

        try {
            String appId = MSCRunningManager.getCurrentForegroundAppIdInThisProcess();
            if (appId == null) {
                return;
            }

            List<PageJSMemData> memDataList = memInfoMap.get(appId);
            if (memDataList == null) {
                return;
            }

            ArrayList<Map<String, Object>> pageMemDataList = new ArrayList<>();
            // 这里再转一次格式的木的是为了减少字符串的长度
            // 最终上报格式为
            // [
            //  {
            //    "/packages/shopping-cart-inner/index/shopping-cart":12000 //key为小程序页面路径，value为当前小程序页面占用的js内存，单位KB
            //  }
            //]
            for (PageJSMemData info : memDataList) {
                HashMap<String, Object> tempMap = new HashMap<>();
                tempMap.put(info.pagePath, info.jsMemKB);

                int length = pageMemDataList.toString().length();
                if (length + tempMap.toString().length() > lengthLimit) {
                    break;
                }

                pageMemDataList.add(tempMap);
            }

            map.put("mmpJSDetails", pageMemDataList);
        } catch (Throwable e) {
            MSCLog.e(TAG, null,"setLastNPageMemInfo error:", e);
        }
    }

    public static void registerCrashInfoProviders() {
        CrashReporter.getInstance().registerCrashInfoProvider(new CrashInfoProvider() {
            @Override
            public Map<String, Object> getCrashInfo(String s, boolean b) {
                MSCLog.e(TAG, "registerCrashInfoProviders start");

                Map<String, Object> map = new HashMap<>();
                // 上报前台小程序最近一次获取到的总内存占用
                setLastTotalJSSize(map);
                // 上报前台小程序页面平均内存占用
                setPageAverageJSSize(map);
                // 上报前台小程序内存占用排名前5的页面
                setTop5PageMemInfo(map, 1000 - map.toString().length());
                // 上报前台小程序最后N个页面内存占用
                setLastNPageMemInfo(map, 1000 - map.toString().length());

                MSCLog.e(TAG, null, "registerCrashInfoProviders:", map);
                return map;
            }
        });
    }

    static void setLastTotalJSSize(Map<String, Object> map) {
        if (map == null) {
            return;
        }
        try {
            String appId = MSCRunningManager.getCurrentForegroundAppIdInThisProcess();
            if (appId == null) {
                return;
            }

            Long lastJSTotalSize = JSMemoryHelper.lastJSTotalSize.get(appId);
            map.put("lastJSTotalSize", lastJSTotalSize);
        } catch (Throwable e) {
            MSCLog.e(TAG, null, "setPageAverageJSSize error:", e);
        }
    }

    public static void clearPageMemCache(@Nullable String mAppId) {
        if (mAppId == null) {
            return;
        }

        memInfoMap.remove(mAppId);
        memInfoTopNMap.remove(mAppId);
        lastJSTotalSize.remove(mAppId);
    }

    public static class PageJSMemData {
        @NonNull
        public String pagePath;
        public long jsMemKB;

        public PageJSMemData(@NonNull String pagePath, long jsMemKB) {
            this.pagePath = pagePath;
            this.jsMemKB = jsMemKB;
        }

        @Override
        public String toString() {
            return "PageJSMemInfo{" +
                    "pagePath='" + pagePath + '\'' +
                    ", jsMemKB=" + jsMemKB +
                    '}';
        }
    }
}
