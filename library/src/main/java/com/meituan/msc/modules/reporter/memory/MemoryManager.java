package com.meituan.msc.modules.reporter.memory;

import android.content.ComponentCallbacks2;
import android.content.Context;
import android.content.res.Configuration;
import android.os.SystemClock;

import com.meituan.msc.modules.engine.RuntimeManager;
import com.meituan.msc.modules.preload.PreloadTasksManager;
import com.meituan.msc.modules.reporter.MSCLog;

/**
 * Created by letty on 2019/11/12.
 **/
public class MemoryManager {

    public static final int TRIM_MEMORY_NEVER = 0;  // 作为默认值，从未发生过TrimMemory

    private static int lastFgTrimLevel = TRIM_MEMORY_NEVER;
    private static long lastFgTrimTime;

    private static int lastBgTrimLevel = TRIM_MEMORY_NEVER;
    private static long lastBgTrimTime;

    public static void init(Context context) {
        if (context == null) {
            return;
        }
        context.registerComponentCallbacks(new ComponentCallbacks2() {
            @Override
            public void onTrimMemory(int level) {
                if (level == TRIM_MEMORY_BACKGROUND ||
                        level == TRIM_MEMORY_UI_HIDDEN) {
                    // 仅处理能表达系统内存紧张状况的level，对于进后台固定会引起的trim，无视
                    return;
                }

                if (level >= TRIM_MEMORY_RUNNING_LOW &&
                        level != TRIM_MEMORY_COMPLETE) {
                    MSCLog.w("MemoryManager", "onTrimMemory:" + level);

                    // TODO 保活且只打开了一个小程序时，返回桌面会立即导致TRIM_MEMORY_COMPLETE调用，可能应该忽略，但忽略后内存回收功能可能就基本失效了
//                    if (MSCProcess.isInMainProcess()) {
//                        // 因AppBrand信息只在主进程集中，只在主进程进行操作
//                        AppBrandRouterCenter.killAllAppBrand(true);
//                    }
                    RuntimeManager.onLowMemory();
                    PreloadTasksManager.instance.onLowMemory();
                }

//                MetricsModule.reportMetrics(ReporterFields.REPORT_STABILITY_TRIM_MEMORY,
//                        HashMapHelper.of(
//                                "level", level
////                                "totalMemory", DeviceUtil.getTotalMemory(MSCEnvHelper.getContext()),
////                                "availMemory", DeviceUtil.getAvailableMemory(MSCEnvHelper.getContext()))
//                        ));

//                if (DebugHelper.isDebug()) {
//                    Map<String, Long> memData = MemoryMonitor.parseMemInfo();
//                    StringBuilder sb = new StringBuilder();
//                    for (Map.Entry<String, Long> entry : memData.entrySet()) {
//                        sb.append(entry.getKey()).append(": ").append(FileSizeUtil.formatFileSize(entry.getValue())).append("\n");
//                    }
//                    MSCLog.d("MemoryMonitor", sb);
//                }

                if (level < TRIM_MEMORY_UI_HIDDEN) {
                    lastFgTrimLevel = level;
                    lastFgTrimTime = SystemClock.elapsedRealtime();
                } else if (level > TRIM_MEMORY_BACKGROUND) {
                    lastBgTrimLevel = level;
                    lastBgTrimTime = SystemClock.elapsedRealtime();
                }
            }

            @Override
            public void onConfigurationChanged(Configuration newConfig) {

            }

            @Override
            public void onLowMemory() {

            }
        });
    }

    public static int getLastFgTrimLevel() {
        return lastFgTrimLevel;
    }

    public static long getLastFgTrimTime() {
        return lastFgTrimTime;
    }

    public static int getLastBgTrimLevel() {
        return lastBgTrimLevel;
    }

    public static long getLastBgTrimTime() {
        return lastBgTrimTime;
    }
}
