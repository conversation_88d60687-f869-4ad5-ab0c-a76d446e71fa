package com.meituan.msc.modules.reporter.memory;

import android.os.Build;
import android.os.Debug;
import android.support.annotation.UiThread;

import com.meituan.msc.common.executor.MSCExecutors;
import com.meituan.msc.common.support.java.util.concurrent.CompletableFuture;
import com.meituan.msc.modules.reporter.MSCLog;
import com.sankuai.android.jarvis.Jarvis;

import java.util.List;
import java.util.concurrent.CopyOnWriteArrayList;
import java.util.concurrent.ExecutorService;
import java.util.concurrent.LinkedBlockingQueue;
import java.util.concurrent.TimeUnit;

/**
 * App级别的内存监控，默认每1s获取一次当前进程占用的总内存大小
 * 尽量不要在当前线程直接获取内存大小，比较耗时
 **/
public class MemoryMonitor {

    private static final String TAG = "MemoryMonitor";
    private static final long TIMED_SAMPLING_INTERVAL = 1000;
    private static final long CALL_GET_MEMORY_IN_MB_OF_CURRENT_PROCESS_ASYNC_INTERVAL = 5;

    private static final MemoryMonitor INSTANCE = new MemoryMonitor();

    public static MemoryMonitor getInstance() {
        return INSTANCE;
    }

    private MemoryMonitor() {

    }

    // 保证串行，耗时的采样操作尽量不干扰正常行为，且线程可释放
    private final ExecutorService executor = Jarvis.newThreadPoolExecutor("MSC-MemoryMonitor",
            0, 1, 5L, TimeUnit.SECONDS,
            new LinkedBlockingQueue<Runnable>());

    private final List<IMemorySampleListener> sampleListeners = new CopyOnWriteArrayList<>();
    private boolean isMonitoring = false;
    private int lastSampleValue = -1;
    private long lastCallTimeOfGetMemoryInMbOfCurrentProcessAsync = -1;

    public void addMemorySampleListener(IMemorySampleListener sampleListener) {
        if (!sampleListeners.contains(sampleListener)) {
            sampleListeners.add(sampleListener);
        }
        if (!sampleListeners.isEmpty()) {
            startMonitor();
        }
    }

    public void removeMemorySampleListener(IMemorySampleListener sampleListener) {
        sampleListeners.remove(sampleListener);
        if (sampleListeners.isEmpty()) {
            stopMonitor();
        }
    }

    private void postTimedSampling() {
        MSCExecutors.removeUIThreadCallbacks(timedSamplingRunnable);
        MSCExecutors.runOnUiThreadDelayed(timedSamplingRunnable, TIMED_SAMPLING_INTERVAL);
    }

    private final Runnable timedSamplingRunnable = new Runnable() {
        @UiThread
        @Override
        public void run() {
            doSampleWork(null);
        }
    };

    private void doSampleWork(CompletableFuture<Integer> completableFuture) {
        MSCExecutors.removeUIThreadCallbacks(timedSamplingRunnable);

        executor.submit(new Runnable() {
            @Override
            public void run() {
                int memoryUsage = sample();
                if (completableFuture != null) {
                    completableFuture.complete(memoryUsage);
                }
                if (isMonitoring) {
                    // 计划下一轮
                    postTimedSampling();
                }
            }
        });
    }

    private int sample() {
        int memoryUsageInMB = getMemoryInMbOfCurrentProcess();
//        MSCLog.d(TAG, "Get process memory size: ", memoryUsageInMB);
        lastSampleValue = memoryUsageInMB;

        if (isMonitoring) {
            // 通知页面更新数据，这里判断一下是否在work，避免stop之后且监听器还没remove的时候，调用到监听器回调导致并发问题
            for (IMemorySampleListener sampleListener : sampleListeners) {
                sampleListener.onSample(memoryUsageInMB);
            }
        }
        return memoryUsageInMB;
    }

    public void startMonitor() {
        if (isMonitoring) {
            return;
        }
        doSampleWork(null);
        isMonitoring = true;
    }

    public void stopMonitor() {
        if (!isMonitoring) {
            return;
        }
        MSCExecutors.removeUIThreadCallbacks(timedSamplingRunnable);
        isMonitoring = false;
    }

    /**
     * 获取进程的内存占用信息，单位为 MB
     * 中端机耗时4ms
     * @return 进程的 /proc/{pid}/smaps_rollup 中的 pss + graphics占用
     */
    private static int getMemoryInMbOfCurrentProcess() {
        return (int) (Debug.getPss() / 1024);
    }

    /**
     * 获取进程的内存占用信息，单位为 MB
     *
     * @return
     */
    public CompletableFuture<Integer> getMemoryInMbOfCurrentProcessAsync() {
        long startTime = System.currentTimeMillis();
        CompletableFuture<Integer> completableFuture = new CompletableFuture<>();
        if (startTime - lastCallTimeOfGetMemoryInMbOfCurrentProcessAsync < CALL_GET_MEMORY_IN_MB_OF_CURRENT_PROCESS_ASYNC_INTERVAL) {
            completableFuture.complete(lastSampleValue);
            MSCLog.d(TAG, "调用速度过快");
        } else {
            doSampleWork(completableFuture);
            lastCallTimeOfGetMemoryInMbOfCurrentProcessAsync = startTime;
        }
        return completableFuture;
    }

    public int getLastSampleValue() {
        return lastSampleValue;
    }

    @Deprecated
    private static long gcTrackerStartTime;

    public static void startTrackingGCTime() {
        if (Build.VERSION.SDK_INT >= Build.VERSION_CODES.M) {
            try {
                gcTrackerStartTime = Long.parseLong(Debug.getRuntimeStat("art.gc.gc-time"));
            } catch (Throwable e) {
                MSCLog.e(TAG, e);
            }
        }
    }

    public static long getGCTimeCost() {
        long gcTimeCost = 0;
        if (android.os.Build.VERSION.SDK_INT >= android.os.Build.VERSION_CODES.M) {
            try {
                gcTimeCost = Math.max(0, Long.parseLong(Debug.getRuntimeStat("art.gc.gc-time")) - gcTrackerStartTime);
            } catch (Throwable e) {
                MSCLog.e(TAG, e);
            }
        }
        return gcTimeCost;
    }
}
