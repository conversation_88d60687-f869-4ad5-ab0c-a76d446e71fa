package com.meituan.msc.modules.reporter.memory;

import com.meituan.msc.modules.engine.MSCHornRollbackConfig;
import com.meituan.msc.modules.page.render.AppPageReporter;
import com.meituan.msc.modules.reporter.ReporterFields;

/**
 * 页面内存监听
 */
public class PageMemoryMonitor implements IMemorySampleListener {
    private final MemoryMonitor memoryMonitor;
    private boolean isWorking = false;
    private int peakMemorySize = -1;
    private int valleyMemorySize = -1;
    private int pageStartMemorySize = -1;
    private int ffpMemorySize = -1;
    private String appId;

    public PageMemoryMonitor(String appId) {
        this.memoryMonitor = MemoryMonitor.getInstance();
        this.appId = appId;
    }

    @Override
    public void onSample(int processMemoryUsageInMB) {
        if (processMemoryUsageInMB > peakMemorySize) {
            peakMemorySize = processMemoryUsageInMB;
        }
        if (valleyMemorySize < 0 || processMemoryUsageInMB < valleyMemorySize) {
            valleyMemorySize = processMemoryUsageInMB;
        }
    }

    public void onCreate() {
        if (MSCHornRollbackConfig.isRollbackPageMemoryReport()
                || MSCHornRollbackConfig.isRollbackPageMemoryReportWithAppId(appId)) {
            return;
        }
        if (pageStartMemorySize < 0) {
            memoryMonitor.getMemoryInMbOfCurrentProcessAsync().thenAccept(integer -> pageStartMemorySize = integer);
        }
    }

    public void onPageExit(AppPageReporter reporter) {
        if (MSCHornRollbackConfig.isRollbackPageMemoryReport()
                || MSCHornRollbackConfig.isRollbackPageMemoryReportWithAppId(appId)) {
            return;
        }
        stop();
        memoryMonitor.getMemoryInMbOfCurrentProcessAsync().thenAccept(leaveMemory -> {
            reporter.once(ReporterFields.REPORT_PAGE_MEMORY_USAGE)
                    // 内存峰值绝对值
                    .tag("peakMemory", peakMemorySize)
                    // 内存最小值
                    .tag("valleyMemory", valleyMemorySize)
                    // 进入页面时内存绝对值
                    .tag("enterMemory", pageStartMemorySize)
                    // 秒开时刻内存绝对值
                    .tag("ffpMemory", ffpMemorySize)
                    // 离开页面时内存绝对值
                    .tag("leaveMemory", leaveMemory)
                    // 页面内存峰值与最小值的差值
                    .value(peakMemorySize - valleyMemorySize)
                    .sendDelay();
        });
    }

    public void onFFPEnd() {
        if (MSCHornRollbackConfig.isRollbackPageMemoryReport()
                || MSCHornRollbackConfig.isRollbackPageMemoryReportWithAppId(appId)) {
            return;
        }
        memoryMonitor.getMemoryInMbOfCurrentProcessAsync().thenAccept(integer -> ffpMemorySize = integer);
    }

    public void onHide() {
        stop();
    }

    public void onShow() {
        start();
    }

    private void start() {
        if (MSCHornRollbackConfig.isRollbackPageMemoryReport()
                || MSCHornRollbackConfig.isRollbackPageMemoryReportWithAppId(appId)) {
            return;
        }
        if (isWorking) {
            return;
        }
        memoryMonitor.addMemorySampleListener(this);
        isWorking = true;
    }

    private void stop() {
        if (MSCHornRollbackConfig.isRollbackPageMemoryReport()
                || MSCHornRollbackConfig.isRollbackPageMemoryReportWithAppId(appId)) {
            return;
        }
        if (!isWorking) {
            return;
        }
        memoryMonitor.removeMemorySampleListener(this);
        isWorking = false;
    }
}
