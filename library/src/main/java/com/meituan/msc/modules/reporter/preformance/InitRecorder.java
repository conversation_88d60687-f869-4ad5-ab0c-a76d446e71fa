package com.meituan.msc.modules.reporter.preformance;

import android.os.SystemClock;

import com.meituan.metrics.util.TimeUtil;

import static com.meituan.msc.common.utils.TimeUtil.convertSystemElapsedRealtimeToUnixTime;

public class InitRecorder {

    /**
     * 本进程启动时间
     */
    private static long processStartTime = 0;

    /**
     * 在子进程中如可能，取主进程启动时间
     */
    private static long applicationStartTime = 0;

    private static void ensureInit() {
        if (processStartTime == 0) {
            processStartTime = TimeUtil.processStartElapsedTimeMillis();
        }
        if (applicationStartTime == 0) {
            applicationStartTime = processStartTime;
        }
    }

    /**
     * 可重复调用，取记录到的最早的时间为应用启动时间
     * TODO 目前还没有确保从主进程传入此时间到子进程，仅在主进程用于统计预加载开始及消费时间
     */
    public static void setApplicationStartTime(long time) {
        if (applicationStartTime == 0 || applicationStartTime > time) {
            applicationStartTime = time;
        }
    }

    public static long getApplicationStartElapsedRealtime() {
        ensureInit();
        return applicationStartTime;
    }

    public static long getApplicationStartUnixTime() {
        ensureInit();
        return convertSystemElapsedRealtimeToUnixTime(applicationStartTime);
    }

    public static long getDurationSinceApplicationStart() {
        ensureInit();
        return SystemClock.elapsedRealtime() - applicationStartTime;
    }
}
