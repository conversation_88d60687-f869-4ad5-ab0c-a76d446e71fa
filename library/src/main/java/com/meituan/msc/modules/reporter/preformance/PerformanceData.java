package com.meituan.msc.modules.reporter.preformance;

import com.meituan.msc.modules.reporter.MSCLog;

import org.json.JSONException;
import org.json.JSONObject;


public class PerformanceData extends JSONObject{
    public static final String ENTRY_TYPE = "entryType";
    public static final String ENTRY_TYPE_NAVIGATION = "navigation";
    public static final String ENTRY_TYPE_RENDER = "render";
    public static final String ENTRY_TYPE_MT = "mt";
    public static final String ENTRY_TYPE_SCRIPT = "script";
    public static final String NAVIGATION_TYPE = "navigationType";
    public static final String ENTRY_TYPE_LOAD_PACKAGE = "loadPackage";
    private static final String TAG = "PerformanceData";

    long mStartTime;

    public PerformanceData() {
    }

    public PerformanceData addEntryType(String type) {
        try {
            put(ENTRY_TYPE, type);
        } catch (JSONException e) {
            e.printStackTrace();
        }
        return this;
    }

    public PerformanceData addName(String name) {
        try {
            put("name", name);
        } catch (JSONException e) {
            e.printStackTrace();
        }
        return this;
    }

    public PerformanceData addNavigationType(String type) {
        try {
            put(NAVIGATION_TYPE, type);
        } catch (JSONException e) {
            e.printStackTrace();
        }
        return this;
    }

    public PerformanceData addPageId(int pageId) {
        try {
            put("pageId", pageId);
        } catch (JSONException e) {
            e.printStackTrace();
        }
        return this;
    }

    public PerformanceData addPagePath(String path) {
        try {
            put("path", path);
        } catch (JSONException e) {
            e.printStackTrace();
        }
        return this;
    }

    public PerformanceData addStartTime(long startTime) {
        mStartTime = startTime;
        try {
            put("startTime", startTime);
        } catch (JSONException e) {
            e.printStackTrace();
        }
        return this;
    }

    /**
     * 注意算duration，需要endTime在startTime之后调用
     * @param endTime
     * @return
     */
    public PerformanceData addEndTime(long endTime) {
        try {
            put("endTime", endTime);
            put("duration", endTime - mStartTime);
        } catch (JSONException e) {
            e.printStackTrace();
        }
        return this;
    }

    public PerformanceData addNavigationStartTime(long time) {
        try {
            put("navigationStart", time);
        } catch (JSONException e) {
            e.printStackTrace();
        }
        return this;
    }

    public PerformanceData addWebViewPreloadState(String webViewPreloadState) {
        try {
            put("webviewPreloadState", webViewPreloadState);
        } catch (JSONException e) {
            MSCLog.e(TAG, e, "addWebViewPreloadState");
        }
        return this;
    }

    public PerformanceData addHostAppAttachTime() {
        try {
            put("hostAppAttachTime", InitRecorder.getApplicationStartUnixTime());
        } catch (JSONException e) {
            e.printStackTrace();
        }
        return this;
    }

}
