package com.meituan.msc.modules.reporter.preformance;

import android.support.annotation.NonNull;

import com.meituan.msc.common.utils.VersionUtil;
import com.meituan.msc.modules.api.legacy.appstate.AppListener;
import com.meituan.msc.modules.engine.MSCHornRollbackConfig;
import com.meituan.msc.modules.engine.MSCRuntime;
import com.meituan.msc.modules.page.render.BaseRenderer;
import com.meituan.msc.modules.page.render.MSCHornPerfConfig;
import com.meituan.msc.modules.reporter.MSCLog;
import com.meituan.msc.modules.update.bean.PackageInfoWrapper;

import org.json.JSONArray;
import org.json.JSONException;
import org.json.JSONObject;

import java.util.List;
import java.util.concurrent.CopyOnWriteArrayList;

/**
 * 用于处理 wx.getPerformance() 相关数据的类
 * 数据存储于JS层，客户端发送完就清掉，避免过多的内存占用
 *
 * ref: https://developers.weixin.qq.com/miniprogram/dev/api/base/performance/wx.getPerformance.html
 *
 */
public class PerformanceManager {
    public static final String TAG = "PerformanceManager";

    private final MSCRuntime runtime;
    private final List<PerformanceData> cachedPerformanceData = new CopyOnWriteArrayList<>();

    public PerformanceManager(@NonNull MSCRuntime runtime) {
        this.runtime = runtime;
    }

    /**
     * 通知前端小程序性能相关的信息
     * https://developers.weixin.qq.com/miniprogram/dev/api/open-api/performance/wx.getPerformance.html
     */
    public void onFirstRender(BaseRenderer.BasePageData pageData, int pageId) {
        if (VersionUtil.compare(runtime.getMSCAppModule().getBasePkgVersion(), "1.8.0") < 0) {
            return;
        }

        long endTime = System.currentTimeMillis();
        JSONArray jsonArray = new JSONArray();
        jsonArray.put(createNavigationPerformanceData(pageData, endTime, pageId));
        jsonArray.put(createRenderPerformanceData(pageData, endTime, pageId));
        jsonArray.put(runtime.getScriptPerformanceData());

        // 刷新一波缓存
        for (PerformanceData data : cachedPerformanceData) {
            jsonArray.put(data);
        }
        cachedPerformanceData.clear();

        onPerformanceDataChange(jsonArray);
    }

    private void onPerformanceDataChange(JSONArray dataArray) {
        // TODO by chendacai 控制发送频率，避免消息过多阻塞消息通道
        JSONObject jsonObject = new JSONObject();
        try {
            jsonObject.put("list", dataArray);
            String param = jsonObject.toString();
            MSCLog.i(TAG, "onPerformanceDataChange", param);
            runtime.getJSModuleDelegate(AppListener.class, null)
                    .onPerformanceDataChange(param);
        } catch (JSONException e) {
            MSCLog.e(TAG, e);
        }
    }

    private void onPerformanceDataChange(PerformanceData data) {
        if (data == null) {
            return;
        }
        JSONArray jsonArray = new JSONArray();
        jsonArray.put(data);
        onPerformanceDataChange(jsonArray);
    }

    public void onDataPrefetch(String pagePath, long startTime) {
        PerformanceData dataPrefetch = new PerformanceData()
                .addEntryType(PerformanceData.ENTRY_TYPE_MT)
                .addName("dataPrefetch")
                .addPagePath(pagePath)
                .addStartTime(startTime)
                .addEndTime(System.currentTimeMillis());
        onPerformanceDataChange(dataPrefetch);
    }

    public void onFFP(String pagePath, int pageId, long ffpStartTime, long ffpEndTime) {
        PerformanceData data = new PerformanceData()
                .addEntryType(PerformanceData.ENTRY_TYPE_MT)
                .addName("fsp2")
                .addPagePath(pagePath)
                .addPageId(pageId)
                .addStartTime(ffpStartTime)
                .addEndTime(ffpEndTime);
        onPerformanceDataChange(data);
    }

    public void onDownloadPackage(PackageInfoWrapper packageInfo) {
        if (packageInfo != null && packageInfo.isFromNet()) {
            PerformanceData data = new PerformanceData()
                    .addEntryType(PerformanceData.ENTRY_TYPE_LOAD_PACKAGE)
                    .addName("downloadPackage")
                    .addStartTime(packageInfo.getDownloadStartTimeInMs())
                    .addEndTime(packageInfo.getDownloadEndTimeInMs());
            try {
                data.put("packageName", packageInfo.getPackageName());
                data.put("packageSize", packageInfo.getPackageSize());
            } catch (JSONException e) {
                MSCLog.e(TAG, e);
            }
            cachedPerformanceData.add(data);
        }
    }

    /**
     * 获取navigation数据
     * entryType=appLaunch 启动数据
     * entryType=route 前端调用路由Api导航页面数据
     * 各字段说明https://km.sankuai.com/page/976955294
     */
    private static JSONObject createNavigationPerformanceData(BaseRenderer.BasePageData pageData, long endTime, int pageId) {
        long startTime;
        if (MSCHornPerfConfig.getInstance().enableFPUsePageStartTime()) {
            startTime = pageData.appPageReporter.getPageStartTime();
        } else {
            startTime = pageData.containerReporter != null
                    ? pageData.containerReporter.getLaunchStartTimeCurrentTimeMillis()
                    : pageData.pageStartRecord.pageStartTimeCurrentTimeMillis;
        }
        PerformanceData performanceData = new PerformanceData()
                .addEntryType(PerformanceData.ENTRY_TYPE_NAVIGATION)
                .addName(pageData.containerReporter != null ? "appLaunch" : "route")
                .addNavigationType(pageData.openType)
                .addPagePath(pageData.mPagePath)
                .addPageId(pageId)
                .addStartTime(startTime)
                .addEndTime(endTime);
        if (pageData.containerReporter == null) {
            performanceData.addNavigationStartTime(pageData.onAppRouteTime);
        }
        if (!MSCHornRollbackConfig.isRollbackPerformanceAppAttach() && pageData.containerReporter != null) {
            // 新增宿主app启动时机：https://km.sankuai.com/collabpage/2255596340
            performanceData.addHostAppAttachTime();
        }
        return performanceData;
    }


    /**
     * 获取render数据
     */
    private static JSONObject createRenderPerformanceData(BaseRenderer.BasePageData pageData, long endTime, int pageId) {
        return new PerformanceData()
                .addEntryType(PerformanceData.ENTRY_TYPE_RENDER)
                .addName("firstRender")
                .addPagePath(pageData.mPagePath)
                .addPageId(pageId)
                .addWebViewPreloadState(pageData.webViewPreloadState)
                .addStartTime(pageData.onAppRouteTime)
                .addEndTime(endTime);
    }

    public static JSONObject createScriptPerformanceData(long startTime) {
        return new PerformanceData()
                .addEntryType(PerformanceData.ENTRY_TYPE_SCRIPT)
                .addStartTime(startTime)
                .addName("evaluateScript")
                .addEndTime(System.currentTimeMillis());
    }
}
