package com.meituan.msc.modules.reporter.prexception;

import android.support.annotation.StringDef;

import java.lang.annotation.Retention;
import java.lang.annotation.RetentionPolicy;

/**
 * 页面层状态
 * pageStage 视图层/渲染层 在上报时机的执行阶段
 */
public interface AppPageState {
    String DEFAULT = "page_default";
    String LOAD_HTML = "page_loadHTML";
    String PACKAGE_INJECT = "page_packageInject";
    String FIRST_SCRIPT = "page_firstScript";
    String READY = "page_ready";
    /**
     * 普通和复用情况下的正常状态
     */
    String FIRST_RENDER = "page_firstRender";

    /**
     * 渲染缓存下的正常状态
     */
    String SNAPSHOT_INTERACTIVE = "page_snapshot_interactive";

    @Retention(RetentionPolicy.SOURCE)
    @StringDef({DEFAULT, LOAD_HTML, PACKAGE_INJECT, FIRST_SCRIPT, READY, FIRST_RENDER, SNAPSHOT_INTERACTIVE})
    @interface State {

    }

}
