package com.meituan.msc.modules.reporter.prexception;

import android.support.annotation.StringDef;

import java.lang.annotation.Retention;
import java.lang.annotation.RetentionPolicy;

/**
 * appservice相关状态
 * serviceStage 逻辑层在上报时机的执行阶段
 */
public interface AppServiceState {
    /**
     * 默认
     */
    String DEFAULT = "service_default";
    /**
     * 所有包下载完成（基础库、主包、子包）, 相对于mmp新增
     */
    String PACKAGE_INSTALLED = "service_packageInstalled";
    /**
     * 所有包注入完成（基础库、主包、子包）
     */
    String PACKAGE_INJECT = "service_packageInject";
    /**
     * 监听 custom_event_serviceReady 事件, 相对于mmp废弃
     */
    String READY = "service_ready";
    /**
     * native主动触发appRoute
     */
    String APP_ROUTE = "service_appRoute";
    /**
     * mmp 中监听 custom_event_appLaunch 事件
     * msc 中监听 WebViewModule#appLaunch(long, boolean)
     */
    String APP_LAUNCH = "service_appLaunch";

    @Retention(RetentionPolicy.SOURCE)
    @StringDef({DEFAULT, PACKAGE_INSTALLED, PACKAGE_INJECT, READY, APP_ROUTE, APP_LAUNCH})
    @interface State {

    }
}
