package com.meituan.msc.modules.reporter.prexception;

import android.support.annotation.NonNull;
import android.text.TextUtils;

import com.meituan.msc.common.config.MSCConfig;
import com.meituan.msc.common.utils.PathUtil;
import com.meituan.msc.extern.MSCEnvHelper;
import com.meituan.msc.lib.BuildConfig;
import com.meituan.msc.modules.engine.MSCHornRollbackConfig;
import com.meituan.msc.modules.engine.MSCRuntime;
import com.meituan.msc.modules.reporter.CommonTags;
import com.meituan.msc.modules.reporter.MSCLog;
import com.meituan.msc.modules.reporter.MSCReporter;
import com.meituan.msc.modules.reporter.ReporterFields;
import com.meituan.msc.modules.update.MSCAppModule;
import com.meituan.msc.modules.update.bean.AppMetaInfoWrapper;
import com.sankuai.common.utils.VersionUtil;

import java.util.HashMap;
import java.util.Map;
import java.util.concurrent.ConcurrentHashMap;

/**
 * msc容器启动异常感知能力
 * https://km.sankuai.com/collabpage/2111790230
 */
public class PageExceptionRecordReporter {
    public final String TAG = "PageExceptionRecordReporter@" + Integer.toHexString(hashCode());
    // 前端基础库自此版本支持上报 service_appLaunch
    private static final String TARGET_VERSION = "1.36.1.154";
//    private final MSCApp mApp;
    private final MSCRuntime mRuntime;
    /**
     * 实时获取的埋点数据
     */
    private final Map<String, Object> mInfoMap = new ConcurrentHashMap<>();
    /**
     * 小程序内不变的数据
     */
    private final Map<String, Object> mInvariableInfoMap = new ConcurrentHashMap<>();
    /**
     * route start time
     */
    private long mStartTime;
    private boolean isWidget = false;
    private boolean isFirstPage = false;
    private String mBasePkgVersion = "";

    /**
     * 是否需要上报异常
     */
    private boolean needReportExceptionDot = true;

    /**
     * WebView复用场景白色蒙层是否展示
     */
    private boolean isWhiteForegroundShow = false;

    /**
     * 是否为页面退出
     */
    private boolean isExit = false;
    /**
     * 是否是WebView渲染页面
     */
    private boolean isWebViewRenderer = true;
    @AppPageState.State
    public volatile String pageState = AppPageState.DEFAULT;
    @AppServiceState.State
    public volatile String serviceStage = AppServiceState.DEFAULT;

    public PageExceptionRecordReporter(@NonNull MSCRuntime runtime) {
        //mApp = app.getApp();
        mRuntime = runtime;
        mInvariableInfoMap.put("mscAppId", String.valueOf(runtime.getAppId()));
        //mmp sdk version
        mInvariableInfoMap.put("sdkVersion", BuildConfig.AAR_VERSION);
        if (MSCEnvHelper.isInited()) {
            mInvariableInfoMap.put("env", MSCEnvHelper.getEnvInfo().isProdEnv() ? "prod" : "test");
        } else {
            mInvariableInfoMap.put("env", "test");
        }
    }

    public void setStartTime(long startTime) {
        mStartTime = startTime;
    }

    public long getStartTime() {
        return mStartTime;
    }

    public void setPagePath(String pagePath) {
        addExtraInfo("pagePath", pagePath);
        addExtraInfo("purePath", PathUtil.getPath(pagePath));
    }

    public void setIsWhiteForegroundShow(boolean isWhiteForegroundShow) {
        this.isWhiteForegroundShow = isWhiteForegroundShow;
    }

    public void setIsWebViewRenderer(boolean isWebViewRenderer) {
        this.isWebViewRenderer = isWebViewRenderer;
    }

    public void setNeedReportExceptionDot(boolean needReportExceptionDot) {
        this.needReportExceptionDot = needReportExceptionDot;
    }

    public void setIsExit(boolean isExit) {
        this.isExit = isExit;
    }


    /**
     * 仅在栈底页面添加
     * 跳链里面的 extraData.scene，仅启动页面需要
     *
     * @param scene 前置页面
     */
    public void setScene(String scene) {
        addExtraInfo("scene", scene);
    }

    /**
     * 运行时来源
     *
     * @param runtimeSource 运行时来源
     */
    public void setRuntimeSource(String runtimeSource) {
        addExtraInfo(CommonTags.TAG_RUNTIME_SOURCE, runtimeSource);
    }

    /**
     * 设置包类型
     *
     * @param pkgMode 包类型
     */
    public void setPkgMode(String pkgMode) {
        addExtraInfo("pkgMode", pkgMode);
    }

    public void setPkgModeDetail(String pkgModeDetail) {
        addExtraInfo("pkgModeDetail", pkgModeDetail);
    }

    public void setIsWidget(boolean isWidget) {
        this.isWidget = isWidget;
    }

    public void setIsFirstPage(boolean isFirstPage) {
        this.isFirstPage = isFirstPage;
    }

    public void setPkgInstalled(boolean pkgInstalled) {
        addExtraInfo("pkgInstalled", String.valueOf(pkgInstalled));
    }

    public void setPkgName(String pkgName) {
        addExtraInfo("pkgName", pkgName);
    }

    /**
     * 一个Runtime可能有多个页面, 不再在Runtime中存储pageStage
     * @param pageState 上报时单独设置
     */
    public void setPageState(String pageState) {
        addExtraInfo("pageStage", pageState);
        this.pageState = pageState;
    }

    public void setServiceState(String serviceStage) {
        addExtraInfo("serviceStage", serviceStage);
        this.serviceStage = serviceStage;
    }

    public void report(boolean activityPop) {
        if (activityPop || isExit) {
            reportExit();
        } else {
            reportHide();
        }
    }

    /**
     * 上报退出
     */
    private void reportExit() {
        reportInner(true);
    }

    /**
     * 上报hide
     */
    private void reportHide() {
        reportInner(false);
    }


    private String chooseOp(boolean exit, boolean isWidget) {
        if (exit) {
            return isWidget ? "widgetExit" : "pageExit";
        }
        return isWidget ? "widgetHide" : "pageHide";
    }

    private Map<String, Object> getInfoMap() {
        HashMap<String, Object> result = new HashMap<>(mInvariableInfoMap);
        result.putAll(mInfoMap);
        return result;
    }

    private void addExtraInfo(String key, Object value) {
        if (value != null) {
            mInfoMap.put(key, value);
        }
    }

    private void ensureSdkVer() {
        // 基础库和业务库一个包，所以判断一个就行。
        if (mInvariableInfoMap.containsKey("publishId")) {
            return;
        }
        MSCAppModule module = mRuntime.getMSCAppModule();
        final AppMetaInfoWrapper info = module.getMetaInfo();
        if (null != info) {
            String publishId = info.getPublishId();
            String mscAppVersion = info.getVersion();
            if (!TextUtils.isEmpty(publishId)) {
                mInvariableInfoMap.put("publishId", publishId);
            }
            if (!TextUtils.isEmpty(mscAppVersion)) {
                mInvariableInfoMap.put("mscAppVersion", mscAppVersion);
            }
        }
        if (null != module.getBasePackage()) {
            mBasePkgVersion = module.getBasePackage().getVersion();
            if (!TextUtils.isEmpty(mBasePkgVersion)) {
                mInvariableInfoMap.put("basePkgVersion", mBasePkgVersion);
            }
        }
    }

    protected void reportInner(boolean exit) {
        if (!needReportExceptionDot) {
            MSCLog.i(TAG, "#reportInner,return by needReportExceptionDot");
            return;
        }
        ensureSdkVer();
        // 仅针对目标版本以上生效。
        if (VersionUtil.compare(mBasePkgVersion, TARGET_VERSION) < 0) {
            MSCLog.i(TAG, "#reportInner,return by mBasePkgVersion is lower than ", TARGET_VERSION);
            return;
        }
        // Native渲染不上报
        if (!isWebViewRenderer) {
            MSCLog.i(TAG, "#reportInner,return by native renderer");
            return;
        }

        MSCLog.i(TAG, "#reportInner, isServiceSuccess:", isServiceSuccess(), ", isPageSuccess:", isPageSuccess());
        // 正常情况不报
        if (isServiceSuccess() && isPageSuccess()) {
            MSCLog.i(TAG, "#reportInner,return by service and page state success.");
            return;
        }
        // addExtraInfo("appState", mApp.isAppInForeground() ? "foreground" : "background");
        addExtraInfo("operation", chooseOp(exit, isWidget));
        addExtraInfo("widget", String.valueOf(isWidget));
        addExtraInfo("isFirstPage", String.valueOf(isFirstPage));
        // false表示逻辑层异常, true表示渲染层异常
        addExtraInfo("onlyPageException", isServiceSuccess());
        boolean enableSetWebViewWhiteForegroundColor = MSCConfig.enableSetWebViewWhiteForegroundColor(mRuntime.getAppId());
        if (isWebViewRenderer && enableSetWebViewWhiteForegroundColor) {
            addExtraInfo("isWhiteForegroundShow", isWhiteForegroundShow);
        }
        new MSCReporter().record(ReporterFields.REPORT_PAGE_EXCEPTION_RECORD_COUNT)
                .tags(getInfoMap())
                .durationEnd(mStartTime)
                .sendDelay();
    }

    protected boolean isServiceSuccess() {
        return isWebViewRenderer && AppServiceState.APP_LAUNCH.equals(serviceStage);
    }

    protected boolean isPageSuccess() {
        return isWebViewRenderer && (AppPageState.FIRST_RENDER.equals(pageState) || AppPageState.SNAPSHOT_INTERACTIVE.equals(pageState));
    }

}
