package com.meituan.msc.modules.reporter.whitescreen;

public class ErrorRecordInfo {
    private static final String TAG = "ErrorRecordInfo";
    protected static final String ERROR_NAME_PAGE_LOAD = "pageLoad";
    protected static final String ERROR_NAME_JS = "js";

    protected String errorName;
    protected String errorMessage;
    protected int errorCode;

    public String getErrorMessage() {
        return errorMessage;
    }

    public int getErrorCode() {
        return errorCode;
    }

    /**
     * 记录PageLoadError信息
     */
    public static class PageLoadErrorRecordInfo extends ErrorRecordInfo {
        public PageLoadErrorRecordInfo(String errorMessage, int errorCode) {
            this.errorName = ERROR_NAME_PAGE_LOAD;
            this.errorMessage = errorMessage;
            this.errorCode = errorCode;
        }
    }

    /**
     * 记录JsError信息
     */
    public static class JsErrorRecordInfo extends ErrorRecordInfo {
        public JsErrorRecordInfo(String errorMessage) {
            this.errorName = ERROR_NAME_JS;
            this.errorMessage = errorMessage;
        }
    }
}
