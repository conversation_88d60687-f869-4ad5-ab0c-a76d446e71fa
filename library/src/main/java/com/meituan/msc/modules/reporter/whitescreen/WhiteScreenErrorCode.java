package com.meituan.msc.modules.reporter.whitescreen;

//白屏错误码说明：https://km.sankuai.com/collabpage/2186071269
public class WhiteScreenErrorCode {
    /**
     * Page层状态未达到最终态
     */
    public static final int REASON_WHITE_SCREEN_PAGE_STATE_ERROR = 1000;

    /**
     * Service层状态未达到最终态
     */
    public static final int REASON_WHITE_SCREEN_SERVICE_STATE_ERROR = 2000;

    /**
     * 渲染慢导致白屏
     */
    public static final int REASON_WHITE_SCREEN_RENDER_SLOW = 3000;

    /**
     * JS错误产生白屏
     */
    public static final int REASON_WHITE_SCREEN_JS_ERROR = 4000;

    /**
     * consoleLog Error产生白屏
     */
    public static final int REASON_WHITE_SCREEN_WEB_VIEW_CONSOLE_LOG_ERROR = 4005;

    /**
     * renderProcessGone产生白屏
     */
    public static final int REASON_WHITE_SCREEN_RENDER_PROCESS_GONE = 5000;

    /**
     * renderProcessGone重复发生产生白屏
     */
    public static final int REASON_WHITE_SCREEN_RENDER_PROCESS_GONE_REPEAT = 5001;

    /**
     * 页面加载异常产生白屏（包含了JS为Fatal的错误）,详细看PageLoadSuccessRateErrorCode
     */
    public static final int REASON_WHITE_SCREEN_PAGE_LOAD_ERROR = 6000;

    /**
     * 白屏原因未知
     */
    public static final int REASON_WHITE_SCREEN_UNKNOWN = 9999;
}
