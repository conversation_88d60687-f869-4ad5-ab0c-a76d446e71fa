package com.meituan.msc.modules.reporter.whitescreen;

import com.meituan.msc.modules.reporter.MSCLog;
import com.meituan.msc.modules.reporter.prexception.AppPageState;
import com.meituan.msc.modules.reporter.prexception.AppServiceState;

import java.util.HashMap;
import java.util.List;
import java.util.Map;

public class WhiteScreenReasonRecord {
    private static final String TAG = "WhiteScreenReasonRecord";

    // Service层状态
    private volatile String mServiceStage = AppServiceState.DEFAULT;

    // Page层状态
    private volatile String mPageState = AppPageState.DEFAULT;

    // 是否存在页面加载错误
    private volatile boolean mIsExistPageLoadError = false;

    // 页面加载错误错误码
    private volatile int mPageLoadSuccessRateErrorCode = 0;

    // 是否发生过WebViewConsoleLogError
    private boolean mIsExitConsoleLogError;

    // 是否发生过Js执行错误
    private boolean mIsExistJsError;

    // js错误信息
    private String mJsError;

    // WebViewConsoleLogError内容
    private String mWebViewConsoleLogError;

    // 是否发生过渲染进程崩溃
    private Map<String, Object> mRenderProcessGoneInfo;

    // 使用渲染缓存且不复用
    private boolean mUseRenderCacheNotRecycle;

    // 是否是渲染进程重复崩溃
    private boolean mIsRenderProcessGoneRepeated;

    public WhiteScreenReasonRecord(Builder builder) {
        this.mIsExistPageLoadError = builder.mIsExistPageLoadError;
        this.mPageLoadSuccessRateErrorCode = builder.mPageLoadSuccessRateErrorCode;
        this.mWebViewConsoleLogError = builder.mWebViewConsoleLogError;
        this.mJsError = builder.mJsError;
        this.mIsExistJsError = builder.mIsExitJsError;
        this.mRenderProcessGoneInfo = builder.mRenderProcessGoneInfo;
        this.mServiceStage = builder.mServiceStage;
        this.mPageState = builder.mPageState;
        this.mUseRenderCacheNotRecycle = builder.mIsUseRenderCacheAndNotRecycle;
        this.mIsRenderProcessGoneRepeated = builder.mIsRenderProcessGoneRepeated;
        if (mWebViewConsoleLogError != null && !mWebViewConsoleLogError.isEmpty()) {
            this.mIsExitConsoleLogError = true;
        }
    }

    public static class Builder {
        private boolean mIsExistPageLoadError;
        private int mPageLoadSuccessRateErrorCode;
        private String mWebViewConsoleLogError;
        private String mJsError;
        private boolean mIsExitJsError;
        private Map<String, Object> mRenderProcessGoneInfo;
        private boolean mIsRenderProcessGoneRepeated;
        private String mServiceStage;
        private String mPageState;
        private boolean mIsUseRenderCacheAndNotRecycle;

        public Builder() {

        }

        /**
         *  设置页面加载状态，如何获取这个值,这里只收集到AppPageReporter上报的错误。
         * @param info
         */
        public Builder setPageLoadStatus(ErrorRecordInfo.PageLoadErrorRecordInfo info) {
            if (info != null) {
                MSCLog.i(TAG, " mPageLoadSuccessRateErrorCode = " + info.getErrorCode());
                this.mIsExistPageLoadError= true;
                this.mPageLoadSuccessRateErrorCode = info.getErrorCode();
            } else {
                this.mIsExistPageLoadError= false;
            }
            return this;
        }

        /**
         * 设置WebView的consoleLog错误信息
         * @param webViewConsoleLogError
         */
        public Builder setWebViewConsoleLogError(String webViewConsoleLogError) {
            this.mWebViewConsoleLogError = webViewConsoleLogError;
            return this;
        }

        /**
         *  设置JavaScript错误信息,这里只收集到AppPageReporter上报的错误。
         * @param info
         */
        public Builder setJsError(ErrorRecordInfo.JsErrorRecordInfo info) {
            if (info != null) {
                MSCLog.i(TAG, " js error = " + info.getErrorMessage());
                this.mIsExitJsError = true;
                this.mJsError= info.getErrorMessage();
            } else {
                this.mIsExitJsError = false;
            }
            return this;
        }

        /**
         * 设置渲染进程崩溃错误信息
         * @param renderProcessGoneInfo
         */
        public Builder setRenderProcessGoneInfo(Map<String, Object> renderProcessGoneInfo, List<Long> renderProcessGoneTimeList) {
            this.mRenderProcessGoneInfo = renderProcessGoneInfo;
            this.mIsRenderProcessGoneRepeated = isRenderProcessGoneRepeated(renderProcessGoneTimeList);
            return this;
        }

        /**
         * 设置逻辑层状态
         *
         * @param serviceStage
         */
        public Builder setServiceStage(String serviceStage) {
            this.mServiceStage = serviceStage;
            return this;
        }

        /**
         * 设置渲染层状态
         *
         * @param pageState
         */
        public Builder setPageState(String pageState) {
            this.mPageState = pageState;
            return this;
        }

        /**
         * 是否使用渲染缓存&&!回收复用
         * @param useRenderCacheAndNotRecycle
         * @return
         */
        public Builder setIsUseRenderCacheAndNotRecycle(boolean useRenderCacheAndNotRecycle) {
            MSCLog.i(TAG, "useRenderCacheAndNotRecycle = " + useRenderCacheAndNotRecycle);
            this.mIsUseRenderCacheAndNotRecycle = useRenderCacheAndNotRecycle;
            return this;
        }

        public WhiteScreenReasonRecord build() {
            return new WhiteScreenReasonRecord(this);
        }

        public boolean isRenderProcessGoneRepeated(List<Long> renderProcessGoneTimeList) {
            if (renderProcessGoneTimeList != null && !renderProcessGoneTimeList.isEmpty()) {
                long currentTime = System.currentTimeMillis();
                int count = 0;
                for (Long time : renderProcessGoneTimeList) {
                    if (currentTime - time <= 10000) {
                        count++;
                        if (count >= 2) {
                            return true;
                        }
                    }
                }
            }
            return false;
        }
    }

    /**
     * 白屏上报维度
     * @return 返回白屏上报维度
     */
    public Map<String, Object> getWhiteScreenReportMap() {
        Map<String, Object> tags = new HashMap<>();
        tags.put("whiteScreenReasonErrorCode", getWhiteScreenReasonErrorCode());
        tags.put("pageState", mPageState);
        tags.put("serviceState", mServiceStage);
        tags.put("pageLoadSuccessRateErrorCode", mPageLoadSuccessRateErrorCode);
        tags.put("webViewLogError", mWebViewConsoleLogError);
        tags.put("jsErrorMessage", mJsError);
        return tags;
    }

//        白屏原因归类：
//        1、收到FP之前发生过任何错误，均会导致白屏；难点如果在白屏上报是统计到所有错误呢？
//        错误包括：页面加载失败错误码、包下载错误
//        2、未收到FP也没有发生错误，可能是事件丢失？导致白屏。只能针对特殊的case进行监控，比如未发送onPageStart
//        3、其他的归类为加载慢。
//        4、收到FP发生错误（Page、Service的JS错误、renderProcessGone）
//        5、收到FP，没有收到FFP、FMP等，可认为是渲染慢。

    /**
     * 通过页面加载过程各模块状态确定白屏原因
     * todo 评估耗时影响,白屏上报是在主线程进行,因此需要评估对性能影响
     * @return
     */
    private int getWhiteScreenReasonErrorCode() {
        if (mIsExistPageLoadError) {
            return WhiteScreenErrorCode.REASON_WHITE_SCREEN_PAGE_LOAD_ERROR;
        } else if (mIsExistJsError) {
            return WhiteScreenErrorCode.REASON_WHITE_SCREEN_JS_ERROR;
        } else if (mIsExitConsoleLogError) {
            return WhiteScreenErrorCode.REASON_WHITE_SCREEN_WEB_VIEW_CONSOLE_LOG_ERROR;
        } else if (mRenderProcessGoneInfo != null) {
            return mIsRenderProcessGoneRepeated ? WhiteScreenErrorCode.REASON_WHITE_SCREEN_RENDER_PROCESS_GONE_REPEAT : WhiteScreenErrorCode.REASON_WHITE_SCREEN_RENDER_PROCESS_GONE;
        } else if (isServiceSuccess() && isPageSuccess()) {
            return WhiteScreenErrorCode.REASON_WHITE_SCREEN_UNKNOWN;
        } else if (!isServiceSuccess() && !isPageSuccess()){
            return WhiteScreenErrorCode.REASON_WHITE_SCREEN_RENDER_SLOW;
        } else if (isServiceSuccess()) {
            return WhiteScreenErrorCode.REASON_WHITE_SCREEN_PAGE_STATE_ERROR;
        } else {
            return WhiteScreenErrorCode.REASON_WHITE_SCREEN_SERVICE_STATE_ERROR;
        }
    }

    /**
     * 逻辑层是否成功
     * @return
     */
    private boolean isServiceSuccess() {
        return AppServiceState.APP_LAUNCH.equals(mServiceStage);
    }

    /**
     * 视图层是否状态流转成功
     * @return
     */
    private boolean isPageSuccess() {
        if (mUseRenderCacheNotRecycle) {
            return AppPageState.SNAPSHOT_INTERACTIVE.equals(mPageState);
        } else {
            return AppPageState.FIRST_RENDER.equals(mPageState);
        }
    }
}
