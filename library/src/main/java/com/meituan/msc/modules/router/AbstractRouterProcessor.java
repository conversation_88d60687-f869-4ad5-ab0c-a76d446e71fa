package com.meituan.msc.modules.router;

import android.content.Context;
import android.content.Intent;
import android.net.Uri;
import android.support.annotation.Nullable;

import com.meituan.android.common.horn.Horn;
import com.meituan.android.common.horn.HornCallback;
import com.meituan.msc.modules.reporter.MSCLog;

/**
 * 路由拦截处理器
 * Created by letty on 2022/9/15.
 **/
public abstract class AbstractRouterProcessor {
    Uri mUri;

    AbstractRouterProcessor(Uri uri) {
        this.mUri = uri;
    }

    Uri getUri() {
        return mUri;
    }

    boolean isEnable() {
        return true;
    }

    abstract boolean processIntent(Context context, Uri uri, Intent originalIntent, boolean isStartActivity);

    protected boolean isUriMatched(Uri uri){
        return MSCRouterInstrumentation.matchWithoutQuery(uri, getUri());
    }

    /**
     * 注册Horn配置
     *
     * @param hornConfig
     */
    protected void registerHorn(String hornConfig) {
        String hornCache = Horn.accessCache(hornConfig);
        MSCLog.i("AbstractRouterProcessor", "registerHorn:", hornConfig, ", horn cache:", hornCache);
        processConfig(hornCache);
        Horn.register(hornConfig, new HornCallback() {
            @Override
            public void onChanged(boolean enable, String result) {
                if (enable) {
                    processConfig(result);
                } else {
                    processConfig(null);
                }
            }
        });
    }

    /**
     * 处理Horn配置信息
     *
     * @param result
     */
    protected void processConfig(@Nullable String result) {

    }

}
