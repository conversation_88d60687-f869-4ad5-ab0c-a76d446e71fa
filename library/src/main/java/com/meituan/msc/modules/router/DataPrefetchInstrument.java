package com.meituan.msc.modules.router;

import android.content.Context;
import android.content.Intent;

import com.meituan.msc.common.utils.IntentUtil;
import com.meituan.msc.modules.container.MSCIntentInstrumentation;
import com.meituan.msc.modules.engine.MSCHornRollbackConfig;
import com.meituan.msc.modules.engine.requestPrefetch.DataPrefetchManager;

/**
 * 数据预拉取路由
 */
public class DataPrefetchInstrument extends MSCIntentInstrumentation {
    public DataPrefetchInstrument(Context context) {
        super(context);
    }

    @Override
    public boolean processMSCIntent(Context context, Intent originalIntent, boolean isStartActivity) {
        if (isStartActivity) {
            if (MSCHornRollbackConfig.enableExternalAppPrefSourceLimit()) {
                boolean isExternalApp = IntentUtil.getBooleanExtra(originalIntent, "externalApp", false);
                if (isExternalApp) {
                    return true;
                }
            }
            DataPrefetchManager.prefetchDataIfNeededInMSCInstrumentation(null, originalIntent);
        }
        return true;
    }
}
