package com.meituan.msc.modules.router;

import android.annotation.SuppressLint;
import android.content.ComponentName;
import android.content.Context;
import android.content.Intent;
import android.net.Uri;
import android.text.TextUtils;

import com.meituan.android.common.horn.Horn;
import com.meituan.android.common.horn.HornCallback;
import com.meituan.msc.common.executor.MSCExecutors;
import com.meituan.msc.modules.container.MSCActivity;

/**
 * MSC路由管理类
 * <p>
 * MMP 跳转小程序
 * 文档：https://km.sankuai.com/page/316180291
 * 配置：https://horn.sankuai.com/files/edit/1447
 * <p>
 * Created by letty on 2020-05-26.
 **/
public class KNBRouterManager {

    private static final String MSC_ROUTER_HORN_CONFIG_NAME = "msc_h5_url_rewrite";
    private static RouterTrie mRouterTrie;
    @SuppressLint("StaticFieldLeak")
    private static Context mContext;

    public static void init(Context context) {
        mContext = context.getApplicationContext();

        processConfig(Horn.accessCache(MSC_ROUTER_HORN_CONFIG_NAME));
        Horn.register(MSC_ROUTER_HORN_CONFIG_NAME, new HornCallback() {
            @Override
            public void onChanged(boolean enable, String result) {
                if (enable) {
                    processConfig(result);
                }
            }
        });
    }

    static boolean isEnable(){
        return mRouterTrie != null;
    }

    private static void processConfig(String config) {
//        long start = SystemClock.elapsedRealtime();
        final RouterTrie routerTrie = TextUtils.isEmpty(config) ? null : RouterTrie.processConfig(config);
        MSCExecutors.runOnUiThread(new Runnable() {
            @Override
            public void run() {
                //只在主线程修改
                mRouterTrie = routerTrie;
            }
        });
//        System.out.println("processConfig total cost" + (SystemClock.elapsedRealtime() - start));
    }

    public static boolean processIntent(Context context, Intent originalIntent) {
        if (mRouterTrie == null ) {
            return false;
        }

        Uri uri = originalIntent.getData();
        if (!uri.isHierarchical()) {
            return false;
        }
        // 当前一期仅支持web页面拦截
        // imeituan://www.meituan.com/web?url=...
        // imeituan://web?url=...

        String url = uri.getQueryParameter("url");
        if (TextUtils.isEmpty(url)) {
            return false;
        }

        RouterTrie.MPConfig mpConfig = mRouterTrie.match(Uri.parse(url));
        if (mpConfig != null) {
            originalIntent.setData(
                    MSCInstrumentation.getMscUri()
                            .buildUpon()
                            .appendQueryParameter("appId", mpConfig.appId)
                            .appendQueryParameter("targetPath",
                                    Uri.parse(mpConfig.pagePath).buildUpon().appendQueryParameter("q", url).build().toString())
                            .build());
            originalIntent.setComponent(new ComponentName(mContext, MSCActivity.class));
            originalIntent.setPackage(context.getPackageName());
//                            System.out.println("total cost" + (SystemClock.elapsedRealtime() - start));
            return true;
        }
        return false;
    }

}
