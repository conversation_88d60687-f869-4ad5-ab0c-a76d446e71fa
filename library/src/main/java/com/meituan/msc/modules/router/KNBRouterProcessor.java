package com.meituan.msc.modules.router;

import android.content.Context;
import android.content.Intent;
import android.net.Uri;


/**
 * KNB 跳转小程序
 * 文档：https://km.sankuai.com/page/316180291
 * 配置：https://horn.sankuai.com/files/edit/1447
 * <p>
 * Created by letty on 2020-05-26.
 **/
public class KNBRouterProcessor extends AbstractRouterProcessor {

    /**
     * @param context 全局对象
     * @api 组件标准化注释_标准API
     * MMP跳转拦截器
     */
    public KNBRouterProcessor(Context context, Uri uri) {
        super(uri);
        KNBRouterManager.init(context);
    }

    @Override
    public boolean isEnable() {
        return KNBRouterManager.isEnable();
    }

    /**
     * 美团中历史支持 imeituan://web ，knb 不对外提供该协议，临时放在这后续确认不需要可以删除
     */
    public boolean isKNBPath(Uri uri) {
        String scheme = uri.getScheme();
        String path = uri.getPath();
        String host = uri.getHost();

        // 仅支持web页面拦截
        // imeituan://www.meituan.com/web?...
        // imeituan://web?...

        if ("imeituan".equalsIgnoreCase(scheme) && ("web".equals(host))) {
            return true;
        }
        return false;
    }

    @Override
    protected boolean isUriMatched(Uri uri) {
        return super.isUriMatched(uri)
                || (MSCInstrumentation.isMeituan() && isKNBPath(uri));
    }

    @Override
    public boolean processIntent(Context context, Uri uri, Intent originalIntent, boolean isStartActivity) {
        return KNBRouterManager.processIntent(context, originalIntent);
    }
}
