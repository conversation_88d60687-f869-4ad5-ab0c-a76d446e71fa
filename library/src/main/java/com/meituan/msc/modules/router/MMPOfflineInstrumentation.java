package com.meituan.msc.modules.router;

import android.content.Context;
import android.content.Intent;
import android.net.Uri;
import android.text.TextUtils;

import com.meituan.msc.common.config.MSCConfig;
import com.meituan.msc.common.process.MSCProcess;
import com.meituan.msc.common.utils.IntentUtil;
import com.meituan.msc.extern.MSCEnvHelper;
import com.meituan.msc.modules.container.IntentInstrumentation;
import com.meituan.msc.modules.reporter.MSCLog;
import com.meituan.msc.modules.reporter.MSCReporter;
import com.meituan.msc.modules.reporter.ReporterFields;

import java.util.HashMap;
import java.util.Map;


public class MMPOfflineInstrumentation extends IntentInstrumentation {

    private static final String TAG = "MMPOfflineInstrumentation";

    private volatile static Map<String, String> offlineConfig = new HashMap<>();

    private static final String FALLBACK_APP_ID = "a48a966cba094c58";

    public static void addOfflineRouterConfig(String mmpId, String mmpName) {
        offlineConfig.put(mmpId, mmpName);
    }

    public MMPOfflineInstrumentation(Context context) {
        super(context);
    }


    @Override
    public boolean processIntent(Context context, Intent originalIntent, boolean isStartActivity) {
        // newActivity 阶段拦截只在MSC 可运行的进程中拦截
        if (!isStartActivity) {
            if (!(MSCProcess.isInMainProcess() || MSCProcess.STANDARD.isCurrentProcess())) {
                return false;
            }
        }
        Uri uri = originalIntent.getData();
        if (uri == null || !uri.isHierarchical()) {
            return false;
        }

        boolean processed = processIntent(context, uri, originalIntent, isStartActivity);
        if (processed) {
            return true;
        }

        return false;
    }

    public static boolean processIntent(Context context, Uri uri, Intent originalIntent, boolean isStartActivity) {
        if (MSCConfig.getRollbackRouteBackup()) {
            MSCLog.w(TAG, "need to rollback route backup");
            return false;
        }
        if (!TextUtils.equals(uri.getPath(), "/mmp")) {
            return false;
        }
        String appId = IntentUtil.getStringExtra(originalIntent, "appId");
        // mmp 小程序是否已线下
        boolean isOffline = false;
        String appName = "";

        Map<String, String> hornOfflineConfig = MSCConfig.getShowOfflineTipAppIds();
        if (!MMPRouterManager.isMMPNeedRouteToMSC(appId) && MSCEnvHelper.isMMPOffline()) {
            isOffline = true;
        }
        if (hornOfflineConfig != null && !hornOfflineConfig.isEmpty()) {
            //有horn配置
            if (hornOfflineConfig.containsKey(appId)) {
                isOffline = true;
                appName = hornOfflineConfig.get(appId);
            }
        } else if (!offlineConfig.isEmpty()) {
            if (offlineConfig.containsKey(appId)) {
                isOffline = true;
                appName = offlineConfig.get(appId);
            }
        }

        // mmp小程序未下线
        if (!isOffline) {
            return false;
        }

        String scheme = Uri.parse(uri.toString()).getScheme();
        Uri processedUri = new Uri.Builder().scheme(scheme)
                .authority("www.meituan.com")
                .path("mmpoffline")
                .appendQueryParameter("appId", appId)
                .appendQueryParameter("mmpAppName", appName)
                .appendQueryParameter("targetPath",
                        new Uri.Builder().path("/pages/index/index").appendQueryParameter("mmpAppId", appId)
                                .appendQueryParameter("mmpAppName", appName).toString())
                .appendQueryParameter("routeFromMMP", "true")
                .build();
        originalIntent.setPackage(context.getPackageName());
        originalIntent.setData(processedUri);
        // todo query
        // todo maybe modify target Activity

//        originalIntent.putExtra(MSCParams.APP_ID, targetAppId);

        new MSCReporter().record(ReporterFields.MSC_OFFLINE_MMP_LAUNCH_COUNT)
                .tag("mmpAppId", appId)
                .tag("mmpUrl", processedUri.toString())
                .tag("originUrl", uri.toString())
                .tag("originPath", IntentUtil.getStringExtra(originalIntent, "targetPath"))
                .sendDelay();

        MSCLog.i(TAG, "originUri:", uri.toString(),
                "newUri:", originalIntent.getData().toString());
        return true;
    }
}
