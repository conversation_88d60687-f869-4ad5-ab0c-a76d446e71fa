package com.meituan.msc.modules.router;

import android.content.ComponentName;
import android.content.Context;
import android.content.Intent;
import android.net.Uri;
import android.support.annotation.Nullable;
import android.text.TextUtils;

import com.meituan.msc.common.utils.MPConcurrentHashMap;
import com.meituan.msc.lib.interfaces.container.MSCParams;
import com.meituan.msc.modules.container.MSCActivity;
import com.meituan.msc.modules.engine.MSCHornRollbackConfig;
import com.meituan.msc.modules.reporter.MSCLog;

import org.json.JSONException;
import org.json.JSONObject;

import java.util.HashMap;
import java.util.Iterator;
import java.util.Map;
import java.util.Set;
import java.util.concurrent.CopyOnWriteArraySet;


/**
 * Created by letty on 2022/9/14.
 **/
public class MMPRouterManager {

    private static final String TAG = "MMPRouterManager";
    //current support only appId mapping <mmpAppId,<mscAppId, appLifeCyclePersist>>
    private volatile static Map<String, RouteConfig> mConfig = new HashMap<>();
    //structure <mmpAppId,<mscAppId, canRoute>>
    private static final Map<String, CacheRouteConfig> mCachedConfigs = new MPConcurrentHashMap<>();
    //navigateToMiniProgram API 支持宿主直接配置MSC跳转MMP白名单
    private static final Set<String> mMSCNavigateToMMPWhiteList = new CopyOnWriteArraySet<>();
    // APP生命周期内首次调用默认是使用缓存
    private volatile static boolean isCache = true;
    /**
     * 确保mConfig中存在指定AppId对应的Config，解决Horn可能无配置的问题
     */
    private static final Map<String, RouteConfig> mRequiredConfigs = new HashMap<>();

    public static Map<String, RouteConfig> getConfig() {
        return mConfig;
    }


    public static void addRequiredAndRouterConfig(String mmpId, String mscId) {
        addRequiredAndRouterConfig(mmpId,mscId,true);
    }

    public static void addRequiredAndRouterConfig(String mmpId, String mscId, boolean appLifeCyclePersist) {
        mRequiredConfigs.put(mmpId, new RouteConfig(mscId, appLifeCyclePersist));
        mConfig.put(mmpId, new RouteConfig(mscId, appLifeCyclePersist));
    }

    private static void removeRequiredAndRouterConfig(String mmpId){
        mRequiredConfigs.remove(mmpId);
        mConfig.remove(mmpId);
    }

    public static void addRouterConfig(String mmpId, String mscId) {
        mConfig.put(mmpId, new RouteConfig(mscId, false));
    }

    public static void addRouterConfig(String mmpId, String mscId, boolean appLifeCyclePersist) {
        mConfig.put(mmpId, new RouteConfig(mscId, appLifeCyclePersist));
    }

    // 供业务方调用
    public static void addMSCNavigateToMMPWhiteList(String mmpAppId) {
        mMSCNavigateToMMPWhiteList.add(mmpAppId);
    }
    // 内部逻辑使用
    public static boolean isInMSCNavigateToMMPWhiteList(String mmpAppId) {
        return mMSCNavigateToMMPWhiteList.contains(mmpAppId);
    }

    public static boolean isEnable() {
        return mConfig != null;
    }

    public static boolean isMMPNeedRouteToMSC(String mmpAppId) {
        return isMMPNeedRouteToMSC(null, mmpAppId);
    }

    public static boolean isMMPNeedRouteToMSC(Context context, String mmpAppId) {
        return canRouteFromCacheConfig(mmpAppId);
    }

    /**
     * 如果配置中存在 mscAppId 对应配置, 直接删除
     * @param mscAppId  mscAppId
     */
    public static void rollbackConfig(String mscAppId) {
        if (TextUtils.isEmpty(mscAppId)) {
            return;
        }
        Iterator<Map.Entry<String, RouteConfig>> iterator = mConfig.entrySet().iterator();
        while (iterator.hasNext()) {
            Map.Entry<String, RouteConfig> entry = iterator.next();
            if (mscAppId.equals(entry.getValue().mscAppId)) {
                iterator.remove();
            }
        }
    }

    public static String getMSCAppIdRouteByMMP(String mmpAppId) {
        RouteConfig mscConfig = mConfig.get(mmpAppId);
        return mscConfig != null ? mscConfig.mscAppId : null;
    }

    @Nullable
    public static String getMMPAppIdByMSC(String mscAppId) {
        Set<Map.Entry<String, RouteConfig>> entries = mConfig.entrySet();
        for (Map.Entry<String, RouteConfig> entry : entries) {
            if (entry == null || entry.getValue() == null) {
                continue;
            }
            String appId = entry.getValue().mscAppId;
            if (TextUtils.equals(appId, mscAppId)) {
                return entry.getKey();
            }
        }
        return null;
    }

    /**
     * 处理 MMP/MSC 路由映射 Horn 配置
     * 需求文档: https://km.sankuai.com/collabpage/1837032537
     * 技术方案: https://km.sankuai.com/collabpage/1839408993
     * @param config    Horn配置
     */
    public static void processConfig(String config) {
        Map<String, RouteConfig> newConfig = processConfigInner(config);
        if (newConfig == null) {
            MSCLog.w(TAG, "processConfig newConfig == null, config:", config);
            return;
        }
        if (!MSCHornRollbackConfig.isRollBackForceRouteMapping()) {
            for (String appId : mRequiredConfigs.keySet()) {
                if (!newConfig.containsKey(appId) && mRequiredConfigs.get(appId) != null) {
                    MSCLog.i(TAG, appId, "not exits in newConfig, add routerConfig");
                    newConfig.put(appId, mRequiredConfigs.get(appId));
                }
            }
            MSCLog.i(TAG, "mRequiredConfigs inited");
        }
        MSCLog.i(TAG, "process before mCache:", getConfigContent());
        synchronized (MMPRouterManager.class) {
            // true: 直接全量更新Config; false: 需要合并新旧数据
            if (isCache) {
                MSCLog.i(TAG, "app lifecycle first assignment, use horn cache, config:", config);
                mConfig = newConfig;
                isCache = false;
            } else {
                MSCLog.i(TAG, "not app lifecycle first assignment, merge memory cache and net config, net config:", config);
                Map<String, RouteConfig> oldConfig = new HashMap<>(mConfig);
                Map<String, RouteConfig> finalConfig = new HashMap<>();
                while (!oldConfig.isEmpty() || !newConfig.isEmpty()) {
                    // 先处理旧数据集合, 再处理新数据集合
                    mergeOldConfig(finalConfig, newConfig, oldConfig.entrySet().iterator());
                    mergeNewConfig(finalConfig, oldConfig, newConfig.entrySet().iterator());
                }
                mConfig = finalConfig;
            }
        }

        MSCLog.i(TAG, "process after mCache:", getConfigContent());
    }

    private static String getConfigContent() {
        StringBuilder stringBuilder = new StringBuilder();
        for (Map.Entry<String, RouteConfig> entry : mConfig.entrySet()) {
            stringBuilder.append("{appId:").append(entry.getKey())
                    .append(", targetId:").append(entry.getValue().mscAppId)
                    .append(", appLifeCyclePersist:").append(entry.getValue().appLifeCyclePersist)
                    .append("}");
        }
        return stringBuilder.toString();
    }

    /**
     * 处理旧数据集合
     * @param finalConfig   最终结果集
     * @param newConfig     新数据集
     * @param oldIterator   旧数据集
     */
    private static void mergeOldConfig(Map<String, RouteConfig> finalConfig,
                                       Map<String, RouteConfig> newConfig,
                                       Iterator<Map.Entry<String, RouteConfig>> oldIterator) {
        if (!oldIterator.hasNext()) {
            return;
        }
        Map.Entry<String, RouteConfig> oldEntry = oldIterator.next();
        String oldKey = oldEntry.getKey();
        RouteConfig oldValue = oldEntry.getValue();
        RouteConfig newValue = newConfig.get(oldKey);
        if (oldValue.appLifeCyclePersist) {
            // 处理业务方配置了强制回滚的逻辑, 仅处理降级, 即旧配置有, 新配置没有
            if (MMPRouterRollbackManager.isAppConfigRollback(oldKey) && newValue == null) {
                boolean immediatelyChange = MMPRouterRollbackManager.changeConfigByEngineState(oldValue.mscAppId);
                if (immediatelyChange) {
                    oldIterator.remove();
                    return;
                }
            }
            // 缓存中要求保持状态, 使用缓存数据
            finalConfig.put(oldKey, oldValue);
            newConfig.remove(oldKey);
        } else {
            if (newValue != null) {
                if (newValue.appLifeCyclePersist) {
                    // 缓存中不要求保持状态, 新数据要求, 此时使用缓存数据
                    finalConfig.put(oldKey, oldValue);
                } else {
                    // 缓存中不要求保持状态, 新数据也不要求, 此时使用新数据
                    finalConfig.put(oldKey, newValue);
                }
                newConfig.remove(oldKey);
            } else {
                // 修复Horn旧版本缓存缺少内置配置项，导致内置配置丢失问题
                if (!MSCHornRollbackConfig.readConfig().rollbackRouteConfigFix) {
                    finalConfig.put(oldKey, oldValue);
                }
            }
        }
        oldIterator.remove();
    }

    /**
     * 处理新数据集合
     * @param finalConfig   最终结果集
     * @param oldConfig     旧数据集
     * @param newIterator   新数据集
     */
    private static void mergeNewConfig(Map<String, RouteConfig> finalConfig,
                                       Map<String, RouteConfig> oldConfig,
                                       Iterator<Map.Entry<String, RouteConfig>> newIterator) {
        if (!newIterator.hasNext()) {
            return;
        }
        Map.Entry<String, RouteConfig> newEntry = newIterator.next();
        RouteConfig oldValue = oldConfig.get(newEntry.getKey());
        if (oldValue != null) {
            // 跳过, 放到旧数据处理逻辑中处理
            return;
        }
        if (!newEntry.getValue().appLifeCyclePersist) {
            // 缓存中没有该条配置, 新数据有, 且不要求保持状态, 使用新数据
            finalConfig.put(newEntry.getKey(), newEntry.getValue());
        }
        newIterator.remove();
    }

    private static Map<String, RouteConfig> processConfigInner(String config) {
        if (TextUtils.isEmpty(config)) {
            return null;
        }
        Map<String, RouteConfig> map = null;
        try {
//            mConfig = new Gson().fromJson(config, new TypeToken<Map<String, String>>() {}.getType());
            JSONObject jsonObject = new JSONObject(config);
            if (jsonObject.length() > 0) {
                map = new HashMap<>();
                Iterator<String> keys = jsonObject.keys();
                while (keys.hasNext()) {
                    String key = keys.next();
                    JSONObject value = jsonObject.optJSONObject(key);
                    if (value == null) {
                        continue;
                    }
                    String appId = value.optString("appId");
                    if (TextUtils.isEmpty(appId)) {
                        continue;
                    }
                    boolean appLifeCyclePersist = value.optBoolean("appLifeCyclePersist", false);
                    map.put(key, new RouteConfig(appId, appLifeCyclePersist));
                }
            }
        } catch (JSONException e) {
            MSCLog.e("MSCMPRouterManager processConfig error", e);
        }
        return map;
    }

    public static boolean processIntent(Context context, Uri uri, Intent originalIntent, boolean isStartActivity) {
        String appId = uri.getQueryParameter("appId");
        String targetAppId = getMscAppIdFromCacheConfig(appId);
        MSCLog.i("MMPRouterManager@processIntent", appId, targetAppId);
        if (TextUtils.isEmpty(targetAppId)) {
            MSCLog.w(TAG, "targetAppId is empty. appId:", appId);
            return false;
        }
        final Uri processedUri = Uri.parse(uri.toString().replace("appId="+appId, "appId="+targetAppId));
        originalIntent.setPackage(context.getPackageName());
        if (MSCInstrumentation.getMscUri() != null) {
            originalIntent.setData(
                    MSCInstrumentation.getMscUri()
                            .buildUpon()
                            .encodedQuery(processedUri.getEncodedQuery())
                            .appendQueryParameter("routeFromMMP", "true").build());
        } else {
            //change mmp path into msc path
            originalIntent.setData(
                    processedUri.buildUpon()
                            .path("msc")
                            .appendQueryParameter("routeFromMMP", "true").build());

        }
        // todo query
        // todo maybe modify target Activity

        originalIntent.putExtra(MSCParams.APP_ID, targetAppId);
        // 处理多进程路由协议转换
        boolean processIntent = MultiProcessRouterHelper.processIntent(context, originalIntent, isStartActivity);
        if (!processIntent) {
            // 处理半屏弹窗样式路由协议转换
            processIntent = MSCTransparentRouterHelper.processIntent(context, originalIntent, isStartActivity);
        }
        // 前面已经设置了msc支持的data格式，这里不需要再加component了。
        if (!processIntent) {
            originalIntent.setComponent(new ComponentName(context, MSCActivity.class));
        }
        MSCLog.i(TAG, "originUri:", uri.toString(),
                "newUri:", originalIntent.getData().toString(),
                "processIntent:", processIntent);
        return true;
    }

    public static class RouteConfig {
        public String mscAppId = "";
        public boolean appLifeCyclePersist = false;

        public RouteConfig(String mscAppId, boolean persist) {
            this.mscAppId = mscAppId;
            this.appLifeCyclePersist = persist;
        }
    }

    public static class CacheRouteConfig {
        public String mscAppId = "";
        public boolean canRoute = false;

        public CacheRouteConfig(String mscAppId, boolean canRoute) {
            this.canRoute = canRoute;
            this.mscAppId = mscAppId;
        }
    }

    /**
     * 为确保声明周期内只读取一次配置, 缓存读取结果, 中转一次路由结果
     * <a href="https://km.sankuai.com/collabpage/2703680427">Mola App 首次进入openLink需确保mmp/msc切换一致性</a>
     * @param mmpAppId key值
     * @return 路由映射到的mscAppId，如果没有配置，或者没有获取到配置，会返回mscAppId为空
     */
    public static String getMscAppIdFromCacheConfig(String mmpAppId) {
        CacheRouteConfig cachedConfig = getOrUpdateCacheConfig(mmpAppId);
        return cachedConfig.mscAppId;
    }

    /**
     * 当前是否获取到路由配置
     * @param mmpAppId key值
     * @return 是否获取到路由配置
     */
    public static boolean canRouteFromCacheConfig(String mmpAppId) {
        CacheRouteConfig cachedConfig = getOrUpdateCacheConfig(mmpAppId);
        return cachedConfig.canRoute;
    }

    private static CacheRouteConfig getOrUpdateCacheConfig(String mmpAppId) {
        CacheRouteConfig cachedConfig = mCachedConfigs.get(mmpAppId);
        if (cachedConfig == null) {
            boolean canRoute = mConfig.containsKey(mmpAppId);
            RouteConfig routeConfig = canRoute ? mConfig.get(mmpAppId) : null;
            String mscAppId = (routeConfig != null) ? routeConfig.mscAppId : "";
            mCachedConfigs.put(mmpAppId, new CacheRouteConfig(mscAppId, canRoute));
            cachedConfig = mCachedConfigs.get(mmpAppId);
        }
        return cachedConfig;
    }
}
