package com.meituan.msc.modules.router;

import android.content.Context;
import android.content.Intent;
import android.net.Uri;
import android.support.annotation.Nullable;

/**
 * 拦截 mmp 跳转至 msc
 * https://km.sankuai.com/collabpage/**********
 * Created by letty on 2022/9/14.
 **/
class MMPRouterProcessor extends AbstractRouterProcessor {
    public static String MSC_MMP_ROUTER_CONFIG = "msc_mmp_routing_converter_config";

    public MMPRouterProcessor(Context context, Uri uri, boolean enableRouterConfig) {
        super(uri);
        if (enableRouterConfig) {
            registerHorn(MSC_MMP_ROUTER_CONFIG);
        }
    }

    @Override
    protected void processConfig(@Nullable String result) {
        MMPRouterManager.processConfig(result);
    }

    @Override
    public boolean isEnable() {
        return MMPRouterManager.isEnable();
    }

    @Override
    public boolean processIntent(Context context, Uri uri, Intent originalIntent, boolean isStartActivity) {
        return MMPRouterManager.processIntent(context, uri, originalIntent, isStartActivity);
    }
}
