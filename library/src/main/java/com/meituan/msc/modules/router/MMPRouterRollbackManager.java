package com.meituan.msc.modules.router;

import android.text.TextUtils;

import com.meituan.msc.modules.engine.MSCRuntime;
import com.meituan.msc.modules.engine.RuntimeDestroyReason;
import com.meituan.msc.modules.engine.RuntimeManager;
import com.meituan.msc.modules.reporter.MSCLog;

import org.json.JSONException;
import org.json.JSONObject;

import java.util.HashMap;
import java.util.Iterator;
import java.util.Map;
import java.util.concurrent.CopyOnWriteArraySet;

public class MMPRouterRollbackManager {

	private volatile static CopyOnWriteArraySet<String> delayedRollbackAppList = new CopyOnWriteArraySet<>();
	private volatile static Map<String, Boolean> rollbackAppConfigList = new HashMap<>();

	/**
	 * 如果当前小程序引擎正在保活态 / 预热态，杀掉预热引擎, 直接更新映射配置 return true
	 * 如果当前小程序引擎正在前台运行, 当该小程序退出的时候, 不进入保活态, 并且更新路由映射配置
	 * @param mscAppId	mscAppId
	 * @return			true: 立即更新配置	false: 延迟更新配置
	 */
	public static boolean changeConfigByEngineState(String mscAppId) {
		MSCRuntime runtime = RuntimeManager.getRuntimeWithAppId(mscAppId);
		if (runtime == null) {
			return true;
		}
		// 当前小程序处于预热态/保活态, 杀掉引擎, 更新配置
		if (runtime.isBizPreloadAndNoLaunched() || RuntimeManager.getKeepAliveAppWithAppId(mscAppId) != null) {
			RuntimeManager.destroyRuntime(runtime, RuntimeDestroyReason.MMP_ROUTER_ROLLBACK);
			return true;
		}
		// 当前小程序正在前台运行, 保持现状; 等小程序退出时, 不进入保活, 同时更新映射配置
		if (runtime.hasContainerAttached()) {
			delayedRollbackAppList.add(mscAppId);
			return false;
		}
		return false;
	}

	/**
	 * 是否需要在小程序退出时回滚 MMP/MSC 路由映射配置
	 * @param mscAppId	mscAppId
	 * @return			true: 需要回滚
	 */
	public static boolean needRollback(String mscAppId) {
		if (TextUtils.isEmpty(mscAppId)) {
			return false;
		}
		if (delayedRollbackAppList.contains(mscAppId)) {
			MMPRouterManager.rollbackConfig(mscAppId);
			delayedRollbackAppList.remove(mscAppId);
			return true;
		}
		return false;
	}

	public static boolean isAppConfigRollback(String mmpAppId) {
		return rollbackAppConfigList.containsKey(mmpAppId) && rollbackAppConfigList.get(mmpAppId);
	}

	public static void processConfig(String config) {
		if (TextUtils.isEmpty(config)) {
			return;
		}
		try {
			JSONObject jsonObject = new JSONObject(config);
			if (jsonObject.length() <= 0) {
				rollbackAppConfigList.clear();
				return;
			}
			Iterator<String> keys = jsonObject.keys();
			while (keys.hasNext()) {
				String mmpAppId = keys.next();
				boolean value = jsonObject.optBoolean(mmpAppId, false);
				rollbackAppConfigList.put(mmpAppId, value);
			}
		} catch (JSONException e) {
			MSCLog.e("MMPRouterRollbackManager processConfig error", e);
		}
	}
}
