package com.meituan.msc.modules.router;

import android.content.Context;
import android.content.Intent;
import android.net.Uri;
import android.support.annotation.Nullable;

public class MMPRouterRollbackProcessor extends AbstractRouterProcessor {

	private final static String MSC_MMP_ROUTER_ROLLBACK_CONFIG = "msc_mmp_route_allow_rollback_list";

	MMPRouterRollbackProcessor(Context context, Uri uri) {
		super(uri);
		registerHorn(MSC_MMP_ROUTER_ROLLBACK_CONFIG);
	}

	@Override
	protected void processConfig(@Nullable String result) {
		MMPRouterRollbackManager.processConfig(result);
	}

	@Override
	boolean processIntent(Context context, Uri uri, Intent originalIntent, boolean isStartActivity) {
		return false;
	}
}
