package com.meituan.msc.modules.router;

import android.app.Activity;
import android.content.Context;
import android.content.Intent;
import android.net.Uri;
import android.os.Bundle;
import android.text.TextUtils;

import com.meituan.msc.modules.container.IntentInstrumentation;
import com.meituan.msc.modules.container.MSCIntentInstrumentation;
import com.meituan.msc.modules.container.fusion.MSCFusionInstrumentation;
import com.meituan.msc.modules.engine.MSCHornRollbackConfig;
import com.meituan.msc.modules.manager.MSCRuntimeException;

import java.util.ArrayList;
import java.util.List;

/**
 * MSC 路由相关能力提供
 * 请使用 Builder 构建
 * Created by letty on 2022/9/14.
 **/
public class MSCInstrumentation extends IntentInstrumentation {

    List<IntentInstrumentation> instrumentations = new ArrayList<>();

    protected MSCInstrumentation(Context context) {
        super(context);
    }

    protected MSCInstrumentation addInstrumentation(IntentInstrumentation instrumentation) {
        instrumentations.add(instrumentation);
        return this;
    }

    private static Uri mscUri;

    public static Uri getMscUri() {
        return mscUri;
    }

    private static Uri mmpUri;

    public static Uri getMmpUri() {
        return mmpUri;
    }
    private static Boolean isMeituan;

    public static boolean isMeituan() {
        return isMeituan != null && isMeituan;
    }

    public static class Builder {
        Context mContext;
        boolean enableFusion = true;
        boolean enableMMPRouter;
        boolean enableKNBRouter;
        boolean enableRouterConfig = true;

        Uri mmpUri;
        Uri mscUri;
        Uri knbUri;
        List<IntentInstrumentation> intentInstrumentations = new ArrayList<>();

        public Builder(Context context) {
            mContext = context;
        }

        public Context getContext() {
            return mContext;
        }

        /**
         * 目标 MSC 页面的路径
         *
         * @param urlString
         * @return
         */
        public Builder setMSCRouterSchema(String urlString) {
            if (TextUtils.isEmpty(urlString)) {
                return this;
            }
            mscUri = Uri.parse(urlString);
            return this;
        }

        /**
         * 开启MMP路由能力,需同时设置 MMP 路由路径 setMMPRouterSchema
         *
         * @param enableMMPRouter
         * @return
         */
        public Builder enableMMPRouter(boolean enableMMPRouter) {
            this.enableMMPRouter = enableMMPRouter;
            return this;
        }

        /**
         * 开启MMP路由映射配置能力（https://horn.sankuai.com/file/6330/edit/DEVELOPMENT/content）
         * 默认开启，本地受Horn配置更新控制，关闭后Horn无法更新本地配置
         * @param enableRouterConfig
         * @return
         */
        public Builder enableRouterConfig(boolean enableRouterConfig) {
            this.enableRouterConfig = enableRouterConfig;
            return this;
        }


        /**
         * 开启KNB路由能力,需同时设置 KNB 路由路径 setKNBRouterSchema
         *
         * @param enableKNBRouter
         * @return
         */
        public Builder enableKNBRouter(boolean enableKNBRouter) {
            this.enableKNBRouter = enableKNBRouter;
            return this;
        }

        /**
         * 设置MMP路由，页面路径
         *
         * @param urlString
         * @return
         */
        public Builder setMMPRouterSchema(String urlString) {
            if (TextUtils.isEmpty(urlString)) {
                return this;
            }
            mmpUri = Uri.parse(urlString);
            return this;
        }

        public Builder setKNBRouterSchema(String urlString){
            if (TextUtils.isEmpty(urlString)) {
                return this;
            }
            knbUri = Uri.parse(urlString);
            return this;
        }

        public Builder addIntentInstrumentation(IntentInstrumentation intentInstrumentation) {
            intentInstrumentations.add(intentInstrumentation);
            return this;
        }

        public MSCInstrumentation build() {
            MSCInstrumentation instrumentation = new MSCInstrumentation(mContext);
            MSCInstrumentation.mscUri = mscUri;
            MSCInstrumentation.mmpUri = mmpUri;
            MSCRouterInstrumentation mscRouterInstrumentation = new MSCRouterInstrumentation(mContext);
            if (enableMMPRouter) {
                if (mmpUri == null) {
                    throw new MSCRuntimeException("MSCInstrumentation enableMMPRouter must setMMPRouterSchema");
                }
                mscRouterInstrumentation.addRouterProcessor(new MMPRouterProcessor(mContext, mmpUri, enableRouterConfig));
                mscRouterInstrumentation.addRouterProcessor(new MMPRouterRollbackProcessor(mContext, mmpUri));
            }
            if (enableKNBRouter) {
                if (knbUri == null) {
                    throw new MSCRuntimeException("MSCInstrumentation enableKNBRouter must setKNBRouterSchema");
                }
                mscRouterInstrumentation.addRouterProcessor(new KNBRouterProcessor(mContext, knbUri));
            }
            if (mscRouterInstrumentation.isEnable()) {
                instrumentation.addInstrumentation(mscRouterInstrumentation);
            }
            instrumentation.addInstrumentation(new MMPOfflineInstrumentation(mContext));
            instrumentation.addInstrumentation(new MSCMultiProcessInstrumentation(mContext, mscUri));
            instrumentation.addInstrumentation(new MSCTransparentInstrumentation(mContext, mscUri));
            instrumentation.addInstrumentation(new DataPrefetchInstrument(mContext));
            instrumentation.addInstrumentation(new MSCLaunchReferInstrumentation(mContext));
            // fusion must be last one
            if (enableFusion) {
                instrumentation.addInstrumentation(new MSCFusionInstrumentation(mContext));
            }
            if (intentInstrumentations != null && !intentInstrumentations.isEmpty()) {
                for (IntentInstrumentation intentInstrumentation : intentInstrumentations) {
                    instrumentation.addInstrumentation(intentInstrumentation);
                }
            }
            if (MSCHornRollbackConfig.enableLaunchTaskOnRoute()) {
                instrumentation.addInstrumentation(new MSCLaunchTaskInstrumentation(mContext));
            }
            return instrumentation;
        }
    }

    @Override
    public boolean processIntent(Context context, Intent originalIntent, boolean isStartActivity) {
        checkIsMeituan(context);
        boolean processed = false;
        Uri uri = originalIntent.getData();
        boolean isMSCIntent = uri != null && uri.isHierarchical() && MSCRouterInstrumentation.matchWithoutQuery(uri, mscUri);
        for (IntentInstrumentation anInstrumentation : instrumentations) {
            if (anInstrumentation instanceof MSCIntentInstrumentation) {
                if (!isMSCIntent) {
                    continue;
                }
                if (((MSCIntentInstrumentation) anInstrumentation).processMSCIntent(context, originalIntent, isStartActivity)) {
                    processed = true;
                }
            } else if (anInstrumentation.processIntent(context, originalIntent, isStartActivity)) {
                processed = true;
            }
        }
        return processed;
    }

    @Override
    public boolean processActivityOnPause(Activity activity) {
        boolean processed = false;
        for (IntentInstrumentation anInstrumentation : instrumentations) {
            if (anInstrumentation.processActivityOnPause(activity)) {
                processed = true;
            }
        }
        return processed;
    }

    @Override
    public boolean processActivityOnCreate(Activity activity, Bundle icicle) {
        boolean processed = false;
        for (IntentInstrumentation anInstrumentation : instrumentations) {
            if (anInstrumentation.processActivityOnCreate(activity, icicle)) {
                processed = true;
            }
        }
        return processed;
    }

    private void checkIsMeituan(Context context){
        if (isMeituan == null) {
            isMeituan = "com.sankuai.meituan".equals(context.getPackageName());
        }
    }
}
