package com.meituan.msc.modules.router;

import android.app.Activity;
import android.content.Context;
import android.content.Intent;
import android.os.Bundle;
import android.support.annotation.NonNull;

import com.meituan.metrics.MetricsNameProvider;
import com.meituan.msc.common.lib.ISetLaunchRefer;
import com.meituan.msc.common.utils.Constants;
import com.meituan.msc.modules.container.IntentInstrumentation;
import com.meituan.msc.modules.reporter.MSCLog;
import com.meituan.robust.resource.util.ProcessUtil;

import java.lang.ref.WeakReference;

/**
 * 获取启动来源
 */
public class MSCLaunchReferInstrumentation extends IntentInstrumentation {

    private static final String TAG = "MSCLaunchRefer";
    private WeakReference<Activity> pausedActivityWeakRef;

    public MSCLaunchReferInstrumentation(Context context) {
        super(context);
        MSCLog.i(TAG, "curr process:", ProcessUtil.getCurrentProcessName(context));
    }

    @Override
    public boolean processActivityOnPause(Activity activity) {
        pausedActivityWeakRef = new WeakReference<>(activity);
        MSCLog.i(TAG, "callActivityOnPause activity:", activity != null ? activity.toString() : null, ",pausedActivityWeakRef:", pausedActivityWeakRef);
        return true;
    }

    @Override
    public boolean processActivityOnCreate(@NonNull Activity activity, Bundle icicle) {
        MSCLog.i(TAG, "processActivityOnCreate activity:", activity.toString());
        if (!ISetLaunchRefer.class.isAssignableFrom(activity.getClass())) {
            // 只存储当前页面是MSCActivity时的上一个Activity的引用
            Intent intent = activity.getIntent();
            intent.removeExtra(Constants.LAUNCH_REFER);
            MSCLog.i(TAG, "remove launchRefer, pausedActivityWeakRef:", pausedActivityWeakRef);
            if (pausedActivityWeakRef != null) {
                pausedActivityWeakRef.clear();
                pausedActivityWeakRef = null;
                MSCLog.i(TAG, "clear pausedActivityWeakRef");
            }
        }
        return true;
    }

    @Override
    public boolean processIntent(Context context, Intent originalIntent, boolean isStartActivity) {
        MSCLog.i(TAG, "processIntent context：", context, ", isStartActivity:", isStartActivity);
        if (!isStartActivity) {
            return false;
        }
        try {
            Activity startOriginActivity = null;
            MSCLog.i(TAG, "processIntent pausedActivityWeakRef：", pausedActivityWeakRef);
            if (context instanceof Activity) {
                startOriginActivity = (Activity) context;
            } else if (pausedActivityWeakRef != null) {
                startOriginActivity = pausedActivityWeakRef.get();
            }
            if (startOriginActivity == null) {
                MSCLog.i(TAG, "processIntent startOriginActivity null");
                return false;
            }
            MSCLog.i(TAG, "processIntent startOriginActivity:", startOriginActivity.getClass().getName());
            String launchRefer = "unknown";
            if (MetricsNameProvider.class.isAssignableFrom(startOriginActivity.getClass())) {
                MetricsNameProvider interfaceImpl = (MetricsNameProvider) startOriginActivity;
                launchRefer = interfaceImpl.getName();
            } else {
                // 如果上一个页面是非MSC、非MRN的Native页面，期望PV埋点带上Activity的class名称
                launchRefer = startOriginActivity.getClass().getName();
            }
            originalIntent.putExtra(Constants.LAUNCH_REFER, launchRefer);
            MSCLog.i(TAG, "processIntent launchRefer:", launchRefer);
        } catch (Exception e) {
            MSCLog.e(TAG, "processIntent e: ", e.toString());
        }
        return false;
    }
}
