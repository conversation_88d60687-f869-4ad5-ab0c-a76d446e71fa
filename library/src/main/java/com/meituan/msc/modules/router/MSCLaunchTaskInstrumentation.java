package com.meituan.msc.modules.router;

import android.content.Context;
import android.content.Intent;

import com.meituan.msc.common.utils.IntentUtil;
import com.meituan.msc.lib.interfaces.container.MSCParams;
import com.meituan.msc.modules.apploader.InstrumentLaunchManager;
import com.meituan.msc.modules.container.MSCIntentInstrumentation;
import com.meituan.msc.modules.reporter.MSCLog;

public class MSCLaunchTaskInstrumentation extends MSCIntentInstrumentation {
    private static final String TAG = "MSCLaunchTaskInstrumentation";
    public MSCLaunchTaskInstrumentation(Context context) {
        super(context);
    }

    @Override
    public boolean processMSCIntent(Context context, Intent originalIntent, boolean isStartActivity) {
        try {
            InstrumentLaunchManager.getInstance().startLaunch(originalIntent);
        } catch (Exception e) {
            MSCLog.e(TAG, "startLaunchTask failed", e);
        }

        return true;
    }
}
