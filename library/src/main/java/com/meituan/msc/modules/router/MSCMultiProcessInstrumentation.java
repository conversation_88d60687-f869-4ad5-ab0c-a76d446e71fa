package com.meituan.msc.modules.router;

import android.content.Context;
import android.content.Intent;
import android.net.Uri;

import com.meituan.msc.modules.container.IntentInstrumentation;
import com.meituan.msc.modules.engine.requestPrefetch.DataPrefetchManager;

/**
 * msc多进程切换路由
 */
public class MSCMultiProcessInstrumentation extends IntentInstrumentation {

    private Uri mscUri;

    public MSCMultiProcessInstrumentation(Context context, Uri mscUri) {
        super(context);
        this.mscUri = mscUri;
    }

    @Override
    public boolean processIntent(Context context, Intent originalIntent, boolean isStartActivity) {
        Uri uri = originalIntent.getData();
        if (uri == null || !uri.isHierarchical()) {
            return false;
        }
        if (!MSCRouterInstrumentation.matchWithoutQuery(uri, mscUri)) {
            return false;
        }
        return MultiProcessRouterHelper.processIntent(context, originalIntent, isStartActivity);
    }
}
