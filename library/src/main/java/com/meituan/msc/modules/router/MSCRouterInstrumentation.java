package com.meituan.msc.modules.router;

import android.content.Context;
import android.content.Intent;
import android.net.Uri;

import com.meituan.msc.common.process.MSCProcess;
import com.meituan.msc.modules.container.IntentInstrumentation;
import com.meituan.msc.modules.engine.MSCHornRollbackConfig;

import java.util.ArrayList;
import java.util.List;


public class MSCRouterInstrumentation extends IntentInstrumentation {
    List<AbstractRouterProcessor> mIMSCRouterProcessors = new ArrayList<>();

    public MSCRouterInstrumentation(Context context) {
        super(context);
        /**
         * 这里要做一下MSCProcess.init()的原因：
         *    MSC在美团的 com.sankuai.meituan.mbc.dsp.DspInstrumentation#checkInstrumentation 方法中注册了路由拦截器，用于处理MMP迁移过程中的路由切换逻辑。
         *    但是在PinProcess或小游戏的MGC子进程的某些外投场景中跳转优选时，主进程此时没有初始化MSC（外投场景主进程不初始化MSC），但是注册了路由拦截器。
         *    如果没有初始化MSC（调用com.meituan.msc.extern.MSCEnvHelper.ensureFullInited）的话，会导致com.meituan.msc.common.process.MSCProcess.PACKAGE_NAME变量没有赋值，
         *    进而导致在主进程中执行MSCProcess.isInMainProcess()也会返回false，导致路由转换逻辑失效。
         *    路由转换逻辑失效的话，会导致用mmp跳链打开页面时，会先打开RouterCenterActivity（此时触发二级页启动任务初始化MSC），然后再打开HeraActivity时路由拦截，然后再转而打开MSCActivity。
         *    这样多跳一下RouterCenterActivity会额外增加耗时。
         */
        MSCProcess.init(context);
    }

    public void addRouterProcessor(AbstractRouterProcessor processor) {
        mIMSCRouterProcessors.add(processor);
    }

    public boolean isEnable(){
        return mIMSCRouterProcessors.size() > 0;
    }

    @Override
    public boolean processIntent(Context context, Intent originalIntent, boolean isStartActivity) {
        // newActivity 阶段拦截只在MSC 可运行的进程中拦截
        if (!isStartActivity) {
            if (!(MSCProcess.isInMainProcess() || MSCProcess.STANDARD.isCurrentProcess())) {
                return false;
            }
        }
        Uri uri = originalIntent.getData();
        if (uri == null || !uri.isHierarchical()) {
            return false;
        }

        for (AbstractRouterProcessor processor : mIMSCRouterProcessors) {
            if (!processor.isEnable()) {
                continue;
            }
            if (!processor.isUriMatched(uri)) {
                continue;
            }
            boolean processed = processor.processIntent(context, uri, originalIntent, isStartActivity);
            if (processed) {
                return true;
            }
        }
        return false;
    }

    public static boolean matchWithoutQuery(Uri a, Uri b) {
        if (!b.getPath().equalsIgnoreCase(a.getPath())) {
            return false;
        }
        if (!b.getHost().equalsIgnoreCase(a.getHost())) {
            return false;
        }
        if (!b.getScheme().equalsIgnoreCase(a.getScheme())) {
            return false;
        }
        return true;
    }

}
