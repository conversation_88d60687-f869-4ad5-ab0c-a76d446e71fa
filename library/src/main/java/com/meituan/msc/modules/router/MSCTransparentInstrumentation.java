package com.meituan.msc.modules.router;

import android.content.Context;
import android.content.Intent;
import android.net.Uri;

import com.meituan.msc.modules.container.IntentInstrumentation;

/**
 * MSC切换为透明容器路由
 */
public class MSCTransparentInstrumentation extends IntentInstrumentation {

    private final Uri mscUri;

    public MSCTransparentInstrumentation(Context context, Uri mscUri) {
        super(context);
        this.mscUri = mscUri;
    }

    @Override
    public boolean processIntent(Context context, Intent originalIntent, boolean isStartActivity) {
        Uri uri = originalIntent.getData();
        if (uri == null || !uri.isHierarchical()) {
            return false;
        }
        if (!MSCRouterInstrumentation.matchWithoutQuery(uri, mscUri)) {
            return false;
        }
        MSCTransparentRouterHelper.processIntent(context, originalIntent, isStartActivity);
        return true;
    }
}
