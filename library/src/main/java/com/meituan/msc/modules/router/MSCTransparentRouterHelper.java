package com.meituan.msc.modules.router;

import android.content.ComponentName;
import android.content.Context;
import android.content.Intent;
import android.net.Uri;
import android.text.TextUtils;

import com.meituan.msc.common.config.MSCConfig;
import com.meituan.msc.common.process.MSCProcess;
import com.meituan.msc.common.utils.IntentUtil;
import com.meituan.msc.lib.interfaces.container.MSCParams;
import com.meituan.msc.modules.container.MSCTransparentActivity;
import com.meituan.msc.modules.engine.MSCHornRollbackConfig;
import com.meituan.msc.modules.page.transition.PageTransitionConfig;
import com.meituan.msc.modules.reporter.MSCLog;

public final class MSCTransparentRouterHelper {

    public static final String TAG = "MSCTransparentRouterHelper";

    public static boolean processIntent(Context context, Intent originalIntent, boolean isStartActivity) {
        if (MSCHornRollbackConfig.readConfig().rollbackHalfDialog) {
            MSCLog.i(TAG, "rollbackHalfDialog");
            return false;
        }

        Uri uri = originalIntent.getData();
        if (uri == null || !uri.isHierarchical()) {
            return false;
        }
        if (!isStartActivity) {
            if (!(MSCProcess.isInMainProcess() || MSCProcess.STANDARD.isCurrentProcess())) {
                return false;
            }
        }
        Uri data = originalIntent.getData();
        if (data == null) {
            return false;
        }
        MSCLog.i(TAG, "data:", data.toString());
        if (TextUtils.equals(data.getPath(), "/msc_transparent")) {
            return false;
        }
        int pushStyle = IntentUtil.getIntExtra(originalIntent, MSCParams.PUSH_STYLE, 0);
        if (pushStyle != PageTransitionConfig.TransitionStyle.SLIDE_UP_SLIDE_DOWN) {
            return false;
        }
        Uri finalUri = data.buildUpon().path("msc_transparent").build();
        originalIntent.setData(finalUri);
        originalIntent.setComponent(new ComponentName(context, MSCTransparentActivity.class));
        MSCLog.i(TAG, isStartActivity, finalUri);
        return true;
    }
}
