package com.meituan.msc.modules.router;

import android.content.ComponentName;
import android.content.Context;
import android.content.Intent;
import android.net.Uri;
import android.text.TextUtils;

import com.meituan.msc.common.config.MSCMultiProcessConfig;
import com.meituan.msc.common.process.MSCActivity0;
import com.meituan.msc.common.process.MSCProcess;
import com.meituan.msc.common.utils.IntentUtil;
import com.meituan.msc.lib.interfaces.container.MSCParams;
import com.meituan.msc.modules.engine.RuntimeManager;
import com.meituan.msc.modules.reporter.MSCLog;

public class MultiProcessRouterHelper {

    private static final String TAG = "MultiProcessRouterHelper";

    public static boolean processIntent(Context context, Intent originalIntent, boolean isStartActivity) {
        if (!isStartActivity) {
            return false;
        }
        Uri data = originalIntent.getData();
        if (data == null) {
            return false;
        }
        MSCLog.i(TAG, "data:", data.toString());
        String appId = IntentUtil.getStringExtra(originalIntent, MSCParams.APP_ID);
        if (TextUtils.isEmpty(appId)) {
            appId = originalIntent.getData().getQueryParameter(MSCParams.APP_ID);
        }
        if (TextUtils.isEmpty(appId) || RuntimeManager.existUsableRuntime(appId)) {
            MSCLog.i(TAG, "exist runtime at main process", appId);
            return false;
        }
        if (!MSCMultiProcessConfig.get().inMultiProcessWhiteList(appId)) {
            MSCLog.i(TAG, "not in multi process white list", appId);
            return false;
        }
        Uri uri = data.buildUpon().path("msc_sub").build();
        originalIntent.setData(uri);
        originalIntent.setComponent(new ComponentName(context, MSCActivity0.class));
        MSCLog.i(TAG, data, MSCProcess.getCurrentProcessName(), isStartActivity);
        return true;
    }
}
