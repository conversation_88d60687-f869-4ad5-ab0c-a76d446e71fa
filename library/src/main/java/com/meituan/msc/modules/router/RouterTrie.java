package com.meituan.msc.modules.router;

import android.net.Uri;
import android.text.TextUtils;

import org.json.JSONException;
import org.json.JSONObject;

import java.util.Comparator;
import java.util.HashMap;
import java.util.Iterator;
import java.util.Map;
import java.util.TreeMap;

/**
 * MMP路由树
 * <p>
 * MMP 跳转小程序
 * 文档：https://km.sankuai.com/page/316180291
 * 配置：https://horn.sankuai.com/files/edit/1447
 * <p>
 * Created by letty on 2020-05-27.
 **/
public class RouterTrie {

    /**
     * 下级节点
     */
    private Map<String, RouterTrie> children;
    /**
     * query param 前缀匹配
     */
    private Map<String, MPConfig> paramMatch;
    /**
     * 子路径匹配
     * - 子路径自身匹配
     * - 子路径的 query 匹配
     */
    private RouterTrie subPathMatch;
    /**
     * 当前节点配置
     */
    private MPConfig mPConfig;

    public static class MPConfig {
        String appId;
        String pagePath;
    }

    /**
     * 返回child RouterTrie
     *
     * @param key
     * @return
     */
    private RouterTrie appendChild(String key) {
        if (children == null) {
            children = new HashMap<>();
        }
        if (!children.containsKey(key)) {
            children.put(key, new RouterTrie());
        }
        return children.get(key);
    }


    private RouterTrie appendSubPath(MPConfig config) {
        if (subPathMatch == null) {
            subPathMatch = new RouterTrie();
        }
        subPathMatch.mPConfig = config;
        return subPathMatch;
    }

    private void appendSubPathParam(String key, MPConfig config) {
        if (subPathMatch == null) {
            subPathMatch = new RouterTrie();
        }
        subPathMatch.appendParam(key, config);
    }


    private void appendParam(String key, MPConfig mpConfig) {
        if (paramMatch == null) {
            /**
             * 取最长query 需要优先匹配更长的query 直接使用排序的Map
             */
            paramMatch = new TreeMap<>(new Comparator<String>() {
                @Override
                public int compare(String o1, String o2) {
                    return -Integer.compare(o1.length(), o2.length());
                }
            });
        }
        paramMatch.put(key, mpConfig);
    }

    /**
     * 匹配方式：
     * 1.普通匹配
     * 2.子路径匹配
     * 3.query前缀匹配
     * 取最长的
     *
     * @param uri
     * @return
     */
    public MPConfig match(Uri uri) {
        if (uri == null || children == null || !uri.isHierarchical()) {
            return null;
        }
        RouterTrie cur = children.get(uri.getScheme());
        // 不直接匹配scheme
        if (cur == null || cur.children == null) {
            return null;
        }
        MPConfig mpConfig = null;
        /**
         * for host match
         */
        cur = cur.children.get(uri.getHost());
        if (cur == null) {
            return null;
        }
        // 无path情况，直接匹配当前节点和query 完成后返回即可
        if (TextUtils.isEmpty(uri.getPath())) {
            mpConfig = replaceIfNotNull(mpConfig, matchConfigOrParamConfig(cur, uri.getEncodedQuery()));
            return mpConfig;
        }
        // for host/path/subPath all need queryMatch

        /**
         * for path match
         */
        // 此时一定符合该host下的子路径匹配 （如果有规则的话）
        if (cur.subPathMatch != null) {
            mpConfig = replaceIfNotNull(mpConfig, cur.subPathMatch.mPConfig);
        }

        // 根据 PathSegments 个数来进行后续操作
        int length = uri.getPathSegments().size();
        String queryStr = uri.getEncodedQuery();
        // getPathSegments.size() == 0 即 path = "/"
        // 此时判断子路径匹配下所有规则即可，前面判断过 子路径匹配 此处query匹配即可
        if (length == 0) {
            if (cur.subPathMatch != null && cur.subPathMatch.paramMatch != null && queryStr != null) {
                // for 子路径匹配 & query
                mpConfig = replaceIfNotNull(mpConfig, checkIfParamMatched(cur.subPathMatch, queryStr));
            }
        } else {
            for (int i = 0; i < length; i++) {
                if (cur.children == null) {
                    break;
                }
                String pathSegment = uri.getPathSegments().get(i);
                cur = cur.children.get(pathSegment);
                if (cur == null) {
                    break;
                } else {
                    // 非最后一个path 只需要考虑子路径匹配即可，不需要考虑query等情况
                    if (i < length - 1) {
                        if (cur.subPathMatch != null) {
                            mpConfig = replaceIfNotNull(mpConfig, cur.subPathMatch.mPConfig);
                        }
                    } else {
                        // 最后一个path 考虑子路径匹配 + 子路径query；普通匹配 + query
                        if (uri.getPath().endsWith("/")) {
                            if (cur.subPathMatch != null) {
                                mpConfig = replaceIfNotNull(mpConfig, matchConfigOrParamConfig(cur.subPathMatch, queryStr));
                            }
                        } else {
                            mpConfig = replaceIfNotNull(mpConfig, matchConfigOrParamConfig(cur, queryStr));
                        }
                    }
                }
            }
        }

        return mpConfig;
    }

    /**
     * 返回新配置如果新配置不为空
     *
     * @param config
     * @param newConfig
     * @return
     */
    private MPConfig replaceIfNotNull(MPConfig config, MPConfig newConfig) {
        if (newConfig != null) {
            return newConfig;
        } else {
            return config;
        }
    }

    /**
     * 匹配当前节点或者query
     * <p>
     * query满足的情况下 优先匹配query
     *
     * @param cur
     * @param queryStr
     * @return
     */
    private MPConfig matchConfigOrParamConfig(RouterTrie cur, String queryStr) {
        return replaceIfNotNull(cur.mPConfig, checkIfParamMatched(cur, queryStr));
    }

    /**
     * query 匹配
     * <p>
     * 优先匹配最长query
     *
     * @param cur
     * @param query
     * @return
     */
    private static MPConfig checkIfParamMatched(RouterTrie cur, String query) {
        if (cur != null && cur.paramMatch != null && query != null) {
            for (String key : cur.paramMatch.keySet()) {
                //取个最长的 query , 由于默认是有序的 取最前面的即可
                if (query.startsWith(key)) {
                    return cur.paramMatch.get(key);
                }
            }
        }
        return null;
    }

    /**
     * 路由配置信息解析
     * <p>
     * {
     * "https://cs.meituan.com/resource/phone-charge/index.html": // 需要转化的 URL
     * {
     * "appId": "gh_d9004ba7511f",// 目标小程序 ID
     * "pagePath": "/page/index/index?bar=1"// 目标小程序的页面路径
     * },
     * "https://cs.meituan.com/resource/phone-charge/index.html?a=":
     * {
     * "appId": "gh_d9004ba7511f",
     * "pagePath": "/page/index/index?bar=2"
     * }
     * }
     *
     * @param config
     */
    public static RouterTrie processConfig(String config) {
        JSONObject jsonObject;
        try {
            jsonObject = new JSONObject(config);
        } catch (JSONException e) {
            e.printStackTrace();
            return null;
        }

        RouterTrie routerTrie = new RouterTrie();
        Iterator<String> iterable = jsonObject.keys();
        while (iterable.hasNext()) {
            try {
                String key = iterable.next();
                JSONObject configObj = jsonObject.optJSONObject(key);
                if (configObj == null) {
                    continue;
                }
                String appId = configObj.optString("appId");
                String pagePath = configObj.optString("pagePath");
                Uri uri = Uri.parse(key);
                if (TextUtils.isEmpty(appId) || TextUtils.isEmpty(pagePath) || !uri.isHierarchical()) {
                    continue;
                }
                RouterTrie schema = routerTrie.appendChild(uri.getScheme());
                RouterTrie host = schema.appendChild(uri.getHost());
                RouterTrie cur = host;
                String pathStr = uri.getPath();
                if (!TextUtils.isEmpty(pathStr)) {
                    for (String path1 : uri.getPathSegments()) {
                        cur = cur.appendChild(path1);
                    }
                }

                MPConfig mpConfig = new MPConfig();
                mpConfig.appId = appId;
                mpConfig.pagePath = pagePath;

                boolean subPathMatch = pathStr != null && pathStr.endsWith("/");
                String query = uri.getEncodedQuery();
                if (query != null) {
                    if (subPathMatch) {
                        cur.appendSubPathParam(query, mpConfig);
                    } else {
                        cur.appendParam(query, mpConfig);
                    }
                } else if (subPathMatch) {
                    cur.appendSubPath(mpConfig);
                } else {
                    cur.mPConfig = mpConfig;
                }
            } catch (Exception e) {
                e.printStackTrace();
            }

        }
        return routerTrie;
    }


}

