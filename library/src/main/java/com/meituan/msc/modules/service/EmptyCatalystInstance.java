package com.meituan.msc.modules.service;

import android.support.annotation.UiThread;

import com.meituan.msc.jse.bridge.CatalystInstance;
import com.meituan.msc.jse.bridge.IMessageInterface;
import com.meituan.msc.jse.bridge.JavaCallback;
import com.meituan.msc.jse.bridge.JavaFunctionsInterface;
import com.meituan.msc.jse.bridge.JavaScriptModule;
import com.meituan.msc.jse.bridge.LoadJSCodeCacheCallback;
import com.meituan.msc.jse.bridge.NativeArray;
import com.meituan.msc.jse.bridge.queue.ReactQueueConfiguration;
import com.meituan.msc.jse.bridge.queue.ReactQueueConfigurationImpl;
import com.meituan.msc.jse.bridge.queue.ReactQueueConfigurationSpec;

import org.json.JSONArray;

public class EmptyCatalystInstance implements CatalystInstance {

    private final ReactQueueConfiguration mReactQueueConfiguration;
    private volatile boolean mIsDestroyed;

    public EmptyCatalystInstance(ReactQueueConfigurationSpec spec) {
        mReactQueueConfiguration = ReactQueueConfigurationImpl.create(spec, null);
    }

    @UiThread
    @Override
    public void destroy() {
        if (mIsDestroyed) {
            return;
        }
        mIsDestroyed = true;
        mReactQueueConfiguration.destroy();
    }

    @Override
    public long getJSRuntimePtr() {
        // ignore
        return -1;
    }

    @Override
    public boolean isDestroyed() {
        return mIsDestroyed;
    }

    @Override
    public ReactQueueConfiguration getReactQueueConfiguration() {
        return mReactQueueConfiguration;
    }

    @Override
    public void setGlobalVariableString(String propName, String stringValue) {

    }

    @Override
    public void changeV8InspectorName(String name) {

    }

    @Override
    public void notifyContextReady() {

    }

    @Override
    public void setMessageInterface(IMessageInterface messageInterface) {

    }

    @Override
    public String evaluateJavaScript(String script, String assetURL, String codeCacheFile, LoadJSCodeCacheCallback loadJSCodeCacheCallback) {
        return null;
    }

    @Override
    public void callFunction(String module, String method, JSONArray arguments) {

    }

    @Override
    public void invokeCallback(int callbackID, JSONArray arguments) {

    }

    @Override
    public void invokeCallback(int callbackID, NativeArray arguments) {

    }

    @Override
    public void garbageCollect() {

    }

    @Override
    public long getMemoryUsage() {
        return 0;
    }

    @Override
    public void startCPUProfiling(String profilerName, int interval) {

    }

    @Override
    public void stopCPUProfiling(String profilerName, String traceFilePath) {

    }

    @Override
    public <T extends JavaScriptModule> T getJSModule(Class<T> jsInterface) {
        return null;
    }

    @Override
    public void registerJavaCallback(String functionName, JavaCallback callback) {

    }

    @Override
    public void registerJSObject(String name, JavaFunctionsInterface functionsInterface) {

    }

    @Override
    public String executeJSFunction(String moduleName, String methodName, String params) {
        return null;
    }

    @Override
    public String executeListFunction(String moduleName, String methodName, String jsModuleName, String jsMethodName, String params) {
        return null;
    }

    @Override
    public void handleMemoryPressure(int level) {

    }

    @Override
    public String getName() {
        return "Empty";
    }
}
