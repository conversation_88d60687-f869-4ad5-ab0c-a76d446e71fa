package com.meituan.msc.modules.service;

import android.webkit.JavascriptInterface;

public interface IBridgeFeToNative {
    /**
     * 获取Native Module的config信息，用于Fe构建Module对象，前端构建后会运行时缓存
     */
    @JavascriptInterface
    String getNativeModuleConfig(String moduleName);

    /**
     * Fe同步调用Native
     */
    @JavascriptInterface
    String nativeCallSyncHook(String moduleName, String methodName, String params);

    /**
     * Fe异步调用Native
     */
    @JavascriptInterface
    void nativeFlushQueueImmediate(String queue);
}