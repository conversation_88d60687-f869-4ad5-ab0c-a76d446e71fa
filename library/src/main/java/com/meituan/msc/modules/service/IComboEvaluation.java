package com.meituan.msc.modules.service;

import android.support.annotation.Nullable;
import android.webkit.ValueCallback;

import com.meituan.dio.easy.DioFile;

import java.io.IOException;
import java.util.Collection;

/**
 * Created by letty on 2021/6/30.
 **/
public interface IComboEvaluation {
    void evaluateJsFilesCombo(Collection<DioFile> files, String source, @Nullable ValueCallback<String> resultCallback);

    void evaluateJsFilesComboThrow(Collection<DioFile> files, String source, @Nullable ValueCallback<String> resultCallback);
}
