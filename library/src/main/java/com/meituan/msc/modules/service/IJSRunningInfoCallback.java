package com.meituan.msc.modules.service;

import android.support.annotation.NonNull;

import javax.annotation.Nullable;

public interface IJSRunningInfoCallback {


    /**
     * 获取JS引擎运行信息
     *
     * @param runningInfo null说明引擎已经销毁或者还未创建
     */
    void onGetRunningInfo(@Nullable JSRunningInfo runningInfo);

    class JSRunningInfo {
        public final int threadId;
        public final long heapSizeKB;

        JSRunningInfo(int threadId, long heapSizeKB) {
            this.threadId = threadId;
            this.heapSizeKB = heapSizeKB;
        }

        @NonNull
        @Override
        public String toString() {
            return threadId + ":" + heapSizeKB;
        }
    }
}
