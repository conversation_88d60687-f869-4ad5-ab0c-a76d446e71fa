package com.meituan.msc.modules.service;

import android.content.Context;
import android.support.annotation.Keep;
import android.support.annotation.Nullable;
import android.webkit.ValueCallback;

import com.meituan.dio.easy.DioFile;
import com.meituan.msc.jse.bridge.JSInstance;
import com.meituan.msc.jse.bridge.JavaScriptModule;
import com.meituan.msc.jse.bridge.LoadJSCodeCacheCallback;
import com.meituan.msc.modules.engine.MSCRuntime;
import com.meituan.msc.modules.engine.ResultCallback;
import com.meituan.msc.modules.engine.async.IBaseEngine;
import com.meituan.msc.modules.page.render.webview.OnEngineInitFailedListener;
import com.meituan.msc.modules.update.bean.PackageInfoWrapper;


@Keep
public interface IServiceEngine extends IBaseEngine, IComboEvaluation {

    void launch(MSCRuntime runtime, Context context,ILaunchJSEngineCallback launchJSEngineCallback);

    long getJSRuntimePtr();

    void relaunch();

    void release();

    /**
     * 注意：该方法会直接执行JS代码片段，应避免在线上功能中直接拼接执行大片段的JS代码片段，影响性能
     * @param tag
     * @param script
     * @param resultCallback
     */
    void evaluateJavascript(String tag, String script, @Nullable ValueCallback<String> resultCallback);

    void evaluateJsFile(DioFile file, String sourceUri, int packageType, String packageName, @Nullable ResultCallback resultCallback, String codeCacheFile, LoadJSCodeCacheCallback loadJSCodeCacheCallback);

    void setOnEngineInitFailedListener(OnEngineInitFailedListener onEngineInitFailedListener);

    void setOnJsUncaughtErrorHandler(Thread.UncaughtExceptionHandler onJsUncaughtErrorHandler);

    <T extends JavaScriptModule> T getJSModule(Class<T> classOfT);

    JSInstance getJSInstance();

    ServiceInstance getJsExecutor();

    EngineStatus getEngineStatus();

    void getJsMemoryUsage(IEngineMemoryHelper.JSMemoryListener listener);

    void getJsRunningInfo(IJSRunningInfoCallback callback);
}
