package com.meituan.msc.modules.service;

public interface IThreadFactory {

    void runOnUiThread(Runnable runnable);

    void runOnJSQueueThread(Runnable runnable);

    void runOnNativeModulesQueueThread(Runnable runnable);

    void postOnUiThread(Runnable runnable);

    void postOnJSQueueThread(Runnable runnable);

    void postOnNativeModulesQueueThread(Runnable runnable);

    boolean isOnUiThread();

    boolean isOnJSQueueThread();

    boolean isOnNativeModulesQueueThread();
}
