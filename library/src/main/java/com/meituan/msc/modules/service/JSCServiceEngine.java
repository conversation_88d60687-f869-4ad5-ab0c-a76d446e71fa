package com.meituan.msc.modules.service;

import com.meituan.msc.jse.bridge.queue.ReactQueueConfigurationSpec;

public class JSCService<PERSON>ng<PERSON> extends JSCServiceEngineBase {

    public static final String MSC_EVENT_ENGINE_STATUS_CHANGED = "msc_event_engine_status_changed";

    private static final String NATIVE_JS_ENGINE_INIT_BEGIN = "native_js_engine_init_begin";
    private static final String NATIVE_JS_ENGINE_INIT_END = "native_js_engine_init_end";

    @Override
    protected String serviceEngineName() {
        return "";
    }

    @Override
    protected String engineStateChangedEventKey() {
        return MSC_EVENT_ENGINE_STATUS_CHANGED;
    }

    @Override
    protected String jsEngineInitBeginEventKey() {
        return NATIVE_JS_ENGINE_INIT_BEGIN;
    }

    @Override
    protected String jsEngineInitEndEventKey() {
        return NATIVE_JS_ENGINE_INIT_END;
    }

    @Override
    protected ReactQueueConfigurationSpec queueConfigurationSpec() {
        return ReactQueueConfigurationSpec.createDefault();
    }
}
