package com.meituan.msc.modules.service;

import android.content.Context;
import android.os.Looper;
import android.os.Process;
import android.support.annotation.Nullable;
import android.webkit.ValueCallback;

import com.meituan.dio.easy.DioFile;
import com.meituan.msc.common.executor.MSCExecutors;
import com.meituan.msc.common.framework.MPListenerManager;
import com.meituan.msc.common.perf.PerfEventConstant;
import com.meituan.msc.common.utils.ExceptionHelper;
import com.meituan.msc.common.utils.FileSizeUtil;
import com.meituan.msc.common.utils.HashMapHelper;
import com.meituan.msc.jse.bridge.CatalystInstance;
import com.meituan.msc.jse.bridge.JSFunctionCaller;
import com.meituan.msc.jse.bridge.JSInstance;
import com.meituan.msc.jse.bridge.JavaScriptModule;
import com.meituan.msc.jse.bridge.LoadJSCodeCacheCallback;
import com.meituan.msc.jse.bridge.UiThreadUtil;
import com.meituan.msc.jse.bridge.queue.MessageQueueThread;
import com.meituan.msc.jse.bridge.queue.ReactQueueConfiguration;
import com.meituan.msc.jse.bridge.queue.ReactQueueConfigurationSpec;
import com.meituan.msc.modules.IMSCLibraryInterfaceHelper;
import com.meituan.msc.modules.apploader.events.AppLoadException;
import com.meituan.msc.modules.engine.MSCHornRollbackConfig;
import com.meituan.msc.modules.engine.MSCRuntime;
import com.meituan.msc.modules.engine.MSCRuntimeReporter;
import com.meituan.msc.modules.engine.ResultCallback;
import com.meituan.msc.modules.manager.IMSCLibraryInterface;
import com.meituan.msc.modules.manager.MSCEvent;
import com.meituan.msc.modules.manager.MSCRuntimeException;
import com.meituan.msc.modules.page.render.webview.OnEngineInitFailedListener;
import com.meituan.msc.modules.reporter.MSCLoadErrorConstants;
import com.meituan.msc.modules.reporter.MSCLog;
import com.meituan.msc.modules.reporter.ReporterFields;
import com.meituan.msc.modules.update.JSFileSourceHelper;
import com.meituan.msc.modules.update.bean.PackageInfoWrapper;
import com.meituan.msc.util.perf.PerfTrace;

import java.io.IOException;
import java.lang.ref.SoftReference;
import java.util.Collection;
import java.util.concurrent.CountDownLatch;

public abstract class JSCServiceEngineBase implements IServiceEngine, IEngineMemoryHelper {
    private static final String TAG = "JSCServiceEngine";
    // 用于调用方同步获取JS内存占用大小
    public final CountDownLatch getJsMemoryUsageCountDownLatch = new CountDownLatch(1);
    // 最后一次调用getJsMemoryUsage获取到的JS内存占用大小
    public long memoryUsedKB;
    /**
     * 记录上一次JS内存大小，用于通过计算差量来得到对应页面的内存占用
     */
    public long lastJSUsage;
    private ServiceInstance mServiceInstance;
    private volatile boolean isDestroy = false;
    private OnEngineInitFailedListener mOnEngineInitFailedListener;
    private MSCRuntime mscRuntime;
    private volatile EngineStatus engineStatus = EngineStatus.Unknown;
    private SoftReference<Thread.UncaughtExceptionHandler> mExceptionHandlerRef = null;


    protected abstract String serviceEngineName();

    // js引擎状态变化
    protected abstract String engineStateChangedEventKey();

    // 开始初始化 JS 引擎
    protected abstract String jsEngineInitBeginEventKey();

    // JS 引擎初始化完成
    protected abstract String jsEngineInitEndEventKey();

    protected abstract ReactQueueConfigurationSpec queueConfigurationSpec();

    protected JSFunctionCaller jsFunctionCaller() { //可以自定义调用js方法
        return null;
    }

    @Override
    public void runOnJSQueueThreadSafe(Runnable runnable) {
        getJsExecutor().runOnJSQueueThreadSafe(runnable);
    }

    @Override
    public long getJSRuntimePtr() {
        return mServiceInstance.getJSRuntimePtr();
    }

    @Override
    public void evaluateJavascript(String tag, final String script, final @Nullable ValueCallback<String> resultCallback) {
        if (isDestroy) {
            return;
        }
        mServiceInstance.runOnJSQueueThreadSafe(new Runnable() {
            @Override
            public void run() {
                evaluateJsSync(tag, script, JSFileSourceHelper.UNKNOWN_SOURCE, resultCallback, null, null, null);
            }
        });
    }

    private void evaluateJsSync(String tag, String script, String source, @Nullable ValueCallback<String> resultCallback, ResultCallback failureCallback,
                                String codeCacheFile, LoadJSCodeCacheCallback loadJSCodeCacheCallback) {
        try {
            long startTime = System.currentTimeMillis();
//                pkgsource/{md5}/xxxxxxxx之后的 xxxxx https://km.sankuai.com/page/555945880 service文件加载时间注入
            String fileName = !JSFileSourceHelper.UNKNOWN_SOURCE.equals(source) ? source : null;
            String result = mServiceInstance.evaluateJavaScript(script, fileName, codeCacheFile, loadJSCodeCacheCallback);
//            MSCLog.d("evaluate JS", source, script);

            if (fileName != null) {
//                pkgsource/{md5}/xxxxxxxx之后的 xxxxx https://km.sankuai.com/page/555945880 service文件加载时间注入
                String evalFileInjectTime = "if (typeof __mmp_file_timing === 'undefined') {var __mmp_file_timing = {};}__mmp_file_timing['" + fileName + "']" +
                        "  = " + startTime;
                mServiceInstance.evaluateJavaScript(evalFileInjectTime, null, null, null);
            }
            if (resultCallback != null) {
                resultCallback.onReceiveValue(result);
            }
        } catch (Exception t) {
            // 运行时销毁后，不再上报捕获的异常
            // 1. 运行时被销毁时V8引擎仍在执行JS，会抛出异常，对应JS错误为：Error: getPropertyAsObject: property 'module' is undefined
            if (!mscRuntime.isDestroyed) {
                mscRuntime.getNativeExceptionHandler().handleException(t);
                mscRuntime.getRuntimeReporter()
                        .once(ReporterFields.REPORT_UNCAUGHT_ERROR)
                        .tags(HashMapHelper.of("msg", t.getMessage(), "stack", ExceptionHelper.readStack(t)))
                        .sendDelay();
            }
            if (failureCallback != null) {
                MSCLog.e("evaluateJsException", t);
                // TODO: 2022/4/22 tianbin red box reportjs
                failureCallback.onReceiveFailValue(t);
            } else {
                throw t;    //无回调处理的异常 抛出去上报
            }
        }
    }

    @Override
    public void evaluateJsFilesCombo(Collection<DioFile> files, String source, @Nullable ValueCallback<String> resultCallback) {
        if (isDestroy) {
            return;
        }
        if (files == null) {
            return;
        }
        Runnable readRunnable = new Runnable() {
            @Override
            public void run() {
                String fileString = MSCFileUtils.concatComboFileString(files, mscRuntime, resultCallback);
                Runnable evalRunnable = new Runnable() {
                    @Override
                    public void run() {
                        if (isDestroy) {
                            return;
                        }
                        MSCRuntimeReporter.recordJsFileEvaluate(mscRuntime, files);
                        evaluateJsSync("loadFile: combo " + files.size() + ", " + source, fileString, source, resultCallback,
                                resultCallback instanceof ResultCallback ? (ResultCallback) resultCallback : null, null, null);
                    }
                };
                mServiceInstance.runOnJSQueueThreadSafe(evalRunnable);
            }
        };
        executeRunnableWithIO(readRunnable);
    }

    @Override
    public void evaluateJsFilesComboThrow(Collection<DioFile> files, String source, @Nullable ValueCallback<String> resultCallback) {
        if (isDestroy) {
            MSCLog.i(TAG, "#evaluateJsFilesComboThrow, isDestroy=", isDestroy);
            if (resultCallback instanceof ResultCallback) {
                ((ResultCallback)resultCallback).onReceiveFailValue(new IllegalArgumentException("env is not satisfied, engine is destroyed."));
            }
            // 环境不满足，退出
            return;
        }
        if (null == files) {
            MSCLog.i(TAG, "#evaluateJsFilesComboThrow, files is null");
            if (resultCallback instanceof ResultCallback) {
                ((ResultCallback)resultCallback).onReceiveFailValue(new IllegalArgumentException("env is not satisfied, files is null."));
            }
            // DioFile列表为空，无需注入，退出
            return;
        }
        if (files.isEmpty()) {
            MSCLog.i(TAG, "#evaluateJsFilesComboThrow, files is empty");
            if (resultCallback instanceof ResultCallback) {
                ((ResultCallback)resultCallback).onReceiveFailValue(new IllegalArgumentException("env is not satisfied, files is empty."));
            }
            // DioFile列表为空，无需注入，退出
            return;
        }

        Runnable readRunnable = new Runnable() {
            @Override
            public void run() {
                if (isDestroy || null == mscRuntime) {
                    MSCLog.i(TAG, "#evaluateJsFilesComboThrow, isDestroy in read =", isDestroy);
                    if (resultCallback instanceof ResultCallback) {
                        ((ResultCallback)resultCallback).onReceiveFailValue(new IllegalArgumentException("read env is not satisfied"));
                    }
                    // 环境不满足，退出
                    return;
                }
                String fileString = null;
                try {
                    fileString = MSCFileUtils.concatComboFileStringThrow(files);
                } catch (IOException e) {
                    MSCLog.e(TAG, e, "#evaluateJsFilesComboThrow, concatComboFile error,");
                    if (resultCallback instanceof ResultCallback) {
                        ((ResultCallback)resultCallback).onReceiveFailValue(e);
                    }
                    // 读取出现异常，退出
                    return;
                }
                final String injectFileString = fileString;
                Runnable evalRunnable = new Runnable() {
                    @Override
                    public void run() {
                        if (isDestroy || null == mscRuntime || mscRuntime.isDestroyed) {
                            MSCLog.i(TAG, "#evaluateJsFilesComboThrow, isDestroy in inject =", isDestroy);
                            if (resultCallback instanceof ResultCallback) {
                                ((ResultCallback)resultCallback).onReceiveFailValue(new IllegalArgumentException("inject env is not satisfied"));
                            }
                            return;
                        }
                        MSCRuntimeReporter.recordJsFileEvaluate(mscRuntime, files);
                        evaluateJsSync("loadFile: combo " + files.size() + ", " + source,
                                injectFileString,
                                source,
                                resultCallback,
                                resultCallback instanceof ResultCallback ? (ResultCallback) resultCallback : null,
                                null,
                                null);
                    }
                };
                mServiceInstance.runOnJSQueueThreadSafe(evalRunnable);
            }
        };
        MSCExecutors.submit(readRunnable);
    }

    @Override
    public void evaluateJsFile(DioFile file, String sourceUri, int packageType, String packageName, @Nullable ResultCallback resultCallback, String codeCacheFile, LoadJSCodeCacheCallback loadJSCodeCacheCallback) {
        boolean rollback = MSCHornRollbackConfig.isRollbackDestroyInEval();
        if (isDestroy) {
            return;
        }
        if (file == null) {
            return;
        }
        Runnable readRunnable = new Runnable() {
            @Override
            public void run() {
                // 下面调用了onReceiveFailValue，不需要传入回调到读取内部。
                long readStartTime = System.currentTimeMillis();
                String fileContent = MSCFileUtils.readContent(TAG, file, rollback ? resultCallback : null);
                long readDuration = System.currentTimeMillis() - readStartTime;
                if (fileContent == null) {
                    final Exception ex = new MSCRuntimeException("file " + file.getName() + " content is null, abort evaluateJsFile");
                    mscRuntime.getNativeExceptionHandler().handleException(ex);
                    if (resultCallback != null) {
                        resultCallback.onReceiveFailValue(ex);
                    }
                    return;
                }
                recordJsFileRead(readDuration, fileContent.length(), packageType, packageName);
                final String finalFileContent = fileContent;
                Runnable evalRunnable = new Runnable() {
                    @Override
                    public void run() {
                        if (isDestroy) {
                            return;
                        }
                        // loadPackage（InjectBasePkgTask和InjectBuzPkgTask场景）以外的方法调用evaluateJsFile时值为-1，不做记录
                        if (packageType != -1) {
                            PerfTrace.online().begin(packageTypeToString(packageType)).report();
                        }
                        MSCRuntimeReporter.recordJsFileEvaluate(mscRuntime, file);
                        evaluateJsSync(TAG, finalFileContent, sourceUri, resultCallback, resultCallback,
                                codeCacheFile, loadJSCodeCacheCallback);
                        if (packageType != -1) {
                            PerfTrace.online().end(packageTypeToString(packageType)).report();
                        }
                    }
                };
                mServiceInstance.runOnJSQueueThreadSafe(evalRunnable);
            }
        };
        executeRunnableWithIO(readRunnable);
    }

    private void recordJsFileRead(long duration, int fileSize, int packageType, String packageName) {
        // loadPackage（InjectBasePkgTask和InjectBuzPkgTask场景）以外的方法调用evaluateJsFile时值为-1，不做记录
        if (packageType == -1) {
            return;
        }
        if (packageType == PackageInfoWrapper.PACKAGE_TYPE_BASE) {
            mscRuntime.getRuntimeReporter().setReadBasePkgServiceFileDuration(duration).setReadBasePkgServiceFileSize(fileSize);
        } else if (packageType == PackageInfoWrapper.PACKAGE_TYPE_MAIN) {
            mscRuntime.getRuntimeReporter().setReadMainBizPkgServiceFileDuration(duration).setReadMainBizPkgServiceFileSize(fileSize);
        } else {
            mscRuntime.getRuntimeReporter().setReadSubBizPkgServiceFileDuration(duration).setReadSubBizPkgServiceFileSize(fileSize).setServiceInjectSubBizPkgName(packageName);
        }
    }

    private String packageTypeToString(int packageType) {
        if (packageType == PackageInfoWrapper.PACKAGE_TYPE_BASE) {
            return PerfEventConstant.PURE_INJECT_BASE_PKG;
        } else if (packageType == PackageInfoWrapper.PACKAGE_TYPE_MAIN) {
            return PerfEventConstant.PURE_INJECT_BIZ_MAIN_PKG;
        } else {
            return PerfEventConstant.PURE_INJECT_BIZ_SUB_PKG;
        }
    }

    // 涉及到 IO 读取, 如果当前就是在指定线程，则直接在指定线程执行，
    // 否则先在子线程做 IO 操作后在抛到指定线程
    private void executeRunnableWithIO(Runnable readRunnable) {
        if (mServiceInstance.isOnJSQueueThread()) {
            readRunnable.run();
        } else {
            MSCExecutors.submit(readRunnable);
        }
    }

    private void prepareJSExecutor() {
        getJSInstance().setGlobalVariableString("platform", "Android");
    }

    @Override
    public void launch(MSCRuntime runtime, Context context, ILaunchJSEngineCallback launchJSEngineCallback) {
        engineStatus = EngineStatus.Launching;
        mscRuntime = runtime;
        MPListenerManager.getInstance().launchEventListener.onEvent(jsEngineInitBeginEventKey());

        mServiceInstance = new ServiceInstance(runtime, serviceEngineName(), queueConfigurationSpec(), jsFunctionCaller());
        mServiceInstance.runOnJSQueueThreadSafe(new Runnable() {
            @Override
            public void run() {
                try {
                    if (!UiThreadUtil.isOnUiThread()) {
                        Process.setThreadPriority(Process.THREAD_PRIORITY_DEFAULT);
                    }
                    prepareJSExecutor();
                    engineStatus = EngineStatus.Launched;
                } catch (Exception e) {
                    if (mOnEngineInitFailedListener != null) {
                        Exception finalException = e;
                        if (!MSCHornRollbackConfig.readConfig().rollbackLoadErrorReportFix) {
                            runtime.getRuntimeReporter().reportMSCLoadError(runtime.hasContainerAttached(),
                                    MSCLoadErrorConstants.ERROR_INIT_JS_ENGINE_ERROR, e == null ? "" : e.getMessage());
                            finalException = new AppLoadException(MSCLoadErrorConstants.ERROR_INIT_JS_ENGINE_ERROR, e == null ? "" : e.getMessage());
                        }
                        mOnEngineInitFailedListener.onEngineInitFailed(finalException);
                    }
                    release();
                }
                if (mscRuntime != null) {
                    mscRuntime.publish(new MSCEvent<>(engineStateChangedEventKey(), engineStatus));
                }
                MPListenerManager.getInstance().launchEventListener.onEvent(jsEngineInitEndEventKey());
                if (launchJSEngineCallback != null) {
                    launchJSEngineCallback.callBack(JSCServiceEngineBase.this);
                }
            }
        });

        // 重置lastJSUsage
        resetLastJSUsage();

    }

    private void resetLastJSUsage() {
        getJsMemoryUsage(new JSMemoryListener() {
            @Override
            public void onGetMemorySuccess(long jsMemoryKB) {
                lastJSUsage = jsMemoryKB;
            }
        });
    }

    // TODO: relaunch 方法内只进行了 destroy, 没有进行 relaunch, 历史遗留逻辑, 暂不处理
    // 该方法上层方法当前没有调用方, 所以没出问题
    @Override
    public void relaunch() {
        if (mServiceInstance == null) {
            return;
        }
        mServiceInstance.runOnJSQueueThreadSafe(new Runnable() {
            @Override
            public void run() {
                prepareJSExecutor();
                mServiceInstance.destroy();
            }
        });
    }

    @Override
    public ServiceInstance getJsExecutor() {
        return mServiceInstance;
    }

    @Override
    public void release() {
        if (isDestroy) {
            return;
        }
        isDestroy = true;

        if (mServiceInstance == null) {
            return;
        }
        mServiceInstance.runOnJSQueueThread(new Runnable() {
            @Override
            public void run() {
                engineStatus = EngineStatus.Released;
                //FIXME 此处Thread似乎存在内存泄漏，为防止泄漏波及listener（含Activity），先清空listener
                mServiceInstance.destroy();
                mOnEngineInitFailedListener = null;
                if (mscRuntime != null) {
                    mscRuntime.publish(new MSCEvent<>(engineStateChangedEventKey(), engineStatus));
                }
                onReleaseInJSThread();
            }
        });
    }

    private void onReleaseInJSThread() {
        IMSCLibraryInterface libraryInterface = IMSCLibraryInterfaceHelper.getIMSCLibraryInterface();
        if (libraryInterface != null) {
            libraryInterface.onJSCServiceEngineRelease(getJSInstance());
        }
    }

    @Override
    public void setOnJsUncaughtErrorHandler(Thread.UncaughtExceptionHandler onJsUncaughtErrorHandler) {
        mExceptionHandlerRef = new SoftReference<>(onJsUncaughtErrorHandler);
    }

    @Override
    public JSInstance getJSInstance() {
        return mServiceInstance.getInstance();
    }

    private long getMemoryUsage() {
        if (mServiceInstance == null) {
            return 0;
        }
        return mServiceInstance.getHeapStatistics();
    }

    @Override
    public void getJsMemoryUsage(final JSMemoryListener listener) {
        Runnable getJSMemUsageRunnable = new Runnable() {
            @Override
            public void run() {
                long memoryUsed = getMemoryUsage();
                MSCLog.w(null, "MMP AppEngine used memory heap size:", memoryUsed, " bytes");
                long memoryUsedKB = (long) (memoryUsed / 1024);
                if (listener != null) {
                    listener.onGetMemorySuccess(memoryUsedKB);
                }
            }
        };
        mServiceInstance.runOnJSQueueThreadSafe(getJSMemUsageRunnable);
    }

    @Override
    public void getJsMemoryUsage() {
        mServiceInstance.runOnJSQueueThreadSafe(new Runnable() {
            @Override
            public void run() {
                long memoryUsed = getMemoryUsage();
                MSCLog.w(null, "MMP AppEngine used memory heap size:", FileSizeUtil.formatFileSize(memoryUsed));
                memoryUsedKB = memoryUsed / 1024;
                getJsMemoryUsageCountDownLatch.countDown();
            }
        });
    }

    @Override
    public void getJsRunningInfo(IJSRunningInfoCallback callback) {
        if (callback == null) {
            return;
        }
        if (mServiceInstance == null || isDestroy) {
            MSCLog.w(TAG, "getJsRunningInfo: mServiceInstance is uninitialized");
            callback.onGetRunningInfo(null);
            return;
        }
        mServiceInstance.runOnJSQueueThreadSafe(new Runnable() {
            @Override
            public void run() {
                callback.onGetRunningInfo(new IJSRunningInfoCallback.JSRunningInfo(Process.myTid(), getMemoryUsage() / 1024));
            }
        });
    }

    @Override
    public void garbageCollect() {
        if (mServiceInstance != null) {
            long memoryUsedBeforeGC = getMemoryUsage();
            long memoryUsedAfterGC = getMemoryUsage();
            long diff = memoryUsedBeforeGC - memoryUsedAfterGC;
            MSCLog.w("MMP AppEngine performing V8 GC, memory released: ", FileSizeUtil.formatFileSize(diff));
        }
    }

    @Override
    public void setOnEngineInitFailedListener(OnEngineInitFailedListener onEngineInitFailedListener) {
        mOnEngineInitFailedListener = onEngineInitFailedListener;
    }

    public <T extends JavaScriptModule> T getJSModule(Class<T> classOfT) {
        if (mServiceInstance != null) {
            return mServiceInstance.getJSModule(classOfT);
        }
        return null;
    }

    @Override
    public EngineStatus getEngineStatus() {
        return engineStatus;
    }
}
