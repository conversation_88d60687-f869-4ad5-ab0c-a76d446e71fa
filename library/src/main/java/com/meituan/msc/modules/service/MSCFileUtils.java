package com.meituan.msc.modules.service;

import android.support.annotation.NonNull;
import android.support.annotation.Nullable;
import android.text.TextUtils;
import android.webkit.ValueCallback;

import com.meituan.dio.easy.DioFile;
import com.meituan.msc.common.utils.DioDataCache;
import com.meituan.msc.common.utils.JsonUtil;
import com.meituan.msc.modules.devtools.DebugHelper;
import com.meituan.msc.modules.engine.MSCHornRollbackConfig;
import com.meituan.msc.modules.engine.MSCRuntime;
import com.meituan.msc.modules.engine.ResultCallback;
import com.meituan.msc.modules.manager.MSCRuntimeException;
import com.meituan.msc.modules.preload.MSCHornPreloadConfig;
import com.meituan.msc.modules.reporter.MSCLog;
import com.meituan.msc.modules.reporter.MSCReporter;
import com.meituan.msc.modules.update.JSFileSourceHelper;
import com.meituan.msc.modules.update.PackageLoadReporter;
import com.meituan.msc.modules.update.bean.PackageInfoWrapper;
import com.meituan.msc.modules.update.pkg.PackageLoadManager;

import org.json.JSONObject;

import java.io.IOException;
import java.util.Arrays;
import java.util.Collection;
import java.util.List;

/**
 * https://km.sankuai.com/page/917121228 webview importScripts 接口
 * https://km.sankuai.com/page/847678042 importScripts & webview combo 模式
 */
public class MSCFileUtils {
    private static final String TAG = "MSCFileUtils";

    /**
     * TODO page层加载的文件 也可可以走类似逻辑优化 ；page层文件会被反复读取，多个页面；page层与service层有部分文件是一致的
     *
     * @param tag
     * @param file
     * @param resultCallback
     * @return
     */
    public static String readContent(String tag, final DioFile file, @Nullable final ValueCallback<String> resultCallback) {
        DioDataCache.Result result = DioDataCache.readContentThroughCache(file);
        if (result.e != null) {
            if (resultCallback instanceof ResultCallback) {
                ((ResultCallback) resultCallback).onReceiveFailValue(new IOException(tag + "#evaluateJsFile readContent failed", result.e));
            }
            return null;
        }
        return result.content;
    }

    /**
     * 读取文本。若出现异常，直接抛出。
     *
     * @param file
     * @return
     * @throws IOException
     */
    public static String readContentThrow(final DioFile file) throws IOException{
        DioDataCache.Result result = DioDataCache.readContentThroughCache(file);
        if (result.e != null) {
            throw result.e;
        }
        if (TextUtils.isEmpty(result.content)) {
            throw new IOException("mscfileutils read content is null.");
        }
        return result.content;
    }


    /**
     * 支持combo模式的加载script
     * @param files
     * @param params
     * @param runtime
     * @param iComboEvaluation
     * @return
     */
    public static String importScript(String[] files, String params, MSCRuntime runtime, IComboEvaluation iComboEvaluation) {
        if (runtime == null) {
            MSCLog.i(TAG, "Cancel_Import_Script_When_Runtime_Is_NULL", files, params);
            return null;
        }

        if (files == null) {
            runtime.getNativeExceptionHandler().handleException(new MSCRuntimeException("AppService#importScripts Error: files null"));
            return null;
        }

        JSONObject jsonObject = JsonUtil.parseToJson(params);
        boolean enableComboFeature = !MSCHornRollbackConfig.get().getConfig().isRollbackImportScriptSupportCombo;
        boolean isCombo = enableComboFeature && jsonObject.optBoolean("combo", false);
        // TODO: 5/16/23  IDE 调试模式下,可能需要关闭 combo 模式
        boolean withoutEval = jsonObject.optBoolean("withoutEval", false);

        if (withoutEval) {
            return importScriptsWithCombo(files, true, runtime, iComboEvaluation);
        }
        try {
            // debugService时需要通过AppPage.APPEND_SCRIPT加载js文件，以便调试时能找到文件，不使用combo
            if (isCombo && !DebugHelper.debugWebView) {
                // 支持combo，多个文件一次返回
                importScriptsWithCombo(files, false, runtime, iComboEvaluation);
            } else {
                // 不支持combo，则每次加载一个
                for (String file : files) {
                    importScriptsWithCombo(new String[]{file}, false, runtime, iComboEvaluation);
                }
            }
        } catch (Exception e) {
            MSCLog.e(TAG, e, "Import_Script_With_Combo_Failed", files, params);
            runtime.getNativeExceptionHandler().handleException(e);
        }
        return null;
    }

    public static String concatComboFileString(Collection<DioFile> files, MSCRuntime runtime, @Nullable ValueCallback<String> resultCallback) {
        StringBuilder sb = new StringBuilder();
        for (DioFile file : files) {
            if (!file.exists()) {
                MSCLog.i(TAG, "Cancel_Concat_Combo_File_When_File_Not_Exist", file);
                continue;
            }
            String fileContent = MSCFileUtils.readContent(TAG, file, resultCallback);
            if (fileContent == null) {
                if (MSCHornRollbackConfig.isRollbackImportScriptsDoubleUploadError()) {
                    // 回滚逻辑。发现异常，上报异常，且继续遍历。
                    runtime.getNativeExceptionHandler().handleException(new MSCRuntimeException("file " + file.getName() + " content is null, abort evaluateJsFile"));
                }
                // 新，发现异常，继续下一个文件，上报异常统一到callback中（之前这里和callback都上报异常，多报了。）。resultCallback中会进行上报异常。
                continue;
            }
            if (sb.length() > 0) {
                sb.append("\n");
            }
            sb.append(fileContent);
        }
        return sb.toString();
    }

    /**
     * 将文件内容拼接返回。
     *
     * @param files
     * @return
     * @throws IOException
     */
    public static String concatComboFileStringThrow(Collection<DioFile> files) throws IOException {
        StringBuilder sb = new StringBuilder();
        for (DioFile file : files) {
            if (!file.exists()) {
                MSCLog.i(TAG, "Cancel_Concat_Combo_File_When_File_Not_Exist", file);
                throw new IOException("file not exits with name " + file.getName());
            }
            // 这里会throw异常，若文件读取异常。
            String fileContent = MSCFileUtils.readContentThrow(file);
            if (sb.length() > 0) {
                sb.append("\n");
            }
            sb.append(fileContent);
        }
        return sb.toString();
    }

    /**
     * 避免回调不执行情况。内部已执行上报和埋点统计。外面不要重复调用。
     *
     * @param fileUris
     * @param runtime
     * @param iComboEvaluation
     * @param resultCallback
     */
    public static void importScriptsAsyncWithComboSafe(String [] fileUris,
                                                       MSCRuntime runtime,
                                                       @NonNull IComboEvaluation iComboEvaluation,
                                                       @Nullable ResultCallback resultCallback) {
        List<DioFile> files = runtime.getMSCAppModule().getDioFiles(fileUris);
        String source = JSFileSourceHelper.getSourceOfImportCombo(fileUris);
        String[] pkgNames = JSFileSourceHelper.getPkgNamesFromUrl(fileUris, runtime);
        iComboEvaluation.evaluateJsFilesComboThrow(files, source, new ResultCallback() {
            @Override
            public void onReceiveFailValue(Exception e) {
                MSCLog.e(TAG, e, "Import_Script_With_Combo_Failed", fileUris);
                runtime.getNativeExceptionHandler().handleException(e);
                if (!runtime.isDestroyed) {
                    // 容器销毁，不需要上报埋点统计。老逻辑同步。
                    PackageLoadReporter.create(runtime).onInjectPackage(MSCReporter.ReportValue.FAILED,
                            "files", fileUris, pkgNames, -1, e != null ? e.toString() : "", true);
                }
                if (null != resultCallback) {
                    resultCallback.onReceiveFailValue(e);
                }
            }

            @Override
            public void onReceiveValue(String value) {
                MSCLog.i(TAG, "Import_Script_With_Combo_Success", fileUris);
                PackageLoadReporter.create(runtime).onInjectPackage(MSCReporter.ReportValue.SUCCESS,
                        "files", fileUris, pkgNames, -1, null, true);
                if (null != resultCallback) {
                    resultCallback.onReceiveValue(value);
                }
            }
        });

    }


    // TODO: 2023/5/9 tianbin 按需加载失败不在启动流程中
    public static String importScriptsWithCombo(String[] fileUris, boolean withoutEval, final MSCRuntime runtime, IComboEvaluation iComboEvaluation) {
        List<DioFile> files = runtime.getMSCAppModule().getDioFiles(fileUris);
        String source = JSFileSourceHelper.getSourceOfImportCombo(fileUris);
        String[] pkgNames = JSFileSourceHelper.getPkgNamesFromUrl(fileUris, runtime);
        if (withoutEval) {
            return MSCFileUtils.concatComboFileString(files, runtime, new ResultCallback() {
                @Override
                public void onReceiveFailValue(Exception e) {
                    if (!MSCHornRollbackConfig.isRollbackImportScriptsDoubleUploadError()) {
                        runtime.getNativeExceptionHandler().handleException(e);
                    }
                }

                @Override
                public void onReceiveValue(String value) {

                }
            });
        }

        if (iComboEvaluation != null) {
            iComboEvaluation.evaluateJsFilesCombo(files, source, new ResultCallback() {
                @Override
                public void onReceiveFailValue(Exception e) {
                    MSCLog.e(TAG, e, "Import_Script_With_Combo_Failed", fileUris);
                    runtime.getNativeExceptionHandler().handleException(e);
                    // todo mmp 旧埋点
//                    if (e instanceof IOException) {
//                        //FileUtil.reportReadDioContentFailed(null, source, e, null, runtime.getAppId());
//                    }
                    PackageLoadReporter.create(runtime).onInjectPackage(MSCReporter.ReportValue.FAILED,
                            "files", fileUris, pkgNames, -1, e != null ? e.toString() : "");
                }

                @Override
                public void onReceiveValue(String value) {
                    MSCLog.i(TAG, "Import_Script_With_Combo_Success", fileUris);
                    PackageLoadReporter.create(runtime).onInjectPackage(MSCReporter.ReportValue.SUCCESS,
                            "files", fileUris, pkgNames);
                }
            });
        }
        return null;
    }


    public static boolean checkMd5AndDeleteIfNeed(String checkScene, PackageInfoWrapper infoWrapper) {
        if (MSCHornPreloadConfig.preCheckDDResourceMd5AndRetryDownload()) {
            MSCLog.i(TAG, "checkMd5AndDeleteIfNeed rollback");
            return true;
        }
        return PackageLoadManager.getInstance().checkMd5AndDeleteIfNeed(checkScene, infoWrapper);
    }
}
