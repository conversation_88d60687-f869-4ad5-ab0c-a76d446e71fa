package com.meituan.msc.modules.service;

import com.meituan.metrics.JSRuntimeInfo;
import com.meituan.metrics.JSRuntimeInfoProvider;
import com.meituan.msc.common.executor.MSCExecutors;
import com.meituan.msc.modules.engine.MSCHornRollbackConfig;
import com.meituan.msc.modules.engine.MSCRuntime;
import com.meituan.msc.modules.engine.RuntimeManager;
import com.meituan.msc.modules.engine.RuntimeSource;
import com.meituan.msc.modules.page.IPageModule;
import com.meituan.msc.modules.page.render.RendererType;
import com.meituan.msc.modules.reporter.MSCLog;
import com.sankuai.meituan.serviceloader.annotation.ServiceLoaderInterface;

import java.util.Collection;
import java.util.Collections;
import java.util.List;
import java.util.concurrent.CopyOnWriteArrayList;
import java.util.concurrent.atomic.AtomicInteger;

import javax.annotation.Nullable;

@ServiceLoaderInterface(key = "MSCJSRuntimeInfoProvider", interfaceClass = JSRuntimeInfoProvider.class, singleton = true)
public class MSCJSRuntimeInfoProvider implements JSRuntimeInfoProvider {
    private static final String TAG = "MSCJSRuntimeInfoProvider";
    private static final String APP_ID_UNKNOWN = "unknown";
    private static final String APP_ID_PRELOAD = RuntimeSource.toReportString(RuntimeSource.BASE_PRELOAD);
    private static final String MSC_RENDER_TYPE_UNKNOWN = "msc-unknown";
    private static final String MSC_RENDER_TYPE_NATIVE = "msc-native";
    private static final String MSC_RENDER_TYPE_WEB = "msc-web";
    private static final String MSC_RENDER_TYPE_BASE_PRELOAD = "msc-base-preload";
    private static final String MSC_RENDER_TYPE_BIZ_PRELOAD = "msc-biz-preload";
    private static final String MSC_RENDER_TYPE_NEW = "msc-runtime-new";
    private static final String MSC_RENDER_TYPE_KEEP_ALIVE = "msc-runtime-keep-alive";

    @Override
    public void getInfos(Callback callback) {
        if (callback == null) {
            return;
        }
        if (!MSCHornRollbackConfig.enableJsRuntimeInfoProvider()) {
            callback.onDataReady(Collections.emptyList());
            return;
        }
        Collection<MSCRuntime> waitToCollectRuntimes = RuntimeManager.getAllRuntimes();
        MSCLog.d(TAG, "getInfos, total size: ", waitToCollectRuntimes.size());
        new JSRuntimeInfoCollectTask(callback, waitToCollectRuntimes).run();
    }

    @Override
    public int getBizType() {
        return BIZ_MSC;
    }

    /**
     * JS运行时信息收集
     */
    static class JSRuntimeInfoCollectTask {
        private final Callback callback;
        private final Collection<MSCRuntime> waitToCollectRuntimes;
        private final List<JSRuntimeInfo> resultList = new CopyOnWriteArrayList<>();
        private final AtomicInteger completeCount;

        JSRuntimeInfoCollectTask(Callback callback, Collection<MSCRuntime> waitToCollectRuntimes) {
            this.callback = callback;
            this.waitToCollectRuntimes = waitToCollectRuntimes;
            completeCount = new AtomicInteger(0);
        }

        public void run() {
            if (callback == null) {
                return;
            }
            if (waitToCollectRuntimes == null || waitToCollectRuntimes.isEmpty()) {
                callback.onDataReady(Collections.emptyList());
                return;
            }
            for (MSCRuntime runtime : waitToCollectRuntimes) {
                if (runtime == null) {
                    MSCLog.i(TAG, "JSRuntimeInfoCollectTask#run: runtime null");
                    tryCallback();
                    continue;
                }
                // 获取小程序信息
                String appId = runtime.getAppId();
                String runningStack = MSC_RENDER_TYPE_UNKNOWN;
                if (appId == null && runtime.getSource() == RuntimeSource.BASE_PRELOAD) {
                    appId = APP_ID_PRELOAD;
                } else if (appId == null) {
                    appId = APP_ID_UNKNOWN;
                }
                IPageModule pageModule = runtime.getTopPageModule();
                if (pageModule != null) {
                    RendererType topPageRendererType = pageModule.getRendererType();
                    if (topPageRendererType == RendererType.NATIVE || topPageRendererType == RendererType.RN) {
                        runningStack = MSC_RENDER_TYPE_NATIVE;
                    } else if (topPageRendererType == RendererType.WEBVIEW) {
                        runningStack = MSC_RENDER_TYPE_WEB;
                    }
                } else if (runtime.getSource() == RuntimeSource.BASE_PRELOAD) {
                    runningStack = MSC_RENDER_TYPE_BASE_PRELOAD;
                } else if (runtime.getSource() == RuntimeSource.BIZ_PRELOAD) {
                    runningStack = MSC_RENDER_TYPE_BIZ_PRELOAD;
                } else if (runtime.getSource() == RuntimeSource.NEW) {
                    runningStack = MSC_RENDER_TYPE_NEW;
                } else if (runtime.getSource() == RuntimeSource.KEEP_ALIVE) {
                    runningStack = MSC_RENDER_TYPE_KEEP_ALIVE;
                }
                String finalRunningStack = runningStack;
                String finalAppId = appId;
                runtime.getJsRunningInfo(new IJSRunningInfoCallback() {
                    @Override
                    public void onGetRunningInfo(@Nullable JSRunningInfo runningInfo) {
                        MSCLog.d(TAG, "JSRuntimeInfoCollectTask#run: info-> ", runningInfo);
                        if (runningInfo != null) {
                            resultList.add(new JSRuntimeInfo(runningInfo.threadId, runningInfo.heapSizeKB, finalRunningStack, finalAppId));
                        }
                        tryCallback();
                    }
                });
            }
        }

        /**
         * 尝试回调：完成数=待收集数时，真正执行回调
         */
        private void tryCallback() {
            if (waitToCollectRuntimes.size() == completeCount.incrementAndGet()) {
                // 调度到子线程回调，防止外部逻辑占用JS线程。
                MSCExecutors.ioSerialized.execute(new Runnable() {
                    @Override
                    public void run() {
                        MSCLog.d(TAG, "JSRuntimeInfoCollectTask#callback: result: ", resultList);
                        callback.onDataReady(resultList);
                    }
                });
            }
        }
    }
}
