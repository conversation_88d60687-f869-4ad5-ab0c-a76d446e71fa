package com.meituan.msc.modules.service;

import com.meituan.msc.jse.bridge.queue.MessageQueueThread;
import com.meituan.msc.jse.bridge.queue.ReactQueueConfiguration;

public class ReactThreadFactory implements IThreadFactory {

    private final MessageQueueThread mUiThread;
    private final MessageQueueThread mJSThread;
    private final MessageQueueThread mNativeModulesThread;

    public ReactThreadFactory(ReactQueueConfiguration configuration) {
        mUiThread = configuration.getUIQueueThread();
        mJSThread = configuration.getJSQueueThread();
        mNativeModulesThread = configuration.getNativeModulesQueueThread();
    }

    @Override
    public void runOnUiThread(Runnable runnable) {
        if (mUiThread.isOnThread()) {
            runnable.run();
        } else {
            mUiThread.runOnQueue(runnable);
        }
    }

    @Override
    public void runOnJSQueueThread(Runnable runnable) {
        if (mJSThread.isOnThread()) {
            runnable.run();
        } else {
            mJSThread.runOnQueue(runnable);
        }
    }

    @Override
    public void runOnNativeModulesQueueThread(Runnable runnable) {
        if (mNativeModulesThread.isOnThread()) {
            runnable.run();
        } else {
            mNativeModulesThread.runOnQueue(runnable);
        }
    }

    @Override
    public void postOnUiThread(Runnable runnable) {
        mUiThread.runOnQueue(runnable);
    }

    @Override
    public void postOnJSQueueThread(Runnable runnable) {
        mJSThread.runOnQueue(runnable);
    }

    @Override
    public void postOnNativeModulesQueueThread(Runnable runnable) {
        mNativeModulesThread.runOnQueue(runnable);
    }

    @Override
    public boolean isOnUiThread() {
        return mUiThread.isOnThread();
    }

    @Override
    public boolean isOnJSQueueThread() {
        return mJSThread.isOnThread();
    }

    @Override
    public boolean isOnNativeModulesQueueThread() {
        return mNativeModulesThread.isOnThread();
    }
}
