package com.meituan.msc.modules.service;

import com.meituan.msc.common.utils.MSCTraceUtil;
import com.meituan.msc.jse.bridge.CallFunctionContext;
import com.meituan.msc.jse.bridge.ICallFunctionContext;
import com.meituan.msc.jse.bridge.IMessageInterface;
import com.meituan.msc.modules.engine.MSCRuntime;
import com.meituan.msc.modules.manager.ExecutorContext;
import com.meituan.msc.modules.reporter.MSCLog;
import com.meituan.msc.util.perf.PerfEventName;
import com.meituan.msc.util.perf.PerfTrace;

import org.json.JSONArray;
import org.json.JSONException;

public class RuntimeMessageInterface implements IMessageInterface {

    private final String TAG = "RuntimeMessageInterface@" + Integer.toHexString(hashCode());

    private final MSCRuntime runtime;
    private final ExecutorContext executorContext;

    public RuntimeMessageInterface(MSCRuntime runtime, ExecutorContext executorContext) {
        this.runtime = runtime;
        this.executorContext = executorContext;
    }

    private void log(Object... objects) {
//        MSCLog.d(TAG, objects);
    }

    @Override
    public Object invokeSync(ICallFunctionContext context, String moduleName, String methodName, JSONArray params) {
        Object value = runtime.invoke(context, moduleName, methodName, params, executorContext);
        log("invokeSync, moduleName:", moduleName, ", methodName:", methodName, ", value:", value);
        return value;
    }

    @Override
    public void invoke(String moduleName, String methodName, JSONArray params) {
        log("invoke, moduleName:", moduleName, ", methodName:", methodName);
        runtime.invoke(CallFunctionContext.DO_NOTHING_CONTEXT, moduleName, methodName, params, executorContext);
    }

    @Override
    public void batchInvoke(String queue) {
        try {
            long startTimeNS = PerfTrace.currentTime();
            JSONArray array = new JSONArray(queue);

            JSONArray modules = array.optJSONArray(0);
            JSONArray methods = array.optJSONArray(1);
            JSONArray params = array.optJSONArray(2);

            for (int i = 0; i < modules.length(); i++) {
                String moduleName = modules.optString(i);
                String methodName = methods.optString(i);
                JSONArray param = params.optJSONArray(i);
                if (MSCTraceUtil.TRACE_LEVEL >= MSCTraceUtil.LEVEL_HEAVY) {
                    if ("onDataChange".equals(methodName) && "NativeRList".equals(moduleName)) {
                        MSCTraceUtil.RListOnDataChangedCmdStartTime.add(startTimeNS);
                        PerfTrace.duration(PerfEventName.R_LIST_ONDATACHANGE_DECODE, startTimeNS);
                        PerfTrace.begin(PerfEventName.R_LIST_ONDATACHANGE_JS2SHADOW);
                    }
                }
                invoke(moduleName, methodName, param);
            }
        } catch (JSONException e) {
            MSCLog.e(TAG, e);
        }
    }

    @Override
    public JSONArray getConfig(String moduleName) {
        return runtime.getConfig(moduleName);
    }
}
