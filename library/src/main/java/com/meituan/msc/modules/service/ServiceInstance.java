package com.meituan.msc.modules.service;

import android.app.Activity;
import android.content.Context;
import android.os.Build;
import android.os.Handler;
import android.os.Looper;
import android.text.TextUtils;

import com.meituan.android.soloader.SoLoader;
import com.meituan.msc.common.executor.MSCExecutors;
import com.meituan.msc.common.perf.PerfEventConstant;
import com.meituan.msc.common.utils.MSCTraceUtil;
import com.meituan.msc.extern.MSCEnvHelper;
import com.meituan.msc.jse.bridge.CatalystInstance;
import com.meituan.msc.jse.bridge.CatalystInstanceImpl;
import com.meituan.msc.jse.bridge.JSFunctionCaller;
import com.meituan.msc.jse.bridge.JSInstance;
import com.meituan.msc.jse.bridge.JavaScriptExecutor;
import com.meituan.msc.jse.bridge.JavaScriptExecutorFactory;
import com.meituan.msc.jse.bridge.JavaScriptModule;
import com.meituan.msc.jse.bridge.LoadJSCodeCacheCallback;
import com.meituan.msc.jse.bridge.NativeMap;
import com.meituan.msc.jse.bridge.RNArguments;
import com.meituan.msc.jse.bridge.ReactBridge;
import com.meituan.msc.jse.bridge.UiThreadUtil;
import com.meituan.msc.jse.bridge.queue.ReactQueueConfiguration;
import com.meituan.msc.jse.bridge.queue.ReactQueueConfigurationSpec;
import com.meituan.msc.jse.common.futures.SimpleSettableFuture;
import com.meituan.msc.jse.jscexecutor.JSCExecutorFactory;
import com.meituan.msc.jse.modules.core.DefaultHardwareBackBtnHandler;
import com.meituan.msc.jse.modules.core.ReactChoreographer;
import com.meituan.msc.modules.container.IContainerManager;
import com.meituan.msc.modules.devtools.DebugHelper;
import com.meituan.msc.modules.engine.MSCHornRollbackConfig;
import com.meituan.msc.modules.engine.MSCRuntime;
import com.meituan.msc.modules.exception.MSCModuleCallExceptionHandler;
import com.meituan.msc.modules.manager.ExecutorContext;
import com.meituan.msc.modules.manager.JSONDataParser;
import com.meituan.msc.modules.manager.MSCHandler;
import com.meituan.msc.modules.reporter.MSCLog;
import com.meituan.msc.util.perf.PerfTrace;

import org.apache.commons.logging.Log;
import org.apache.commons.logging.LogFactory;
import org.json.JSONException;
import org.json.JSONObject;

/**
 * 引擎通信相关，跨端通信
 */
public class ServiceInstance implements DefaultHardwareBackBtnHandler {

    public static final String TAG = "ServiceInstance";
    private static final Log log = LogFactory.getLog(ServiceInstance.class);

    public final String INSTANCE_TAG = TAG + hashCode();

    private final V8Profiler v8Profiler;
    private final MSCRuntime runtime;
    private final CatalystInstance mCatalystInstance;
    private final IThreadFactory mThreadFactory;
    private final String engineName;

    public static void staticInit() {
        MSCLog.i(TAG, "staticInit:" + UiThreadUtil.isOnUiThread());
        MSCExecutors.runOnUiThread(new Runnable() {
            @Override
            public void run() {
                MSCLog.i(TAG, "ReactChoreographer initialize");
                ReactChoreographer.initialize();
            }
        });
    }

    public ServiceInstance(MSCRuntime runtime, String engineName, ReactQueueConfigurationSpec queueConfigurationSpec, JSFunctionCaller caller) {
        this.runtime = runtime;
        this.engineName = engineName;
        this.v8Profiler = new V8Profiler(this);
        Context context = MSCEnvHelper.getContext();
        runtime.mCatalystInstance = mCatalystInstance = createCatalystInstance(context, queueConfigurationSpec, caller);
        mThreadFactory = new ReactThreadFactory(mCatalystInstance.getReactQueueConfiguration());
    }

    public long getJSRuntimePtr() {
        return mCatalystInstance.getJSRuntimePtr();
    }

    public V8Profiler getV8Profiler() {
        return this.v8Profiler;
    }

    public static String getFriendlyDeviceName() {
        return Build.MODEL + " - " + Build.VERSION.RELEASE + " - API " + Build.VERSION.SDK_INT;
    }

    String obtainInspectName(MSCRuntime runtime) {
        if (MSCEnvHelper.getEnvInfo() == null) return "";
        if (MSCEnvHelper.getEnvInfo().isProdEnv()) return "";
        if (runtime == null) return "";
        final String name = runtime.getAppId();
        return (TextUtils.isEmpty(name) ? "mtv8_java_default" : name) + ":" + (MSCEnvHelper.getEnvInfo().isProdEnv() ? "_release" : "_debug");
    }

    /**
     * 执行js
     *
     * @param script js代码
     */
    public String evaluateJavaScript(String script, String sourceURL, String codeCacheFile, LoadJSCodeCacheCallback loadJSCodeCacheCallback) {
        // TODO 观察线上日志情况, 若量比较大再删除
        if (!TextUtils.isEmpty(sourceURL) && !TextUtils.equals("unknow", sourceURL)) {
            MSCLog.i(INSTANCE_TAG, "evaluateJavaScript: ", sourceURL);
        }
        String result = mCatalystInstance.evaluateJavaScript(script, sourceURL, codeCacheFile, loadJSCodeCacheCallback);
        if (result == null) {
            return null;
        }
        try {
            JSONObject jsonObject = new JSONObject(result);
            String type = jsonObject.optString("type");
            if ("object".equals(type)) {
                return jsonObject.optString("result");
            } else if ("boolean".equals(type)) {
                return String.valueOf(jsonObject.optBoolean("result"));
            }
        } catch (JSONException ignore) {
        }
        return null;
    }

    public String getEngineFullName() {
        return engineName + obtainInspectName(runtime);
    }

    public String getEngineType() {
        return mCatalystInstance != null ? mCatalystInstance.getName() : "";
    }

    public void destroy() {
        MSCExecutors.runOnUiThread(new Runnable() {
            @Override
            public void run() {
                mCatalystInstance.destroy();
            }
        });
    }

    @Override
    public void invokeDefaultOnBackPressed() {
        try {
            IContainerManager containerManager;
            Activity topActivity;
            if ((containerManager = runtime.getContainerManagerModule()) != null && (topActivity = containerManager.getTopActivity()) != null) {
                topActivity.finish();
            }
        } catch (Throwable t) {
            MSCLog.e(TAG, t);
        }
        getThreadConfiguration().getNativeModulesQueueThread().removeCallbacks(null);
    }

    private CatalystInstance createWebViewCatalystInstance(Context context, ReactQueueConfigurationSpec queueConfigurationSpec, JSFunctionCaller caller)
            throws Exception {
        MSCModuleCallExceptionHandler exceptionHandler = new MSCModuleCallExceptionHandler(runtime);
        SimpleSettableFuture<CatalystInstance> future = new SimpleSettableFuture<>();
        MSCExecutors.runOnUiThread(new Runnable() {
            @Override
            public void run() {
                future.set(new WebViewCatalystInstance(context, runtime, queueConfigurationSpec, caller, exceptionHandler));
            }
        });
        return future.get();
    }

    private CatalystInstance createJSCCatalystInstance(Context context, ReactQueueConfigurationSpec queueConfigurationSpec, JSFunctionCaller caller)
            throws Throwable {
        if (DebugHelper.forceWebViewService) {
            MSCLog.throwIfDebug(TAG, new Throwable("testForceWebViewService"));
        }
        ReactBridge.staticInit();
        String engineName = getEngineFullName();
        String appName = context.getPackageName();
        String deviceName = getFriendlyDeviceName();
        if (MSCTraceUtil.TRACE_LEVEL >= MSCTraceUtil.LEVEL_LIGHT) {
            PerfTrace.begin(PerfEventConstant.CREATE_JS_EXECUTOR);
        }
        JavaScriptExecutorFactory executorFactory = getDefaultJSExecutorFactory(appName, deviceName, context);
        JavaScriptExecutor executor = executorFactory.create(engineName);
        if (MSCTraceUtil.TRACE_LEVEL >= MSCTraceUtil.LEVEL_LIGHT) {
            PerfTrace.end(PerfEventConstant.CREATE_JS_EXECUTOR);
        }

        MSCModuleCallExceptionHandler exceptionHandler = new MSCModuleCallExceptionHandler(runtime);
        if (MSCTraceUtil.TRACE_LEVEL >= MSCTraceUtil.LEVEL_LIGHT) {
            PerfTrace.begin(PerfEventConstant.CREATE_CATALYST_INSTANCE);
        }
        CatalystInstanceImpl catalystInstance = new CatalystInstanceImpl(queueConfigurationSpec, executor, caller, exceptionHandler, MSCHornRollbackConfig.readConfig().rollbackMainThreadEngineFilter, false);
        if (MSCTraceUtil.TRACE_LEVEL >= MSCTraceUtil.LEVEL_LIGHT) {
            PerfTrace.end(PerfEventConstant.CREATE_CATALYST_INSTANCE);
        }
        return catalystInstance;
    }

    private CatalystInstance createCatalystInstance(Context context, ReactQueueConfigurationSpec queueConfigurationSpec, JSFunctionCaller caller) {
        staticInit();
        CatalystInstance catalystInstance;
        try {
            catalystInstance = createJSCCatalystInstance(context, queueConfigurationSpec, caller);
        } catch (Throwable throwable) {
            //v8、yoga等so不存在或者加载失败时，会抛异常
            try {
                catalystInstance = createWebViewCatalystInstance(context, queueConfigurationSpec, caller);
            } catch (Throwable t) {
                //WebView创建失败时，会抛异常
                catalystInstance = new EmptyCatalystInstance(queueConfigurationSpec);
            }
        }
        final CatalystInstance finalCatalystInstance = catalystInstance;
        ExecutorContext executorContext = new ExecutorContext() {
            @Override
            public void invokeCallback(int callbackID, Object arguments) {
                if (arguments instanceof NativeMap) {
                    if (finalCatalystInstance instanceof CatalystInstanceImpl) {
                        finalCatalystInstance.invokeCallback(callbackID, RNArguments.fromJavaArgs(new Object[]{arguments}));
                    } else {
                        finalCatalystInstance.invokeCallback(callbackID, JSONDataParser.getJSONArgContainNativeMap(arguments));
                    }
                } else {
                    finalCatalystInstance.invokeCallback(callbackID, JSONDataParser.getInstance().serialize(arguments));
                }
            }

            @Override
            public MSCHandler acquireAsyncMethodHandler() {
                return new MSCHandler() {
                    @Override
                    public void handle(Runnable runnable) {
                        runOnNativeModuleThread(runnable);
                    }
                };
            }
        };
        catalystInstance.setMessageInterface(new RuntimeMessageInterface(runtime, executorContext));
        return catalystInstance;
    }

    public void runOnJSQueueThread(Runnable runnable) {
        mThreadFactory.postOnJSQueueThread(runnable);
    }

    public void runOnJSQueueThreadSafe(Runnable runnable) {
        mThreadFactory.runOnJSQueueThread(runnable);
    }

    public void runOnNativeModuleThread(Runnable runnable) {
        mThreadFactory.postOnNativeModulesQueueThread(runnable);
    }

    public void notifyContextReady() {
        mCatalystInstance.notifyContextReady();
    }

    public boolean isOnJSQueueThread() {
        return mThreadFactory.isOnJSQueueThread();
    }

    public long getHeapStatistics() {
        return mCatalystInstance.getMemoryUsage();
    }

    public static JavaScriptExecutorFactory getDefaultJSExecutorFactory(
            String appName, String deviceName, Context applicationContext) {
        try {
            // If JSC is included, use it as normal
            SoLoader.init(applicationContext, /* native exopackage */ false);
            SoLoader.loadLibraryWithRelink("mscexecutor");
            return new JSCExecutorFactory(appName, deviceName);
        } catch (UnsatisfiedLinkError jscE) {
            // https://github.com/facebook/hermes/issues/78 shows that
            // people who aren't trying to use Hermes are having issues.
            // https://github.com/facebook/react-native/issues/25923#issuecomment-554295179
            // includes the actual JSC error in at least one case.
            //
            // So, if "__cxa_bad_typeid" shows up in the jscE exception
            // message, then we will assume that's the failure and just
            // throw now.

            throw jscE;
        }
    }

    public JSInstance getInstance() {
        return mCatalystInstance;
    }

    public <T extends JavaScriptModule> T getJSModule(Class<T> classOfT) {
        return mCatalystInstance.getJSModule(classOfT);
    }

    public boolean hasActiveCatalystInstance() {
        return !mCatalystInstance.isDestroyed();
    }

    public ReactQueueConfiguration getThreadConfiguration() {
        return mCatalystInstance.getReactQueueConfiguration();
    }

    @Override
    public String toString() {
        return "ServiceInstance{" +
                "INSTANCE_TAG='" + INSTANCE_TAG +
                ", runtime=" + runtime +
                '}';
    }
}
