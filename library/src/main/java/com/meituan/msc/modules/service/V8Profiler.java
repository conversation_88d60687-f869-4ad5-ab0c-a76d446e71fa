package com.meituan.msc.modules.service;

import com.meituan.msc.common.utils.FileUtil;
import com.meituan.msc.jse.bridge.JSInstance;
import com.meituan.msc.modules.reporter.MSCLog;
import com.meituan.msc.util.perf.PerfTrace;

import org.json.JSONException;
import org.json.JSONObject;

import java.io.File;

public class V8Profiler {
    // private final String TAG = "V8Profiler@" + Integer.toHexString(hashCode());

    private static final String V8_PROFILER_NAME = "zzc";
    private volatile Boolean mProfilingStarted = null;
    private final ServiceInstance mServiceInstance;
    private CpuProfilingListener mListener;

    public V8Profiler(ServiceInstance serviceInstance) {
        mServiceInstance = serviceInstance;
    }

    public void setCpuProfilingListener(CpuProfilingListener listener) {
        mListener = listener;
    }

    private static String profilerName(int hash) {
        return V8_PROFILER_NAME + "_" + hash;
    }

    public void startCpuProfiling() {
        MSCLog.i("startCpuProfiling", mProfilingStarted);
        // MSCLog.i(TAG, "[MSC_LOG]startCpuProfiling mProfilingStarted: ", mProfilingStarted,
        //                ",time:", CatalystInstanceImpl.TIME_FORMAT.format(new Date(System.currentTimeMillis())));
        JSInstance jsInstance = mServiceInstance.getInstance();
        if (jsInstance == null) {
            // MSCLog.i(TAG, "[MSC_LOG]startCpuProfiling jsInstance null return," +
            //                    " time:", CatalystInstanceImpl.TIME_FORMAT.format(new Date(System.currentTimeMillis())));
            return;
        }
        if (mProfilingStarted != null) {
            // MSCLog.i(TAG, "[MSC_LOG]startCpuProfiling mProfilingStarted not null return," +
            //                    " time:", CatalystInstanceImpl.TIME_FORMAT.format(new Date(System.currentTimeMillis())));
            return;
        }
        mProfilingStarted = false;
        // MSCLog.i(TAG, "[MSC_LOG]startCpuProfiling mServiceInstance.runOnJSQueueThreadSafe," +
        //                " time:", CatalystInstanceImpl.TIME_FORMAT.format(new Date(System.currentTimeMillis())));
        mServiceInstance.runOnJSQueueThreadSafe(new Runnable() {
            @Override
            public void run() {
                String name = profilerName(mServiceInstance.hashCode());
                // MSCLog.i(TAG, "[MSC_LOG]startCpuProfiling mServiceInstance.runOnJSQueueThreadSafe run() name:", name,
                //                        " time:", CatalystInstanceImpl.TIME_FORMAT.format(new Date(System.currentTimeMillis())));
                jsInstance.startCPUProfiling(name, 1000);
                // MSCLog.i(TAG, "[MSC_LOG]startCpuProfiling mServiceInstance.runOnJSQueueThreadSafe run() after jsInstance.startCPUProfiling,",
                //                        " time:", CatalystInstanceImpl.TIME_FORMAT.format(new Date(System.currentTimeMillis())));
                mProfilingStarted = true;
                if (mListener != null) {
                    mListener.onCpuProfilingStarted(V8Profiler.this);
                }
            }
        });
    }

    public void stopCpuProfiling(String dirPath) {
        MSCLog.i("stopCpuProfiling", mProfilingStarted);
        // MSCLog.i(TAG, "[MSC_LOG]stopCpuProfiling mProfilingStarted: ", mProfilingStarted,
        //                ",time:", CatalystInstanceImpl.TIME_FORMAT.format(new Date(System.currentTimeMillis())));
        JSInstance jsInstance = mServiceInstance.getInstance();
        if (jsInstance == null) {
            // MSCLog.i(TAG, "[MSC_LOG]stopCpuProfiling jsInstance null return," +
            //                    " time:", CatalystInstanceImpl.TIME_FORMAT.format(new Date(System.currentTimeMillis())));
            return;
        }
        if (mProfilingStarted == null || !mProfilingStarted) {
            // MSCLog.i(TAG, "[MSC_LOG]stopCpuProfiling mProfilingStarted null or false. return," +
            //                    " time:", CatalystInstanceImpl.TIME_FORMAT.format(new Date(System.currentTimeMillis())));
            return;
        }
        mServiceInstance.runOnJSQueueThreadSafe(new Runnable() {
            @Override
            public void run() {
                String name = profilerName(mServiceInstance.hashCode());
                String path = new File(dirPath, "js.profile").getPath();
                // MSCLog.i(TAG, "[MSC_LOG]stopCpuProfiling mServiceInstance.runOnJSQueueThreadSafe run() name:", name,",path:", path,
                //                        " time:", CatalystInstanceImpl.TIME_FORMAT.format(new Date(System.currentTimeMillis())));
                jsInstance.stopCPUProfiling(name, path);
                // MSCLog.i(TAG, "[MSC_LOG]stopCpuProfiling mServiceInstance.runOnJSQueueThreadSafe run() after jsInstance.stopCPUProfiling,",
                //                        " time:", CatalystInstanceImpl.TIME_FORMAT.format(new Date(System.currentTimeMillis())));
                long stopTs = PerfTrace.currentTime();
                long stopUnixTs = PerfTrace.currentTimeMillis();
                String threadPath = new File(dirPath, "js.thread").getPath();
                JSONObject jsonObject = new JSONObject();
                try {
                    jsonObject.put("stop_ts", stopTs);
                    jsonObject.put("stop_unix_ts", stopUnixTs);
                    jsonObject.put("pid", android.os.Process.myPid());
                    jsonObject.put("tid", android.os.Process.myTid());
                    jsonObject.put("thread_name", Thread.currentThread().getName());
                    jsonObject.put("thread_id", Thread.currentThread().getId());
                    FileUtil.writeContent(jsonObject.toString(), threadPath);
                } catch (JSONException e) {
                    e.printStackTrace();
                }
                mProfilingStarted = null;
                if (mListener != null) {
                    mListener.onCpuProfilingStopped(V8Profiler.this);
                }
            }
        });
    }

    public boolean isCpuProfiling() {
        return mProfilingStarted != null && mProfilingStarted;
    }

    public interface CpuProfilingListener {

        void onCpuProfilingStarted(V8Profiler profiler);

        void onCpuProfilingStopped(V8Profiler profiler);
    }
}
