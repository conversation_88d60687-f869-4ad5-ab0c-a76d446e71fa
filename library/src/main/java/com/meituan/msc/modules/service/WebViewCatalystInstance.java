package com.meituan.msc.modules.service;

import android.content.Context;
import android.os.AsyncTask;
import android.support.annotation.UiThread;
import android.text.TextUtils;
import android.webkit.JavascriptInterface;
import android.webkit.ValueCallback;

import com.meituan.msc.jse.bridge.CallFunctionContext;
import com.meituan.msc.jse.bridge.CatalystInstance;
import com.meituan.msc.jse.bridge.IMessageInterface;
import com.meituan.msc.jse.bridge.JSFunctionCaller;
import com.meituan.msc.jse.bridge.JavaCallback;
import com.meituan.msc.jse.bridge.JavaFunctionsInterface;
import com.meituan.msc.jse.bridge.JavaScriptModule;
import com.meituan.msc.jse.bridge.JavaScriptModuleRegistry;
import com.meituan.msc.jse.bridge.LazyParseJSONArray;
import com.meituan.msc.jse.bridge.LoadJSCodeCacheCallback;
import com.meituan.msc.jse.bridge.NativeArray;
import com.meituan.msc.jse.bridge.NativeModuleCallExceptionHandler;
import com.meituan.msc.jse.bridge.PendingJSCall;
import com.meituan.msc.jse.bridge.PendingJSCallExecutor;
import com.meituan.msc.jse.bridge.PendingJSCallManager;
import com.meituan.msc.jse.bridge.queue.QueueThreadExceptionHandler;
import com.meituan.msc.jse.bridge.queue.ReactQueueConfiguration;
import com.meituan.msc.jse.bridge.queue.ReactQueueConfigurationImpl;
import com.meituan.msc.jse.bridge.queue.ReactQueueConfigurationSpec;
import com.meituan.msc.jse.common.futures.SimpleSettableFuture;
import com.meituan.msc.modules.engine.MSCRuntime;
import com.meituan.msc.modules.page.render.webview.IWebView;
import com.meituan.msc.modules.page.render.webview.PureWebViewJavaScript;
import com.meituan.msc.modules.page.render.webview.WebViewJavaScript;
import com.meituan.msc.modules.page.render.webview.impl.EmptyWebViewImpl;
import com.meituan.msc.modules.reporter.MSCLog;

import org.json.JSONArray;
import org.json.JSONException;
import org.json.JSONObject;

import java.util.concurrent.ExecutionException;

/**
 * WebView当做逻辑JS引擎，实际中WebView必须在主线程创建和使用，虽然WebView源码只要求在Looper线程使用
 */
public class WebViewCatalystInstance implements CatalystInstance, IBridgeFeToNative, IBridgeNativeToFe,
        PendingJSCallExecutor, QueueThreadExceptionHandler {

    public final String TAG = "WebViewCatalystInstance@" + Integer.toHexString(hashCode());

    private final ReactQueueConfiguration mReactQueueConfiguration;
    private volatile boolean mDestroyed = false;
    private final JavaScriptModuleRegistry mJSModuleRegistry;
    private final PendingJSCallManager mPendingJSCallManager;

    private final NativeModuleCallExceptionHandler mNativeModuleCallExceptionHandler;
    private IMessageInterface messageInterface;
    private final JSFunctionCaller mJSFunctionCaller;

    private final IWebView mWebView;
    private final IThreadFactory mThreadFactory;

    private static IWebView getServiceWebView(Context context, MSCRuntime runtime) {
        IWebView webView;
        try {
            webView = runtime.webViewCacheManager.getSimpleWebView(context, runtime.getAppId(), "msc-service");
        } catch (Exception ignore) {
            webView = new EmptyWebViewImpl();
        }
        return webView;
    }

    @Override
    public String getName() {
        return "WebView";
    }

    @UiThread
    public WebViewCatalystInstance(Context context,
                                   MSCRuntime runtime,
                                   ReactQueueConfigurationSpec queueConfigurationSpec,
                                   JSFunctionCaller caller,
                                   NativeModuleCallExceptionHandler nativeModuleCallExceptionHandler) {
        mReactQueueConfiguration = ReactQueueConfigurationImpl.create(queueConfigurationSpec, this);
        mThreadFactory = new ReactThreadFactory(mReactQueueConfiguration);
        mJSModuleRegistry = new JavaScriptModuleRegistry();
        mPendingJSCallManager = new PendingJSCallManager(this);
        mJSFunctionCaller = caller;
        mNativeModuleCallExceptionHandler = nativeModuleCallExceptionHandler;
        mWebView = getServiceWebView(context, runtime);
        mWebView.addJavascriptInterface(this, "WebViewServiceBridge");
    }

    private void log(Object... objects) {
        MSCLog.d(TAG, objects);
    }

    @Override
    public void setMessageInterface(IMessageInterface messageInterface) {
        this.messageInterface = messageInterface;
    }

    @UiThread
    @Override
    public void destroy() {
        if (mDestroyed) {
            return;
        }
        mDestroyed = true;
        //跟CatalystInstanceImpl保持一致
        mThreadFactory.runOnNativeModulesQueueThread(() -> mThreadFactory.runOnJSQueueThread(() -> mThreadFactory.runOnUiThread(() -> {
            mWebView.onDestroy();
            AsyncTask.execute(mReactQueueConfiguration::destroy);
        })));
    }

    @Override
    public long getJSRuntimePtr() {
        // ignore
        return -1;
    }


    @Override
    public boolean isDestroyed() {
        if (mDestroyed) {
            log("destroyed");
        }
        return mDestroyed;
    }

    @Override
    public ReactQueueConfiguration getReactQueueConfiguration() {
        return mReactQueueConfiguration;
    }

    @Override
    public void setGlobalVariableString(String propName, String stringValue) {
        // FIXME by chendacai 这里的实现没有考虑字符串中包含'和"的场景，谨慎使用，后面优化
        evaluateJavascript(PureWebViewJavaScript.from(String.format("%s='%s'", propName, stringValue)), null);
    }

    @Override
    public void changeV8InspectorName(String name) {
    }

    @Override
    public void notifyContextReady() {
        mPendingJSCallManager.acceptCalls();
    }

    @Override
    public void callFunction(String module, String method, JSONArray arguments) {
        if (isDestroyed()) {
            return;
        }
        mPendingJSCallManager.cacheOrAcceptCall(new PendingJSCall(module, method, arguments));
    }

    @Override
    public void invokeCallback(int callbackID, JSONArray arguments) {
        invokeCallback(callbackID, arguments.toString());
    }

    private void invokeCallback(int callbackID, String arguments) {
        if (isDestroyed()) {
            return;
        }
        invokeCallbackAndReturnFlushedQueue(callbackID, arguments, null);
    }

    /**
     * 预拉取数据传递类型为 WritableNativeMap, 但是 WebView 环境不支持该类型,
     * 所以外部直接调用这个方法是没用的, 会导致流程中断
     * 当前处理方案是在外部转换数据, 然后调用 {@link WebViewCatalystInstance#invokeCallback(int, org.json.JSONArray)}
     */
    @Override
    public void invokeCallback(int callbackID, NativeArray arguments) {
        //TODO:理论上不应该支持，因为NativeArray依赖ReactBridge
    }

    @Override
    public void garbageCollect() {

    }

    @Override
    public long getMemoryUsage() {
        return 0;
    }

    @Override
    public void startCPUProfiling(String profilerName, int interval) {
    }

    @Override
    public void stopCPUProfiling(String profilerName, String traceFilePath) {
    }

    @Override
    public <T extends JavaScriptModule> T getJSModule(Class<T> jsInterface) {
        return mJSModuleRegistry.getJavaScriptModule(mJSFunctionCaller == null ? this : mJSFunctionCaller, jsInterface);
    }

    @Override
    public void registerJSObject(String name, JavaFunctionsInterface functionsInterface) {
        mThreadFactory.runOnUiThread(() -> mWebView.addJavascriptInterface(functionsInterface, name));
    }

    @Override
    public void registerJavaCallback(String functionName, JavaCallback callback) {
    }

    @Override
    public String executeJSFunction(String moduleName, String methodName, String params) {
        if (TextUtils.isEmpty(moduleName) || TextUtils.isEmpty(methodName) || TextUtils.isEmpty(params)) {
            return null;
        }
        return executeFunctionSync(moduleName, methodName, params);
    }

    private String executeFunctionSync(String moduleName, String methodName, String params) {
        SimpleSettableFuture<String> future = new SimpleSettableFuture<>();
        callFunctionReturnFlushedQueue(moduleName, methodName, params, future::set);
        try {
            return future.get();
        } catch (InterruptedException | ExecutionException e) {
            return null;
        }
    }

    @Override
    public String executeListFunction(String moduleName, String methodName, String jsModuleName, String jsMethodName, String params) {
        if (TextUtils.isEmpty(moduleName) || TextUtils.isEmpty(methodName) || TextUtils.isEmpty(jsModuleName) || TextUtils.isEmpty(jsMethodName) || TextUtils.isEmpty(params)) {
            return null;
        }
        //TODO:可能耗时
        try {
            JSONArray jsonArray = new JSONArray(params);
            jsonArray.put(0, jsModuleName);
            jsonArray.put(1, jsMethodName);
            return executeFunctionSync(moduleName, methodName, jsonArray.toString());
        } catch (JSONException ignore) {
        }
        return null;
    }

    @Override
    public String evaluateJavaScript(String script, String assetURL, String codeCacheFile, LoadJSCodeCacheCallback loadJSCodeCacheCallback) {
        return evaluateJavascriptSync(script);
    }

    @Override
    public void handleMemoryPressure(int level) {

    }

    private static boolean isStringInvalid(String s) {
        return s == null || s.length() <= 0 || "null".equalsIgnoreCase(s);
    }

    //js->N，同步调用，不需要判断是否destroy
    @JavascriptInterface
    @Override
    public String getNativeModuleConfig(String moduleName) {
        if (isStringInvalid(moduleName)) {
            return null;
        }
        if (messageInterface == null) {
            return null;
        }
        return messageInterface.getConfig(moduleName).toString();
    }

    //js->N，同步调用，不需要判断是否destroy
    @JavascriptInterface
    @Override
    public String nativeCallSyncHook(String moduleName, String methodName, String params) {
        if (isStringInvalid(moduleName) || isStringInvalid(methodName)) {
            return null;
        }
        if (messageInterface == null) {
            return null;
        }
        Object object = messageInterface.invokeSync(CallFunctionContext.DO_NOTHING_CONTEXT, moduleName, methodName, new LazyParseJSONArray(params));
        try {
            JSONObject jsonObject = new JSONObject();
            jsonObject.put("data", object);
            return jsonObject.toString();
        } catch (JSONException e) {
            MSCLog.e(TAG, e);
        }
        return null;
    }

    @JavascriptInterface
    @Override
    public void nativeFlushQueueImmediate(String queue) {
        if (isStringInvalid(queue)) {
            return;
        }
        if (messageInterface == null) {
            return;
        }
        messageInterface.batchInvoke(queue);
    }

    @Override
    public void invokeCallbackAndReturnFlushedQueue(int callbackID, String arguments, ValueCallback<String> callback) {
        String script = String.format("javascript:window.__jsBatchedBridge.invokeCallbackAndReturnFlushedQueue('%s', %s)", callbackID, arguments);
        evaluateJavascript(PureWebViewJavaScript.from(script), value -> {
            if (callback != null) {
                callback.onReceiveValue(value);
            }
            //TODO:确保在调用线程执行
            mThreadFactory.runOnJSQueueThread(() -> nativeFlushQueueImmediate(value));
        });
    }

    @Override
    public void callFunctionReturnFlushedQueue(String module, String method, String arguments, ValueCallback<String> callback) {
        String script = String.format("javascript:window.__jsBatchedBridge.callFunctionReturnFlushedQueue('%s', '%s', %s)", module, method, arguments);
        evaluateJavascript(PureWebViewJavaScript.from(script), value -> {
            if (callback != null) {
                callback.onReceiveValue(value);
            }
            //TODO:确保在调用线程执行
            mThreadFactory.runOnJSQueueThread(() -> nativeFlushQueueImmediate(value));
        });
    }

    private void evaluateJavascript(WebViewJavaScript script, ValueCallback<String> callback) {
        mThreadFactory.runOnUiThread(() -> mWebView.evaluateJavascript(script, value -> {
            log("evaluateJavascript, script:", script);
            log("evaluateJavascript, value:", value);
            if (callback != null) {
                callback.onReceiveValue(value);
            }
        }));
    }

    private String evaluateJavascriptSync(String script) {
        SimpleSettableFuture<String> future = new SimpleSettableFuture<>();
        evaluateJavascript(PureWebViewJavaScript.from(script), future::set);
        try {
            return future.get();
        } catch (InterruptedException | ExecutionException e) {
            return null;
        }
    }

    @Override
    public void execute(PendingJSCall call) {
        callFunctionReturnFlushedQueue(call.mModule, call.mMethod, call.argumentsString(), null);
    }

    @Override
    public void handleException(Exception e) {
        mNativeModuleCallExceptionHandler.handleException(e);
    }
}
