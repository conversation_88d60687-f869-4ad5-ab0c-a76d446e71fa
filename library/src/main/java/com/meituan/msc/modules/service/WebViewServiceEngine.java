//package com.meituan.msc.modules.service;
//
//import android.content.Context;
//import android.os.Handler;
//import android.os.Looper;
//import android.support.annotation.Nullable;
//import android.webkit.ValueCallback;
//
//import com.meituan.dio.easy.DioFile;
//import com.meituan.msc.common.framework.interfaces.IBridge2Native;
//import com.meituan.msc.common.support.java.util.concurrent.CompletableFuture;
//import com.meituan.msc.common.utils.CollectionUtil;
//import com.meituan.msc.common.utils.StorageUtil;
//import com.meituan.msc.jse.bridge.JSInstance;
//import com.meituan.msc.jse.bridge.JavaScriptModule;
//import com.meituan.msc.modules.devtools.DebugHelper;
//import com.meituan.msc.modules.engine.MSCApp;
//import com.meituan.msc.modules.engine.MSCRuntime;
//import com.meituan.msc.modules.page.render.webview.MSCWebView;
//import com.meituan.msc.modules.page.render.webview.MSCWebViewRenderer;
//import com.meituan.msc.modules.page.render.webview.OnEngineInitFailedListener;
//import com.meituan.msc.modules.page.render.webview.TemplateHelper;
//import com.meituan.msc.modules.reporter.MSCLog;
//import com.meituan.msc.modules.reporter.PerformanceReporter;
//
//import java.util.Collection;
//
//import static com.meituan.msc.modules.page.render.webview.MSCWebView.TYPE_SERVICE;
//
///**
// * not work in msc
// */
//public class WebViewServiceEngine implements IServiceEngine {
//    private static final String BLANK_HTML = "about:blank";
//    private MSCWebView webView;
//    private MSCRuntime mMSCRuntime;
//    private Handler handler = new Handler(Looper.getMainLooper());
//    private IBridge2Native bridge2Native;
//    private OnEngineInitFailedListener mOnEngineInitFailedListener;
//
//    private PerformanceReporter mReporter;
//    @Override
//    public void launch(MSCRuntime runtime, Context context, CompletableFuture future) {
//        final Context applicationContext = context.getApplicationContext();
//        if (Looper.myLooper() == Looper.getMainLooper()) {
//            prepareWebView(applicationContext);
//        } else {
//            //webView 创建和适用要在一个进程中
//            handler.post(new Runnable() {
//                @Override
//                public void run() {
//                    prepareWebView(applicationContext);
//                }
//            });
//        }
//    }
//
//    private void prepareWebView(Context context){
//        webView = new MSCWebView(context, mMSCRuntime, TYPE_SERVICE);
//        webView.setOnEngineInitFailedListener(mOnEngineInitFailedListener);
////        webView.setJsHandler(bridge2Native);
//        webView.loadUrl(BLANK_HTML);
//        webView.evaluateJavascript("platform='Android'", null);
//    }
//
//    @Override
//    public void setReporter(PerformanceReporter reporter) {
//        mReporter = reporter;
//    }
//
//    @Override
//    public void relaunch() {
//        handler.post(new Runnable() {
//            @Override
//            public void run() {
//                webView.loadUrl(BLANK_HTML);
//            }
//        });
//    }
//
//    @Override
//    public void setJsHandler(IBridge2Native handler) {
//        bridge2Native = handler;
//    }
//
//    @Override
//    public void release() {
//        handler.postDelayed(new Runnable() {
//            @Override
//            public void run() {
//                webView.onDestroy();
//            }
//        }, RELEASE_DELAY_MILLIS);
//    }
//
//    @Override
//    public void evaluateJavascript(String tag, final String script, final @Nullable ValueCallback<String> callback) {
//        handler.post(new Runnable() {
//            @Override
//            public void run() {
//                final long start = System.currentTimeMillis();
//                ValueCallback<String> resultCallback = new ValueCallback<String>() {
//                    @Override
//                    public void onReceiveValue(String value) {
//                        long cost = System.currentTimeMillis() - start;
//                        if (script.length() > 1000) {
//                            MSCLog.d("evaluateJavascript", cost + " " + script.length() + " ");
//                        }
//                        if (callback != null) {
//                            callback.onReceiveValue(value);
//                        }
//                    }
//                };
//                webView.evaluateJavascript(script, resultCallback);
//            }
//        });
//    }
//
//    @Override
//    public void evaluateJsFilesCombo(Collection<DioFile> files, String source, @Nullable ValueCallback<String> resultCallback) {
//        if (files == null) {
//            return;
//        }
//        if (DebugHelper.debugService) {
//            //debugWebView 注入script标签是个异步的操作。
//            handler.post(new Runnable() {
//                @Override
//                public void run() {
//                    for (DioFile file : files) {
//                        //document.write 效率低 不建议使用
//                        webView.evaluateJavascript(String.format(TemplateHelper.APPEND_SCRIPT, StorageUtil.SCHEME_LOCAL_FILE + file.getAbsolutePath(), false), resultCallback);
//                    }
//                }
//            });
//        } else {
//            String finalS = MSCFileUtils.concatComboFileString(files,resultCallback);
//            handler.post(new Runnable() {
//                @Override
//                public void run() {
//                    webView.evaluateJavascript(finalS, resultCallback);
//                }
//            });
//        }
//    }
//
//    @Override
//    public void evaluateJsFile(final DioFile file, @Nullable final ValueCallback<String> resultCallback) {
//        evaluateJsFilesCombo(CollectionUtil.asList(file), file.getAbsolutePath(), resultCallback);
//    }
//
//    @Override
//    public void setOnEngineInitFailedListener(OnEngineInitFailedListener onEngineInitFailedListener) {
//        mOnEngineInitFailedListener = onEngineInitFailedListener;
//    }
//
//    @Override
//    public void setOnJsUncaughtErrorHandler(Thread.UncaughtExceptionHandler onJsUncaughtErrorHandler) {
//        //ignore
//    }
//
//    @Override
//    public <T extends JavaScriptModule> T getJSModule(Class<T> classOfT) {
//        return null;
//    }
//
//    @Override
//    public JSInstance getJSInstance() {
//        return null;
//    }
//
//    @Override
//    public ServiceModule getJsExecutor() {
//        return null;
//    }
//
//    @Override
//    public EngineStatus getEngineStatus() {
//        return null;
//    }
//}
