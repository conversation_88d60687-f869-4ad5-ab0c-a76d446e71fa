package com.meituan.msc.modules.service.codecache;

import static com.meituan.msc.common.constant.APPIDConstants.TIAN_XING_JIAN;

import android.support.annotation.Keep;
import android.text.TextUtils;

import com.meituan.msc.common.constant.APPIDConstants;
import com.meituan.msc.jse.bridge.ReactBridge;
import com.meituan.msc.lib.interfaces.BaseRemoteConfig;
import com.meituan.msc.modules.engine.MSCApp;
import com.meituan.msc.modules.engine.MSCHornRollbackConfig;
import com.meituan.msc.modules.storage.StorageManageUtil;

import java.util.HashSet;
import java.util.Set;

public class CodeCacheConfig extends BaseRemoteConfig<CodeCacheConfig.Config> {
    public static final CodeCacheConfig INSTANCE = new CodeCacheConfig();

    @Keep
    public static class Config {
        // 用于gson及其他正常创建的情况
        public Config() {
            preDownloadBlackList = new HashSet<>();
            preDownloadBlackList.add(TIAN_XING_JIAN);
            appBlackList = new HashSet<>();
            appBlackList.add(APPIDConstants.FANG_XIN_TUI);
            appBlackList.add(APPIDConstants.ZHI_NENG_JIAO_FU);
        }

        public boolean enableCodeCache = true;

        public Set<String> appBlackList = null;

        /**
         * 禁止在预下载的时候生成CodeCache的黑名单
         */
        public Set<String> preDownloadBlackList;

        /**
         * minJSFileSize说明，单位B
         * 需求：https://km.sankuai.com/collabpage/1940658116
         * 默认值依据：基础库包中总共有101个JS，其中有58个文件小于等于2KB，如果阈值设置为2KB，会过滤近50%的小JS文件
         */
        public int minJSFileSize = 2048;

        public int createDelayTimesInSeconds = 2;

        public boolean enableUsageReport = true;

        public int minUsageReportIntervalInHour = 24;

        public boolean attachDirectoryAsync = false;

        public boolean writeRecordInTemporary = true;

        public boolean enableLru = true;

        public int lruCacheLimit = 80;

        public boolean enableCodeCacheAfterCreate = true;

        public boolean enableCodeCacheReportSuccess = false;

        public boolean enableCodeCacheReportFail = true;
    }

    private CodeCacheConfig() {
        super("msc_code_cache", Config.class);
    }

    @Override
    protected void onRemoteConfigChanged(String rawConfigString) {
        super.onRemoteConfigChanged(rawConfigString);
        if (TextUtils.isEmpty(rawConfigString)) {
            return;
        }
        Config tempConfig = parseRemoteConfig(rawConfigString);
        if (config != null && INSTANCE != null) {
            INSTANCE.config.enableLru = tempConfig.enableLru;
            INSTANCE.config.enableCodeCache = tempConfig.enableCodeCache;
        }
    }

    /**
     * 最小的生成CodeCache的JS文件大小，单位是B
     *
     * @return
     */
    public int getMinJSFileSize() {
        return config.minJSFileSize;
    }

    /**
     * CodeCache 总开关
     *
     * @return
     */
    public boolean isEnableCodeCache() {
        return config.enableCodeCache && StorageManageUtil.getLRUMaxSize() > 0;
    }


    public boolean isEnableUsageReport() {
        return config.enableUsageReport;
    }

    public int getMinUsageReportIntervalInHour() {
        return config.minUsageReportIntervalInHour;
    }

    public boolean isEnableCodeCache(MSCApp mscApp) {
        return isEnableCodeCache(mscApp != null ? mscApp.getAppId() : null);
    }

    /**
     * 是否允许指定的appId使用CodeCache
     *
     * @param appId
     * @return
     */
    public boolean isEnableCodeCache(String appId) {
        if (!isEnableCodeCache()) {
            return false;
        }
        // 如果在黑名单中就不能使用
        if (config.appBlackList != null && config.appBlackList.contains(appId)) {
            return false;
        }
        return ReactBridge.isInitialized();
    }

    /**
     * 获取延迟执行的时间
     *
     * @return
     */
    public int getExecuteDelayTimesInSeconds() {
        return config.createDelayTimesInSeconds;
    }

    /**
     * 是否异步执行绑定ddd包名和codeCache目录
     *
     * @return
     */
    public boolean isAttachDirectoryAsync() {
        return config.attachDirectoryAsync;
    }

    /**
     * 是否在临时文件写入codeCache数据
     *
     * @return
     */
    public boolean isWriteRecordInTemporary() {
        return config.writeRecordInTemporary;
    }

    /**
     * LRU 开关
     *
     * @return
     */
    public boolean isEnableLru() {
        return config.enableLru;
    }

    /**
     * LRU Cache Limit
     *
     * @return
     */
    public int getLruCacheLimit() {
        return config.lruCacheLimit;
    }

    /**
     * 开启创建codeCache之后的清理工作
     *
     * @return
     */
    public boolean isCodeCacheAfterCreateEnable() {
        return config.enableCodeCacheAfterCreate;
    }

    public boolean isCodeCacheReportSuccess() {
        return config.enableCodeCacheReportSuccess;
    }

    public boolean isCodeCacheReportFail() {
        return config.enableCodeCacheReportFail;
    }

    public boolean isBlockedToCreateCodeCacheWhenPreDownload(String appId) {
        return config.preDownloadBlackList != null && config.preDownloadBlackList.contains(appId);
    }
}
