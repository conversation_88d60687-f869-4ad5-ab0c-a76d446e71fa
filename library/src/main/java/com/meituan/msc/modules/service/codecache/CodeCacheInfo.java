package com.meituan.msc.modules.service.codecache;

import android.text.TextUtils;

import com.meituan.dio.easy.DioFile;
import com.meituan.msc.modules.engine.MSCRuntime;
import com.meituan.msc.modules.update.packageattachment.PackageAttachment;
import com.meituan.msc.modules.update.packageattachment.PackageAttachmentManager;
import com.meituan.msc.modules.update.bean.PackageInfoWrapper;

import javax.annotation.Nullable;

class CodeCacheInfo extends CodeCacheKey {
    private final PackageInfoWrapper packageInfo;
    private final DioFile jsFile;
    private final String sourceUri;
    private String md5OfPackage;
    private String taskKey;
    private PackageAttachment packageAttachment;
    @Nullable
    private final MSCRuntime mscRuntime;

    public CodeCacheInfo(String appId, String appVersion, PackageInfoWrapper packageInfo, DioFile jsFile, String sourceUri, @Nullable MSCRuntime mscRuntime) {
        super(appId, appVersion == null ? packageInfo.getVersion() : appVersion, packageInfo.getDDResourceName(), jsFile.getChildFilePath());
        this.packageInfo = packageInfo;
        this.jsFile = jsFile;
        this.sourceUri = sourceUri;
        this.mscRuntime = mscRuntime;
    }

    public String getMd5OfPackage() {
        if (md5OfPackage == null) {
            String md5OfPackage = packageInfo.getMd5();
            if (TextUtils.isEmpty(md5OfPackage)) {
                md5OfPackage = String.valueOf(packageInfo.getLocalPath().hashCode());
            }
            this.md5OfPackage = md5OfPackage;
        }
        return md5OfPackage;
    }

    public String getRelativePathOfJsFile() {
        return jsFile.getChildFilePath();
    }

    public DioFile getJsFile() {
        return jsFile;
    }

    public String getSourceUri() {
        return sourceUri;
    }

    public MSCRuntime getMscRuntime() {
        return mscRuntime;
    }

    public String getTaskKey() {
        if (taskKey == null) {
            taskKey = "cc_" + getMd5OfPackage() + getRelativePathOfJsFile();
        }
        return taskKey;
    }

    public PackageAttachment getPackageAttachment() {
        if (packageAttachment == null) {
            packageAttachment = PackageAttachmentManager.getInstance().getAttachment(packageInfo);
        }
        return packageAttachment;
    }

    public PackageInfoWrapper getPackageInfo() {
        return packageInfo;
    }

}
