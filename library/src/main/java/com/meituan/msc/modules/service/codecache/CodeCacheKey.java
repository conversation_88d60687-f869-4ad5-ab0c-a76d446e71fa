package com.meituan.msc.modules.service.codecache;

import android.support.annotation.Keep;

/**
 * 确定CodeCache文件唯一性的Key
 */
@Keep
class CodeCacheKey {
    public CodeCacheKey(String appId, String appVersion, String packageName, String jsFileRelativePath) {
        this.appId = appId;
        this.appVersion = appVersion;
        this.packageName = packageName;
        this.jsFileRelativePath = jsFileRelativePath;
    }

    public String getAppId() {
        return appId;
    }

    public String getAppVersion() {
        return appVersion;
    }

    public String getPackageName() {
        return packageName;
    }

    public String getJsFileRelativePath() {
        return jsFileRelativePath;
    }

    private final String appId;
    private final String appVersion;
    /**
     * 包名
     */
    private final String packageName;
    /**
     * js文件在包内的相对路径
     */
    private final String jsFileRelativePath;
}
