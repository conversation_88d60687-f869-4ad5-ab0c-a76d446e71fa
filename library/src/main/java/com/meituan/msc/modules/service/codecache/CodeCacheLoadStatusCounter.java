package com.meituan.msc.modules.service.codecache;

import com.meituan.msc.jse.bridge.LoadJSCodeCacheCallback;

import java.io.File;
import java.util.Arrays;
import java.util.concurrent.atomic.AtomicInteger;

public class CodeCacheLoadStatusCounter {
    private final int[] statusCountArray = new int[LoadJSCodeCacheCallback.LoadStatus.values().length];

    public int getCount(LoadJSCodeCacheCallback.LoadStatus status) {
        return status != null ? statusCountArray[status.ordinal()] : null;
    }

    public void increment(LoadJSCodeCacheCallback.LoadStatus status) {
        if (status == null) {
            return;
        }
        statusCountArray[status.ordinal()] += 1;
    }

    public Snapshot minus(Snapshot snapshot) {
        Snapshot current = getSnapshot();
        for (int i = 0; i < current.statusCountArray.length; i++) {
            current.statusCountArray[i] -= snapshot.statusCountArray[i];
        }
        return current;
    }

    public Snapshot getSnapshot() {
        return new Snapshot(Arrays.copyOf(statusCountArray, statusCountArray.length));
    }

    public static class Snapshot {
        private final int[] statusCountArray;

        private Snapshot(int[] statusCountArray) {
            this.statusCountArray = statusCountArray;
        }

        public int getCount(LoadJSCodeCacheCallback.LoadStatus status) {
            return status != null ? statusCountArray[status.ordinal()] : null;
        }
    }
}
