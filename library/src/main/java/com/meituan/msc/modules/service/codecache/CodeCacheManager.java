package com.meituan.msc.modules.service.codecache;

import android.content.Context;
import android.support.annotation.NonNull;
import android.text.TextUtils;

import com.meituan.dio.easy.DioFile;
import com.meituan.msc.common.report.MetricsEntry;
import com.meituan.msc.extern.MSCEnvHelper;
import com.meituan.msc.jse.bridge.JSCodeCacheCreator;
import com.meituan.msc.jse.bridge.LoadJSCodeCacheCallback;
import com.meituan.msc.modules.engine.MSCHornRollbackConfig;
import com.meituan.msc.modules.engine.MSCRuntime;
import com.meituan.msc.modules.engine.MSCRuntimeHelper;
import com.meituan.msc.modules.engine.MSCRuntimeReporter;
import com.meituan.msc.modules.preload.executor.TaskExecutor;
import com.meituan.msc.modules.reporter.MSCLog;
import com.meituan.msc.modules.reporter.MSCReporter;
import com.meituan.msc.modules.reporter.ReporterFields;
import com.meituan.msc.modules.update.bean.PackageInfoWrapper;
import com.meituan.msc.modules.update.packageattachment.PackageAttachmentManager;

import java.io.File;
import java.lang.ref.WeakReference;

public class CodeCacheManager {
    private static final String TAG = "CodeCache";

    private static volatile CodeCacheManager sInstance;

    private final CodeCacheUsageInfoManager usageInfoManager;

    public static CodeCacheManager getInstance() {
        if (sInstance == null) {
            synchronized (CodeCacheManager.class) {
                if (sInstance == null) {
                    Context context = MSCEnvHelper.getContext();
                    if (context == null) {
                        throw new IllegalArgumentException("Invalid context argument");
                    }
                    sInstance = new CodeCacheManager(context, PackageAttachmentManager.createInstance(context));
                }
            }
        }
        return sInstance;
    }

    private final TaskExecutor mTaskExecutor;

    private CodeCacheManager(Context context, PackageAttachmentManager packageAttachmentManager) {
        mTaskExecutor = packageAttachmentManager.getTaskExecutor();
        usageInfoManager = CodeCacheConfig.INSTANCE.isEnableUsageReport() ? new CodeCacheUsageInfoManager(context, mTaskExecutor) : null;
    }

    public String getCodeCacheFilePath(MSCRuntime runtime, PackageInfoWrapper packageInfo, DioFile jsFile, String sourceUri, boolean asyncCreateIfAbsent) {
        if (packageInfo == null || !CodeCacheConfig.INSTANCE.isEnableCodeCache(packageInfo.appId)) {
            return null;
        }
        if (MSCHornRollbackConfig.enableExternalAppCodeCacheLimit()) {
            if (runtime != null) {
                boolean isExternalApp = runtime.getMSCAppModule().getExternalApp();
                if (isExternalApp) {
                    return null;
                }
            }
        }
        CodeCacheInfo codeCacheInfo = new CodeCacheInfo(packageInfo.appId, MSCRuntimeHelper.getAppVersion(runtime), packageInfo, jsFile, sourceUri, runtime);
        String codeCacheFilePath = getCodeCacheFile(codeCacheInfo).getAbsolutePath();
        if (asyncCreateIfAbsent) {
            createCodeCacheAsync(codeCacheInfo);
        }
        return codeCacheFilePath;
    }

    public LoadJSCodeCacheCallback getLoadJSCodeCacheCallback(@NonNull MSCRuntime mscRuntime, DioFile jsFile) {
        return new MyLoadJSCodeCacheCallback(mscRuntime);
    }

    public File getCodeCacheFile(CodeCacheInfo codeCacheInfo) {
        return codeCacheInfo.getPackageAttachment().getAttachFile("codecache/" + codeCacheInfo.getRelativePathOfJsFile()).getAbsoluteFile();
    }

    private void createCodeCacheAsync(CodeCacheInfo codeCacheInfo) {
        createCodeCacheAsync(codeCacheInfo, CodeCacheConfig.INSTANCE.getExecuteDelayTimesInSeconds() * 1000);
    }

    private void createCodeCacheAsync(CodeCacheInfo codeCacheInfo, int delay) {
//        if (!canCreateCodeCache(codeCacheInfo, true)) {
//            return;
//        }
        mTaskExecutor.addDelayedTask(new CreateCodeCacheTask(this, codeCacheInfo), delay);
    }

    public void createCodeCacheAsync(String appId, String appVersion, PackageInfoWrapper packageInfo) {
        if (!CodeCacheConfig.INSTANCE.isEnableCodeCache(packageInfo.appId)) {
            return;
        }
        mTaskExecutor.addTask(new CreatePackageCodeCacheTask(this, appId, appVersion, packageInfo));
    }

    public boolean existCodeCache(CodeCacheInfo codeCacheInfo) {
        File codeCache = getCodeCacheFile(codeCacheInfo);
        return codeCache != null && codeCache.exists();
    }

    boolean canCreateCodeCache(CodeCacheInfo codeCacheInfo, boolean strict) {
        if (codeCacheInfo == null) {
            return false;
        }

        if (!CodeCacheConfig.INSTANCE.isEnableCodeCache(codeCacheInfo.getAppId())) {
            return false;
        }

        // 判断是否已经生成过了
        if (existCodeCache(codeCacheInfo)) {
            return false;
        }
        // 判断JS大小是否满足条件
        if (!checkJSFileSize(codeCacheInfo)) {
            return false;
        }
        // 判断是否三方小程序
        if (MSCHornRollbackConfig.enableExternalAppCodeCacheLimit()) {
            MSCRuntime runtime = codeCacheInfo.getMscRuntime();
            if (runtime != null) {
                boolean isExternalApp = runtime.getMSCAppModule().getExternalApp();
                return !isExternalApp;
            }
        }
        return true;
    }

    /**
     * 检查JS文件大小
     *
     * @return 是否满足<minJSFileSize条件
     */
    public boolean checkJSFileSize(CodeCacheInfo codeCacheInfo) {
        if (CodeCacheConfig.INSTANCE.getMinJSFileSize() <= 0) {
            // minJSFileSize未设置有效值，不过滤
            return true;
        }
        return codeCacheInfo.getJsFile().length() >= CodeCacheConfig.INSTANCE.getMinJSFileSize();
    }

    public void removeCodeCache(CodeCacheInfo codeCacheInfo) {
        getCodeCacheFile(codeCacheInfo).delete();
    }

    void createCodeCache(CodeCacheInfo codeCacheInfo) {
        // 空间收缩
//        shrinkSpace();
        // 创建

        createCodeCacheInner(codeCacheInfo);


//        String bundleCompleteName = codeCacheInfo.getStoreKey();
//        // 判断是否创建成功
//        if (!existCodeCache(codeCacheInfo)) {
//            return;
//        }
//        long storageSize = FileUtil.getTotalStorageSize(getCodeCacheDirectory(bundleCompleteName));
//        if (storageSize <= 0) {
//            return;
//        }
        // 创建成功后添加占用空间的数据
//        CodeCacheStorageInfo storageInfo = new CodeCacheStorageInfo();
//        storageInfo.setBundleName(codeCacheInfo.name);
//        storageInfo.setBundleVersion(codeCacheInfo.version);
//        storageInfo.setStorageSize(storageSize);
//        mStorageInfoStore.set(storageInfo);
    }

    private void createCodeCacheInner(CodeCacheInfo codeCacheInfo) {
        // 准备目录，并将目录写入记录中
        codeCacheInfo.getPackageAttachment().prepareDirectory();
        File codeCacheFile = getCodeCacheFile(codeCacheInfo);
        DioFile jsFile = codeCacheInfo.getJsFile();
        if (!jsFile.exists()) {
            return;
        }
        String codeCacheFilePath = codeCacheFile.getAbsolutePath();
        boolean success = false;
        try {
            success = JSCodeCacheCreator.createCodeCacheFromDioFile(jsFile.getLocalFile().getAbsolutePath(), jsFile.getChildFilePath(), codeCacheInfo.getSourceUri(), codeCacheFilePath);
        } catch (Throwable t) {
            reportCodeCacheError(codeCacheInfo, t.toString());
//            throw t;
        }
        MSCLog.i("CodeCacheManager", "Create code cache: ", codeCacheInfo.getJsFile(), ", CodeCacheFile: ", codeCacheFile, ", success: ", success);
        if (success && usageInfoManager != null) {
            reportCodeCacheSuccess(codeCacheInfo);
            usageInfoManager.onCreateCodeCacheInner(codeCacheInfo, codeCacheFile);
        } else {
            if (!success) {
                reportCodeCacheError(codeCacheInfo, "codeCache is not exist!");
            } else {
                reportCodeCacheError(codeCacheInfo, "usageInfoManager is null!");
            }
        }
    }

    public void recordCodeCacheUsage(String appId, String packageName, String jsFileRelativePath) {
        if (usageInfoManager != null) {
            usageInfoManager.recordCodeCacheUsage(appId, packageName, jsFileRelativePath);
        }
    }

//    public void removeCodeCache(String bundleName, String bundleVersion, int reason) {
//        FLog.i("[CodeCacheManager@removeCodeCache]", bundleName + " " + bundleVersion + " " + reason);
//        //reason: 0:因为内存超出导致remove，1:因为移除CodeCache白名单导致删除, 2:超过时间，统一移除
//        if (reason == REASON_CACHE_SIZE || reason == REASON_DISABLE) { //2和3上报量太大
//            MRNDashboard.newInstance().appendTag(MRNDashboard.KEY_MRN_BUNDLE_NAME, bundleName)
//                    .appendTag(MRNDashboard.KEY_MRN_BUNDLE_VERSION, bundleVersion)
//                    .appendTag("reason", String.valueOf(reason)).sendKV("MRNCodeCacheRemove", 1);
//        }
//
//        String bundleCompleteName = MRNBundle.getStoreKey(bundleName, bundleVersion);
//        // 删除目录
//        FileUtil.deleteDirectory(getCodeCacheDirectory(bundleCompleteName));
//        // 删除占用空间数据
//        mStorageInfoStore.remove(bundleCompleteName);
//    }
//
//    public void clearOutOfDateCodeCache() {
//        Set<String> toRemoveBundleNames = new HashSet<>();
//        Map<String, BundleUsageInfo> map = BundleUsageInfoStore.getInstance().getStore();
//        for (Map.Entry<String, BundleUsageInfo> entry : map.entrySet()) {
//            if ((System.currentTimeMillis() - entry.getValue().getLastPageEnterTime()) > CodeCacheConfig.INSTANCE.getMaxLiveDays() * TimeUtils.ONE_DAY_MILLIS) {
//                // 已经过期了，删除掉
//                toRemoveBundleNames.add(entry.getKey());
//            }
//        }
//
//        // 删除Bundle
//        for (CodeCacheStorageInfo info : mStorageInfoStore.getAllValues()) {
//            if (toRemoveBundleNames.contains(info.getBundleName())) {
//                removeCodeCache(info.getBundleName(), info.getBundleVersion(), REASON_OUT_OF_DATE);
//            }
//        }
//
//        // TODO chdc 当配置值改变的时候，删除因为文件大小小于指定值的CodeCache文件
//    }

    private static class MyLoadJSCodeCacheCallback implements LoadJSCodeCacheCallback {
        private final WeakReference<MSCRuntime> mscRuntimeWeakReference;

        private MyLoadJSCodeCacheCallback(MSCRuntime runtime) {
            this.mscRuntimeWeakReference = new WeakReference<>(runtime);
        }

        @Override
        public void onLoad(String sourceURL, String jsCodeCachePath, LoadStatus status) {
            if (TextUtils.isEmpty(jsCodeCachePath)) {
                return;
            }
            switch (status) {
                case invalid:
                case unsupported:
                    // 无效直接删除CodeCache文件
                    MSCLog.i(TAG, "Load CodeCache, file: ", jsCodeCachePath, "status: ", status);
                    new File(jsCodeCachePath).delete();
                    break;
                case nonexistent:
                    MSCLog.i(TAG, "Load CodeCache, file: ", jsCodeCachePath, "status: ", status);
                    break;
            }
            MSCRuntime mscRuntime = mscRuntimeWeakReference.get();
            if (mscRuntime == null) {
                return;
            }
            MSCRuntimeReporter runtimeReporter = mscRuntime.getRuntimeReporter();
            runtimeReporter.recordJsCodeCacheLoadStatus(jsCodeCachePath, status);
        }
    }

    void reportCodeCacheError(CodeCacheInfo codeCacheInfo, String error) {
        if (!CodeCacheConfig.INSTANCE.isCodeCacheReportFail()) {
            return;
        }
        reportCodeCacheCreate(codeCacheInfo, error);
    }

    void reportCodeCacheSuccess(CodeCacheInfo codeCacheInfo) {
        if (!CodeCacheConfig.INSTANCE.isCodeCacheReportSuccess()) {
            return;
        }
        reportCodeCacheCreate(codeCacheInfo, null);
    }

    private void reportCodeCacheCreate(CodeCacheInfo codeCacheInfo, String error) {
        PackageInfoWrapper packageInfoWrapper = codeCacheInfo.getPackageInfo();
        if (packageInfoWrapper == null) {
            return;
        }
        MSCRuntime runtime = codeCacheInfo.getMscRuntime();
        MSCReporter reporter = null;
        if (runtime != null) {
            reporter = runtime.getRuntimeReporter();
        }
        if (reporter == null) {
            reporter = new MSCReporter();
        }
        String createState = error == null ? "success" : "fail";
        MetricsEntry metricsEntry = reporter.record(ReporterFields.CODE_CACHE_CREATE)
                .tag("create", createState)
                .tag("bundleName", packageInfoWrapper.getDDResourceName())
                .tag("bundleVersion", packageInfoWrapper.getVersion());
        if (createState.equals("fail")) {
            metricsEntry = metricsEntry.tag("error", error);
        }
        metricsEntry.sendRealTime();
    }
}
