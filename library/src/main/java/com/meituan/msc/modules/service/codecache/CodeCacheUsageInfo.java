package com.meituan.msc.modules.service.codecache;

import android.support.annotation.Keep;

import com.meituan.msc.common.utils.collection.LocalCacheMap;

@Keep
class CodeCacheUsageInfo extends CodeCacheKey {
    public void attachLocalCacheMap(LocalCacheMap localCacheMap) {
        this.localCacheMap = localCacheMap;
    }

    protected void saveChanges() {
        if (localCacheMap != null) {
            localCacheMap.write();
        }
    }

    public CodeCacheUsageInfo(String appId, String appVersion, String packageName, String jsFileRelativePath, String codeCacheFile, long createTimeInMs, long fileSize, int usageCount) {
        super(appId, appVersion, packageName, jsFileRelativePath);
        this.codeCacheFile = codeCacheFile;
        this.createTimeInMs = createTimeInMs;
        this.usageCount = usageCount;
        this.fileSize = fileSize;
    }

    public long getCreateTimeInMs() {
        return createTimeInMs;
    }

    public long getLastUseTimeInMs() {
        return lastUseTimeInMs;
    }

    public void setLastUseTimeInMs(long lastUseTimeInMs) {
        this.lastUseTimeInMs = lastUseTimeInMs;
        saveChanges();
    }

    public int getUsageCount() {
        return usageCount;
    }

    public long getFileSize() {
        return fileSize;
    }

    public void setFileSize(long fileSize) {
        this.fileSize = fileSize;
    }

    public void setUsageCount(int usageCount) {
        this.usageCount = usageCount;
        saveChanges();
    }

    public void usageCountIncrement() {
        usageCountIncrement(1);
    }

    public void usageCountIncrement(int incrementCount) {
        setUsageCount(this.usageCount + incrementCount);
    }

    public String getCodeCacheFile() {
        return codeCacheFile;
    }

    public boolean isMarkedToRemove() {
        return markedToRemove;
    }

    public void setMarkedToRemove(boolean markedToRemove) {
        this.markedToRemove = markedToRemove;
        saveChanges();
    }

    public void setCodeCacheFile(String codeCacheFile) {
        this.codeCacheFile = codeCacheFile;
        saveChanges();
    }

    private String codeCacheFile;
    private final long createTimeInMs;
    private long lastUseTimeInMs;
    private int usageCount;
    private long fileSize;
    private boolean markedToRemove = false;

    private transient LocalCacheMap localCacheMap;
}
