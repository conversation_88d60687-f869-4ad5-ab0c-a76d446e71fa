package com.meituan.msc.modules.service.codecache;

import android.content.Context;

import com.google.gson.reflect.TypeToken;
import com.meituan.msc.common.report.MetricsEntry;
import com.meituan.msc.common.utils.CIPStorageFileUtil;
import com.meituan.msc.common.utils.collection.LocalCacheMap;
import com.meituan.msc.extern.MSCEnvHelper;
import com.meituan.msc.modules.preload.executor.Task;
import com.meituan.msc.modules.preload.executor.TaskExecuteContext;
import com.meituan.msc.modules.preload.executor.TaskExecutor;
import com.meituan.msc.modules.reporter.MSCLog;
import com.meituan.msc.modules.reporter.MSCReporter;

import org.json.JSONException;
import org.json.JSONObject;

import java.io.File;
import java.util.ArrayList;
import java.util.Collection;
import java.util.HashMap;
import java.util.List;
import java.util.Map;

import static com.meituan.msc.modules.reporter.CommonTags.TAG_MSC_APP_ID;
import static com.meituan.msc.modules.reporter.CommonTags.TAG_MSC_APP_VERSION;
import static com.meituan.msc.modules.reporter.CommonTags.TAG_MSC_ENV;

class CodeCacheUsageInfoManager {
    private static final String TAG = " CodeCacheUsageInfo";

    private static final String REPORT_CODE_CACHE_USAGE = "msc.codecache.usage";
    private static final String KEY_LAST_REPORT_TIME = "codecache_last_report_time";
    private static final long ONE_HOUR_IN_MS = 1000 * 60 * 60;

    private final TaskExecutor mTaskExecutor;

    /**
     * 存储CodeCache情况的map，key是 appId_packageName_包内JS路径 ，value是详细的信息
     */
    private LocalCacheMap<String, CodeCacheUsageInfo> codeCacheUsageInfos;

    CodeCacheUsageInfoManager(Context context, TaskExecutor taskExecutor) {
        codeCacheUsageInfos = new LocalCacheMap<String, CodeCacheUsageInfo>(context, CIPStorageFileUtil.getCIPStorageCenter(context), "CodeCacheUsageInfo",
                new TypeToken<Map<String, CodeCacheUsageInfo>>() {}.getType()) {
            @Override
            protected void readFromLocal() {
                super.readFromLocal();
                for (CodeCacheUsageInfo usageInfo : (Collection<CodeCacheUsageInfo>) getStore().values()) {
                    usageInfo.attachLocalCacheMap(codeCacheUsageInfos);
                }
            }
        };
        this.mTaskExecutor = taskExecutor;
        mTaskExecutor.addTask(new Task("initCodeCacheUsageInfo") {
            @Override
            protected void execute(TaskExecuteContext taskExecuteContext) {
                initCodeCacheUsageInfo();
                // 每人每天上报一次
                reportCodeCacheUsage(context);
            }
        });
    }

    private String getCodeCacheKey(String appId, String packageName, String jsFileRelativePath) {
        return appId + packageName + jsFileRelativePath;
    }

    private void initCodeCacheUsageInfo() {
        // 遍历所有信息
        for (CodeCacheUsageInfo usageInfo : codeCacheUsageInfos.values()) {
            File codeCacheFile = new File(usageInfo.getCodeCacheFile());
            if (!codeCacheFile.exists()) {
                // 处理删除的数据
                // 标记删除：删除CodeCache时，不应该将它的使用次数和存储空间立即删掉。待下次指标上报时，将其算在统计范围内进行上报，上报成功后，再将该记录删掉
                usageInfo.setMarkedToRemove(true);
            }
        }
    }

    public void recordCodeCacheUsage(String appId, String packageName, String jsFileRelativePath) {
        CodeCacheUsageInfo codeCacheUsageInfo = codeCacheUsageInfos.get(getCodeCacheKey(appId, packageName, jsFileRelativePath));
        if (codeCacheUsageInfo == null) {
            return;
        }
        codeCacheUsageInfo.usageCountIncrement();
        codeCacheUsageInfo.setLastUseTimeInMs(System.currentTimeMillis());
    }

    public void onCreateCodeCacheInner(CodeCacheInfo codeCacheInfo, File codeCacheFile) {
        // 记录下创建时间和文件大小
        String key = getCodeCacheKey(codeCacheInfo.getAppId(), codeCacheInfo.getPackageName(), codeCacheInfo.getJsFileRelativePath());
        CodeCacheUsageInfo usageInfo = codeCacheUsageInfos.get(key);
        String codeCacheFilePath = codeCacheFile.getAbsolutePath();
        if (usageInfo != null) {
            // 如果已经存在了，说明有旧版本的CodeCache，直接更新新版本的路径和文件大小
            usageInfo.setCodeCacheFile(codeCacheFilePath);
            usageInfo.setFileSize(codeCacheFile.length());
            usageInfo.setMarkedToRemove(false);
        } else {
            usageInfo = new CodeCacheUsageInfo(codeCacheInfo.getAppId(), codeCacheInfo.getAppVersion(),
                    codeCacheInfo.getPackageName(), codeCacheInfo.getJsFileRelativePath(),
                    codeCacheFilePath, System.currentTimeMillis(), codeCacheFile.length(), 0);
            usageInfo.attachLocalCacheMap(codeCacheUsageInfos);
            codeCacheUsageInfos.put(key, usageInfo);
        }
    }

    public void reportCodeCacheUsage(Context context) {
        // 如果今天已经上报过了，则直接退出
        long lastReportTime = CIPStorageFileUtil.getCIPStorageCenter(context).getLong(KEY_LAST_REPORT_TIME, 0);
        if (System.currentTimeMillis() - lastReportTime < ONE_HOUR_IN_MS * CodeCacheConfig.INSTANCE.getMinUsageReportIntervalInHour()) {
            return;
        }

        Map<String, List<CodeCacheUsageInfo>> infosByAppId = new HashMap<>();
        // 以小程序维度进行聚合上报
        for (CodeCacheUsageInfo info : codeCacheUsageInfos.values()) {
            String key = info.getAppId() + info.getAppVersion();
            List<CodeCacheUsageInfo> list = infosByAppId.get(key);
            if (list == null) {
                list = new ArrayList<>();
                infosByAppId.put(key, list);
            }
            list.add(info);
        }

        MSCReporter mscReporter = new MSCReporter();
        List<MetricsEntry> metricsEntries = new ArrayList<>();
        for (List<CodeCacheUsageInfo> infoList : infosByAppId.values()) {
            CodeCacheUsageInfo first = infoList.get(0);
            JSONObject appCodeCacheUsage = new JSONObject();
            try {
                for (CodeCacheUsageInfo info : infoList) {
                    JSONObject usage = new JSONObject();
                    usage.put("fileSize", info.getFileSize());
                    usage.put("lastUseTime", info.getLastUseTimeInMs());
                    usage.put("usageCount", info.getUsageCount());
                    usage.put("createTime", info.getCreateTimeInMs());
                    appCodeCacheUsage.put(info.getPackageName() + "/" + info.getJsFileRelativePath(), usage);
                }
            } catch (JSONException e) {
                MSCLog.e(TAG, e);
            }
            metricsEntries.add(mscReporter.record(REPORT_CODE_CACHE_USAGE)
                    .tag(TAG_MSC_ENV, MSCEnvHelper.getEnvInfo().isProdEnv() ? "prod" : "test")
                    .tag(TAG_MSC_APP_ID, first.getAppId())
                    .tag(TAG_MSC_APP_VERSION, first.getAppVersion())
                    .tag("USAGE", appCodeCacheUsage));
        }

        mscReporter.sendBatchDelay(metricsEntries);

        // 存储上报时间
        CIPStorageFileUtil.getCIPStorageCenter(context).setLong(KEY_LAST_REPORT_TIME, System.currentTimeMillis());
        removeMarkedUsageInfo();
    }

    private void removeMarkedUsageInfo() {
        // 遍历所有信息
        List<String> removeKeys = new ArrayList<>();
        for (Map.Entry<String, CodeCacheUsageInfo> entry : codeCacheUsageInfos.entrySet()) {
            if (entry.getValue().isMarkedToRemove()) {
                removeKeys.add(entry.getKey());
            }
        }
        for (String key : removeKeys) {
            codeCacheUsageInfos.remove(key);
        }
    }
}
