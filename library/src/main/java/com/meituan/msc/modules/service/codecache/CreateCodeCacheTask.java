package com.meituan.msc.modules.service.codecache;

import com.meituan.msc.modules.preload.executor.Task;
import com.meituan.msc.modules.preload.executor.TaskExecuteContext;
import com.meituan.msc.modules.update.packageattachment.PackageAttachmentManager;

class CreateCodeCacheTask extends Task {

    private CodeCacheManager mCodeCacheManager;
    private CodeCacheInfo mCodeCacheInfo;

    public CreateCodeCacheTask(CodeCacheManager codeCacheManager, CodeCacheInfo codeCacheInfo) {
        super(codeCacheInfo.getTaskKey());
        mCodeCacheInfo = codeCacheInfo;
        mCodeCacheManager = codeCacheManager;
    }

    @Override
    protected void execute(TaskExecuteContext taskExecuteContext) {
        if (mCodeCacheManager.canCreateCodeCache(mCodeCacheInfo, true)) {
            mCodeCacheManager.createCodeCache(mCodeCacheInfo);
            // 生成 CodeCache 后进行 LRU 清理
            if (CodeCacheConfig.INSTANCE.isCodeCacheAfterCreateEnable()) {
                PackageAttachmentManager.getInstance().cleanAbandonedAttachmentAsync();
            }
        }
    }
}
