package com.meituan.msc.modules.service.codecache;

import com.meituan.dio.easy.DioFile;
import com.meituan.msc.modules.preload.executor.Task;
import com.meituan.msc.modules.preload.executor.TaskExecuteContext;
import com.meituan.msc.modules.reporter.MSCLog;
import com.meituan.msc.modules.update.JSFileSourceHelper;
import com.meituan.msc.modules.update.bean.PackageInfoWrapper;
import com.meituan.msc.modules.update.packageattachment.PackageAttachmentManager;

import java.io.IOException;
import java.util.List;

class CreatePackageCodeCacheTask extends Task {
    private static final String TAG = "CreatePackageCodeCacheTask";

    private final CodeCacheManager mCodeCacheManager;
    private final String appId;
    private final String appVersion;
    private final PackageInfoWrapper packageInfoWrapper;

    public CreatePackageCodeCacheTask(CodeCacheManager codeCacheManager, String appId, String appVersion, PackageInfoWrapper packageInfoWrapper) {
        super(packageInfoWrapper.getLocalPath());
        this.appId = appId;
        this.appVersion = appVersion;
        this.packageInfoWrapper = packageInfoWrapper;
        mCodeCacheManager = codeCacheManager;
    }

    @Override
    protected void execute(TaskExecuteContext taskExecuteContext) {
        // 遍历包内的所有js文件
        DioFile packageFile = new DioFile(packageInfoWrapper.getLocalPath());
        List<DioFile> children;
        try {
            children = DioFile.getAllFiles(packageFile);
        } catch (IOException e) {
            MSCLog.e(TAG, e);
            return;
        }
        boolean isCreatedNewCodeCache = false;
        for (DioFile childFile : children) {
            String fileName = childFile.getName();
            if (canCreateCodeCache(fileName)) {
                // 如果正在执行，则取消掉
                CodeCacheInfo codeCacheInfo = new CodeCacheInfo(appId, appVersion, packageInfoWrapper, childFile,
                        JSFileSourceHelper.getJSFileSourceForCodeCache(packageInfoWrapper, childFile.getChildFilePath()), null);
                if (taskExecuteContext.getTaskExecutor().existsTask(codeCacheInfo.getTaskKey())) {
                    taskExecuteContext.getTaskExecutor().cancelTask(codeCacheInfo.getTaskKey());
                }
                if (mCodeCacheManager.canCreateCodeCache(codeCacheInfo, true)) {
                    mCodeCacheManager.createCodeCache(codeCacheInfo);
                    isCreatedNewCodeCache = true;
                }
            } else {
                // FIXME by chendacai 下面的逻辑是为了尽快清除生成出错的codecache文件，临时逻辑，12.15.200版本可删掉
                if (fileName.endsWith(".js")) {
                    // 不合法的JS文件需要清除掉
                    try {
                        CodeCacheInfo codeCacheInfo = new CodeCacheInfo(appId, appVersion, packageInfoWrapper, childFile,
                                JSFileSourceHelper.getJSFileSourceForCodeCache(packageInfoWrapper, childFile.getChildFilePath()), null);
                        mCodeCacheManager.removeCodeCache(codeCacheInfo);
                    } catch (Throwable e) {
                        MSCLog.e(TAG, e, "Filed to remove codecache");
                    }
                }
            }
        }
        // 生成 CodeCache 后进行 LRU 清理
        if (isCreatedNewCodeCache && CodeCacheConfig.INSTANCE.isCodeCacheAfterCreateEnable()) {
            PackageAttachmentManager.getInstance().cleanAbandonedAttachmentAsync();
        }
    }

    protected boolean canCreateCodeCache(String fileName) {
        if (fileName == null) {
            return false;
        }
        if (!fileName.endsWith(".js")) {
            return false;
        }
        // JS生成条件文档链接： https://km.sankuai.com/collabpage/1302970856#id-Package%E7%BB%B4%E5%BA%A6%E5%85%A8%E9%87%8F%E7%94%9F%E6%88%90%E2%80%8B11.20.200
        // 文件名包含“service”、“runtime”、“v8”字符串的js文件会提前生成codecahe
        return fileName.contains("service") || fileName.contains("runtime") || fileName.contains("v8");
    }
}
