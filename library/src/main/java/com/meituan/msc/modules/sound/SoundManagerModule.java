/*
 * Copyright (c) Facebook, Inc. and its affiliates.
 *
 * This source code is licensed under the MIT license found in the
 * LICENSE file in the root directory of this source tree.
 */

package com.meituan.msc.modules.sound;

import android.content.Context;
import android.media.AudioManager;

import com.meituan.msc.extern.MSCEnvHelper;
import com.meituan.msc.modules.manager.MSCMethod;
import com.meituan.msc.modules.manager.MSCModule;
import com.meituan.msc.modules.manager.ModuleName;

@ModuleName(name = "SoundManager")
public class SoundManagerModule extends MSCModule {

    @MSCMethod
    public void playTouchSound() {
        AudioManager audioManager =
                (AudioManager) MSCEnvHelper.getContext().getSystemService(Context.AUDIO_SERVICE);
        if (audioManager != null) {
            audioManager.playSoundEffect(AudioManager.FX_KEY_CLICK);
        }
    }
}
