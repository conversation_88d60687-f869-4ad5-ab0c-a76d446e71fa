/*
 * Copyright (c) Facebook, Inc. and its affiliates.
 *
 * This source code is licensed under the MIT license found in the
 * LICENSE file in the root directory of this source tree.
 */

package com.meituan.msc.modules.statusbar;

import android.animation.ArgbEvaluator;
import android.animation.ValueAnimator;
import android.annotation.TargetApi;
import android.app.Activity;
import android.content.Context;
import android.content.res.Resources;
import android.os.Build;
import android.support.annotation.Nullable;
import android.view.View;
import android.view.WindowInsets;
import android.view.WindowManager;

import com.meituan.msc.extern.MSCEnvHelper;
import com.meituan.msc.jse.bridge.UiThreadUtil;
import com.meituan.msc.modules.manager.MSCMethod;
import com.meituan.msc.modules.manager.MSCModule;
import com.meituan.msc.modules.manager.ModuleName;

import android.support.v4.view.ViewCompat;

import org.json.JSONException;
import org.json.JSONObject;

@ModuleName(name = "StatusBarManager")
public class StatusBarModule extends MSCModule {

  private static final String HEIGHT_KEY = "HEIGHT";
  private static final String DEFAULT_BACKGROUND_COLOR_KEY = "DEFAULT_BACKGROUND_COLOR";

  public StatusBarModule() {
    super();
  }

  private static int getStatusBarHeight(Context context) {
    int result = 0;
    Resources resources = Resources.getSystem();
    int resourceId = resources.getIdentifier("status_bar_height", "dimen", "android");
    if (resourceId > 0) {
      result = resources.getDimensionPixelSize(resourceId);
    }
    return result;
  }

  private static float px2dp(Context context, float value) {
      return value / Resources.getSystem().getDisplayMetrics().density;
  }

  public @Nullable JSONObject getTypedExportedConstants() {
    final Context context = MSCEnvHelper.getContext();
    final Activity activity = getCurrentActivity();

    // MRN54 luojiani MRN-1251085 Android-titlebar高度计算调整
    final float height = getStatusBarHeight(context);
    String statusBarColorString = "black";

    if (activity != null && Build.VERSION.SDK_INT >= Build.VERSION_CODES.LOLLIPOP) {
      final int statusBarColor = activity.getWindow().getStatusBarColor();
      statusBarColorString = String.format("#%06X", (0xFFFFFF & statusBarColor));
    }
    JSONObject result = new JSONObject();
    try {
      result.put(HEIGHT_KEY, px2dp(context, height));
      result.put(DEFAULT_BACKGROUND_COLOR_KEY, statusBarColorString);
    } catch (JSONException e) {
      e.printStackTrace();
    }
    return result;
  }

  @MSCMethod
  public void setColor(final double colorDouble, final boolean animated) {
    final int color = (int) colorDouble;

    final Activity activity = getCurrentActivity();
    if (activity == null) {
      return;
    }

    if (Build.VERSION.SDK_INT >= Build.VERSION_CODES.LOLLIPOP) {

      UiThreadUtil.runOnUiThread(
          new Runnable() {
            @TargetApi(Build.VERSION_CODES.LOLLIPOP)
            @Override
            public void run() {
              activity
                  .getWindow()
                  .addFlags(WindowManager.LayoutParams.FLAG_DRAWS_SYSTEM_BAR_BACKGROUNDS);
              if (animated) {
                int curColor = activity.getWindow().getStatusBarColor();
                ValueAnimator colorAnimation =
                    ValueAnimator.ofObject(new ArgbEvaluator(), curColor, color);

                colorAnimation.addUpdateListener(
                    new ValueAnimator.AnimatorUpdateListener() {
                      @Override
                      public void onAnimationUpdate(ValueAnimator animator) {
                        activity
                            .getWindow()
                            .setStatusBarColor((Integer) animator.getAnimatedValue());
                      }
                    });
                colorAnimation.setDuration(300).setStartDelay(0);
                colorAnimation.start();
              } else {
                activity.getWindow().setStatusBarColor(color);
              }
            }
          });
    }
  }

  @MSCMethod
  public void setTranslucent(final boolean translucent) {
    final Activity activity = getCurrentActivity();
    if (activity == null) {
      return;
    }

    if (Build.VERSION.SDK_INT >= Build.VERSION_CODES.LOLLIPOP) {
      UiThreadUtil.runOnUiThread(
          new Runnable() {
            @TargetApi(Build.VERSION_CODES.LOLLIPOP)
            @Override
            public void run() {
              // If the status bar is translucent hook into the window insets calculations
              // and consume all the top insets so no padding will be added under the status bar.
              View decorView = activity.getWindow().getDecorView();
              if (translucent) {
                decorView.setOnApplyWindowInsetsListener(
                    new View.OnApplyWindowInsetsListener() {
                      @Override
                      public WindowInsets onApplyWindowInsets(View v, WindowInsets insets) {
                        WindowInsets defaultInsets = v.onApplyWindowInsets(insets);
                        return defaultInsets.replaceSystemWindowInsets(
                            defaultInsets.getSystemWindowInsetLeft(),
                            0,
                            defaultInsets.getSystemWindowInsetRight(),
                            defaultInsets.getSystemWindowInsetBottom());
                      }
                    });
              } else {
                decorView.setOnApplyWindowInsetsListener(null);
              }

              ViewCompat.requestApplyInsets(decorView);
            }
          });
    }
  }

  @MSCMethod
  public void setHidden(final boolean hidden) {
    final Activity activity = getCurrentActivity();
    if (activity == null) {
      return;
    }
    UiThreadUtil.runOnUiThread(
        new Runnable() {
          @Override
          public void run() {
            if (hidden) {
              activity.getWindow().addFlags(WindowManager.LayoutParams.FLAG_FULLSCREEN);
              activity.getWindow().clearFlags(WindowManager.LayoutParams.FLAG_FORCE_NOT_FULLSCREEN);
            } else {
              activity.getWindow().addFlags(WindowManager.LayoutParams.FLAG_FORCE_NOT_FULLSCREEN);
              activity.getWindow().clearFlags(WindowManager.LayoutParams.FLAG_FULLSCREEN);
            }
          }
        });
  }

  @MSCMethod
  public void setStyle(@Nullable final String style) {
    final Activity activity = getCurrentActivity();
    if (activity == null) {
      return;
    }

    if (Build.VERSION.SDK_INT >= Build.VERSION_CODES.M) {
      UiThreadUtil.runOnUiThread(
          new Runnable() {
            @TargetApi(Build.VERSION_CODES.M)
            @Override
            public void run() {
              View decorView = activity.getWindow().getDecorView();
              int systemUiVisibilityFlags = decorView.getSystemUiVisibility();
              if ("dark-content".equals(style)) {
                systemUiVisibilityFlags |= View.SYSTEM_UI_FLAG_LIGHT_STATUS_BAR;
              } else {
                systemUiVisibilityFlags &= ~View.SYSTEM_UI_FLAG_LIGHT_STATUS_BAR;
              }
              decorView.setSystemUiVisibility(systemUiVisibilityFlags);
            }
          });
    }
  }

  @Override
  public final @javax.annotation.Nullable JSONObject getConstants() {
    return getTypedExportedConstants();
  }

  private Activity getCurrentActivity() {
    try {
      return getRuntime().getContainerManagerModule().getTopActivity();
    } catch (Throwable th) {
      return null;
    }
  }
}
