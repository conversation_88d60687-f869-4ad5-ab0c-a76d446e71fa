package com.meituan.msc.modules.storage;

import com.meituan.msc.modules.engine.RuntimeManager;

import java.util.ArrayList;
import java.util.List;
import java.util.Set;

/**
 * 多进程调用.获取在指定进程上，正在使用的业务包资源信息。
 */
class IPCTask implements IIPCTask {

    @Override
    public List<String> getUsingBizResources() {
        Set<String> resources = RuntimeManager.getUsingBizResources();
        // 目前支持元数据类型的有map和list
        return new ArrayList<>(resources);
    }
}
