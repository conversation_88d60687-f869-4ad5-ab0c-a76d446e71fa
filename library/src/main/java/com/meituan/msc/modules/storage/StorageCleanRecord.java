package com.meituan.msc.modules.storage;

public class StorageCleanRecord {
    public long totalSizeBeforeClean;
    public long totalSizeAfterClean;

    public static class FileDeleteResult {
        private final long deletedSize;
        private final String deletedFileName;
        private final long lastModified;

        public FileDeleteResult(long length, String name, long lastModified) {
            deletedSize = length;
            deletedFileName = name;
            this.lastModified = lastModified;
        }

        @Override
        public String toString() {
            return "FileDeleteResult{" +
                    "deletedSize=" + deletedSize +
                    ", deletedFileName='" + deletedFileName + '\'' +
                    ", lastModified=" + lastModified +
                    '}';
        }
    }
}

