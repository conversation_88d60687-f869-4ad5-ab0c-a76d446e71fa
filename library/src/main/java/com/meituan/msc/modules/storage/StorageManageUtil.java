package com.meituan.msc.modules.storage;

import android.content.Context;
import android.support.annotation.NonNull;
import android.support.annotation.VisibleForTesting;

import com.meituan.android.cipstorage.CIPSStrategy;
import com.meituan.met.mercury.load.core.DDLoaderManager;
import com.meituan.msc.common.process.MSCProcess;
import com.meituan.msc.common.process.ProcessMonitor;
import com.meituan.msc.common.process.ipc.IPCInvoke;
import com.meituan.msc.common.support.java.util.Objects;
import com.meituan.msc.common.utils.CollectionUtil;
import com.meituan.msc.common.utils.FileUtil;
import com.meituan.msc.common.utils.MSCStorageCleanScene;
import com.meituan.msc.common.utils.MSCStorageUtil;
import com.meituan.msc.extern.MSCEnvHelper;
import com.meituan.msc.modules.engine.RuntimeManager;
import com.meituan.msc.modules.reporter.MSCLog;
import com.meituan.msc.modules.service.codecache.CodeCacheConfig;
import com.meituan.msc.modules.update.packageattachment.PackageAttachmentManager;

import java.io.File;
import java.util.HashMap;
import java.util.List;
import java.util.Map;
import java.util.Set;
import java.util.concurrent.Callable;
import java.util.concurrent.CopyOnWriteArraySet;

/**
 * @hide 存储治理相关封装
 * MSC 包管理在 DDD 侧进行
 * MSC 内部存储管理，目前仅管理 CodeCache 部分
 * <p>
 * Created by letty on 2023/5/22.
 **/
public class StorageManageUtil {

    private static volatile CIPSStrategy.LRUConfig mLRUConfig;
    private static volatile String mStrategyString;

    public static final long LIMIT_SIZE_UNIT_MB = 1024 * 1024;
    public static final long LIMIT_SIZE_UNIT_GB = 1024 * 1024 * 1024;
    // usr 文件夹 tmp前缀临时文件，在App启动时默认清理阈值为 200M
    public static final int DEFAULT_USR_TMP_FILE_SIZE_LIMIT_MB_AT_APP_LAUNCH = 200;
    // usr 文件夹 tmp前缀临时文件，在切后台时默认清理阈值为 400M
    public static final int DEFAULT_USR_TMP_FILE_SIZE_LIMIT_MB_AT_ENTER_FOREGROUND = 400;

    // temp 文件夹 临时文件，在App启动时默认清理阈值为 2G
    public static final int DEFAULT_TEMP_FILES_SIZE_LIMIT_GB_AT_APP_LAUNCH = 2;
    // temp 文件夹 临时文件，在切后台时默认清理阈值为 4G
    public static final int DEFAULT_TEMP_FILES_SIZE_LIMIT_GB_AT_ENTER_BACKGROUND = 4;

    public static int getStorageType() {
        return CIPSStrategy.userStorageType(MSCEnvHelper.getContext());
    }

    public static boolean isLowFreqAndStorage() {
        return CIPSStrategy.isLowFreqAndStorage(MSCEnvHelper.getContext());
    }

    public static int getLRUMaxSize() {
        return getLRUConfigWithFramework().maxSize;
    }

    /**
     * 存储治理内部实验策略获取
     *
     * @return
     */
    public static String getStorageTestStrategy() {
        if (mStrategyString == null) {
            mStrategyString = CIPSStrategy.AutoCleanABTestKeyForFramework(CIPSStrategy.FRAMEWORK_MSC);
        }
        return mStrategyString;
    }

    @NonNull
    public static CIPSStrategy.LRUConfig getLRUConfigWithFramework() {
        if (mLRUConfig == null) {
            synchronized (StorageManageUtil.class) {
                if (mLRUConfig == null) {
                    mLRUConfig = CIPSStrategy.getLRUConfigWithFramework(CIPSStrategy.FRAMEWORK_MSC, MSCEnvHelper.getContext());
                }
                // compat if null
                if (mLRUConfig == null) {
                    MSCLog.e("getLRUConfigWithFramework null");
                    mLRUConfig = new CIPSStrategy.LRUConfig(CodeCacheConfig.INSTANCE.getLruCacheLimit(), 0);
                }
            }
        }
        return mLRUConfig;
    }

    public static CIPSStrategy.LRUCleanData getLRUCleanData(long removedSize,
                                                            int maxSize,
                                                            int duration,
                                                            List<CIPSStrategy.SingleFileCleanData> removedFiles,
                                                            List<CIPSStrategy.SingleFileCleanData> failedFiles) {
        CIPSStrategy.LRUCleanData cleanData = new CIPSStrategy.LRUCleanData();
        cleanData.duration = duration;
        cleanData.maxSize = maxSize;
        cleanData.removeSize = removedSize;
        cleanData.removedFiles = removedFiles;
        cleanData.failedFiles = failedFiles;
//        MSCLog.d("getLRUCleanData", .getInstance().get().toJson(cleanData));
        return cleanData;
    }

    public static CIPSStrategy.SingleFileCleanData generateCleanData(String fileName,
//                                                                     String fileVersion,
                                                                     long fileSize) {
        return generateCleanData(fileName, fileSize, 0, null);
    }

    public static CIPSStrategy.SingleFileCleanData generateCleanData(String fileName,
//                                                                     String fileVersion,
                                                                     long fileSize,
                                                                     int failedType,
                                                                     String failMsg) {
        CIPSStrategy.SingleFileCleanData cleanData = new CIPSStrategy.SingleFileCleanData();
        cleanData.fileName = fileName;
        cleanData.fileSize = fileSize;
//        cleanData.fileVersion = fileVersion;
        cleanData.failedType = failedType;
        cleanData.failedMsg = failMsg;
        return cleanData;
    }


    @VisibleForTesting
    static final String MSC_DDD_RES_NAME = "msc";

    /**
     * 一键清理能力注册
     */
    public static void registerOneTouchCleanHandler() {
        CIPSStrategy.registerOneTouchCleanHandler(CIPSStrategy.FRAMEWORK_MSC, new Callable<CIPSStrategy.LRUCleanData>() {
            @Override
            public CIPSStrategy.LRUCleanData call() throws Exception {
                return dealMSCCleaner();
            }
        });
    }

    @VisibleForTesting
    static CIPSStrategy.LRUCleanData dealMSCCleaner() {
        CIPSStrategy.LRUCleanData dddCleanData = DDLoaderManager.getLoader(MSC_DDD_RES_NAME).syncCleanAllCacheResource(getAllProcessUsingBizResources());
        CIPSStrategy.LRUCleanData codeCacheCleanData = PackageAttachmentManager.getInstance().cleanAllAttachmentSync();
        return mergeLRUCleanData(dddCleanData, codeCacheCleanData);
    }

    @VisibleForTesting
    static Set<String> getAllProcessUsingBizResources() {
        // add current process
        Set<String> ret = RuntimeManager.getUsingBizResources();
        // add subprocess when current is not subprocess and subprocess is alive
        if (!MSCProcess.STANDARD.isCurrentProcess() && ProcessMonitor.isProcessAlive(MSCProcess.STANDARD.getProcessName())) {
            List<String> list = ((IIPCTask) IPCInvoke.getInvokeProxy(IPCTask.class, MSCProcess.STANDARD)).getUsingBizResources();
            if (null != list && !list.isEmpty()) {
                ret.addAll(list);
            }
        }
        return ret;
    }

    /**
     * 是否允许预下载
     * https://km.sankuai.com/collabpage/1769555843
     *
     * @param appId
     * @return
     */
    public static boolean isPreDownloadAllowed(String appId) {
        boolean isPreDownloadEnable = !CIPSStrategy.disablePredownload(CIPSStrategy.FRAMEWORK_MSC, appId);
        if (!isPreDownloadEnable) {
            disabledPreDownloadAppId.add(appId);
        }
        MSCLog.i("StorageManageUtil isPreDownloadAllowed", appId, isPreDownloadEnable);
        return isPreDownloadEnable;
    }

    private static Set<String> disabledPreDownloadAppId = new CopyOnWriteArraySet<>();

    public static boolean hasDisabledPreDownload(String appId) {
        if (appId == null) {
            return false;
        }
        return disabledPreDownloadAppId.contains(appId);
    }

    @VisibleForTesting
    static CIPSStrategy.LRUCleanData mergeLRUCleanData(CIPSStrategy.LRUCleanData cleanData1,
                                                              CIPSStrategy.LRUCleanData cleanData2) {
        if (cleanData1 == null) {
            return cleanData2;
        } else if (cleanData2 == null) {
            return cleanData1;
        } else {
            return getLRUCleanData(cleanData1.removeSize + cleanData2.removeSize,
                    cleanData1.maxSize,
                    cleanData1.duration,
                    CollectionUtil.concatAll(cleanData1.removedFiles, cleanData2.removedFiles),
                    CollectionUtil.concatAll(cleanData1.failedFiles, cleanData2.failedFiles));
        }
    }

    public static Map<String, StorageCleanRecord> cleanTempDirAndUsrTmpFiles(Context context, String appId,
                                                                               @MSCStorageCleanScene String cleanScene,
                                                                               long tempDirSizeLimitMB, long usrTmpFilesSizeLimitMB) {
        String mscPath = MSCStorageUtil.getMSCPath(context);

        // 清理 temp 目录缓存文件
        File miniAppTempDir = MSCStorageUtil.getMiniAppTempDir(MSCStorageUtil.getMiniAppDir(mscPath, appId));
        StorageCleanRecord cleanTempDirResults =
                FileUtil.deleteLRUFiles(miniAppTempDir, getTempDirSizeLimitBytesWithDefault(cleanScene, tempDirSizeLimitMB));

        // 清理 usr 目录中 前缀为tmp的缓存文件
        File miniAppUsrDir = MSCStorageUtil.getMiniAppUsrDir(MSCStorageUtil.getMiniAppDir(mscPath, appId));
        StorageCleanRecord cleanUsrTmpFilesResults =
                FileUtil.deleteLRUFilesByFilePrefix(miniAppUsrDir, MSCStorageUtil.PREFIX_TMP,
                        getUsrTmpFilesSizeLimitBytesWithDefault(cleanScene, usrTmpFilesSizeLimitMB));

        // 返回结果
        Map<String, StorageCleanRecord> cleanResults = new HashMap<>();
        cleanResults.put("temp", cleanTempDirResults);
        cleanResults.put("usr", cleanUsrTmpFilesResults);
        return cleanResults;
    }

    private static long getUsrTmpFilesSizeLimitBytesWithDefault(String cleanScene, long usrTmpFilesSizeLimitMB) {
        long finalUsrTmpFilesSizeLimitBytes = usrTmpFilesSizeLimitMB * LIMIT_SIZE_UNIT_MB;
        if (Objects.equals(cleanScene, MSCStorageCleanScene.CLEAN_SCENE_APP_LAUNCH)) {
            // usr 文件夹 tmp前缀临时文件，在App启动时默认清理阈值为 200M
            if (finalUsrTmpFilesSizeLimitBytes <= 0) {
                finalUsrTmpFilesSizeLimitBytes = DEFAULT_USR_TMP_FILE_SIZE_LIMIT_MB_AT_APP_LAUNCH * LIMIT_SIZE_UNIT_MB;
            }
        } else if (Objects.equals(cleanScene, MSCStorageCleanScene.CLEAN_SCENE_ENTER_BACKGROUND)) {
            // usr 文件夹 tmp前缀临时文件，在切后台时默认清理阈值为 400M
            if (finalUsrTmpFilesSizeLimitBytes <= 0) {
                finalUsrTmpFilesSizeLimitBytes = DEFAULT_USR_TMP_FILE_SIZE_LIMIT_MB_AT_ENTER_FOREGROUND * LIMIT_SIZE_UNIT_MB;
            }
        }
        return finalUsrTmpFilesSizeLimitBytes;
    }

    private static long getTempDirSizeLimitBytesWithDefault(String cleanScene, long tempDirSizeLimitMB) {
        long finalTempDirSizeLimitBytes = tempDirSizeLimitMB * LIMIT_SIZE_UNIT_MB;
        if (Objects.equals(cleanScene, MSCStorageCleanScene.CLEAN_SCENE_APP_LAUNCH)) {
            // temp 文件夹 临时文件，在App启动时默认清理阈值为 2G
            if (finalTempDirSizeLimitBytes <= 0) {
                finalTempDirSizeLimitBytes = DEFAULT_TEMP_FILES_SIZE_LIMIT_GB_AT_APP_LAUNCH * LIMIT_SIZE_UNIT_GB;
            }
        } else if (Objects.equals(cleanScene, MSCStorageCleanScene.CLEAN_SCENE_ENTER_BACKGROUND)) {
            // temp 文件夹 临时文件，在切后台时默认清理阈值为 4G
            if (finalTempDirSizeLimitBytes <= 0) {
                finalTempDirSizeLimitBytes = DEFAULT_TEMP_FILES_SIZE_LIMIT_GB_AT_ENTER_BACKGROUND * LIMIT_SIZE_UNIT_GB;
            }
        }
        return finalTempDirSizeLimitBytes;
    }
}
