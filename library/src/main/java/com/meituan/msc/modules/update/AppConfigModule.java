package com.meituan.msc.modules.update;

import android.graphics.Color;
import android.os.Trace;
import android.support.annotation.ColorInt;
import android.support.annotation.NonNull;
import android.support.annotation.Nullable;
import android.text.TextUtils;

import com.meituan.dio.easy.DioFile;
import com.meituan.msc.common.config.MSCConfig;
import com.meituan.msc.common.utils.ColorUtil;
import com.meituan.msc.common.utils.FileUtil;
import com.meituan.msc.common.utils.PathUtil;
import com.meituan.msc.modules.api.RouteMappingModule;
import com.meituan.msc.modules.api.msi.tabbar.TabBarApi;
import com.meituan.msc.modules.container.fusion.IFusionPageManager;
import com.meituan.msc.modules.engine.MSCHornRollbackConfig;
import com.meituan.msc.modules.manager.MSCModule;
import com.meituan.msc.modules.manager.ModuleName;
import com.meituan.msc.modules.page.render.RendererType;
import com.meituan.msc.modules.page.view.tab.TabItemInfo;
import com.meituan.msc.modules.reporter.MSCLog;

import org.json.JSONArray;
import org.json.JSONException;
import org.json.JSONObject;

import java.io.IOException;
import java.util.ArrayList;
import java.util.HashMap;
import java.util.Iterator;
import java.util.List;
import java.util.Map;

@ModuleName(name = "AppConfigModule")
public class AppConfigModule extends MSCModule implements IAppConfigModule {

    private final String TAG = "AppConfigModule@" + hashCode();
    private volatile JSONObject mConfig;
    // 记录读取config时的appProp版本号，appProp版本变化后允许重新读取
    private String publishId;
    private TabBarConfig mTabBarConfig;
    private JSONObject mWindowConfig;
    public volatile JSONObject mPages;
    private JSONObject mTimeoutConfig;
    private boolean mEnableShark;
    private String mWebLongTapSaveImageToken;
    private Map<String, List<String>> pagePreloadRules;
    private Map<String, SubPackagePreloadConfig> mSubPackagePreloadRule;
    private volatile Map<String, RouteMappingModule.RouteParams> mRouteMappings;
    private volatile Map<String, RouteMappingModule.RouteParams> mRouteMappingsPersist;

    public synchronized void initConfig() throws Exception {
        //避免重复初始化
        MSCAppModule mscAppModule = getRuntime().getMSCAppModule();
        if (mConfig != null && TextUtils.equals(publishId, mscAppModule.getPublishId())) {
            return;
        }

        //.dio后缀的，路径为/MD5/.dio/app-config.json
        String packagePath = mscAppModule.getMainPackagePath();
        DioFile configFile = new DioFile(packagePath, "app-config.json");
        checkFileExist(mscAppModule, packagePath, configFile);

        try {
            initConfig(FileUtil.readContent(configFile));
            publishId = mscAppModule.getPublishId();
        } catch (IOException io) {
            throw new IOException("mainPackage config file read error:", io);
        }
        MSCLog.i(TAG, "mainPackage initConfig end");
    }

    private void checkFileExist(MSCAppModule mscAppModule, String packagePath, DioFile configFile) throws RuntimeException {
        if (configFile.exists()) {
            return;
        }

        JSResourceData jsResourceData = new JSResourceData(mscAppModule.getMainPackageWrapper(),
                configFile, "app-config.json");
        mscAppModule.checkResourceAndReport(packagePath, jsResourceData);
        throw new RuntimeException("mainPackage config file not exist:" + packagePath);
    }

    /**
     * 初始化配置
     *
     * @param config 小程序的配置信息，json字符串
     */
    // TODO: 2023/4/11 tianbin 业务包注入不需要依赖initConfig，待优化
    public void initConfig(String config) throws RuntimeException {
        try {
            mConfig = new JSONObject(config);
        } catch (JSONException e) {
            MSCLog.e(TAG, String.format("config is not JSON format! config=%s", config));
            throw new RuntimeException(String.format("config is not JSON format! config=%s", config));
        }

        mWindowConfig = mConfig.optJSONObject("window");
        if (mWindowConfig != null) {
            mPages = mWindowConfig.optJSONObject("pages");
        }

        mTimeoutConfig = mConfig.optJSONObject("networkTimeout");
        mEnableShark = mConfig.optBoolean("enableShark");
        mWebLongTapSaveImageToken = mConfig.optString("webLongTapSaveImageToken");
        JSONObject tabBarJson = mConfig.optJSONObject("tabBar");
        if (tabBarJson != null) {
            mTabBarConfig = new TabBarConfig();
            mTabBarConfig.custom = tabBarJson.optBoolean("custom", false);
            mTabBarConfig.color = tabBarJson.optString("color");
            mTabBarConfig.selectedColor = tabBarJson.optString("selectedColor");
            mTabBarConfig.backgroundColor = tabBarJson.optString("backgroundColor");
            mTabBarConfig.borderStyle = tabBarJson.optString("borderStyle");
            mTabBarConfig.borderColor = tabBarJson.optString("borderColor");
            mTabBarConfig.position = tabBarJson.optString("position");
            mTabBarConfig.list = tabBarJson.optJSONArray("list");
            ArrayList<String> tabList = new ArrayList<>();
            if (MSCHornRollbackConfig.isRollbackTabBarConfig() || mTabBarConfig.list != null) {
                int len = mTabBarConfig.list.length();
                for (int i = 0; i < len; i++) {
                    JSONObject itemJson = mTabBarConfig.list.optJSONObject(i);
                    if (itemJson != null) {
                        tabList.add(itemJson.optString("pagePath"));
                    }
                }
            }
            IFusionPageManager.AppInfo appInfo = new IFusionPageManager.AppInfo();
            appInfo.tabList = tabList;
            appInfo.defaultPageIsTab = tabList.contains(getRootPath());
            appInfo.isFusion = true;
            IFusionPageManager.appTabList.put(getRuntime().getMSCAppModule().getAppId(), appInfo);
        }
    }

    @NonNull
    public Map<String, SubPackagePreloadConfig> getPreloadRule() {
        if (mSubPackagePreloadRule == null) {
            mSubPackagePreloadRule = SubPackagePreloadConfig.extractPreloadRules(mConfig.optJSONObject("preloadRule"));
        }
        return mSubPackagePreloadRule;
    }

    /**
     * 获取TabBar背景色
     *
     * @return TabBar背景色
     */
    public String getTabBarBackgroundColor() {
        if (mTabBarConfig == null || TextUtils.isEmpty(mTabBarConfig.backgroundColor)
                || !mTabBarConfig.backgroundColor.startsWith("#")) {
            return "#ffffff";
        }
        return mTabBarConfig.backgroundColor;
    }

    @ColorInt
    public int getBackgroundColor(String url) {
        String ret = findInPagesAndWindow(url, "backgroundColor");
        MSCLog.i(TAG, "getBackgroundColor", ret);
        // TODO: 2024/8/19 为什么默认是白色
        return ColorUtil.parseColor(ret, Color.WHITE);
    }

    public String getBackgroundColorString(String url) {
        return findInPagesAndWindow(url, "backgroundColor");
    }

    public String getWidgetBackgroundColor(String url) {
        return findInPagesAndWindow(url, "widgetBackgroundColor");
    }

    public String getSinkModeBackgroundColor(String url) {
        return findInPagesAndWindow(url, "sinkModeBackgroundColor");
    }

    public boolean isBackgroundTextStyleDark(String url) {
        String ret = findInPagesAndWindow(url, "backgroundTextStyle");
        return !"light".equals(ret);
    }

    /**
     * 获取导航栏背景色(#000000)
     *
     * @return 导航栏背景色
     */
    public String getNavigationBarBackgroundColor(String url) {
        String ret = findInPagesAndWindow(url, "navigationBarBackgroundColor");
        return TextUtils.isEmpty(ret) ? "#FFFFFF" : ret;
    }

    /**
     * 获取导航栏文字颜色
     *
     * @return 导航栏文字颜色
     */
    public String getNavigationBarTextColor(String url) {
        String ret = findInPagesAndWindow(url, "navigationBarTextStyle");
        return "black".equals(ret) ? "#000000" : "#FFFFFF";
    }

    /**
     * 导航栏是否隐藏返回按钮
     *
     * @return 是否隐藏返回按钮
     */
    public boolean isDisableNavigationBack(String url) {
        return "true".equalsIgnoreCase(findInPagesAndWindow(url, "disableNavigationBack"));
    }

    /**
     * 获取胶囊按钮配置
     *
     * @return 导航栏胶囊按钮配置
     */
    public boolean isHideCapsuleButtons(String url) {
        String ret = findInPagesAndWindow(url, "hideCapsuleButtons");
        if (TextUtils.isEmpty(ret)) {
            return true;
        }
        return "true".equalsIgnoreCase(ret);
    }

    /**
     * 运行时保活结束时，是否允许触发业务预热
     *
     * @return 是否触发
     */
    public boolean shouldRePreload() {
        if (mConfig == null) {
            return false;
        }
        return mConfig.optBoolean("preloadAfterKilled", false);
    }

    public void setRouteMapping(Map<String, RouteMappingModule.RouteParams> routeMap) {
        mRouteMappings = routeMap;
    }

    // TODO: 2024/2/26 tianbin iOS端 setRouteMapping不支持Widget场景，待补充限制或iOS补充支持
    public String getRoutePathIfRouted(String path) {
        if (MSCHornRollbackConfig.rollbackSetRouteMapping()) {
            MSCLog.i(TAG, "getRoutePathIfRouted rollbackSetRouteMapping");
            return null;
        }
        if (mRouteMappings == null) {
            return null;
        }

        RouteMappingModule.RouteParams routeParams = mRouteMappings.get(PathUtil.getPath(path));
        if (routeParams == null) {
            return null;
        }
        String targetPath = routeParams.getTarget();

        // fixme 是否带"/" TODO: 2024/1/23 tianbin MMP迁移来的fixme，待确认
        if (targetPath == null) {
            return null;
        }
        int queryIndex = path.indexOf('?');
        if (queryIndex > 0) {
            String query = path.substring(queryIndex);
            targetPath += query;
        }
        MSCLog.i(TAG, "MSC routeMapping matched! from originUri:", path, "to TargetPath: ", targetPath);
        return targetPath;
    }


    public String getRoutePathPersistIfRouted(String path) {
        if (mRouteMappingsPersist == null) {
            return null;
        }
        RouteMappingModule.RouteParams routeParams = mRouteMappingsPersist.get(PathUtil.getPath(path));
        if (routeParams == null) {
            return null;
        }
        String targetPath = routeParams.getTarget();

        // fixme 是否带"/"
        if (targetPath == null) {
            return null;
        }
        int queryIndex = path.indexOf('?');
        if (queryIndex > 0) {
            String query = path.substring(queryIndex);
            targetPath += query;
        }
        MSCLog.i(TAG, "MSC routeMappingPersist matched! from originUri" + path + "to TargetPath " + targetPath);
        return targetPath;
    }

    /**
     * setRouteMappingPersist 路径对应的isTabDerived
     * 建议从openParams中取，url和isTabDerived需一一对应
     *
     * @param path
     * @return
     */
    public Boolean isTabDerivedPersist(String path) {
        if (mRouteMappingsPersist == null) {
            return null;
        }
        RouteMappingModule.RouteParams routeParams = mRouteMappingsPersist.get(PathUtil.getPath(path));
        if (routeParams == null) {
            return null;
        }
        return routeParams.isTabDerived();
    }

    /**
     * setRouteMapping 路径对应的isTabDerived
     * 建议从openParams中取，url和isTabDerived需一一对应
     *
     * @param path
     * @return
     */
    public Boolean isTabDerived(String path) {
        if (mRouteMappings == null) {
            return null;
        }
        RouteMappingModule.RouteParams routeParams = mRouteMappings.get(PathUtil.getPath(path));
        if (routeParams == null) {
            return null;
        }
        return routeParams.isTabDerived();
    }

    public boolean isOnlyExternalRouterPersist(String path) {
        if (mRouteMappingsPersist == null) {
            return false;
        }
        RouteMappingModule.RouteParams routeParams = mRouteMappingsPersist.get(PathUtil.getPath(path));
        if (routeParams == null) {
            return false;
        }
        return routeParams.isOnlyExternalRouter();
    }

    public boolean isOnlyExternalRouter(String path) {
        if (mRouteMappings == null) {
            return false;
        }
        RouteMappingModule.RouteParams routeParams = mRouteMappings.get(PathUtil.getPath(path));
        if (routeParams == null) {
            return false;
        }
        return routeParams.isOnlyExternalRouter();
    }

    public void applyRouteMapping() {
        if (MSCHornRollbackConfig.rollbackSetRouteMapping()) {
            MSCLog.i(TAG, "applyRouteMapping rollbackSetRouteMapping");
            return;
        }
        RouteMappingModule.Mappings routeMapping = RouteMappingModule.getRouteMapping(getRuntime().getAppId(),
                getRuntime().getMSCAppModule().getPublishId());
        setRouteMapping(RouteMappingModule.getRouteMap(routeMapping, false));
    }

    // 分为三个状态 静态（只读） 动态（可更改） 无
    public enum InitialRenderingCacheState {
        STATIC, DYNAMIC, NONE
    }

    /**
     * 初始渲染缓存配置
     *
     * @param url 页面地址
     * @return 初始渲染缓存状态
     */
    public InitialRenderingCacheState obtainInitialRenderingCacheState(String url) {
        final String state = findInPagesAndWindow(url, "initialRenderingCache");
        if ("static".equals(state)) {
            return InitialRenderingCacheState.STATIC;
        } else if ("dynamic".equals(state)) {
            return InitialRenderingCacheState.DYNAMIC;
        } else {
            return InitialRenderingCacheState.NONE;
        }
    }

    public boolean obtainInitialRenderingSnapshotState(String url) {
        return "true".equalsIgnoreCase(findInPagesAndWindow(url, "initialRenderingSnapshot"));
    }

    public boolean isCustomNavigationStyle(String url) {
        return "custom".equalsIgnoreCase(findInPagesAndWindow(url, "navigationStyle"));
    }

    /**
     * 获取页面的标题
     *
     * @param url 页面路径
     * @return 页面标题
     */
    public String getPageTitle(String url) {
        return findInPagesAndWindow(url, "navigationBarTitleText");
    }


    /**
     * 判断页面是否启用了下拉刷新
     *
     * @param url 页面路径
     * @return true：启用了下拉刷新，否则亦然
     */
    public boolean isEnablePullDownRefresh(String url) {
        return "true".equalsIgnoreCase(findInPagesAndWindow(url, "enablePullDownRefresh"));
    }

    /**
     * 是否是自定义TabBar
     *
     * @return isCustomTabBar
     */
    public boolean isCustomTabBar() {
        if (mTabBarConfig != null) {
            return mTabBarConfig.custom;
        }
        return false;
    }

    public String getPermissionDesc(String scope) {
        JSONObject permissionObject = getPermissionObject(scope);
        return permissionObject != null ? permissionObject.optString("desc") : null;
    }

    /**
     * 各类网络请求的超时时间，单位均为毫秒，request/connectSocket/uploadFile/downloadFile
     *
     * @param key
     * @return
     */
    public int getNetworkTimeout(String key) {
        if (mTimeoutConfig != null) {
            return mTimeoutConfig.optInt(key, 60_000);
        }

        return 60_000;
    }

    public int getRequestTimeout() {
        if (mTimeoutConfig != null) {
            return mTimeoutConfig.optInt("request", 60_000);
        }

        return 60_000;
    }

    /**
     * enableShark 需要支持Shark长连
     *
     * @return
     */
    public boolean isEnableShark() {
        return mEnableShark;
    }

    /**
     * 获取TabBar上边框的颜色
     *
     * @return TabBar上边框的颜色
     */
    public String getTabBarBorderColor() {
        if (mTabBarConfig != null && !TextUtils.isEmpty(mTabBarConfig.borderColor)) {
            return mTabBarConfig.borderColor;
        }
        if (mTabBarConfig != null && "white".equals(mTabBarConfig.borderStyle)) {
            return TabBarApi.WHITE;
        }
        return TabBarApi.BLACK;
    }

    /**
     * 检查小程序app.json requiredBackgroundModes配置
     * 小程序配置 app.json 中的 requiredBackgroundModes 数组包含 location 元素
     *
     * @return
     */
    public boolean isInRequiredBackgroundModes() {
        if (mConfig == null) {
            return false;
        }
        JSONArray requiredBackgroundModesArray = mConfig.optJSONArray("requiredBackgroundModes");
        if (requiredBackgroundModesArray != null) {
            for (int i = 0; i < requiredBackgroundModesArray.length(); i++) {
                if (TextUtils.equals("location", requiredBackgroundModesArray.optString(i))) {
                    return true;
                }
            }
        }
        return false;
    }

    /**
     * 检查小程序app.json enableMSCAppPreload配置
     * 小程序配置 app.json 中的 enableMSCAppPreload 的 boolean值
     *
     * @return
     */
    public boolean isEnableMSCAppPreload() {
        if (mConfig == null) {
            return false;
        }
        return mConfig.optBoolean("enableMSCAppPreload", false);
    }

    private String findInPagesAndWindow(String url, String param) {
        // 应用全局、页面全局均未定义
        if (mWindowConfig == null && mPages == null) {
            return null;
        }

        // 页面全局未定义 返回应用全局配置
        if (mPages != null) {// 取具体页面配置
            JSONObject item = mPages.optJSONObject(PathUtil.getPath(url));
            if (item != null) {
                // 通常空字符串按未传处理
                // 仅对navigationBarTitleText，页面级配置为空字符串时有实际含义，表示title不显示，为null时则使用全局app-config配置
                //https://ones.sankuai.com/ones/product/21594/workItem/defect/detail/8571753
                if (TextUtils.equals(param, "navigationBarTitleText")) {
                    Object object = item.opt(param);
                    if (object != null) {
                        return object.toString();
                    }
                } else {
                    String optString = item.optString(param);
                    if (!TextUtils.isEmpty(optString)) {
                        return optString;
                    }
                }
            }
        }
        // 如果具体页面有值 则以具体页面为准，否则以应用全局为准
        return mWindowConfig != null ? mWindowConfig.optString(param) : null;
    }


    private JSONObject getPermissionObject(String scope) {
        if (mConfig == null) {
            return null;
        }

        JSONObject permission = mConfig.optJSONObject("permission");
        if (permission == null) {
            return null;
        }

        return permission.optJSONObject(scope);
    }

    public boolean hasConfig() {
        return mConfig != null;
    }

    /**
     * 供定制Config解析全局配置
     *
     * @return mWindowConfig
     */
    public JSONObject getWindowConfig() {
        return mWindowConfig;
    }

    /**
     * 获取所有页面信息
     *
     * @return
     */
    public JSONObject getPages() {
        return mPages;
    }

    /**
     * 获取配置的根路径
     *
     * @return 根路径
     */
    public String getRootPath() {
        if (mConfig == null) {
            throw new RuntimeException("getRootPath when config null");
        }
        String root = mConfig.optString("root");
        return TextUtils.isEmpty(root) ? "" : root;
    }

    public boolean hasPage(String checkPath) {
        if (mPages == null) {
            return false;
        }
        String path = PathUtil.getPath(checkPath);
        return mPages.has(path);
    }

    /**
     * TabBar是否在顶部
     *
     * @return true：顶部
     */
    public boolean isTopTabBar() {
        if (mTabBarConfig != null && "top".equals(mTabBarConfig.position)) {
            return true;
        }
        return false;
    }

    /**
     * 获取Tab项列表
     *
     * @return Tab项列表
     */
    public List<TabItemInfo> getTabItemList() {
        if (mTabBarConfig == null || mTabBarConfig.list == null) {
            return null;
        }

        List<TabItemInfo> list = new ArrayList<>();
        int len = mTabBarConfig.list.length();
        for (int i = 0; i < len; i++) {
            JSONObject itemJson = mTabBarConfig.list.optJSONObject(i);
            if (itemJson == null || itemJson.length() == 0) {
                continue;
            }
            TabItemInfo info = new TabItemInfo();
            info.color = mTabBarConfig.color;
            info.selectedColor = mTabBarConfig.selectedColor;
            info.iconPath = itemJson.optString("iconPath");
            info.selectedIconPath = itemJson.optString("selectedIconPath");
            info.text = itemJson.optString("text");
            info.pagePath = itemJson.optString("pagePath");
            info.isLargerIcon = itemJson.optBoolean("isLargerIcon");
            list.add(info);
        }
        return list;
    }

    /**
     * 检查被给的url是否属于Tab页面
     *
     * @param url 页面路径
     * @return true：是Tab页，否则亦然
     */
    public boolean isTabPage(String url) {
        if (TextUtils.isEmpty(url)) {
            return false;
        }
        if (mTabBarConfig == null || mTabBarConfig.list == null) {
            return false;
        }
        String pagePath = PathUtil.getPath(url);
        int len = mTabBarConfig.list.length();
        for (int i = 0; i < len; i++) {
            JSONObject itemJson = mTabBarConfig.list.optJSONObject(i);
            if (itemJson != null && pagePath.equals(itemJson.optString("pagePath"))) {
                return true;
            }
        }
        return false;
    }

    public RendererType getRendererTypeForPage(String url) {
        String path = PathUtil.getPath(url);
        String renderType;
        RendererType rendererType = null;
        JSONObject pages = this.mPages;
        JSONObject jsonObject;
        if (pages != null && (jsonObject = pages.optJSONObject(path)) != null) {
            renderType = jsonObject.optString("rendererType");
            rendererType = RendererType.fromString(renderType);
        }
        if (rendererType == null) {
            rendererType = RendererType.WEBVIEW;
        }
        //temp default
        return rendererType;
    }

    /**
     * 检查被给的url是否开启c++渲染
     *
     * @param url 页面路径
     * @return true表示开启c++渲染，否则不开启
     */
    public boolean cppRendererEnabled(String url) {
        String path = PathUtil.getPath(url);
        JSONObject pages = this.mPages;
        JSONObject jsonObject;
        if (pages != null && (jsonObject = pages.optJSONObject(path)) != null) {
            if (jsonObject.has("enableCppRenderer")) {
                Object obj = jsonObject.opt("enableCppRenderer");
                if (obj instanceof Boolean) {
                    return (Boolean)obj;
                }
            }
        }
        return false;
    }

    /**
     * 在此页面时，下级页面应接着预加载哪些页面
     * https://km.sankuai.com/page/898455742
     */
    @Nullable
    public List<String> getPreloadPagesForThisPage(String url) {
        String path;
        if ("/".equals(url)) {
            path = "/"; //特殊路径，表示未进入页面时应预加载此配置
        } else {
            path = PathUtil.getPath(url);
        }

        if (pagePreloadRules == null) {
            Map<String, List<String>> newRules = new HashMap<>();
            JSONObject object = null;
            if (mConfig != null) {
                object = mConfig.optJSONObject("pagePreloadRule");
            }

            if (object != null) {
                //深度预加载默认支持5个页面，可动态配置
                int limit = MSCConfig.getWebViewResourceLimit();
                Iterator<String> pages = object.keys();
                while (pages.hasNext()) {
                    String page = pages.next();
                    JSONArray resArr = object.optJSONArray(page);
                    if (resArr == null) {
                        continue;
                    }
                    List<String> resList = new ArrayList<>();
                    for (int i = 0; i < resArr.length(); i++) {
                        if (i >= limit) {
                            MSCLog.w(TAG, resArr.length() + " resources for page " + page + " exceeds limit " + limit + ", ignore more");
                            break;
                        }
                        String res = resArr.optString(i);
                        if (res != null && hasPage(res)) {
                            resList.add(res);
                        } else {
                            MSCLog.w(TAG, "invalid resource path in pagePreloadRules: " + res);
                        }
                    }
                    newRules.put(page, resList);
                }
            }
            pagePreloadRules = newRules;
        }

        return pagePreloadRules.get(path);
    }

    public boolean isWebViewRecycleEnabled() {
        return MSCConfig.isWebViewRecycleEnabled() && mConfig != null && mConfig.optBoolean("enableWebViewRecycle", true);
    }

    public String getWebLongTapSaveImageToken() {
        return mWebLongTapSaveImageToken;
    }

    public void setRouteMappingsPersist(Map<String, RouteMappingModule.RouteParams> mappings) {
        mRouteMappingsPersist = mappings;
    }

    public void applyRouteMappingPersist() {
        String curBuildIdStr = getRuntime().getMSCAppModule().getBuildId();
        if (TextUtils.isEmpty(curBuildIdStr)) {
            return;
        }
        if (MSCHornRollbackConfig.enableFixRouteMappingValidVersion()) {
            RouteMappingModule.Mappings routeMapping = RouteMappingModule.getRouteMappingPersist(getRuntime().getAppId());
            if (routeMapping != null && routeMapping.isValidVersionPersist(curBuildIdStr)) {
                MSCLog.i(TAG, "applyRouteMappingPersist");
                setRouteMappingsPersist(RouteMappingModule.getRouteMap(routeMapping, true));
            } else {
                MSCLog.i(TAG, "downgrade clearSetRouteMappingPersist");
                RouteMappingModule.innerClearSetRouteMappingPersist(getRuntime());
            }
        } else {
            long curBuildId = Long.parseLong(curBuildIdStr);
            long exBuildId = RouteMappingModule.getRouteMappingPersistBuildId();
            if (curBuildId < exBuildId) {
                MSCLog.i(TAG, "downgrade clearSetRouteMappingPersist");
                RouteMappingModule.innerClearSetRouteMappingPersist(getRuntime());
            } else {
                MSCLog.i(TAG, "applyRouteMappingPersist");
                setRouteMappingsPersist(RouteMappingModule.getRouteMap(RouteMappingModule.getRouteMappingPersist(getRuntime().getAppId()), true));
            }
            RouteMappingModule.setRouteMappingPersistBuildId(curBuildId);
        }
    }

    /**
     * TabBar配置
     */
    private static class TabBarConfig {
        boolean custom; //是否支持自定义TabBar
        String color; //tab 上的文字默认颜色
        String selectedColor; //tab 上的文字选中时的颜色
        String backgroundColor; //tab 的背景色
        String borderStyle; //tabbar上边框的颜色， 仅支持 black / white
        String borderColor; //tabbar 上边框的颜色，仅支持十六进制颜色，优先级高于 borderStyle
        String position; //可选值 bottom、top
        JSONArray list; //tab 的列表，详见 list 属性说明，最少2个、最多5个 tab
    }
}
