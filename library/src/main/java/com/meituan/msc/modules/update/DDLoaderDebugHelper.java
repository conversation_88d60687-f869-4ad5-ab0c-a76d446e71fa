package com.meituan.msc.modules.update;

import android.content.SharedPreferences;

import com.meituan.msc.common.utils.MSCSharePreferenceHelper;
import com.meituan.msc.extern.MSCEnvHelper;

public final class DDLoaderDebugHelper {

    private static final String KEY_HOST_BETA_ENABLE = "msc_host_beta_enable";
    private static final String KEY_MSC_DD_LOADER_DEBUG_ENABLE = "msc_dd_loader_debug_enable";

    private static SharedPreferences getDefaultSharedPreferences() {
        return MSCSharePreferenceHelper.getDefaultSharedPreferences();
    }

    public static void setCheckUpdateFromTestEnv(boolean enable) {
        getDefaultSharedPreferences().edit()
                .putBoolean(KEY_HOST_BETA_ENABLE, enable)
                .apply();
    }

    public static boolean shouldCheckUpdateFromTestEnv() {
        return getDefaultSharedPreferences().getBoolean(KEY_HOST_BETA_ENABLE, false);
    }

    /**
     * 如果需要使用仅发布到美团上的前端基础包或小程序，需要开启DD的调试模式
     *
     * @param enable 是否开启
     */
    public static void setDDLoaderDebugEnable(boolean enable) {
        getDefaultSharedPreferences().edit()
                .putBoolean(KEY_MSC_DD_LOADER_DEBUG_ENABLE, enable)
                .apply();
    }

    public static boolean enableDDLoaderDebug() {
        if (isProdEnv()) {
            return false;
        }
        return getDefaultSharedPreferences().getBoolean(KEY_MSC_DD_LOADER_DEBUG_ENABLE, false);
    }

    private static boolean isProdEnv() {
        return MSCEnvHelper.getEnvInfo().isProdEnv();
    }
}
