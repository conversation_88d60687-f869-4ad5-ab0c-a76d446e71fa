package com.meituan.msc.modules.update;

import com.meituan.met.mercury.load.bean.DDLoadPhaseData;
import com.meituan.msc.common.support.java.util.concurrent.CompletableFuture;
import com.meituan.msc.modules.update.bean.AppMetaInfoWrapper;
import com.meituan.msc.modules.update.bean.LaunchPageParams;
import com.meituan.msc.modules.update.bean.PackageInfoWrapper;

import java.util.List;

/**
 * Created by letty on 2019/7/29.
 **/
public interface IPageLoadModule {

    /**
     * 获取小程序元信息
     *
     * @param params
     * @return
     */
    CompletableFuture<AppMetaInfoWrapper> fetchAppMetaInfo(LaunchPageParams params);


    /**
     * 下载包资源
     *
     * @param path     path
     * @param callback callback
     */
    void downLoadBizPackages(String path, String loadScene, long routeId, PackageDownloadCallback callback);

    /**
     * 下载并缓存子包资源。
     *
     * @param packageInfoWrapper 子包信息
     * @param callback 回调
     */
    void downloadSubPackage(PackageInfoWrapper packageInfoWrapper, String loadScene,PackageDownloadCallback callback);

    /**
     * 注入包资源
     *
     * @param packageInfoWrapperList list
     * @param callback               callback
     */
    @Deprecated
    void injectPackages(List<PackageInfoWrapper> packageInfoWrapperList, PackageInjectCallback callback);

    /**
     * 子页面启动
     *
     * @param path              页面路径
     * @param showLoadingDialog 是否显示loading
     * @param callback          页面启动结果回调
     */
//    void loadPage(String path, boolean showLoadingDialog, @NonNull final PageLoadCallback callback);

    /**
     * 注入包资源
     *
     * @param targetPath 页面路径
     * @param callback 回调
     */
    void injectPackages(String targetPath, PackageInjectCallback callback);

    DDLoadPhaseData getLoadPackageDetails(long routeId, boolean isMainPackage);
}
