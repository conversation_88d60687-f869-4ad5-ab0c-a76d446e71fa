package com.meituan.msc.modules.update;

import android.text.TextUtils;

import com.meituan.msc.modules.engine.MSCHornRollbackConfig;
import com.meituan.msc.modules.engine.MSCRuntime;
import com.meituan.msc.modules.update.bean.PackageInfoWrapper;

import java.io.File;
import java.util.ArrayList;
import java.util.List;

/**
 * JSError上报需求 https://km.sankuai.com/page/1298806147
 */
public final class JSFileSourceHelper {

    public static final String UNKNOWN_SOURCE = "unknown";

    /**
     * combo模式注入时调用
     */
    public static String getSourceOfImportCombo(String[] fileUris) {
        if (fileUris == null) {
            return UNKNOWN_SOURCE;
        }

        String source;
        if (fileUris.length > 1) {
            StringBuilder stringBuilder = new StringBuilder();
            stringBuilder.append("/");
            for (String url : fileUris) {
                stringBuilder.append(getSourceOfImportComboInner(url));
                stringBuilder.append(",");
            }
            source = stringBuilder.substring(0, stringBuilder.length() - 1);
        } else {
            String fileUri = fileUris[0];
            source = "/" + getSourceOfImportComboInner(fileUri);
        }
        return source;
    }

    public static String getSourceOfImport(String url) {
        return "/" + getSourceOfImportComboInner(url);
    }

    private static String getSourceOfImportComboInner(String url) {
        String result = UNKNOWN_SOURCE;
        if (url.startsWith(MSCAppModule.PREFIX_FRAMEWORK)) {
            //框架包
            result = url.replace(MSCAppModule.PREFIX_FRAMEWORK, "mscsdk_base.dio/");
        } else if (url.startsWith(MSCAppModule.PREFIX_MAIN_APP)) {
            // 主包
            result = url.replace(MSCAppModule.PREFIX_MAIN_APP, "main_app.dio/");
        } else if (url.startsWith(MSCAppModule.PREFIX_APP)) {
            //子包
            int index = url.indexOf(File.separatorChar, MSCAppModule.PREFIX_APP_LENGTH);
            if (index > MSCAppModule.PREFIX_APP_LENGTH) {
                String subPackName = url.substring(MSCAppModule.PREFIX_APP_LENGTH, index);
                result = url.replace(MSCAppModule.PREFIX_APP + subPackName, subPackName + ".dio");
            }
        }
        return result;
    }

    public static String getPkgNameFromUrl(String url, MSCRuntime mscRuntime) {
        String result = UNKNOWN_SOURCE;
        if (url.startsWith(MSCAppModule.PREFIX_FRAMEWORK)) {
            //框架包
            result = "mscsdk_base";
        } else if (url.startsWith(MSCAppModule.PREFIX_MAIN_APP)) {
            // 主包
            result = MSCAppModule.MAIN_PACKAGE_NAME;
        } else if (url.startsWith(MSCAppModule.PREFIX_APP)) {
            //子包
            int index = url.indexOf(File.separatorChar, MSCAppModule.PREFIX_APP_LENGTH);
            if (index > MSCAppModule.PREFIX_APP_LENGTH) {
                result = url.substring(MSCAppModule.PREFIX_APP_LENGTH, index);;
            }
        }

        return result;
    }

    public static String[] getPkgNamesFromUrl(String[] fileUris, MSCRuntime mscRuntime) {
        if (!MSCHornRollbackConfig.enableUseNewInjectPackageRate()) {
            return null;
        }
        List<String> pkgNames = new ArrayList<>();
        String pkgName;
        if (fileUris != null && fileUris.length > 0) {
            for (String url : fileUris) {
                pkgName = getPkgNameFromUrl(url, mscRuntime);
                if (pkgName == null) {
                    pkgName = "";
                }
                pkgNames.add(pkgName);
            }
        }

        return pkgNames.toArray(new String[0]);
    }

    /**
     * 基础包、主包注入时调用
     */
    public static String getSourceOfImportServiceFile(PackageInfoWrapper packageInfoWrapper) {
        if (TextUtils.isEmpty(packageInfoWrapper.getLocalPath())) {
            return UNKNOWN_SOURCE;
        }
        if (packageInfoWrapper.packageType == PackageInfoWrapper.PACKAGE_TYPE_BASE) {
            return "/" + packageInfoWrapper.getDDResourceName() + ".dio/" + PackageInfoWrapper.PACKAGE_FRAMEWORK_SERVICE_FILE;
        } else if (packageInfoWrapper.packageType == PackageInfoWrapper.PACKAGE_TYPE_MAIN) {
            return "/" + "main_app.dio/" + PackageInfoWrapper.PACKAGE_SERVICE_FILE;
        } else if (packageInfoWrapper.packageType == PackageInfoWrapper.PACKAGE_TYPE_SUB) {
            return "/" + packageInfoWrapper.getPackageName() + ".dio/" + PackageInfoWrapper.PACKAGE_SERVICE_FILE;
        }
        return UNKNOWN_SOURCE;
    }

    /**
     * 生成code cache时调用
     * eg:
     * app-service.js
     * comps/text.service.js
     */
    public static String getJSFileSourceForCodeCache(PackageInfoWrapper packageInfoWrapper, String childFilePath) {
        if (TextUtils.isEmpty(packageInfoWrapper.getLocalPath())) {
            return UNKNOWN_SOURCE;
        }
        if (packageInfoWrapper.packageType == PackageInfoWrapper.PACKAGE_TYPE_BASE) {
            return "/" + packageInfoWrapper.getDDResourceName() + ".dio/" + childFilePath;
        } else if (packageInfoWrapper.packageType == PackageInfoWrapper.PACKAGE_TYPE_MAIN) {
            return "/" + "main_app.dio/" + childFilePath;
        } else if (packageInfoWrapper.packageType == PackageInfoWrapper.PACKAGE_TYPE_SUB) {
            return "/" + packageInfoWrapper.getDDResourceName() + ".dio/" + childFilePath;
        }
        return UNKNOWN_SOURCE;
    }
}
