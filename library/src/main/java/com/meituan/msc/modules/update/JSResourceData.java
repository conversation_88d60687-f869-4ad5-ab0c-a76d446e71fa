package com.meituan.msc.modules.update;

import com.meituan.dio.easy.DioFile;
import com.meituan.msc.modules.update.bean.PackageInfoWrapper;

public class JSResourceData {

    public PackageInfoWrapper infoWrapper;
    public DioFile jsResourceFile;
    public String fileUrl;

    public JSResourceData(DioFile jsResourceFile) {
        this.jsResourceFile = jsResourceFile;
    }

    public JSResourceData(PackageInfoWrapper infoWrapper, DioFile jsResourceFile, String fileUrl) {
        this.infoWrapper = infoWrapper;
        this.jsResourceFile = jsResourceFile;
        this.fileUrl = fileUrl;
    }

    public String getPackageName() {
        if (infoWrapper != null) {
            return infoWrapper.getPackageName();
        }

        if (jsResourceFile != null) {
            return jsResourceFile.getName();
        }

        return null;
    }
}
