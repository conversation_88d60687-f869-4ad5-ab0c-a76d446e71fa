package com.meituan.msc.modules.update;

import android.support.annotation.NonNull;
import android.support.annotation.Nullable;

import com.meituan.android.turbo.Turbo;
import com.meituan.android.turbo.exceptions.JsonParseException;
import com.meituan.msc.common.abtest.IMSCABTestKV;
import com.meituan.msc.common.abtest.MSCABTestCache;
import com.meituan.msc.common.abtest.MSCABTestManager;
import com.meituan.msc.modules.manager.IMSCCompletableCallback;
import com.meituan.msc.modules.manager.MSCMethod;
import com.meituan.msc.modules.manager.MSCModule;
import com.meituan.msc.modules.manager.ModuleName;
import com.meituan.msc.modules.reporter.MSCLog;
import com.sankuai.meituan.abtestv2.mode.ABTestStrategy;

import org.json.JSONArray;
import org.json.JSONException;
import org.json.JSONObject;

import java.util.HashMap;

/**
 * MSC 容器 ABTest 模块，管理 MSC 桥和容器内 ABTest 生命周期的缓存
 * <p>
 * 桥方案：https://km.sankuai.com/collabpage/2712825238
 */
@ModuleName(name = "ABTest")
public class MSCABTestModule extends MSCModule implements IMSCABTestKV {
    private static final String TAG = "MSCABTestModule";
    public static final int ABTEST_CACHE_TYPE_TIMELY = 0;
    public static final int ABTEST_CACHE_TYPE_MINIAPP = 1;
    public static final int ABTEST_CACHE_TYPE_GLOBAL = 2;

    private final MSCABTestCache mscABTestCache = new MSCABTestCache();

    @MSCMethod(isSync = false)
    public void getMSCABAsync(@NonNull JSONArray keys, IMSCCompletableCallback<JSONObject> callback) throws JSONException, JsonParseException {
        callback.onComplete(getMSCABSync(keys));
    }

    @MSCMethod(isSync = true)
    public JSONObject getMSCABSync(@NonNull JSONArray keys) {
        JSONObject result = new JSONObject();
        for (int i = 0; i < keys.length(); i++) {
            JSONObject keyObj = keys.optJSONObject(i);
            checkBridgeKeyObject(keyObj);
            if (keyObj == null) {
                continue;
            }
            try {
                String testKey = keyObj.optString("testKey");
                int cacheType = keyObj.getInt("cacheType");
                if (!isTestKeyAndCacheTypeValid(testKey, cacheType)) {
                    continue;
                }
                ABTestStrategy abTestStrategy;
                if (cacheType == ABTEST_CACHE_TYPE_GLOBAL) {
                    abTestStrategy = MSCABTestManager.getGlobalDetailStrategy(testKey);
                } else if (cacheType == ABTEST_CACHE_TYPE_MINIAPP) {
                    abTestStrategy = MSCABTestManager.getMiniAppDetailStrategy(getRuntime(), testKey);
                } else {
                    abTestStrategy = MSCABTestManager.getTimelyDetailStrategy(testKey);
                }
                if (abTestStrategy == null) {
                    abTestStrategy = new ABTestStrategy();
                    abTestStrategy.testKey = testKey;
                    abTestStrategy.extraMap = new HashMap<>();
                }
                JSONObject abTestObj = new JSONObject();
                abTestObj.putOpt("strategyKey", abTestStrategy.strategyKey);
                abTestObj.putOpt("strategyInfo", abTestStrategy.strategyInfo);
                abTestObj.putOpt("testKey", abTestStrategy.testKey);
                abTestObj.putOpt("flowKey", abTestStrategy.flowKey);
                abTestObj.putOpt("flowStrategy", abTestStrategy.flowStrategy);
                abTestObj.putOpt("layerId", abTestStrategy.layerId);
                if (abTestStrategy.extraMap != null) {
                    abTestObj.putOpt("params", new JSONObject(Turbo.toJson(abTestStrategy.extraMap)));
                }
                result.putOpt(testKey, abTestObj);
            } catch (Exception e) {
                MSCLog.e(TAG, e);
            }
        }
        return result;
    }

    private boolean isTestKeyAndCacheTypeValid(String testKey, int cacheType) {
        return testKey != null && !testKey.isEmpty() && cacheType >= ABTEST_CACHE_TYPE_TIMELY && cacheType <= ABTEST_CACHE_TYPE_GLOBAL;
    }

    /**
     * hook 验证参数
     */
    private void checkBridgeKeyObject(JSONObject keyObject) {
    }

    @Nullable
    public String getStrategy(@NonNull String key) {
        return mscABTestCache.getStrategy(key);
    }

    @Nullable
    public ABTestStrategy getDetailStrategy(@NonNull String key) {
        return mscABTestCache.getDetailStrategy(key);
    }

    @Override
    public void onDestroy() {
        super.onDestroy();
        mscABTestCache.clear();
    }
}
