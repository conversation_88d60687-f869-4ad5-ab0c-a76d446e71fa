package com.meituan.msc.modules.update;

import android.support.annotation.ColorInt;
import android.support.annotation.NonNull;
import android.support.annotation.Nullable;
import android.text.TextUtils;

import com.meituan.android.mercury.msc.adaptor.bean.MSCAppMetaInfo;
import com.meituan.dio.DioEntry;
import com.meituan.dio.easy.DioFile;
import com.meituan.msc.common.utils.CollectionUtil;
import com.meituan.msc.common.utils.FileScope;
import com.meituan.msc.modules.apploader.events.AppLoadException;
import com.meituan.msc.modules.core.FileModule;
import com.meituan.msc.modules.engine.MSCHornRollbackConfig;
import com.meituan.msc.modules.engine.ReportUtils;
import com.meituan.msc.modules.manager.MSCModule;
import com.meituan.msc.modules.manager.MSCRuntimeException;
import com.meituan.msc.modules.manager.ModuleName;
import com.meituan.msc.modules.page.render.RendererType;
import com.meituan.msc.modules.page.render.webview.WebViewCacheManager;
import com.meituan.msc.modules.page.view.tab.TabItemInfo;
import com.meituan.msc.modules.reporter.MSCLoadErrorConstants;
import com.meituan.msc.modules.reporter.MSCLog;
import com.meituan.msc.modules.update.bean.AppMetaInfoWrapper;
import com.meituan.msc.modules.update.bean.PackageInfoWrapper;

import java.io.File;
import java.io.IOException;
import java.util.ArrayList;
import java.util.List;
import java.util.concurrent.ConcurrentHashMap;

@ModuleName(name = "MSCAppModule")
public class MSCAppModule extends MSCModule implements IMSCAppModule {

    private final String TAG = "MSCAppModule@" + Integer.toHexString(hashCode());
    /**
     * WebView组件版本号
     */
    public static String WEB_VIEW_VERSION;
    /**
     * WebView组件类型
     */
    public static WebViewCacheManager.WebViewType WEB_VIEW_TYPE;
    //微信默认__APP__
    public static final String MAIN_PACKAGE_NAME = "main_app";
    public static final String PREFIX_FRAMEWORK = "/__framework/";
    public static final String PREFIX_APP = "/__app/";
    public static final String PREFIX_MAIN_APP = PREFIX_APP + MAIN_PACKAGE_NAME + "/";
    public static final int PREFIX_APP_LENGTH = PREFIX_APP.length();
    public static final String WEIXING_UA = "MicroMessenger/6.5.7  miniprogram";

    /**
     * 当前运行时未获取到版本信息，按预期不会出现。如出现，则DDD标记删除逻辑存在Bug
     */
    public static final String OFFLINE_BIZ_FAILED_REASON_NO_VERSION_INFO = "no_meta_info";
    /**
     * 当前运行时已挂载页面，运行时使用中不可销毁
     */
    public static final String OFFLINE_FAILED_REASON_ATTACHED_PAGE = "attached_page";
    /**
     * 当前运行时未获取到基础库版本信息
     */
    public static final String OFFLINE_BASE_FAILED_REASON_NO_BASE_PACKAGE_INFO = "no_basepkg_info";

    // private String mShareEnvironment = ShareUiApi.SHARE_ENVIRONMENT_RELEASE;
    // FIXME: 2022/2/17 分享环境相关 待和msi确认后传过去
    private String mShareEnvironment = "release";

    // 是否三方小程序，来自跳链
    private boolean isUrlExternalApp = false;

    // 小程序Id，来自启动参数
    public String appIdOfScheme;
    // 当前启动的小程序元信息，生命周期短于MSCAppModule
    @Nullable
    private volatile AppMetaInfoWrapper metaInfo;
    // 当前小程序使用的前端基础包，生命周期短于MSCAppModule
    private volatile PackageInfoWrapper basePackageInfo;
    // 线下环境 预热指定版本基础库时使用
    private String basePkgVersionOfDebug;
    // 下线业务包失败原因
    private String offlineBizFailReason;
    // 下线基础包失败原因
    private String offlineBaseFailReason;
    // 当前小程序下的js error
    private ConcurrentHashMap<String, Long> recentError = new ConcurrentHashMap();

    public MSCAppModule() {
    }
    /**
     * 获取TabBar背景色
     *
     * @return TabBar背景色
     */
    public String getTabBarBackgroundColor() {
        return getRuntime().getAppConfigModule().getTabBarBackgroundColor();
    }

    /**
     * 是否是自定义TabBar
     *
     * @return isCustomTabBar
     */
    public boolean isCustomTabBar() {
        return getRuntime().getAppConfigModule().isCustomTabBar();
    }

    /**
     * 各类网络请求的超时时间，单位均为毫秒，request/connectSocket/uploadFile/downloadFile
     *
     * @param key key
     * @return getNetworkTimeout
     */
    public int getNetworkTimeout(String key) {
        return getRuntime().getAppConfigModule().getNetworkTimeout(key);
    }

    public int getRequestTimeout() {
        return getRuntime().getAppConfigModule().getRequestTimeout();
    }

    /**
     * 获取TabBar上边框的颜色
     *
     * @return TabBar上边框的颜色
     */
    public String getTabBarBorderColor() {
        return getRuntime().getAppConfigModule().getTabBarBorderColor();
    }

    /**
     * 检查小程序app.json requiredBackgroundModes配置
     * 小程序配置 app.json 中的 requiredBackgroundModes 数组包含 location 元素
     *
     * @return isInRequiredBackgroundModes
     */
    public boolean isInRequiredBackgroundModes() {
        return getRuntime().getAppConfigModule().isInRequiredBackgroundModes();
    }


    public String getPermissionDesc(String scope) {
        return getRuntime().getAppConfigModule().getPermissionDesc(scope);
    }


    @ColorInt
    public int getBackgroundColor(String url) {
        return getRuntime().getAppConfigModule().getBackgroundColor(url);
    }

    public String getBackgroundColorString(String url) {
        return getRuntime().getAppConfigModule().getBackgroundColorString(url);
    }

    public String getWidgetBackgroundColor(String url) {
        return getRuntime().getAppConfigModule().getWidgetBackgroundColor(url);
    }

    public String getSinkModeBackgroundColor(String url) {
        return getRuntime().getAppConfigModule().getSinkModeBackgroundColor(url);
    }

    public boolean isBackgroundTextStyleDark(String url) {
        return getRuntime().getAppConfigModule().isBackgroundTextStyleDark(url);
    }

    /**
     * 获取导航栏背景色(#000000)
     *
     * @return 导航栏背景色
     */
    public String getNavigationBarBackgroundColor(String url) {
        return getRuntime().getAppConfigModule().getNavigationBarBackgroundColor(url);
    }

    /**
     * 获取导航栏文字颜色
     *
     * @return 导航栏文字颜色
     */
    public String getNavigationBarTextColor(String url) {
        return getRuntime().getAppConfigModule().getNavigationBarTextColor(url);
    }

    /**
     * 导航栏是否隐藏返回按钮
     *
     * @return 是否隐藏返回按钮
     */
    public boolean isDisableNavigationBack(String url) {
        return getRuntime().getAppConfigModule().isDisableNavigationBack(url);
    }

    /**
     * 获取胶囊按钮配置
     *
     * @return 导航栏胶囊按钮配置
     */
    public boolean isHideCapsuleButtons(String url) {
        return getRuntime().getAppConfigModule().isHideCapsuleButtons(url);
    }

    /**
     * 获取页面的标题
     *
     * @param url 页面路径
     * @return 页面标题
     */
    public String getPageTitle(String url) {
        return getRuntime().getAppConfigModule().getPageTitle(url);
    }


    /**
     * 判断页面是否启用了下拉刷新
     *
     * @param url 页面路径
     * @return true：启用了下拉刷新，否则亦然
     */
    public boolean isEnablePullDownRefresh(String url) {
        return getRuntime().getAppConfigModule().isEnablePullDownRefresh(url);
    }

    public void setAppId(String appId) {
        appIdOfScheme = appId;
    }

    private void checkMetaInfoIsNull(AppMetaInfoWrapper info) {
        if (info == null) {
            throw new AppLoadException(MSCLoadErrorConstants.ERROR_FETCH_METAINFO_FAILED, "metaInfo is null");
        }
    }

    public String getMainPackagePath() {
        AppMetaInfoWrapper info = this.metaInfo;
        checkMetaInfoIsNull(info);
        return info.getMainPackagePath();
    }

    /**
     * 初始渲染缓存配置
     *
     * @param url 页面地址
     * @return 初始渲染缓存状态
     */
    public AppConfigModule.InitialRenderingCacheState obtainInitialRenderingCacheState(String url) {
        return getRuntime().getAppConfigModule().obtainInitialRenderingCacheState(url);
    }

    public boolean obtainInitialRenderingSnapshotState(String url) {
        return getRuntime().getAppConfigModule().obtainInitialRenderingSnapshotState(url);
    }

    public boolean isCustomNavigationStyle(String url) {
        return getRuntime().getAppConfigModule().isCustomNavigationStyle(url);
    }


    /**
     * 获取配置的根路径
     *
     * @return 根路径
     */
    public String getRootPath() {
        return getRuntime().getAppConfigModule().getRootPath();
    }

    public boolean hasPage(String checkPath) {
        return getRuntime().getAppConfigModule().hasPage(checkPath);
    }

    /**
     * TabBar是否在顶部
     *
     * @return true：顶部
     */
    public boolean isTopTabBar() {
        return getRuntime().getAppConfigModule().isTopTabBar();
    }

    /**
     * 获取Tab项列表
     *
     * @return Tab项列表
     */
    public List<TabItemInfo> getTabItemList() {
        return getRuntime().getAppConfigModule().getTabItemList();
    }

    /**
     * 检查被给的url是否属于Tab页面
     *
     * @param url 页面路径
     * @return true：是Tab页，否则亦然
     */
    public boolean isTabPage(String url) {
        return getRuntime().getAppConfigModule().isTabPage(url);
    }

    public boolean checkIsTabPageIfRouted(String url) {
        if (MSCHornRollbackConfig.isRollbackSetRouteMappingPersist()) {
            return isTabPage(url);
        } else {
            AppConfigModule appConfigModule = getRuntime().getAppConfigModule();
            String pathIfRoutedPersist = appConfigModule.getRoutePathPersistIfRouted(url);
            String pathIfRouted = appConfigModule.getRoutePathIfRouted(url);
            Boolean isTabDerivedPersist = appConfigModule.isTabDerivedPersist(pathIfRoutedPersist);
            Boolean isTabDerived = appConfigModule.isTabDerived(pathIfRouted);
            String finalPath = url;
            if (isTabDerivedPersist != null) {
                if (!isTabDerivedPersist) {
                    finalPath = pathIfRoutedPersist;
                }
            } else if (isTabDerived != null) {
                if (!isTabDerived) {
                    finalPath = pathIfRouted;
                }
            }
            return isTabPage(finalPath);
        }
    }

    public boolean checkHasPageIfRouted(String url) {
        if (MSCHornRollbackConfig.isRollbackSetRouteMappingPersist()) {
            return hasPage(url);
        } else {
            AppConfigModule appConfigModule = getRuntime().getAppConfigModule();
            String pathIfRoutedPersist = appConfigModule.getRoutePathPersistIfRouted(url);
            String pathIfRouted = appConfigModule.getRoutePathIfRouted(url);
            return hasPage(url) || hasPage(pathIfRouted) || hasPage(pathIfRoutedPersist);
        }
    }

    public RendererType getRendererTypeForPage(String url) {
        return getRuntime().getAppConfigModule().getRendererTypeForPage(url);
    }

    /**
     * 在此页面时应接着预加载哪些页面
     */
    @Nullable
    public List<String> getPreloadPagesForThisPage(String url) {
        return getRuntime().getAppConfigModule().getPreloadPagesForThisPage(url);
    }

    public boolean isWebViewRecycleEnabled() {
        return getRuntime().getAppConfigModule().isWebViewRecycleEnabled();
    }

    /**
     * 获取小程序id
     *
     * @return 返回小程序id
     */
    public String getAppId() {
        AppMetaInfoWrapper info = this.metaInfo;
        return info != null ? info.getAppId() : this.appIdOfScheme;
    }

    public String getAppName() {
        AppMetaInfoWrapper info = this.metaInfo;
        return info != null ? info.getAppName() : "";
    }

    public void updateMetaInfo(AppMetaInfoWrapper metaInfoWrapper) {
        this.metaInfo = metaInfoWrapper;
        getRuntime().getFileModule().setCompatMMPAppId(
                MSCHornRollbackConfig.isRollbackMMPSharedStorage() ? null :
                        (String) getExtraConfigValue(FileModule.EXTRA_KEY_MMP_APP_ID));
        ReportUtils.addBizMetaInfoToMSIContainer(getRuntime(), metaInfoWrapper);
    }

    public String getIconUrl() {
        AppMetaInfoWrapper info = this.metaInfo;
        return info != null ? info.getIconPath() : null;
    }

    public boolean isShareSupported() {
        AppMetaInfoWrapper info = this.metaInfo;
        return info != null && info.shareSupported();
    }

    /**
     * 基础库版本
     *
     * @return basePackageVersion
     */
    public String getBasePkgVersion() {
        PackageInfoWrapper basePackageInfo = this.basePackageInfo;
        if (basePackageInfo == null) {
            return "";
        } else {
            return basePackageInfo.getVersion();
        }
    }

    public boolean hasMetaInfo() {
        return metaInfo != null;
    }

    @Nullable
    public AppMetaInfoWrapper getMetaInfo() {
        return metaInfo;
    }

    public boolean enableAsyncSubPkg() {
        if (MSCHornRollbackConfig.readConfig().rollbackInjectAdvanceBuildConfig) {
            MSCLog.i(TAG,"injectMetaInfoConfig rollback");
            return false;
        }

        AppMetaInfoWrapper metaInfo = getMetaInfo();
        if (metaInfo == null) {
            MSCLog.i(TAG, "isEnableAsyncSubPkg metaInfo is null");
            return false;
        }
        MSCAppMetaInfo.AdvanceBuildConfig advanceBuildConfig = metaInfo.getAdvanceBuildConfig();
        if (advanceBuildConfig == null) {
            MSCLog.i(TAG, "isEnableAsyncSubPkg advanceBuildConfig is null");
            return false;
        }
        return advanceBuildConfig.isAsyncSubPkg();
    }

    public String getPublishId() {
        AppMetaInfoWrapper info = this.metaInfo;
        if (info == null) {
            return "0";
        }
        if (TextUtils.isEmpty(info.getPublishId())) {
            return info.getVersion();
        }
        return info.getPublishId();
    }

    public String getPackageNameByPath(String path) {
        AppMetaInfoWrapper info = this.metaInfo;
        return info != null ? info.getLoadedPackageInfoDefaultReturnMain(path).getPackageName() : "";
    }

    public boolean isDebug() {
        AppMetaInfoWrapper info = this.metaInfo;
        return info != null && info.isDebug();
    }

    public String getBuildId() {
        AppMetaInfoWrapper info = this.metaInfo;
        return info != null ? info.getBuildId() : "";
    }

    public void cachePackageWrapper(@NonNull PackageInfoWrapper packageInfoWrapper) {
        AppMetaInfoWrapper info = this.metaInfo;
        checkMetaInfoIsNull(info);
        info.cachePackageWrapper(packageInfoWrapper);
    }

    public String getMSCAppVersion() {
        AppMetaInfoWrapper info = this.metaInfo;
        return info != null ? info.getVersion() : "";
    }

    /**
     * 获取指定页面路径所在的子包数据，如不存在，则返回null
     *
     * @param path 页面路径
     * @return packageInfo
     */
    @Nullable
    public PackageInfoWrapper getOrCreateSubPackageByPath(String path) {
        AppMetaInfoWrapper info = this.metaInfo;
        checkMetaInfoIsNull(info);
        return info.getOrCreateSubPackageWrapperByPath(path);
    }

    /**
     * 获取指定页面路径所在的包数据，先检查子包，如不存在，则认为是主包页面
     *
     * @param path 页面路径
     * @return packageInfo
     */
    public PackageInfoWrapper getLoadedPackageInfoByPath(String path) {
        AppMetaInfoWrapper info = this.metaInfo;
        checkMetaInfoIsNull(info);
        return info.getLoadedPackageInfoDefaultReturnMain(path);
    }

    public PackageInfoWrapper createMainPackageWrapper() {
        AppMetaInfoWrapper info = this.metaInfo;
        checkMetaInfoIsNull(info);
        return info.createMainPackageWrapper();
    }

    public Object getExtraConfigValue(String key) {
        AppMetaInfoWrapper info = this.metaInfo;
        checkMetaInfoIsNull(info);
        return info.getExtraConfigValue(key);
    }

    public boolean isSameVersion(AppMetaInfoWrapper metaInfo) {
        return MiniAppPropertyUtil.isSameVersion(this.metaInfo, metaInfo);
    }

    public List<DioFile> getDioFiles(String[] fileUris) {
        List<DioFile> files = new ArrayList<>();
        for (final String fileUri : fileUris) {
            if (TextUtils.isEmpty(fileUri)) {
                continue;
            }
            JSResourceData jsResourceData = getResourcePathWithPackageInfo(fileUri);
            if (jsResourceData == null) {
                MSCLog.e(TAG, "jsResourceData is null");
                continue;
            }

            final DioFile file = jsResourceData.jsResourceFile;
            if (file == null || !file.exists()) {
                getRuntime().getNativeExceptionHandler().handleException(
                        new MSCRuntimeException("importScripts not exist! " + fileUri + ","
                                + getResourceCheckResult(fileUri, jsResourceData)));
                if (file != null) {
                    MSCLog.e(TAG, "DioFile: " + file.getPath());
                }

                checkResourceAndReport(fileUri, jsResourceData);
                continue;
            }
            MSCLog.d("AppService", "importScripts: ", fileUri, " -> ", file);
            files.add(file);
        }
        return files;
    }

    public String getResourceCheckResult(String fileUri, JSResourceData jsResourceData) {
        if (jsResourceData.infoWrapper == null) {
            PackageLoadReporter.create(getRuntime()).reportJSResourceNoExist(fileUri);
            return "infoWrapper is null";
        }

        // check dio file exist
        DioFile dioFile = new DioFile(jsResourceData.infoWrapper.getLocalPath());
        boolean isDioFileExist = dioFile.exists();

        // check dio file md5
        boolean isMd5Same = false;

        boolean isJsResourceExist = false;
        // check js file
        try {
            isMd5Same = jsResourceData.infoWrapper.isLocalCacheValid();
            DioEntry dioEntry = dioFile.getDioReader() != null ? dioFile.getDioReader().findDioEntryByPath(jsResourceData.fileUrl) : null;
            isJsResourceExist = dioEntry != null;
        } catch (IOException e) {
            MSCLog.e(TAG, e, "findDioEntryByPath or isLocalCacheValid Error" );
        }
        return "isDioFileExist:" + isDioFileExist + ",isMd5Same:" + isMd5Same + ",isJsResourceExist:" + isJsResourceExist;
    }

    public void checkResourceAndReport(String fileUri, JSResourceData jsResourceData) {
        if (jsResourceData.infoWrapper == null) {
            MSCLog.e(TAG, "getDioFiles infoWrapper is null");
            PackageLoadReporter.create(getRuntime()).reportJSResourceNoExist(fileUri);
            return;
        }

        // check dio file exist
        DioFile dioFile = new DioFile(jsResourceData.infoWrapper.getLocalPath());
        boolean isDioFileExist = dioFile.exists();

        // check dio file md5
        boolean isMd5Same = false;

        boolean isJsResourceExist = false;
        // check js file
        try {
            isMd5Same = jsResourceData.infoWrapper.isLocalCacheValid();
            DioEntry dioEntry = dioFile.getDioReader().findDioEntryByPath(jsResourceData.fileUrl);
            isJsResourceExist = dioEntry != null;
        } catch (IOException e) {
            MSCLog.e(TAG, e, "findDioEntryByPath");
        }

        // report result
        PackageLoadReporter
                .create(getRuntime())
                .reportJSResourceNoExist(fileUri, dioFile.getAbsolutePath(),
                        isDioFileExist, isMd5Same, isJsResourceExist);
    }

    /**
     * fix http://git.sankuai.com/projects/MET/repos/mmp-android/pull-requests/817/overview
     *
     * @param url url
     * @return 获取的路径需要使用FileScope检测文件权限
     */
    @Nullable
    public DioFile getResourcePath(String url) {
        JSResourceData jsResourceData = getResourcePathWithPackageInfo(url);
        if (jsResourceData == null) {
            return null;
        }
        return jsResourceData.jsResourceFile;
    }

    // TODO: 2022/6/20 tianbin 资源完整性校验逻辑复用，减少维护成本
    @Nullable
    public JSResourceData getResourcePathWithPackageInfo(String url) {
        if (url.startsWith("file:///data/")) {
            //某些情况下如果webview直接访问小程序的绝对路径，直接返回，不做分包检测
            // fixme 确认为什么会有这样的访问
            if (FileScope.accessFile(url, getRuntime().getFileModule().getMiniAppDir())) {
                return new JSResourceData(new DioFile(url));
            }
            return null;
        }
        //需要使用FileScope检测文件访问喧嚣
        if (url.startsWith(PREFIX_FRAMEWORK)) {
            PackageInfoWrapper basePackageInfo = this.basePackageInfo;
            // 框架包
            if (basePackageInfo == null) {
                return null;
            }
            return new JSResourceData(basePackageInfo,
                    new DioFile(basePackageInfo.getLocalPath(), url.replace(PREFIX_FRAMEWORK, "")),
                    url.replace(PREFIX_FRAMEWORK, ""));
        } else if (url.startsWith(PREFIX_MAIN_APP)) {
            AppMetaInfoWrapper info = this.metaInfo;
            checkMetaInfoIsNull(info);
            // 主包
            return new JSResourceData(info.getMainPackageCached(),
                    new DioFile(info.getMainPackageCached().getLocalPath(), url.replace(PREFIX_MAIN_APP, "")),
                    url.replace(PREFIX_MAIN_APP, ""));
        } else if (url.startsWith(PREFIX_APP)) {
            AppMetaInfoWrapper metaInfoWrapper = this.metaInfo;
            checkMetaInfoIsNull(metaInfoWrapper);
            if (!CollectionUtil.isEmpty(metaInfoWrapper.getSubPackagesCached())) {
                // 子包
                int index = url.indexOf(File.separatorChar, PREFIX_APP_LENGTH);
                if (index > PREFIX_APP_LENGTH) {
                    String subPackName = url.substring(PREFIX_APP_LENGTH, index);
                    PackageInfoWrapper info = getSubPackageByName(subPackName);
                    if (info != null) {
                        // 当前端传入路径为 /__app/ 时会下标越界，添加异常兜底
                        if (index + 1 >= url.length()) {
                            return null;
                        }
                        return new JSResourceData(info,
                                new DioFile(info.getLocalPath(), url.substring(index + 1)),
                                url.substring(index + 1));
                    }
                }
            }
        }

        // 注入template的html信息时，shouldInterceptRequest拦截到的request的url就是data:text/html;charset=utf-8;base64。
        // 此为webview默认行为。
        if (!hasMetaInfo()) {
            MSCLog.i("MSCAppModule", "has not meta info. back");
            return null;
        }

        // 根据root匹配，默认认为在主包
        PackageInfoWrapper info = getLoadedPackageInfoByPath(url);
        MSCLog.i("MSCAppModule", "has metainfo,", url, info, metaInfo);
        if (null == info) {
            // 存在一种情况，基础库注入没有完成时，业务就开始使用，此时metaInfo有值，但packageInfo还没拿到。
            return null;
        }
        return new JSResourceData(info, new DioFile(info.getLocalPath(), url), url);
    }

    @Nullable
    public PackageInfoWrapper getPackageInfoByUrl(String url, boolean onlyCache) {
        //需要使用FileScope检测文件访问喧嚣
        if (url.startsWith(PREFIX_FRAMEWORK)) { //框架包
            return basePackageInfo;
        } else if (url.startsWith(PREFIX_MAIN_APP)) { // 主包
            AppMetaInfoWrapper info = this.metaInfo;
            return info == null ? null : info.getMainPackageCached();
        } else if (url.startsWith(PREFIX_APP)) {
            AppMetaInfoWrapper metaInfoWrapper = this.metaInfo;
            checkMetaInfoIsNull(metaInfoWrapper);
            if (!onlyCache || !CollectionUtil.isEmpty(metaInfoWrapper.getSubPackagesCached())) {
                //子包
                int index = url.indexOf(File.separatorChar, PREFIX_APP_LENGTH);
                if (index > PREFIX_APP_LENGTH) {
                    String subPackName = url.substring(PREFIX_APP_LENGTH, index);
                    PackageInfoWrapper info = getSubPackageByName(subPackName);
                    if (info != null) {
                        return info;
                    }
                }
            }
        }

        // 如果没有，需要考虑直接按照路径从子包中取。
        return getLoadedPackageInfoByPath(url);
    }

    /**
     * 获取指定子包名对应的子包数据，如不存在，则返回null
     *
     * @param subPackName 子包名
     * @return packageInfo
     */
    @Nullable
    private PackageInfoWrapper getSubPackageByName(String subPackName) {
        AppMetaInfoWrapper info = this.metaInfo;
        checkMetaInfoIsNull(info);
        return info.getSubPackageByName(subPackName);
    }

    public String getMTWebViewAppTag() {
        return "msc_" + getAppId();
    }


    public void setShareEnvironment(String environment) {
        this.mShareEnvironment = environment;
    }

    public String getShareEnvironment() {
        return mShareEnvironment;
    }

    public String getBasePackageMd5() {
        PackageInfoWrapper packageInfoWrapper = this.basePackageInfo;
        if (packageInfoWrapper == null) {
            return "";
        }
        return packageInfoWrapper.getMd5();
    }

    public PackageInfoWrapper getBasePackage() {
        return basePackageInfo;
    }

    public String getBasePkgName() {
        PackageInfoWrapper packageInfoWrapper = this.basePackageInfo;
        if (packageInfoWrapper == null) {
            return "";
        }
        return packageInfoWrapper.getDDResourceName();
    }

    public String getBasePackageLoadType() {
        if (getBasePackage() == null) {
            return "";
        }
        return getBasePackage().getLoadType();
    }

    public String getBasePkgMinVersion() {
        AppMetaInfoWrapper info = this.metaInfo;
        if (info == null) {
            return "";
        }
        return info.getBasePackageMinVersion();
    }

    public PackageInfoWrapper getMainPackageWrapper() {
        AppMetaInfoWrapper metaInfo = this.metaInfo;
        checkMetaInfoIsNull(metaInfo);
        return metaInfo.getMainPackageCached();
    }

    public PackageInfoWrapper createSubPackageWrapper(String path) {
        AppMetaInfoWrapper metaInfo = this.metaInfo;
        checkMetaInfoIsNull(metaInfo);
        return metaInfo.createSubPackageWrapper(path);
    }

    public PackageInfoWrapper createConfigPackageWrapper() {
        AppMetaInfoWrapper metaInfo = this.metaInfo;
        checkMetaInfoIsNull(metaInfo);
        return metaInfo.createConfigPackageWrapper();
    }

    @NonNull
    public PackageInfoWrapper createBasePackageWrapper() {
        if (basePackageInfo == null) {
            basePackageInfo = new PackageInfoWrapper();
        }
        return basePackageInfo;
    }

    public void setBasePkgVersionOfDebugPreload(String basePkgVersionOfDebug) {
        this.basePkgVersionOfDebug = basePkgVersionOfDebug;
    }

    public String getBasePkgVersionOfDebugPreload() {
        return basePkgVersionOfDebug;
    }

    public boolean isPackageLoaded(String path) {
        AppMetaInfoWrapper metaInfo = this.metaInfo;
        checkMetaInfoIsNull(metaInfo);
        return metaInfo.isPackageLoaded(path);
    }

    public PackageInfoWrapper getLoadedPackageInfoWithoutDefault(String path) {
        AppMetaInfoWrapper metaInfo = this.metaInfo;
        checkMetaInfoIsNull(metaInfo);
        return metaInfo.getLoadedPackageInfoWithoutDefault(path);
    }

    public boolean isSubPackagePage(String path) {
        AppMetaInfoWrapper metaInfo = this.metaInfo;
        checkMetaInfoIsNull(metaInfo);
        return metaInfo.isSubPackagePage(path);
    }

    public PackageInfoWrapper getSubPackageCachedByPath(String path) {
        AppMetaInfoWrapper metaInfo = this.metaInfo;
        checkMetaInfoIsNull(metaInfo);
        return metaInfo.getSubPackageCachedByPath(path);
    }

    public void setOfflineBizFailReason(String offlineBizFailReason) {
        this.offlineBizFailReason = offlineBizFailReason;
    }

    public void setOfflineBaseFailReason(String offlineBaseFailReason) {
        this.offlineBaseFailReason = offlineBaseFailReason;
    }

    public String getOfflineBizFailReason() {
        return offlineBizFailReason;
    }

    public String getOfflineBaseFailReason() {
        return offlineBaseFailReason;
    }

    public ConcurrentHashMap<String, Long> getRecentError() { return recentError; }

    public void setUrlExternalApp(boolean isUrlExternalApp) {
        this.isUrlExternalApp = isUrlExternalApp;
    }

    public boolean getExternalApp() {
        AppMetaInfoWrapper info = this.metaInfo;
        if (info == null) {
            return isUrlExternalApp;
        } else {
            return info.getExternalApp();
        }
    }
}
