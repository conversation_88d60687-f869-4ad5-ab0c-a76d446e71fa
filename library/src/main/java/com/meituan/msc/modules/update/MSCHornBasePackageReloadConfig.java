package com.meituan.msc.modules.update;

import android.os.Build;
import android.support.annotation.Keep;
import android.text.TextUtils;

import com.google.gson.annotations.SerializedName;
import com.meituan.android.singleton.CityControllerSingleton;
import com.meituan.msc.extern.MSCEnvHelper;
import com.meituan.msc.lib.interfaces.BaseRemoteConfig;
import com.meituan.msc.modules.api.web.WebViewUtil;
import com.meituan.msc.modules.reporter.MSCLog;

import java.util.ArrayList;
import java.util.HashMap;
import java.util.List;
import java.util.Map;

/**
 * 基础库强制更新配置，用于已发布的基础库存在重大问题时下发
 * https://km.sankuai.com/page/1286025836
 */
public class MSCHornBasePackageReloadConfig extends BaseRemoteConfig<MSCHornBasePackageReloadConfig.Config> {

    private static final String TAG = "MSCHornBasePackageReloadConfig";
    public volatile boolean isFetchedRemoteConfig;

    @Keep
    public static class Config {
        // 用于gson及其他正常创建的情况
        public Config() {
        }

        @SerializedName("sdkReloadVersions")
        public String[] sdkReloadVersions;
    }

    public String[] getSDKReloadVersions() {
        return config.sdkReloadVersions;
    }

    // 由于sdkReloadVersions在配置变更时实时变化，为避免不一致问题，可先缓存为临时变量，再进行判断
    public boolean isInReloadVersions(String basePackageVersion, String[] sdkReloadVersions) {
        if (sdkReloadVersions == null || sdkReloadVersions.length == 0) {
            return false;
        }

        for (String reloadVersion : sdkReloadVersions) {
            if (TextUtils.equals(reloadVersion, basePackageVersion)) {
                return true;
            }
        }
        return false;
    }

    public boolean isInReloadVersions(String basePackageVersion) {
        return isInReloadVersions(basePackageVersion, config.sdkReloadVersions);
    }

    private static MSCHornBasePackageReloadConfig sInstance;

    public static MSCHornBasePackageReloadConfig get() {
        if (sInstance == null) {
            synchronized (MSCHornBasePackageReloadConfig.class) {
                if (sInstance == null) {
                    sInstance = new MSCHornBasePackageReloadConfig();
                }
            }
        }
        return sInstance;
    }

    private MSCHornBasePackageReloadConfig() {
        super("msc_base_package_reload", Config.class);
    }

    @Override
    protected Map<String, Object> getHornQuery() {
        Map<String, Object> extra = new HashMap<>();
        extra.put("cityId", CityControllerSingleton.getInstance().getCityId());
        extra.put("chromeVersion", WebViewUtil.getChromeWebViewVersion(MSCEnvHelper.getContext()));
        extra.put("deviceLevel", com.meituan.metrics.util.DeviceUtil.getDeviceLevel(MSCEnvHelper.getContext()).getValue());
        String manufacturer = Build.MANUFACTURER;
        if (!TextUtils.isEmpty(manufacturer)) {
            extra.put("manufacturer", manufacturer);
        }
        return extra;
    }

    @Override
    protected void onRemoteConfigChanged(String rawConfigString) {
        super.onRemoteConfigChanged(rawConfigString);
        // 轮训 推送 实时生效
        config = parseRemoteConfig(rawConfigString);
        if (config == null) {
            MSCLog.i(TAG, "sdkReloadVersions is null");
            return;
        }
        MSCLog.i(TAG, "onRemoteConfigChanged", rawConfigString);
        triggerReloadBasePackage(config.sdkReloadVersions);
        reportRemoteConfigFirstChangedDuration(config.sdkReloadVersions);
        isFetchedRemoteConfig = true;
    }

    private void reportRemoteConfigFirstChangedDuration(String[] sdkReloadVersions) {
        if (!isFetchedRemoteConfig) {
            long fetchDuration = System.currentTimeMillis() - registerHornTimeMill;
            PackageLoadReporter.CommonReporter.create().reportBasePackageReloadConfigFetchDuration(sdkReloadVersions, fetchDuration);
        }
    }

    public void triggerReloadBasePackage(String[] sdkReloadVersions) {
        if (sdkReloadVersions == null || sdkReloadVersions.length == 0) {
            MSCLog.i(TAG, "sdkReloadVersions is empty");
            return;
        }

        for (BasePackageReloadListener listener : listeners) {
            listener.onReload(sdkReloadVersions);
        }
    }

    private final List<BasePackageReloadListener> listeners = new ArrayList<>();

    public void addBasePackageReloadListener(BasePackageReloadListener listener) {
        listeners.add(listener);
    }

    public void removeBasePackageReloadListener(BasePackageReloadListener listener) {
        listeners.remove(listener);
    }

    public interface BasePackageReloadListener {
        void onReload(String[] sdkReloadVersions);
    }

}
