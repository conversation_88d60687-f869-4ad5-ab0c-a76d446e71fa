package com.meituan.msc.modules.update;

import com.meituan.android.mercury.msc.adaptor.core.MSCLoadExeption;
import com.meituan.met.mercury.load.core.DDLoaderException;

public final class MSCLoadExceptionHelper {

    public static int getErrorCode(Exception error) {
        if (error instanceof MSCLoadExeption) {
            return ((MSCLoadExeption) error).getErrCode();
        }
        return DDLoaderException.ERROR_CODE_UNKNOWN;
    }

    public static String getErrorMsg(Exception error) {
        if (error == null) {
            return "";
        }
        return error.getMessage();
    }
}
