package com.meituan.msc.modules.update;

import android.content.Context;
import android.support.annotation.Nullable;
import android.support.annotation.VisibleForTesting;
import android.text.TextUtils;

import com.google.gson.Gson;
import com.google.gson.JsonParseException;
import com.google.gson.annotations.SerializedName;
import com.google.gson.reflect.TypeToken;
import com.meituan.msc.common.config.MSCConfig;
import com.meituan.msc.common.utils.IOUtil;
import com.meituan.msc.common.utils.MPConcurrentHashMap;
import com.meituan.msc.extern.MSCEnvHelper;
import com.meituan.msc.modules.devtools.DebugHelper;
import com.meituan.msc.modules.reporter.MSCLog;

import java.util.ArrayList;

public class MetaInfoMinVersionManager {

    private static final String TAG = "MetaInfoMinVersionManager";
    private static final String MIN_VERSION_ASSET_FILE_NAME = "msc/msc_min_versions.json";
    private static volatile MetaInfoMinVersionManager mInstance;
    private volatile boolean hasInitFlag = false;
    private final MPConcurrentHashMap<String, String> minBuildIdMap = new MPConcurrentHashMap<>();

    public static MetaInfoMinVersionManager getInstance() {
        if (mInstance == null) {
            synchronized (MetaInfoMinVersionManager.class) {
                if (mInstance == null) {
                    mInstance = new MetaInfoMinVersionManager();
                }
            }
        }
        return mInstance;
    }

    @Nullable
    private String readMinVersionInfo(Context context) {
        String minVersionInfo = IOUtil.readAssetFile(context, MIN_VERSION_ASSET_FILE_NAME);
        MSCLog.i(TAG, "minVersion json = ", minVersionInfo);
        return minVersionInfo;
    }

    public void init() {
        Context context = MSCEnvHelper.getEnvInfo().getApplicationContext();
        if (context == null) {
            MSCLog.e("context is null ");
            return;
        }
        if (!hasInitFlag) {
            synchronized (this) {
                if (!hasInitFlag) {
                    String minVersionJson = readMinVersionInfo(context);
                    Gson gson = new Gson();
                    ArrayList<MinVersion> list = null;
                    if (!TextUtils.isEmpty(minVersionJson)) {
                        try {
                            list = gson.fromJson(minVersionJson, new TypeToken<ArrayList<MinVersion>>() {
                            }.getType());
                        } catch (JsonParseException e) {
                            MSCLog.e(TAG, e, "init");
                        }
                    }
                    if (list != null) {
                        for (MinVersion minVersion : list) {
                            if (minVersion == null || TextUtils.isEmpty(minVersion.buildId)) {
                                continue;
                            }
                            MSCLog.i(TAG, "appendMinVersion", minVersion.appId, minVersion.buildId);
                            minBuildIdMap.put(minVersion.appId, minVersion.buildId);
                        }
                    }
                }
                hasInitFlag = true;
            }
        }
    }

    public boolean isMatchMinBuildId(String appId, String currentBuildId, boolean isInner) {
        String minBuildId = minBuildIdMap.get(appId);
        // 参数无效，默认当前元信息可正常使用
        if (TextUtils.isEmpty(currentBuildId) || TextUtils.isEmpty(minBuildId)) {
            return true;
        }
        try {
            if (Integer.parseInt(currentBuildId) >= Integer.parseInt(minBuildId)) {
                return true;
            } else {
                if (isInner && MSCEnvHelper.getEnvInfo().enableInnerMetaInfo()) {
                    DebugHelper.throwNotProd(new Throwable("inner version is smaller than min version"));
                }
                return false;
            }
        } catch (NumberFormatException e) {
            MSCLog.e(TAG, e, "isMatchMinBuildId");
        }
        // 参数格式异常，默认当前元信息可正常使用
        return true;
    }

    public boolean hasInnerMinBuildId(String appId) {
        return minBuildIdMap.containsKey(appId);
    }

    private static class MinVersion {
        @SerializedName("mscId")
        public String appId;
        @SerializedName("mscBuildId")
        public String buildId;

        @Override
        public String toString() {
            return "MinVersion{" + "appId='" + appId + '\'' + ", minVersion='" + buildId + '\'' + '}';
        }
    }

    @VisibleForTesting
    public void clearData() {
        minBuildIdMap.clear();
    }

    @VisibleForTesting
    public boolean hasInitFlag() {
        return hasInitFlag;
    }
}
