package com.meituan.msc.modules.update;

import android.text.TextUtils;

import com.meituan.msc.modules.update.bean.AppMetaInfoWrapper;

public class MiniAppPropertyUtil {
    public static final int KEY_APP_SHARE_ENABLE = 1;

    /**
     * 判断小程序元信息是否为同一版本
     * PS：与MMP的差异为不在判断基础库MD5，原因为基础库版本不再仅由小程序业务指定
     *
     * @param infoWrapper1 元信息1
     * @param infoWrapper2 元信息2
     * @return 是否相同
     */
    public static boolean isSameVersion(AppMetaInfoWrapper infoWrapper1, AppMetaInfoWrapper infoWrapper2) {
        if (infoWrapper1 == null || infoWrapper2 == null) {
            return false;
        }
        if (!TextUtils.equals(infoWrapper1.getBuildId(), infoWrapper2.getBuildId())) {
            return false;
        }
        if (!TextUtils.equals(infoWrapper1.getPublishId(), infoWrapper2.getPublishId())) {
            return false;
        }
        if (!TextUtils.equals(infoWrapper1.getMainPkgMd5(), infoWrapper2.getMainPkgMd5())) {
            return false;
        }

        return true;
    }
}
