package com.meituan.msc.modules.update;

import com.meituan.msc.modules.apploader.events.AppLoadException;
import com.meituan.msc.modules.update.bean.PackageInfoWrapper;

public interface PackageInjectCallback {

    /**
     * 单包逻辑层注入成功
     *
     * @param packageInfo 包信息
     * @param realLoaded  是否加载完毕
     */
    void onPackageInjectSuccess(PackageInfoWrapper packageInfo, boolean realLoaded);

    /**
     * 单包逻辑层注入失败
     *
     * @param packageInfo 包信息
     * @param e           异常
     */
    void onPackageInjectFailed(PackageInfoWrapper packageInfo, String errorMsg, AppLoadException e);

    /**
     * 所有包 注入完毕 回调
     */
    void onAllPackageInjected();
}
