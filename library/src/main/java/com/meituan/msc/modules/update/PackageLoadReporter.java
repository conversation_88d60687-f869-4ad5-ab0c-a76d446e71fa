package com.meituan.msc.modules.update;

import android.support.annotation.NonNull;
import android.support.annotation.Nullable;
import android.support.annotation.StringDef;
import android.text.TextUtils;

import com.meituan.android.mercury.msc.adaptor.core.MSCLoadExeption;
import com.meituan.met.mercury.load.core.DDResource;
import com.meituan.msc.common.config.MSCConfig;
import com.meituan.msc.common.report.MetricsEntry;
import com.meituan.msc.extern.MSCEnvHelper;
import com.meituan.msc.lib.BuildConfig;
import com.meituan.msc.modules.apploader.events.AppLoadException;
import com.meituan.msc.modules.engine.MSCHornRollbackConfig;
import com.meituan.msc.modules.engine.MSCRuntime;
import com.meituan.msc.modules.preload.MSCHornPreloadConfig;
import com.meituan.msc.modules.reporter.CommonTags;
import com.meituan.msc.modules.reporter.MSCCommonTagReporter;
import com.meituan.msc.modules.reporter.MSCLog;
import com.meituan.msc.modules.reporter.MSCReporter;
import com.meituan.msc.modules.reporter.ReporterFields;
import com.meituan.msc.modules.update.bean.MSCInjectType;
import com.meituan.msc.modules.update.bean.PackageInfoWrapper;
import com.meituan.msc.modules.update.pkg.PackageLoadManager;

import org.json.JSONArray;
import org.json.JSONException;

import java.io.IOException;
import java.lang.annotation.Retention;
import java.lang.annotation.RetentionPolicy;
import java.util.ArrayList;
import java.util.Arrays;
import java.util.HashMap;
import java.util.List;
import java.util.Map;

/**
 * 包模块指标数据上报
 * https://km.sankuai.com/page/1287876075
 * https://km.sankuai.com/page/1440213974
 */
public class PackageLoadReporter extends MSCCommonTagReporter {

    private PackageLoadReporter(CommonTags commonTags) {
        super(commonTags);
    }

    public static PackageLoadReporter create(MSCRuntime runtime) {
        return new PackageLoadReporter(CommonTags.build(runtime));
    }

    public void onFetchMetaInfoSuccess(@LoadType String loadType, @Source String sourceFrom, String useNetworkRes) {
        onFetchMetaInfo(MSCReporter.ReportValue.SUCCESS, loadType, sourceFrom, useNetworkRes, null);
    }

    public void onFetchMetaInfoFail(@LoadType String loadType, @Source String sourceFrom, Exception error) {
        onFetchMetaInfo(ReportValue.FAILED, loadType, sourceFrom, null, error);
    }

    public void onFetchMetaInfo(@ReportValue int value, @LoadType String loadType, @Source String sourceFrom, String useNetworkRes, Exception error) {
        record(ReporterFields.REPORT_MSC_METAINFO_LOAD_SUCCESS_RATE)
                .value(value)
                .tag(CommonTags.TAG_LOAD_TYPE, loadType)
                .tag(CommonTags.TAG_SOURCE_FROM, sourceFrom)
                .tag(CommonTags.TAG_ERROR_CODE, getErrorCode(error))
                .tag(CommonTags.TAG_ERROR_MSG, getErrorMsg(error))
                .tag("enableInnerMeta", MSCEnvHelper.getEnvInfo().enableInnerMetaInfo())
                .tag("useNetworkRes", !TextUtils.isEmpty(useNetworkRes)? useNetworkRes : "unKnown")
                .sendDelay();
    }

    /**
     * TODO 缓存版本小于最小限制版本重新拉取（retryCheckUpdateByNetwork），应当上报两次耗时
     */
    public void reportLoadMetaInfoDuration(@ReportValue int status, long value, @LoadType String loadType, @Source String sourceFrom, String useNetworkRes) {
        record(ReporterFields.REPORT_MSC_METAINFO_LOAD_DURATION)
                .value(value)
                .tag(CommonTags.TAG_LOAD_TYPE, loadType)
                .tag(CommonTags.TAG_SOURCE_FROM, sourceFrom)
                .tag("status", status)
                .tag("$sr", 0.05)
                .tag("enableInnerMeta", MSCEnvHelper.getEnvInfo().enableInnerMetaInfo())
                .tag("useNetworkRes", !TextUtils.isEmpty(useNetworkRes)? useNetworkRes : "unKnown")
                .sendDelay();
    }

    public static int getErrorCode(@Nullable Exception error) {
        if (error instanceof MSCLoadExeption) {
            return ((MSCLoadExeption) error).getErrCode();
        }
        if (error instanceof AppLoadException) {
            return ((AppLoadException) error).getErrorCode();
        }
        return -1;
    }

    public static String getErrorMsg(@Nullable Exception error) {
        return error == null ? "empty error" : error.getMessage();
    }

    public void onLoadPackageSuccess(PackageReportBean reportBean) {
        reportLoadPackage(reportBean, ReportValue.SUCCESS, null);
    }

    public void onLoadPackageFailed(PackageReportBean reportBean, @Nullable Exception error) {
        reportLoadPackage(reportBean, ReportValue.FAILED, error);
    }

    private void reportLoadPackage(PackageReportBean reportBean, @ReportValue int value, @Nullable Exception error) {
        Map<String, Object> tmpTags = new HashMap<>();
        tmpTags.put(CommonTags.TAG_LOAD_TYPE, reportBean.getLoadType());
        tmpTags.put(CommonTags.TAG_PKG_TYPE, reportBean.getPkgType());
        tmpTags.put(CommonTags.TAG_PKG_NAME, reportBean.getPkgName());
        tmpTags.put(CommonTags.TAG_SOURCE_FROM, reportBean.getSourceFrom());
        tmpTags.put(CommonTags.TAG_ERROR_CODE, getErrorCode(error));
        tmpTags.put(CommonTags.TAG_ERROR_MSG, getErrorMsg(error));
        record(ReporterFields.REPORT_MSC_PACKAGE_LOAD_SUCCESS_RATE)
                .value(value)
                .tags(tmpTags)
                .tag("$sr", 0.05)
                .sendDelay();
        if (value == ReportValue.FAILED) {
            record(ReporterFields.REPORT_MSC_PACKAGE_LOAD_FAIL_COUNT)
                    .tags(tmpTags)
                    .sendDelay();
        }
    }

    public void reportLoadPackageSuccessDuration(PackageReportBean reportBean, long value) {
        reportLoadPackageDuration(reportBean, value, ReportValue.SUCCESS);
    }

    private void reportLoadPackageDuration(PackageReportBean reportBean, long value, @ReportValue int status) {
        MetricsEntry entry = record(ReporterFields.REPORT_MSC_PACKAGE_LOAD_DURATION)
                .tag(CommonTags.TAG_MSC_APP_VERSION, reportBean.getMscAppVersion())
                .tag(CommonTags.TAG_LOAD_TYPE, reportBean.getLoadType())
                .tag(CommonTags.TAG_PKG_NAME, reportBean.getPkgName())
                .tag(CommonTags.TAG_PKG_TYPE, reportBean.getPkgType())
                .tag(CommonTags.TAG_SOURCE_FROM, reportBean.getSourceFrom())
                .tag("status", status)
                .tag("$sr", 0.05)
                .value(value);

        if (MSCHornRollbackConfig.enableAddLoadPackageDetails()) {
           entry.tag(CommonTags.TAG_LOAD_PKG_Details, reportBean.getDdLoadPhaseDataJsonObject());
        }

        entry.sendDelay();
    }

    public void onInjectPackage(@ReportValue int value, String injectType, String pkgName) {
        onInjectPackage(value, injectType, pkgName,-1, "");
    }

    public void onInjectPackage(@ReportValue int value, String injectType, String pkgName, int errorCode, String errorMsg) {
        String[] pkgNames = null;
        if (!TextUtils.isEmpty(pkgName)) {
            pkgNames = new String[]{pkgName};
        }
        onInjectPackage(value, injectType, new String[]{}, pkgNames, errorCode, errorMsg);
    }

    public void onInjectPackage(@ReportValue int value, String injectType, String[] fileUris, String[] pkgNames) {
        onInjectPackage(value, injectType, fileUris, pkgNames, -1,"");
    }

    public void onInjectPackage(@ReportValue int value, String injectType, String[] fileUris, String[] pkgNames,
                                int errorCode, String errorMsg) {
        onInjectPackage(value, injectType, fileUris, pkgNames, errorCode, errorMsg, false);
    }

    public void onInjectPackage(@ReportValue int value, String injectType, String[] fileUris, String[] pkgNames,
                                int errorCode, String errorMsg, boolean isAsync) {
        boolean combo = false;
        if (null != fileUris && fileUris.length > 1) {
            combo = true;
        }
        if (!MSCHornRollbackConfig.enableUseNewInjectPackageRate()) {
            onInjectPackage(value, injectType, fileUris, "", errorCode, errorMsg, isAsync, combo);
        } else {
            if (TextUtils.equals(injectType, MSCInjectType.MSC_INJECT_FILES)) {
                Map<String, List<String>> fileUrisForPkgNameMap = getFileUrisForPkgNameMap(fileUris, pkgNames);
                if (fileUrisForPkgNameMap != null) {
                    for (Map.Entry<String, List<String>> entry : fileUrisForPkgNameMap.entrySet()) {
                        String pkgName = entry.getKey();
                        if (TextUtils.equals(pkgName, PackageLoadManager.BASE_PACKAGE_NAME)) {
                            injectType = MSCInjectType.MSC_INJECT_BASE_FILES;
                        } else if (TextUtils.equals(pkgName, MSCAppModule.MAIN_PACKAGE_NAME)) {
                            injectType = MSCInjectType.MSC_INJECT_MAIN_FILES;
                        } else {
                            injectType = MSCInjectType.MSC_INJECT_SUB_FILES;
                        }
                        onInjectPackage(value, injectType, entry.getValue().toArray(new String[0]), pkgName, errorCode, errorMsg, isAsync, combo);
                    }
                }
            } else {
                String pkgName = null;
                if (pkgNames != null && pkgNames.length > 0) {
                    pkgName = pkgNames[0];
                }
                onInjectPackage(value, injectType, fileUris, pkgName, errorCode, errorMsg, isAsync, combo);
            }
        }
    }

    private Map<String, List<String>> getFileUrisForPkgNameMap(String[] fileUris, String[] pkgNames) {
        if (fileUris == null || pkgNames == null || fileUris.length != pkgNames.length) {
            return null;
        }

        Map<String, List<String>> result = new HashMap<>();
        for (int i = 0; i < fileUris.length; i++) {
            String pkgName = pkgNames[i];
            List<String> fileUriList = result.get(pkgName);
            if (fileUriList == null) {
                fileUriList = new ArrayList<>();
                result.put(pkgName, fileUriList);
            }
            fileUriList.add(fileUris[i]);
        }
        return result;
    }

    private void onInjectPackage(@ReportValue int value, String injectType, String[] fileUris, String pkgName,
                                int errorCode, String errorMsg, boolean isAsync, boolean combo) {
        Map<String, Object> tmpTags = new HashMap<>();
        tmpTags.put("injectType", injectType);
        tmpTags.put("fileUris", Arrays.asList(fileUris));
        tmpTags.put("isAsync", isAsync);
        tmpTags.put("combo", combo);
        if (!TextUtils.isEmpty(pkgName)) {
            tmpTags.put(CommonTags.TAG_PKG_NAME, pkgName);
        }

        tmpTags.put(CommonTags.TAG_ERROR_CODE, errorCode);
        tmpTags.put(CommonTags.TAG_ERROR_MSG, errorMsg);
        record(ReporterFields.REPORT_MSC_PACKAGE_INJECT_SUCCESS_RATE)
                .value(value)
                .tags(tmpTags)
                .tag("$sr", 0.01)
                .sendRealTime();
        if (value == MSCReporter.ReportValue.FAILED) {
            record(ReporterFields.REPORT_MSC_PACKAGE_INJECT_FAIL_COUNT)
                    .tags(tmpTags)
                    .sendRealTime();
        }
    }

    @StringDef({LoadType.NETWORK, LoadType.LOCAL, LoadType.INNER, LoadType.S3_DEGRADE})
    @Retention(RetentionPolicy.SOURCE)
    public @interface LoadType {
        String NETWORK = "network";
        String LOCAL = "local";
        String INNER = "inner";
        String S3_DEGRADE = "s3Degrade";
    }

    @StringDef({Source.PREFETCH, Source.LAUNCH, Source.BATCH_PREFETCH, Source.S3_DEGRADE_LAUNCH})
    @Retention(RetentionPolicy.SOURCE)
    public @interface Source {
        String PREFETCH = "prefetch";
        String LAUNCH = "launch";
        String S3_DEGRADE_LAUNCH = "s3DegradeLaunch";
        String BATCH_PREFETCH = "batchPrefetch";
    }

    public void reportJSResourceNoExist(String fileUri) {
        record(ReporterFields.REPORT_MSC_JS_RESOURCE_NOT_EXIST_COUNT)
                .tag("fileUri", fileUri)
                .sendDelay();
    }

    public void reportJSResourceNoExist(String fileUri, String dioFilePath, boolean isDioFileExist, boolean isMd5Same, boolean isJsResourceExist) {
        record(ReporterFields.REPORT_MSC_JS_RESOURCE_NOT_EXIST_COUNT)
                .tag("fileUri", fileUri)
                .tag("dioFilePath", dioFilePath)
                .tag("isDioFileExist", isDioFileExist)
                .tag("isMd5Same", isMd5Same)
                .tag("isJsResourceExist", isJsResourceExist)
                .sendDelay();
    }

    /**
     * 业务指定版本时，上报版本匹配结果
     * Perf配置告警
     *
     * @param minVersion 业务指定的版本
     * @param value      不满足时，value为1；满足时，value为0
     */
    public void reportBasePackageVersionError(String minVersion, boolean isTriggerUpgrade, int value) {
        record(ReporterFields.REPORT_MSC_BASE_PACKAGE_VERSION_ERROR_COUNT)
                .tag("minVersion", minVersion)
                .tag("isTriggerUpgrade", isTriggerUpgrade)
                .value(value)
                .sendDelay();
    }

    /**
     * 上报获取到非法的基础库版本
     */
    public void reportIllegalBaseVersion(String[] sdkReloadVersions, @LoadType String loadType) {
        record(ReporterFields.REPORT_MSC_BASE_PACKAGE_VERSION_ILLEGAL_COUNT)
                .tag("sdkReloadVersions", arrayToJsonArray(sdkReloadVersions))
                .tag("loadType", loadType)
                .sendDelay();
    }

    private JSONArray arrayToJsonArray(String[] sdkReloadVersions) {
        JSONArray array = new JSONArray();
        for (String version : sdkReloadVersions) {
            array.put(version);
        }
        return array;
    }

    public static class CommonReporter extends MSCReporter {

        public static CommonReporter create() {
            return new CommonReporter();
        }

        private CommonReporter() {
            this.commonTag(CommonTags.SDK_VERSION, BuildConfig.AAR_VERSION);
        }

        public void reportDDResourceInvalid(String checkScene, String pkgType, @NonNull DDResource ddResource,
                                            boolean fileExist, boolean isMd5Same, boolean preCheckFileExist,
                                            boolean preCheckIsMd5Same) {
            record(ReporterFields.REPORT_MSC_PACKAGE_INVALID_COUNT)
                    .value(ReportValue.FAILED)
                    .tag(CommonTags.TAG_PKG_TYPE, pkgType)
                    .tag(CommonTags.TAG_PKG_NAME, ddResource.getName())
                    .tag("md5", ddResource.getMd5())
                    .tag("isFromNet", ddResource.isFromNet())
                    .tag("checkScene", checkScene)
                    .tag("fileExist", fileExist)
                    .tag("isMd5Same", isMd5Same)
                    .tag("enablePreCheck", MSCHornPreloadConfig.enablePreCheckDDResourceMd5())
                    .tag("preCheckFileExist", preCheckFileExist)
                    .tag("preCheckIsMd5Same", preCheckIsMd5Same)
                    .sendRealTime();
        }

        public void reportUsingRuntimeCountWhenDDResourceInvalid(int usingRuntimeCount) {
            record(ReporterFields.REPORT_MSC_PACKAGE_INVALID_USING_RUNTIME_COUNT)
                    .value(ReportValue.FAILED)
                    .tag("usingRuntimeCount", usingRuntimeCount)
                    .sendRealTime();
        }

        public void reportResourceMD5CheckDuration(DDResource resource, long duration) {
            record(ReporterFields.REPORT_MSC_PACKAGE_MD5_CHECK_DURATION)
                    .tag("name", resource.getName())
                    .tag("md5", resource.getMd5())
                    .value(duration)
                    .sendDelay();
        }

        public void reportDDResourceInvalidPreCheck(@NonNull PackageInfoWrapper packageInfoWrapper,
                                                    boolean resourceExists) {
            DDResource ddResource = packageInfoWrapper.getDDResource();
            if (ddResource == null) {
                return;
            }
            record(ReporterFields.REPORT_MSC_PACKAGE_PRE_CHECK_INVALID_COUNT)
                    .value(ReportValue.FAILED)
                    .tag(CommonTags.TAG_PKG_TYPE, packageInfoWrapper.getPkgTypeString())
                    .tag(CommonTags.TAG_PKG_NAME, ddResource.getName())
                    .tag("md5", ddResource.getMd5())
                    .tag("isFromNet", ddResource.isFromNet())
                    .tag("fileExist", resourceExists)
                    .tag("isMd5Same", false)
                    .sendRealTime();
        }

        public void reportDDResourceFileExist(String pkgType, @NonNull DDResource ddResource,
                                              boolean resourceExists, boolean serviceFileExits) {
            record(ReporterFields.REPORT_MSC_PACKAGE_PRE_CHECK_FILE_NOT_EXIST_COUNT)
                    .value(ReportValue.FAILED)
                    .tag(CommonTags.TAG_PKG_TYPE, pkgType)
                    .tag(CommonTags.TAG_PKG_NAME, ddResource.getName())
                    .tag("md5", ddResource.getMd5())
                    .tag("isFromNet", ddResource.isFromNet())
                    .tag("fileExist", false)
                    .tag("resourceExists", resourceExists)
                    .tag("serviceFileExits", serviceFileExits)
                    .sendRealTime();
        }

        public void reportResourceCheckResult(String pkgType, String pkgName, String pkgMD5, boolean isFromNet, String checkScene, String errorMessage, long duration) {
            record(ReporterFields.REPORT_MSC_RESOURCE_CHECK_RESULT_COUNT)
                    .tag(CommonTags.TAG_PKG_TYPE, pkgType)
                    .tag(CommonTags.TAG_PKG_NAME, pkgName)
                    .tag("md5", pkgMD5)
                    .tag("isFromNet", isFromNet)
                    .tag("checkScene", checkScene)
                    .tag("errorMessage", errorMessage)
                    .sendRealTime();
        }

        public void reportLoadMinVersionError(IOException e) {
            record(ReporterFields.REPORT_MSC_LOAD_MINVERSION_ERROR_COUNT)
                    .tag("reason", e == null ? "" : e.toString())
                    .sendRealTime();
        }

        public void reportBasePackageReloadConfigFetchDuration(String[] sdkReloadVersions, long fetchDuration) {
            try {
                record(ReporterFields.REPORT_MSC_BASE_PACKAGE_RELOAD_CONFIG_FETCH_DURATION)
                        .tag("sdkReloadVersions", new JSONArray(sdkReloadVersions))
                        .value(fetchDuration)
                        .sendRealTime();
            } catch (JSONException e) {
                MSCLog.e(TAG, e, "reportBasePackageReloadConfigFetchDuration");
            }
        }
    }
}
