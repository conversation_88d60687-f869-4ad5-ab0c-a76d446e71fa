package com.meituan.msc.modules.update;

import android.support.annotation.Nullable;

import com.meituan.android.mercury.msc.adaptor.bean.MSCAppMetaInfo;
import com.meituan.android.mercury.msc.adaptor.bean.MSCMetaInfo;
import com.meituan.android.mercury.msc.adaptor.callback.MSCMetaAndPackageInfoCallback;
import com.meituan.android.mercury.msc.adaptor.core.DDLoadMSCAdaptor;
import com.meituan.android.mercury.msc.adaptor.core.MSCLoadExeption;
import com.meituan.met.mercury.load.bean.ExtraParamsBean;
import com.meituan.met.mercury.load.core.DDResource;
import com.meituan.msc.modules.container.ContainerStartState;
import com.meituan.msc.modules.engine.MSCHornRollbackConfig;
import com.meituan.msc.modules.reporter.MSCLog;
import com.meituan.msc.modules.service.codecache.CodeCacheConfig;
import com.meituan.msc.modules.service.codecache.CodeCacheManager;
import com.meituan.msc.modules.storage.StorageManageUtil;
import com.meituan.msc.modules.update.bean.AppMetaInfoWrapper;
import com.meituan.msc.modules.update.bean.PackageInfoWrapper;
import com.meituan.msc.modules.update.metainfo.AppCheckUpdateManager;
import com.meituan.msc.modules.update.metainfo.MetaFetchRulerManager;
import com.meituan.msc.modules.update.pkg.MSCLoadPackageScene;
import com.meituan.msc.modules.update.pkg.PackageLoadManager;
import com.meituan.msc.modules.update.pkg.PrefetchPackageManager;

import java.util.List;

public final class PackagePreDownloadManager {

    private static final String TAG = "PackagePreDownloadManager";

    // TODO: 2024/9/25 tianbin03 通用优化，业务对触达率要求不高的话可以使用，支持其他业务开启的方式在MMP下线迁移预下载功能时一起设计
    public static void predownloadMainPackageByAppId(String appId, boolean enablePreloadAllowCheck, String scene, PreDownloadCallback callback) {
        // 容器启动中，不预下载
        if (ContainerStartState.instance.isContainerLaunching()) {
            AppCheckUpdateManager.sNeedBatchCheckUpdateAfterFP = true;
            AppCheckUpdateManager.sBatchCheckUpdateErrorMsg = "pre download cancel,has page launching";
            MSCLog.i(TAG, AppCheckUpdateManager.sBatchCheckUpdateErrorMsg);
            return;
        }

        // 统一存储管控预下载
        if (enablePreloadAllowCheck && !StorageManageUtil.isPreDownloadAllowed(appId)) {
            String errInfo = "MSC prefetch is disabled by storage management";
            AppCheckUpdateManager.sBatchCheckUpdateErrorMsg = errInfo;
            MSCLog.w(TAG, errInfo, appId);
            return;
        }

        MSCLog.i(TAG, "[MSC][PreDownload]start:", appId);
        MSCMetaAndPackageInfoCallback mscMetaAndPackageInfoCallback = new MSCMetaAndPackageInfoCallback() {
            @Override
            public void onSuccess(@Nullable MSCMetaInfo metaInfo, @Nullable DDResource resource) {
                if (resource == null) {
                    MSCLog.e(TAG, "predownloadMainPackageByAppId resource is null", appId);
                    return;
                }
                if (metaInfo == null || metaInfo.getMscApps() == null || metaInfo.getMscApps().isEmpty()) {
                    MSCLog.e(TAG, "predownloadMainPackageByAppId metaInfo is null", appId);
                    return;
                }
                MSCLog.i(TAG, "[MSC][PreDownload]end:", appId, metaInfo, resource);
                MSCAppMetaInfo mscAppMetaInfo = metaInfo.getMscApps().get(0);
                PackageInfoWrapper mainPackageWrapper = new AppMetaInfoWrapper(mscAppMetaInfo).createMainPackageWrapper();
                mainPackageWrapper.setDDResource(resource);

                if (callback != null) {
                    callback.onSuccess(new AppMetaInfoWrapper(mscAppMetaInfo), mainPackageWrapper);
                }

                // 这里预下载完成，生成CodeCache文件
                if (!CodeCacheConfig.INSTANCE.isBlockedToCreateCodeCacheWhenPreDownload(appId)) {
                    CodeCacheManager.getInstance().createCodeCacheAsync(appId, mscAppMetaInfo.getVersion(), mainPackageWrapper);
                }
                // 用于上报预下载成功率维度
                PrefetchPackageManager.cachePreDownloadPackages(appId, mainPackageWrapper);

                PackagePreLoadReporter.create(appId).onFetchMetaInfoSuccess();
                PackagePreLoadReporter.reportPackageLoadSuccessRate(appId, mscAppMetaInfo.getVersion(), mainPackageWrapper, null);
                PackageLoadManager.getInstance().checkDDResourceMd5AndReport("preDownload", mainPackageWrapper);
            }

            @Override
            public void onFail(int errorStep, @Nullable MSCLoadExeption exception) {
                MSCLog.e(TAG, "predownloadMainPackageByAppId fail", appId, exception);

                if (callback != null) {
                    callback.onFail(errorStep, exception);
                }

                if (errorStep == MSCLoadExeption.ERROR_STEP_META) {
                    PackagePreLoadReporter.create(appId).onFetchMetaInfoFailed(exception);
                } else if (errorStep == MSCLoadExeption.ERROR_STEP_PACKAGE) {
                    PackagePreLoadReporter.reportPackageLoadSuccessRate(appId, null, null, exception);
                }
            }
        };
        List<ExtraParamsBean> globPkgExtraParams = AppCheckUpdateManager.getInstance().getPkgExtraParams();
        List<ExtraParamsBean> pkgExtraParams = globPkgExtraParams;
        if (MSCHornRollbackConfig.enablePkgExtraParam()) {
            DDLoadMSCAdaptor.setEnableSingleParamToAppExtraParam(true);
            pkgExtraParams = MetaFetchRulerManager.getInstance().getExtraParamsList(appId, globPkgExtraParams);
        }

        if (MSCHornRollbackConfig.enablePrefetchOptimizer()) {
            DDLoadMSCAdaptor.updateMainPackageWithAppId(appId, scene, MSCLoadPackageScene.isUseImmediately(scene), pkgExtraParams, true, mscMetaAndPackageInfoCallback);
        } else {
            DDLoadMSCAdaptor.updateMainPackageWithAppId(appId, scene, MSCLoadPackageScene.isUseImmediately(scene), pkgExtraParams, mscMetaAndPackageInfoCallback);
        }
    }

    public interface PreDownloadCallback {
        void onSuccess(AppMetaInfoWrapper metaInfoWrapper, PackageInfoWrapper mainPackageWrapper);

        void onFail(int errorStep, @Nullable MSCLoadExeption exception);
    }
}
