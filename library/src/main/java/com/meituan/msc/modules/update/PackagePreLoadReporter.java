package com.meituan.msc.modules.update;

import android.support.annotation.Nullable;
import android.support.annotation.StringDef;

import com.meituan.android.mercury.msc.adaptor.core.MSCLoadExeption;
import com.meituan.met.mercury.load.core.DDResource;
import com.meituan.msc.common.config.MSCConfig;
import com.meituan.msc.common.report.MetricsEntry;
import com.meituan.msc.extern.MSCEnvHelper;
import com.meituan.msc.lib.BuildConfig;
import com.meituan.msc.modules.engine.MSCHornRollbackConfig;
import com.meituan.msc.modules.reporter.CommonTags;
import com.meituan.msc.modules.reporter.MSCReporter;
import com.meituan.msc.modules.reporter.ReporterFields;
import com.meituan.msc.modules.update.bean.PackageInfoWrapper;

import java.lang.annotation.Retention;
import java.lang.annotation.RetentionPolicy;
import java.util.HashMap;
import java.util.Map;

import static com.meituan.met.mercury.load.core.DDLoaderException.ERROR_CODE_UNKNOWN;

/**
 * 包模块预加载模式下指标数据上报，区别为没有runtime 部分公共维度无数据
 * https://km.sankuai.com/page/1287876075
 */
public class PackagePreLoadReporter extends MSCReporter {

    private PackagePreLoadReporter() {
        this.commonTag(CommonTags.SDK_VERSION, BuildConfig.AAR_VERSION);
    }

    private PackagePreLoadReporter(String appId) {
        this();
        this.commonTag(CommonTags.TAG_MSC_APP_ID, appId);
    }

    public static PackagePreLoadReporter create() {
        return new PackagePreLoadReporter();
    }

    public static PackagePreLoadReporter create(String appId) {
        return new PackagePreLoadReporter(appId);
    }

    public void onFetchMetaInfoSuccess() {
        reportFetchMetaInfo(ReportValue.SUCCESS , null);
    }

    public void onFetchMetaInfoFailed(MSCLoadExeption error) {
        reportFetchMetaInfo(ReportValue.FAILED, error);
    }

    private void reportFetchMetaInfo(int value, @Nullable MSCLoadExeption error) {
        record(ReporterFields.REPORT_MSC_METAINFO_LOAD_SUCCESS_RATE)
                .value(value)
                .tag(CommonTags.TAG_LOAD_TYPE, PackageLoadReporter.LoadType.NETWORK)
                .tag(CommonTags.TAG_SOURCE_FROM, "prefetch")
                .tag(CommonTags.TAG_ERROR_CODE, error != null ? error.getErrCode() : ERROR_CODE_UNKNOWN)
                .tag(CommonTags.TAG_ERROR_MSG, error != null ? error.getMessage() : "")
                .tag("enableInnerMeta", MSCEnvHelper.getEnvInfo().enableInnerMetaInfo())
                .tag("useNetworkRes", "forceUpdate") // 强制更新
                .sendDelay();
    }

    public void reportBatchLoadMetaInfoDuration(@ReportValue int status, long value) {
        record(ReporterFields.REPORT_MSC_METAINFO_LOAD_DURATION)
                .value(value)
                .tag(CommonTags.TAG_LOAD_TYPE, PackageLoadReporter.LoadType.NETWORK)
                .tag(CommonTags.TAG_SOURCE_FROM, PackageLoadReporter.Source.BATCH_PREFETCH)
                .tag("status", status)
                .tag("$sr", 0.05)
                .tag("enableInnerMeta", MSCEnvHelper.getEnvInfo().enableInnerMetaInfo())
                .sendDelay();
    }

    public void onLoadPackageSuccess(PackageReportBean reportBean) {
        reportLoadPackage(reportBean, ReportValue.SUCCESS, null);
    }

    public void onLoadPackageFailed(PackageReportBean reportBean, @Nullable Exception error) {
        reportLoadPackage(reportBean, ReportValue.FAILED, error);
    }

    private void reportLoadPackage(PackageReportBean reportBean, @ReportValue int value, @Nullable Exception error) {
        Map<String, Object> tmpTags = new HashMap<>();
        tmpTags.put(CommonTags.TAG_MSC_APP_ID, reportBean.getMscAppId());
        tmpTags.put(CommonTags.TAG_MSC_APP_VERSION, reportBean.getMscAppVersion());
        tmpTags.put(CommonTags.TAG_SOURCE_FROM, reportBean.getSourceFrom());
        tmpTags.put(CommonTags.TAG_LOAD_TYPE, reportBean.getLoadType());
        tmpTags.put(CommonTags.TAG_PKG_TYPE, reportBean.getPkgType());
        tmpTags.put(CommonTags.TAG_PKG_NAME, reportBean.getPkgName());
        tmpTags.put(CommonTags.TAG_ERROR_CODE, PackageLoadReporter.getErrorCode(error));
        tmpTags.put(CommonTags.TAG_ERROR_MSG, PackageLoadReporter.getErrorMsg(error));
        record(ReporterFields.REPORT_MSC_PACKAGE_LOAD_SUCCESS_RATE)
                .value(value)
                .tags(tmpTags)
                .tag("$sr", 0.05)
                .sendDelay();
        if (value == ReportValue.FAILED) {
            record(ReporterFields.REPORT_MSC_PACKAGE_LOAD_FAIL_COUNT)
                    .tags(tmpTags)
                    .sendDelay();
        }
    }

    public void reportLoadPackageSuccessDuration(PackageReportBean reportBean, long value) {
        reportLoadPackageDuration(reportBean, value, ReportValue.SUCCESS);
    }

    private void reportLoadPackageDuration(PackageReportBean reportBean, long value, @ReportValue int status) {
        MetricsEntry metricsEntry = record(ReporterFields.REPORT_MSC_PACKAGE_LOAD_DURATION)
                .tag(CommonTags.TAG_MSC_APP_VERSION, reportBean.getMscAppVersion())
                .tag(CommonTags.TAG_LOAD_TYPE, reportBean.getLoadType())
                .tag(CommonTags.TAG_PKG_NAME, reportBean.getPkgName())
                .tag(CommonTags.TAG_PKG_TYPE, reportBean.getPkgType())
                .tag(CommonTags.TAG_SOURCE_FROM, reportBean.getSourceFrom())
                .tag("status", status)
                .tag("$sr", 0.05)
                .value(value);

        if (MSCHornRollbackConfig.enableAddLoadPackageDetails()) {
            metricsEntry.tag(CommonTags.TAG_LOAD_PKG_Details, reportBean.getDdLoadPhaseDataJsonObject());
        }

        metricsEntry.sendDelay();
    }

    public static void reportPackageLoadSuccessRate(String mscAppId, String mscAppVersion,
                                                    PackageInfoWrapper packageInfoWrapper,
                                                    MSCLoadExeption e) {
        if (!MSCEnvHelper.isInited()) {
            // TODO MSCEnvHelper未初始化时也应当上报指标
            return;
        }
        PackageReportBean reportBean = new PackageReportBean.Builder()
                .setPkgType("main")
                .setMscAppVersion(mscAppVersion)
                .setSourceFrom(PackagePreLoadReporter.SOURCE_FROM_TYPE_PREDOWNLOAD)
                .setMscAppId(mscAppId)
                .build();
        if (packageInfoWrapper != null) {
            DDResource ddResource = packageInfoWrapper.getDDResource();
            reportBean.setLoadType(ddResource.isFromNet() ? PackageLoadReporter.LoadType.NETWORK : PackageLoadReporter.LoadType.LOCAL);
            reportBean.setPkgName(ddResource.getName());
            reportBean.setPkgType(packageInfoWrapper.getPkgTypeString());
            PackageLoadReporter.create(null).onLoadPackageSuccess(reportBean);
        } else {
            reportBean.setDdLoadPhaseData(e != null ? e.getLoadPhaseData() : null);
            PackageLoadReporter.create(null).onLoadPackageFailed(reportBean, e);
        }
    }

    public static final String SOURCE_FROM_TYPE_PREDOWNLOAD = "predownload";
    public static final String SOURCE_FROM_TYPE_BGDOWNLOAD = "backgrounddownload";
    public static final String SOURCE_FROM_TYPE_PRELOAD = "preload";
    public static final String SOURCE_FROM_TYPE_LAUNCH = "launch";

    @StringDef({SOURCE_FROM_TYPE_PREDOWNLOAD,
            SOURCE_FROM_TYPE_BGDOWNLOAD,
            SOURCE_FROM_TYPE_PRELOAD,
            SOURCE_FROM_TYPE_LAUNCH})
    @Retention(RetentionPolicy.SOURCE)
    public @interface PackageLoadSourceFrom {

    }
}
