package com.meituan.msc.modules.update;

import android.text.TextUtils;

import com.google.gson.Gson;
import com.meituan.met.mercury.load.bean.DDLoadPhaseData;
import com.meituan.msc.jse.bridge.ConversionUtil;

import org.json.JSONException;
import org.json.JSONObject;

public class PackageReportBean {

    private String mscAppId;
    private String mscAppVersion;
    @PackagePreLoadReporter.PackageLoadSourceFrom
    private String sourceFrom;
    @PackageLoadReporter.LoadType
    private String loadType;
    private String pkgName;
    private String pkgType;

    private DDLoadPhaseData ddLoadPhaseData;

    public String getMscAppId() {
        return mscAppId;
    }

    public void setMscAppId(String mscAppId) {
        this.mscAppId = mscAppId;
    }

    public String getMscAppVersion() {
        return mscAppVersion;
    }

    public void setMscAppVersion(String mscAppVersion) {
        this.mscAppVersion = mscAppVersion;
    }

    @PackagePreLoadReporter.PackageLoadSourceFrom
    public String getSourceFrom() {
        return sourceFrom;
    }

    public void setSourceFrom(@PackagePreLoadReporter.PackageLoadSourceFrom String sourceFrom) {
        this.sourceFrom = sourceFrom;
    }

    @PackageLoadReporter.LoadType
    public String getLoadType() {
        return loadType;
    }

    public void setLoadType(@PackageLoadReporter.LoadType String loadType) {
        this.loadType = loadType;
    }

    public String getPkgName() {
        return pkgName;
    }

    public void setPkgName(String pkgName) {
        this.pkgName = pkgName;
    }

    public String getPkgType() {
        return pkgType;
    }

    public void setPkgType(String pkgType) {
        this.pkgType = pkgType;
    }

    public JSONObject getDdLoadPhaseDataJsonObject() {
        if (ddLoadPhaseData == null) {
            return new JSONObject();
        }

        String dataJsonString = ConversionUtil.getGson().toJson(ddLoadPhaseData);
        if (!TextUtils.isEmpty(dataJsonString)) {
            try {
               return new JSONObject(dataJsonString);
            } catch (JSONException e) {
            }
        }

        return new JSONObject();
    }

    public void setDdLoadPhaseData(DDLoadPhaseData ddLoadPhaseData) {
        this.ddLoadPhaseData = ddLoadPhaseData;
    }

    private PackageReportBean() {
    }

    public static class Builder {

        private String mscAppId;
        private String mscAppVersion;
        @PackagePreLoadReporter.PackageLoadSourceFrom
        private String sourceFrom;
        @PackageLoadReporter.LoadType
        private String loadType;
        private String pkgName;
        private String pkgType;

        private DDLoadPhaseData ddLoadPhaseData;

        public Builder setMscAppId(String mscAppId) {
            this.mscAppId = mscAppId;
            return this;
        }

        public Builder setMscAppVersion(String mscAppVersion) {
            this.mscAppVersion = mscAppVersion;
            return this;
        }

        public Builder setSourceFrom(@PackagePreLoadReporter.PackageLoadSourceFrom String sourceFrom) {
            this.sourceFrom = sourceFrom;
            return this;
        }

        public Builder setLoadType(@PackageLoadReporter.LoadType String loadType) {
            this.loadType = loadType;
            return this;
        }

        public Builder setPkgName(String pkgName) {
            this.pkgName = pkgName;
            return this;
        }

        public Builder setPkgType(String pkgType) {
            this.pkgType = pkgType;
            return this;
        }

        public Builder setDdLoadPhaseData(DDLoadPhaseData ddLoadPhaseData) {
            this.ddLoadPhaseData = ddLoadPhaseData;
            return this;
        }

        public PackageReportBean build() {
            PackageReportBean packageReportBean = new PackageReportBean();
            packageReportBean.setMscAppId(mscAppId);
            packageReportBean.setMscAppVersion(mscAppVersion);
            packageReportBean.setSourceFrom(sourceFrom);
            packageReportBean.setLoadType(loadType);
            packageReportBean.setPkgName(pkgName);
            packageReportBean.setPkgType(pkgType);
            packageReportBean.setDdLoadPhaseData(ddLoadPhaseData);
            return packageReportBean;
        }
    }
}
