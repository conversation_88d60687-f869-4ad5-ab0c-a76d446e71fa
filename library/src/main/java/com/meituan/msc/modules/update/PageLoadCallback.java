package com.meituan.msc.modules.update;

import com.meituan.msc.modules.update.bean.PackageInfoWrapper;

import java.util.List;

public interface PageLoadCallback {

    /**
     * 单包加载回调
     *
     * @param packageInfo 单包信息
     */
    void onPackageLoaded(PackageInfoWrapper packageInfo);

    /**
     * 所有包 加载完毕 回调
     *
     * @param packageList 所有包信息
     */
    void onAllPackageLoaded(List<PackageInfoWrapper> packageList);

    /**
     * 包加载失败
     *
     * @param msg 错误描述
     * @param e   异常
     */
    void onPackageLoadFailed(String msg, Exception e);

    /**
     * 包加载被取消
     * 1. 子包加载显示loading支持取消
     */
    void onPackageLoadCanceled();

    /**
     * 单包逻辑层注入成功
     *
     * @param packageInfo 包信息
     * @param realLoaded  是否加载完毕
     */
    void onPackageInjectSuccess(PackageInfoWrapper packageInfo, boolean realLoaded);

    /**
     * 单包逻辑层注入失败
     *
     * @param packageInfo 包信息
     * @param e           异常
     */
    void onPackageInjectFailed(PackageInfoWrapper packageInfo, String errorMsg, Exception e);

    /**
     * 所有包 注入完毕 回调
     */
    void onAllPackageInjected();
}
