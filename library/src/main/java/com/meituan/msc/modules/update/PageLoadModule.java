package com.meituan.msc.modules.update;


import android.support.annotation.NonNull;
import android.support.annotation.Nullable;
import android.text.TextUtils;

import com.meituan.android.mercury.msc.adaptor.bean.MSCMetaInfo;
import com.meituan.android.mercury.msc.adaptor.callback.MSCMetaInfoCallback;
import com.meituan.android.mercury.msc.adaptor.core.DDLoadMSCAdaptor;
import com.meituan.android.mercury.msc.adaptor.core.MSCLoadExeption;
import com.meituan.met.mercury.load.bean.DDLoadPhaseData;
import com.meituan.met.mercury.load.fault.FaultDetectionManager;
import com.meituan.msc.common.config.MSCDegradeConfig;
import com.meituan.msc.common.support.java.util.concurrent.CompletableFuture;
import com.meituan.msc.common.utils.CollectionUtil;
import com.meituan.msc.modules.apploader.events.AppLoadException;
import com.meituan.msc.modules.engine.AppService;
import com.meituan.msc.modules.engine.MSCApp;
import com.meituan.msc.modules.engine.MSCHornRollbackConfig;
import com.meituan.msc.modules.engine.MSCPackageLoadCallback;
import com.meituan.msc.modules.engine.MSCRuntime;
import com.meituan.msc.modules.engine.RuntimeManager;
import com.meituan.msc.modules.manager.MSCModule;
import com.meituan.msc.modules.manager.ModuleName;
import com.meituan.msc.modules.reporter.MSCLoadErrorConstants;
import com.meituan.msc.modules.reporter.MSCLog;
import com.meituan.msc.modules.reporter.MSCReporter;
import com.meituan.msc.modules.update.bean.AppMetaInfoWrapper;
import com.meituan.msc.modules.update.bean.CheckUpdateParams;
import com.meituan.msc.modules.update.bean.LaunchPageParams;
import com.meituan.msc.modules.update.bean.PackageInfoWrapper;
import com.meituan.msc.modules.update.metainfo.AppCheckUpdateManager;
import com.meituan.msc.modules.update.metainfo.CheckUpdateCallback;
import com.meituan.msc.modules.update.pkg.PackageLoadCallback;
import com.meituan.msc.modules.update.pkg.PackageLoadHelper;
import com.meituan.msc.modules.update.pkg.PackageLoadManager;
import com.meituan.msc.util.perf.PerfEventRecorder;

import java.util.ArrayList;
import java.util.List;
import java.util.Map;
import java.util.concurrent.ConcurrentHashMap;
import com.meituan.met.mercury.load.fault.FaultMonitor;

/**
 * 包下载时显示Dialog，失败时退出，成功时触发包加载至Service
 * <p>
 * Created by letty on 2019/7/29.
 **/

@ModuleName(name = "PackageLoader")
public class PageLoadModule extends MSCModule implements IPageLoadModule {

    private final String TAG = "PageLoadModule@" + Integer.toHexString(hashCode());
    private volatile PackageLoadReporter packageLoadReporter;

    //主包 routeId和包获取各阶段耗时map
    private Map<String, DDLoadPhaseData> loadMainPackagePhaseDataMap = new ConcurrentHashMap<>();

    //子包 routeId和包获取各阶段耗时map
    private Map<String, DDLoadPhaseData> loadSubPackagePhaseDataMap = new ConcurrentHashMap<>();

    public PageLoadModule() {
    }

    public CompletableFuture<AppMetaInfoWrapper> fetchAppMetaInfo(LaunchPageParams params) {
        CompletableFuture<AppMetaInfoWrapper> future = new CompletableFuture<>();
        CheckUpdateParams checkUpdateParams = new CheckUpdateParams(params.appId, params.needForceUpdate ? CheckUpdateParams.Type.NETWORK : CheckUpdateParams.Type.CACHE_OR_NETWORK);
        if (!TextUtils.isEmpty(params.checkUpdateUrl)) {
            checkUpdateParams.checkUpdateUrl = params.checkUpdateUrl;
        }
        final boolean isCheckUpdateInProgress = AppCheckUpdateManager.getInstance().isCheckUpdateInProgress(checkUpdateParams);
        final long startCheckUpdateTs = System.currentTimeMillis();
        MSCRuntime runtime = getRuntime();
        runtime.getRuntimeReporter().addStatisticsToMap("Pre_Meta_Read");
        // 获取版本信息
        AppCheckUpdateManager.getInstance().checkUpdate(checkUpdateParams, new CheckUpdateCallback<AppMetaInfoWrapper>() {
            @Override
            public void onSuccess(@NonNull AppMetaInfoWrapper metaInfo) {
                String loadType = metaInfo.getLoadType();
                if (!isCheckUpdateInProgress) {
                    createPackageLoadReporter().reportLoadMetaInfoDuration(MSCReporter.ReportValue.SUCCESS,
                            System.currentTimeMillis() - startCheckUpdateTs,
                            loadType,
                            PackageLoadReporter.Source.LAUNCH, metaInfo.getUseNetworkRes());
                }

                createPackageLoadReporter().onFetchMetaInfoSuccess(loadType, PackageLoadReporter.Source.LAUNCH, metaInfo.getUseNetworkRes());

                MSCRuntime runtime = getRuntime();
                if (runtime == null) {
                    return;
                }
                MSCApp app = runtime.getApp();
                if (app == null || !TextUtils.equals(metaInfo.getAppId(), params.appId)) {
                    String errorMessage = "小程序appId错误";
                    if (MSCHornRollbackConfig.readConfig().rollbackLoadErrorReportFix) {
                        runtime.getRuntimeReporter().reportMSCLoadError(MSCLoadErrorConstants.ERROR_CODE_WRONG_APP_ID, errorMessage);
                    } else {
                        runtime.getRuntimeReporter().reportMSCLoadError(runtime.hasContainerAttached(),
                                MSCLoadErrorConstants.ERROR_CODE_WRONG_APP_ID, errorMessage);
                    }
                    future.completeExceptionally(new AppLoadException(MSCLoadErrorConstants.ERROR_CODE_WRONG_APP_ID, errorMessage));
                    return;
                }
                runtime.getRuntimeReporter().addStatisticsToMap("After_Meta_Read");
                runtime.getMSCAppModule().updateMetaInfo(metaInfo);
                runtime.getAppConfigModule().applyRouteMapping();
                if (!MSCHornRollbackConfig.isRollbackSetRouteMappingPersist()) {
                    runtime.getAppConfigModule().applyRouteMappingPersist();
                }
                future.complete(metaInfo);
            }

            @Override
            public void onFail(String errMsg, AppLoadException error) {
                MSCLog.i(TAG, "fetchAppMetaInfo normal failed ", errMsg);
                createPackageLoadReporter().onFetchMetaInfoFail(null,
                        PackageLoadReporter.Source.LAUNCH,
                        error);
                MSCRuntime runtime = getRuntime();
                if (runtime != null && runtime.getRuntimeReporter() != null  && error != null) {
                    if (MSCHornRollbackConfig.readConfig().rollbackLoadErrorReportFix) {
                        runtime.getRuntimeReporter().reportMSCLoadError(error.getErrorCode(), error.getMessage());
                    } else {
                        runtime.getRuntimeReporter().reportMSCLoadError(runtime.hasContainerAttached(),
                                error.getErrorCode(), error.getMessage());
                    }
                }

                //元信息获取是否降级状态
                boolean isMetaDegrade = false;
                if (TextUtils.isEmpty(params.checkUpdateUrl) && MSCDegradeConfig.isMetaDegradeEnable()) {
                    FaultMonitor faultMonitor = FaultDetectionManager.getInstance().getMonitor("msc", DDLoadMSCAdaptor.MSC_META_MONITOR_KEY);
                    if (faultMonitor != null) {
                        isMetaDegrade = faultMonitor.isDegraded();
                    }
                }

                if (!params.needForceUpdate || isMetaDegrade) { //非强制更新 或 开启元信息降级
                    // 本地缓存失效时请求线上最新版本，网络不佳等原因失败时，兜底使用本地缓存，对齐MMP逻辑
                    if (isMetaDegrade)  {
                        MSCLog.i(TAG, "get metaInfo in degrade status");
                    } else {
                        MSCLog.i(TAG, "get metaInfo not in degrade status");
                    }
                    getMetInfoFromCache(params, isMetaDegrade, future, error);
                } else {
                    future.completeExceptionally(error);
                }
            }
        });
        return future;
    }

    @Override
    public void downLoadBizPackages(String path, String loadScene, long routeId, PackageDownloadCallback callback) {
        // 准备需要下载更新的包
        List<PackageInfoWrapper> needUpdatePackages = prepareNeedUpdatePkgs(path);
        downloadBizPackages(needUpdatePackages, loadScene, routeId, callback);
    }

    private void addLoadPackageDetails(PackageInfoWrapper packageInfoWrapper, long routeId, DDLoadPhaseData ddLoadPhaseData) {
        if (packageInfoWrapper == null || ddLoadPhaseData == null || routeId <= 0) {
            return;
        }
        MSCLog.i(TAG, "addLoadPackageDetails:", ddLoadPhaseData.toString());
        if (packageInfoWrapper.isMainPackage()) {
            loadMainPackagePhaseDataMap.put(String.valueOf(routeId), ddLoadPhaseData);
        } else {
            loadSubPackagePhaseDataMap.put(String.valueOf(routeId), ddLoadPhaseData);
        }
    }

    @Override
    public DDLoadPhaseData getLoadPackageDetails(long routeId, boolean isMainPackage) {
        if (isMainPackage) {
            return loadMainPackagePhaseDataMap.get(String.valueOf(routeId));
        }

        return loadSubPackagePhaseDataMap.get(String.valueOf(routeId));
    }

    private void downloadBizPackages(List<PackageInfoWrapper> needUpdatePackages, String loadScene, long routeId, PackageDownloadCallback callback) {
        PerfEventRecorder recorder = getRuntime().getPerfEventRecorder();
        if (CollectionUtil.isEmpty(needUpdatePackages)) {
            MSCLog.i(TAG, "needUpdatePackages empty");
            callback.onAllPackageLoaded(needUpdatePackages);
            return;
        }
        for (PackageInfoWrapper packageInfo : needUpdatePackages) {
            MSCLog.i(TAG, "loadAndInjectPackages:", packageInfo);
            if (MSCHornRollbackConfig.enableAddLoadPackageDetails()) {
                downloadMainOrSubPackage(recorder, needUpdatePackages, new PackageDownloadCallback() {
                    @Override
                    public void onPackageLoaded(PackageInfoWrapper packageInfo) {
                        if (packageInfo != null && packageInfo.getDDResource() != null) {
                            addLoadPackageDetails(packageInfo, routeId, packageInfo.getDDResource().getLoadPhaseData());
                        }

                        if (callback != null) {
                            callback.onPackageLoaded(packageInfo);
                        }
                    }

                    @Override
                    public void onAllPackageLoaded(List<PackageInfoWrapper> packageList) {
                        if (callback != null) {
                            callback.onAllPackageLoaded(packageList);
                        }
                    }

                    @Override
                    public void onPackageLoadFailed(String msg, Exception e) {
                        if (e instanceof AppLoadException) {
                            Throwable cause = e.getCause();
                            if (cause instanceof MSCLoadExeption) {
                                MSCLoadExeption mscLoadExeption = (MSCLoadExeption) cause;
                                addLoadPackageDetails(packageInfo, routeId, mscLoadExeption.getLoadPhaseData());
                            }
                        }
                        if (callback != null) {
                            callback.onPackageLoadFailed(msg, e);
                        }
                    }

                    @Override
                    public void onPackageLoadCanceled() {
                        if (callback != null) {
                            callback.onPackageLoadCanceled();
                        }
                    }
                }, packageInfo, loadScene);
            } else {
                downloadMainOrSubPackage(recorder, needUpdatePackages, callback, packageInfo, loadScene);
            }
        }
    }

    @Override
    public void injectPackages(List<PackageInfoWrapper> needUpdatePackages, PackageInjectCallback callback) {
        // MSCLog.i(TAG, "[MSC_LOG]injectPackages... appId:", getRuntime().getAppId());
        injectMainPackage(needUpdatePackages, new PackageInjectCallback() {
            @Override
            public void onPackageInjectSuccess(PackageInfoWrapper packageInfo, boolean realLoaded) {
                for (PackageInfoWrapper packageInfoWrapper : needUpdatePackages) {
                    if (packageInfoWrapper.isMainPackage()) {
                        continue;
                    }
                    // MSCLog.i(TAG, "[MSC_LOG]injectPackage 子包注入 appId:", getRuntime().getAppId(),
                    //                            ",time:", ContainerController.TIME_FORMAT.format(new Date(System.currentTimeMillis())));
                    injectPackage(packageInfoWrapper, needUpdatePackages, callback);
                }
            }

            @Override
            public void onPackageInjectFailed(PackageInfoWrapper packageInfo, String errorMsg, AppLoadException e) {
                callback.onPackageInjectFailed(packageInfo, errorMsg, e);
            }

            @Override
            public void onAllPackageInjected() {
                callback.onAllPackageInjected();
            }
        });
    }

    @Override
    public void injectPackages(String targetPath, PackageInjectCallback callback) {
        List<PackageInfoWrapper> needUpdatePackages = getDependPackages(targetPath);
        injectPackages(needUpdatePackages, callback);
    }

    private void injectMainPackage(List<PackageInfoWrapper> needUpdatePackages, PackageInjectCallback callback) {
        if (CollectionUtil.isEmpty(needUpdatePackages)) {
            callback.onAllPackageInjected();
        }
        for (PackageInfoWrapper packageInfoWrapper : needUpdatePackages) {
            if (packageInfoWrapper.isMainPackage()) {
                // MSCLog.i(TAG, "[MSC_LOG]injectPackage 主包注入 appId:", getRuntime().getAppId(),
                //                        ",time:", ContainerController.TIME_FORMAT.format(new Date(System.currentTimeMillis())));
                injectPackage(packageInfoWrapper, needUpdatePackages, callback);
                return;
            }
        }
    }

    private PackageLoadReporter createPackageLoadReporter() {
        if (packageLoadReporter == null) {
            packageLoadReporter = PackageLoadReporter.create(getRuntime());
        }
        return packageLoadReporter;
    }

    private void getMetInfoFromCache(LaunchPageParams params, boolean bMetaDegrade, CompletableFuture<AppMetaInfoWrapper> future,
                                     AppLoadException fetchError) {
        MSCLog.i(TAG, "getMetInfoFromCache start");
        final long startCheckUpdateTs = System.currentTimeMillis();
        CheckUpdateParams checkUpdateParams = new CheckUpdateParams(params.appId, CheckUpdateParams.Type.CACHE);
        final boolean isCheckUpdateInProgress = AppCheckUpdateManager.getInstance().isCheckUpdateInProgress(checkUpdateParams);
        AppCheckUpdateManager.getInstance().checkUpdate(
                checkUpdateParams,
                new CheckUpdateCallback<AppMetaInfoWrapper>() {
                    @Override
                    public void onSuccess(@NonNull AppMetaInfoWrapper metaInfo) {
                        if (!isCheckUpdateInProgress) {
                            createPackageLoadReporter().reportLoadMetaInfoDuration(MSCReporter.ReportValue.SUCCESS,
                                    System.currentTimeMillis() - startCheckUpdateTs,
                                    metaInfo.getLoadType(),
                                    PackageLoadReporter.Source.LAUNCH, metaInfo.getUseNetworkRes());
                        }
                        createPackageLoadReporter().onFetchMetaInfoSuccess(metaInfo.getLoadType(),
                                PackageLoadReporter.Source.LAUNCH, metaInfo.getUseNetworkRes());

                        MSCRuntime runtime = getRuntime();
                        if (runtime == null) {
                            return;
                        }
                        MSCAppModule mscAppModule = runtime.getMSCAppModule();
                        if (mscAppModule == null) {
                            return;
                        }

                        //降级情况下，不校验合法性
                        if (!bMetaDegrade && !MSCHornRollbackConfig.get().getConfig().isRollbackCacheVersionCheck
                                && !AppCheckUpdateManager.getInstance().isLargerThanMinVersion(metaInfo)) {
                            future.completeExceptionally(new AppLoadException(
                                    MSCLoadErrorConstants.ERROR_FETCH_METAINFO_NOT_MATCH_MIN_VERSION, "local cache not match min version"));
                            return;
                        }

                        mscAppModule.updateMetaInfo(metaInfo);
                        future.complete(metaInfo);
                    }

                    @Override
                    public void onFail(String errMsg, AppLoadException error) {
                        MSCLog.i(TAG, "getMetInfoFromCache fail", errMsg);
                        createPackageLoadReporter().onFetchMetaInfoFail(PackageLoadReporter.LoadType.LOCAL,
                                PackageLoadReporter.Source.LAUNCH,
                                error);
                        if (bMetaDegrade) {
                            getMetInfoFromS3(params, future, fetchError);
                        } else {
                            // 缓存获取失败时，返回更前置的网络拉取出现的异常，便于归因根本问题
                            // 本地可能没有缓存，会返回异常
                            if (MSCHornRollbackConfig.readConfig().rollbackGetMetaInfoCacheError) {
                                future.completeExceptionally(error);
                            } else {
                                future.completeExceptionally(fetchError);
                            }
                        }
                    }
                });
    }

    private void getMetInfoFromS3(LaunchPageParams params, CompletableFuture<AppMetaInfoWrapper> future,
                                       AppLoadException fetchError) {
        //从S3服务器获取元信息
        MSCLog.i(TAG, "getMetInfoFromS3 start", params != null ? params.appId : "");
        if (params == null) {
            if (future != null) {
                future.completeExceptionally(fetchError);
            }
            return;
        }

        final long startCheckUpdateTs = System.currentTimeMillis();
        CheckUpdateParams checkUpdateParams = new CheckUpdateParams(params.appId, CheckUpdateParams.Type.S3_DEGRADE);
        final boolean isCheckUpdateInProgress = AppCheckUpdateManager.getInstance().isCheckUpdateInProgress(checkUpdateParams);
        AppCheckUpdateManager.getInstance().checkUpdate(
                checkUpdateParams,
                new CheckUpdateCallback<AppMetaInfoWrapper>() {
                    @Override
                    public void onSuccess(@NonNull AppMetaInfoWrapper metaInfo) {
                        MSCLog.i(TAG, "getMetInfoFromS3 success");
                        MSCRuntime runtime = getRuntime();
                        if (runtime != null) {
                            MSCAppModule mscAppModule = runtime.getMSCAppModule();
                            if (mscAppModule != null) {
                                mscAppModule.updateMetaInfo(metaInfo);
                            }
                        }

                        if (!isCheckUpdateInProgress) {
                            createPackageLoadReporter().reportLoadMetaInfoDuration(MSCReporter.ReportValue.SUCCESS,
                                    System.currentTimeMillis() - startCheckUpdateTs,
                                    metaInfo.getLoadType(),
                                    PackageLoadReporter.Source.S3_DEGRADE_LAUNCH, metaInfo.getUseNetworkRes());
                        }
                        createPackageLoadReporter().onFetchMetaInfoSuccess(metaInfo.getLoadType(),
                                PackageLoadReporter.Source.S3_DEGRADE_LAUNCH, metaInfo.getUseNetworkRes());

                        if (future != null) {
                            future.complete(metaInfo);
                        }

                    }

                    @Override
                    public void onFail(String errMsg, AppLoadException error) {
                        MSCLog.i(TAG, "getMetInfoFromS3 fail", errMsg);
                        createPackageLoadReporter().onFetchMetaInfoFail(PackageLoadReporter.LoadType.S3_DEGRADE,
                                PackageLoadReporter.Source.S3_DEGRADE_LAUNCH,
                                error);
                        if (future != null) {
                            future.completeExceptionally(fetchError);
                        }
                    }
                });
    }

    /**
     * 准备需要下载的包，有以下几种场景：
     * 主包
     * 主包 + 子包
     * 独立子包
     * <p>
     * PS:基础包在单独的任务中加载
     *
     * @param path 页面路径
     * @return 需要更新的包列表
     */
    @NonNull
    private List<PackageInfoWrapper> prepareNeedUpdatePkgs(String path) {
        MSCAppModule mscAppModule = getRuntime().getMSCAppModule();
        // 包资源已存在
        if (!MSCHornRollbackConfig.get().getConfig().isRollbackKeepAliveMd5CheckOptimizeChange
                && mscAppModule.isPackageLoaded(path)) {
            MSCLog.i(TAG, "packages loaded");
            return new ArrayList<>();
        }
        return getDownloadPackages(path);
    }

    /**
     * 获取该页面路径需要下载的包列表
     *
     * @param path 页面路径
     * @return 下载包列表
     */
    public List<PackageInfoWrapper> getDownloadPackages(String path) {
        List<PackageInfoWrapper> downloadPackages = new ArrayList<>();
        MSCAppModule mscAppModule = getRuntime().getMSCAppModule();

        if (mscAppModule.isSubPackagePage(path)) {
            if (mscAppModule.getSubPackageCachedByPath(path) == null) {
                downloadPackages.add(mscAppModule.createSubPackageWrapper(path));
            }
        }
        if (mscAppModule.getMainPackageWrapper() == null) {
            downloadPackages.add(mscAppModule.createMainPackageWrapper());
        }

        return downloadPackages;
    }

    public List<PackageInfoWrapper> getDependPackages(String path) {
        List<PackageInfoWrapper> dependPackages = new ArrayList<>();
        MSCAppModule mscAppModule = getRuntime().getMSCAppModule();
        PackageInfoWrapper subPackage = mscAppModule.getSubPackageCachedByPath(path);
        if (subPackage != null) {
            dependPackages.add(subPackage);
            //        } else if (indepSubPkg != null) {
            // 页面在 独立子包，预留
        }
        dependPackages.add(mscAppModule.getMainPackageWrapper());

        return dependPackages;
    }

    private void injectPackage(@NonNull PackageInfoWrapper data, List<PackageInfoWrapper> needInjectPackages, @NonNull PackageInjectCallback callback) {
        // MSCLog.i(TAG, "[MSC_LOG]injectPackage appId:", getRuntime().getAppId(),
        //                ",time:", ContainerController.TIME_FORMAT.format(new Date(System.currentTimeMillis())));
        getRuntime().getModule(AppService.class).loadPackage(data, new MSCPackageLoadCallback() {
            @Override
            public void onPackageLoadSuccess(@NonNull PackageInfoWrapper packageInfo, boolean realLoaded) {
                MSCLog.i(TAG, "onPackageInjectSuccess:", packageInfo);
                data.isPackageInjected = true;
                createPackageLoadReporter().onInjectPackage(MSCReporter.ReportValue.SUCCESS,
                        data.getPkgTypeString(), data.getPackageName());
                callback.onPackageInjectSuccess(packageInfo, realLoaded);

                if (PackageLoadHelper.isAllPackageInjectToAppService(needInjectPackages)) {
                    callback.onAllPackageInjected();
                }
            }

            @Override
            public void onPackageLoadFailed(@NonNull PackageInfoWrapper packageInfo, AppLoadException e) {
                String errorMsg = "onPackageLoadFailed:" + (e == null ? "" : e.toString());
                MSCLog.e(TAG, e, errorMsg);
                createPackageLoadReporter().onInjectPackage(MSCReporter.ReportValue.FAILED,
                        data.getPkgTypeString(), data.getPackageName(), MSCLoadExceptionHelper.getErrorCode(e),
                        MSCLoadExceptionHelper.getErrorMsg(e));
                callback.onPackageInjectFailed(packageInfo, errorMsg, e);
            }
        });
    }

    @Override
    public void downloadSubPackage(PackageInfoWrapper packageInfoWrapper, String loadScene, PackageDownloadCallback callback) {
        downloadMainOrSubPackage(null, null, callback, packageInfoWrapper, loadScene);
    }

    public void downloadMainOrSubPackage(PerfEventRecorder recorder, List<PackageInfoWrapper> needUpdatePackages,
                                         @NonNull PackageDownloadCallback callback, PackageInfoWrapper packageInfo, String loadScene) {
        final long startLoadPackageTime = System.currentTimeMillis();
        PackageLoadManager.getInstance().loadPackageWithInfo(
                recorder,
                packageInfo,
                true,
                // TODO: 8/19/24 待确认下载场景
                null,
                loadScene,
                new PackageLoadCallback<PackageInfoWrapper>() {
                    @Override
                    public void onSuccess(@NonNull PackageInfoWrapper packageInfoWrapper) {
                        // TODO loadPackageWithInfo中有重试逻辑，需要补充上报
                        PackageReportBean packageReportBean = new PackageReportBean.Builder()
                                .setLoadType(packageInfoWrapper.isFromNet() ?
                                        PackageLoadReporter.LoadType.NETWORK : PackageLoadReporter.LoadType.LOCAL)
                                .setPkgName(packageInfo.getPackageName())
                                .setPkgType(packageInfo.getPkgTypeString())
                                .setDdLoadPhaseData(packageInfoWrapper.getDDLoadPhaseData())
                                .setSourceFrom(RuntimeManager.getSourceFrom(getRuntime()))
                                .build();
                        createPackageLoadReporter().reportLoadPackageSuccessDuration(packageReportBean, System.currentTimeMillis() - startLoadPackageTime);
                        createPackageLoadReporter().onLoadPackageSuccess(packageReportBean);

                        final MSCRuntime runtime = getRuntime();
                        if (runtime == null) {
                            if (MSCHornRollbackConfig.readConfig().rollbackInjectAdvanceBuildConfig) {
                                MSCLog.i(TAG,"injectMetaInfoConfig rollback");
                                if (MSCHornRollbackConfig.isEnableEnsureDioFile()) {
                                    callback.onPackageLoadCanceled();
                                }
                            }
                            return;
                        }
                        MSCAppModule mscAppModule = runtime.getMSCAppModule();
                        if (mscAppModule == null) {
                            if (MSCHornRollbackConfig.readConfig().rollbackInjectAdvanceBuildConfig) {
                                MSCLog.i(TAG,"injectMetaInfoConfig rollback");
                                if (MSCHornRollbackConfig.isEnableEnsureDioFile()) {
                                    callback.onPackageLoadCanceled();
                                }
                            }
                            return;
                        }
                        mscAppModule.cachePackageWrapper(packageInfoWrapper);
                        callbackResult(packageInfo, needUpdatePackages, callback);
                        runtime.getPerformanceManager().onDownloadPackage(packageInfoWrapper);
                    }

                    @Override
                    public void onFail(String errMsg, AppLoadException error) {
                        createPackageLoadReporter().onLoadPackageFailed(
                                new PackageReportBean.Builder()
                                        .setPkgName(packageInfo.getPackageName())
                                        .setPkgType(packageInfo.getPkgTypeString())
                                        .setDdLoadPhaseData(error != null ? error.getDDPhaseData(): null)
                                        .setSourceFrom(RuntimeManager.getSourceFrom(getRuntime()))
                                        .build(),
                                error);
                        if (MSCHornRollbackConfig.readConfig().rollbackLoadErrorReportFix) {
                            getRuntime().getRuntimeReporter().reportMSCLoadError(error.getErrorCode(), error.getMessage());
                        } else {
                            getRuntime().getRuntimeReporter().reportMSCLoadError(getRuntime().hasContainerAttached(),
                                    error.getErrorCode(), error.getMessage());
                        }
                        callback.onPackageLoadFailed(errMsg, error);
                    }
                });
    }


    private void callbackResult(PackageInfoWrapper packageInfo, List<PackageInfoWrapper> needUpdatePackages, PackageDownloadCallback callback) {
        packageInfo.isSourceReady = true;
        MSCLog.i(TAG, "onPackageLoaded:", packageInfo);
        callback.onPackageLoaded(packageInfo);

        boolean allPackageReady = PackageLoadHelper.isAllPackageReady(needUpdatePackages);
        if (allPackageReady) {
            MSCLog.i(TAG, "onAllPackageLoaded");
            callback.onAllPackageLoaded(needUpdatePackages);
        }
    }
}