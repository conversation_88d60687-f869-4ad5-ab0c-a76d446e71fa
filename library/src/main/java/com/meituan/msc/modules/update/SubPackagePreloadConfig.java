package com.meituan.msc.modules.update;

import android.content.Context;
import android.text.TextUtils;

import com.meituan.msc.common.utils.NetWorkUtils;

import org.json.JSONArray;
import org.json.JSONObject;

import java.util.ArrayList;
import java.util.HashMap;
import java.util.Iterator;

/**
 * 进入小程序页面后预下载子包的配置
 */
public class SubPackagePreloadConfig {

    public static final String NETWORK_WIFI = "wifi";
    public static final String NETWORK_ALL = "all";

    public String network = NETWORK_WIFI; //在指定网络下预下载，可选值为：all: 不限网络  wifi: 仅wifi下预下载
    public ArrayList<String> packages; //进入页面后预下载分包的 root 或 name。__APP__ 表示主包。

    /**
     * 判断当前网络情况是否需要预下载当前配置
     */
    public static boolean isNetWorkNeedDownload(Context context, SubPackagePreloadConfig preloadConfig) {
        if (context == null || preloadConfig == null) return false;
        if (TextUtils.equals(NETWORK_ALL, preloadConfig.network)) {
            return true;
        } else {
            return NetWorkUtils.isWifiConnected(context);
        }
    }

    /**
     * 从config.json文件中提取preloadRules
     */
    public static HashMap<String, SubPackagePreloadConfig> extractPreloadRules(JSONObject jsonObject) {
        HashMap<String, SubPackagePreloadConfig> preloadRules = new HashMap<>();
        if (jsonObject != null) {
            Iterator<String> iterator = jsonObject.keys();
            while (iterator.hasNext()) {
                String key = iterator.next();
                JSONObject rule = jsonObject.optJSONObject(key);
                if (rule != null) {
                    SubPackagePreloadConfig preloadConfig = new SubPackagePreloadConfig();
                    preloadConfig.network = rule.optString("network", SubPackagePreloadConfig.NETWORK_WIFI);
                    JSONArray jsonArray = rule.optJSONArray("packages");
                    if (jsonArray != null && jsonArray.length() > 0) {
                        ArrayList<String> packages = new ArrayList<>();
                        int len = jsonArray.length();
                        for (int i = 0; i < len; i++) {
                            String pkg = jsonArray.optString(i);
                            if (!TextUtils.isEmpty(pkg)) {
                                packages.add(pkg);
                            }
                        }
                        preloadConfig.packages = packages;
                    }
                    preloadRules.put(key, preloadConfig);
                }
            }
        }
        return preloadRules;
    }
}

