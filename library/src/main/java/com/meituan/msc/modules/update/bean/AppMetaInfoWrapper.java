package com.meituan.msc.modules.update.bean;

import android.support.annotation.NonNull;
import android.support.annotation.Nullable;
import android.text.TextUtils;

import com.meituan.android.mercury.msc.adaptor.bean.MSCAppMetaInfo;
import com.meituan.android.mercury.msc.adaptor.bean.MSCCacheFetchResult;
import com.meituan.android.mercury.msc.adaptor.bean.MSCPackageInfo;
import com.meituan.met.mercury.load.bean.BundleData;
import com.meituan.msc.common.config.MSCConfig;
import com.meituan.msc.common.utils.CollectionUtil;
import com.meituan.msc.common.utils.Constants;
import com.meituan.msc.common.utils.MSCAppPropertyUtil;
import com.meituan.msc.common.utils.PathUtil;
import com.meituan.msc.extern.MSCEnvHelper;
import com.meituan.msc.modules.engine.MSCHornRollbackConfig;
import com.meituan.msc.modules.engine.requestPrefetch.PrefetchConfig;
import com.meituan.msc.modules.engine.requestPrefetch.RequestPrefetchManager;
import com.meituan.msc.modules.reporter.MSCLog;
import com.meituan.msc.modules.update.MiniAppPropertyUtil;

import java.util.List;
import java.util.Map;
import java.util.concurrent.ConcurrentHashMap;
import java.util.concurrent.CopyOnWriteArrayList;

public class AppMetaInfoWrapper {

    private final String TAG = "AppMetaInfoWrapper@" + Integer.toHexString(hashCode());

    private final Map<String, PrefetchConfig> prefetchConfigs = new ConcurrentHashMap<>();
    public final static String APP_PREFETCH_CONFIG_KEY = "app_prefetch_config";

    @NonNull
    private final MSCAppMetaInfo metaInfo;
    /**
     * 小程序主包资源信息，loadPackage 后赋值
     */
    public volatile PackageInfoWrapper mainPackageCached;
    /**
     * 小程序子包资源信息，loadPackage 后赋值
     */
    private final List<PackageInfoWrapper> subPackagesCached = new CopyOnWriteArrayList<>();
    /**
     * 小程序版本信息是否来自本地缓存
     */
    private final boolean isFromCache;

    /**
     * 本地预览/调试 小程序版本信息请求地址
     */
    public String checkUpdateUrl;
    /**
     * 是否受最低版本限制进行了网络拉取，用于启动耗时指标上报
     */
    public boolean isFetchedByMinVersionLimit;

    /**
     * 更新MetaInfo结束的时间戳，如果是缓存中取的MetaInfo，则时间戳为-1
     */
    private final long lastUpdateTimeInMs;

    public AppMetaInfoWrapper(@NonNull MSCAppMetaInfo metaInfo) {
        this.metaInfo = metaInfo;
        this.isFromCache = metaInfo.getFrom() == 1;
        // TODO by chendacai: 后面将lastUpdateTimeInMs改为由外部传入
        this.lastUpdateTimeInMs = this.isFromCache ? -1 : System.currentTimeMillis();
    }

    @Nullable
    public PackageInfoWrapper createSubPackageWrapper(String pagePath) {
        if (metaInfo.getSubPackages() == null) {
            return null;
        }
        for (MSCPackageInfo packageInfo : metaInfo.getSubPackages()) {
            if (packageInfo != null && hasPagePathAtMSCPackageInfo(pagePath, packageInfo)) {
                return new PackageInfoWrapper(metaInfo.getAppId(), metaInfo.getPublishId(), PackageInfoWrapper.PACKAGE_TYPE_SUB, packageInfo);
            }
        }
        return null;
    }

    public PackageInfoWrapper createConfigPackageWrapper() {
        if (metaInfo.getConfigPackage() == null) {
            return null;
        }

        return new PackageInfoWrapper(metaInfo.getAppId(), metaInfo.getPublishId(), PackageInfoWrapper.PACKAGE_TYPE_APP_CONFIG, metaInfo.getConfigPackage());
    }

    private boolean hasPagePathAtMSCPackageInfo(String pagePath, @NonNull MSCPackageInfo packageInfo) {
        // 健壮性测试发现的问题 https://km.sankuai.com/collabpage/2709010305
        if (packageInfo.getRoot() == null) {
            return false;
        }
        return PathUtil.eliminateDuplicateSlashForPkgFile(pagePath).startsWith(packageInfo.getRoot());
    }

    @Nullable
    public PackageInfoWrapper getOrCreateSubPackageWrapperByPath(String path) {
        if (TextUtils.isEmpty(path) || CollectionUtil.isEmpty(metaInfo.getSubPackages())) {
            return null;
        }

        // 优先取缓存
        for (PackageInfoWrapper packageInfo : subPackagesCached) {
            if (packageInfo != null && packageInfo.hasPagePath(path)) {
                return packageInfo;
            }
        }

        for (MSCPackageInfo packageInfo : metaInfo.getSubPackages()) {
            if (packageInfo != null && hasPagePathAtMSCPackageInfo(path, packageInfo)) {
                return new PackageInfoWrapper(metaInfo.getAppId(), metaInfo.getPublishId(), PackageInfoWrapper.PACKAGE_TYPE_SUB, packageInfo);
            }
        }
        return null;
    }

    @Nullable
    public PackageInfoWrapper getSubPackageByName(String name) {
        if (TextUtils.isEmpty(name) || CollectionUtil.isEmpty(metaInfo.getSubPackages())) {
            MSCLog.i(TAG, "getSubPackageByName subpackages is empty", name);
            return null;
        }

        // 优先取缓存
        for (PackageInfoWrapper packageInfo : subPackagesCached) {
            if (packageInfo != null && TextUtils.equals(packageInfo.getPackageName(), name)) {
                return packageInfo;
            }
        }

        for (MSCPackageInfo packageInfo : metaInfo.getSubPackages()) {
            if (packageInfo != null && TextUtils.equals(packageInfo.getName(), name)) {
                return new PackageInfoWrapper(metaInfo.getAppId(), metaInfo.getPublishId(), PackageInfoWrapper.PACKAGE_TYPE_SUB, packageInfo);
            }
        }
        return null;
    }

    public String getAppName() {
        MSCAppMetaInfo.BasicInfo basicInfo = getBasicInfo();
        if (basicInfo == null) {
            return "";
        }
        return basicInfo.getName();
    }

    public String getIconPath() {
        MSCAppMetaInfo.BasicInfo basicInfo = getBasicInfo();
        if (basicInfo == null) {
            return "";
        }
        return basicInfo.getIcon();
    }

    public String getLoadingIconURL() {
        MSCAppMetaInfo.BasicInfo basicInfo = getBasicInfo();
        if (basicInfo == null) {
            return "";
        }
        return basicInfo.getLoadingIconURL();
    }

    public String getLoadingTitle() {
        MSCAppMetaInfo.BasicInfo basicInfo = getBasicInfo();
        if (basicInfo == null) {
            return "";
        }
        return basicInfo.getLoadingTitle();
    }

    public boolean getExternalApp() {
        MSCAppMetaInfo.BasicInfo basicInfo = getBasicInfo();
        if (basicInfo == null) {
            return false;
        }
        return basicInfo.getExternalApp();
    }

    public void cachePackageWrapper(@NonNull PackageInfoWrapper packageInfoWrapper) {
        MSCLog.i(TAG, "cachePackageWrapper", packageInfoWrapper);
        if (packageInfoWrapper.packageType == PackageInfoWrapper.PACKAGE_TYPE_MAIN) {
            mainPackageCached = packageInfoWrapper;
        } else if (packageInfoWrapper.packageType == PackageInfoWrapper.PACKAGE_TYPE_BASE) {
            if (!MSCEnvHelper.getEnvInfo().isProdEnv()) {
                throw new IllegalStateException("base package set error");
            }
            MSCLog.w(TAG, "illegal setPackageInfo", packageInfoWrapper);
        } else if (packageInfoWrapper.packageType == PackageInfoWrapper.PACKAGE_TYPE_SUB) {
            subPackagesCached.add(packageInfoWrapper);
        }
    }

    public String getMainPackagePath() {
        return mainPackageCached.getLocalPath();
    }

    public PackageInfoWrapper createMainPackageWrapper() {
        if (mainPackageCached != null) {
            return mainPackageCached;
        }
        return new PackageInfoWrapper(metaInfo.getAppId(), metaInfo.getPublishId(), PackageInfoWrapper.PACKAGE_TYPE_MAIN, getMainPackage());
    }

    public String getPublishId() {
        return metaInfo.getPublishId();
    }

    public PackageInfoWrapper getLoadedPackageInfoDefaultReturnMain(String path) {
        PackageInfoWrapper subPackageInfoByPath = getLoadedSubPackageInfoByPath(path);
        return subPackageInfoByPath != null ? subPackageInfoByPath : mainPackageCached;
    }

    @Nullable
    public PackageInfoWrapper getLoadedPackageInfoWithoutDefault(String path) {
        if (!CollectionUtil.isEmpty(metaInfo.getSubPackages())) {
            for (MSCPackageInfo packageInfo : metaInfo.getSubPackages()) {
                if (packageInfo != null && hasPagePathAtMSCPackageInfo(path, packageInfo)) {
                    return getLoadedSubPackageInfoByPath(path);
                }
            }
        }
        return mainPackageCached;
    }

    @Nullable
    public PackageInfoWrapper getLoadedSubPackageInfoByPath(String path) {
        for (PackageInfoWrapper packageInfo : subPackagesCached) {
            if (packageInfo != null && packageInfo.hasPagePath(path)) {
                return packageInfo;
            }
        }
        return null;
    }

    public boolean isFromCache() {
        return isFromCache;
    }

    public int getMetaFrom() {
        return metaInfo.getFrom();
    }

    public String getBuildId() {
        return metaInfo.getBuildId();
    }

    public boolean isDebug() {
        return !TextUtils.isEmpty(checkUpdateUrl);
    }

    public String getVersion() {
        return metaInfo.getVersion();
    }

    private MSCAppMetaInfo.BasicInfo getBasicInfo() {
        return metaInfo.getBasicInfo();
    }

    public boolean isInner() {
        MSCAppMetaInfo.BasicInfo basicInfo = getBasicInfo();
        if (basicInfo == null) {
            return false;
        }
        return basicInfo.getIsInner();
    }

    public boolean shareSupported() {
        MSCAppMetaInfo.BasicInfo basicInfo = getBasicInfo();
        if (basicInfo == null) {
            return false;
        }
        return basicInfo.getShareSupported() == MiniAppPropertyUtil.KEY_APP_SHARE_ENABLE;
    }

    public String getAppId() {
        return metaInfo.getAppId();
    }

    @Nullable
    public Object getExtraConfigValue(String key) {
        MSCAppMetaInfo.BasicInfo basicInfo = getBasicInfo();
        Map<String, Object> extraConfig;
        if (basicInfo == null || (extraConfig = basicInfo.getExtraConfig()) == null) {
            return null;
        }
        return extraConfig.get(key);
    }

    private MSCPackageInfo getMainPackage() {
        return metaInfo.getMainPackage();
    }

    public String getMainPkgMd5() {
        MSCPackageInfo mainPackage = getMainPackage();
        BundleData ddd;
        if (mainPackage == null || (ddd = mainPackage.getDdd()) == null) {
            return "";
        }
        return ddd.getMd5();
    }

    public List<PackageInfoWrapper> getSubPackagesCached() {
        return subPackagesCached;
    }

    public PackageInfoWrapper getSubPackageCachedByPath(String path){
        if (TextUtils.isEmpty(path) || CollectionUtil.isEmpty(metaInfo.getSubPackages())) {
            return null;
        }

        for (PackageInfoWrapper packageInfo : subPackagesCached) {
            if (packageInfo != null && packageInfo.hasPagePath(path)) {
                return packageInfo;
            }
        }
        return null;
    }

    public PackageInfoWrapper getMainPackageCached() {
        return mainPackageCached;
    }

    public String getMainPath() {
        return metaInfo.getMainPath();
    }

    public String getMetaInfoToString() {
        return metaInfo.toString();
    }

    public String getBasePackageMinVersion() {
        return metaInfo.getMinSdkVersion();
    }

    public long getLastUpdateTimeInMs() {
        return lastUpdateTimeInMs;
    }

    public boolean isPackageLoaded(String path) {
        // 线下构建二维码不包含子包页面场景下，子包数据会为null
        if (metaInfo.getSubPackages() == null) {
            return mainPackageCached != null;
        }
        for (MSCPackageInfo packageInfo : metaInfo.getSubPackages()) {
            if (packageInfo != null && hasPagePathAtMSCPackageInfo(path, packageInfo)) {
                PackageInfoWrapper loadedPackageInfoByPath = getLoadedSubPackageInfoByPath(path);
                if (MSCHornRollbackConfig.isRollbackPackageLoaded()) {
                    return loadedPackageInfoByPath != null;
                } else {
                    return loadedPackageInfoByPath != null && mainPackageCached != null;
                }
            }
        }
        return mainPackageCached != null;
    }

    public PrefetchConfig getPrefetchConfig(String targetPath) {
        boolean isAppLevelPrefetch = RequestPrefetchManager.isAppLevelPrefetchByMetaInfo(this, getAppId());
        //数据预拉取配置的key，用于存取不同类型的数据预拉取配置
        String prefetchConfigKey = isAppLevelPrefetch ? APP_PREFETCH_CONFIG_KEY : targetPath;
        PrefetchConfig config = prefetchConfigs.get(prefetchConfigKey);
        if (config == null) {
            try {
                config = getPrefetchConfig(this, targetPath);
                if (config != null) {
                    prefetchConfigs.put(prefetchConfigKey, config);
                }
            } catch (Exception e) {
                MSCLog.i(e.getMessage());
            }
        }
        return config;
    }

    private PrefetchConfig getPrefetchConfig(AppMetaInfoWrapper metaInfo, String targetPath) throws ClassCastException, IllegalArgumentException {
        Map<String, Object> map = (Map<String, Object>) metaInfo.getExtraConfigValue(Constants.TARGET_PATH_PREFETCH);
        if (map == null) {
            MSCLog.i(RequestPrefetchManager.TAG, "targetPathPrefetch is null!");
            return null;
        }
        // PrefetchConfig
        Object object = null;
        if (!MSCHornRollbackConfig.isRollbackAppDataPrefetchJudge()) {
            if (map.containsKey(Constants.APP_DATA_PREFETCH)) {
                object = map.get(Constants.APP_DATA_PREFETCH);
            } else {
                object = map.get(targetPath);
            }
        } else {
            if (MSCConfig.isAppLevelBackgroundFetchData(metaInfo.getAppId())) {
                if (map.entrySet() != null && map.entrySet().iterator().hasNext()) {
                    object = map.entrySet().iterator().next().getValue();
                }
            } else {
                object = map.get(targetPath);
            }
        }
        if (object == null) {
            MSCLog.i(RequestPrefetchManager.TAG, "use targetPath obtain prefetch is null!");
            return null;
        }
        PrefetchConfig prefetchConfig = PrefetchConfig.parse((Map<String, Object>) object);
        prefetchConfig.pagePath = targetPath;
        return prefetchConfig;
    }

    public boolean isSubPackagePage(String path){
        if (metaInfo.getSubPackages() == null) {
            return false;
        }
        for (MSCPackageInfo packageInfo : metaInfo.getSubPackages()) {
            if (packageInfo != null && hasPagePathAtMSCPackageInfo(path, packageInfo)) {
                return true;
            }
        }
        return false;
    }

    @Nullable
    public MSCAppMetaInfo.AdvanceBuildConfig getAdvanceBuildConfig() {
        return metaInfo.getAdvanceBuildConfig();
    }

    public String getLoadType() {
        return MSCAppPropertyUtil.getLoadType(metaInfo);
    }

    public MSCPackageInfo getConfigPackage() {
        if (metaInfo != null) {
            return metaInfo.getConfigPackage();
        }

        return null;
    }

    public String getUseNetworkRes() {
        if (metaInfo != null) {
            MSCCacheFetchResult.NoCacheReason noCacheReason = metaInfo.getNoCacheReason();
            String useNetworkRes = "";
            if (noCacheReason == MSCCacheFetchResult.NoCacheReason.NO_CACHE) {
                useNetworkRes = "noCache";
            } else if (noCacheReason == MSCCacheFetchResult.NoCacheReason.TIME_OUT) {
                useNetworkRes = "timeout";
            } else if (noCacheReason == MSCCacheFetchResult.NoCacheReason.FORCE_UPDATE) {
                useNetworkRes = "forceUpdate";
            } else if (noCacheReason == MSCCacheFetchResult.NoCacheReason.NOT_MATCH_RULER) {
                useNetworkRes = "notMatchRuler";
            } else if (noCacheReason == MSCCacheFetchResult.NoCacheReason.VERSION_INVALID) {
                useNetworkRes = "versionInvalid";
            }

            return useNetworkRes;
        }

        return "";
    }

    /**
     * 元信息来自网络场景下，区分旧缓存过期和首次拉取场景
     */
    public boolean hasPreviousExpiredCache() {
        return metaInfo.hasPreviousExpiredCache();
    }

    public long getPreviousExpiredCacheSavedTime() {
        return metaInfo.getPreviousSavedTime();
    }
}
