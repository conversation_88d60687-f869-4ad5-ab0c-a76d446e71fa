package com.meituan.msc.modules.update.bean;

import android.support.annotation.IntDef;

import java.lang.annotation.Retention;
import java.lang.annotation.RetentionPolicy;
import java.util.Objects;

public class CheckUpdateParams {
    public String appId;
    public String checkUpdateUrl;

    /**
     * 数据获取方式
     */
    @Type
    private final int type;

    public CheckUpdateParams(String appId, @Type int type) {
        this.appId = appId;
        this.type = type;
    }

    @Type
    public int getType() {
        return type;
    }

    @Override
    public String toString() {
        return "CheckUpdateParams{" +
                "appId='" + appId + '\'' +
                ", checkUpdateUrl='" + checkUpdateUrl + '\'' +
                ", type=" + type +
                '}';
    }

    @Override
    public int hashCode() {
        return Objects.hash(appId, checkUpdateUrl, type);
    }


    @IntDef({Type.NETWORK, Type.CACHE, Type.CACHE_OR_NETWORK, Type.NETWORK_OR_CACHE, Type.S3_DEGRADE})
    @Retention(RetentionPolicy.SOURCE)
    public @interface Type {
        /**
         * 仅从网络获取
         */
        int NETWORK = 1;
        /**
         * 仅从缓存获取
         */
        int CACHE = 2;
        /**
         * 网络优先，失败后 从缓存获取
         */
        int NETWORK_OR_CACHE = 3;
        /**
         * 缓存优先，无缓存 从网络获取
         */
        int CACHE_OR_NETWORK = 4;

        /**
         * S3降级服务
         */
        int S3_DEGRADE = 5;
    }

    @Override
    public boolean equals(Object o) {
        if (this == o) return true;
        if (o == null || getClass() != o.getClass()) return false;
        CheckUpdateParams that = (CheckUpdateParams) o;
        return type == that.type && Objects.equals(appId, that.appId) && Objects.equals(checkUpdateUrl, that.checkUpdateUrl);
    }
}
