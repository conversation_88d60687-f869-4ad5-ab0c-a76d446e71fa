package com.meituan.msc.modules.update.bean;

import android.support.annotation.IntDef;
import android.support.annotation.NonNull;
import android.support.annotation.Nullable;
import android.text.TextUtils;

import com.meituan.android.mercury.msc.adaptor.bean.MSCPackageInfo;
import com.meituan.dio.easy.DioFile;
import com.meituan.met.mercury.load.bean.DDLoadPhaseData;
import com.meituan.met.mercury.load.core.DDResource;
import com.meituan.msc.common.utils.PathUtil;
import com.meituan.msc.modules.update.MSCAppModule;
import com.meituan.msc.modules.update.PackageLoadReporter;

import java.io.File;
import java.lang.annotation.Retention;
import java.lang.annotation.RetentionPolicy;

public class PackageInfoWrapper {

    /**
     * 包类型
     */
    @PackageType
    public final int packageType;
    /**
     * 包基础数据模型
     * 不含包资源数据（DDResource）
     */
    @Nullable
    private MSCPackageInfo packageInfo;
    /**
     * 包资源相关数据，包含md5、localPath等
     */
    private DDResource ddResource;
    /**
     * 包资源是否就绪
     */
    public volatile boolean isSourceReady;
    /**
     * 包资源是否被注入
     */
    public volatile boolean isPackageInjected;
    public String appId;
    public String publishId;

    private long downloadStartTimeInMs = -1;
    private long downloadEndTimeInMs = -1;
    private long serviceLoadedTimeInMs = -1;
    /**
     * 前置检查资源是否存在
     */
    private volatile boolean preCheckResourceExists;
    /**
     * 前置检查资源是否有效，md5比对
     */
    private volatile boolean preCheckLocalCacheValid;
    /**
     * 包资源否被删除过
     */
    public volatile boolean isPackageDeleted;

    /**
     * package类型
     */
    @IntDef({PACKAGE_TYPE_BASE, PACKAGE_TYPE_MAIN, PACKAGE_TYPE_SUB, PACKAGE_TYPE_INDEPENDENT_SUB, PACKAGE_TYPE_APP_CONFIG})
    @Retention(RetentionPolicy.SOURCE)
    public @interface PackageType {
    }

    public static final int PACKAGE_TYPE_BASE = 1;
    public static final int PACKAGE_TYPE_MAIN = 2;
    public static final int PACKAGE_TYPE_SUB = 3;
    public static final int PACKAGE_TYPE_INDEPENDENT_SUB = 4;
    public static final int PACKAGE_TYPE_APP_CONFIG = 5;

    public static final String PACKAGE_TYPE_STR_BASE = "base";
    public static final String PACKAGE_SERVICE_FILE = "app-service.js";
    public static final String PACKAGE_FRAMEWORK_SERVICE_FILE = "service.js";
    public static final String PACKAGE_PAGE_BOOTSTRAP = "page-bootstrap.js";
    public static final String PACKAGE_PAGE_FRAME = "pageframe.js";
    public static final String PACKAGE_APP_PAGE = "app-page.js";

    // 用于基础包下载，预热场景下没有元信息 无法收口到AppMetaInfoWrapper进行创建 目前收口到MSCAppModule中
    // TODO: 2022/9/8 tianbin 应该使用 default 限定符，暂不调整
    public PackageInfoWrapper() {
        this.packageType = PackageInfoWrapper.PACKAGE_TYPE_BASE;
    }

    PackageInfoWrapper(String appId, String publishId, @PackageType int packageType, @NonNull MSCPackageInfo packageInfo) {
        this.appId = appId;
        this.publishId = publishId;
        this.packageType = packageType;
        this.packageInfo = packageInfo;
    }

    public boolean isBasePackage() {
        return packageType == PACKAGE_TYPE_BASE;
    }

    public boolean isMainPackage() {
        return packageType == PACKAGE_TYPE_MAIN;
    }

    public DioFile getServiceFile() {
        return new DioFile(ddResource.getLocalPath(), isBasePackage() ?
                PACKAGE_FRAMEWORK_SERVICE_FILE : PACKAGE_SERVICE_FILE);
    }

    public String getPkgTypeString() {
        switch (packageType) {
            case PACKAGE_TYPE_SUB:
                return "sub";
            case PACKAGE_TYPE_MAIN:
                return "main";
            case PACKAGE_TYPE_BASE:
                return "base";
            case PACKAGE_TYPE_INDEPENDENT_SUB:
                return "indepsub";
        }
        return null;
    }

    public String getMd5() {
        // 优先取DDResource
        // 基础库预热时，有ddResource，没有packageInfo
        if (ddResource != null) {
            return ddResource.getMd5();
        }
        // 还未loadPackage时 DDResource为null，可以取packageInfo，来自元信息
        // 启动小程序流程
        return packageInfo == null ? "" : packageInfo.getDdd().getMd5();
    }

    public String getVersion() {
        if (ddResource != null) {
            return ddResource.getVersion();
        }
        if (packageInfo == null) {
            return "";
        }
        return packageInfo.getDdd().getBundleVersion();
    }

    /**
     * 包是否来自网络下载
     * 注意：isFromNet 和 isFromCache 不是互补的，还有一个未知状态
     * @return 如果是网络下载的，返回true；如果不是来自网络或者无法判断，则返回false
     */
    public boolean isFromNet() {
        return ddResource != null && ddResource.isFromNet();
    }

    /**
     * 包是否从缓存中读取的
     * 注意：isFromNet 和 isFromCache 不是互补的，还有一个未知状态
     * @return 如果包是从缓存中读取的，返回true；如果是来自网络或者无法判断，则返回false
     */
    public boolean isFromCache() {
        return ddResource != null && !ddResource.isFromNet();
    }

    /**
     * 当前包是否是在某个指定的时间点之前就下载好了
     * @return 如果 isFromCache 返回true，则直接返回true；如果下载时间戳未知，则返回false
     */
    public boolean isDownloadedBefore(long timeStamp) {
        return isFromCache() || downloadEndTimeInMs > 0 && downloadEndTimeInMs < timeStamp;
    }

    public boolean isServiceLoadedBefore(long timeStamp) {
        return serviceLoadedTimeInMs > 0 && serviceLoadedTimeInMs < timeStamp;
    }

    @PackageLoadReporter.LoadType
    public String getLoadType() {
        return isFromNet() ? PackageLoadReporter.LoadType.NETWORK : PackageLoadReporter.LoadType.LOCAL;
    }

    @Override
    public String toString() {
        return "PackageInfoWrapper{" +
                "packageType=" + packageType +
                ", packageInfo=" + packageInfo +
                ", ddResource=" + ddResource +
                ", isSourceReady=" + isSourceReady +
                ", isPackageInjected=" + isPackageInjected +
                ", appId='" + appId + '\'' +
                '}';
    }

    public String getSourcePackageDir() {
        return ddResource != null ? ddResource.getLocalPath() : null;
    }

    public String getRoot() {
        if (packageInfo == null) {
            return "";
        }
        // 主包/基础包 没有root字段
        if (packageType == PACKAGE_TYPE_MAIN || packageType == PACKAGE_TYPE_BASE) {
            return "";
        }
        return packageInfo.getRoot();
    }

    public boolean isEmpty() {
        return TextUtils.isEmpty(getMd5()) || ddResource == null || TextUtils.isEmpty(ddResource.getUrl());
    }

    @Nullable
    public MSCPackageInfo getPackageInfo() {
        return packageInfo;
    }

    public void setDDResource(@NonNull DDResource ddResource) {
        this.ddResource = ddResource;
    }

    public String getLocalPath() {
        return ddResource == null ? "" : ddResource.getLocalPath();
    }

    // 主包无相关名称，onPageStart需要用到名称，强制置为MAIN_PACKAGE_NAME
    // 主包：main_app 子包：eg:pack1
    public String getPackageName() {
        if (isMainPackage()) {
            return MSCAppModule.MAIN_PACKAGE_NAME;
        }
        return packageInfo == null ? "" : packageInfo.getName();
    }

    public DioFile getPageBootStrapFile() {
        String sourceDir = getSourcePackageDir();
        if (sourceDir != null) {
            return new DioFile(sourceDir, PACKAGE_PAGE_BOOTSTRAP);
        }
        return null;
    }

    public DioFile getSourcePackageDirFile(String fileName) {
        String sourceDir = getSourcePackageDir();
        if (sourceDir != null) {
            return new DioFile(sourceDir, fileName);
        }
        return null;
    }

    // 主包：appId 子包：app_1664435087169_0pages_1mrnPage
    public String getDDResourceName() {
        return ddResource == null ? "" : ddResource.getName();
    }

    public boolean isLocalCacheValid() {
        if (ddResource == null) {
            return false;
        }
        return ddResource.isLocalCacheValid();
    }

    public void setDownloadTimeInMs(long downloadStartTimeInMs, long downloadEndTimeInMs) {
        if (ddResource != null && ddResource.isFromNet()) {
            this.downloadEndTimeInMs = downloadEndTimeInMs;
            this.downloadStartTimeInMs = downloadStartTimeInMs;
        } else {
            this.downloadEndTimeInMs = -1;
            this.downloadStartTimeInMs = -1;
        }
    }

    public void markServiceLoadedTime() {
        this.serviceLoadedTimeInMs = System.currentTimeMillis();
    }

    public long getPackageSize() {
        return ddResource != null ? new File(ddResource.getLocalPath()).length() : -1L;
    }

    public long getDownloadEndTimeInMs() {
        return downloadEndTimeInMs;
    }

    public long getDownloadStartTimeInMs() {
        return downloadStartTimeInMs;
    }

    public boolean hasPagePath(String path) {
        return PathUtil.eliminateDuplicateSlashForPkgFile(path).startsWith(getRoot());
    }

    public DDResource getDDResource() {
        return ddResource;
    }

    public void setPackagePreCheckResult(boolean resourceExists, boolean localCacheValid) {
        this.preCheckResourceExists = resourceExists;
        this.preCheckLocalCacheValid = localCacheValid;
    }

    public boolean preCheckFileExist() {
        if (isBasePackage() || isMainPackage()) {
            return preCheckResourceExists;
        }
        return true;
    }

    public boolean preCheckIsMd5Same() {
        if (isBasePackage() || isMainPackage()) {
            return preCheckLocalCacheValid;
        }
        return true;
    }

    public DDLoadPhaseData getDDLoadPhaseData() {
        return ddResource != null ? ddResource.getLoadPhaseData() : null;
    }
}
