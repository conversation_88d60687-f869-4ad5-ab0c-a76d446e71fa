
package com.meituan.msc.modules.update.metainfo;

import android.support.annotation.NonNull;
import android.support.annotation.Nullable;
import android.support.annotation.VisibleForTesting;
import android.text.TextUtils;

import com.meituan.android.mercury.msc.adaptor.bean.MSCAppMetaInfo;
import com.meituan.android.mercury.msc.adaptor.bean.MSCMetaInfo;
import com.meituan.android.mercury.msc.adaptor.callback.MSCMetaInfoCallback;
import com.meituan.android.mercury.msc.adaptor.core.DDLoadMSCAdaptor;
import com.meituan.android.mercury.msc.adaptor.core.MSCLoadExeption;
import com.meituan.met.mercury.load.bean.ExtraParamsBean;
import com.meituan.met.mercury.load.bean.MSCAppIdPublishId;
import com.meituan.msc.common.config.MSCConfig;
import com.meituan.msc.common.constant.APPIDConstants;
import com.meituan.msc.modules.update.pkg.MSCLoadPackageScene;
import com.meituan.msc.common.executor.MSCExecutors;
import com.meituan.msc.common.support.java.util.concurrent.CompletableFuture;
import com.meituan.msc.common.support.java.util.function.Consumer;
import com.meituan.msc.common.support.java.util.function.Function;
import com.meituan.msc.common.utils.CollectionUtil;
import com.meituan.msc.common.utils.MPConcurrentHashMap;
import com.meituan.msc.common.utils.VersionUtil;
import com.meituan.msc.modules.apploader.events.AppLoadException;
import com.meituan.msc.modules.container.ContainerDebugLaunchData;
import com.meituan.msc.modules.container.ContainerStartState;
import com.meituan.msc.modules.engine.MSCHornRollbackConfig;
import com.meituan.msc.modules.engine.RecentMSCManager;
import com.meituan.msc.modules.preload.MSCHornPreloadConfig;
import com.meituan.msc.modules.preload.PackageDebugHelper;
import com.meituan.msc.modules.reporter.MSCLoadErrorConstants;
import com.meituan.msc.modules.reporter.MSCLog;
import com.meituan.msc.modules.reporter.MSCReporter;
import com.meituan.msc.modules.service.codecache.CodeCacheConfig;
import com.meituan.msc.modules.service.codecache.CodeCacheManager;
import com.meituan.msc.modules.storage.StorageManageUtil;
import com.meituan.msc.modules.update.MetaInfoMinVersionManager;
import com.meituan.msc.modules.update.PackageLoadReporter;
import com.meituan.msc.modules.update.PackagePreDownloadManager;
import com.meituan.msc.modules.update.PackagePreLoadReporter;
import com.meituan.msc.modules.update.PackageReportBean;
import com.meituan.msc.modules.update.bean.AppMetaInfoWrapper;
import com.meituan.msc.modules.update.bean.CheckUpdateParams;
import com.meituan.msc.modules.update.bean.PackageInfoWrapper;
import com.meituan.msc.modules.update.pkg.PackageLoadCallback;
import com.meituan.msc.modules.update.pkg.PackageLoadManager;
import com.meituan.msc.modules.update.pkg.PrefetchPackageManager;

import java.util.List;
import java.util.Map;
import java.util.concurrent.ConcurrentHashMap;
import java.util.concurrent.CopyOnWriteArrayList;

public class AppCheckUpdateManager implements IAppCheckUpdate<AppMetaInfoWrapper> {

    private static final String TAG = "AppCheckUpdateManager";
    private static volatile AppCheckUpdateManager mInstance;
    private long lastCheckUpdateTimeMill;
    private final Map<CheckUpdateParams, CompletableFuture<AppMetaInfoWrapper>> checkUpdateFutures = new ConcurrentHashMap<>();
    private final CopyOnWriteArrayList<OnAppVersionOfflineListener> appVersionOfflineListeners = new CopyOnWriteArrayList<>();
    private final MPConcurrentHashMap<String, String> bizMinVersionMap = new MPConcurrentHashMap<>();
    private List<ExtraParamsBean> pkgExtraParams;
    // 批量检查更新成功
    public static String sBatchCheckUpdateErrorMsg = "batch update not start";
    /**
     * 冷启场景为优化启动性能，会中断批量检查更新，在FP之后重新触发
     */
    public static volatile boolean sNeedBatchCheckUpdateAfterFP;

    private AppCheckUpdateManager() {
    }

    public static AppCheckUpdateManager getInstance() {
        if (mInstance == null) {
            synchronized (AppCheckUpdateManager.class) {
                if (mInstance == null) {
                    mInstance = new AppCheckUpdateManager();
                }
            }
        }
        return mInstance;
    }

    /**
     * 批量检查更新
     */
    public void batchCheckUpdate() {
        if (noOutOfTimeInterval()) {
            sBatchCheckUpdateErrorMsg = "batch update failed,not out of time interval";
            MSCLog.i(TAG, sBatchCheckUpdateErrorMsg);
            return;
        }
        AppCheckUpdateManager.sNeedBatchCheckUpdateAfterFP = false;
        lastCheckUpdateTimeMill = System.currentTimeMillis();

        List<String> recentAppList = RecentMSCManager.getRecentAppList();
        MSCLog.i(TAG, "batchCheckUpdate:", CollectionUtil.toString(recentAppList));

        PackagePreLoadReporter preLoadReporter = PackagePreLoadReporter.create();

        final long startLoadMetaInfoTs = System.currentTimeMillis();
        MSCMetaInfoCallback callback = new MSCMetaInfoCallback() {
            @Override
            public void onSuccess(@Nullable MSCMetaInfo metaInfo) {
                if (metaInfo == null) {
                    MSCLog.e(TAG, "batchCheckUpdate metaInfo is null");
                    preLoadReporter.onFetchMetaInfoFailed(null);
                    return;
                }
                preLoadReporter.reportBatchLoadMetaInfoDuration(MSCReporter.ReportValue.SUCCESS, System.currentTimeMillis() - startLoadMetaInfoTs);
                preLoadReporter.onFetchMetaInfoSuccess();
                sBatchCheckUpdateErrorMsg = "batch update succeed";

                // 预下载主包、配置包(如果有配置包)
                preDownloadPackages(metaInfo, preLoadReporter);
                // 发送小程序版本下线事件，如对应小程序引擎处于保活状态则需要销毁
                sendAppVersionsOfflineEvent(metaInfo);
            }

            @Override
            public void onFail(MSCLoadExeption e) {
                preLoadReporter.onFetchMetaInfoFailed(e);
                MSCLog.e(TAG, e, "batchCheckUpdate");
                sBatchCheckUpdateErrorMsg = "batch update failed:" + (e != null ? e.getMessage() : "");
            }
        };
        // 拦截最近使用小程序的批量检查更新，如果为优选，则使用DDD新接口更新
        if (!MSCHornRollbackConfig.readConfig().rollbackYouXuanPreDownloadChange && recentAppList.contains(APPIDConstants.YOU_XUAN)) {
            recentAppList.remove(APPIDConstants.YOU_XUAN);
            PackagePreDownloadManager.predownloadMainPackageByAppId(APPIDConstants.YOU_XUAN, true, MSCLoadPackageScene.LOAD_PACKAGE_TYPE_BATCH_UPDATE, null);
        }
        callBatchCheckUpdate(recentAppList, callback);
    }

    /**
     * 再次触发批量检查更新，冷启场景会中断容器初始化时触发的批量检查更新
     */
    public void batchCheckUpdateAgain() {
        if (!AppCheckUpdateManager.sNeedBatchCheckUpdateAfterFP) {
            return;
        }
        MSCExecutors.submit(new MSCExecutors.Serialized.SubmitRunnable(new Runnable() {
            @Override
            public void run() {
                AppCheckUpdateManager.getInstance().batchCheckUpdate();
            }
        }, MSCHornPreloadConfig.get().getConfig().batchCheckUpdateDelayAfterFP * 1000L));
    }

    /**
     * 本方法抽取目的为支持自动化测试AOP实现，禁止其他用途调用
     */
    public void callBatchCheckUpdate(List<String> recentAppList, MSCMetaInfoCallback callback) {
        if (MSCHornRollbackConfig.enablePkgExtraParam()) {
            Map<String, List<ExtraParamsBean>> recentAppExtraParamsMap = MetaFetchRulerManager.getInstance().getExtraParamsMap(recentAppList, pkgExtraParams);
            DDLoadMSCAdaptor.checkUpdateWithRecentlyUsedList(recentAppList, null, recentAppExtraParamsMap, callback);
        }  else {
            DDLoadMSCAdaptor.checkUpdateWithRecentlyUsedList(recentAppList, pkgExtraParams, null, callback);
        }
    }

    private boolean noOutOfTimeInterval() {
        return System.currentTimeMillis() - lastCheckUpdateTimeMill <= MSCConfig.getBatchCheckUpdateTimeIntervalMillis();
    }

    private void sendAppVersionsOfflineEvent(MSCMetaInfo metaInfo) {
        List<MSCAppIdPublishId> appVersionsToDelete = metaInfo.getMscAppVersionsToDelete();
        if (appVersionsToDelete == null || appVersionsToDelete.isEmpty()) {
            MSCLog.e(TAG, "checkUpdateWithRecentlyUsedList appVersionsToDelete is empty");
            return;
        }

        for (OnAppVersionOfflineListener listener : appVersionOfflineListeners) {
            MSCLog.d(TAG, "sendAppVersionsOfflineEventTo:", listener);
            listener.onOffline(appVersionsToDelete);
        }
    }

    private void preDownloadPackages(MSCMetaInfo metaInfo, PackagePreLoadReporter preLoadReporter) {
        List<MSCAppMetaInfo> mscApps = metaInfo.getMscApps();
        if (mscApps == null || mscApps.isEmpty()) {
            MSCLog.i(TAG, "batchCheckUpdate mscApps is empty");
            return;
        }

        for (MSCAppMetaInfo info : mscApps) {
            if (info == null || TextUtils.isEmpty(info.getAppId())) {
                continue;
            }

            if (ContainerStartState.instance.isContainerLaunching()) {
                sNeedBatchCheckUpdateAfterFP = true;
                sBatchCheckUpdateErrorMsg = "pre download cancel,has page launching";
                MSCLog.i(TAG, sBatchCheckUpdateErrorMsg);
                break;
            }

            // 统一存储管控预下载
            if (!StorageManageUtil.isPreDownloadAllowed(info.getAppId())) {
                String errInfo = "MSC prefetch is disabled by storage management";
                MSCLog.w(TAG, errInfo,info.getAppId());
                continue;
            }
            AppMetaInfoWrapper appMetaInfoWrapper = new AppMetaInfoWrapper(info);
            preDownLoadPackage(info.getAppId(), info.getVersion(),
                    appMetaInfoWrapper.createMainPackageWrapper(),
                    appMetaInfoWrapper.createConfigPackageWrapper(),
                    preLoadReporter, null);
        }
    }

    public void preDownLoadPackage(String appId, String appVersion,
                                   PackageInfoWrapper mainPackageInfo,
                                   PackageInfoWrapper configPackageInfo,
                                   PackagePreLoadReporter preLoadReporter,
                                   PackageLoadCallback<PackageInfoWrapper> callback) {
        realPreDownLoadPackage(appId, appVersion, mainPackageInfo, configPackageInfo, preLoadReporter, callback);
    }

    private void realPreDownLoadPackage(String appId, String appVersion,
                                        PackageInfoWrapper mainPackageInfo,
                                        PackageInfoWrapper configPackageInfo,
                                        PackagePreLoadReporter preLoadReporter,
                                        PackageLoadCallback<PackageInfoWrapper> callback) {
        // 自动化测试Log 勿删
        MSCLog.i(TAG, "[MSC][PreDownload]start:", mainPackageInfo.getMd5());
        //TODO: set PerfEventRecorder
        final long startLoadPackageTime = System.currentTimeMillis();
        PackageLoadManager.getInstance().loadPackageWithInfo(null, mainPackageInfo, false, PackageLoadManager.CheckScene.PREDOWNLOAD, MSCLoadPackageScene.LOAD_PACKAGE_TYPE_BATCH_UPDATE,
                new PackageLoadCallback<PackageInfoWrapper>() {
                    @Override
                    public void onSuccess(@NonNull PackageInfoWrapper data) {
                        MSCLog.i(TAG, "[MSC][PreDownload]end:", mainPackageInfo.getMd5());
                        // 这里预下载完成，生成CodeCache文件
                        if (!CodeCacheConfig.INSTANCE.isBlockedToCreateCodeCacheWhenPreDownload(mainPackageInfo.appId)) {
                            CodeCacheManager.getInstance().createCodeCacheAsync(appId, appVersion, data);
                        }
                        // 用于上报预下载成功率维度
                        PrefetchPackageManager.cachePreDownloadPackages(appId, data);
                        PackageReportBean packageReportBean = new PackageReportBean.Builder()
                                .setMscAppId(appId)
                                .setMscAppVersion(appVersion)
                                .setPkgName(data.getPackageName())
                                .setSourceFrom(PackagePreLoadReporter.SOURCE_FROM_TYPE_PREDOWNLOAD)
                                .setDdLoadPhaseData(data.getDDLoadPhaseData())
                                .setLoadType(data.isFromNet() ? PackageLoadReporter.LoadType.NETWORK : PackageLoadReporter.LoadType.LOCAL)
                                .setPkgType(mainPackageInfo.getPkgTypeString())
                                .build();
                        preLoadReporter.reportLoadPackageSuccessDuration(packageReportBean, System.currentTimeMillis() - startLoadPackageTime);
                        preLoadReporter.onLoadPackageSuccess(packageReportBean);

                        PackageLoadManager.getInstance().checkDDResourceMd5AndReport("preDownload", mainPackageInfo);
                        if (callback != null) {
                            callback.onSuccess(data);
                        }
                    }

                    @Override
                    public void onFail(String errMsg, AppLoadException error) {
                        MSCLog.e(TAG, String.format("preDownLoadPackage failed,%s,%s,%s", appId, mainPackageInfo.toString(), errMsg));
                        preLoadReporter.onLoadPackageFailed(new PackageReportBean.Builder()
                                .setMscAppId(appId)
                                .setMscAppVersion(appVersion)
                                .setSourceFrom(PackagePreLoadReporter.SOURCE_FROM_TYPE_PREDOWNLOAD)
                                .setDdLoadPhaseData(error != null ? error.getDDPhaseData(): null)
                                .setPkgType(mainPackageInfo.getPkgTypeString())
                                .build(), error);
                        if (callback != null) {
                            callback.onFail(errMsg, error);
                        }
                    }
                });
        if (MSCHornRollbackConfig.enablePreDownloadConfigPkg()) {
            if (configPackageInfo != null) {
                PackageLoadManager.getInstance().loadPackageWithInfo(null, configPackageInfo, false, PackageLoadManager.CheckScene.PREDOWNLOAD, MSCLoadPackageScene.LOAD_PACKAGE_TYPE_BATCH_UPDATE, null);
            }
        }
    }


    public void checkUpdateBeforeRuntimeInit(String appId, @NonNull ContainerDebugLaunchData debugLaunchData) {
        if (!MSCConfig.enableCheckUpdateBeforeRuntimeInit()) {
            return;
        }

        String checkUpdateUrl = PackageDebugHelper.instance.getCheckUpdateUrlOfIDEPreview(appId, debugLaunchData);
        CheckUpdateParams params = new CheckUpdateParams(appId,
                debugLaunchData.needForceCheckUpdate() ? CheckUpdateParams.Type.NETWORK : CheckUpdateParams.Type.CACHE_OR_NETWORK);
        if (!TextUtils.isEmpty(checkUpdateUrl)) {
            params.checkUpdateUrl = checkUpdateUrl;
        }

        final CompletableFuture<AppMetaInfoWrapper> future = new CompletableFuture<>();
        checkUpdateFutures.put(params, future);

        MSCLog.i(TAG, "checkupdate before runtime init " + params.appId + " from " + params.checkUpdateUrl + " type: " + params.getType());
        CheckUpdateCallback<AppMetaInfoWrapper> callback = new CheckUpdateCallback<AppMetaInfoWrapper>() {
            @Override
            public void onSuccess(@NonNull AppMetaInfoWrapper data) {
                future.complete(data);
                data.checkUpdateUrl = checkUpdateUrl;
            }

            @Override
            public void onFail(String errMsg, AppLoadException error) {
                future.completeExceptionally(error);
                checkUpdateFutures.remove(params);
            }
        };

        checkUpdateInner(params, callback);

    }

    /**
     * 是否有正在执行的checkUpdate请求
     *
     * @param params 检查更新参数
     * @return result
     */
    public boolean isCheckUpdateInProgress(@NonNull CheckUpdateParams params) {
        return checkUpdateFutures.get(params) != null;
    }

    /**
     * 单包检查更新
     *
     * @param params 检查更新参数
     * @param cb     单包获取回调
     */
    @Override
    public void checkUpdate(@NonNull CheckUpdateParams params, @NonNull CheckUpdateCallback<AppMetaInfoWrapper> cb) {
        // 如果已经有正在执行的checkUpdate请求，则不重复执行。
        CompletableFuture<AppMetaInfoWrapper> pendingFuture = checkUpdateFutures.get(params);
        if (pendingFuture != null) {
            // 有正在执行的checkUpdate, 不再重复调用，直接回调
            if (cb != null) {
                pendingFuture.thenAccept(new Consumer<AppMetaInfoWrapper>() {
                    @Override
                    public void accept(AppMetaInfoWrapper metInfoWrapper) {
                        cb.onSuccess(metInfoWrapper);
                        checkUpdateFutures.remove(params);
                    }
                }).exceptionally(new Function<Throwable, Void>() {
                    @Override
                    public Void apply(Throwable throwable) {
                        if (MSCHornRollbackConfig.readConfig().rollbackLoadErrorReportFix) {
                            cb.onFail(throwable.getMessage(), new AppLoadException(throwable));
                        } else {
                            Throwable cause = throwable.getCause();
                            AppLoadException exception;
                            if (cause instanceof AppLoadException) {
                                exception = ((AppLoadException) cause);
                            } else {
                                exception = new AppLoadException(MSCLoadErrorConstants.ERROR_FETCH_METAINFO_FAILED, throwable == null ? "checkUpdate failed" : throwable.getMessage());
                            }
                            cb.onFail(exception.getMessage(), exception);
                        }
                        checkUpdateFutures.remove(params);
                        return null;
                    }
                });
            }
            return;
        }

        // 如果没有正在执行的checkupdate, 执行一次
        MSCLog.i(TAG, "checkupdate for " + params.appId + " from " + params.checkUpdateUrl + " type: " + params.getType());

        CheckUpdateCallback<AppMetaInfoWrapper> callback = new CheckUpdateCallback<AppMetaInfoWrapper>() {
            @Override
            public void onSuccess(@NonNull AppMetaInfoWrapper data) {
                cb.onSuccess(data);
            }

            @Override
            public void onFail(String errMsg, AppLoadException error) {
                cb.onFail(errMsg, error);
            }
        };

        checkUpdateInner(params, callback);
    }


    private void checkUpdateInner(@NonNull CheckUpdateParams params, CheckUpdateCallback<AppMetaInfoWrapper> cb) {
        MSCLog.d(TAG, "checkUpdate:", params);
        String appId = params.appId;
        switch (params.getType()) {
            case CheckUpdateParams.Type.NETWORK:
                fetchMetaInfoByNetwork(appId, params.checkUpdateUrl, cb);
                break;
            case CheckUpdateParams.Type.CACHE:
                getMetaInfoFromCache(appId, cb);
                break;
            case CheckUpdateParams.Type.CACHE_OR_NETWORK:
                // 回滚页面找不到兜底方案 或者 宿主App非美团 ，使用最低版本号策略
                CheckUpdateCallback<AppMetaInfoWrapper> realCallback = restrictBizMinVersion(params, cb);
                getMetaInfoFromCacheOrNetwork(appId, params.checkUpdateUrl, realCallback);
                break;
            case CheckUpdateParams.Type.NETWORK_OR_CACHE:
                getMetaInfoFromNetworkOrCache(appId, params.checkUpdateUrl, cb);
                break;
            case CheckUpdateParams.Type.S3_DEGRADE:
                fetchMetaInfoByS3Degrade(appId, cb);
                break;
            default:
                MSCLog.e(TAG, "error type:" + params.getType());
                break;
        }
    }

    private CheckUpdateCallback<AppMetaInfoWrapper> restrictBizMinVersion(CheckUpdateParams params,
                                                                          CheckUpdateCallback<AppMetaInfoWrapper> cb) {
        return new CheckUpdateCallback<AppMetaInfoWrapper>() {
                    @Override
                    public void onSuccess(@NonNull AppMetaInfoWrapper data) {
                        // 网络
                        if (!data.isFromCache()) {
                            cb.onSuccess(data);
                            return;
                        }

                        // 缓存
                        if (isLargerThanMinVersion(data)) {
                            cb.onSuccess(data);
                        } else {
                            retryCheckUpdateByNetwork(data);
                        }
                    }

                    private void retryCheckUpdateByNetwork(AppMetaInfoWrapper cacheData) {
                        MSCLog.i(TAG, "retryCheckUpdateByNetwork", params.toString());
                        fetchMetaInfoByNetwork(params.appId, params.checkUpdateUrl, new CheckUpdateCallback<AppMetaInfoWrapper>() {
                            @Override
                            public void onSuccess(@NonNull AppMetaInfoWrapper data) {
                                data.isFetchedByMinVersionLimit = true;
                                cb.onSuccess(data);
                            }

                            @Override
                            public void onFail(String errMsg, AppLoadException error) {
                                // 地图业务要求强制更新失败后返回失败
                                if (TextUtils.equals(params.appId, APPIDConstants.TIAN_XING_JIAN)) {
                                    cb.onFail(errMsg, error);
                                } else {
                                    // 其他业务强制更新失败后也返回失败，对齐iOS；
                                    if (MSCHornRollbackConfig.get().getConfig().isRollbackMinVersionCheckChange) {
                                        cb.onSuccess(cacheData);
                                    } else {
                                        cb.onFail(errMsg, error);
                                    }
                                }
                            }
                        });
                    }

                    @Override
                    public void onFail(String errMsg, AppLoadException error) {
                        cb.onFail(errMsg, error);
                    }
                };
    }

    public boolean isLargerThanMinVersion(@NonNull AppMetaInfoWrapper data) {
        boolean hasInnerMinBuildId = MetaInfoMinVersionManager.getInstance().hasInnerMinBuildId(data.getAppId());
        if (hasInnerMinBuildId) {
            return MetaInfoMinVersionManager.getInstance().isMatchMinBuildId(data.getAppId(), data.getBuildId(), TextUtils.equals(data.getLoadType(), PackageLoadReporter.LoadType.INNER));
        } else {
            String appId = data.getAppId();
            String minVersion = this.bizMinVersionMap.get(appId);
            if (TextUtils.isEmpty(minVersion)) {
                return true;
            }
            return VersionUtil.compare(data.getVersion(), minVersion) >= 0;
        }
    }


    /**
     * 优先请求网络，获取失败后读取缓存
     *
     * @param appId    appId
     * @param callback callback
     */
    private void getMetaInfoFromNetworkOrCache(String appId, String metaInfoTestUrl, CheckUpdateCallback<AppMetaInfoWrapper> callback) {
        MSCLog.i(TAG, "getMetaInfoFromNetworkOrCache checkUpdateWithAppId:" + appId);
        List<ExtraParamsBean> paramsBeanList = getExtraParamsForAppId(appId);
        DDLoadMSCAdaptor.checkUpdateWithAppId(appId, metaInfoTestUrl, paramsBeanList, new MSCMetaInfoCallback() {
            @Override
            public void onSuccess(@Nullable MSCMetaInfo metaInfo) {
                getMetaInfoSuccess(metaInfo, callback);
            }

            @Override
            public void onFail(MSCLoadExeption e) {
                MSCLog.e(TAG, e, "getMetaInfoFromNetworkOrCache onFailed");
                MSCLog.i(TAG, e, "getMetaInfoFromNetworkOrCache getCacheMetaWithAppId:", appId);
                DDLoadMSCAdaptor.getCacheMetaWithAppId(appId, new MSCMetaInfoCallback() {
                    @Override
                    public void onSuccess(@Nullable MSCMetaInfo metaInfo) {
                        getMetaInfoSuccess(metaInfo, callback);
                    }

                    @Override
                    public void onFail(MSCLoadExeption e) {
                        getMetaInfoFailed(e, callback);
                    }
                });
            }
        });
    }

    private void fetchMetaInfoByS3Degrade(String appId, CheckUpdateCallback<AppMetaInfoWrapper> callback) {
        MSCLog.i(TAG, "MSCMeteInfoFromS3 fetchMetaInfoByS3Degrade appId:" + appId);
        DDLoadMSCAdaptor.getMetaInfoFromS3(appId, new MSCMetaInfoCallback() {
            @Override
            public void onSuccess(@Nullable MSCMetaInfo metaInfo) {
                getMetaInfoSuccess(metaInfo, callback);
            }

            @Override
            public void onFail(MSCLoadExeption e) {
                MSCLog.i(TAG, "MSCMeteInfoFromS3 onFail " + e.getMessage());
                getMetaInfoFailed(e, callback);
            }
        });
    }

    /**
     * 优先读取缓存，无缓存｜缓存过期 请求网络获取
     *
     * @param appId    appId
     * @param callback callback
     */
    private void getMetaInfoFromCacheOrNetwork(String appId, String metaInfoTestUrl, CheckUpdateCallback<AppMetaInfoWrapper> callback) {
        long cacheTimeout = getCacheTimeout(appId);
        MSCLog.i(TAG, "getMetaInfoFromCacheOrNetwork getMetaWithAppId:" + appId + ",cacheTimeOut:" + cacheTimeout);
        List<ExtraParamsBean> paramsBeanList = getExtraParamsForAppId(appId);
        DDLoadMSCAdaptor.getMetaWithAppId(appId, cacheTimeout, metaInfoTestUrl, paramsBeanList, new MSCMetaInfoCallback() {
            @Override
            public void onSuccess(@Nullable MSCMetaInfo metaInfo) {
                getMetaInfoSuccess(metaInfo, callback);
            }

            @Override
            public void onFail(MSCLoadExeption e) {
                getMetaInfoFailed(e, callback);
            }
        });
    }

    /**
     * 仅读取缓存，忽略缓存时间
     *
     * @param appId    appId
     * @param callback callback
     */
    private void getMetaInfoFromCache(String appId, CheckUpdateCallback<AppMetaInfoWrapper> callback) {
        MSCLog.i(TAG, "getMetaInfoFromCache getCacheMetaWithAppId:", appId);
        DDLoadMSCAdaptor.getCacheMetaWithAppId(appId, new MSCMetaInfoCallback() {
            @Override
            public void onSuccess(@Nullable MSCMetaInfo metaInfo) {
                getMetaInfoSuccess(metaInfo, callback);
            }

            @Override
            public void onFail(MSCLoadExeption e) {
                getMetaInfoFailed(e, callback);
            }
        });
    }

    /**
     * 请求网络获取
     *
     * @param appId    appId
     * @param callback callback
     */
    private void fetchMetaInfoByNetwork(String appId, String metaInfoTestUrl, CheckUpdateCallback<AppMetaInfoWrapper> callback) {
        MSCLog.i(TAG, "fetchMetaInfoByNetwork checkUpdateWithAppId:", appId, ",metaInfoTestUrl:", metaInfoTestUrl);
        List<ExtraParamsBean> paramsBeanList = getExtraParamsForAppId(appId);
        DDLoadMSCAdaptor.checkUpdateWithAppId(appId, metaInfoTestUrl, paramsBeanList, new MSCMetaInfoCallback() {
            @Override
            public void onSuccess(@Nullable MSCMetaInfo metaInfo) {
                getMetaInfoSuccess(metaInfo, callback);
            }

            @Override
            public void onFail(MSCLoadExeption e) {
                getMetaInfoFailed(e, callback);
            }
        });
    }

    // 自动化测试Mock数据使用 勿删
    public long getCacheTimeout(String appId) {
        return PackageExpirationTimeManager.getInstance().getPackageExpirationTime(appId);
    }

    private void getMetaInfoFailed(MSCLoadExeption e, CheckUpdateCallback<AppMetaInfoWrapper> callback) {
        String errorMessage = (e == null) ? "getMetaInfoFailed" : (e.getMessage() == null ? "getMetaInfoFailed" : e.getMessage());
        MSCLog.e(TAG, e, errorMessage);
        AppLoadException exception = new AppLoadException(getFetchMetaInfoErrorCode(e), errorMessage, e);
        callback.onFail(errorMessage, exception);
    }

    private int getFetchMetaInfoErrorCode(@Nullable MSCLoadExeption e) {
        if (e == null) {
            return MSCLoadErrorConstants.ERROR_FETCH_METAINFO_FAILED_EXCEPTION_EMPTY;
        }
        return MSCLoadErrorConstants.ERROR_FETCH_METAINFO_FAILED + e.getErrCode() % 1000;
    }

    private void getMetaInfoSuccess(MSCMetaInfo metaInfo, CheckUpdateCallback<AppMetaInfoWrapper> callback) {
        if (metaInfo == null) {
            String errorMsg = "checkUpdate metaInfo is null";
            MSCLog.e(TAG, errorMsg);
            callback.onFail(errorMsg, new AppLoadException(MSCLoadErrorConstants.ERROR_FETCH_METAINFO_RESULT_EMPTY, errorMsg));
            return;
        }

        List<MSCAppMetaInfo> mscApps = metaInfo.getMscApps();
        if (mscApps != null && !mscApps.isEmpty()) {
            AppMetaInfoWrapper infoWrapper = new AppMetaInfoWrapper(mscApps.get(0));
            if (infoWrapper.isFromCache()) {
                MSCLog.i(TAG, "getMetaInfoSuccess from cache");
            }

            MSCLog.i(TAG, "checkUpdate success");
            processMetaInfoSuccess(callback, infoWrapper);
        } else {
            String errorMsg = "checkUpdate mscApps is empty";
            MSCLog.e(TAG, errorMsg);
            callback.onFail(errorMsg, new AppLoadException(MSCLoadErrorConstants.ERROR_FETCH_METAINFO_RESULT_EMPTY, errorMsg));
        }
    }

    /**
     * 该方法会被AOP，请不要删除或者改动
     */
    private void processMetaInfoSuccess(CheckUpdateCallback<AppMetaInfoWrapper> callback, AppMetaInfoWrapper infoWrapper){
        callback.onSuccess(infoWrapper);
    }

    /**
     * 添加小程序版本下载监听器
     *
     * @param listener 监听器
     */
    public void addAppVersionOfflineListener(OnAppVersionOfflineListener listener) {
        appVersionOfflineListeners.add(listener);
    }

    /**
     * 移除小程序版本下线监听器
     *
     * @param listener 监听器
     */
    public void removeAppVersionOfflineListener(OnAppVersionOfflineListener listener) {
        appVersionOfflineListeners.remove(listener);
    }

    /**
     * 清除所有小程序版本下线监听器
     */
    public void clearAppVersionOfflineListeners() {
        appVersionOfflineListeners.clear();
    }

    public interface OnAppVersionOfflineListener {
        void onOffline(List<MSCAppIdPublishId> appIdVersions);
    }

    public void addBizMinVersion(String appId, String minVersion) {
        bizMinVersionMap.put(appId, minVersion);
    }

    @VisibleForTesting
    public void clearBizMinVersionMap() {
        bizMinVersionMap.clear();
    }

    @Nullable
    public List<ExtraParamsBean> getPkgExtraParams() {
        return pkgExtraParams;
    }

    public void setPkgExtraParams(List<ExtraParamsBean> bundleExtraParams) {
        MSCLog.i(TAG, "setPkgExtraParams bundleExtraParams:" + (bundleExtraParams != null ? bundleExtraParams.toString() : "null"));
        this.pkgExtraParams = bundleExtraParams;
    }

    @VisibleForTesting
    public Map<String, List<ExtraParamsBean>> getAppsExtraParams(List<String> appIds) {
       return MetaFetchRulerManager.getInstance().getExtraParamsMap(appIds, pkgExtraParams);
    }

    private List<ExtraParamsBean> getExtraParamsForAppId(String appId) {
        List<ExtraParamsBean> paramsBeanList = null;
        boolean enablePkgExtraParam = MSCHornRollbackConfig.enablePkgExtraParam();
        if (enablePkgExtraParam) {
            paramsBeanList = MetaFetchRulerManager.getInstance().getExtraParamsList(appId, pkgExtraParams);
        } else {
            paramsBeanList = pkgExtraParams;
        }
        DDLoadMSCAdaptor.setEnableSingleParamToAppExtraParam(enablePkgExtraParam);

        return paramsBeanList;
    }
}
