package com.meituan.msc.modules.update.metainfo;

import android.support.annotation.NonNull;
import android.support.annotation.Nullable;
import android.text.TextUtils;

import com.meituan.android.mercury.msc.adaptor.core.MSCLoadExeption;
import com.meituan.msc.common.config.MSCConfig;
import com.meituan.msc.common.constant.APPIDConstants;
import com.meituan.msc.modules.update.pkg.MSCLoadPackageScene;
import com.meituan.msc.common.executor.MSCExecutors;
import com.meituan.msc.modules.apploader.events.AppLoadException;
import com.meituan.msc.modules.engine.MSCHornRollbackConfig;
import com.meituan.msc.modules.engine.MSCRuntime;
import com.meituan.msc.modules.manager.MSCModule;
import com.meituan.msc.modules.manager.ModuleName;
import com.meituan.msc.modules.manager.UpdateManager;
import com.meituan.msc.modules.preload.MSCHornPreloadConfig;
import com.meituan.msc.modules.reporter.MSCLog;
import com.meituan.msc.modules.reporter.MSCReporter;
import com.meituan.msc.modules.update.MSCAppModule;
import com.meituan.msc.modules.update.PackageLoadReporter;
import com.meituan.msc.modules.update.PackagePreDownloadManager;
import com.meituan.msc.modules.update.PackagePreLoadReporter;
import com.meituan.msc.modules.update.PackageReportBean;
import com.meituan.msc.modules.update.bean.AppMetaInfoWrapper;
import com.meituan.msc.modules.update.bean.CheckUpdateParams;
import com.meituan.msc.modules.update.bean.PackageInfoWrapper;
import com.meituan.msc.modules.update.pkg.PackageLoadCallback;
import com.meituan.msc.modules.update.pkg.PackageLoadManager;

import java.util.ArrayList;
import java.util.List;

@ModuleName(name = "BackgroundCheckUpdateModule")
public class BackgroundCheckUpdateModule extends MSCModule {

    private static final String TAG = "BackgroundCheckUpdateManager";
    private volatile long lastCheckUpdateTimestamp;

    public void checkUpdate(MSCRuntime runtime) {
        // 后台检查更新delay 1s
        checkUpdate(runtime, 1000);
    }

    private void checkUpdate(MSCRuntime runtime,long delayTimeMillis) {
        MSCExecutors.Serialized.schedule(new Runnable() {
            @Override
            public void run() {
                checkUpdateInner(runtime);
            }
        }, delayTimeMillis);
    }

    private void checkUpdateInner(MSCRuntime runtime) {
        if (!isCheckUpdateOutOfTimeInterval()) {
            MSCLog.i(TAG, "checkUpdate not out of time interval", runtime.getAppId(), getRuntimeHashCodeStr(runtime));
            return;
        }
        MSCLog.i(TAG, "background checkUpdate", runtime.getAppId(), getRuntimeHashCodeStr(runtime));
        recordLastCheckUpdateTimestamp();
        PackageLoadReporter packageLoadReporter = PackageLoadReporter.create(runtime);
        CheckUpdateParams checkUpdateParams = new CheckUpdateParams(runtime.getAppId(), CheckUpdateParams.Type.NETWORK);
        UpdateManager updateManager = runtime.getModule(UpdateManager.class);
        MSCLog.i(TAG, "background checkUpdate, updateManager", updateManager);
        final boolean isCheckUpdateInProgress = AppCheckUpdateManager.getInstance().isCheckUpdateInProgress(checkUpdateParams);
        final long startCheckUpdateTs = System.currentTimeMillis();
        if (!MSCHornRollbackConfig.readConfig().rollbackYouXuanPreDownloadChange && TextUtils.equals(APPIDConstants.YOU_XUAN, runtime.getAppId())) {
            PackagePreDownloadManager.predownloadMainPackageByAppId(runtime.getAppId(), false, MSCLoadPackageScene.LOAD_PACKAGE_TYPE_BACKGROUND_UPDATE, new PackagePreDownloadManager.PreDownloadCallback() {
                @Override
                public void onSuccess(AppMetaInfoWrapper metaInfoWrapper, PackageInfoWrapper mainPackageWrapper) {
                    MSCAppModule mscAppModule = runtime.getMSCAppModule();
                    if (mscAppModule == null) {
                        MSCLog.i(TAG, "msc app exit:", metaInfoWrapper.getAppId(), getRuntimeHashCodeStr(runtime));
                        return;
                    }
                    if (mscAppModule.isSameVersion(metaInfoWrapper)) {
                        MSCLog.i(TAG, "background checkUpdate is same version", runtime.getAppId(), getRuntimeHashCodeStr(runtime));
                        if (updateManager != null) {
                            updateManager.setStatus(UpdateManager.Status.STATUS_APP_NOT_HAS_UPDATE);
                        } else {
                            MSCLog.i(TAG, "updateManager is null", runtime.getAppId(), getRuntimeHashCodeStr(runtime));
                        }
                        return;
                    }
                    MSCLog.i(TAG, "background update success", runtime.getAppId(), getRuntimeHashCodeStr(runtime));
                    mainPackageWrapper.isSourceReady = true;
                    if (updateManager != null) {
                        updateManager.setStatus(UpdateManager.Status.STATUS_APP_HAS_UPDATE);
                        updateManager.setStatus(UpdateManager.Status.STATUS_APP_DOWNLOAD_SUCCESS);
                    } else {
                        MSCLog.i(TAG, "updateManager is null", runtime.getAppId(), getRuntimeHashCodeStr(runtime));
                    }
                    PackageLoadManager.getInstance().checkDDResourceMd5AndReport("backgroundCheckUpdate", mainPackageWrapper);
                }

                @Override
                public void onFail(int errorStep, @Nullable MSCLoadExeption exception) {
                    MSCLog.e(TAG, exception, "background update failed", runtime.getAppId(), getRuntimeHashCodeStr(runtime));
                    // 后台检查更新失败，重置时间窗口计时
                    lastCheckUpdateTimestamp = 0;
                    if (updateManager != null) {
                        updateManager.setStatus(UpdateManager.Status.STATUS_APP_NOT_HAS_UPDATE);
                        updateManager.setStatus(UpdateManager.Status.STATUS_APP_DOWNLOAD_FAIL);
                    } else {
                        MSCLog.i(TAG, "updateManager is null", runtime.getAppId(), getRuntimeHashCodeStr(runtime));
                    }
                }
            });
        } else {
            checkUpdateMainPackage(runtime, checkUpdateParams, isCheckUpdateInProgress, packageLoadReporter, startCheckUpdateTs, updateManager);
        }
    }

    private static String getRuntimeHashCodeStr(MSCRuntime runtime) {
        return "MSCRuntime@" + Integer.toHexString(runtime.hashCode());
    }

    private void checkUpdateMainPackage(MSCRuntime runtime, CheckUpdateParams checkUpdateParams, boolean isCheckUpdateInProgress, PackageLoadReporter packageLoadReporter, long startCheckUpdateTs, UpdateManager updateManager) {
        AppCheckUpdateManager.getInstance().checkUpdate(checkUpdateParams, new CheckUpdateCallback<AppMetaInfoWrapper>() {
            @Override
            public void onSuccess(@NonNull AppMetaInfoWrapper metaInfo) {
                MSCAppModule mscAppModule = runtime.getMSCAppModule();
                if (mscAppModule == null) {
                    MSCLog.i(TAG, "msc app exit:", metaInfo.getAppId(), runtime);
                    return;
                }
                if (!isCheckUpdateInProgress) {
                    packageLoadReporter.reportLoadMetaInfoDuration(MSCReporter.ReportValue.SUCCESS,
                            System.currentTimeMillis() - startCheckUpdateTs,
                            metaInfo.getLoadType(),
                            PackageLoadReporter.Source.PREFETCH, metaInfo.getUseNetworkRes());
                }
                packageLoadReporter.onFetchMetaInfoSuccess(metaInfo.getLoadType(),
                        PackageLoadReporter.Source.PREFETCH, metaInfo.getUseNetworkRes());
                if (mscAppModule.isSameVersion(metaInfo)) {
                    MSCLog.i(TAG, "background checkUpdate is same version", runtime);
                    if (updateManager != null) {
                        updateManager.setStatus(UpdateManager.Status.STATUS_APP_NOT_HAS_UPDATE);
                    }
                    return;
                }
                if (updateManager != null) {
                    updateManager.setStatus(UpdateManager.Status.STATUS_APP_HAS_UPDATE);
                }
                List<PackageInfoWrapper> needLoadPackages = new ArrayList<>();
                needLoadPackages.add(metaInfo.createMainPackageWrapper());
                for (PackageInfoWrapper infoWrapper : needLoadPackages) {
                    MSCLog.i(TAG, "background loadPackageWithInfo:", infoWrapper, runtime);
                    PackageLoadCallback<PackageInfoWrapper> callback = createPackageLoadCallback(infoWrapper,
                            packageLoadReporter, updateManager);
                    PackageLoadManager.getInstance().loadPackageWithInfo(runtime.getPerfEventRecorder(),
                            infoWrapper, false, PackageLoadManager.CheckScene.BACKGROUND_UPDATE, MSCLoadPackageScene.LOAD_PACKAGE_TYPE_BACKGROUND_UPDATE, callback);
                }
                if (MSCHornRollbackConfig.enablePreDownloadConfigPkg()) {
                    if (metaInfo.getConfigPackage() != null) {
                        PackageLoadManager.getInstance().loadPackageWithInfo(runtime.getPerfEventRecorder(),
                                metaInfo.createConfigPackageWrapper(), false, PackageLoadManager.CheckScene.BACKGROUND_UPDATE, MSCLoadPackageScene.LOAD_PACKAGE_TYPE_BACKGROUND_UPDATE, null);
                    }
                }
            }

            @Override
            public void onFail(String errMsg, AppLoadException error) {
                MSCLog.w(TAG, error, "background checkUpdate", errMsg, runtime);
                // 后台检查更新失败，重置时间窗口计时
                lastCheckUpdateTimestamp = 0;

                if (updateManager != null) {
                    updateManager.setStatus(UpdateManager.Status.STATUS_APP_NOT_HAS_UPDATE);
                }
                packageLoadReporter.onFetchMetaInfoFail(null,
                        PackageLoadReporter.Source.PREFETCH,
                        error);
            }
        });
    }

    private PackageLoadCallback<PackageInfoWrapper> createPackageLoadCallback(PackageInfoWrapper infoWrapper,
                                                                              PackageLoadReporter packageLoadReporter,
                                                                              UpdateManager updateManager) {
        return new PackageLoadCallback<PackageInfoWrapper>() {
            final long startLoadPackageTime = System.currentTimeMillis();

            @Override
            public void onSuccess(@NonNull PackageInfoWrapper packageInfo) {
                String sourceFrom = PackagePreLoadReporter.SOURCE_FROM_TYPE_PREDOWNLOAD;
                PackageReportBean packageReportBean = new PackageReportBean.Builder()
                        .setLoadType(packageInfo.isFromNet() ?
                                PackageLoadReporter.LoadType.NETWORK : PackageLoadReporter.LoadType.LOCAL)
                        .setPkgName(infoWrapper.getPackageName())
                        .setPkgType(infoWrapper.getPkgTypeString())
                        .setDdLoadPhaseData(packageInfo.getDDLoadPhaseData())
                        .setSourceFrom(sourceFrom)
                        .build();

                packageLoadReporter.reportLoadPackageSuccessDuration(packageReportBean, System.currentTimeMillis() - startLoadPackageTime);
                packageLoadReporter.onLoadPackageSuccess(packageReportBean);

                packageInfo.isSourceReady = true;
                if (updateManager != null) {
                    updateManager.setStatus(UpdateManager.Status.STATUS_APP_DOWNLOAD_SUCCESS);
                }

                PackageLoadManager.getInstance().checkDDResourceMd5AndReport("backgroundCheckUpdate", packageInfo);
            }

            @Override
            public void onFail(String errMsg, AppLoadException error) {
                MSCLog.w(TAG, error, "background checkUpdate", errMsg);
                // 后台检查更新失败，重置时间窗口计时
                lastCheckUpdateTimestamp = 0;
                String sourceFrom = PackagePreLoadReporter.SOURCE_FROM_TYPE_PREDOWNLOAD;
                packageLoadReporter.onLoadPackageFailed(
                        new PackageReportBean.Builder()
                                .setPkgName(infoWrapper.getPackageName())
                                .setPkgType(infoWrapper.getPkgTypeString())
                                .setDdLoadPhaseData(error != null ? error.getDDPhaseData(): null)
                                .setSourceFrom(sourceFrom)
                                .build(),
                        error);
                if (updateManager != null) {
                    updateManager.setStatus(UpdateManager.Status.STATUS_APP_DOWNLOAD_FAIL);
                }
            }
        };
    }

    public void recordLastCheckUpdateTimestamp() {
        lastCheckUpdateTimestamp = System.currentTimeMillis();
        MSCLog.d(TAG, "recordLastCheckUpdateTimestamp:", lastCheckUpdateTimestamp);
    }

    public boolean isCheckUpdateOutOfTimeInterval() {
        long currentTimeMillis = System.currentTimeMillis();
        MSCLog.d(TAG, "isCheckUpdateOutOfTimeInterval:", currentTimeMillis);
        return currentTimeMillis - lastCheckUpdateTimestamp > MSCConfig.getAliveLaunchBackgroundCheckUpdateTimeInterval();
    }

    /**
     * 收到FP指令后，延迟5s执行后台检查更新，对齐MMP热启触发检查更新策略
     * PS：5min内执行过后台检查更新，不再重复检查
     *
     * @param runtime runtime
     */
    public void backgroundCheckUpdateAfterFP(MSCRuntime runtime) {
        if (MSCHornPreloadConfig.get().getConfig().disableCheckUpdateAfterFP) {
            MSCLog.i(TAG, "disableCheckUpdateAfterFP");
            return;
        }
        if (runtime != null) {
            checkUpdate(runtime, MSCConfig.getBackgroundUpdateDelayTimeMillis());
        }
    }
}
