package com.meituan.msc.modules.update.metainfo;

import android.content.SharedPreferences;
import android.text.TextUtils;

import com.google.gson.Gson;
import com.google.gson.reflect.TypeToken;
import com.meituan.met.mercury.load.bean.ExtraParamsBean;
import com.meituan.msc.extern.MSCEnvHelper;
import com.meituan.msc.modules.reporter.MSCLog;

import java.util.ArrayList;
import java.util.HashMap;
import java.util.List;
import java.util.Map;
import java.util.concurrent.ConcurrentHashMap;
import java.util.concurrent.CopyOnWriteArrayList;

public class MetaFetchRulerManager {
    private static final String TAG = "MetaFetchRulerManager";

    private static volatile MetaFetchRulerManager sInstance;

    private Map<String, Map<String, String>> mPkgExtraParamPersistMap = new ConcurrentHashMap<>();
    private Map<String, Boolean> mAppLoadMap = new ConcurrentHashMap<>();
    private Object appLoadlock = new Object();

    private volatile boolean needSaveExtraParamsAfterInit = false;
    private List<String> pendingSaveExtraParamList= new CopyOnWriteArrayList<>();

    private MetaFetchRulerManager() {
    }

    public static MetaFetchRulerManager getInstance() {
        if (sInstance == null) {
            synchronized (MetaFetchRulerManager.class) {
                if (sInstance == null) {
                    sInstance = new MetaFetchRulerManager();
                }
            }
        }
        return sInstance;
    }

    public Map<String, String> getPkgExtraParamsPersist(String appId) {
        if (TextUtils.isEmpty(appId)) {
            return null;
        }

        loadPkgExtraParamsPersist(appId);
        return mPkgExtraParamPersistMap.get(appId);
    }

    public List<ExtraParamsBean> getExtraParamsList(String appId, List<ExtraParamsBean> extraParamsBeanList) {
        if (TextUtils.isEmpty(appId)) {
            return extraParamsBeanList;
        }

        Map<String, String> appExtraMap = getPkgExtraParamsPersist(appId);
        if (appExtraMap == null || appExtraMap.isEmpty()) {
            return extraParamsBeanList;
        }

        List<ExtraParamsBean> finalExtraParamsList = new ArrayList<>();
        for (Map.Entry<String, String> entry : appExtraMap.entrySet()) {
            if (entry == null) {
                continue;
            }

            ExtraParamsBean extraParamsBean = new ExtraParamsBean(entry.getKey(), entry.getValue());
            finalExtraParamsList.add(extraParamsBean);
        }

        if (extraParamsBeanList != null && !extraParamsBeanList.isEmpty()) {
            for (ExtraParamsBean extraParamsBean : extraParamsBeanList) {
                if (extraParamsBean == null || appExtraMap.containsKey(extraParamsBean.getKey())) {
                    continue;
                }

                finalExtraParamsList.add(extraParamsBean);
            }
        }

        return finalExtraParamsList;
    }

    public Map<String, List<ExtraParamsBean>> getExtraParamsMap(List<String> appIdList, List<ExtraParamsBean> extraParamsBeanList) {
        if (appIdList == null || appIdList.isEmpty()) {
            return null;
        }

        Map<String, List<ExtraParamsBean>> extraParamsBeanMap = new HashMap<>();
        for (String appId : appIdList) {
            List<ExtraParamsBean> appExtraParamsList = getExtraParamsList(appId, extraParamsBeanList);
            if (appExtraParamsList != null && !appExtraParamsList.isEmpty()) {
                extraParamsBeanMap.put(appId, appExtraParamsList);
            }
        }
        MSCLog.i(TAG, "ExtraParamsBean: ", extraParamsBeanList != null ? extraParamsBeanList.toString() : "null");
        MSCLog.i(TAG, "getExtraParamsMap: ", extraParamsBeanMap.toString());
        return  extraParamsBeanMap;
    }

    public boolean addPkgExtraParamPersist(String appId, String key, String value) {
        if (TextUtils.isEmpty(appId) || TextUtils.isEmpty(key) || TextUtils.isEmpty(value)) {
            return false;
        }
        loadPkgExtraParamsPersist(appId);
        Map<String, String> appExtraMap = null;
        if (mPkgExtraParamPersistMap.containsKey(appId)) {
            appExtraMap = mPkgExtraParamPersistMap.get(appId);
        }

        if (appExtraMap == null) {
            appExtraMap = new ConcurrentHashMap<>();
            mPkgExtraParamPersistMap.put(appId, appExtraMap);
        }
        appExtraMap.put(key, value);

        //更新本地存储
        savePkgExtraParamsPersist(appId);
        return true;
    }

    public void removePkgExtraParamPersist(String appId, String key) {
        if (TextUtils.isEmpty(appId) || TextUtils.isEmpty(key)) {
            return;
        }
        loadPkgExtraParamsPersist(appId);
        Map<String, String> appExtraMap = null;
        if (mPkgExtraParamPersistMap.containsKey(appId)) {
            appExtraMap = mPkgExtraParamPersistMap.get(appId);
        }
        if (appExtraMap == null) {
            return;
        }

        appExtraMap.remove(key);
        //更新本地存储
        savePkgExtraParamsPersist(appId);
    }

    public void clearPkgExtraParamsPersist(String appId) {
        if (TextUtils.isEmpty(appId)) {
            return;
        }

        mPkgExtraParamPersistMap.remove(appId);
        //更新本地存储
        savePkgExtraParamsPersist(appId);
    }

    public static SharedPreferences getPersistSP() {
        return MSCEnvHelper.getSharedPreferences("mscPkgExtraParams");
    }

    private void loadPkgExtraParamsPersist(String appId) {
        if (TextUtils.isEmpty(appId)) {
            return;
        }
        if (mAppLoadMap.containsKey(appId)) {
            return;
        }

        if (!MSCEnvHelper.isInited()) {
            return;
        }

        //一个小程序仅加载一次
        synchronized (appLoadlock) {
            if (mAppLoadMap.containsKey(appId)) {
                return;
            }
            String persistStr = getPersistSP().getString(appId, "");
            mAppLoadMap.put(appId, true);
            if (TextUtils.isEmpty(persistStr)) {
                return;
            }
            try {
                Map<String, String> appExtraMap = new Gson().fromJson(persistStr, new TypeToken<Map<String, String>>() {
                }.getType());
                if (appExtraMap != null && !appExtraMap.isEmpty()) {
                    mPkgExtraParamPersistMap.put(appId, new ConcurrentHashMap<>(appExtraMap));
                }
            } catch (Exception e) {
                MSCLog.e(TAG, e);
            }

        }
    }

    private void savePkgExtraParamsPersist(String appId) {
        if (TextUtils.isEmpty(appId)) {
            return;
        }

        if (!MSCEnvHelper.isInited()) {
            needSaveExtraParamsAfterInit = true;
            if (!pendingSaveExtraParamList.contains(appId)) {
                pendingSaveExtraParamList.add(appId);
            }
            return;
        }

        Map<String, String> ruler = mPkgExtraParamPersistMap.containsKey(appId) ? mPkgExtraParamPersistMap.get(appId): null;
        if (ruler == null) { //整体移除
            getPersistSP().edit().remove(appId).apply();
        } else { //存储最新值
            try {
                String persistStr = new Gson().toJson(ruler, new TypeToken<Map<String, String>>() {
                }.getType());
                getPersistSP().edit().putString(appId, persistStr).apply();
            } catch (Exception e) {
                MSCLog.e(TAG, e);
            }
        }
    }

    public void savaPkgExtraParamsPersistAfterInit() {
        if (needSaveExtraParamsAfterInit) {
            if (pendingSaveExtraParamList == null || pendingSaveExtraParamList.isEmpty()) {
                return;
            }

            for (String appId : pendingSaveExtraParamList) {
                savePkgExtraParamsPersist(appId);
            }
        }
    }
}
