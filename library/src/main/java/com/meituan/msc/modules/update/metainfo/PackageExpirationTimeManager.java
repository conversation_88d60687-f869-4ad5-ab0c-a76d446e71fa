package com.meituan.msc.modules.update.metainfo;

import android.content.SharedPreferences;

import com.meituan.msc.extern.MSCEnvHelper;

import org.json.JSONException;
import org.json.JSONObject;

import java.util.Iterator;
import java.util.concurrent.TimeUnit;

public class PackageExpirationTimeManager {
    public static final long DEFAULT_TIMEOUT_SECOND = TimeUnit.DAYS.toSeconds(2);
    private static PackageExpirationTimeManager instance;
    /**
     * 业务从native设置的包有效期缓存
     */
    private final SharedPreferences nativeSp;
    /**
     * horn设置的包有效期缓存
     */
    private final SharedPreferences hornSp;

    private PackageExpirationTimeManager() {
        nativeSp = MSCEnvHelper.getSharedPreferences("msc_package_expiration_time");
        hornSp = MSCEnvHelper.getSharedPreferences("msc_horn_config");
    }

    public static PackageExpirationTimeManager getInstance() {
        if (instance == null) {
            instance = new PackageExpirationTimeManager();
        }
        return instance;
    }

    public void setPackageExpirationTimeFromNative(String appId, long seconds) {
        SharedPreferences.Editor editor = nativeSp.edit();
        editor.putLong(appId, seconds);
        editor.apply();
    }

    public long getPackageExpirationTime(String appId) {
        if (nativeSp.contains(appId)) {
            return nativeSp.getLong(appId, DEFAULT_TIMEOUT_SECOND);
        } else if (hornSp.contains(appId)) {
            return hornSp.getLong(appId, DEFAULT_TIMEOUT_SECOND);
        } else {
            return DEFAULT_TIMEOUT_SECOND;
        }
    }

    /**
     * 以分钟为单位
     * 配置信息为jsonObejct {"gh_f18c2f54b122":5,"gh_f18c2f54b12e":6}
     */
    public void updatePackageExpirationTimeFromHorn(String result) {
        try {
            JSONObject jsonObject = new JSONObject(result);
            Iterator<String> keys = jsonObject.keys();
            SharedPreferences.Editor editor = hornSp.edit();
            editor.clear();
            while (keys.hasNext()) {
                String key = keys.next();
                long timeout = jsonObject.optLong(key);
                editor.putLong(key, timeout * 60);
            }
            editor.apply();
        } catch (JSONException ignore) {
        }
    }

}
