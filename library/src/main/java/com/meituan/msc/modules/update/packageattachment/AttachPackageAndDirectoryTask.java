package com.meituan.msc.modules.update.packageattachment;

import com.meituan.msc.modules.preload.executor.Task;
import com.meituan.msc.modules.preload.executor.TaskExecuteContext;

class AttachPackageAndDirectoryTask extends Task {

    private final PackageAttachmentManager packageAttachmentManager;
    private final PackageAttachmentManager.RecordEntry recordEntry;

    AttachPackageAndDirectoryTask(PackageAttachmentManager packageAttachmentManager, PackageAttachmentManager.RecordEntry recordEntry) {
        super(recordEntry.packageFile);
        this.packageAttachmentManager = packageAttachmentManager;
        this.recordEntry = recordEntry;
    }

    @Override
    protected void execute(TaskExecuteContext taskExecuteContext) {
        packageAttachmentManager.attachPackageAndDirectory(recordEntry);
    }
}
