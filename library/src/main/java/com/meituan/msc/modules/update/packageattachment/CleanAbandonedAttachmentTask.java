package com.meituan.msc.modules.update.packageattachment;

import com.meituan.msc.modules.preload.executor.Task;
import com.meituan.msc.modules.preload.executor.TaskExecuteContext;
import com.meituan.msc.modules.reporter.MSCLog;

import java.io.IOException;

public class CleanAbandonedAttachmentTask extends Task {

    private final PackageAttachmentManager packageAttachmentManager;

    CleanAbandonedAttachmentTask(PackageAttachmentManager packageAttachmentManager) {
        super("CleanAbandonedAttachmentTask");
        this.packageAttachmentManager = packageAttachmentManager;
    }

    @Override
    protected void execute(TaskExecuteContext taskExecuteContext) {
        try {
            packageAttachmentManager.cleanAbandonedAttachment();
        } catch (IOException e) {
            MSCLog.e(PackageAttachmentManager.TAG, e);
        }
    }
}

