package com.meituan.msc.modules.update.packageattachment;

import android.os.Build;
import android.system.Os;
import android.system.StructStat;

import com.meituan.android.cipstorage.CIPSStrategy;
import com.meituan.msc.common.utils.FileUtil;
import com.meituan.msc.modules.engine.MSCHornRollbackConfig;
import com.meituan.msc.modules.reporter.MSCLog;
import com.meituan.msc.modules.reporter.MSCReporter;
import com.meituan.msc.modules.storage.StorageManageUtil;

import java.io.File;
import java.util.ArrayList;
import java.util.Arrays;
import java.util.Collections;
import java.util.Comparator;
import java.util.Iterator;
import java.util.List;
import java.util.Set;
import java.util.TreeSet;
import java.util.concurrent.locks.ReentrantLock;

/**
 * LRU 清理指定目录
 * 1、根据目录下文件的最后修改时间，按照从小到大排序
 * 2、删除文件，直到目录下文件大小小于指定大小
 * 3、如果目录下文件大小小于指定大小，不做任何操作
 * <p>
 * Created by letty on 2023/4/18.
 **/
public class LruCleaner {
    private static ReentrantLock sFileLock = new ReentrantLock(true);

    public static final String TAG = "LRUCleaner";
    private static final long DAY = 24 * 60 * 60 * 1000;
    private static final long MB = 1024 * 1024;

    /**
     * 清理指定目录
     *
     * @param path  目录路径
     * @param limit 目录大小 单位M
     */
    public static CIPSStrategy.LRUCleanData clean(String path, int lruStrategy, int limit, int duration, boolean onlyRemoveDir) {
        return clean(path, lruStrategy, limit, duration, onlyRemoveDir, null);
    }

    public static CIPSStrategy.LRUCleanData clean(String path, int lruStrategy, int limit, int duration, boolean onlyRemoveDir, ILruAction action) {
        if (path == null || path.length() == 0) {
            return null;
        }
        return clean(new File(path), lruStrategy, limit, duration, onlyRemoveDir, action);
    }

    public static CIPSStrategy.LRUCleanData clean(String path, int limit, boolean onlyRemoveDir, ILruAction action) {
        return clean(path, -1, limit, 0, onlyRemoveDir, action);
    }

    public static CIPSStrategy.LRUCleanData clean(String path, int limit, boolean onlyRemoveDir) {
        return clean(path, -1, limit, 0, onlyRemoveDir);
    }

    /**
     * 清理指定目录
     *
     * @param fileDir 目录路径
     * @param limit   目录大小 单位M
     */
    public static CIPSStrategy.LRUCleanData clean(File fileDir, int lruStrategy,
                                                  int limit,
                                                  int duration,
                                                  boolean onlyRemoveDir) {
        return clean(fileDir, lruStrategy, limit, duration, onlyRemoveDir, null);
    }

    public static CIPSStrategy.LRUCleanData clean(File fileDir, int lruStrategy,
                                                  int limit,
                                                  int duration,
                                                  boolean onlyRemoveDir,
                                                  ILruAction lruAction) {
        if (fileDir == null || !fileDir.exists() || fileDir.isFile()) {
            return null;
        }
        String path = fileDir.getPath();
        long limitSize = limit * MB;
        MSCLog.i(TAG, "clean:", path, "limitSize(M):", limit,
                "lruStrategy:", lruStrategy, "duration(D):", duration);
        File[] files = fileDir.listFiles();
        if (files == null || files.length == 0) {
            return null;
        }
        Set<File> orderedFiles = getOrderedFiles(files);
        List<CIPSStrategy.SingleFileCleanData> removedFiles = new ArrayList<>();

        long sumSize = 0L;
        long deleteSize = 0L;
        Iterator iterator = orderedFiles.iterator();
        boolean getLock = false;
        try {
            getLock = sFileLock.tryLock();
            if (!getLock) {
                // 若文件正在使用，则不进行删除。
                MSCLog.i(TAG, "clean when in use. skip...");
                return null;
            }
            while (iterator.hasNext()) {
                File file = (File) iterator.next();
                long size = getFileSize(file);
                long sum = sumSize + size;
                boolean isDeleteMatched = isLruStrategyDeleteMatched(lruStrategy, limitSize, sum, duration, file);
                if (lruAction != null) {
                    // 支持自定义匹配条件
                    isDeleteMatched = isDeleteMatched || lruAction.canDelete(file);
                }
                if (isDeleteMatched && (!onlyRemoveDir || file.isDirectory())) {
                    boolean result = FileUtil.deleteFile(file);
                    MSCLog.i(TAG, "delete file", result, file.getAbsolutePath(), size);
                    removedFiles.add(StorageManageUtil.generateCleanData(file.getName(), size));
                    deleteSize += size;
                } else {
                    if (!file.isDirectory()) {
                        MSCLog.i(TAG, "keep file", file.getAbsolutePath(), size);
                    } else {
                        MSCLog.d(TAG, "keep file", file.getAbsolutePath(), size);
                    }
                    sumSize = sum;
                }
            }
        } finally {
            if (getLock) {
                sFileLock.unlock();
            }
        }
        MSCLog.i(TAG, "clean finished.", path, "limitSize:", limitSize, "curSize:", sumSize, "delSize:", deleteSize);
        reportCodeCacheUsage(path, limitSize, sumSize, deleteSize);
        return StorageManageUtil.getLRUCleanData(deleteSize, limit, duration, removedFiles, null);
    }

    interface ILruAction {

        /**
         * 判断是否符合删除条件
         */
        boolean canDelete(File file);
    }

    /**
     * 当前文件是否需要删除
     * <p>
     * 按照时间 LRU ,需要根据文件特性判断
     * 按照文件总大小LRU，根据文件总大小判断
     *
     * @param file
     * @param lruStrategy
     * @param duration
     * @return
     */
    private static boolean isLruStrategyDeleteMatched(int lruStrategy, long limitSize, long sum, int duration, File file) {
        if (isLruDurationStrategy(lruStrategy)) {
            return duration * DAY <= (System.currentTimeMillis() - getFileLastModifiedTime(file) * 1000);
        }
        return sum > limitSize;
    }

    public static boolean isLruDurationStrategy(int lruStrategy) {
        return CIPSStrategy.CLEAN_STRATEGY_DURATION_LRU == lruStrategy;
    }

    private static final String REPORT_LRU_CLEANER = "msc.lru.cleaner";
    private static final String FIELD_PATH = "path";

    private static final String FIELD_LIMIT_SIZE = "limitSize";
    private static final String FIELD_CUR_SIZE = "curSize";
    private static final String FIELD_DEL_SIZE = "delSize";

    private static void reportCodeCacheUsage(String path, long limitSize, long curSize, long delSize) {
        CIPSStrategy.LRUConfig lruConfig = StorageManageUtil.getLRUConfigWithFramework();
        new MSCReporter().once(REPORT_LRU_CLEANER)
                .tag(FIELD_PATH, path)
                .tag(FIELD_LIMIT_SIZE, limitSize)
                .tag(FIELD_CUR_SIZE, curSize)
                .tag(FIELD_DEL_SIZE, delSize)
                // 存储优化相关维度
                .tag("storageUserType", StorageManageUtil.getStorageType())
                .tag("cleanStrategy", lruConfig.strategy)
                .tag("currentLRUSize", lruConfig.maxSize)
                .tag("currentLRUDuration", lruConfig.duration)
                .tag("autoCleanABTestKey", StorageManageUtil.getStorageTestStrategy())
                .sendRealTime();
    }

    /**
     * 获取文件最后修改时间
     *
     * @param file 文件
     * @return 最后修改时间，返回atime\ctime\mtime中最大的 单位s
     */
    public static long getFileLastModifiedTime(File file) {
        if (Build.VERSION.SDK_INT >= 21) {
            try {
                StructStat stat = Os.stat(file.getPath());
                return Math.max(Math.max(stat.st_atime, stat.st_mtime), stat.st_ctime);
            } catch (Throwable var4) {
                //ignore
            }
        }
        return file.lastModified() / 1000L;
    }

    /**
     * 获取文件/文件夹大小
     *
     * @param file
     * @return
     */
    public static long getFileSize(File file) {
        if (file != null && file.exists()) {
            if (file.isFile()) {
                return file.length();
            } else {
                String[] files = file.list();
                if (files != null && files.length >= 1) {
                    long size = 0L;
                    int length = files.length;

                    for (int i = 0; i < length; ++i) {
                        String child = files[i];
                        size += getFileSize(new File(file, child));
                    }
                    return size;
                } else {
                    return 0L;
                }
            }
        } else {
            return 0L;
        }
    }

    /**
     * 文件新->老排序
     *
     * @param files
     * @return
     */
    private static Set<File> getOrderedFiles(final File[] files) {
        if (files != null && files.length >= 1) {
            TreeSet<File> orderFiles = new TreeSet(new Comparator<File>() {
                public int compare(File o1, File o2) {
                    int dis = (int) (getFileLastModifiedTime(o2) - getFileLastModifiedTime(o1));
                    return dis == 0 ? o2.compareTo(o1) : dis;
                }
            });
            orderFiles.addAll(Arrays.asList(files));
            return orderFiles;
        } else {
            return Collections.emptySet();
        }
    }

    public static ReentrantLock getFileDelLock() {
        return sFileLock;
    }

}
