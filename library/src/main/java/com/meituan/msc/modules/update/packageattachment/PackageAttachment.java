package com.meituan.msc.modules.update.packageattachment;

import android.text.TextUtils;

import com.meituan.msc.modules.service.codecache.CodeCacheConfig;
import com.meituan.msc.modules.update.bean.PackageInfoWrapper;

import java.io.File;

/**
 * 用于管理和Package同生命周期的文件
 */
public class PackageAttachment {

    private PackageAttachmentManager packageAttachmentManager;
    private PackageInfoWrapper packageInfo;

    PackageAttachment(PackageAttachmentManager packageAttachmentManager, PackageInfoWrapper packageInfo) {
        this.packageAttachmentManager = packageAttachmentManager;
        this.packageInfo = packageInfo;
    }

    private String getMd5OfPackage() {
        String md5OfPackage = packageInfo.getMd5();
        if (TextUtils.isEmpty(md5OfPackage)) {
            md5OfPackage = String.valueOf(packageInfo.getLocalPath().hashCode());
        }
        return md5OfPackage;
    }

    public File getAttachFile(String attachFilePath) {
        return new File(getDirectory(), attachFilePath);
    }

    public File getDirectory() {
        return new File(packageAttachmentManager.getAttachmentDirectory(), getMd5OfPackage());
    }

    public void prepareDirectory() {
        File directory = getDirectory();
        // 写记录，创建目录
        if (!directory.exists()) {
            directory.mkdirs();
            if (CodeCacheConfig.INSTANCE.isAttachDirectoryAsync()) {
                packageAttachmentManager.attachPackageAndDirectoryAsync(packageInfo, directory);
            } else {
                packageAttachmentManager.attachPackageAndDirectory(packageInfo, directory);
            }
        }
    }
}
