package com.meituan.msc.modules.update.packageattachment;

import android.content.Context;

import com.meituan.android.cipstorage.CIPSStrategy;
import com.meituan.dio.utils.IOUtil;
import com.meituan.msc.common.utils.CIPStorageFileUtil;
import com.meituan.msc.common.utils.FileSizeUtil;
import com.meituan.msc.common.utils.FileUtil;
import com.meituan.msc.common.utils.MSCSyncTask;
import com.meituan.msc.modules.preload.executor.ScheduledTaskExecutor;
import com.meituan.msc.modules.preload.executor.TaskExecutor;
import com.meituan.msc.modules.reporter.MSCLog;
import com.meituan.msc.modules.service.codecache.CodeCacheConfig;
import com.meituan.msc.modules.storage.StorageManageUtil;
import com.meituan.msc.modules.update.bean.PackageInfoWrapper;

import java.io.BufferedOutputStream;
import java.io.File;
import java.io.FileInputStream;
import java.io.FileOutputStream;
import java.io.IOException;
import java.io.OutputStream;
import java.util.ArrayList;
import java.util.Collections;
import java.util.HashSet;
import java.util.Iterator;
import java.util.List;
import java.util.Objects;
import java.util.Set;

/**
 * 用于管理和Package同生命周期的文件
 */
public class PackageAttachmentManager {
    public static final String TAG = "PackageAttachmentManager";

    private static final String PACKAGE_ATTACHMENT_DIRECTORY_NAME = "PackageAttachment";

    private static volatile PackageAttachmentManager sInstance;

    private static final float CODE_CACHE_TO_JS_FILE_SIZE_RADIO = 2;

    public static PackageAttachmentManager createInstance(Context context) {
        if (context == null) {
            throw new IllegalArgumentException("Invalid context argument");
        }
        if (sInstance == null) {
            synchronized (PackageAttachmentManager.class) {
                if (sInstance == null) {
                    sInstance = new PackageAttachmentManager(context);
                }
            }
        }
        return sInstance;
    }

    public static synchronized PackageAttachmentManager getInstance() {
        if (sInstance == null) {
            throw new IllegalStateException("createInstance() needs to be called before getInstance()");
        }
        return sInstance;
    }

    private final ScheduledTaskExecutor mTaskExecutor;
    private final Context mContext;
    private File attachmentDirectory;
    private File recordFile;
    private final Object recordFileLock = new Object();

    private PackageAttachmentManager(Context mContext) {
        this.mContext = mContext;
        mTaskExecutor = new ScheduledTaskExecutor("PackageAttachmentManager");
    }

    public PackageAttachment getAttachment(PackageInfoWrapper packageInfo) {
        return new PackageAttachment(this, packageInfo);
    }

    File getRecordFile() {
        if (recordFile == null) {
            recordFile = new File(getAttachmentDirectory(), "record.txt");
        }
        return recordFile;
    }

    public File getAttachmentDirectory() {
        if (attachmentDirectory == null) {
            attachmentDirectory = CIPStorageFileUtil.getFilesDir(mContext, PACKAGE_ATTACHMENT_DIRECTORY_NAME);
        }
        return attachmentDirectory;
    }

    void attachPackageAndDirectoryAsync(PackageInfoWrapper packageInfo, File directory) {
        mTaskExecutor.addTask(new AttachPackageAndDirectoryTask(this,
                new RecordEntry(packageInfo.getLocalPath(), directory.getAbsolutePath())));
    }

    void attachPackageAndDirectory(PackageInfoWrapper packageInfo, File directory) {
        attachPackageAndDirectory(new RecordEntry(packageInfo.getLocalPath(), directory.getAbsolutePath()));
    }

    void attachPackageAndDirectory(RecordEntry recordEntry) {
        if (recordEntry == null) {
            return;
        }
        // FIXME by chendacai: 5/10/22 多进程场景下需要适配
        synchronized (recordFileLock) {
            File recordFile = getRecordFile();
            FileOutputStream fos = null;
            try {
                fos = new FileOutputStream(recordFile, true);
                fos.write(recordEntry.toEntryString().getBytes());
            } catch (IOException e) {
                MSCLog.e(TAG, e);
            } finally {
                IOUtil.closeQuietly(fos);
            }
            MSCLog.i(TAG, "Attach attachment directory, packageFile: ", recordEntry.packageFile, ", directory: ", recordEntry.attachmentDirectory);
        }
    }

    public TaskExecutor getTaskExecutor() {
        return mTaskExecutor;
    }


    /**
     * 清理所有附件
     */
    public CIPSStrategy.LRUCleanData cleanAllAttachmentSync() {
        // 这里用CodeCache的开关来控制附件的清理工作
        if (!CodeCacheConfig.INSTANCE.isEnableCodeCache()) {
            return null;
        }
        MSCLog.i(TAG, "cleanAllAttachmentSync");
        return new MSCSyncTask<CIPSStrategy.LRUCleanData>() {
            @Override
            public CIPSStrategy.LRUCleanData run() {
                return LruCleaner.clean(getAttachmentDirectory().getPath(), 0, true);
            }
        }.executeInExecutor(mTaskExecutor.getExecutor());
    }

    /**
     * 清理所有过期的附件
     */
    public void cleanAbandonedAttachmentAsync() {
        // 这里用CodeCache的开关来控制附件的清理工作
        if (!CodeCacheConfig.INSTANCE.isEnableCodeCache()) {
            return;
        }
        MSCLog.i(TAG, "cleanAbandonedAttachmentAsync");
        mTaskExecutor.addTask(new CleanAbandonedAttachmentTask(this));
    }

    void cleanAbandonedAttachment() throws IOException {
        synchronized (recordFileLock) {
            // 读取整个文件，然后遍历，处理，并写入文件
            List<RecordEntry> recordEntries = readAllRecordEntry();
            boolean hasAbandonedAttachment = false;
            Iterator<RecordEntry> iterator = recordEntries.iterator();
            while (iterator.hasNext()) {
                RecordEntry recordEntry = iterator.next();
                if (!new File(recordEntry.packageFile).exists()) {
                    // 删除目录中的所有数据
                    FileUtil.deleteFile(recordEntry.attachmentDirectory);
                    iterator.remove();
                    MSCLog.i(TAG, "Remove abandoned attachment, packageFile: ", recordEntry.packageFile, "attachmentDirectory: ", recordEntry.attachmentDirectory);
                    hasAbandonedAttachment = true;
                }
            }
            // 写记录到文件中
            if (hasAbandonedAttachment) {
                if (CodeCacheConfig.INSTANCE.isWriteRecordInTemporary()) {
                    writeAllRecordEntryInTemporary(recordEntries);
                } else {
                    writeAllRecordEntry(recordEntries);
                }
            }
        }
        if (CodeCacheConfig.INSTANCE.isEnableLru()) {
//            LruCleaner.clean(getAttachmentDirectory().getPath(), StorageManageUtil.getLRUMaxSize(), true);
            // CodeCache LRU清理增加自定义逻辑
            LruCleaner.clean(getAttachmentDirectory().getPath(), StorageManageUtil.getLRUMaxSize(), true, (LruCleaner.ILruAction) file -> {
                if (CodeCacheConfig.INSTANCE.getMinJSFileSize() <= 0) {
                    return false;
                }
                // 1. 是否为CodeCache文件：path为CodeCache目录，可以根据路径做判断
                // 2. 对应JSFile大小是否小于阈值：/ 2为线下测试值
                return file.getAbsolutePath().contains("codecache/") &&
                        file.length() / CODE_CACHE_TO_JS_FILE_SIZE_RADIO < CodeCacheConfig.INSTANCE.getMinJSFileSize();
            });
        }
    }

    private List<RecordEntry> readAllRecordEntry() throws IOException {
        File recordFile = getRecordFile();
        if (!recordFile.exists()) {
            return Collections.emptyList();
        }
        String data = new String(IOUtil.readAllData(new FileInputStream(recordFile)));
        MSCLog.i(TAG, "record.txt:", data);
        String[] entryStrings = data.split("\n");
        // 针对RecordEntry去重。线上存在大量重复行。
        Set<RecordEntry> entrySet = new HashSet<>();
        for (String entryString : entryStrings) {
            RecordEntry entry = new RecordEntry(entryString);
            if (entry.packageFile != null && entry.attachmentDirectory != null) {
                entrySet.add(entry);
            }
        }
        return new ArrayList<>(entrySet);
    }

    private void writeAllRecordEntryInTemporary(List<RecordEntry> recordEntryList) throws IOException {
        File temporaryFile = new File(getAttachmentDirectory(), "temporary.txt");
        boolean hasException = false;
        OutputStream fos = null;
        try {
            fos = new BufferedOutputStream(new FileOutputStream(temporaryFile));
            for (RecordEntry recordEntry : recordEntryList) {
                MSCLog.i(TAG, "write record entry:" + recordEntry.toEntryString());
                fos.write(recordEntry.toEntryString().getBytes());
            }
        } catch (IOException e) {
            hasException = true;
            FileUtil.deleteFile(temporaryFile.getAbsolutePath());
            MSCLog.e(TAG, e);
        } finally {
            IOUtil.closeQuietly(fos);
        }
        if (!hasException) {
            File recordFile = getRecordFile();
            if (temporaryFile.renameTo(recordFile)) {
                MSCLog.i("rename temporary file success");
            } else {
                MSCLog.e("rename temporay file fail");
            }
        }
    }

    private void writeAllRecordEntry(List<RecordEntry> recordEntryList) throws IOException {
        File recordFile = getRecordFile();
        OutputStream fos = null;
        try {
            fos = new BufferedOutputStream(new FileOutputStream(recordFile));
            for (RecordEntry recordEntry : recordEntryList) {
                MSCLog.i(TAG, "write record entry:" + recordEntry.toEntryString());
                fos.write(recordEntry.toEntryString().getBytes());
            }
        } catch (IOException e) {
            MSCLog.e(TAG, e);
        } finally {
            IOUtil.closeQuietly(fos);
        }
    }


    static class RecordEntry {
        String packageFile;
        String attachmentDirectory;

        public RecordEntry(String entryString) {
            String[] parts = entryString.split(":");
            if (parts.length == 2) {
                this.packageFile = parts[0];
                this.attachmentDirectory = parts[1];
            } else {
                this.packageFile = null;
                this.attachmentDirectory = null;
            }
        }

        public RecordEntry(String packageFile, String attachmentDirector) {
            this.packageFile = packageFile;
            this.attachmentDirectory = attachmentDirector;
        }

        public String toEntryString() {
            return packageFile + ":" + attachmentDirectory + "\n";
        }

        @Override
        public boolean equals(Object o) {
            if (this == o) return true;
            if (o == null || getClass() != o.getClass()) return false;
            RecordEntry that = (RecordEntry) o;
            return Objects.equals(packageFile, that.packageFile) && Objects.equals(attachmentDirectory, that.attachmentDirectory);
        }

        @Override
        public int hashCode() {
            return Objects.hash(packageFile, attachmentDirectory);
        }
    }
}
