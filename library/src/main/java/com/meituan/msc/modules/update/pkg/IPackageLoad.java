package com.meituan.msc.modules.update.pkg;

import com.meituan.met.mercury.load.core.DDResource;
import com.meituan.msc.modules.update.bean.PackageInfoWrapper;
import com.meituan.msc.util.perf.PerfEventRecorder;

public interface IPackageLoad<T> {


    /**
     * 检查更新前端基础库资源
     */
    void checkUpdateLatestBasePackage();

    /**
     * 获取最新版本基础库
     * 本地有缓存则使用缓存，无缓存请求网络
     *
     * @param recorder 用于性能埋点
     * @param cb       结果回调
     */
    void loadLatestBasePackage(PerfEventRecorder recorder, String appId, String mscVersionOfLaunchDebug, String checkScene, PackageLoadCallback<DDResource> cb);

    /**
     * 据元信息中的基础包、主包、子包信息，获取对应包文件
     * 先检查缓存，缓存不存在，则下载完后回调
     *
     * @param recorder                  用于性能埋点
     * @param infoWrapper               包信息
     * @param needCheckResourceAndRetry 是否需要检查资源有效性并进行重试
     * @param callback                  结果回调
     */
    void loadPackageWithInfo(PerfEventRecorder recorder, PackageInfoWrapper infoWrapper, boolean needCheckResourceAndRetry, String checkScene, String loadScene, PackageLoadCallback<T> callback);

    /**
     * 检查资源文件是否可用（通过md5校验实现），如不可用则删除缓存
     *
     * @param infoWrapper 包资源数据
     * @return 返回MD5校验结果，如校验通过为ture，否则为false；文件不存在将返回false
     */
    boolean checkMd5AndDeleteIfNeed(String checkScene, PackageInfoWrapper infoWrapper);

    /**
     * 检查DD资源MD5 并上报结果
     *
     * @param checkScene  检查场景
     * @param packageInfo 包数据
     * @return 返回MD5校验结果，如校验通过为ture，否则为false；文件不存在将返回false
     */
    boolean checkDDResourceMd5AndReport(String checkScene, PackageInfoWrapper packageInfo);
}
