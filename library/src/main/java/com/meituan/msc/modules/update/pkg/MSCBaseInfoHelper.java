package com.meituan.msc.modules.update.pkg;

import android.support.annotation.Nullable;
import android.text.TextUtils;
import android.util.Pair;

import com.meituan.msc.modules.engine.RuntimeManager;
import com.meituan.msc.modules.preload.PackageDebugHelper;
import com.meituan.msc.modules.reporter.MSCLog;

import java.util.List;
import java.util.Objects;

/**
 * 供业务调用获取缓存的基础库数据，未持久化
 * https://km.sankuai.com/collabpage/1418627468#id-%E6%8A%80%E6%9C%AF%E6%96%B9%E6%A1%88
 */
public class MSCBaseInfoHelper {

    public static final String TAG = "MSCBaseInfoHelper";

    @Nullable
    private static BasePackageInfo basePackageInfoCached;

    static void cacheBasePackageInfo(BasePackageInfo packageInfo) {
        basePackageInfoCached = packageInfo;
    }

    /**
     * @api
     * 组件标准化注释_标准API
     * 获取缓存的基础包版本数据
     * 注意：仅当运行时缓存池中基础库数据与最新拉取到的基础库数据一致时才返回数据
     *
     * @return basePackageInfo
     */
    @Nullable
    public static BasePackageInfo getBasePackageInfoCached() {
        if (basePackageInfoCached == null) {
            MSCLog.i(TAG, "getBasePackageInfoCached basePackageInfoCached is null");
            return null;
        }

        // TODO: 2022/11/1 tianbin 更优的方案为返回启动时即将被征用的运行时对应的基础库数据 需要业务改动传入appId 后续优化
        // 检查已存在的运行时中 基础库版本号是否一致
        List<Pair<String, String>> usingBasePackageVersions = RuntimeManager.getUsingBasePackageVersions();
        String lastBasePackageName = null;
        String lastBasePackageVersion = null;
        for (Pair<String, String> pair : usingBasePackageVersions) {
            if (lastBasePackageVersion == null) {
                lastBasePackageName = pair.first;
                lastBasePackageVersion = pair.second;
            } else {
                if (!TextUtils.equals(lastBasePackageName, pair.first)
                        || !TextUtils.equals(lastBasePackageVersion, pair.second)) {
                    // 基础库版本号不一致时 返回null，业务走兜底逻辑
                    MSCLog.i(TAG, "getBasePackageInfoCached has different base package version1");
                    return null;
                }
            }
        }

        BasePackageInfo basePackageInfo = new BasePackageInfo.Builder()
                .setEnv(PackageDebugHelper.BASE_PACKAGE_ENV_PROD)
                .setName(lastBasePackageName)
                .setVersion(lastBasePackageVersion)
                .build();

        // 检查 运行时中基础库数据 包模块请求成功后缓存的数据
        if (basePackageInfo.equals(basePackageInfoCached)) {
            MSCLog.i(TAG, "getBasePackageInfoCached", basePackageInfo);
            return basePackageInfoCached;
        } else {
            MSCLog.i(TAG, "getBasePackageInfoCached has different base package version2");
            return null;
        }
    }

    public static class BasePackageInfo {
        @PackageDebugHelper.BasePackageEnv
        private String env;
        private String name;
        private String version;

        @PackageDebugHelper.BasePackageEnv
        public String getEnv() {
            return env;
        }

        public String getName() {
            return name;
        }

        public String getVersion() {
            return version;
        }

        @Override
        public String toString() {
            return "BasePackageInfo{" +
                    "env='" + env + '\'' +
                    ", name='" + name + '\'' +
                    ", version='" + version + '\'' +
                    '}';
        }

        @Override
        public boolean equals(Object o) {
            if (this == o) return true;
            if (o == null || getClass() != o.getClass()) return false;
            BasePackageInfo that = (BasePackageInfo) o;
            return Objects.equals(env, that.env) && Objects.equals(name, that.name) && Objects.equals(version, that.version);
        }

        @Override
        public int hashCode() {
            return Objects.hash(env, name, version);
        }

        public static class Builder {
            @PackageDebugHelper.BasePackageEnv
            private String env;
            private String name;
            private String version;

            Builder setEnv(@PackageDebugHelper.BasePackageEnv String env) {
                this.env = env;
                return this;
            }

            Builder setName(String name) {
                this.name = name;
                return this;
            }

            Builder setVersion(String version) {
                this.version = version;
                return this;
            }

            BasePackageInfo build() {
                BasePackageInfo basePackageInfo = new BasePackageInfo();
                basePackageInfo.env = this.env;
                basePackageInfo.name = this.name;
                basePackageInfo.version = this.version;
                return basePackageInfo;
            }
        }
    }
}
