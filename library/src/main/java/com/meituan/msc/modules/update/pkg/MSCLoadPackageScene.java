package com.meituan.msc.modules.update.pkg;

import android.text.TextUtils;

import com.meituan.msc.modules.engine.MSCHornRollbackConfig;

public class MSCLoadPackageScene {
    //启动
    public static final String LOAD_PACKAGE_TYPE_LAUNCH = "launch";

    //批量更新预下载
    public static final String LOAD_PACKAGE_TYPE_BATCH_UPDATE = "batchPreDownload";

    //后台更新预下载
    public static final String LOAD_PACKAGE_TYPE_BACKGROUND_UPDATE = "backgroundPreDownload";

    //MSC初始化预下载
    public static final String LOAD_PACKAGE_TYPE_MSC_INIT = "initPreDownload";

    //业务触发预加载
    public static final String LOAD_PACKAGE_TYPE_BIZ_PRE_LOAD = "bizPreLoad";

    //业务深度预热，资源预热
    public static final String LOAD_PACKAGE_TYPE_DEEP_BIZ_PRE_LOAD = "deepBizPreload";

    //预下载
    public static final String LOAD_PACKAGE_TYPE_OTHER_PRE_DOWNLOAD = "preDownload";

    //分包预下载, 页面FP后发起子包加载
    public static final String LOAD_PACKAGE_TYPE_FP_PRE_DOWNLOAD = "afterPageFPPreDownload";

    public static boolean isUseImmediately(String scene) {
        //开关关闭，不管什么场景，返回立即下载
        if (!MSCHornRollbackConfig.enableLoadPackageWithColor()) {
            return true;
        }

        //启动场景和深度业务预热场景立即下载
        if (TextUtils.equals(scene, LOAD_PACKAGE_TYPE_LAUNCH) || TextUtils.equals(scene, LOAD_PACKAGE_TYPE_DEEP_BIZ_PRE_LOAD)) {
            return true;
        }

        //业务预热开启立即下载
        if (MSCHornRollbackConfig.enableBizPreloadImmediately()) {
            if (TextUtils.equals(scene, LOAD_PACKAGE_TYPE_BIZ_PRE_LOAD)) {
                return true;
            }
        }

        return false;
    }
}
