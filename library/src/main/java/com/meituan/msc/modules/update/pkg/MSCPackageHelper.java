package com.meituan.msc.modules.update.pkg;

import static com.meituan.msc.modules.update.bean.CheckUpdateParams.Type.NETWORK;

import android.support.annotation.NonNull;
import android.support.annotation.Nullable;
import android.text.TextUtils;

import com.meituan.android.mercury.msc.adaptor.bean.MSCMetaInfo;
import com.meituan.android.mercury.msc.adaptor.callback.MSCMetaInfoCallback;
import com.meituan.android.mercury.msc.adaptor.core.MSCLoadExeption;
import com.meituan.met.mercury.load.bean.ExtraParamsBean;
import com.meituan.msc.common.framework.Callback;
import com.meituan.msc.common.utils.CollectionUtil;
import com.meituan.msc.modules.apploader.events.AppLoadException;
import com.meituan.msc.modules.engine.MSCHornRollbackConfig;
import com.meituan.msc.modules.engine.MSCRuntime;
import com.meituan.msc.modules.engine.RecentMSCManager;
import com.meituan.msc.modules.engine.RuntimeManager;
import com.meituan.msc.modules.preload.MSCHornPreloadConfig;
import com.meituan.msc.modules.reporter.MSCLog;
import com.meituan.msc.modules.update.PackagePreLoadReporter;
import com.meituan.msc.modules.update.bean.AppMetaInfoWrapper;
import com.meituan.msc.modules.update.bean.CheckUpdateParams;
import com.meituan.msc.modules.update.bean.PackageInfoWrapper;
import com.meituan.msc.modules.update.metainfo.AppCheckUpdateManager;
import com.meituan.msc.modules.update.metainfo.CheckUpdateCallback;
import com.meituan.msc.modules.update.metainfo.MetaFetchRulerManager;

import java.util.ArrayList;
import java.util.Iterator;
import java.util.List;
import java.util.Map;

public final class MSCPackageHelper {

    private static final String TAG = "MSCPackageHelper";

    /**
     * 获取缓存的业务包版本号，优先从内存中获取，如果内存中获取不到，则调用DD获取本次缓存接口获取
     *
     * @param appId    appId
     * @param callback callback
     */
    public static void getCachedMetaInfoAppVersion(String appId, Callback<String> callback) {
        if (callback == null) {
            MSCLog.e(TAG, "getCachedMetaInfoAppVersion callback is null");
            return;
        }
        MSCLog.i(TAG, "getCachedMetaInfoAppVersion", appId);
        MSCRuntime runtime = RuntimeManager.getRuntimeWithAppId(appId);
        if (runtime != null) {
            String mscAppVersion = runtime.getMSCAppModule().getMSCAppVersion();
            if (!TextUtils.isEmpty(mscAppVersion)) {
                callback.onSuccess(mscAppVersion);
                return;
            }
        }

        AppCheckUpdateManager.getInstance().checkUpdate(new CheckUpdateParams(appId, CheckUpdateParams.Type.CACHE),
                new CheckUpdateCallback<AppMetaInfoWrapper>() {
                    @Override
                    public void onSuccess(@NonNull AppMetaInfoWrapper data) {
                        callback.onSuccess(data.getVersion());
                    }

                    @Override
                    public void onFail(String errMsg, AppLoadException error) {
                        callback.onFail(errMsg, error);
                    }
                });
    }

    /**
     * 预下载主包
     *
     * @param appId    appId
     * @param callback callback
     */
    public static void preDownloadMainPackage(String appId, Callback<Void> callback) {
        if (TextUtils.isEmpty(appId)) {
            MSCLog.e(TAG, "preDownloadMainPackage appId is empty");
            if (callback != null) {
                callback.onFail("appId is empty", null);
            }
            return;
        }
        MSCLog.i(TAG, "preDownloadMainPackage", appId);
        AppCheckUpdateManager.getInstance().checkUpdate(new CheckUpdateParams(appId, NETWORK),
                new CheckUpdateCallback<AppMetaInfoWrapper>() {
                    @Override
                    public void onSuccess(@NonNull AppMetaInfoWrapper data) {
                        AppCheckUpdateManager.getInstance().preDownLoadPackage(appId, data.getVersion(),
                                data.createMainPackageWrapper(), data.createConfigPackageWrapper(), PackagePreLoadReporter.create(),
                                new PackageLoadCallback<PackageInfoWrapper>() {
                                    @Override
                                    public void onSuccess(@NonNull PackageInfoWrapper data) {
                                        MSCLog.i(TAG, "preDownloadMainPackage success", appId);
                                        if (callback != null) {
                                            callback.onSuccess(null);
                                        }
                                    }

                                    @Override
                                    public void onFail(String errMsg, AppLoadException error) {
                                        MSCLog.e(TAG, error, "preDownloadMainPackage failed", appId, errMsg);
                                        if (callback != null) {
                                            callback.onFail(errMsg, error);
                                        }
                                    }
                                });
                    }

                    @Override
                    public void onFail(String errMsg, AppLoadException error) {
                        MSCLog.e(TAG, error, "preDownloadMainPackage failed", appId, errMsg);
                        if (callback != null) {
                            callback.onFail(errMsg, error);
                        }
                    }
                });

    }

    /**
     * 设置包更新额外参数
     *
     * @param pkgExtraParams 额外参数
     */
    public static void setPkgExtraParams(List<ExtraParamsBean> pkgExtraParams) {
        AppCheckUpdateManager.getInstance().setPkgExtraParams(pkgExtraParams);
    }

    /**
     * 最近使用过的小程序批量包更新
     */
    public static void batchCheckUpdate() {
        List<String> recentAppList = RecentMSCManager.getRecentAppList();
        MSCLog.i(TAG, "batchCheckUpdate:", CollectionUtil.toString(recentAppList));
        MSCMetaInfoCallback callback = new MSCMetaInfoCallback() {
            @Override
            public void onSuccess(@Nullable MSCMetaInfo metaInfo) {
                if (metaInfo == null) {
                    MSCLog.e(TAG, "batchCheckUpdate metaInfo is null");
                }
            }

            @Override
            public void onFail(MSCLoadExeption e) {
                MSCLog.e(TAG, e);
            }
        };
        AppCheckUpdateManager.getInstance().callBatchCheckUpdate(recentAppList, callback);
    }

    /**
     * 指定小程序批量包更新
     *
     * @param appList 小程序列表
     */
    public static void batchCheckUpdate(List<String> appList) {
        if (CollectionUtil.isEmpty(appList)) {
            MSCLog.e(TAG, "batchCheckUpdate appList is empty");
            return;
        }
        List<String> mAppList = new ArrayList<>(appList);
        Iterator<String> iterator = mAppList.iterator();
        while (iterator.hasNext()) {
            String appId = iterator.next();
            if (!MSCHornPreloadConfig.enableUpdateBizPackageAppList(appId)) {
                MSCLog.e(TAG, "nonsupport update appId: " + appId);
                iterator.remove();
            }
        }
        MSCLog.i(TAG, "new batchCheckUpdate:", CollectionUtil.toString(mAppList));
        MSCMetaInfoCallback callback = new MSCMetaInfoCallback() {
            @Override
            public void onSuccess(@Nullable MSCMetaInfo metaInfo) {
                if (metaInfo == null) {
                    MSCLog.e(TAG, "new batchCheckUpdate metaInfo is null");
                }
            }

            @Override
            public void onFail(MSCLoadExeption e) {
                MSCLog.e(TAG, e);
            }
        };
        AppCheckUpdateManager.getInstance().callBatchCheckUpdate(mAppList, callback);
    }

    /**
     * 新增包更新额外参数
     *
     * @param pkgExtraParams 额外参数
     */
    public static void addPkgExtraParams(List<ExtraParamsBean> pkgExtraParams) {
        if (CollectionUtil.isEmpty(pkgExtraParams)) {
            MSCLog.e(TAG, "addPkgExtraParams pkgExtraParams is empty");
            return;
        }
        List<ExtraParamsBean> mExtraParamBeans = AppCheckUpdateManager.getInstance().getPkgExtraParams();
        if (CollectionUtil.isEmpty(mExtraParamBeans)) {
            setPkgExtraParams(pkgExtraParams);
        } else {
            mExtraParamBeans.addAll(pkgExtraParams);
        }
    }

    /**
     * 删除包更新额外参数
     */
    public static void clearPkgExtraParams() {
        AppCheckUpdateManager.getInstance().setPkgExtraParams(null);
    }

    /**
     * 获取包更新额外参数
     *
     * @return 额外参数
     */
    @Nullable
    public static List<ExtraParamsBean> getPkgExtraParams() {
        return AppCheckUpdateManager.getInstance().getPkgExtraParams();
    }

    public static void addPkgExtraParamPersist(String appId, Map<String, String> extraParams) {
        if (!MSCHornRollbackConfig.enablePkgExtraParam()) {
            return;
        }

        if (TextUtils.isEmpty(appId) || extraParams == null || extraParams.isEmpty()) {
            return;
        }

        for (Map.Entry<String, String> entry : extraParams.entrySet()) {
            if (entry == null ) {
                continue;
            }
            MetaFetchRulerManager.getInstance().addPkgExtraParamPersist(appId, entry.getKey(), entry.getValue());
        }
    }

    public static void clearPkgExtraParamPersist(String appId) {
        if (!MSCHornRollbackConfig.enablePkgExtraParam()) {
            return;
        }

        MetaFetchRulerManager.getInstance().clearPkgExtraParamsPersist(appId);
    }

    public static void removePkgExtraParamPersist(String appId, String key) {
        if (!MSCHornRollbackConfig.enablePkgExtraParam()) {
            return;
        }

        MetaFetchRulerManager.getInstance().removePkgExtraParamPersist(appId, key);
    }
}
