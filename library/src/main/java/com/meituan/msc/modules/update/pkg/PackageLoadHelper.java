package com.meituan.msc.modules.update.pkg;

import com.meituan.msc.common.utils.CollectionUtil;
import com.meituan.msc.modules.update.bean.PackageInfoWrapper;

import java.util.List;

public class PackageLoadHelper {

    public static boolean isAllPackageReady(List<PackageInfoWrapper> needUpdatePackages) {
        if (CollectionUtil.isEmpty(needUpdatePackages)) {
            return true;
        }
        for (PackageInfoWrapper packageInfoWrapper : needUpdatePackages) {
            if (!packageInfoWrapper.isSourceReady) {
                return false;
            }
        }
        return true;
    }

    public static boolean isAllPackageInjectToAppService(List<PackageInfoWrapper> needInjectPackages) {
        if (CollectionUtil.isEmpty(needInjectPackages)) {
            return true;
        }
        for (PackageInfoWrapper packageInfoWrapper : needInjectPackages) {
            if (!packageInfoWrapper.isPackageInjected) {
                return false;
            }
        }
        return true;
    }
}
