package com.meituan.msc.modules.update.pkg;

import static com.meituan.msc.modules.update.bean.PackageInfoWrapper.PACKAGE_FRAMEWORK_SERVICE_FILE;
import static com.meituan.msc.modules.update.bean.PackageInfoWrapper.PACKAGE_SERVICE_FILE;

import android.content.SharedPreferences;
import android.os.SystemClock;
import android.support.annotation.NonNull;
import android.support.annotation.Nullable;
import android.text.TextUtils;

import com.meituan.android.mercury.msc.adaptor.bean.MSCLoadPackageInfo;
import com.meituan.android.mercury.msc.adaptor.callback.MSCPackageInfoCallback;
import com.meituan.android.mercury.msc.adaptor.core.DDLoadMSCAdaptor;
import com.meituan.android.mercury.msc.adaptor.core.MSCLoadExeption;
import com.meituan.dio.DioEntry;
import com.meituan.dio.easy.DioFile;
import com.meituan.met.mercury.load.bean.DDLoadPhaseData;
import com.meituan.met.mercury.load.bean.ResourceNameVersion;
import com.meituan.met.mercury.load.core.DDLoaderManager;
import com.meituan.met.mercury.load.core.DDResource;
import com.meituan.msc.common.config.MSCConfig;
import com.meituan.msc.common.perf.PerfEventConstant;
import com.meituan.msc.common.utils.Constants;
import com.meituan.msc.common.utils.MPConcurrentHashMap;
import com.meituan.msc.common.utils.MSCTraceUtil;
import com.meituan.msc.common.utils.VersionUtil;
import com.meituan.msc.extern.MSCEnvHelper;
import com.meituan.msc.lib.BuildConfig;
import com.meituan.msc.modules.apploader.events.AppLoadException;
import com.meituan.msc.modules.engine.MSCHornRollbackConfig;
import com.meituan.msc.modules.engine.MSCRuntime;
import com.meituan.msc.modules.engine.RuntimeManager;
import com.meituan.msc.modules.preload.MSCHornPreloadConfig;
import com.meituan.msc.modules.preload.PackageDebugHelper;
import com.meituan.msc.modules.reporter.MSCLoadErrorConstants;
import com.meituan.msc.modules.reporter.MSCLog;
import com.meituan.msc.modules.update.MSCHornBasePackageReloadConfig;
import com.meituan.msc.modules.update.PackageLoadReporter;
import com.meituan.msc.modules.update.PackagePreLoadReporter;
import com.meituan.msc.modules.update.PackageReportBean;
import com.meituan.msc.modules.update.bean.PackageInfoWrapper;
import com.meituan.msc.util.perf.PerfEventRecorder;
import com.meituan.msc.util.perf.PerfTrace;

import java.util.Collections;
import java.util.concurrent.ConcurrentHashMap;
import java.util.concurrent.CopyOnWriteArrayList;
import java.util.concurrent.atomic.AtomicInteger;

/**
 * 包加载 管理类
 */
public class PackageLoadManager implements IPackageLoad<PackageInfoWrapper> {

    public static final String SP_MSC_VERSION = "msc_version";
    public static final String KEY_AAR_VERSION = "aar_version";

    private static final String TAG = "PackageLoadManager";
    private static volatile PackageLoadManager mInstance;
    public String checkUpdateBasePackageErrorMsg = "check not start";

    /**
     * 前端基础库发布包资源名
     * https://diva.sankuai.com/bundle/mscsdk_base/versions
     */
    public static final String BASE_PACKAGE_NAME = "mscsdk_base";
    /**
     * 前端基础库调试包资源名
     */
    public static final String BASE_PACKAGE_NAME_TEST = "mscsdk_base_test";

    public static final int BASE_PACKAGE_FETCH_STATE_UNCHECK = 0;
    private static final int BASE_PACKAGE_FETCH_STATE_CHECKING = 1;
    /**
     * 合并基础库更新过程中其余场景触发的更新操作，避免多余网络请求
     */
    public static final AtomicInteger lockBaseNetworkRequest = new AtomicInteger(BASE_PACKAGE_FETCH_STATE_UNCHECK);
    /**
     * 基础包版本网络检查结果分发
     */
    public static final CopyOnWriteArrayList<MSCPackageInfoCallback> baseNetworkRequestCallbackList = new CopyOnWriteArrayList<>();
    private long lastCheckUpdateTimeMill;

    private PackageLoadManager() {
    }

    public static PackageLoadManager getInstance() {
        if (mInstance == null) {
            synchronized (PackageLoadManager.class) {
                if (mInstance == null) {
                    mInstance = new PackageLoadManager();
                }
            }
        }
        return mInstance;
    }

    @Override
    public void loadLatestBasePackage(PerfEventRecorder recorder, String appId, String mscVersionOfLaunchDebug, String checkScene, PackageLoadCallback<DDResource> cb) {
        recorder.beginDurableEvent(PerfEventConstant.FETCH_BASE_PACKAGE);

        MSCPackageInfoCallback callback = new MSCPackageInfoCallback() {
            @Override
            public void onSuccess(@Nullable DDResource resource) {
                if (resource == null) {
                    String errorMsg = "load base package failed, ddResource is null";
                    onFetchLatestBasePackageFailed(cb, recorder, errorMsg,
                            new AppLoadException(MSCLoadErrorConstants.ERROR_DOWNLOAD_BASE_EMPTY_DATA, errorMsg));
                    return;
                }

                MSCLog.d(TAG, "[MSC][Base] download success", resource);
                cacheBasePackageInfoForAPI(resource);

                cb.onSuccess(resource);

                ConcurrentHashMap<String, Object> eventExtra = new MPConcurrentHashMap<>();
                eventExtra.put("md5", resource.getMd5());
                eventExtra.put("status", "ok");
                recorder.endDurableEvent(PerfEventConstant.FETCH_BASE_PACKAGE, eventExtra);
            }

            @Override
            public void onFail(MSCLoadExeption mscLoadExeption) {
                String errorMsg = "load base package failed:" + (mscLoadExeption == null ? "" : mscLoadExeption.toString());
                AppLoadException loadException = new AppLoadException(getDownloadBasePackageErrorCode(mscLoadExeption), mscLoadExeption);
                onFetchLatestBasePackageFailed(cb, recorder, errorMsg, loadException);
            }
        };

        // 判断基础组件版本是否变化，如是，则执行网络更新
        SharedPreferences sp = MSCEnvHelper.getSharedPreferences(PackageLoadManager.SP_MSC_VERSION);
        String cachedAARVersion = sp.getString(PackageLoadManager.KEY_AAR_VERSION, null);
        boolean aarVersionChanged = VersionUtil.isAARVersionChanged(cachedAARVersion);
        if (MSCTraceUtil.TRACE_LEVEL >= MSCTraceUtil.LEVEL_LIGHT) {
            PerfTrace.begin(PerfEventConstant.LOAD_LATEST_BASE_PACKAGE);
        }
        MSCPackageInfoCallback packageInfoCallback = createRetryDownloadBasePackageCallback(callback, checkScene);
        if (aarVersionChanged) {
            fetchLatestBasePackage(mscVersionOfLaunchDebug, checkScene, packageInfoCallback);
            MSCRuntime mRuntime = RuntimeManager.getRuntimeWithAppId(appId);
            if (mRuntime != null) {
                mRuntime.markBasePkgAsForceUpdate();
            }
        } else {
            loadLatestBasePkgCacheFirst(mscVersionOfLaunchDebug, checkScene, packageInfoCallback);
        }
    }

    private int getDownloadBasePackageErrorCode(@Nullable MSCLoadExeption mscLoadExeption) {
        if (mscLoadExeption == null) {
            return MSCLoadErrorConstants.ERROR_DOWNLOAD_BASE_FAILED_EXCEPTION_EMPTY;
        }
        return MSCLoadErrorConstants.ERROR_DOWNLOAD_BASE_FAILED + mscLoadExeption.getErrCode() % 1000;
    }

    private MSCPackageInfoCallback createRetryDownloadBasePackageCallback(MSCPackageInfoCallback callback, String checkScene) {
        return new MSCPackageInfoCallback() {
            @Override
            public void onSuccess(@Nullable DDResource ddResource) {
                if (MSCTraceUtil.TRACE_LEVEL >= MSCTraceUtil.LEVEL_LIGHT) {
                    PerfTrace.end(PerfEventConstant.LOAD_LATEST_BASE_PACKAGE);
                }
                if (ddResource == null) {
                    callback.onSuccess(null);
                    return;
                }
                if (MSCTraceUtil.TRACE_LEVEL >= MSCTraceUtil.LEVEL_LIGHT) {
                    PerfTrace.begin(PerfEventConstant.CHECK_BASE_PACKAGE_EXIST_AND_REPORT);
                }
                // 文件是否存在不耗时，默认启用
                if (!MSCHornRollbackConfig.get().getConfig().isRollbackFileExistCheck
                        && !checkDDResourceExistAndReport(PackageInfoWrapper.PACKAGE_TYPE_STR_BASE, ddResource)) {
                    destroyRuntimesAndRetryDownloadBasePackage(ddResource, checkScene, callback);
                    return;
                }
                // 检查必要文件
                if (!MSCHornRollbackConfig.isRollbackCheckResource() && !checkPackageInfoExistAndReport(PackageInfoWrapper.PACKAGE_TYPE_STR_BASE, ddResource, checkScene)) {
                    destroyRuntimesAndRetryDownloadBasePackage(ddResource, checkScene, callback);
                    return;
                }
                // MD5校验耗时，默认不启动，支持云控开启
                if (MSCHornPreloadConfig.preCheckDDResourceMd5AndRetryDownload()
                        && !checkDDResourceMd5AndReport("downloadBase", PackageInfoWrapper.PACKAGE_TYPE_STR_BASE, ddResource)) {
                    destroyRuntimesAndRetryDownloadBasePackage(ddResource, checkScene, callback);
                    return;
                }
                if (MSCTraceUtil.TRACE_LEVEL >= MSCTraceUtil.LEVEL_LIGHT) {
                    PerfTrace.end(PerfEventConstant.CHECK_BASE_PACKAGE_EXIST_AND_REPORT);
                }
                callback.onSuccess(ddResource);
            }

            @Override
            public void onFail(MSCLoadExeption mscLoadExeption) {
                callback.onFail(mscLoadExeption);
            }
        };
    }

    private void destroyRuntimesAndRetryDownloadBasePackage(@NonNull DDResource ddResource, String scene, MSCPackageInfoCallback callback) {
        MSCLog.i(TAG, "destroyRuntimesAndRetryDownloadBasePackage", ddResource);
        RuntimeManager.destroyRuntimesAndDeleteDDResource(ddResource);
        DDLoadMSCAdaptor.updateBasePackageWithName(BASE_PACKAGE_NAME, scene, true, false, callback);
    }

    private boolean checkDDResourceExistAndReport(String pkgType, @NonNull DDResource ddResource) {
        boolean resourceExists = new DioFile(ddResource.getLocalPath()).exists();
        boolean serviceFileExits;
        if (TextUtils.equals(pkgType, PackageInfoWrapper.PACKAGE_TYPE_STR_BASE)) {
            serviceFileExits = new DioFile(ddResource.getLocalPath(), PACKAGE_FRAMEWORK_SERVICE_FILE).exists();
        } else {
            serviceFileExits = new DioFile(ddResource.getLocalPath(), PACKAGE_SERVICE_FILE).exists();
        }
        if (!resourceExists || !serviceFileExits) {
            PackageLoadReporter.CommonReporter reporter = PackageLoadReporter.CommonReporter.create();
            reporter.reportDDResourceFileExist(pkgType, ddResource, resourceExists, serviceFileExits);
        }
        return resourceExists && serviceFileExits;
    }

    /**
     * 检查必要文件（基础包、业务包）https://km.sankuai.com/collabpage/2405678808
     *
     * @param pkgType
     * @param ddResource
     * @param checkScene
     * @return
     */
    private boolean checkPackageInfoExistAndReport(String pkgType, @NonNull DDResource ddResource, String checkScene) {
        long startTime = System.currentTimeMillis();
        DioEntry[] dioEntries = new DioFile(ddResource.getLocalPath()).getDioReader().getEntries();
        if (dioEntries.length == 0) {
            String errorMessage = "dioEntries is empty";
            reportPackageInfoError(pkgType, ddResource, checkScene, errorMessage, System.currentTimeMillis() - startTime);
            return false;
        }
        for (DioEntry dioEntry : dioEntries) {
            String path = dioEntry.getPath();
            if (TextUtils.isEmpty(path) || path.charAt(0) == 0) {
                String errorMessage = "dioEntries is empty";
                reportPackageInfoError(pkgType, ddResource, checkScene, errorMessage, System.currentTimeMillis() - startTime);
                return false;
            }
        }
        return true;
    }

    private void reportPackageInfoError(String pkgType, DDResource ddResource, String checkScene, String errorMessage, long duration) {
        PackageLoadReporter.CommonReporter reporter = PackageLoadReporter.CommonReporter.create();
        reporter.reportResourceCheckResult(pkgType, ddResource.getName(), ddResource.getMd5(), ddResource.isFromNet(), checkScene, errorMessage, duration);
        MSCLog.e(TAG, "#checkPackageInfoExistAndReport fail:", errorMessage);
    }

    /**
     * 缓存MSC组件版本，组件版本前两位变更时会触发基础库更新
     */
    private void cacheMSCAARVersion() {
        SharedPreferences mscVersionSP = MSCEnvHelper.getSharedPreferences(SP_MSC_VERSION);
        MSCLog.i(TAG, "cacheMSCAARVersion", mscVersionSP.getString(KEY_AAR_VERSION, null), BuildConfig.AAR_VERSION);
        mscVersionSP.edit().putString(KEY_AAR_VERSION, BuildConfig.AAR_VERSION).apply();
    }

    /**
     * 清除缓存的MSC组件版本，下次获取基础库时会触发更新
     */
    public void cleanMSCAARVersionCache() {
        SharedPreferences mscVersionSP = MSCEnvHelper.getSharedPreferences(SP_MSC_VERSION);
        mscVersionSP.edit().remove(KEY_AAR_VERSION).apply();
    }

    // AOP
    public AtomicInteger getBasePkgVersionCheckState(String basePkgVersionOnTestEnv) {
        return lockBaseNetworkRequest;
    }

    // AOP
    public void loadLatestBasePkgCacheFirst(String mscVersionOfLaunchDebug, String scene, MSCPackageInfoCallback callback) {
        // 检查更新基础包信息，对应DDD的缓存优先模式 缓存有最新立即回调，否则请求下载完成回调
        MSCLog.i(TAG, "loadLatestBasePkgCacheFirst");
        DDLoadMSCAdaptor.checkUpdateBasePackageWithName(BASE_PACKAGE_NAME, scene, true, callback);
    }

    @Override
    public void checkUpdateLatestBasePackage() {
        if (noOutOfTimeInterval()) {
            checkUpdateBasePackageErrorMsg = "not out of time interval";
            MSCLog.i(Constants.BUNDLE, "base package check update not out of time interval");
            return;
        }

        MSCLog.i(Constants.BUNDLE, "forceCheckUpdateLatestBasePackage");

        MSCPackageInfoCallback callback = new MSCPackageInfoCallback() {
            @Override
            public void onSuccess(@Nullable DDResource resource) {
                lastCheckUpdateTimeMill = System.currentTimeMillis();
                if (resource == null) {
                    checkUpdateBasePackageErrorMsg = "resource is null";
                    MSCLog.e(TAG, null, "forceCheckUpdateLatestBasePackage failed,resource is null");
                    reportLoadPackageFailed(new IllegalStateException("resource is null"));
                } else {
                    if (resource.isFromNet()) {
                        checkUpdateBasePackageErrorMsg = "check update success,new resource";
                    } else {
                        checkUpdateBasePackageErrorMsg = "check update success,cache";
                    }
                    MSCLog.i(TAG, "forceCheckUpdateLatestBasePackage success:", resource);
                    cacheBasePackageInfoForAPI(resource);
                    String sourceFrom = PackagePreLoadReporter.SOURCE_FROM_TYPE_PREDOWNLOAD;
                    PackagePreLoadReporter.create().onLoadPackageSuccess(
                            new PackageReportBean.Builder()
                                    .setLoadType(resource.isFromNet() ? PackageLoadReporter.LoadType.NETWORK : PackageLoadReporter.LoadType.LOCAL)
                                    .setPkgName(resource.getName())
                                    .setDdLoadPhaseData(resource.getLoadPhaseData())
                                    .setPkgType("base")
                                    .setSourceFrom(sourceFrom)
                                    .build()
                    );
                }
            }

            @Override
            public void onFail(MSCLoadExeption e) {
                MSCLog.e(TAG, e, "forceCheckUpdateLatestBasePackage failed");
                checkUpdateBasePackageErrorMsg = "check update failed:" + (e != null ? e.getMessage() : "");
                reportLoadPackageFailed(e);
            }
        };
        TimedPackageInfoCallback timedPackageInfoCallback = new TimedPackageInfoCallback(callback) {
            @Override
            protected void onTaskTriggerSuccess(@Nullable DDResource ddResource, long startTimeMill) {
                if (ddResource != null) {
                    PackagePreLoadReporter.create().reportLoadPackageSuccessDuration(
                            new PackageReportBean.Builder()
                                    .setLoadType(ddResource.isFromNet() ? PackageLoadReporter.LoadType.NETWORK : PackageLoadReporter.LoadType.LOCAL)
                                    .setPkgName(ddResource.getName())
                                    .setDdLoadPhaseData(ddResource.getLoadPhaseData())
                                    .setPkgType("base")
                                    .setSourceFrom(PackagePreLoadReporter.SOURCE_FROM_TYPE_BGDOWNLOAD)
                                    .build(), System.currentTimeMillis() - startTimeMill
                    );
                }
            }
        };
        fetchLatestBasePackage(null, MSCLoadPackageScene.LOAD_PACKAGE_TYPE_OTHER_PRE_DOWNLOAD, timedPackageInfoCallback);
    }

    private void reportLoadPackageFailed(Exception exception) {
        String sourceFrom = PackagePreLoadReporter.SOURCE_FROM_TYPE_PREDOWNLOAD;
        DDLoadPhaseData ddLoadPhaseData = null;
        if (exception instanceof MSCLoadExeption) {
            ddLoadPhaseData = ((MSCLoadExeption) exception).getLoadPhaseData();
        }
        PackagePreLoadReporter.create().onLoadPackageFailed(
                new PackageReportBean.Builder()
                        .setPkgType("base")
                        .setDdLoadPhaseData(ddLoadPhaseData)
                        .setSourceFrom(sourceFrom)
                        .build(), exception);
    }

    private void cacheBasePackageInfoForAPI(@NonNull DDResource resource) {
        MSCBaseInfoHelper.BasePackageInfo.Builder builder = new MSCBaseInfoHelper.BasePackageInfo.Builder();
        builder.setEnv(PackageDebugHelper.instance.getBasePackageEnv())
                .setName(resource.getName())
                .setVersion(resource.getVersion());
        MSCBaseInfoHelper.cacheBasePackageInfo(builder.build());
    }

    private boolean noOutOfTimeInterval() {
        return System.currentTimeMillis() - lastCheckUpdateTimeMill <= MSCConfig.getBasePackageCheckUpdateTimeIntervalMillis();
    }

    private void fetchLatestBasePackage(String mscVersionOfLaunchDebug, String scene, MSCPackageInfoCallback callback) {
        // 拦截处理基础包版本，支持调试面板锁包
        String basePkgVersionOfDebug = PackageDebugHelper.instance.getBasePkgVersionOfDebug(mscVersionOfLaunchDebug);
        if (getBasePkgVersionCheckState(basePkgVersionOfDebug).get() == BASE_PACKAGE_FETCH_STATE_UNCHECK) {
            getBasePkgVersionCheckState(basePkgVersionOfDebug).compareAndSet(BASE_PACKAGE_FETCH_STATE_UNCHECK, BASE_PACKAGE_FETCH_STATE_CHECKING);
            getFetchBasePkgCallbackList(basePkgVersionOfDebug).add(callback);
            if (callback instanceof TimedPackageInfoCallback) {
                ((TimedPackageInfoCallback) callback).setTaskTrigger(true, System.currentTimeMillis());
            }
            doFetchBasePackage(basePkgVersionOfDebug, scene);
        } else {
            getFetchBasePkgCallbackList(basePkgVersionOfDebug).add(callback);
        }
    }

    // AOP
    public void doFetchBasePackage(String basePkgVersionOfDebug, String scene) {
        MSCLog.i(TAG, "doFetchBasePackage", basePkgVersionOfDebug);
        DDLoadMSCAdaptor.updateBasePackageWithName(BASE_PACKAGE_NAME, scene, true, false, createPackageInfoCallback(basePkgVersionOfDebug));
    }

    public MSCPackageInfoCallback createPackageInfoCallback(String basePkgVersionOfDebug) {
        return new MSCPackageInfoCallback() {
            @Override
            public void onSuccess(@Nullable DDResource ddResource) {
                if (MSCTraceUtil.TRACE_LEVEL >= MSCTraceUtil.LEVEL_LIGHT) {
                    PerfTrace.end(PerfEventConstant.LOAD_LATEST_BASE_PACKAGE);
                }
                getBasePkgVersionCheckState(basePkgVersionOfDebug).set(BASE_PACKAGE_FETCH_STATE_UNCHECK);
                if (ddResource == null) {
                    String errorMsg = "doFetchBasePackage failed,ddResource is null";
                    MSCLog.i(Constants.BUNDLE, errorMsg);
                    doFetchBasePackageFailed(basePkgVersionOfDebug, errorMsg, null);
                } else {
                    MSCLog.i(TAG, "doFetchBasePackage success:", ddResource);
                    // 获取到合法的基础库，缓存当前SDK版本，下次获取方式为缓存优先
                    if (!MSCHornBasePackageReloadConfig.get().isInReloadVersions(ddResource.getVersion())) {
                        cacheMSCAARVersion();
                    }
                    for (MSCPackageInfoCallback callback1 : getFetchBasePkgCallbackList(basePkgVersionOfDebug)) {
                        callback1.onSuccess(ddResource);
                    }
                    getFetchBasePkgCallbackList(basePkgVersionOfDebug).clear();
                }
            }

            @Override
            public void onFail(MSCLoadExeption mscLoadExeption) {
                getBasePkgVersionCheckState(basePkgVersionOfDebug).set(BASE_PACKAGE_FETCH_STATE_UNCHECK);
                String errorMsg = "fetch LatestBasePackage failed:" + (mscLoadExeption == null ? "" : mscLoadExeption.toString());
                MSCLog.i(TAG, errorMsg);
                doFetchBasePackageFailed(basePkgVersionOfDebug, errorMsg, mscLoadExeption);
            }
        };
    }

    // AOP
    public CopyOnWriteArrayList<MSCPackageInfoCallback> getFetchBasePkgCallbackList(String basePkgVersionOnDebug) {
        return baseNetworkRequestCallbackList;
    }

    private void onFetchLatestBasePackageFailed(PackageLoadCallback<DDResource> callback, PerfEventRecorder recorder,
                                                String errorMsg, AppLoadException e) {
        MSCLog.e(TAG, errorMsg);

        callback.onFail(errorMsg, e);
        ConcurrentHashMap<String, Object> eventExtra = new MPConcurrentHashMap<>();
        eventExtra.put("status", "fail");
        recorder.endDurableEvent(PerfEventConstant.FETCH_BASE_PACKAGE, eventExtra);
    }

    private void doFetchBasePackageFailed(String basePkgVersionOfDebug, String errorMsg, MSCLoadExeption throwable) {
        MSCLog.e(TAG, throwable, "doFetchBasePackageFailed", errorMsg);
        for (MSCPackageInfoCallback callback : getFetchBasePkgCallbackList(basePkgVersionOfDebug)) {
            callback.onFail(throwable);
        }
        getFetchBasePkgCallbackList(basePkgVersionOfDebug).clear();
    }

    @Override
    public void loadPackageWithInfo(PerfEventRecorder recorder, @NonNull PackageInfoWrapper infoWrapper,
                                    boolean needCheckResourceAndRetry,
                                    String checkScene, String loadScene,
                                    PackageLoadCallback<PackageInfoWrapper> cb) {
        String eventName = PerfEventConstant.FETCH_BIZ_PACKAGE;
        ConcurrentHashMap<String, Object> eventExtra = new MPConcurrentHashMap<>();
        eventExtra.put("md5", infoWrapper.getMd5());
        if (recorder != null) {
            recorder.beginDurableEvent(eventName, eventExtra);
        }
        PackageLoadCallback<PackageInfoWrapper> callback = new PackageLoadCallback<PackageInfoWrapper>() {
            @Override
            public void onSuccess(@NonNull PackageInfoWrapper data) {
                if (cb != null) {
                    cb.onSuccess(data);
                }
                eventExtra.put("status", "ok");
                if (recorder != null) {
                    recorder.endDurableEvent(eventName, eventExtra);
                }
            }

            @Override
            public void onFail(String errMsg, AppLoadException error) {
                if (cb != null) {
                    cb.onFail(errMsg, error);
                }
                eventExtra.put("status", "fail");
                if (recorder != null) {
                    recorder.endDurableEvent(eventName, eventExtra);
                }
            }
        };

        MSCLog.i(TAG, "fetchPackageWithInfo:" + infoWrapper);
        long startTime = System.currentTimeMillis();
        final boolean isMainPackage = infoWrapper.isMainPackage();
        MSCPackageInfoCallback packageInfoCallback = new MSCPackageInfoCallback() {
            @Override
            public void onSuccess(@Nullable DDResource ddResource) {
                if (MSCTraceUtil.TRACE_LEVEL >= MSCTraceUtil.LEVEL_LIGHT) {
                    PerfTrace.end("fetch_package_with_info:" + infoWrapper.getMd5());
                }
                if (ddResource == null) {
                    String errorMsg = "fetch package info failed，ddResource is null";
                    MSCLog.e(TAG, errorMsg);
                    int errorCode = isMainPackage ? MSCLoadErrorConstants.ERROR_DOWNLOAD_MAIN_PACKAGE_EMPTY_DATA
                            : MSCLoadErrorConstants.ERROR_DOWNLOAD_SUB_PACKAGE_EMPTY_DATA;
                    callback.onFail(errorMsg, new AppLoadException(errorCode, errorMsg));
                } else {
                    infoWrapper.setDDResource(ddResource);
                    infoWrapper.setDownloadTimeInMs(startTime, System.currentTimeMillis());
                    MSCLog.i(TAG, String.format("fetchPackageWithInfo success,pckType:%s,isFromNet:%s,md5:%s",
                            infoWrapper.getPkgTypeString(), infoWrapper.isFromNet(), infoWrapper.getMd5()));
                    callback.onSuccess(infoWrapper);
                }
            }

            @Override
            public void onFail(MSCLoadExeption e) {
                String errorMsg = "fetch package info failed:" + (e == null ? "" : e.getMessage());
                MSCLog.e(TAG, e, errorMsg);
                int errorCode = getLoadPackageErrorCode(e, isMainPackage);
                callback.onFail(errorMsg, new AppLoadException(errorCode, errorMsg, e));
            }
        };

        MSCPackageInfoCallback realCallback = needCheckResourceAndRetry ?
                createRetryDownloadBizPackageCallback(infoWrapper, packageInfoCallback, checkScene, loadScene) : packageInfoCallback;
        if (MSCTraceUtil.TRACE_LEVEL >= MSCTraceUtil.LEVEL_LIGHT) {
            PerfTrace.begin("fetch_package_with_info::" + infoWrapper.getMd5()).arg("pkgType", infoWrapper.toString());
        }

        DDLoadMSCAdaptor.fetchPackageWithInfo(infoWrapper.getPackageInfo(),
                new MSCLoadPackageInfo(infoWrapper.appId, infoWrapper.publishId, loadScene, MSCLoadPackageScene.isUseImmediately(loadScene)), realCallback);
    }

    private int getLoadPackageErrorCode(@Nullable MSCLoadExeption e, boolean isMainPackage) {
        if (e == null) {
            return isMainPackage ? MSCLoadErrorConstants.ERROR_DOWNLOAD_MAIN_PACKAGE_FAILED
                    : MSCLoadErrorConstants.ERROR_DOWNLOAD_SUB_PACKAGE_FAILED;
        }
        int errorCode = isMainPackage ? MSCLoadErrorConstants.ERROR_DOWNLOAD_MAIN_PACKAGE_FAILED
                : MSCLoadErrorConstants.ERROR_DOWNLOAD_SUB_PACKAGE_FAILED;
        return errorCode + e.getErrCode() % 1000;
    }

    private MSCPackageInfoCallback createRetryDownloadBizPackageCallback(PackageInfoWrapper infoWrapper, MSCPackageInfoCallback infoCallback, String checkScene, String loadScene) {
        return new MSCPackageInfoCallback() {
            @Override
            public void onSuccess(@Nullable DDResource ddResource) {
                if (MSCTraceUtil.TRACE_LEVEL >= MSCTraceUtil.LEVEL_LIGHT) {
                    PerfTrace.end("fetch_package_with_info::" + infoWrapper.getMd5());
                    PerfTrace.begin("check_package_and_report::" + infoWrapper.getMd5());
                }
                if (ddResource == null) {
                    infoCallback.onSuccess(null);
                    return;
                }
                // 文件是否存在不耗时，默认启用
                if (!MSCHornRollbackConfig.get().getConfig().isRollbackFileExistCheck
                        && !checkDDResourceExistAndReport(infoWrapper.getPkgTypeString(), ddResource)) {
                    deleteDDDCacheAndRetryDownloadBizPackage(ddResource, infoWrapper, loadScene, infoCallback);
                    return;
                }
                // 检查必要文件
                if (!MSCHornRollbackConfig.isRollbackCheckResource() && !checkPackageInfoExistAndReport(infoWrapper.getPkgTypeString(), ddResource, checkScene)) {
                    deleteDDDCacheAndRetryDownloadBizPackage(ddResource, infoWrapper, loadScene, infoCallback);
                    return;
                }
                // MD5校验耗时，默认不启动，支持云控开启
                if (MSCHornPreloadConfig.preCheckDDResourceMd5AndRetryDownload()
                        && !checkDDResourceMd5AndReport("downLoadBiz", infoWrapper.getPkgTypeString(), ddResource)) {
                    deleteDDDCacheAndRetryDownloadBizPackage(ddResource, infoWrapper, loadScene, infoCallback);
                    return;
                }
                if (MSCTraceUtil.TRACE_LEVEL >= MSCTraceUtil.LEVEL_LIGHT) {
                    PerfTrace.end("check_package_and_report::" + infoWrapper.getMd5());
                }
                infoCallback.onSuccess(ddResource);
            }

            @Override
            public void onFail(MSCLoadExeption mscLoadExeption) {
                infoCallback.onFail(mscLoadExeption);
            }
        };
    }

    private void deleteDDDCacheAndRetryDownloadBizPackage(@NonNull DDResource ddResource,
                                                          PackageInfoWrapper infoWrapper, String loadScene,
                                                          MSCPackageInfoCallback infoCallback) {
        MSCLog.i(TAG, "deleteDDDCacheAndRetryDownloadBizPackage", ddResource, infoWrapper);
        deleteDDResource(ddResource);
        DDLoadMSCAdaptor.fetchPackageWithInfo(infoWrapper.getPackageInfo(),
                new MSCLoadPackageInfo(infoWrapper.appId, infoWrapper.publishId, loadScene, MSCLoadPackageScene.isUseImmediately(loadScene)), infoCallback);
    }

    @Override
    public boolean checkMd5AndDeleteIfNeed(String checkScene, PackageInfoWrapper infoWrapper) {
        if (infoWrapper == null || infoWrapper.getDDResource() == null) {
            return true;
        }
        // 基础库，已删除过不再检查
        if (!MSCHornRollbackConfig.get().getConfig().isRollbackDeletePackageChange
                && infoWrapper.isBasePackage() && infoWrapper.isPackageDeleted) {
            MSCLog.iSync(TAG, "checkMd5AndDeleteIfNeed already delete base package");
            return true;
        }
        // 主包，已删除过不再检查
        if (!MSCHornRollbackConfig.get().getConfig().isRollbackDeletePackageChange
                && infoWrapper.isMainPackage() && infoWrapper.isPackageDeleted) {
            MSCLog.iSync(TAG, "checkMd5AndDeleteIfNeed already delete main package");
            return true;
        }

        // 资源未损坏
        if (checkDDResourceMd5AndReport(checkScene,
                infoWrapper.getPkgTypeString(),
                infoWrapper.getDDResource(),
                infoWrapper.preCheckFileExist(),
                infoWrapper.preCheckIsMd5Same())) {
            return true;
        }

        MSCLog.iSync(TAG, "deleteDDDPackageCache", infoWrapper);
        if (infoWrapper.isBasePackage()) {
            infoWrapper.isPackageDeleted = true;
            RuntimeManager.destroyRuntimesAndDeleteDDResource(infoWrapper.getDDResource());
        } else {
            if (infoWrapper.isMainPackage()) {
                infoWrapper.isPackageDeleted = true;
            }
            deleteDDResource(infoWrapper.getDDResource());
        }
        return false;
    }

    // TODO: 2023/5/9 tianbin 调用应该收口到PackageLoadManager
    public void deleteDDResource(DDResource resource) {
        if (resource == null) {
            MSCLog.e(TAG, "deleteDDDPackageCache null");
            return;
        }
        MSCLog.iSync(TAG, "deleteDDDPackageCache", resource);
        ResourceNameVersion.Builder builder = new ResourceNameVersion.Builder();
        builder.name(resource.getName());
        builder.version(resource.getVersion());
        DDLoaderManager.getLoader(resource.getBusiness()).deleteCacheResource(Collections.singletonList(builder.build()));
    }

    @Override
    public boolean checkDDResourceMd5AndReport(String checkScene, PackageInfoWrapper packageInfo) {
        return checkDDResourceMd5AndReport(checkScene, packageInfo.getPkgTypeString(), packageInfo.getDDResource());
    }

    private boolean checkDDResourceMd5AndReport(String checkScene, String pkgType, DDResource resource) {
        return checkDDResourceMd5AndReport(checkScene, pkgType, resource, false, false);
    }

    /**
     * 检查DD资源MD5 并上报结果
     *
     * @param checkScene 检查场景
     * @param pkgType    包类型
     * @param resource   DDD资源
     * @return 如文件存在且md5相同，则返回true；否则返回false
     */
    public boolean checkDDResourceMd5AndReport(String checkScene, String pkgType, DDResource resource,
                                               boolean preCheckFileExist, boolean preCheckIsMd5Same) {
        if (resource == null) {
            MSCLog.e(TAG, "isResourceInvalid resource null");
            return false;
        }
        PackageLoadReporter.CommonReporter reporter = PackageLoadReporter.CommonReporter.create();
        long startTime = SystemClock.elapsedRealtime();
        boolean resourceExists = new DioFile(resource.getLocalPath()).exists();
        boolean localCacheValid = false;
        if (resourceExists) {
            localCacheValid = resource.isLocalCacheValid();
        }
        reporter.reportResourceMD5CheckDuration(resource, SystemClock.elapsedRealtime() - startTime);
        // 仅在资源异常时上报
        if (!resourceExists || !localCacheValid) {
            reporter.reportDDResourceInvalid(checkScene, pkgType,
                    resource, resourceExists, false, preCheckFileExist, preCheckIsMd5Same);
        }
        return resourceExists && localCacheValid;
    }

    public interface CheckScene {
        String PREDOWNLOAD = "predownload";
        String PRELOAD = "preload";
        String LAUNCH = "launch";
        String INJECT_ERROR = "injectError";
        String IMPORT_SCRIPTS = "importScripts";
        String BACKGROUND_UPDATE = "backgroundUpdate";
    }
}
