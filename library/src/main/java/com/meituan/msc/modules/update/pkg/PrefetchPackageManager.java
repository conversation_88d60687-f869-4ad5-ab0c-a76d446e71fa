package com.meituan.msc.modules.update.pkg;

import android.support.annotation.NonNull;

import com.meituan.msc.common.executor.MSCExecutors;
import com.meituan.msc.common.utils.CollectionUtil;
import com.meituan.msc.common.utils.MPConcurrentHashMap;
import com.meituan.msc.common.utils.PathUtil;
import com.meituan.msc.extern.MSCEnvHelper;
import com.meituan.msc.modules.apploader.events.AppLoadException;
import com.meituan.msc.modules.engine.MSCRuntime;
import com.meituan.msc.modules.reporter.MSCLog;
import com.meituan.msc.modules.update.MSCAppModule;
import com.meituan.msc.modules.update.PackageLoadReporter;
import com.meituan.msc.modules.update.PackagePreLoadReporter;
import com.meituan.msc.modules.update.PackageReportBean;
import com.meituan.msc.modules.update.SubPackagePreloadConfig;
import com.meituan.msc.modules.update.bean.AppMetaInfoWrapper;
import com.meituan.msc.modules.update.bean.PackageInfoWrapper;

import java.util.HashSet;
import java.util.Map;
import java.util.Set;

/**
 * 分包预下载
 * https://developers.weixin.qq.com/miniprogram/dev/framework/subpackages/preload.html
 */
public class PrefetchPackageManager {

    private static final String TAG = "PrefetchPackageManager";
    private static volatile PrefetchPackageManager mInstance;

    public final static MPConcurrentHashMap<String, Set<String>> preDownloadPackages = new MPConcurrentHashMap<>();

    public static PrefetchPackageManager getInstance() {
        if (mInstance == null) {
            synchronized (PrefetchPackageManager.class) {
                if (mInstance == null) {
                    mInstance = new PrefetchPackageManager();
                }
            }
        }
        return mInstance;
    }

    private PrefetchPackageManager() {
    }

    public static boolean isPreDownloadPackage(String appId, String packageMd5) {
        Set<String> md5Set = preDownloadPackages.get(appId);
        if (md5Set == null) {
            return false;
        }
        
        return md5Set.contains(packageMd5);
    }

    public void prefetchSubPackage(MSCRuntime runtime, String path, String loadScene) {
        MSCExecutors.submit(new Runnable() {
            @Override
            public void run() {
                MSCAppModule mscAppModule = runtime.getMSCAppModule();
                AppMetaInfoWrapper metaInfo = mscAppModule.getMetaInfo();
                if (metaInfo == null) {
                    MSCLog.i(TAG, "prefetchSubPackage metaInfo null");
                    return;
                }
                Map<String, SubPackagePreloadConfig> preloadRules = runtime.getAppConfigModule().getPreloadRule();
                SubPackagePreloadConfig preloadConfig = preloadRules.get(PathUtil.getPath(path));
                if (preloadConfig == null || CollectionUtil.isEmpty(preloadConfig.packages)
                        || !SubPackagePreloadConfig.isNetWorkNeedDownload(MSCEnvHelper.getContext(), preloadConfig)) {
                    MSCLog.i(TAG, "cant prefetch sub package");
                    return;
                }

                for (String pkgName : preloadConfig.packages) {
                    PackageInfoWrapper packageInfo = metaInfo.getSubPackageByName(pkgName);
                    if (packageInfo == null) {
                        MSCLog.i(TAG, "prefetchSubPackage packageInfo null", pkgName);
                        continue;
                    }
                    final long startLoadPackageTime = System.currentTimeMillis();
                    PackageLoadManager.getInstance().loadPackageWithInfo(runtime.getPerfEventRecorder(),
                            packageInfo, false, PackageLoadManager.CheckScene.PREDOWNLOAD, loadScene, new PackageLoadCallback<PackageInfoWrapper>() {
                                @Override
                                public void onSuccess(@NonNull PackageInfoWrapper data) {
                                    MSCLog.i(TAG, "prefetchSubPackage succeed", data);

                                    // 用于上报预下载成功率维度
                                    cachePreDownloadPackages(data.appId, data);

                                    runtime.getPerformanceManager().onDownloadPackage(data);
                                    PackageReportBean packageReportBean = new PackageReportBean.Builder()
                                            .setMscAppId(runtime.getAppId())
                                            .setDdLoadPhaseData(data.getDDLoadPhaseData())
                                            .setSourceFrom(PackagePreLoadReporter.SOURCE_FROM_TYPE_PREDOWNLOAD)
                                            .setMscAppVersion(runtime.getMSCAppModule().getMSCAppVersion())
                                            .setLoadType(data.isFromNet() ? PackageLoadReporter.LoadType.NETWORK : PackageLoadReporter.LoadType.LOCAL)
                                            .setPkgType(packageInfo.getPkgTypeString())
                                            .setPkgName(pkgName)
                                            .build();
                                    PackagePreLoadReporter.create().reportLoadPackageSuccessDuration(packageReportBean, System.currentTimeMillis() - startLoadPackageTime);
                                    PackagePreLoadReporter.create().onLoadPackageSuccess(packageReportBean);
                                }

                                @Override
                                public void onFail(String errMsg, AppLoadException error) {
                                    MSCLog.e(TAG, error, "prefetchSubPackage failed", errMsg);
                                    PackagePreLoadReporter.create().onLoadPackageFailed(
                                            new PackageReportBean.Builder()
                                                    .setMscAppId(runtime.getAppId())
                                                    .setSourceFrom(PackagePreLoadReporter.SOURCE_FROM_TYPE_PREDOWNLOAD)
                                                    .setMscAppVersion(runtime.getMSCAppModule().getMSCAppVersion())
                                                    .setDdLoadPhaseData(error != null ? error.getDDPhaseData() : null)
                                                    .setPkgType(packageInfo.getPkgTypeString())
                                                    .build(),
                                            error);
                                }
                            });
                }
            }
        });
    }

    /**
     * 缓存预下载过的包数据
     *
     * @param appId appId
     * @param data  packageInfoWrapper
     */
    public static void cachePreDownloadPackages(String appId, PackageInfoWrapper data) {
        Set<String> md5Set = preDownloadPackages.get(appId);
        if (md5Set == null) {
            md5Set = new HashSet<>();
            preDownloadPackages.put(appId, md5Set);
        }
        md5Set.add(data.getMd5());
    }
}
