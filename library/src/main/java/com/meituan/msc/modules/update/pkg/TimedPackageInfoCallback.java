package com.meituan.msc.modules.update.pkg;

import android.support.annotation.Nullable;

import com.meituan.android.mercury.msc.adaptor.callback.MSCPackageInfoCallback;
import com.meituan.android.mercury.msc.adaptor.core.MSCLoadExeption;
import com.meituan.met.mercury.load.core.DDResource;

public abstract class TimedPackageInfoCallback implements MSCPackageInfoCallback {
    private final MSCPackageInfoCallback callback;
    private boolean isTaskTrigger;
    private long startTimeMill;

    TimedPackageInfoCallback(MSCPackageInfoCallback callback) {
        this.callback = callback;
    }

    public void setTaskTrigger(boolean taskTrigger, long startTimeMill) {
        this.isTaskTrigger = taskTrigger;
        this.startTimeMill = startTimeMill;
    }

    @Override
    public void onSuccess(@Nullable DDResource ddResource) {
        if (isTaskTrigger) {
            onTaskTriggerSuccess(ddResource, startTimeMill);
        }
        if (callback != null) {
            callback.onSuccess(ddResource);
        }
    }

    @Override
    public void onFail(MSCLoadExeption mscLoadExeption) {
        if (isTaskTrigger) {
            onTaskTriggerFailed(mscLoadExeption, startTimeMill);
        }
        if (callback != null) {
            callback.onFail(mscLoadExeption);
        }
    }

    protected void onTaskTriggerSuccess(@Nullable DDResource ddResource, long startTimeMill) {

    }

    protected void onTaskTriggerFailed(MSCLoadExeption mscLoadExeption, long startTimeMill) {

    }
}
