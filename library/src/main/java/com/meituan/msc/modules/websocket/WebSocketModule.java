/*
 * Copyright (c) Facebook, Inc. and its affiliates.
 *
 * This source code is licensed under the MIT license found in the
 * LICENSE file in the root directory of this source tree.
 */

package com.meituan.msc.modules.websocket;

import android.support.annotation.Nullable;

import com.meituan.msc.common.utils.Interceptors;
import com.meituan.msc.jse.common.ReactConstants;
import com.meituan.msc.jse.modules.core.JSDeviceEventEmitter;
import com.meituan.msc.modules.engine.AppService;
import com.meituan.msc.modules.engine.MSCRuntime;
import com.meituan.msc.modules.manager.MSCEvent;
import com.meituan.msc.modules.manager.MSCMethod;
import com.meituan.msc.modules.manager.MSCModule;
import com.meituan.msc.modules.manager.MSCSubscriber;
import com.meituan.msc.modules.manager.ModuleName;
import com.meituan.msc.modules.network.ForwardingCookieHandler;
import com.meituan.msc.modules.reporter.MSCLog;
import com.meituan.msc.modules.service.EngineStatus;
import com.meituan.msc.modules.service.JSCServiceEngine;

import org.json.JSONArray;
import org.json.JSONException;
import org.json.JSONObject;

import java.io.IOException;
import java.net.URI;
import java.net.URISyntaxException;
import java.util.HashMap;
import java.util.Iterator;
import java.util.List;
import java.util.Map;
import java.util.concurrent.ConcurrentHashMap;
import java.util.concurrent.TimeUnit;

import okhttp3.OkHttpClient;
import okhttp3.Request;
import okhttp3.Response;
import okhttp3.WebSocket;
import okhttp3.WebSocketListener;
import okio.ByteString;

@ModuleName(name = "WebSocketModule")
public final class WebSocketModule extends MSCModule {
    public static final String TAG = WebSocketModule.class.getSimpleName();

    public interface ContentHandler {
        void onMessage(String text, JSONObject params);

        void onMessage(ByteString byteString, JSONObject params);
    }

    private final MSCSubscriber engineSubscriber = new MSCSubscriber<EngineStatus>() {
        @Override
        public void onReceive(MSCEvent<EngineStatus> event) {
            if (event.getData() == EngineStatus.Released) {
                onCatalystInstanceDestroy();
            }
        }
    };

    private final Map<Integer, WebSocket> mWebSocketConnections = new ConcurrentHashMap<>();
    private final Map<Integer, ContentHandler> mContentHandlers = new ConcurrentHashMap<>();

    private ForwardingCookieHandler mCookieHandler;

    public WebSocketModule() {
        mCookieHandler = new ForwardingCookieHandler();
    }

    private void sendEvent(String eventName, JSONObject params) {
        getRuntime().getModule(AppService.class).getJsExecutor()
                .getJSModule(JSDeviceEventEmitter.class)
                .emit(eventName, params);
    }

    public void setContentHandler(final int id, final ContentHandler contentHandler) {
        if (contentHandler != null) {
            mContentHandlers.put(id, contentHandler);
        } else {
            mContentHandlers.remove(id);
        }
    }

    @Override
    public void onRuntimeAttached(MSCRuntime runtime) {
        super.onRuntimeAttached(runtime);
        getRuntime().subscribe(JSCServiceEngine.MSC_EVENT_ENGINE_STATUS_CHANGED, engineSubscriber);
    }

    @MSCMethod
    public void connect(
            final String url,
            @Nullable final JSONArray protocols,
            @Nullable final JSONObject options,
            final double socketID) {
        final int id = (int) socketID;
        OkHttpClient.Builder clientBuilder = new OkHttpClient.Builder();
        clientBuilder.addInterceptor(Interceptors.OkHttp.getRiskInterceptor());
        OkHttpClient client = clientBuilder.connectTimeout(10, TimeUnit.SECONDS)
                .writeTimeout(10, TimeUnit.SECONDS)
                .readTimeout(0, TimeUnit.MINUTES) // Disable timeouts for read
                .build();

        Request.Builder builder = new Request.Builder().tag(id).url(url);

        String cookie = getCookie(url);
        if (cookie != null) {
            builder.addHeader("Cookie", cookie);
        }

        boolean hasOriginHeader = false;

        if (options != null
                && options.has("headers")
                && (options.opt("headers") instanceof JSONObject)) {

            JSONObject headers = options.optJSONObject("headers");
            Iterator<String> iterator = headers.keys();

            while (iterator.hasNext()) {
                String key = iterator.next();
                if (headers.opt(key) instanceof String) {
                    if (key.equalsIgnoreCase("origin")) {
                        hasOriginHeader = true;
                    }
                    builder.addHeader(key, headers.optString(key));
                } else {
                    MSCLog.w(ReactConstants.TAG, "Ignoring: requested " + key + ", value not a string");
                }
            }
        }

        if (!hasOriginHeader) {
            builder.addHeader("origin", getDefaultOrigin(url));
        }

        if (protocols != null && protocols.length() > 0) {
            StringBuilder protocolsValue = new StringBuilder("");
            for (int i = 0; i < protocols.length(); i++) {
                String v = protocols.optString(i).trim();
                if (!v.isEmpty() && !v.contains(",")) {
                    protocolsValue.append(v);
                    protocolsValue.append(",");
                }
            }
            if (protocolsValue.length() > 0) {
                protocolsValue.replace(protocolsValue.length() - 1, protocolsValue.length(), "");
                builder.addHeader("Sec-WebSocket-Protocol", protocolsValue.toString());
            }
        }

        client.newWebSocket(
                builder.build(),
                new WebSocketListener() {

                    @Override
                    public void onOpen(WebSocket webSocket, Response response) {
                        mWebSocketConnections.put(id, webSocket);
                        JSONObject params = new JSONObject();
                        try {
                            params.put("id", id);
                            params.put("protocol", response.header("Sec-WebSocket-Protocol", ""));
                        } catch (JSONException e) {
                            e.printStackTrace();
                        }
                        sendEvent("websocketOpen", params);
                    }

                    @Override
                    public void onClosing(WebSocket websocket, int code, String reason) {
                        websocket.close(code, reason);
                    }

                    @Override
                    public void onClosed(WebSocket webSocket, int code, String reason) {
                        JSONObject params = new JSONObject();
                        try {
                            params.put("id", id);
                            params.put("code", code);
                            params.put("reason", reason);
                        } catch (JSONException e) {
                            e.printStackTrace();
                        }
                        sendEvent("websocketClosed", params);
                    }

                    @Override
                    public void onFailure(WebSocket webSocket, Throwable t, Response response) {
                        notifyWebSocketFailed(id, t.getMessage());
                    }

                    @Override
                    public void onMessage(WebSocket webSocket, String text) {
                        JSONObject params = new JSONObject();
                        try {
                            params.put("id", id);
                            params.put("type", "text");
                            ContentHandler contentHandler = mContentHandlers.get(id);
                            if (contentHandler != null) {
                                contentHandler.onMessage(text, params);
                            } else {
                                params.put("data", text);
                            }
                        } catch (JSONException e) {
                            e.printStackTrace();
                        }
                        sendEvent("websocketMessage", params);
                    }

                    @Override
                    public void onMessage(WebSocket webSocket, ByteString bytes) {
                        JSONObject params = new JSONObject();
                        try {
                            params.put("id", id);
                            params.put("type", "binary");

                            ContentHandler contentHandler = mContentHandlers.get(id);
                            if (contentHandler != null) {
                                contentHandler.onMessage(bytes, params);
                            } else {
                                String text = bytes.base64();
                                params.put("data", text);
                            }
                        } catch (JSONException e) {
                            e.printStackTrace();
                        }
                        sendEvent("websocketMessage", params);
                    }
                });

        // Trigger shutdown of the dispatcher's executor so this process can exit cleanly
        client.dispatcher().executorService().shutdown();
    }

    @MSCMethod
    public void close(double code, String reason, double socketID) {
        int id = (int) socketID;
        WebSocket client = mWebSocketConnections.get(id);
        if (client == null) {
            // WebSocket is already closed
            // Don't do anything, mirror the behaviour on web
            return;
        }
        try {
            client.close((int) code, reason);
            mWebSocketConnections.remove(id);
            mContentHandlers.remove(id);
        } catch (Exception e) {
            MSCLog.e(ReactConstants.TAG, e, "Could not close WebSocket connection for id " + id);
        }
    }

    @MSCMethod
    public void send(String message, double socketID) {
        final int id = (int) socketID;
        WebSocket client = mWebSocketConnections.get(id);
        if (client == null) {
            // This is a programmer error -- display development warning
            JSONObject params = new JSONObject();
            try {
                params.put("id", id);
                params.put("message", "client is null");
            } catch (JSONException e) {
                e.printStackTrace();
            }
            sendEvent("websocketFailed", params);
            params = new JSONObject();
            try {
                params.put("id", id);
                params.put("code", 0);
                params.put("reason", "client is null");
            } catch (JSONException e) {
                e.printStackTrace();
            }
            sendEvent("websocketClosed", params);
            mWebSocketConnections.remove(id);
            mContentHandlers.remove(id);
            return;
        }
        try {
            client.send(message);
        } catch (Exception e) {
            notifyWebSocketFailed(id, e.getMessage());
        }
    }

    @MSCMethod
    public void sendBinary(String base64String, double socketID) {
        final int id = (int) socketID;
        WebSocket client = mWebSocketConnections.get(id);
        if (client == null) {
            // This is a programmer error -- display development warning
            JSONObject params = new JSONObject();
            try {
                params.put("id", id);
                params.put("message", "client is null");
            } catch (JSONException e) {
                e.printStackTrace();
            }
            sendEvent("websocketFailed", params);
            params = new JSONObject();
            try {
                params.put("id", id);
                params.put("code", 0);
                params.put("reason", "client is null");
            } catch (JSONException e) {
                e.printStackTrace();
            }
            sendEvent("websocketClosed", params);
            mWebSocketConnections.remove(id);
            mContentHandlers.remove(id);
            return;
        }
        try {
            client.send(ByteString.decodeBase64(base64String));
        } catch (Exception e) {
            notifyWebSocketFailed(id, e.getMessage());
        }
    }

    public void sendBinary(ByteString byteString, int id) {
        WebSocket client = mWebSocketConnections.get(id);
        if (client == null) {
            // This is a programmer error -- display development warning
            JSONObject params = new JSONObject();
            try {
                params.put("id", id);
                params.put("message", "client is null");
            } catch (JSONException e) {
                e.printStackTrace();
            }
            sendEvent("websocketFailed", params);
            params = new JSONObject();
            try {
                params.put("id", id);
                params.put("code", 0);
                params.put("reason", "client is null");
            } catch (JSONException e) {
                e.printStackTrace();
            }
            sendEvent("websocketClosed", params);
            mWebSocketConnections.remove(id);
            mContentHandlers.remove(id);
            return;
        }
        try {
            client.send(byteString);
        } catch (Exception e) {
            notifyWebSocketFailed(id, e.getMessage());
        }
    }

    @MSCMethod
    public void ping(double socketID) {
        final int id = (int) socketID;
        WebSocket client = mWebSocketConnections.get(id);
        if (client == null) {
            // This is a programmer error -- display development warning
            JSONObject params = new JSONObject();
            try {
                params.put("id", id);
                params.put("message", "client is null");
            } catch (JSONException e) {
                e.printStackTrace();
            }
            sendEvent("websocketFailed", params);
            params = new JSONObject();
            try {
                params.put("id", id);
                params.put("code", 0);
                params.put("reason", "client is null");
            } catch (JSONException e) {
                e.printStackTrace();
            }
            sendEvent("websocketClosed", params);
            mWebSocketConnections.remove(id);
            mContentHandlers.remove(id);
            return;
        }
        try {
            client.send(ByteString.EMPTY);
        } catch (Exception e) {
            notifyWebSocketFailed(id, e.getMessage());
        }
    }

    private void notifyWebSocketFailed(int id, String message) {
        JSONObject params = new JSONObject();
        try {
            params.put("id", id);
            params.put("message", message);
        } catch (JSONException e) {
            e.printStackTrace();
        }
        sendEvent("websocketFailed", params);
    }

    /**
     * Get the default HTTP(S) origin for a specific WebSocket URI
     *
     * @param uri
     * @return A string of the endpoint converted to HTTP protocol (http[s]://host[:port])
     */
    private static String getDefaultOrigin(String uri) {
        try {
            String defaultOrigin;
            String scheme = "";

            URI requestURI = new URI(uri);
            switch (requestURI.getScheme()) {
                case "wss":
                    scheme += "https";
                    break;
                case "ws":
                    scheme += "http";
                    break;
                case "http":
                case "https":
                    scheme += requestURI.getScheme();
                    break;
                default:
                    break;
            }

            if (requestURI.getPort() != -1) {
                defaultOrigin =
                        String.format("%s://%s:%s", scheme, requestURI.getHost(), requestURI.getPort());
            } else {
                defaultOrigin = String.format("%s://%s", scheme, requestURI.getHost());
            }

            return defaultOrigin;
        } catch (URISyntaxException e) {
            throw new IllegalArgumentException("Unable to set " + uri + " as default origin header");
        }
    }

    /**
     * Get the cookie for a specific domain
     *
     * @param uri
     * @return The cookie header or null if none is set
     */
    private String getCookie(String uri) {
        try {
            URI origin = new URI(getDefaultOrigin(uri));
            Map<String, List<String>> cookieMap = mCookieHandler.get(origin, new HashMap());
            List<String> cookieList = cookieMap.get("Cookie");

            if (cookieList == null || cookieList.isEmpty()) {
                return null;
            }

            return cookieList.get(0);
        } catch (URISyntaxException | IOException e) {
            throw new IllegalArgumentException("Unable to get cookie from " + uri);
        }
    }

    @MSCMethod
    public void addListener(String eventName) {}

    @MSCMethod
    public void removeListeners(double count) {}

    // [MRN60: chendacai] 如果引擎销毁之前的时候没有在JS层主动关闭 WebSocket，则在这里关闭下
    public void onCatalystInstanceDestroy() {
        if (mWebSocketConnections.size() > 0) {
            for (WebSocket client : mWebSocketConnections.values()) {
                if (client != null) {
                    try {
                        client.cancel(); // close connection
                    } catch (Throwable ignore) { }
                }
            }
            mWebSocketConnections.clear();
            mContentHandlers.clear();
        }
    }

    @Override
    public void onDestroy() {
        super.onDestroy();
        getRuntime().unsubscribe(engineSubscriber);
    }
}