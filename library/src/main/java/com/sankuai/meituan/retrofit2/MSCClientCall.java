package com.sankuai.meituan.retrofit2;


import static java.net.HttpURLConnection.HTTP_OK;

import android.annotation.SuppressLint;
import android.text.TextUtils;
import android.util.Log;

import com.sankuai.meituan.retrofit2.cache.Cache;
import com.sankuai.meituan.retrofit2.exception.CacheNotFoundException;
import com.sankuai.meituan.retrofit2.ext.ResponseExt;
import com.sankuai.meituan.retrofit2.raw.RawCall;
import com.sankuai.meituan.retrofit2.raw.RawResponse;

import java.io.IOException;
import java.io.InputStream;
import java.util.ArrayList;
import java.util.List;
import java.util.concurrent.Executor;


/**
 *
 * 为了复用美团基建 使用CallFactory
 * 主要剥离了Retrofit的注解解释逻辑
 *
 * Modified for MMP by letty 2020/6/23
 *
 *
 * Created by liangcan on 16/6/13.
 */
public final class MSCClientCall implements Call<ResponseBody> {
    private final List<Interceptor> clientInterceptors;
//    private final List<Interceptor> defInterceptors;
    private final Executor httpExecutor;
    private final RawCall.Factory callFactory;

    private volatile boolean canceled;
    // All guarded by this.
    private RawCall rawCall;
    private Request originalRequest;
    private Throwable creationFailure; // Either a RuntimeException or IOException.
    private boolean executed;
    private Cache cache;
    private Request realRequest;
    private long convertElapse = -1;

    public MSCClientCall(RawCall.Factory callFactory, List<Interceptor> interceptors) {
        this(callFactory, interceptors, null);
    }

    public MSCClientCall(RawCall.Factory callFactory, List<Interceptor> interceptors, Cache cache) {
        this.callFactory = callFactory;
        this.clientInterceptors = interceptors;
//        this.defInterceptors = defInterceptors;
//        this.httpExecutor = httpExecutor;
        this.httpExecutor = Platform.get().defaultHttpExecutor();
        this.cache = cache;
    }

    public MSCClientCall setRequest(Request request) {
        this.originalRequest = request;
        return this;
    }

    @SuppressWarnings("CloneDoesntCallSuperClone")
    // We are a final type & this saves clearing state.
    @Override
    public Call clone() {
//        return new FakeMMPClientCall(callFactory, clientInterceptors, defInterceptors, cache);
        return new MSCClientCall(callFactory, clientInterceptors, cache);
    }


    @Override
    public synchronized Request request() {
        if (originalRequest != null) {
            return originalRequest;
        }
        return null;
//        if (creationFailure != null) {
//            if (creationFailure instanceof IOException) {
//                throw new RuntimeException("Unable to create request.", creationFailure);
//            } else {
//                throw (RuntimeException) creationFailure;
//            }
//        }
//        try {
//            return originalRequest = serviceMethod.toRequest(args);
//        } catch (RuntimeException e) {
//            creationFailure = e;
//            throw e;
//        } catch (IOException e) {
//            creationFailure = e;
//            throw new RuntimeException("Unable to create request.", e);
//        }
    }

    @Override
    public void enqueue(final Callback<ResponseBody> callback) {
        if (callback == null) {
            throw new NullPointerException("callback == null");
        }
        final long lExecuteTime = System.currentTimeMillis();
        httpExecutor.execute(new Runnable() {
            @Override
            public void run() {
                Throwable failure = creationFailure;
                Request request = originalRequest;

                synchronized (MSCClientCall.this) {
                    if (executed) {
                        throw new IllegalStateException("Already executed.");
                    }
                    executed = true;

                    if (request == null && failure == null) {
                        try {
                            //                    request = originalRequest = serviceMethod.toRequest(args);
                            request = originalRequest;
                        } catch (RuntimeException e) {
                            failure = creationFailure = e;
                        } catch (Throwable t) {
                            failure = t;
                        }
                    }
                }

                if (failure != null) {
                    callback.onFailure(MSCClientCall.this, failure);
                    Retrofit.RequestCallbackDispatcher.onError(MSCClientCall.this, realRequest, failure);
                    return;
                }

                Response<ResponseBody> response;

                String name;
                try {
                    name = findPath(request.url());
                } catch (Throwable ignored) {
                    name = Platform.RUNNING_THREAD_NAME;
                }
                Thread.currentThread().setName(Platform.THREAD_PREFIX + name);
                ResponseExt responseExt = ResponseExt.create();
                responseExt.setColorTags(request.getColorTags());
                responseExt.setStartTime(lExecuteTime);
                try {
                    request = request.newBuilder().addHeader("retrofit_exec_time", String.valueOf(lExecuteTime)).build();
                    RawResponse rawResponse = getResponseWithInterceptorChain(request);
                    response = parseResponse(rawResponse);
                    callback.onResponse(MSCClientCall.this, response);
                    Retrofit.RequestCallbackDispatcher.onSuccess(MSCClientCall.this, realRequest, response, convertElapse);
                } catch (Throwable t) {
                    callback.onFailure(MSCClientCall.this, t);
                    Retrofit.RequestCallbackDispatcher.onError(MSCClientCall.this, realRequest, t);
                } finally {
                    Thread.currentThread().setName(Platform.IDLE_THREAD_NAME);
                    responseExt.setEndTime(System.currentTimeMillis());
                    ResponseExt.remove();
                }
            }
        });
    }

    @Override
    public synchronized boolean isExecuted() {
        return executed;
    }

    @SuppressWarnings("NP")
    @Override
    public Response<ResponseBody> execute() throws IOException {
        Request request;
        long lExecuteTime = System.currentTimeMillis();
        synchronized (this) {
            if (executed) {
                throw new IllegalStateException("Already executed.");
            }
            executed = true;

            if (creationFailure != null) {
                if (creationFailure instanceof IOException) {
                    throw (IOException) creationFailure;
                } else {
                    throw (RuntimeException) creationFailure;
                }
            }

            request = originalRequest;
            if (request == null) {
                try {
//                    request = originalRequest = serviceMethod.toRequest(args);
                    request = originalRequest;
                } catch (RuntimeException e) {
                    creationFailure = e;
                    throw e;
                }
            }
        }

        Response<ResponseBody> response;
        ResponseExt responseExt = ResponseExt.create();
        responseExt.setStartTime(lExecuteTime);
        try {
            request = request.newBuilder().addHeader("retrofit_exec_time", String.valueOf(lExecuteTime)).build();
            RawResponse rawResponse = getResponseWithInterceptorChain(request);
            response = parseResponse(rawResponse);
            Retrofit.RequestCallbackDispatcher.onSuccess(MSCClientCall.this, realRequest, response, convertElapse);
        } catch (Throwable t) {
            Retrofit.RequestCallbackDispatcher.onError(MSCClientCall.this, realRequest, t);
            throw t;
        } finally {
            responseExt.setEndTime(System.currentTimeMillis());
            ResponseExt.remove();
        }
        return response;
    }

    @Override
    public void cancel() {
        canceled = true;

        RawCall call;
        synchronized (this) {
            call = rawCall;
        }
        if (call != null) {
            try {
                call.cancel();
            } catch (Throwable ignored) {
            }
        }
    }

    @Override
    public boolean isCanceled() {
        return canceled;
    }

    private String findPath(String url) {
        int hostStart = url.indexOf("//");
        hostStart = (hostStart < 0) ? 0 : hostStart + 2;
        int pathStart = url.indexOf("/", hostStart);
        pathStart = (pathStart < 0) ? hostStart : pathStart + 1;
        int queryStart = url.indexOf("?", pathStart);
        queryStart = (queryStart < 0) ? url.length() : queryStart;
        return url.substring(pathStart, queryStart);
    }

    private RawResponse getResponseWithInterceptorChain(Request request) throws IOException {
        List<Interceptor> interceptors = new ArrayList<>();
        interceptors.addAll(clientInterceptors);
//        interceptors.addAll(defInterceptors);
        interceptors.add(new BridgeInterceptor());
        if (request.isOpenGzip()) {
            interceptors.add(new GzipRequestInterceptor());
        }
        //TODO for test
//        interceptors.add(new Interceptor() {
//            int i = 0;
//            @Override
//            public RawResponse intercept(Chain chain) throws IOException {
//                HeraTrace.w("application interceptor: " + i++ + "- " + request.url());
//                return chain.proceed(chain.request());
//            }
//        });



        ApplicationInterceptorChain chain = new ApplicationInterceptorChain(0, request, interceptors);
        RawResponse rawResponse = chain.proceed(request);
        if (null != rawResponse && !LogUtils.isDiscardedUrl(rawResponse.url())) {
            RawResponseSubject rawResponseSubject = new RawResponseSubject(rawResponse);
            InputStreamObserverImpl inputStreamObserver = new InputStreamObserverImpl();
            inputStreamObserver.setUrl(rawResponse.url());
            rawResponseSubject.setInputStreamObserver(inputStreamObserver);
            return rawResponseSubject;
        }
        return rawResponse;
    }

    private RawResponse getResponse(Request request, boolean hasDowngrade) throws IOException {
        boolean isHttps = (request.url() != null) && request.url().contains("https://");
        RawResponse response = null;
        this.realRequest = request;
        try {
            if (!hasDowngrade && request.isAutoDowngrade() && !isHttps) {
                Log.w("retrofit-mt", "Warning: the request has downgrade annotation but is not https!");
            }

            if (request.origin() != null && TextUtils.isEmpty(request.origin().key())) {
                request.origin().setKey(request.url());
            }

            CacheOrigin.Mode originMode = (request.origin() == null ? CacheOrigin.Mode.NET : request.origin().mode());
            boolean haveCache = isHaveCache();
            if (!haveCache || originMode == CacheOrigin.Mode.NET) {
                response = getNetResponse(request, hasDowngrade);
            } else if (originMode == CacheOrigin.Mode.LOCAL) {
                response = getLocalResponse(request, hasDowngrade);
            } else if (originMode == CacheOrigin.Mode.NET_PREFERRED) {
                response = getNetLocalResponse(request, hasDowngrade);
            } else if (originMode == CacheOrigin.Mode.LOCAL_PREFERRED) {
                response = getLocalNetResponse(request, hasDowngrade);
            } else {
                response = getNetResponse(request, hasDowngrade);
            }
        } catch (IOException e) {
            if (!hasDowngrade && request.isAutoDowngrade() && isHttps) {
                Request newRequest = request.newBuilder().url(request.url().
                        replace("https://", "http://")).build();
                response = getResponse(newRequest, true);
            } else {
                throw e;
            }
        }

        return response;
    }

    private RawResponse getNetResponse(Request request, boolean hasDowngrade) throws IOException {
        RawResponse response = getRealResponse(request, hasDowngrade);
        if (request.origin() != null && request.origin().saveNet()) {
            return putCacheResponse(request, response);
        }
        return response;
    }


    private RawResponse getLocalResponse(Request request, boolean hasDowngrade) throws IOException {
        RawResponse response = getCacheResponse(request);
        return response;
    }

    private RawResponse getNetLocalResponse(Request request, boolean hasDowngrade) throws IOException {
        RawResponse netResponse = null;
        try {
            RawResponse realResponse = getRealResponse(request, hasDowngrade);
            netResponse = putCacheResponse(request, realResponse);
        } catch (Throwable e) {

        }
        if ((netResponse != null) && Utils.isSuccessfulHttp(netResponse.code())) {
            return netResponse;
        } else {
            RawResponse localResponse = null;
            try {
                localResponse = getCacheResponse(request);
            } catch (Throwable e) {

            }
            if (localResponse != null && validateCode(localResponse.code())) {
                return localResponse;
            }
        }
        return netResponse;
    }

    private RawResponse getLocalNetResponse(Request request, boolean hasDowngrade) throws IOException {
        RawResponse response = null;
        try {
            response = getCacheResponse(request);
        } catch (Throwable e) {

        }
        if (response == null || !validateCode(response.code())) {
            response = getRealResponse(request, hasDowngrade);
            if (request.origin() != null && request.origin().saveNet()) {
                return putCacheResponse(request, response);
            }
        }
        return response;
    }

    protected RawResponse getCacheResponse(Request request) throws IOException {
        RawResponse response = cache.readResponse(request);
        if (response == null) {
            throw new CacheNotFoundException("getting cache response return null");
        }
        return response;
    }

    protected RawResponse putCacheResponse(Request request, RawResponse response) throws IOException {
        if (validateCode(response.code())) {
            return cache.writeResponse(request, response);
        }
        return response;
    }

    protected boolean isHaveCache() {
        return cache != null;
    }

    private boolean validateCode(int code) {
        if (code == HTTP_OK) {
            return true;
        }
        return false;
    }

    @SuppressLint("LogUse")
    private RawResponse getRealResponse(Request request, boolean hasDowngrade) throws IOException {
        RawCall call;
        synchronized (this) {
            rawCall = call = callFactory.get(request);
        }
        if (call == null) {
            throw new NullPointerException("Call.Factory returned null.");
        }
        if (canceled) {
            call.cancel();
        }
        RawResponse rawResponse = call.execute();
        return rawResponse;
    }

    private Response<ResponseBody> parseResponse(RawResponse rawResponse) throws IOException {
        long lParseStartTime = System.currentTimeMillis();
        ResponseBody rawBody = rawResponse.body();
        // Remove the body's source (the only stateful object) so we can pass the response along.
        rawResponse = new NoContentBodyRawResponse(rawResponse);

        int code = rawResponse.code();
        if (!Utils.isSuccessfulHttp(code)) {
            try {
                // Buffer the entire body to avoid future I/O.
                ResponseBody bufferedBody = Utils.buffer(rawBody);
                return Response.error(bufferedBody, rawResponse);
            } finally {
                rawBody.close();
            }
        }

        if (code == 204 || code == 205) {
            rawBody.close();
            return Response.success(null, rawResponse);
        }

        ResponseBody body;
        try {
            // Buffer the entire body to avoid future I/O.
            body = Utils.buffer(rawBody);
        } finally {
            rawBody.close();
        }
//
//        try {
//            body = serviceMethod.toResponse(rawBody);
//            Retrofit.RequestMonitorDispatcher.onConvertSuccess(FakeMMPClientCall.this, realRequest);
//            convertElapse = System.currentTimeMillis() - lParseStartTime;
//        } catch (Throwable t) {
//            ConversionException exception = new ConversionException("Conversion failed", t);
//            Retrofit.RequestMonitorDispatcher.onConvertError(FakeMMPClientCall.this, realRequest, exception);
//            throw exception;
//        }
        return Response.success(body, rawResponse);
    }

    private static class NoContentBodyRawResponse implements RawResponse {
        RawResponse realResponse;
        private ResponseBody body;

        NoContentBodyRawResponse(RawResponse rawResponse) {
            realResponse = rawResponse;
            body = rawResponse.body();
        }

        @Override
        public String url() {
            return realResponse.url();
        }

        @Override
        public int code() {
            return realResponse.code();
        }

        @Override
        public String reason() {
            return realResponse.reason();
        }

        @Override
        public List<Header> headers() {
            return realResponse.headers();
        }

        @Override
        public ResponseBody body() {
            return new NoContentResponseBody(body.contentType(), body.contentLength());
        }

        static final class NoContentResponseBody extends ResponseBody {
            private final String contentType;
            private final long contentLength;

            NoContentResponseBody(String contentType, long contentLength) {
                this.contentType = contentType;
                this.contentLength = contentLength;
            }

            @Override
            public String contentType() {
                return contentType;
            }

            @Override
            public long contentLength() {
                return contentLength;
            }

            @Override
            public InputStream source() {
                throw new IllegalStateException("Cannot read raw response body of a converted body.");
            }
        }
    }

    private class ApplicationInterceptorChain implements Interceptor.Chain {
        private final int index;
        private Request request;
        private List<Interceptor> interceptors;

        ApplicationInterceptorChain(int index, Request request, List<Interceptor> interceptors) {
            this.index = index;
            this.request = request;
            this.interceptors = interceptors;
        }

        @Override
        public Request request() {
            return request;
        }

        @Override
        public RawResponse proceed(Request request) throws IOException {
            if (index < interceptors.size()) {
                ApplicationInterceptorChain chain = new ApplicationInterceptorChain(index + 1, request, interceptors);
                return interceptors.get(index).intercept(chain);
            } else {
                return getResponse(request, false);
            }
        }
    }
}
