package com.sankuai.meituan.retrofit2;

import android.support.annotation.Nullable;

import java.nio.charset.Charset;


/**
 * Created by letty on 2020-06-22.
 **/
public class MSCRequestHelper {
    public static final Charset UTF_8 = Charset.forName("UTF-8");

    /**
     * Returns a new request body that transmits {@code content}. If {@code contentType} is non-null
     * and lacks a charset, this will use UTF-8.
     */
    public static RequestBody createRequestBody(@Nullable MediaType contentType, String content) {
        Charset charset = UTF_8;
        if (contentType != null) {
            charset = contentType.charset();
            if (charset == null) {
                charset = UTF_8;
//                contentType = MediaType.parse(contentType + "; charset=utf-8");
            }
        }
        byte[] bytes = content.getBytes(charset);
        return RequestBodyBuilder.build(contentType, bytes);
    }
}
