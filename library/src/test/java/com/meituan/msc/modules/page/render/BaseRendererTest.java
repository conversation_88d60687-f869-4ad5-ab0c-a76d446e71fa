package com.meituan.msc.modules.page.render;

import android.content.Intent;
import android.os.SystemClock;
import android.text.TextUtils;
import android.view.View;

import com.meituan.msc.common.framework.interfaces.PageEventListener;
import com.meituan.msc.common.utils.IntentUtil;
import com.meituan.msc.common.utils.MSCTestUtils;
import com.meituan.msc.modules.container.ContainerReporter;
import com.meituan.msc.modules.container.IContainerDelegate;
import com.meituan.msc.modules.container.IMSCContainer;
import com.meituan.msc.modules.engine.MSCHornRollbackConfig;
import com.meituan.msc.modules.engine.MSCRuntime;
import com.meituan.msc.modules.page.UserReporter;
import com.meituan.msc.modules.reporter.preformance.PerformanceManager;
import com.meituan.msc.modules.reporter.prexception.AppPageState;
import com.meituan.msc.modules.update.MSCAppModule;
import com.meituan.msc.util.perf.PerfEventRecorder;
import com.meituan.msc.util.perf.PerfTrace;
import com.meituan.msc.util.perf.TraceEvent;
import com.meituan.msi.bean.BroadcastEvent;
import com.meituan.msi.bean.EventType;
import com.meituan.msi.lifecycle.IPageLifecycleCallback;

import org.junit.Before;
import org.junit.Test;
import org.junit.runner.RunWith;
import org.mockito.Mock;
import org.mockito.Mockito;
import org.mockito.MockitoAnnotations;
import org.powermock.api.mockito.PowerMockito;
import org.powermock.core.classloader.annotations.PrepareForTest;
import org.powermock.modules.junit4.PowerMockRunner;
import org.powermock.reflect.Whitebox;

import java.util.HashMap;

import static org.mockito.Mockito.verify;
import static org.mockito.Mockito.when;

@RunWith(PowerMockRunner.class)
@PrepareForTest({TextUtils.class, IntentUtil.class, UserReporter.class, SystemClock.class, PerfTrace.class})
public class BaseRendererTest {
    @Mock
    private MSCAppModule mMSCAppModuleMock;
    @Mock
    private BaseRenderer.BasePageData pageDataMock;
    @Mock
    private AppPageReporter appPageReporterMock;
    @Mock
    private ContainerReporter containerReporterMock;
    @Mock
    private IContainerDelegate containerDelegateMock;
    @Mock
    private IMSCContainer mMSCContainerMock;
    @Mock
    private Intent intentMock;
    @Mock
    private UserReporter userReporterMock;
    @Mock
    private MSCRuntime mscRuntimeMock;

    private BaseRenderer baseRenderer;

    @Before
    public void setup() {
        MSCTestUtils.mockTextUtils();
        // MSCTestUtils.mockSystemClock();
        MockitoAnnotations.initMocks(this);
        pageDataMock.appPageReporter = appPageReporterMock;
        pageDataMock.containerReporter = containerReporterMock;
        baseRenderer = new BaseRenderer() {
            @Override
            protected BasePageData createPageData() {
                return pageDataMock;
            }

            @Override
            protected boolean useOriginCaptureStrategy() {
                return false;
            }

            @Override
            public boolean isWhiteScreen(boolean isInnerWebView, View detectView, boolean hasFirstRender, boolean isStartPageAdvanced) {
                return false;
            }

            @Override
            public RendererType getType() {
                return null;
            }

            @Override
            public IRendererView getRendererView() {
                return null;
            }

            @Override
            public boolean needCoverLayer() {
                return false;
            }

            @Override
            public String getRendererUA() {
                return null;
            }

            @Override
            public void handleViewEvent(EventType eventType, String msg, BroadcastEvent source) {
                
            }

            @Override
            public void onCreate() {

            }

            @Override
            public IPageLifecycleCallback getPageLifecycleCallback() {
                return null;
            }
        };
        baseRenderer.mMSCAppModule = mMSCAppModuleMock;
    }

    @Test
    public void testReportPageStart() {
        // testReportPageStart_alreadyReported
        pageDataMock.isPageStartReported = true;
        baseRenderer.reportPageStart();
//        verify(appPageReporterMock, never()).once("msc.page.load.start");

        // testReportPageStart_notReported_hasMetaInfo
        pageDataMock.isPageStartReported = false;
        when(mMSCAppModuleMock.hasMetaInfo()).thenReturn(true);
        when(mMSCAppModuleMock.getBasePkgVersion()).thenReturn("1.0.0");
        baseRenderer.reportPageStart();
        verify(appPageReporterMock, Mockito.times(1))
                .commonTag("foundationVersion", "1.0.0");

        // testReportPageStart_notReported_noMetaInfo
        when(mMSCAppModuleMock.hasMetaInfo()).thenReturn(false);
        baseRenderer.reportPageStart();
    }

//    @Test
//    public void testReportUserPageSuccess() {
//        // 设置mock行为
//        Map<String, Object> tags = new HashMap<>();
//        tags.put(CommonTags.TAG_PURE_PAGE_PATH, "");
//        tags.put(CommonTags.TAG_PAGE_PATH, "");
//        tags.put(CommonTags.TAG_IS_FIRST_PAGE, false);
////        when(appPageReporterMock.getCommonTags()).thenReturn(pageCommonTags);
//        when(containerDelegateMock.getMSCContainer()).thenReturn(mMSCContainerMock);
//        when(mMSCContainerMock.getIntent()).thenReturn(intentMock);
////        long launchTimestamp = System.currentTimeMillis() - 1000;
////        long renderTimestamp = launchTimestamp + 500;
//        when(containerReporterMock.getLaunchStartTimeCurrentTimeMillis()).thenReturn(0L);
//        when(appPageReporterMock.getRenderPageStartTime()).thenReturn(0L);
//        PowerMockito.mockStatic(IntentUtil.class);
//        when(IntentUtil.getBooleanExtra(intentMock, ContainerController.START_FROM_MIN_PROGRAM, false)).thenReturn(false);
//        PowerMockito.mockStatic(UserReporter.class);
//        when(UserReporter.create()).thenReturn(userReporterMock);
//
//        // 调用方法
//        baseRenderer.reportUserPageSuccess();
//
//        // 验证UserReporter.create().reportUserPageLoadSuccess()被调用
//        verify(userReporterMock, Mockito.times(1)).reportUserPageLoadSuccess(tags,
//                UserReporter.CONSTANT_PORTAL, -1, -1);
//    }

//    @Test
//    public void testOnFirstRender() {
//        BaseRenderer.BasePageData pageData = new BaseRenderer.BasePageData();
//        pageData.mAppPageListener = Mockito.mock(BaseRenderer.AppPageListener.class);
//        pageData.appPageReporter = Mockito.mock(AppPageReporter.class);
//        pageData.hasFirstRender = false;
//        pageData.mEventListener = Mockito.mock(PageEventListener.class);
//        MSCRuntime runtime = Mockito.mock(MSCRuntime.class);
//        Mockito.when(runtime.getPerformanceManager()).thenReturn(Mockito.mock(PerformanceManager.class));
//        baseRenderer.mRuntime = runtime;
//        Whitebox.setInternalState(baseRenderer, "perfEventRecorder", Mockito.mock(PerfEventRecorder.class));
//
//        // 遇到报错  java.lang.NoSuchMethodError: android.os.SystemClock.elapsedRealtimeNanos()J
//        PowerMockito.mockStatic(PerfTrace.class);
//        TraceEvent traceEvent = Mockito.mock(TraceEvent.class);
//        PowerMockito.when(PerfTrace.currentTime()).thenReturn(1L);
//        PowerMockito.when(PerfTrace.end(Mockito.anyString())).thenReturn(traceEvent);
//
//        when(MSCHornRollbackConfig.isRollbackExceptionMetrixsReport()).thenReturn(false);
//        HashMap<String, Object> paramsMap = new HashMap<>();
//        baseRenderer.onFirstRender(paramsMap);
//        verify(baseRenderer.mRuntime).setPageState(AppPageState.FIRST_RENDER);
//    }
}
