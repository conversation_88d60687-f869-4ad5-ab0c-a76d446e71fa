apply from: '../gradle_msc_common.gradle'

android {
    compileSdkVersion project.compileSdkVersion
    buildToolsVersion project.buildToolsVersion

    defaultConfig {
        minSdkVersion project.minSdkVersion
        targetSdkVersion project.targetSdkVersion
        versionCode 1
        versionName "1.0"

        testInstrumentationRunner "android.support.test.runner.AndroidJUnitRunner"
        consumerProguardFiles "consumer-rules.pro"
    }

    buildTypes {
        release {
            minifyEnabled false
            proguardFiles getDefaultProguardFile('proguard-android-optimize.txt'), 'proguard-rules.pro'
        }
    }
    compileOptions {
        sourceCompatibility JavaVersion.VERSION_1_8
        targetCompatibility JavaVersion.VERSION_1_8
    }
}

configurations {
    all*.exclude group: 'com.meituan.android.common.metricx', module: 'sniffer'
    all*.exclude group: 'com.sankuai.model', module: 'base_model'
    all*.exclude group: 'com.meituan.roodesign', module: 'widgets'
    all*.exclude group: 'com.sankuai.meituan.skyeye', module: 'library'
}

dependencies {
    // Bytecode Manipulator https://km.sankuai.com/page/1207222340
    implementation("com.sankuai.waimai:manipulator-annotation:${WAIMAI_MANIPULATOR_VERSION}")
    annotationProcessor('com.sankuai.meituan.serviceloader:processor:2.2.27')

    // 为了避免强依赖MSC库，依赖了一个低版本的MSC库
    String mscVersion = '1.58.4'
    implementation("com.meituan.android.msc:library:${mscVersion}")
    implementation("com.meituan.android.msc:msc-plugin-devtools:${mscVersion}")

    // lynx
    implementation 'com.meituan.lynx:lynx_android:1.2.12-SNAPSHOT-20250716-1'
    implementation 'org.lynxsdk.lynx:primjs:2.11.1-rc.5'
    implementation 'com.meituan.lynx:lynx_trace:1.2.12-SNAPSHOT-20250716-1'
    implementation 'com.meituan.lynx:lynx_js_sdk:1.2.12-SNAPSHOT-20250716-1'
}