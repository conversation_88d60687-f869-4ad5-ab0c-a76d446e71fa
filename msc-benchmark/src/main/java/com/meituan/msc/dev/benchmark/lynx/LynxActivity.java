package com.meituan.msc.dev.benchmark.lynx;

import android.content.Context;
import android.content.res.AssetManager;
import android.os.Bundle;
import android.os.Handler;
import android.os.Looper;
import android.os.StrictMode;
import android.support.annotation.NonNull;
import android.support.v7.app.AppCompatActivity;
import android.text.TextUtils;
import android.util.Log;
import android.view.ViewGroup;
import android.widget.FrameLayout;
import android.widget.Toast;

import com.lynx.tasm.LynxEnv;
import com.lynx.tasm.LynxView;
import com.lynx.tasm.LynxViewBuilder;
import com.lynx.tasm.TimingHandler;
import com.meituan.android.common.weaver.interfaces.Weaver;
import com.meituan.android.common.weaver.interfaces.ffp.FFPReportListener;
import com.meituan.android.msc.benchmark.BuildConfig;
import com.meituan.android.msc.benchmark.R;
import com.meituan.msc.common.utils.IntentUtil;

import java.io.ByteArrayOutputStream;
import java.io.IOException;
import java.io.InputStream;
import java.lang.ref.WeakReference;
import java.lang.reflect.Method;
import java.util.HashMap;
import java.util.Map;
import java.util.concurrent.CopyOnWriteArrayList;
import java.util.concurrent.ExecutorService;
import java.util.concurrent.Executors;
import java.util.concurrent.Future;

public class LynxActivity extends AppCompatActivity implements FFPReportListener{
    private static final String TAG = "MainActivity";
    private TimingHandler.ExtraTimingInfo extraTimingInfo = new TimingHandler.ExtraTimingInfo();
    private ViewGroup mLynxContainer;
    private LynxView lynxView;
    private final Handler mainHandler = new Handler(Looper.getMainLooper());
    private final ExecutorService executorService = Executors.newSingleThreadExecutor();
    private final CopyOnWriteArrayList<Future<?>> pendingTasks = new CopyOnWriteArrayList<>();
    private boolean isViewInitialized = false;
    private boolean isDestroyed = false;

    @Override
    protected void onCreate(Bundle savedInstanceState) {
        super.onCreate(savedInstanceState);

        // 启用严格模式来检测主线程IO操作
        enableStrictMode();

//        System.setProperty("enable_trace", "perfetto");
//        Trace.beginSection("lynx-task");
        extraTimingInfo.mOpenTime = System.currentTimeMillis();
        extraTimingInfo.mContainerInitStart = System.currentTimeMillis();
        setContentView(R.layout.fullscreen_display);
        mLynxContainer = findViewById(R.id.lynx_container);
        extraTimingInfo.mContainerInitEnd = System.currentTimeMillis();

        final String bundleName = IntentUtil.getStringExtra(getIntent(), "bundleName");
        Log.d(TAG, "传进来的bundleName是：" + bundleName);
        String url = TextUtils.isEmpty(bundleName) ? "lynx-bundle/main.lynx.bundle" : bundleName;
        Log.d(TAG, "最终的bundleName是：" + url);

        // 在后台线程初始化LynxEnv
        initLynxEnvAsync(url);
        Weaver.getWeaver().registerListener(this, FFPReportListener.class);
//        Trace.endSection();
    }

    private void enableStrictMode() {
        if (BuildConfig.DEBUG) {
            StrictMode.setThreadPolicy(new StrictMode.ThreadPolicy.Builder()
                    .detectDiskReads()
                    .detectDiskWrites()
                    .detectNetwork()
                    .penaltyLog()
                    .build());
        }
    }

    private void initLynxEnvAsync(final String url) {
        Future<?> future = executorService.submit(new LynxEnvInitTask(this, url));
        pendingTasks.add(future);
    }

    // 使用静态内部类避免内存泄漏
    private static class LynxEnvInitTask implements Runnable {
        private final WeakReference<LynxActivity> activityRef;
        private final String url;

        LynxEnvInitTask(LynxActivity activity, String url) {
            this.activityRef = new WeakReference<>(activity);
            this.url = url;
        }

        @Override
        public void run() {
            // 在后台线程初始化LynxEnv
            LynxEnv.inst().lazyInitIfNeeded();

            final LynxActivity activity = activityRef.get();
            if (activity == null || activity.isDestroyed) {
                return;
            }

            // 在主线程创建视图
            activity.mainHandler.post(new Runnable() {
                @Override
                public void run() {
                    LynxActivity activity = activityRef.get();
                    if (activity == null || activity.isFinishing() || activity.isDestroyed) {
                        return;
                    }

                    // 创建LynxView
                    activity.lynxView = activity.buildLynxView();

                    // 添加到容器中
                    activity.mLynxContainer.addView(activity.lynxView,
                            new FrameLayout.LayoutParams(ViewGroup.LayoutParams.MATCH_PARENT, ViewGroup.LayoutParams.MATCH_PARENT));

                    activity.isViewInitialized = true;

                    // 加载模板
                    activity.loadTemplateAsync(url);
                }
            });
        }
    }

    private void loadTemplateAsync(final String url) {
        extraTimingInfo.mPrepareTemplateStart = System.currentTimeMillis();

        Future<?> future = executorService.submit(new TemplateLoadTask(this, url));
        pendingTasks.add(future);
    }

    // 使用静态内部类避免内存泄漏
    private static class TemplateLoadTask implements Runnable {
        private final WeakReference<LynxActivity> activityRef;
        private final String url;

        TemplateLoadTask(LynxActivity activity, String url) {
            this.activityRef = new WeakReference<>(activity);
            this.url = url;
        }

        @Override
        public void run() {
            final LynxActivity activity = activityRef.get();
            if (activity == null || activity.isDestroyed) {
                return;
            }

            final byte[] templateBundleData = readFileFromAssets(activity, url);
            if (activity.extraTimingInfo != null) {
                activity.extraTimingInfo.mPrepareTemplateEnd = System.currentTimeMillis();
            }

            if (templateBundleData == null) {
                activity.mainHandler.post(new Runnable() {
                    @Override
                    public void run() {
                        LynxActivity activity = activityRef.get();
                        if (activity == null || activity.isFinishing() || activity.isDestroyed) {
                            return;
                        }
                        Toast.makeText(activity, "Failed to load template: " + url, Toast.LENGTH_SHORT).show();
                    }
                });
                return;
            }

            Log.d(TAG, "模板数据读取成功，大小: " + templateBundleData.length + " 字节");

            // 在主线程中更新UI
            activity.mainHandler.post(new Runnable() {
                @Override
                public void run() {
                    LynxActivity activity = activityRef.get();
                    if (activity == null || activity.isFinishing() || activity.isDestroyed || !activity.isViewInitialized) {
                        return;
                    }

                    try {
                        // 设置额外的计时信息
                        activity.lynxView.setExtraTiming(activity.extraTimingInfo);

                        // 使用renderTemplateWithBaseUrl方法加载模板
                        Log.d(TAG, "使用renderTemplateWithBaseUrl方法加载模板");
                        activity.lynxView.renderTemplateWithBaseUrl(templateBundleData, new HashMap<>(), url);
                    } catch (Exception e) {
                        Log.e(TAG, "渲染模板失败", e);
                        Toast.makeText(activity, "渲染模板失败: " + e.getMessage(), Toast.LENGTH_SHORT).show();
                    }
                }
            });
        }
    }

    public static byte[] readFileFromAssets(Context context, String fileName) {
        AssetManager assetManager = context.getAssets();
        ByteArrayOutputStream byteArrayOutputStream = null;
        InputStream inputStream = null;

        try {
            inputStream = assetManager.open(fileName);
            int fileSize = inputStream.available();
            Log.d(TAG, "文件大小: " + fileSize + " 字节");

            byteArrayOutputStream = new ByteArrayOutputStream(fileSize > 0 ? fileSize : 8192);
            byte[] buffer = new byte[8192]; // 使用更大的缓冲区
            int length;
            int totalRead = 0;

            while ((length = inputStream.read(buffer)) != -1) {
                byteArrayOutputStream.write(buffer, 0, length);
                totalRead += length;
            }

            Log.d(TAG, "实际读取: " + totalRead + " 字节");

            return byteArrayOutputStream.toByteArray();
        } catch (IOException e) {
            Log.e(TAG, "读取文件失败: " + fileName, e);
            return null;
        } finally {
            try {
                if (inputStream != null) {
                    inputStream.close();
                }
                if (byteArrayOutputStream != null) {
                    byteArrayOutputStream.close();
                }
            } catch (IOException e) {
                Log.e(TAG, "关闭流失败", e);
            }
        }
    }

    private void onMSCPageFFPReport(@NonNull FFPReportListener.IReportEvent event) {
        // 将业务参数存出来，给其他指标用
        Map<String, Object> extraMap = event.extraMap();
        if (extraMap == null || extraMap.isEmpty()) {
            return;
        }

        long startTimeStamp = event.startTimeInMs();
        long endTimeStamp = event.endTimeInMs();
        long value = endTimeStamp - startTimeStamp;
        // 后面的逻辑需要开启性能测试模式才会启用
        Map<String, Object> extra = new HashMap<>(event.extraMap());

        // 使用字符串常量替代类引用，避免编译错误
        String lynxActivityClassName = "com.meituan.msc.lib.hera.sample.LynxActivity";
        if (!TextUtils.equals(event.activityClass(), lynxActivityClassName)) {
            Log.d("FFP", "FFP, value:" + value + ", endTimeStamp:" + endTimeStamp + "，tags:" + extra);
        }
    }

    private LynxView buildLynxView() {
        LynxViewBuilder viewBuilder = new LynxViewBuilder();
        viewBuilder.setTemplateProvider(new DemoTemplateProvider(this));

        // 尝试设置多线程渲染策略
        try {
            Class<?> threadStrategyClass = Class.forName("com.lynx.tasm.ThreadStrategyForRendering");
            Object multiThreadStrategy = threadStrategyClass.getField("MULTI_THREAD").get(null);
            Method setThreadStrategyMethod = LynxViewBuilder.class.getMethod("setThreadStrategy", threadStrategyClass);
            setThreadStrategyMethod.invoke(viewBuilder, multiThreadStrategy);
            Log.d(TAG, "设置多线程渲染策略成功");
        } catch (Exception e) {
            Log.d(TAG, "设置多线程渲染策略失败: " + e.getMessage());
        }

        return viewBuilder.build(this);
    }

    @Override
    public void onFFPReport(@NonNull FFPReportListener.IReportEvent iReportEvent) {
        onMSCPageFFPReport(iReportEvent);
    }

    @Override
    protected void onPause() {
        super.onPause();
        // LynxView可能没有onPause方法，暂时注释掉
        /*
        if (lynxView != null) {
            lynxView.onPause();
        }
        */
    }

    @Override
    protected void onResume() {
        super.onResume();
        // LynxView可能没有onResume方法，暂时注释掉
        /*
        if (lynxView != null) {
            lynxView.onResume();
        }
        */
    }

    @Override
    protected void onDestroy() {
        isDestroyed = true;

        // 取消所有待处理的任务
        for (Future<?> task : pendingTasks) {
            if (task != null && !task.isDone() && !task.isCancelled()) {
                task.cancel(true);
            }
        }
        pendingTasks.clear();

        // 移除所有回调
        mainHandler.removeCallbacksAndMessages(null);

        // 释放LynxView资源
        if (lynxView != null) {
            try {
                // LynxView可能没有onDestroy方法，暂时注释掉
                // lynxView.onDestroy();
                mLynxContainer.removeView(lynxView);
                lynxView = null;
            } catch (Exception e) {
                Log.e(TAG, "销毁LynxView失败", e);
            }
        }
        Weaver.getWeaver().unregisterListener(this, FFPReportListener.class);

        // 关闭线程池
        if (executorService != null && !executorService.isShutdown()) {
            executorService.shutdownNow();
        }

        super.onDestroy();
    }
}
