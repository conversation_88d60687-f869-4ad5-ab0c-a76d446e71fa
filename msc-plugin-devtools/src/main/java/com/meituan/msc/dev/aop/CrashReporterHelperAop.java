package com.meituan.msc.dev.aop;

import android.app.Activity;
import android.arch.lifecycle.Lifecycle;

import com.meituan.msc.dev.devtools.debugger.utils.MSCDomainUtils;
import com.meituan.msc.modules.container.ApplicationLifecycleMonitor;
import com.meituan.msc.modules.container.ContainerController;
import com.meituan.msc.modules.container.MSCActivity;
import com.meituan.msc.modules.devtools.IDebugger;
import com.meituan.msc.modules.engine.MSCRuntime;
import com.sankuai.waimai.manipulator.annotation.HookMethodEntry;

public class CrashReporterHelperAop {
    @HookMethodEntry(className = "com.meituan.msc.modules.reporter.CrashReporterHelper",
            methodName = "pushPage",
            methodDesc = "(java.lang.String, java.lang.String, java.lang.String, boolean)"
    )
    public static void aopPushPage(String url, String appId, String openType, boolean isWidget) {
        if (ApplicationLifecycleMonitor.MSC.getState().isAtLeast(Lifecycle.State.CREATED)) {
            Activity lastActivity = ApplicationLifecycleMonitor.ALL.getLastActivity();
            if (!(lastActivity instanceof MSCActivity)) {
                return;
            }
            ContainerController containerController = ((MSCActivity) lastActivity).getContainerController();
            if (containerController == null) {
                return;
            }
            MSCRuntime runtime = containerController.getRuntime();
            if (runtime == null) {
                return;
            }
            MSCDomainUtils.recordPagePush(url, appId, openType, isWidget);
        }
    }

    @HookMethodEntry(className = "com.meituan.msc.modules.reporter.CrashReporterHelper",
            methodName = "popPage",
            methodDesc = "()")
    public static void aopPopPage() {
        if (ApplicationLifecycleMonitor.MSC.getState().isAtLeast(Lifecycle.State.CREATED)) {
            Activity lastActivity = ApplicationLifecycleMonitor.ALL.getLastActivity();
            if (!(lastActivity instanceof MSCActivity)) {
                return;
            }
            ContainerController containerController = ((MSCActivity) lastActivity).getContainerController();
            if (containerController == null) {
                return;
            }
            MSCRuntime runtime = containerController.getRuntime();
            if (runtime == null) {
                return;
            }
            // TODO: 2024/9/14 huangyu28 这里需要修复MSCDebugger获取不到的问题, 先兜底处理获取不到也更新currentPage
            IDebugger devTools = runtime.getModuleWithoutDelegate(IDebugger.class);
            if (devTools != null) {
                devTools.popPage();
            } else {
                MSCDomainUtils.recordPagePop();
            }
        }
    }
}
