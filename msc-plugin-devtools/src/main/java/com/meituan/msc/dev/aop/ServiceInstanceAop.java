package com.meituan.msc.dev.aop;

import com.meituan.msc.dev.performance.PerformanceTestManager;
import com.meituan.msc.extern.MSCEnvHelper;
import com.meituan.msc.jse.bridge.CatalystInstanceImpl;
import com.meituan.msc.modules.service.ServiceInstance;
import com.sankuai.waimai.manipulator.annotation.HookMethodEntry;

public class ServiceInstanceAop {
    @HookMethodEntry(className = "com.meituan.msc.modules.service.ServiceInstance", methodName = "runOnJSQueueThreadSafe", methodDesc = "(java.lang.Runnable)")
    public static void runOnJSQueueThreadSafe(ServiceInstance thisObject, Runnable runnable) {
        // 非性功能测试模式，在执行js时执行inspect更名操作
        if (!PerformanceTestManager.isEnabledPerformanceTestMode() && MSCEnvHelper.getEnvInfo() != null && !MSCEnvHelper.getEnvInfo().isProdEnv()) {
            ((CatalystInstanceImpl) thisObject.getInstance()).changeV8InspectorName(thisObject.getEngineFullName());
        }
    }
}
