package com.meituan.msc.dev.automator;

import com.meituan.msc.modules.devtools.DebugHelper;
import com.meituan.msc.modules.devtools.automator.AutomatorManagerLoader;
import com.meituan.msc.modules.devtools.automator.IAutomatorManager;
import com.meituan.msc.modules.manager.MSCMethod;
import com.meituan.msc.modules.manager.MSCModule;
import com.meituan.msc.modules.manager.ModuleName;
import com.meituan.msc.modules.reporter.MSCLog;

import org.json.JSONObject;


@ModuleName(name = "Automator")
public class AutoMessageModule extends MSCModule {
    public static final String TAG = "AutoMessageModule";

    @MSCMethod(isSync = true)
    public void sendAutoMessage(JSONObject param) {
        if (DebugHelper.isDebug()) {
            MSCLog.i(TAG, "param:", param);
        }
        IAutomatorManager automatorManager = AutomatorManagerLoader.getInstance();
        if (automatorManager != null) {
            automatorManager.sendMessage(param.toString());
        }
    }
}
