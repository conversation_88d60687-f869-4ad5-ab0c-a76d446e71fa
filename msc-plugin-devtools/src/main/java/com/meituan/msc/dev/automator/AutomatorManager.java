package com.meituan.msc.dev.automator;

import android.content.Context;
import android.content.Intent;
import android.support.annotation.NonNull;
import android.support.annotation.Nullable;
import android.text.TextUtils;

import com.meituan.msc.common.executor.MSCExecutors;
import com.meituan.msc.common.utils.IntentUtil;
import com.meituan.msc.common.utils.Interceptors;
import com.meituan.msc.dev.automator.bean.ServiceMessageBean;
import com.meituan.msc.dev.automator.interceptor.MessageInterceptor;
import com.meituan.msc.dev.automator.interceptor.NativeAddAppLifecycleObserverInterceptor;
import com.meituan.msc.dev.automator.interceptor.NativeCleanPreloadAppInterceptor;
import com.meituan.msc.dev.automator.interceptor.NativeFinishInterceptor;
import com.meituan.msc.dev.automator.interceptor.NativeGetEnginePoolInfoInterceptor;
import com.meituan.msc.dev.automator.interceptor.NativeGetVersionInfoInterceptor;
import com.meituan.msc.dev.automator.interceptor.NativeMockDDDInterceptor;
import com.meituan.msc.dev.automator.interceptor.NativeMockHornInterceptor;
import com.meituan.msc.dev.automator.interceptor.NativeMockLocationInterceptor;
import com.meituan.msc.dev.automator.interceptor.NativeMockLocationSequenceInterceptor;
import com.meituan.msc.dev.automator.interceptor.NativeMonitorMetricsInterceptor;
import com.meituan.msc.dev.automator.interceptor.NativeOpenUrlInterceptor;
import com.meituan.msc.dev.automator.interceptor.NativePreloadAppInterceptor;
import com.meituan.msc.dev.automator.interceptor.NativeRestoreHornInterceptor;
import com.meituan.msc.dev.automator.interceptor.NativeRestoreLocationInterceptor;
import com.meituan.msc.dev.automator.interceptor.NativeRestoreMockDDDInterceptor;
import com.meituan.msc.dev.automator.interceptor.NativeScreenInfoInterceptor;
import com.meituan.msc.dev.automator.interceptor.NativeStartMonitorFPSInterceptor;
import com.meituan.msc.dev.automator.interceptor.NativeStopMonitorFPSInterceptor;
import com.meituan.msc.dev.automator.interceptor.SystemCloseInterceptor;
import com.meituan.msc.dev.automator.interceptor.SystemPingInterceptor;
import com.meituan.msc.dev.performance.FPSMonitor;
import com.meituan.msc.modules.devtools.automator.AutomatorManagerLoader;
import com.meituan.msc.modules.devtools.automator.AutomatorScriptInjectCallback;
import com.meituan.msc.modules.devtools.automator.IAutomatorManager;
import com.meituan.msc.modules.devtools.automator.ServiceOperateCallback;
import com.meituan.msc.modules.engine.MSCRuntime;
import com.meituan.msc.modules.reporter.MSCLog;

import java.util.ArrayList;
import java.util.Collection;
import java.util.List;
import java.util.concurrent.ConcurrentHashMap;
import java.util.concurrent.TimeUnit;
import java.util.concurrent.atomic.AtomicInteger;

import okhttp3.OkHttpClient;
import okhttp3.Request;
import okhttp3.WebSocket;

/**
 * https://km.sankuai.com/page/1119255521
 * 自动化测试管理类，devtools Module中
 */
public class AutomatorManager implements IAutomatorManager {

    private static final String TAG = "AutomatorManager";
    public final List<String> autoMessageCacheList = new ArrayList<>();
    final List<ServiceMessageBean> serviceMessageCacheList = new ArrayList<>();
    final ConcurrentHashMap<String, ServiceOperateCallback> serviceOperateCallbacks = new ConcurrentHashMap<>();
    final List<AutomatorScriptInjectCallback> pageInjectCallbacks = new ArrayList<>();
    @Nullable
    public WebSocket webSocket;
    @NonNull
    public volatile AtomicInteger reconnectTimes = new AtomicInteger(0);
    volatile String automatorServiceScript;
    volatile String automatorPageScript;
    String topAppId;
    /**
     * WebSocket服务器地址
     */
    private String mAutomatorServer;
    /**
     * 注入包下载地址
     */
    private String mAutomatorDownloadUrl;
    private boolean isConnect;
    private final AutomatorModule automatorModule = new AutomatorModule();
    @Override
    public void connectSocket(@NonNull Context context, @NonNull String automatorServer, @NonNull String automatorDownloadUrl) {
        if (TextUtils.equals(mAutomatorServer, automatorServer)) {
            MSCLog.d(TAG, "connectSocket already exist");
            return;
        }

        MSCLog.d(TAG, "connectSocket start, automatorServer:", automatorServer, ", automatorDownloadUrl", automatorDownloadUrl);
        webSocket = null;
        isConnect = true;
        mAutomatorServer = automatorServer;
        mAutomatorDownloadUrl = automatorDownloadUrl;
        //注册MMP Activity生命周期监听，更新前台小程序Engine，与其进行通信
        LifecycleMonitor lifecycleMonitor = new LifecycleMonitor();
        lifecycleMonitor.setChangeListener(new LifecycleMonitor.ForegroundEngineChangeListener() {
            @Override
            public void onChange(String appId) {
                if (TextUtils.isEmpty(appId)) {
                    MSCLog.e(TAG, "lifecycleMonitor onChange appId is null");
                    return;
                }

                topAppId = appId;
                ServiceOperateCallback callback = serviceOperateCallbacks.get(appId);
                if (callback != null) {
                    sendCachedServiceMessages(appId, callback);
                } else {
                    MSCLog.e(TAG, "onChange callbackWeakReference is null");
                }
            }
        });
        lifecycleMonitor.registerActivityLifecycleMonitor(context);

        connect();
    }

    void sendCachedServiceMessages(@NonNull String appId, @Nullable ServiceOperateCallback callback) {
        if (callback == null) {
            MSCLog.e(TAG, "sendCachedServiceMessages callback is null");
            return;
        }

        if (!serviceMessageCacheList.isEmpty()) {
            for (ServiceMessageBean bean : serviceMessageCacheList) {
                if (!TextUtils.equals(bean.appId, appId)) {
                    continue;
                }
                callback.subscribeHandler(bean.message);
            }
            serviceMessageCacheList.clear();
        }
    }

    public void connect() {
        Request request = new Request.Builder().url(mAutomatorServer).build();
        AutomatorWebSocketListener webSocketListener = getWebSocketListener(mAutomatorDownloadUrl);
        OkHttpClient.Builder builder = new OkHttpClient.Builder()
                .connectTimeout(1000, TimeUnit.MILLISECONDS)
                .readTimeout(1000, TimeUnit.MILLISECONDS)
                .writeTimeout(1000, TimeUnit.MILLISECONDS);
        builder.addInterceptor(Interceptors.OkHttp.getRiskInterceptor());
        webSocket = builder
                .build()
                .newWebSocket(request, webSocketListener);
    }

    @Override
    public boolean sendMessage(String text) {
        if (webSocket != null) {
            return webSocket.send(text);
        } else {
            autoMessageCacheList.add(text);
        }
        return false;
    }

    @Override
    public void addPageOperateCallback(@Nullable AutomatorScriptInjectCallback callback) {
        if (callback == null) {
            return;
        }

        if (!TextUtils.isEmpty(automatorPageScript)) {
            callback.inject(automatorPageScript);
        } else {
            pageInjectCallbacks.add(callback);
        }
    }

    @Override
    public void addServiceOperateCallback(@NonNull String appId, @Nullable ServiceOperateCallback callback) {
        if (callback == null) {
            return;
        }

        if (!TextUtils.isEmpty(automatorServiceScript)) {
            callback.inject(automatorServiceScript);
        }

        topAppId = appId;
        serviceOperateCallbacks.put(appId, callback);
        sendCachedServiceMessages(appId, callback);
    }

    @Override
    public boolean isConnect() {
        return isConnect;
    }

    @Override
    public void registerModule(MSCRuntime mRuntime) {
        mRuntime.registerModule(new AutoMessageModule(), AutoMessageModule.class);
        mRuntime.registerModule(automatorModule, AutomatorModule.class);

        MSCLog.d(TAG, "registerModule end");
    }

    @Override
    public void clearPageOperateCallback() {
        pageInjectCallbacks.clear();
    }

    private AutomatorWebSocketListener getWebSocketListener(String automatorDownloadUrl) {
        return new AutomatorWebSocketListener(this)
                .addMessageInterceptors(createMessageInterceptors())
                .setAutomatorDownloadUrl(automatorDownloadUrl)
                .setDownloadPackageListener(new JSScriptDownloadListener() {

                    @Override
                    public void downloadServiceComplete(@Nullable String automatorService) {
                        automatorServiceScript = automatorService;
//                        MSCLog.d(TAG, "downloadServiceComplete:", mAutomatorService);
                    }

                    @Override
                    public void downloadPageComplete(@Nullable String automatorPage) {
//                        MSCLog.d(TAG, "downloadPageComplete:", automatorPage);
                        automatorPageScript = automatorPage;
                    }
                })
                .setOperateServiceListener(new OperateServiceListener() {
                    @Override
                    public void inject(String script) {
                        if (TextUtils.isEmpty(script)) {
                            MSCLog.d(TAG, "Service automator script is null");
                            return;
                        }

                        injectServiceScript(script);
                    }

                    @Override
                    public void subscribeHandler(@Nullable String appId, @NonNull String script) {
                        sendMessageToAppService(appId, script);
                    }
                })
                .setOperatePageListener(new AutomatorScriptInjectCallback() {
                    @Override
                    public void inject(String script) {
                        if (pageInjectCallbacks.isEmpty()) {
                            MSCLog.d(TAG, "Page evaluateJavascript callbacks is empty");
                            return;
                        }

                        injectPageScript(script);
                    }
                });

    }

    private List<MessageInterceptor> createMessageInterceptors() {
        final ArrayList<MessageInterceptor> list = new ArrayList<>();
        list.add(new SystemCloseInterceptor(this));
        list.add(new SystemPingInterceptor());

        list.add(new NativeMockLocationInterceptor());
        list.add(new NativeRestoreLocationInterceptor());
        list.add(new NativeMockLocationSequenceInterceptor());
        list.add(new NativeOpenUrlInterceptor());

        list.add(new NativeMockDDDInterceptor());
        list.add(new NativeRestoreMockDDDInterceptor());
        list.add(new NativeGetEnginePoolInfoInterceptor());
        list.add(new NativeGetVersionInfoInterceptor(automatorModule));
        list.add(new NativeMockHornInterceptor());
        list.add(new NativeRestoreHornInterceptor());
        list.add(new NativeStartMonitorFPSInterceptor(automatorModule));
        list.add(new NativeStopMonitorFPSInterceptor(automatorModule));
        list.add(new NativeScreenInfoInterceptor(automatorModule));
        list.add(new NativeMonitorMetricsInterceptor(automatorModule));
        list.add(new NativeFinishInterceptor(automatorModule));
        list.add(new NativeAddAppLifecycleObserverInterceptor());
        list.add(new NativePreloadAppInterceptor());
        list.add(new NativeCleanPreloadAppInterceptor());
        return list;
    }

    /**
     * 转发WebSocket消息给小程序Service层
     */
    void sendMessageToAppService(final @Nullable String appId, final @NonNull String script) {
        String finalAppId = "";
        if (TextUtils.isEmpty(appId)) {
            finalAppId = topAppId;
        } else {
            finalAppId = appId;
        }

        if (serviceOperateCallbacks.isEmpty()) {
            serviceMessageCacheList.add(new ServiceMessageBean(finalAppId, script));
            MSCLog.e(TAG, "sendMessageToAppService serviceOperateCallbacks is empty");
            return;
        }
        ServiceOperateCallback callback = serviceOperateCallbacks.get(finalAppId);
        if (callback == null) {
            serviceMessageCacheList.add(new ServiceMessageBean(finalAppId, script));
            MSCLog.e(TAG, "sendMessageToAppService operateCallback is null");
            return;
        }

        callback.subscribeHandler(script);
    }

    void injectServiceScript(String script) {
        if (serviceOperateCallbacks.isEmpty()) {
            return;
        }

        Collection<ServiceOperateCallback> references = serviceOperateCallbacks.values();
        for (ServiceOperateCallback callback : references) {
            if (callback == null) {
                continue;
            }

            callback.inject(script);
        }
    }

    void injectPageScript(@NonNull final String script) {
        for (AutomatorScriptInjectCallback callback : pageInjectCallbacks) {
            if (callback == null) {
                continue;
            }
            callback.inject(script);
        }
        pageInjectCallbacks.clear();
    }

    public void releaseSocket() {
        webSocket = null;
    }

    public static void createAutomatorTestWebSocket(Context context, Intent intent, MSCRuntime mscRuntime) {
        MSCExecutors.submit(new Runnable() {
            @Override
            public void run() {
                // 这里 WebSocket 建立链接
                IAutomatorManager automatorManager = AutomatorManagerLoader.getInstance();
                if (!automatorManager.isConnect()) {
                    // 已经连接过就不再重连了，防止出错
                    String automatorServer = IntentUtil.getStringExtra(intent, "automatorServer");
                    String automatorUrl = IntentUtil.getStringExtra(intent, "automatorUrl");
                    if (automatorServer != null && automatorUrl != null) {
                        automatorManager.connectSocket(context, automatorServer, automatorUrl);
                    } else {
                        MSCLog.e(TAG, "automatorServer or automatorUrl is null");
                    }
                }
                // 注册自动化测试相关的桥
                if (mscRuntime != null) {
                    AutomatorManagerLoader.getInstance().registerModule(mscRuntime);
                }

                FPSMonitor.init(context);

            }
        });
    }
}



