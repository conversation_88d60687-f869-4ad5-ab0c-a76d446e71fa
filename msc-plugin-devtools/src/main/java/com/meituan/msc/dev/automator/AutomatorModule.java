package com.meituan.msc.dev.automator;

import android.util.Log;

import com.meituan.metrics.Metrics;
import com.meituan.metrics.interceptor.MetricsDefaultInterceptor;
import com.meituan.metrics.sampler.fps.FpsEvent;
import com.meituan.metrics.sampler.fps.ScrollHitchEvent;
import com.meituan.msc.dev.performance.FPSMonitor;
import com.meituan.msc.dev.utils.UIThreadUtil;
import com.meituan.msc.modules.manager.MSCModule;
import com.meituan.msc.modules.manager.ModuleName;

import org.json.JSONObject;

import java.util.Map;

@ModuleName(name = "AutomatorModule")
public class AutomatorModule extends MSCModule {

    private Map<String, Object> metricsDetails = null;

    public AutomatorModule() {
        Metrics.getInstance().setDebug(true);
        Metrics.getInstance().addInterceptor(new MetricsDefaultInterceptor() {
            @Override
            public void onReportFpsEvent(FpsEvent event) {
                super.onReportFpsEvent(event);
                if (!("mobile.fps.scroll.avg.v2.n".equals(event.getLocalEventType()))) {
                    return;
                }
                metricsDetails = event.getDetails();
//                try {
//                    File file = CIPStorageCenter.requestExternalFilePath(Metrics.getInstance().getContext(), "msc", "metrics.json");
//                    File fileDir = file.getParentFile();
//                    if (!fileDir.exists()) {
//                        fileDir.mkdirs();
//                    }
//                    if (file.exists()) {
//                        file.delete();
//                    }
//                    Log.e("MSCLOG", " benchmark_write_file1:  " + file.getAbsolutePath());
//                    FileUtils.writeFile(file, new Gson().toJson(details), false);
//                } catch (Throwable e) {
////                    System.out.println(QA_TAG + " benchmark_write_failed " + Log.getStackTraceString(e).replaceAll("\\n", ";"));
//                }
                Log.e("MSCLOG", "onReportEvent1 " + event.getLocalEventType() + " " + event.getPageName());
                Log.e("MSCLOG", "details1 " + metricsDetails);
            }
        });
    }

    public String getVersion() {
        return getRuntime().getMSCAppModule().getMSCAppVersion();
    }

    public String getSDKVersion() {
        return getRuntime().getMSCAppModule().getBasePkgVersion();
    }

    public String getBuildId() {
        return getRuntime().getMSCAppModule().getBuildId();
    }

    public String getAppId() {
        return getRuntime().getMSCAppModule().getAppId();
    }

    public JSONObject stopMonitorFPS() {
        return FPSMonitor.getInstance().stopMonitorFPS();
    }

    public int startMonitorFPS() {
        return FPSMonitor.getInstance().startMonitorFPS();
    }

    public JSONObject getMetricsInfo() {
        if (metricsDetails == null) {
            return null;
        }
        return new JSONObject(metricsDetails);
    }

    public void finish() {
        getRuntime().getContainerManagerModule().getTopActivity().finish();
    }

    public JSONObject screenInfo() {
        return FPSMonitor.getInstance().getScreenInfo();
    }
}
