package com.meituan.msc.dev.automator;

import android.support.annotation.NonNull;
import android.support.annotation.Nullable;
import android.text.TextUtils;

import com.google.gson.Gson;
import com.meituan.msc.common.utils.OkHttpFactory;
import com.meituan.msc.common.utils.OkHttpUtil;
import com.meituan.msc.common.utils.ToastUtils;
import com.meituan.msc.dev.automator.interceptor.MessageInterceptor;
import com.meituan.msc.lib.BuildConfig;
import com.meituan.msc.modules.devtools.automator.AutomatorScriptInjectCallback;
import com.meituan.msc.modules.reporter.MSCLog;

import org.json.JSONException;
import org.json.JSONObject;

import java.io.IOException;
import java.util.ArrayList;
import java.util.List;
import java.util.concurrent.atomic.AtomicInteger;

import okhttp3.Call;
import okhttp3.Callback;
import okhttp3.OkHttpClient;
import okhttp3.Request;
import okhttp3.Response;
import okhttp3.WebSocket;
import okhttp3.WebSocketListener;
import okio.ByteString;

public class AutomatorWebSocketListener extends WebSocketListener {
    private static final String TAG = "AutomatorWebSocketListener";
    private static final Gson gson = new Gson();
    private static final int MAX_RECONNECT_COUNT = 3;
    private final AutomatorManager manager;
    private final List<MessageInterceptor> messageInterceptors = new ArrayList<>();
    String automatorService;
    String automatorPage;
    String automatorDownloadUrl;
    JSScriptDownloadListener downloadListener;
    OperateServiceListener operateServiceListener;
    AutomatorScriptInjectCallback operatorPageListener;

    public AutomatorWebSocketListener(@NonNull AutomatorManager manager) {
        this.manager = manager;
    }

    @NonNull
    public AutomatorWebSocketListener addMessageInterceptors(@NonNull List<MessageInterceptor> interceptors) {
        messageInterceptors.addAll(interceptors);
        return this;
    }

    @NonNull
    public AutomatorWebSocketListener addMessageInterceptor(@NonNull MessageInterceptor interceptor) {
        messageInterceptors.add(interceptor);
        return this;
    }

    @NonNull
    public AutomatorWebSocketListener setDownloadPackageListener(@NonNull JSScriptDownloadListener downloadListener) {
        this.downloadListener = downloadListener;
        return this;
    }

    @NonNull
    public AutomatorWebSocketListener setOperateServiceListener(@NonNull OperateServiceListener operateServiceListener) {
        this.operateServiceListener = operateServiceListener;
        return this;
    }

    @NonNull
    public AutomatorWebSocketListener setOperatePageListener(@NonNull AutomatorScriptInjectCallback operatorPageListenerr) {
        this.operatorPageListener = operatorPageListenerr;
        return this;
    }

    @NonNull
    public AutomatorWebSocketListener setAutomatorDownloadUrl(@Nullable String automatorDownloadUrl) {
        this.automatorDownloadUrl = automatorDownloadUrl;
        return this;
    }

    @Override
    public void onOpen(@NonNull WebSocket webSocket, @NonNull Response response) {
        super.onOpen(webSocket, response);
        MSCLog.d(TAG, "onOpen start, automatorDownloadUrl:", automatorDownloadUrl);

        // 重置连接重试次数
        manager.reconnectTimes = new AtomicInteger(0);

        downLoadInjectPackage();

        // 发送缓存的消息到自动化测试服务
        sendCachedAutoMessageList(webSocket);
    }

    private void sendCachedAutoMessageList(@NonNull WebSocket webSocket) {
        if (manager.autoMessageCacheList.isEmpty()) {
            return;
        }

        for (String message : manager.autoMessageCacheList) {
            MSCLog.d(TAG, "sendCachedAutoMessage:", message);
            webSocket.send(message);
        }
    }

    private void downLoadInjectPackage() {
        if (TextUtils.isEmpty(automatorDownloadUrl)) {
            return;
        }

        String url = OkHttpUtil.appendUrlParam(automatorDownloadUrl, "mmpSDKVersion", BuildConfig.MSC_SDK_VERSION);
        downloadRequest(url, new Callback() {
            @Override
            public void onFailure(@NonNull Call call, @NonNull IOException e) {
                MSCLog.e(TAG, null, "downloadRequest onFailure: ", e);
            }

            @Override
            public void onResponse(@NonNull Call call, @NonNull Response response) throws IOException {
                MSCLog.d(TAG, "downloadRequest onResponse");
                if (response.body() != null) {
                    byte[] bytes = response.body().bytes();
                    try {
                        JSONObject jsonObject = new JSONObject(new String(bytes));
                        requestAutomatorService(jsonObject.optString("automatorService"));
                        requestAutomatorPage(jsonObject.optString("automatorPage"));
                    } catch (JSONException e) {
                        e.printStackTrace();
                    }
                }
            }
        });
    }

    @Override
    public void onMessage(@NonNull WebSocket webSocket, @NonNull String text) {
        super.onMessage(webSocket, text);
        MSCLog.d(TAG, Thread.currentThread().getId(), " onOpen onMessage1, text:", text);
        dealMessage(webSocket, text);
    }

    @Override
    public void onMessage(@NonNull WebSocket webSocket, @NonNull ByteString bytes) {
        super.onMessage(webSocket, bytes);
        MSCLog.d(TAG, Thread.currentThread().getId(), " onOpen onMessage2, text:", bytes);
        dealMessage(webSocket, bytes.base64());
    }

    @Override
    public void onClosing(@NonNull WebSocket webSocket, int code, @NonNull String reason) {
        super.onClosing(webSocket, code, reason);
        MSCLog.i(TAG, "onOpen onClosing, code:", code, ", reason:", reason);
    }

    @Override
    public void onClosed(@NonNull WebSocket webSocket, int code, @NonNull String reason) {
        super.onClosed(webSocket, code, reason);
        MSCLog.i(TAG, "onOpen onClosed, code:", code, ", reason:", reason);

    }

    @Override
    public void onFailure(@NonNull WebSocket webSocket, @NonNull Throwable t, @NonNull Response response) {
        super.onFailure(webSocket, t, response);
        MSCLog.e(TAG, null, "onOpen onFailure: ", getMessage(t));

        // 连接中断，进行重连
        if (manager.reconnectTimes.get() < MAX_RECONNECT_COUNT) {
            manager.reconnectTimes.incrementAndGet();
            MSCLog.e(TAG, null, "WebSocket reconnect, retry count:", manager.reconnectTimes.get());
            manager.connect();
        } else {
            ToastUtils.toastIfDebug("自动化测试连接已断开，已达重试次数上限");
        }
    }

    public static String getMessage(Throwable t) {
        if (t != null) {
            if (t.getCause() != null) {
                String msg = t.getCause().getMessage();
                if (!TextUtils.isEmpty(msg)) {
                    return "message :" + msg + "\nstackTrace :" + MSCLog.getAllStackInformation(t);
                }
            }
            return "message :" + t.getMessage() + "\nstackTrace :" + MSCLog.getAllStackInformation(t);
        }
        return "";
    }

    private void dealMessage(WebSocket webSocket, String msg) {
        if (TextUtils.isEmpty(msg)) {
            return;
        }

        WebSocketMessageBean messageBean = gson.fromJson(msg, WebSocketMessageBean.class);
        if (messageBean == null) {
            MSCLog.e(TAG, "dealMessage messageBean is null");
            return;
        }

        String method = messageBean.method;
        for (MessageInterceptor interceptor : messageInterceptors) {
            if (!interceptor.isMethodMatch(method)) {
                continue;
            }

            if (interceptor.intercept(webSocket, messageBean, msg)) {
                return;
            }
        }

        // interceptors未消费的指令转发到小程序Service层
        operateServiceListener.subscribeHandler(messageBean.appId, msg);
    }

    private void downloadRequest(String url, Callback callback) {
        if (TextUtils.isEmpty(url)) {
            MSCLog.d(TAG, "downloadRequest url is null");
            return;
        }

        MSCLog.d(TAG, "downloadRequest, url:", url);
        Request request = new Request.Builder().url(url).build();
        OkHttpClient client = OkHttpFactory.getInstance().getDownloadClient();
        Call clientCall = client.newCall(request);
        clientCall.enqueue(callback);
    }

    void requestAutomatorService(String url) {
        MSCLog.d(TAG, "requestAutomatorService, url:", url);
        downloadRequest(url, new Callback() {
            @Override
            public void onFailure(@NonNull Call call, @NonNull IOException e) {
                MSCLog.d(TAG, "requestAutomatorService onFailure, error:", e);
            }

            @Override
            public void onResponse(@NonNull Call call, @NonNull Response response) throws IOException {
                MSCLog.d(TAG, "requestAutomatorService onResponse, connectSocket start");
                if (response.body() != null) {
                    automatorService = new String(response.body().bytes());
                    if (downloadListener != null) {
                        downloadListener.downloadServiceComplete(automatorService);
                    }
                    //注入逻辑层
                    operateServiceListener.inject(automatorService);
                }
            }
        });
    }

    void requestAutomatorPage(String url) {
        MSCLog.d(TAG, "requestAutomatorPage, url:", url);
        downloadRequest(url, new Callback() {
            @Override
            public void onFailure(@NonNull Call call, @NonNull IOException e) {
                MSCLog.d(TAG, "requestAutomatorPage onFailure, error:", e);
            }

            @Override
            public void onResponse(@NonNull Call call, @NonNull Response response) throws IOException {
                MSCLog.d(TAG, "requestAutomatorPage onResponse");
                if (response.body() != null) {
                    automatorPage = new String(response.body().bytes());
                    if (downloadListener != null) {
                        downloadListener.downloadPageComplete(automatorPage);
                    }
                    //注入page层
                    operatorPageListener.inject(automatorPage);
                }
            }
        });
    }
}
