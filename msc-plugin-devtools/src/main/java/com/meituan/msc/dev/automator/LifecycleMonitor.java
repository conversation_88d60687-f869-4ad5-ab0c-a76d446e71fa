package com.meituan.msc.dev.automator;

import android.app.Activity;
import android.app.Application;
import android.content.Context;
import android.os.Bundle;
import android.support.annotation.NonNull;
import android.support.annotation.Nullable;

import com.meituan.msc.common.executor.MSCExecutors;
import com.meituan.msc.common.framework.MSCRunningManager;
import com.meituan.msc.modules.container.MSCActivity;
import com.meituan.msc.modules.engine.RuntimeManager;
import com.meituan.msc.modules.engine.MSCRuntime;

public class LifecycleMonitor {
    boolean isInited;

    ForegroundEngineChangeListener changeListener;
    Application.ActivityLifecycleCallbacks lifecycleCallbacks = new Application.ActivityLifecycleCallbacks() {
        @Override
        public void onActivityCreated(@NonNull Activity activity, @Nullable Bundle savedInstanceState) {

        }

        @Override
        public void onActivityStarted(@NonNull Activity activity) {

        }

        @Override
        public void onActivityResumed(@NonNull final Activity activity) {
            if (changeListener != null && activity instanceof MSCActivity) {
                MSCExecutors.runOnUiThreadDelayed(new Runnable() {
                    @Override
                    public void run() {
                        MSCRuntime runtime = RuntimeManager.getRuntimeWithAppId(
                                MSCRunningManager.getCurrentForegroundAppIdInThisProcess());
                        String appId = runtime != null ? runtime.getAppId() : null;
                        if (appId != null) {
                            changeListener.onChange(appId);
                        }
                    }
                }, 0);
            }
        }

        @Override
        public void onActivityPaused(@NonNull Activity activity) {

        }

        @Override
        public void onActivityStopped(@NonNull Activity activity) {

        }

        @Override
        public void onActivitySaveInstanceState(@NonNull Activity activity, @NonNull Bundle outState) {

        }

        @Override
        public void onActivityDestroyed(@NonNull Activity activity) {

        }
    };

    public void registerActivityLifecycleMonitor(Context context) {
        if (isInited) {
            return;
        }

        Context application = context.getApplicationContext();
        if (!(application instanceof Application)) {
            return;
        }
        isInited = true;
        ((Application) application).registerActivityLifecycleCallbacks(lifecycleCallbacks);
    }

    public LifecycleMonitor setChangeListener(ForegroundEngineChangeListener changeListener) {
        this.changeListener = changeListener;
        return this;
    }

    interface ForegroundEngineChangeListener {
        void onChange(String appId);
    }
}
