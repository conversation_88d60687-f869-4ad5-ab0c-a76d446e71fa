package com.meituan.msc.dev.automator;

import android.support.annotation.NonNull;
import android.support.annotation.Nullable;

import com.meituan.msc.modules.devtools.automator.AutomatorScriptInjectCallback;

/**
 * <AUTHOR>
 * @date 2021/9/10.
 */
public interface OperateServiceListener extends AutomatorScriptInjectCallback {

    /**
     * 转发指令到小程序逻辑层
     */
    void subscribeHandler(@Nullable String appId, @NonNull String script);
}
