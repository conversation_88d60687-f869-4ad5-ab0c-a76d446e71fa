package com.meituan.msc.dev.automator;

import android.support.annotation.Keep;

import java.util.List;

/**
 * WebSocket 下发的指令数据结构
 *
 * <AUTHOR>
 * @date 2021/9/10.
 */
@Keep
public class WebSocketMessageBean {

    public String id;
    public String appId;
    public String method;
    public Data params;

    @Override
    public String toString() {
        return "WebSocketMessageBean{" +
                "id='" + id + '\'' +
                ", appId='" + appId + '\'' +
                ", method='" + method + '\'' +
                ", params=" + params +
                '}';
    }

    @Keep
    public static class Data {
        public int code;
        public String message;
        public String value;

        // MSCNative.launchMiniprogram
        public String url;

        // MSCNative.mockDDD
        public List<MockDDDInfoData> infos;
        public String batchCheckUpdateUrl;

        // MSCNative.restoreMockDDD
        public List<String> appIds;

        @Override
        public String toString() {
            return "Data{" +
                    "code=" + code +
                    ", message='" + message + '\'' +
                    '}';
        }
    }

    @Keep
    public static class MockDDDInfoData {
        public String appId;
        public String url;
        // 单位为s
        public long cacheTimeout;
    }
}
