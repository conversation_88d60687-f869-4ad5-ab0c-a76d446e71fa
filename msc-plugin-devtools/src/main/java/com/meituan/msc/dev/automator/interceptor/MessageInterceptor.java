package com.meituan.msc.dev.automator.interceptor;

import android.support.annotation.NonNull;

import com.meituan.msc.dev.automator.WebSocketMessageBean;

import okhttp3.WebSocket;

/**
 * <AUTHOR>
 * @date 2021/9/13.
 */
public interface MessageInterceptor {

    boolean isMethodMatch(@NonNull String method);

    boolean intercept(@NonNull WebSocket webSocket, @NonNull WebSocketMessageBean messageBean, @NonNull String msg);
}
