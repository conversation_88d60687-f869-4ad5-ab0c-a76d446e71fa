package com.meituan.msc.dev.automator.interceptor;

import android.support.annotation.NonNull;

import com.meituan.msc.common.utils.ToastUtils;
import com.meituan.msc.dev.automator.WebSocketMessageBean;
import com.meituan.msc.modules.api.appLifecycle.MSCAppLifecycle;
import com.meituan.msc.modules.api.appLifecycle.MSCAppLifecycleManager;
import com.meituan.msc.modules.api.appLifecycle.MSCAppLifecycleObserver;
import com.meituan.msc.modules.api.appLifecycle.MSCAppLifecycleParams;
import com.meituan.msc.modules.reporter.MSCLog;


import org.json.JSONException;
import org.json.JSONObject;

import java.util.Arrays;
import java.util.List;

import okhttp3.WebSocket;

public class NativeAddAppLifecycleObserverInterceptor extends NativeInterceptor {

    private static final String TAG = "NativeAddAppLifecycleObserverInterceptor";

    @Override
    public boolean intercept(@NonNull WebSocket webSocket, @NonNull WebSocketMessageBean messageBean, @NonNull String msg) {
        boolean isSuccess = addAppLifecycleObserver(msg);
        String tipsStr = isSuccess ? "success" : "fail";
        MSCLog.d(TAG, "addAppLifecycleObserver " + tipsStr);
        if (isSuccess) {
            returnSuccess(webSocket, messageBean);
        }
        return isSuccess;
    }

    public boolean addAppLifecycleObserver(String msg) {
//        {
//            "id": "4cb50887-cff0-42ad-9bdb-ed8d4d257475",
//                "method": "MSCNative.addAppLifecycleObserver",
//                "params":
//            {
//                appId:"qa_mscdemo_2"
//            }
//        }
        try {
            JSONObject jsonObject = new JSONObject(msg);
            JSONObject params = jsonObject.optJSONObject("params");
            if (params == null) {
                return false;
            }
            String appId = params.optString("appId");
            MSCAppLifecycleObserver observer = new MSCAppLifecycleObserver() {
                @Override
                public void onEvent(MSCAppLifecycle mscAppLifecycleName, MSCAppLifecycleParams params) {
                    if(params == null){
                        params = new MSCAppLifecycleParams();//避免null params
                    }
                    MSCLog.d("TestMSCAppLifecycleObserver",
                            "appId:", getAppId(),
                            "mscAppLifecycleName:", mscAppLifecycleName.toString(),
                            "targetPath:", params.targetPath,
                            "enterUri:", params.enterUri,
                            "leaveAppInfo:", params.leaveAppInfo);
                }

                @Override
                public String getAppId() {
                    return appId;
                }

                @Override
                public List<MSCAppLifecycle> getValidLifecycleList() {
                    List<MSCAppLifecycle> mscAppLifecycleList = Arrays
                            .asList(MSCAppLifecycle.MSC_WILL_ENTER_APP_LIFECYCLE,
                                    MSCAppLifecycle.MSC_DID_ENTER_APP_LIFECYCLE,
                                    MSCAppLifecycle.MSC_WILL_LEAVE_APP_LIFECYCLE,
                                    MSCAppLifecycle.MSC_LAUNCH_FAIL_APP_LIFECYCLE);
                    return mscAppLifecycleList;
                }
            };
            boolean isAdded = MSCAppLifecycleManager.getInstance().addObserver(observer);
            String tip = isAdded? "添加成功" : "添加失败";
            ToastUtils.toast("addAppLifecycleObserver: " + tip);
            MSCLog.d(TAG, "addAppLifecycleObserver: " + tip);
            return isAdded;

        } catch (JSONException e) {
            MSCLog.e(TAG, e);
            return false;
        }
    }


    @Override
    String getMethod() {
        return "MSCNative.addAppLifecycleObserver";
    }
}
