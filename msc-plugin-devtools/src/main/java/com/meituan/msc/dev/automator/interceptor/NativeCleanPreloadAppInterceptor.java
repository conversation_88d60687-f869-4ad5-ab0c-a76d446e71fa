package com.meituan.msc.dev.automator.interceptor;

import android.support.annotation.NonNull;
import android.text.TextUtils;

import com.meituan.msc.common.framework.Callback;
import com.meituan.msc.dev.automator.WebSocketMessageBean;
import com.meituan.msc.modules.preload.PreloadTasksManager;
import com.meituan.msc.modules.reporter.MSCLog;

import org.json.JSONException;
import org.json.JSONObject;
import okhttp3.WebSocket;
/*
 * appId级别的清理预热接口，因为本质是调用cleanPreloadApp方法，所以仍然受到预热黑名单影响
 * 需求文档：https://km.sankuai.com/collabpage/2637998622
 */
public class NativeCleanPreloadAppInterceptor extends NativeInterceptor {

    private static final String TAG = "NativeCleanPreloadAppInterceptor";

    @Override
    public boolean intercept(@NonNull WebSocket webSocket, @NonNull WebSocketMessageBean messageBean, @NonNull String msg) {
        cleanPreloadApp(msg);
        returnSuccess(webSocket, messageBean);
        return true;
    }

    public void cleanPreloadApp(String msg) {
        if (TextUtils.isEmpty(msg)) {
            return;
        }
        JSONObject jsonObject = null;
        try {
            jsonObject = new JSONObject(msg);
        } catch (JSONException e) {
            return;
        }
        JSONObject params = jsonObject.optJSONObject("params");
        if (params == null) {
            return;
        }

        String appId = params.optString("appId");
        if (TextUtils.isEmpty(appId)) {
            return;
        }

        PreloadTasksManager.instance.cleanPreloadApp(appId, new Callback<Void>() {
            @Override
            public void onSuccess(Void data) {
                MSCLog.d(TAG, "clean preload success for appId: " + appId);
            }

            @Override
            public void onFail(String errMsg, Exception error) {
                MSCLog.d(TAG, "clean preload fail for appId: " + appId + ", errMsg: " + errMsg);
            }

            @Override
            public void onCancel() {
                MSCLog.d(TAG, "clean preload cancel for appId: " + appId);
            }
        });
    }

    @Override
    String getMethod() {
        return "MSCNative.cleanPreloadedApp";
    }
}
