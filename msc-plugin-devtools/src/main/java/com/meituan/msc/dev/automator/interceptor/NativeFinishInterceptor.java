package com.meituan.msc.dev.automator.interceptor;

import android.support.annotation.NonNull;

import com.meituan.msc.dev.automator.AutomatorModule;
import com.meituan.msc.dev.automator.WebSocketMessageBean;

import okhttp3.WebSocket;

public class NativeFinishInterceptor extends NativeInterceptor {
    private final AutomatorModule automatorModule;

    public NativeFinishInterceptor(AutomatorModule automatorModule) {
        this.automatorModule = automatorModule;
    }

    @Override
    String getMethod() {
        return "MSCNative.finish";
    }

    @Override
    public boolean intercept(@NonNull WebSocket webSocket, @NonNull WebSocketMessageBean messageBean, @NonNull String msg) {
        automatorModule.finish();
        returnSuccess(webSocket, messageBean);
        return true;
    }
}
