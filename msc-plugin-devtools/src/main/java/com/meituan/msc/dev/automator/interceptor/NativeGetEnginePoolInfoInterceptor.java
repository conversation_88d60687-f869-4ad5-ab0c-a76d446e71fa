package com.meituan.msc.dev.automator.interceptor;

import android.support.annotation.NonNull;

import com.meituan.msc.dev.automator.WebSocketMessageBean;
import com.meituan.msc.modules.engine.RuntimeManager;
import com.meituan.msc.modules.engine.MSCApp;
import com.meituan.msc.modules.engine.MSCRuntime;
import com.meituan.msc.modules.reporter.MSCLog;

import org.json.JSONArray;
import org.json.JSONException;
import org.json.JSONObject;

import java.util.Collection;
import java.util.Map;
import java.util.Set;

import okhttp3.WebSocket;

public class NativeGetEnginePoolInfoInterceptor extends NativeInterceptor {
    private static final String TAG = "NativeGetEnginePoolInfoInterceptor";

    @Override
    public boolean intercept(@NonNull WebSocket webSocket, @NonNull WebSocketMessageBean messageBean, @NonNull String msg) {
        JSONObject result = new JSONObject();
        try {
            JSONArray preloadAppIds = new JSONArray();
            Map<Integer, MSCRuntime> allRunningEngines = RuntimeManager.getAllRunningEngines();
            Collection<MSCRuntime> values = allRunningEngines.values();
            for (MSCRuntime runtime : values) {
                String appID = runtime.getAppId();
                if (appID != null)
                    preloadAppIds.put(runtime.getAppId());
            }
            result.put("runningAppIds", preloadAppIds);


            JSONArray keepAliveAppIds = new JSONArray();
            Map<String, MSCApp> allKeepAliveEngines = RuntimeManager.getAllKeepAliveEngines();
            Set<String> appIdSet = allKeepAliveEngines.keySet();
            for (String appId : appIdSet) {
                keepAliveAppIds.put(appId);
            }
            result.put("keepAliveAppIds", keepAliveAppIds);
        } catch (JSONException e) {
            MSCLog.e(TAG, e);
        }
        returnSuccess(webSocket, messageBean, result);
        return true;
    }

    @Override
    String getMethod() {
        return "MSCNative.getEnginePoolInfo";
    }
}
