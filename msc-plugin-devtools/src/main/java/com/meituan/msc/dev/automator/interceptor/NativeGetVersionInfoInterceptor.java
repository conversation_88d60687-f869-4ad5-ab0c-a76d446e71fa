package com.meituan.msc.dev.automator.interceptor;

import android.support.annotation.NonNull;

import com.meituan.msc.dev.automator.AutomatorModule;
import com.meituan.msc.dev.automator.WebSocketMessageBean;
import com.meituan.msc.modules.reporter.MSCLog;

import org.json.JSONException;
import org.json.JSONObject;

import okhttp3.WebSocket;

public class NativeGetVersionInfoInterceptor extends NativeInterceptor {
    private static final String TAG = "NativeGetVersionInfoInterceptor";
    private final AutomatorModule automatorModule;

    public NativeGetVersionInfoInterceptor(AutomatorModule automatorModule) {
        this.automatorModule = automatorModule;
    }

    @Override
    public boolean intercept(@NonNull WebSocket webSocket, @NonNull WebSocketMessageBean messageBean, @NonNull String msg) {
        JSONObject jsonObject = new JSONObject();
        if (automatorModule != null) {
            try {
                jsonObject.put("buildId", automatorModule.getBuildId());
                jsonObject.put("sdkVersion", automatorModule.getSDKVersion());
                jsonObject.put("version", automatorModule.getVersion());
            } catch (JSONException e) {
                MSCLog.e(TAG, e);
            }
        }
        returnSuccess(webSocket, messageBean, jsonObject);
        return true;
    }

    @Override
    String getMethod() {
        return "MSCNative.getVersionInfo";
    }
}
