package com.meituan.msc.dev.automator.interceptor;

import android.content.SharedPreferences;
import android.support.annotation.NonNull;
import android.text.TextUtils;

import com.meituan.msc.dev.automator.WebSocketMessageBean;
import com.meituan.msc.dev.utils.AppContextGetter;
import com.meituan.msc.extern.MSCEnvHelper;
import com.meituan.msc.modules.reporter.MSCLog;

import org.json.JSONException;
import org.json.JSONObject;

import okhttp3.WebSocket;

/**
 * <AUTHOR>
 * @date 2021/9/13.
 */
public abstract class NativeInterceptor implements MessageInterceptor {

    private static final String TAG = "NativeInterceptor";

    @Override
    public boolean isMethodMatch(@NonNull String method) {
        return TextUtils.equals(method, getMethod());
    }

    abstract String getMethod();

    protected void returnSuccess(@NonNull WebSocket webSocket, @NonNull WebSocketMessageBean messageBean, JSONObject result) {
        JSONObject jsonObject = new JSONObject();
        try {
            jsonObject.put("id", messageBean.id);
            // 添加空的result节点原因是前端需要用来判断是成功回调
            // https://www.jsonrpc.org/specification
            jsonObject.put("result", result);
        } catch (JSONException e) {
            e.printStackTrace();
        }
        webSocket.send(jsonObject.toString());
        MSCLog.i(TAG, getMethod(), jsonObject.toString());
    }

    protected void returnSuccess(@NonNull WebSocket webSocket, @NonNull WebSocketMessageBean messageBean) {
        returnSuccess(webSocket, messageBean, new JSONObject());
    }

    public static SharedPreferences getSP() {
        return MSCEnvHelper.getSharedPreferences(AppContextGetter.getContext(), "msc_automator");
    }
}
