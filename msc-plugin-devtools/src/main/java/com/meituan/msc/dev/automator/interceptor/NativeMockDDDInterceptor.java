package com.meituan.msc.dev.automator.interceptor;

import android.support.annotation.NonNull;

import com.meituan.msc.dev.automator.WebSocketMessageBean;
import com.meituan.msc.modules.reporter.MSCLog;
import com.meituan.msc.modules.update.DDLoaderDebugHelper;

import okhttp3.WebSocket;

public class NativeMockDDDInterceptor extends NativeInterceptor {

    private static final String TAG = "NativeMockDDDInterceptor";

    @Override
    String getMethod() {
        return "MSCNative.mockDDD";
    }

    @Override
    public boolean intercept(@NonNull WebSocket webSocket, @NonNull WebSocketMessageBean messageBean, @NonNull String msg) {
        DDLoaderDebugHelper.setCheckUpdateFromTestEnv(true);
        getSP().edit().putString("mockDDD", msg).apply();

        returnSuccess(webSocket, messageBean);
        MSCLog.d(TAG, "mockDDD success");
        return true;
    }
}
