package com.meituan.msc.dev.automator.interceptor;

import android.support.annotation.NonNull;

import com.meituan.msc.dev.automator.WebSocketMessageBean;
import com.meituan.msc.modules.reporter.MSCLog;

import org.json.JSONException;
import org.json.JSONObject;

import okhttp3.WebSocket;

public class NativeMockHornInterceptor extends NativeInterceptor {

    private static final String TAG = "NativeChangeHornInterceptor";
    public static final String KEY_BATCH_CHECK_UPDATE_TIME_INTERVAL = "batch_check_update_time_interval";
    public static final String BACKGROUND_CHECK_UPDATE_TIME_INTERVAL = "background_check_update_time_interval";
    public static final String KEEP_ALIVE_TIME = "keep_alive_time";
    public static final String ENABLE_PRELOAD="enablePreload";
    @Override
    public boolean intercept(@NonNull WebSocket webSocket, @NonNull WebSocketMessageBean messageBean, @NonNull String msg) {
        // 临时方案，后期会替换为直接修改horn配置文件
        mockEngineKeepAliveTime(msg);
        mockBatchCheckUpdateTimeInterval(msg);
        mockBackgroundCheckUpdateTimeInterval(msg);
        mockBanPreLoad(msg);

        returnSuccess(webSocket, messageBean);
        MSCLog.d(TAG, "mockHorn success");
        return true;
    }

    private void mockBanPreLoad(String msg){
        /*
         * {
         *    "id": "4cb50887-cff0-42ad-9bdb-ed8d4d257475",
         *    "method": "MSCNative.mockHorn",
         *    "params":
         *    {
         *        "msc_config":
         *        {
         *            "enablePreload":false //禁用基础包预热
         *        },
         *    }
         * }
         */
        try{
            JSONObject jsonObject = new JSONObject(msg);
            JSONObject params = jsonObject.optJSONObject("params");
            if (params == null) {
                return;
            }
            JSONObject msc_preload = params.optJSONObject("msc_preload");
            if (msc_preload == null) {
                return;
            }
            boolean preloadBasePackage = msc_preload.optBoolean("enablePreload",false);
            getSP().edit().putBoolean(ENABLE_PRELOAD,preloadBasePackage).apply();
        }catch (JSONException e){
            MSCLog.e(TAG,e);
        }
    }


    private void mockBackgroundCheckUpdateTimeInterval(String msg) {
        /*
         * {
         *    "id": "4cb50887-cff0-42ad-9bdb-ed8d4d257475",
         *    "method": "MSCNative.changeHorn",
         *    "params":
         *    {
         *        "msc_config":
         *        {
         *            "batch_update_time": 1
         *        }
         *    }
         * }
         */
        try {
            JSONObject jsonObject = new JSONObject(msg);
            JSONObject params = jsonObject.optJSONObject("params");
            if (params == null) {
                return;
            }
            JSONObject mscConfig = params.optJSONObject("msc_config");
            if (mscConfig == null) {
                return;
            }

            // 单位 秒
            long batchUpdateTime = mscConfig.optLong("background_update_time", 0);
            if (batchUpdateTime <= 0) {
                return;
            }

            getSP().edit().putLong(BACKGROUND_CHECK_UPDATE_TIME_INTERVAL, batchUpdateTime * 1000).apply();
        } catch (JSONException e) {
            MSCLog.e(TAG, e);
        }
    }

    private void mockBatchCheckUpdateTimeInterval(String msg) {
        /*
         * {
         *    "id": "4cb50887-cff0-42ad-9bdb-ed8d4d257475",
         *    "method": "MSCNative.changeHorn",
         *    "params":
         *    {
         *        "msc_config":
         *        {
         *            "batch_update_time": 1
         *        }
         *    }
         * }
         */
        try {
            JSONObject jsonObject = new JSONObject(msg);
            JSONObject params = jsonObject.optJSONObject("params");
            if (params == null) {
                return;
            }
            JSONObject mscConfig = params.optJSONObject("msc_config");
            if (mscConfig == null) {
                return;
            }

            // 单位 秒
            long batchUpdateTime = mscConfig.optLong("batch_update_time", 0);
            if (batchUpdateTime <= 0) {
                return;
            }

            getSP().edit().putLong(KEY_BATCH_CHECK_UPDATE_TIME_INTERVAL, batchUpdateTime * 1000).apply();
        } catch (JSONException e) {
            MSCLog.e(TAG, e);
        }
    }

    private void mockEngineKeepAliveTime(String msg) {
        /*
         * {
         *    "id": "4cb50887-cff0-42ad-9bdb-ed8d4d257475",
         *    "method": "MSCNative.changeHorn",
         *    "params":
         *    {
         *        "msc_config":
         *        {
         *            "keep_alive_time": 300
         *        },
         *    }
         * }
         */
        try {
            JSONObject jsonObject = new JSONObject(msg);
            JSONObject params = jsonObject.optJSONObject("params");
            if (params == null) {
                return;
            }
            JSONObject mmpConfig = params.optJSONObject("msc_config");
            if (mmpConfig == null) {
                return;
            }
            // 单位 秒
            long keepAliveTime = mmpConfig.optLong("keep_alive_time", 0);
            if (keepAliveTime <= 0) {
                return;
            }
            getSP().edit().putLong(KEEP_ALIVE_TIME, keepAliveTime).apply();
        } catch (JSONException e) {
            MSCLog.e(TAG, e);
        }
    }

    @Override
    String getMethod() {
        return "MSCNative.mockHorn";
    }
}
