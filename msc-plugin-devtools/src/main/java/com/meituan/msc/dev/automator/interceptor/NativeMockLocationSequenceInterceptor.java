package com.meituan.msc.dev.automator.interceptor;

import android.support.annotation.NonNull;

import com.meituan.msc.dev.automator.WebSocketMessageBean;
import com.meituan.msc.modules.devtools.automator.LocationMockData;

import okhttp3.WebSocket;

/**
 * <AUTHOR>
 * @date 2021/9/13.
 */
public class NativeMockLocationSequenceInterceptor extends NativeInterceptor {

    @Override
    String getMethod() {
        return "MSCNative.setLocationSequence";
    }

    @Override
    public boolean intercept(@NonNull WebSocket webSocket, @NonNull WebSocketMessageBean messageBean, @NonNull String msg) {
        LocationMockData.sMockLocationArrayData = msg;
        returnSuccess(webSocket, messageBean);
        return true;
    }
}
