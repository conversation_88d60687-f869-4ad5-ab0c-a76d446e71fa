package com.meituan.msc.dev.automator.interceptor;

import android.support.annotation.NonNull;

import com.meituan.msc.dev.automator.AutomatorModule;
import com.meituan.msc.dev.automator.WebSocketMessageBean;
import com.meituan.msc.modules.reporter.MSCLog;

import org.json.JSONObject;

import okhttp3.WebSocket;

public class NativeMonitorMetricsInterceptor extends NativeInterceptor {
    private static final String TAG = "NativeMonitorMetricsInterceptor";

    private final AutomatorModule automatorModule;

    public NativeMonitorMetricsInterceptor(AutomatorModule automatorModule) {
        this.automatorModule = automatorModule;
    }

    @Override
    String getMethod() {
        return "MSCNative.metrics";
    }

    @Override
    public boolean intercept(@NonNull WebSocket webSocket, @NonNull WebSocketMessageBean messageBean, @NonNull String msg) {
        JSONObject details = automatorModule.getMetricsInfo();
        returnSuccess(webSocket, messageBean, details);
        MSCLog.d(TAG, "stop monitoring fps: ", details == null ? "null" : details.toString());
        return true;
    }
}
