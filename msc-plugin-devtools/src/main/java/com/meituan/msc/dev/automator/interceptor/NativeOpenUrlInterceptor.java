package com.meituan.msc.dev.automator.interceptor;

import android.content.Context;
import android.content.Intent;
import android.content.pm.PackageManager;
import android.content.pm.ResolveInfo;
import android.net.Uri;
import android.support.annotation.NonNull;
import android.support.annotation.Nullable;
import android.text.TextUtils;

import com.meituan.msc.dev.automator.WebSocketMessageBean;
import com.meituan.msc.modules.reporter.MSCLog;
import com.meituan.msc.extern.MSCEnvHelper;

import org.json.JSONException;
import org.json.JSONObject;

import okhttp3.WebSocket;

/**
 * <AUTHOR>
 * @date 2021/9/13.
 */
public class NativeOpenUrlInterceptor extends NativeInterceptor {

    public static final String TAG = "NativeOpenUrlInterceptor";

    @Override
    String getMethod() {
        return "MSCNative.openUrl";
    }

    @Override
    public boolean intercept(@NonNull WebSocket webSocket, @NonNull WebSocketMessageBean messageBean, @NonNull String msg) {
        WebSocketMessageBean.Data params = messageBean.params;
        if (params == null || TextUtils.isEmpty(params.url)) {
            returnError(webSocket, messageBean.id, "MSCNative.openUrl params is null");
            MSCLog.e(TAG, "dealNativeMessage MSCNative.openUrl params is null");
            return true;
        }

        openUrl(webSocket, messageBean, params);
        return true;
    }

    private void openUrl(WebSocket webSocket, @NonNull WebSocketMessageBean messageBean, WebSocketMessageBean.Data params) {
        Intent intent = new Intent(Intent.ACTION_VIEW, Uri.parse(params.url));
        Context context = MSCEnvHelper.getContext();
        PackageManager packageManager = context.getPackageManager();
        if (packageManager == null) {
            returnError(webSocket, messageBean.id, "MSCNative.openUrl packageManager is null");
            return;
        }

        Intent hostIntent = new Intent(intent);
        hostIntent.setPackage(context.getPackageName());
        ResolveInfo resolveActivity = null;

        try {
            resolveActivity = packageManager.resolveActivity(hostIntent, PackageManager.MATCH_DEFAULT_ONLY);
        } catch (RuntimeException e) {
            String error = "MSCNative.openUrl error1:" + e.toString();
            MSCLog.e(TAG, error);
        }

        if (resolveActivity == null || resolveActivity.activityInfo == null) {
            try {
                resolveActivity = packageManager.resolveActivity(hostIntent, 0);
            } catch (RuntimeException e) {
                String error = "MSCNative.openUrl error2:" + e.toString();
                MSCLog.e(TAG, error);
            }
        }

        if (resolveActivity != null && resolveActivity.activityInfo != null) {
            hostIntent.addFlags(Intent.FLAG_ACTIVITY_NEW_TASK);
            context.startActivity(hostIntent);
            returnSuccess(webSocket, messageBean);
        } else {
            returnError(webSocket, messageBean.id, "MSCNative.openUrl resolveActivity is null");
        }
    }

    private void returnError(WebSocket webSocket, String id, @Nullable String errorDes) {
        JSONObject jsonObject = new JSONObject();
        try {
            jsonObject.put("id", id);
            JSONObject error = new JSONObject();
            error.put("message", errorDes);
            jsonObject.put("error", error);
        } catch (JSONException e) {
            e.printStackTrace();
        }
        webSocket.send(jsonObject.toString());
    }
}
