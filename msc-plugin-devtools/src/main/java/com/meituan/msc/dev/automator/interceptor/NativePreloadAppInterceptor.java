package com.meituan.msc.dev.automator.interceptor;

import android.support.annotation.NonNull;
import android.text.TextUtils;

import com.meituan.msc.common.framework.Callback;
import com.meituan.msc.dev.automator.WebSocketMessageBean;
import com.meituan.msc.extern.MSCEnvHelper;
import com.meituan.msc.modules.preload.PreloadManager;
import com.meituan.msc.modules.preload.PreloadResultData;
import com.meituan.msc.modules.reporter.MSCLog;

import org.json.JSONException;
import org.json.JSONObject;

import okhttp3.WebSocket;
/*
 * 自动化测试中业务预热接口，因为本质是调用preloadBiz方法，所以仍然受到预热黑名单影响
 * 需求文档：https://km.sankuai.com/collabpage/2637998622
 */
public class NativePreloadAppInterceptor extends NativeInterceptor {

    private static final String TAG = "NativePreloadAppInterceptor";

    @Override
    public boolean intercept(@NonNull WebSocket webSocket, @NonNull WebSocketMessageBean messageBean, @NonNull String msg) {
        boolean result = preloadAppWithPath(msg);
        if (result) {
            returnSuccess(webSocket, messageBean);
        }
        return result;
    }

    public boolean preloadAppWithPath(String msg) {
        if (TextUtils.isEmpty(msg)) {
            return false;
        }
        JSONObject jsonObject = null;
        try {
            jsonObject = new JSONObject(msg);
        } catch (JSONException e) {
            return false;
        }

        JSONObject params = jsonObject.optJSONObject("params");
        if (params == null) {
            return false;
        }

        String appId = params.optString("appId");
        String path = params.optString("path");
        if (TextUtils.isEmpty(appId)) {
            return false;
        }
        MSCEnvHelper.ensureFullInited();
        PreloadManager.getInstance().preloadBiz(MSCEnvHelper.getContext(), appId, path, false, new Callback<PreloadResultData>() {

            @Override
            public void onSuccess(PreloadResultData data) {
                MSCLog.d(TAG, "preload success for appId: " + appId + ", path: " + path);
            }

            @Override
            public void onFail(String errMsg, Exception error) {
                MSCLog.d(TAG, "preload fail for appId: " + appId + ", path: " + path + ", errMsg: " + errMsg);
            }

            @Override
            public void onCancel() {
                MSCLog.d(TAG, "preload cancel for appId: " + appId + ", path: " + path);
            }
        });

        return true;
    }

    @Override
    String getMethod() {
        return "MSCNative.preloadApp";
    }
}
