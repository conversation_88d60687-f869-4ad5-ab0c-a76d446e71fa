package com.meituan.msc.dev.automator.interceptor;


import android.support.annotation.NonNull;

import com.meituan.msc.dev.automator.WebSocketMessageBean;
import com.meituan.msc.modules.reporter.MSCLog;

import okhttp3.WebSocket;

public class NativeRestoreHornInterceptor extends NativeInterceptor {
    private static final String TAG = "NativeRestoreHornInterceptor";

    @Override
    public boolean intercept(@NonNull WebSocket webSocket, @NonNull WebSocketMessageBean messageBean, @NonNull String msg) {
        getSP().edit().remove(NativeMockHornInterceptor.KEY_BATCH_CHECK_UPDATE_TIME_INTERVAL).apply();
        getSP().edit().remove(NativeMockHornInterceptor.KEEP_ALIVE_TIME).apply();
        getSP().edit().remove(NativeMockHornInterceptor.BACKGROUND_CHECK_UPDATE_TIME_INTERVAL).apply();
        getSP().edit().remove(NativeMockHornInterceptor.ENABLE_PRELOAD).apply();
        returnSuccess(webSocket, messageBean);
        MSCLog.d(TAG, "restoreMockHorn success");
        return true;
    }

    @Override
    String getMethod() {
        return "MSCNative.restoreMockHorn";
    }
}
