package com.meituan.msc.dev.automator.interceptor;

import android.content.SharedPreferences;
import android.support.annotation.NonNull;
import android.text.TextUtils;

import com.google.gson.reflect.TypeToken;
import com.meituan.msc.common.utils.CollectionUtil;
import com.meituan.msc.common.utils.CommonGson;
import com.meituan.msc.dev.automator.WebSocketMessageBean;
import com.meituan.msc.modules.reporter.MSCLog;
import com.meituan.msc.modules.update.DDLoaderDebugHelper;

import java.util.ArrayList;
import java.util.LinkedList;
import java.util.List;

import okhttp3.WebSocket;

public class NativeRestoreMockDDDInterceptor extends NativeInterceptor {
    private static final String TAG = "NativeRestoreMockDDDInterceptor";

    @Override
    public boolean intercept(@NonNull WebSocket webSocket, @NonNull WebSocketMessageBean messageBean, @NonNull String msg) {
        DDLoaderDebugHelper.setCheckUpdateFromTestEnv(false);

        List<String> appIds = messageBean.params.appIds;
        if (CollectionUtil.isEmpty(appIds)) {
            getSP().edit().remove("mockDDD").apply();
        } else {
            restoreMockData(messageBean.params.appIds);
        }

        returnSuccess(webSocket, messageBean);
        MSCLog.d(TAG, "restoreMockDDD success");
        return true;
    }

    private void restoreMockData(List<String> appIds) {
        SharedPreferences preferences = getSP();
        String mockDDD = preferences.getString("mockDDD", "");
        if (!TextUtils.isEmpty(mockDDD)) {
            WebSocketMessageBean webSocketMessageBean = CommonGson.GSON.fromJson(mockDDD, WebSocketMessageBean.class);
            if (webSocketMessageBean.params == null || webSocketMessageBean.params.infos == null) {
                return;
            }

            ArrayList<WebSocketMessageBean.MockDDDInfoData> newMockDDDInfoList = new ArrayList<>();
            for (WebSocketMessageBean.MockDDDInfoData info : webSocketMessageBean.params.infos) {
                boolean needDelete = false;
                for (String appId : appIds) {
                    if (TextUtils.equals(info.appId, appId)) {
                        needDelete = true;
                    }
                }
                if (!needDelete) {
                    newMockDDDInfoList.add(info);
                }
            }

            preferences.edit().putString("mockDDD", CommonGson.GSON.toJson(newMockDDDInfoList)).apply();
        }
    }

    @Override
    String getMethod() {
        return "MSCNative.restoreMockDDD";
    }
}
