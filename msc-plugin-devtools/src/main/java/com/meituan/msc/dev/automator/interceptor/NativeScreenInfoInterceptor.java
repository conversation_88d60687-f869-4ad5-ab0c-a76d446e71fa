package com.meituan.msc.dev.automator.interceptor;

import android.support.annotation.NonNull;

import com.meituan.msc.dev.automator.AutomatorModule;
import com.meituan.msc.dev.automator.WebSocketMessageBean;
import com.meituan.msc.dev.performance.FPSMonitor;
import com.meituan.msc.modules.reporter.MSCLog;

import org.json.JSONException;
import org.json.JSONObject;

import okhttp3.WebSocket;

public class NativeScreenInfoInterceptor extends NativeInterceptor {

    private static final String TAG = "NativeScreenInfoInterceptor";
    private final AutomatorModule automatorModule;

    public NativeScreenInfoInterceptor(AutomatorModule automatorModule) {
        this.automatorModule = automatorModule;
    }

    @Override
    public boolean intercept(@NonNull WebSocket webSocket, @NonNull WebSocketMessageBean messageBean, @NonNull String msg) {
        JSONObject result = automatorModule.screenInfo();
        returnSuccess(webSocket, messageBean, result);
        MSCLog.d(TAG, "screen info: "+result.toString());
        return true;
    }


    @Override
    String getMethod() {
        return "MSCNative.screenInfo";
    }
}
