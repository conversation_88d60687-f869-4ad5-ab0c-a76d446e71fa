package com.meituan.msc.dev.automator.interceptor;

import android.support.annotation.NonNull;

import com.meituan.msc.common.utils.ScreenDensityUtil;
import com.meituan.msc.common.utils.ScreenUtil;
import com.meituan.msc.dev.automator.AutomatorModule;
import com.meituan.msc.dev.automator.WebSocketMessageBean;
import com.meituan.msc.dev.performance.FPSMonitor;
import com.meituan.msc.modules.reporter.MSCLog;

import org.json.JSONException;
import org.json.JSONObject;

import okhttp3.WebSocket;

public class NativeStartMonitorFPSInterceptor extends NativeInterceptor {

    private static final String TAG = "NativeStartMonitorFPSInterceptor";
    private final AutomatorModule automatorModule;

    public NativeStartMonitorFPSInterceptor(AutomatorModule automatorModule) {
        this.automatorModule = automatorModule;
    }

    @Override
    public boolean intercept(@NonNull WebSocket webSocket, @NonNull WebSocketMessageBean messageBean, @NonNull String msg) {
        int offset = automatorModule.startMonitorFPS();
        JSONObject result = new JSONObject();
        try {
            result.put("contentOffset", offset);
            result.put("scale", FPSMonitor.getInstance().getScreenDensity());
        } catch (JSONException e) {
            e.printStackTrace();
        }
        returnSuccess(webSocket, messageBean, result);
        MSCLog.d(TAG, "start monitoring fps: "+result.toString());
        return true;
    }


    @Override
    String getMethod() {
        return "MSCNative.startMonitorFPS";
    }
}