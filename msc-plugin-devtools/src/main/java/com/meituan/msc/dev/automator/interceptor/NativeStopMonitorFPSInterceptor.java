package com.meituan.msc.dev.automator.interceptor;

import android.support.annotation.NonNull;

import com.meituan.msc.dev.automator.AutomatorModule;
import com.meituan.msc.dev.automator.WebSocketMessageBean;
import com.meituan.msc.modules.reporter.MSCLog;

import org.json.JSONObject;

import okhttp3.WebSocket;

public class NativeStopMonitorFPSInterceptor extends NativeInterceptor {

    private static final String TAG = "NativeStartMonitorFPSInterceptor";
    private final AutomatorModule automatorModule;

    public NativeStopMonitorFPSInterceptor(AutomatorModule automatorModule) {
        this.automatorModule = automatorModule;
    }

    @Override
    public boolean intercept(@NonNull WebSocket webSocket, @NonNull WebSocketMessageBean messageBean, @NonNull String msg) {
        JSONObject result = automatorModule.stopMonitorFPS();

        returnSuccess(webSocket, messageBean, result);
        MSCLog.d(TAG, "stop monitoring fps: "+result.toString());
        return true;
    }


    @Override
    String getMethod() {
        return "MSCNative.stopMonitorFPS";
    }
}
