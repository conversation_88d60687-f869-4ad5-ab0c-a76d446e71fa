package com.meituan.msc.dev.automator.interceptor;

import android.support.annotation.NonNull;

import com.meituan.msc.dev.automator.AutomatorManager;
import com.meituan.msc.dev.automator.WebSocketMessageBean;
import com.meituan.msc.modules.reporter.MSCLog;
import com.meituan.msc.common.utils.ToastUtils;

import okhttp3.WebSocket;

/**
 * <AUTHOR>
 * @date 2021/9/13.
 */
public class SystemCloseInterceptor extends SystemInterceptor {

    private static final String TAG = "SystemCloseInterceptor";
    private final AutomatorManager manager;

    public SystemCloseInterceptor(@NonNull AutomatorManager manager) {
        this.manager = manager;
    }

    @Override
    String getMethod() {
        return "System.close";
    }

    @Override
    public boolean intercept(@NonNull WebSocket webSocket, @NonNull WebSocketMessageBean messageBean, @NonNull String msg) {
        ToastUtils.toastIfDebug(messageBean.toString());
        webSocket.close(1000, "System.close");
        manager.releaseSocket();

        MSCLog.e(TAG, messageBean.toString());
        return true;
    }
}
