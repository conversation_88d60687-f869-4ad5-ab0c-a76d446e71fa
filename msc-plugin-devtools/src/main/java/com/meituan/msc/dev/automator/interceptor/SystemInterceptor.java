package com.meituan.msc.dev.automator.interceptor;

import android.support.annotation.NonNull;
import android.text.TextUtils;

/**
 * <AUTHOR>
 * @date 2021/9/13.
 */
public abstract class SystemInterceptor implements MessageInterceptor {

    @Override
    public boolean isMethodMatch(@NonNull String method) {
        return TextUtils.equals(method, getMethod());
    }

    abstract String getMethod();
}
