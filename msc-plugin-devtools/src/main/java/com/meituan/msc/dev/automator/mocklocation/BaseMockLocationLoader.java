package com.meituan.msc.dev.automator.mocklocation;

import android.os.Build;
import android.support.annotation.NonNull;
import android.support.annotation.Nullable;
import android.text.TextUtils;

import com.meituan.msc.modules.devtools.automator.AutomatorManagerLoader;
import com.meituan.msc.modules.devtools.automator.IAutomatorManager;
import com.meituan.msc.modules.devtools.automator.LocationMockData;
import com.meituan.msc.modules.reporter.MSCLog;
import com.meituan.msi.api.location.MsiLocation;
import com.meituan.msi.provider.LocationLoaderConfig;

import org.json.JSONArray;
import org.json.JSONException;
import org.json.JSONObject;

/**
 * <AUTHOR>
 * @date 2021/9/23.
 */
public class BaseMockLocationLoader {
    private static final String TAG = "BaseMockLocationLoader";
    private final LocationLoaderConfig.LoadStrategy strategy;
    private int mockArrayDataIndex;

    public BaseMockLocationLoader(@NonNull LocationLoaderConfig locationLoaderConfig) {
        this.strategy = locationLoaderConfig.loadStrategy;
    }

    public void mockLocationData(@NonNull MsiLocation mockLocation) {
        changeLocationData(mockLocation);
    }

    protected boolean enableLocationMock() {
        IAutomatorManager automatorManager = AutomatorManagerLoader.getInstance();
        if (automatorManager == null) {
            return false;
        }
        if (!automatorManager.isConnect()) {
            return false;
        }
        return !TextUtils.isEmpty(LocationMockData.sMockLocationData) || !TextUtils.isEmpty(LocationMockData.sMockLocationArrayData);
    }

    /**
     * 如果存在单次定位的mock数据，就认为是在进行单次定位；否则认为是在进行持续定位，返回持续定位数据
     */
    protected void changeLocationData(@NonNull MsiLocation mockLocation) {
        JSONObject jsonObject = null;
        // 单次定位
        if (strategy == LocationLoaderConfig.LoadStrategy.normal) {
            jsonObject = getMockLocationData();
        } else if (strategy == LocationLoaderConfig.LoadStrategy.instant_forground
                || strategy == LocationLoaderConfig.LoadStrategy.instant_background) {
            // 持续定位
            jsonObject = getMockLocationArrayData();
        }

        if (jsonObject == null) {
            return;
        }

        modifyLocationParams(mockLocation, jsonObject);
    }

    @Nullable
    protected JSONObject getMockLocationData() {
        JSONObject data = null;
        try {
            String locationData = LocationMockData.sMockLocationData;
            if (locationData != null) {
                data = new JSONObject(locationData);
            }
        } catch (JSONException e) {
            MSCLog.e(TAG, "changeLocationData:" + e.toString());
        }
        if (data != null) {
            return data.optJSONObject("params");
        }
        return null;
    }

    @Nullable
    protected JSONObject getMockLocationArrayData() {
        JSONObject data = null;
        try {
            String locationArrayData = LocationMockData.sMockLocationArrayData;
            if (locationArrayData == null) {
                return null;
            }

            JSONObject locationJson = new JSONObject(locationArrayData);
            JSONObject params = locationJson.optJSONObject("params");
            if (params == null) {
                return null;
            }

            JSONArray locations = params.optJSONArray("locations");
            if (locations == null) {
                return null;
            }
            int size = locations.length();
            data = locations.optJSONObject(mockArrayDataIndex % size);
            mockArrayDataIndex++;
        } catch (JSONException e) {
            String error = "sMockLocationSequence parse error:" + e.toString();
            MSCLog.e(TAG, error);
        }
        return data;
    }

    protected void modifyLocationParams(@NonNull MsiLocation location, JSONObject mockLocation) {
        try {
            location.latitude = mockLocation.optDouble("latitude", 0);
            location.longitude = mockLocation.optDouble("longitude", 0);

            location.altitude = mockLocation.optDouble("altitude");
            location.speed = (float) mockLocation.optDouble("speed");
            location.accuracy = (float) mockLocation.optDouble("accuracy");

            // Android 8.0及以上
            if (Build.VERSION.SDK_INT >= Build.VERSION_CODES.O) {
                location.verticalAccuracy = ((float) mockLocation.optDouble("verticalAccuracy"));
            }

            // horizontalAccuracy始终为0，Location没有对应的Set方法

            // provider
            location.provider = mockLocation.optString("provider");

            // _mtGotTimestamp
            location._mtGotTimestamp = mockLocation.optLong("_mtGotTimestamp", 0);

            // mtTimestamp
            location.mtTimestamp = mockLocation.optLong("mtTimestamp", 0);
        } catch (Throwable e) {
            MSCLog.d(TAG, "modifyLocationParams:", e);
        }
    }

    public void resetMockArrayDataIndex() {
        mockArrayDataIndex = 0;
    }
}
