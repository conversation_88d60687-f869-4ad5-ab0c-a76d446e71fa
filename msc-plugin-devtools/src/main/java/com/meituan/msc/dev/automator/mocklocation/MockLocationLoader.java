package com.meituan.msc.dev.automator.mocklocation;

import android.support.annotation.NonNull;

import com.meituan.msc.modules.api.map.ILocation;
import com.meituan.msc.modules.api.map.ILocationLoader;
import com.meituan.msi.api.location.MsiLocation;
import com.meituan.msi.provider.LocationLoaderConfig;

/**
 * <AUTHOR>
 * @date 2021/9/23.
 */
public class MockLocationLoader extends BaseMockLocationLoader implements ILocationLoader {

    @NonNull
    private final ILocationLoader locationLoader;

    public MockLocationLoader(@NonNull ILocationLoader locationLoader, @NonNull LocationLoaderConfig strategy) {
        super(strategy);
        this.locationLoader = locationLoader;
    }

    @Override
    public void startLocation(final ILocation location, String type) {
        locationLoader.startLocation(new ILocation() {
            @Override
            public void onLocation(int error, MsiLocation locationData, String errMsg) {
                if (enableLocationMock()) {
                    changeLocationData(locationData);
                    location.onLocation(error, locationData, errMsg);
                } else {
                    location.onLocation(error, locationData, errMsg);
                }
            }
        }, type);
    }

    @Override
    public void stopLocation() {
        locationLoader.stopLocation();

        // 重置持续定位数组下标
        resetMockArrayDataIndex();
    }
}
