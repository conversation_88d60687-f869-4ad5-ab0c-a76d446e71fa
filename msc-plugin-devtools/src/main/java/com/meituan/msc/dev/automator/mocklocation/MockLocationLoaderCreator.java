package com.meituan.msc.dev.automator.mocklocation;

import android.support.annotation.NonNull;

import com.meituan.msc.modules.devtools.automator.IMockLocationLoaderCreator;
import com.meituan.msc.modules.api.map.ILocationLoader;
import com.meituan.msi.location.IMsiLocationLoader;
import com.meituan.msi.provider.LocationLoaderConfig;
import com.sankuai.meituan.serviceloader.annotation.ServiceLoaderInterface;

/**
 * <AUTHOR>
 * @date 2021/9/23.
 */
@ServiceLoaderInterface(key = "mock_location_loader_creator", interfaceClass = IMockLocationLoaderCreator.class)
public class MockLocationLoaderCreator implements IMockLocationLoaderCreator {

    @NonNull
    @Override
    public ILocationLoader createMockLocationLoader(@NonNull ILocationLoader locationLoader,
                                                    @NonNull LocationLoaderConfig locationLoaderConfig) {
        return new MockLocationLoader(locationLoader, locationLoaderConfig);
    }

    @NonNull
    @Override
    public IMsiLocationLoader createMsiMockLocationLoader(@NonNull IMsiLocationLoader msiLocationLoader,
                                                          @NonNull LocationLoaderConfig locationLoaderConfig) {
        return new MsiMockLocationLoader(msiLocationLoader, locationLoaderConfig);
    }
}
