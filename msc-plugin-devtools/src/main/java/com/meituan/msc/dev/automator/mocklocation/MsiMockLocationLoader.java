package com.meituan.msc.dev.automator.mocklocation;

import android.support.annotation.NonNull;

import com.meituan.msi.api.location.MsiLocation;
import com.meituan.msi.location.IMsiLocation;
import com.meituan.msi.location.IMsiLocationLoader;
import com.meituan.msi.provider.LocationLoaderConfig;

/**
 * <AUTHOR>
 * @date 2021/9/23.
 */
public class MsiMockLocationLoader extends BaseMockLocationLoader implements IMsiLocationLoader {

    @NonNull
    private final IMsiLocationLoader msiLocationLoader;

    public MsiMockLocationLoader(@NonNull IMsiLocationLoader loader, @NonNull LocationLoaderConfig strategy) {
        super(strategy);
        this.msiLocationLoader = loader;
    }

    @Override
    public void startLocation(final IMsiLocation msiLocation, String type) {
        msiLocationLoader.startLocation(new IMsiLocation() {
            @Override
            public void onLocation(int i, MsiLocation location, String errorMsg) {
                if (enableLocationMock()) {
                    mockLocationData(location);
                }
                msiLocation.onLocation(i, location, errorMsg);
            }
        }, type);
    }

    @Override
    public void stopLocation() {
        msiLocationLoader.stopLocation();

        // 重置持续定位数组下标
        resetMockArrayDataIndex();
    }
}
