package com.meituan.msc.dev.automator.mockrequest;

import com.google.gson.JsonObject;
import com.meituan.msc.dev.automator.mockrequest.MockDataBean;
import com.meituan.msc.dev.automator.mockrequest.MockRequestManager;
import com.meituan.msc.modules.reporter.MSCLog;
import com.meituan.msi.api.ApiRequest;
import com.meituan.msi.api.ApiResponse;
import com.meituan.msi.bean.ApiException;
import com.meituan.msi.interceptor.ApiInterceptor;
import com.meituan.msi.interceptor.InterceptorPriority;

import org.json.JSONException;
import org.json.JSONObject;

import java.util.HashMap;

public class MSCRequestInterceptor implements ApiInterceptor {
    private final String TAG = "MockRequest";
    @Override
    public ApiResponse<?> intercept(Chain chain) throws ApiException {
        ApiRequest<?> apiRequest = chain.request();
        // 如果未开启mockRequest功能，直接跳过
        if (!MockRequestManager.getInstance().enableMockRequest()) {
            return chain.proceed(apiRequest);
        }
        // 拦截网络请求
        if ("request".equals(apiRequest.getName())) {
            JsonObject jsonObject = chain.getContext().getArgs().getAsJsonObject();
            // 根据data中的字段进行匹配，返回符合的参数
            MockDataBean mockData = MockRequestManager.getInstance().getMockData(jsonObject);
            if (mockData != null) {
                int delayTime = mockData.getDelay();
                JSONObject response = new JSONObject();
                try {
                    response.put("data", mockData.getResponse());
                    response.put("header", mockData.getHeader());
                    response.put("statusCode", mockData.getStatusCode());
                    response.put("profile", new HashMap<>());
                } catch (JSONException e) {
                    MSCLog.e(TAG, e);
                }
                // 延迟一段时间后返回数值
                if (delayTime > 0) {
                    try {
                        Thread.sleep(delayTime);
                    } catch (InterruptedException e) {
                        MSCLog.e(TAG, e);
                    }
                }
                chain.getContext().onSuccess(response);
                MSCLog.i(TAG, "Mock Request Success,result:" + response.toString());
                return null;
            }
        }
        return chain.proceed(apiRequest);
    }

    @Override
    public int priority() {
        return InterceptorPriority.BEFORE_INNER_INTERCEPTOR;
    }
}
