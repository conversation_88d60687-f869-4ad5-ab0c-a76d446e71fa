package com.meituan.msc.dev.automator.mockrequest;

import android.support.annotation.Keep;

/**
 * AppMock平台配置的信息
 */
@Keep
public class MockDataBean {
    private int delay; // 模拟网络延时
    private String rule; // URL 匹配规则
    private String paramRule; // param 匹配规则
    private String method; // 请求方法
    private String response; // 响应体内容
    private int statusCode; // 响应的 HTTP 状态码
    private String header; // 响应头

    public int getDelay() {
        return delay;
    }

    public void setDelay(int delay) {
        this.delay = delay;
    }

    public String getRule() {
        return rule;
    }

    public void setRule(String rule) {
        this.rule = rule;
    }

    public String getParamRule() {
        return paramRule;
    }

    public void setParamRule(String paramRule) {
        this.paramRule = paramRule;
    }

    public String getMethod() {
        return method;
    }

    public void setMethod(String method) {
        this.method = method;
    }

    public String getResponse() {
        return response;
    }

    public void setResponse(String response) {
        this.response = response;
    }

    public int getStatusCode() {
        return statusCode;
    }

    public void setStatusCode(int statusCode) {
        this.statusCode = statusCode;
    }

    public String getHeader() {
        return header;
    }

    public void setHeader(String header) {
        this.header = header;
    }

    @Override
    public String toString() {
        return "MockDataBean{" +
                "delay=" + delay +
                ", rule='" + rule + '\'' +
                ", paramRule='" + paramRule + '\'' +
                ", method='" + method + '\'' +
                ", response='" + response + '\'' +
                ", statusCode=" + statusCode +
                ", header='" + header + '\'' +
                '}';
    }
}