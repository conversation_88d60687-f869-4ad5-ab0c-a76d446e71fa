package com.meituan.msc.dev.automator.mockrequest;

import android.net.Uri;

import com.google.gson.Gson;
import com.google.gson.JsonObject;
import com.google.gson.reflect.TypeToken;
import com.meituan.msc.dev.utils.RequestUtil;
import com.meituan.msc.modules.reporter.MSCLog;
import org.json.JSONException;
import org.json.JSONObject;
import java.io.UnsupportedEncodingException;
import java.net.URLDecoder;
import java.util.ArrayList;
import java.util.HashMap;
import java.util.Map;
import java.util.Objects;
import java.util.Set;
import java.util.regex.Pattern;

public class MockRequestManager {
    private static MockRequestManager instance;
    private final static String TAG = "MockRequest";
    private boolean enableMockRequest = false;
    // key为匹配规则，value为匹配规则对应的mockData数据。优先级mockId>groupId>userId
    private final Map<String, MockDataBean> mockDataByMockIdMap = new HashMap<>();
    private final Map<String, MockDataBean> mockDataByGroupIdMap = new HashMap<>();
    private final Map<String, MockDataBean> mockDataByUserIdMap = new HashMap<>();
    private static final String APP_MOCK_PLAT_HOST = "https://appmock.sankuai.com";
    private static final String GET_MOCK_CONFIGS_BY_USER_ID_PATH = "/appmockapi/mock/getMockConfigs.api";
    private static final String GET_MOCK_CONFIG_BY_MOCK_ID_PATH = "/appmockapi/mock/getMockConfigByMockId.api";
    private static final String GET_MOCK_CONFIGS_BY_GROUP_ID_PATH = "/appmockapi/mockgroup/getMockConfigsByGroupId.api";

    public static synchronized MockRequestManager getInstance() {
        if (instance == null) {
            instance = new MockRequestManager();
        }
        return instance;
    }

    public boolean enableMockRequest() {
        return this.enableMockRequest;
    }

    public void initMockRequest(String mockId, String mockUserId, String mockGroupId) {
        MSCLog.i(TAG, "start init mock request");
        this.enableMockRequest = true;
        this.fetchMockRequestInfo(mockId, mockUserId, mockGroupId);
    }

    public MockDataBean getMockData(JsonObject jsonObject) {
        MockDataBean mockData = getMockDataFromMap(mockDataByMockIdMap, jsonObject);
        if (mockData != null) {
            return mockData;
        }
        mockData = getMockDataFromMap(mockDataByGroupIdMap, jsonObject);
        if (mockData != null) {
            return mockData;
        }
        mockData = getMockDataFromMap(mockDataByUserIdMap, jsonObject);
        return mockData;
    }

    private MockDataBean getMockDataFromMap(Map<String, MockDataBean> mockDataMap, JsonObject jsonObject) {
        String targetUrl = jsonObject.get("url").getAsString();

        for (String matchRule : mockDataMap.keySet()) {
            if (isMatchUrl(targetUrl, matchRule)) {
                MockDataBean mockData = mockDataMap.get(matchRule);
                if (mockData == null) {
                    return null;
                }
                if (mockData.getParamRule() != null && !mockData.getParamRule().isEmpty() && !isMatchParams(targetUrl, mockData.getParamRule())) {
                    MSCLog.i(TAG, "ParamRule not match. targetUrl:" + targetUrl + ",paramRule:" + mockData.getParamRule());
                    return null;
                }
                if (mockData.getMethod() != null && !mockData.getMethod().isEmpty() && !mockData.getMethod().equals(jsonObject.get("method").getAsString())) {
                    MSCLog.i(TAG, "Method not match. mockMethod:" + mockData.getMethod() + ",currentMethod:" + jsonObject.get("method").getAsString());
                    return null;
                }
                return mockData;
            }
        }
        return null;
    }

    public void fetchMockRequestInfo(String mockId, String mockUserId, String mockGroupId) {
        if (mockUserId != null) {
            fetchMockRequestInfoByUserId(mockUserId);
        }
        if (mockGroupId != null) {
            fetchMockRequestInfoByGroupId(mockGroupId);
        }
        if (mockId != null) {
            fetchMockRequestInfoByMockId(mockId);
        }
    }

    private void fetchMockRequestInfoByMockId(String mockId) {
        String appMockUrl = APP_MOCK_PLAT_HOST + GET_MOCK_CONFIG_BY_MOCK_ID_PATH + "?mockId=" + mockId;
        MSCLog.i(TAG, "start fetch mock request info by mockId:" + mockId + ";url:" + appMockUrl);
        // 这里的response得到的是请求appMock平台得到的数据
        RequestUtil.get(appMockUrl).thenAccept(response -> {
            if (response.code() == 200 && response.body() != null) {
                try {
                    Gson gson = new Gson();
                    JSONObject jsonObject = new JSONObject(response.body().string());
                    if (jsonObject.getInt("code") != 1) {
                        return;
                    }
                    String bodyJson = jsonObject.getJSONObject("data").toString();
                    MockDataBean mockData = gson.fromJson(bodyJson, MockDataBean.class);
                    mockDataByMockIdMap.put(mockData.getRule(), mockData);
                    MSCLog.i(TAG, "fetch mock request info by mockId: "+ mockId +" success. result: " + mockData);
                } catch (JSONException e) {
                    MSCLog.e(TAG, "fetchMockRequestInfoByMockId error. mockId:" + mockId + ",error:" + e.toString());
                }
            }
        });
    }

    private void fetchMockRequestInfoByUserId(String userId) {
        String appMockUrl = APP_MOCK_PLAT_HOST + GET_MOCK_CONFIGS_BY_USER_ID_PATH + "?userId=" + userId;
        MSCLog.i(TAG, "start fetch mock request info by userId:" + userId + ";url:" + appMockUrl);
        RequestUtil.get(appMockUrl).thenAccept(response -> {
            if (response.code() == 200 && response.body() != null) {
                try {
                    Gson gson = new Gson();
                    JSONObject jsonObject = new JSONObject(response.body().string());
                    if (jsonObject.getInt("code") != 1) {
                        return;
                    }
                    String bodyJson = jsonObject.getJSONObject("data").toString();
                    // key是groupId，value是groupId下所有mockData数据
                    Map<String, ArrayList<MockDataBean>> mockDataByGroup = gson.fromJson(bodyJson, new TypeToken<Map<String, ArrayList<MockDataBean>>>() {
                    }.getType());
                    for (ArrayList<MockDataBean> mockDataArray : mockDataByGroup.values()) {
                        for (MockDataBean mockData : mockDataArray) {
                            mockDataByUserIdMap.put(mockData.getRule(), mockData);
                            MSCLog.i(TAG, "fetch mock request info by userId: " + userId + " success. result: " + mockData);
                        }
                    }
                } catch (JSONException e) {
                    MSCLog.e(TAG, "fetchMockRequestInfoByUserId error. userId:" + userId + ",error:" + e.toString());
                }
            }
        });
    }

    private void fetchMockRequestInfoByGroupId(String groupId) {
        String appMockUrl = APP_MOCK_PLAT_HOST + GET_MOCK_CONFIGS_BY_GROUP_ID_PATH + "?groupId=" + groupId;
        MSCLog.i(TAG, "start fetch mock request info by groupId:" + groupId);
        RequestUtil.get(appMockUrl).thenAccept(response -> {
            if (response.code() == 200 && response.body() != null) {
                try {
                    Gson gson = new Gson();
                    JSONObject jsonObject = new JSONObject(response.body().string());
                    if (jsonObject.getInt("code") != 1) {
                        return;
                    }
                    String bodyJson = jsonObject.getJSONArray("data").toString();
                    MockDataBean[] mockDataArray = gson.fromJson(bodyJson, MockDataBean[].class);
                    for (MockDataBean mockData : mockDataArray) {
                        mockDataByGroupIdMap.put(mockData.getRule(), mockData);
                        MSCLog.i(TAG, "fetch mock request info by groupId: "+ groupId + " success. result: " + mockData);
                    }
                } catch (JSONException e) {
                    MSCLog.e(TAG, "fetchMockRequestInfoByGroupId error. groupId:" + groupId + ",error:" + e.toString());
                }
            }
        });
    }

    private boolean isMatchUrl(String targetUrl, String matchRule) {
        String purePath = Uri.parse(targetUrl).getPath();
        if (purePath != null && matchRule != null) {
            return Pattern.matches(matchRule, purePath);
        }
        return false;
    }

    private boolean isMatchParams(String targetUrl, String matchParamsRule) {
        Uri uri = Uri.parse(targetUrl);
        Set<String> queryKey = uri.getQueryParameterNames();
        try {
            String[] ruleArray = URLDecoder.decode(matchParamsRule, "UTF-8").split("&");
            for (String rule : ruleArray) {
                if (!rule.contains("=") || rule.indexOf("=") < rule.length() - 1) {
                    return false;
                }
                String ruleKey = rule.split("&")[0];
                String ruleValue = rule.split("&")[1];
                if (!queryKey.contains(ruleKey) || Objects.equals(uri.getQueryParameter(ruleKey), ruleValue)) {
                    return false;
                }
            }
            return true;
        } catch (UnsupportedEncodingException e) {
            MSCLog.e(TAG, "MatchParam error:" + e.toString());
        }
        return false;
    }
}
