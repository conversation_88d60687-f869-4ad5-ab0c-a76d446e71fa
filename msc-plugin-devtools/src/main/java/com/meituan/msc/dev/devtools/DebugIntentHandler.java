package com.meituan.msc.dev.devtools;

import android.content.Context;
import android.content.Intent;
import android.content.pm.PackageManager;
import android.content.pm.ResolveInfo;
import android.net.Uri;
import android.os.Build;
import android.support.annotation.NonNull;
import android.support.annotation.RequiresApi;
import android.text.TextUtils;
import android.view.View;

import com.dianping.nvnetwork.NVAppMockManager;
import com.google.gson.reflect.TypeToken;
import com.meituan.android.cipstorage.CIPStorageCenter;
import com.meituan.android.cipstorage.CIPStorageConfig;
import com.meituan.dio.utils.FileUtil;
import com.meituan.met.mercury.load.core.DDResource;
import com.meituan.msc.common.config.MSCMultiProcessConfig;
import com.meituan.msc.common.framework.Callback;
import com.meituan.msc.common.process.MSCProcess;
import com.meituan.msc.common.support.java.util.concurrent.CompletableFuture;
import com.meituan.msc.common.support.java.util.function.Consumer;
import com.meituan.msc.common.utils.IntentUtil;
import com.meituan.msc.common.utils.ToastUtils;
import com.meituan.msc.dev.aop.PackageDebugAop;
import com.meituan.msc.dev.automator.AutomatorManager;
import com.meituan.msc.dev.automator.mockrequest.MockRequestManager;
import com.meituan.msc.dev.command.ArgumentName;
import com.meituan.msc.dev.command.Command;
import com.meituan.msc.dev.command.CommandGroup;
import com.meituan.msc.dev.devtools.horn.HornMockConfig;
import com.meituan.msc.dev.devtools.horn.HornMockManager;
import com.meituan.msc.dev.performance.PerformanceTestManager;
import com.meituan.msc.dev.performance.TopActivityGetter;
import com.meituan.msc.dev.performance.checkupdatemock.CheckUpdateMockManager;
import com.meituan.msc.dev.utils.RequestUtil;
import com.meituan.msc.extern.MSCEnvHelper;
import com.meituan.msc.jse.bridge.ConversionUtil;
import com.meituan.msc.lib.BuildConfig;
import com.meituan.msc.lib.interfaces.container.MSCParams;
import com.meituan.msc.modules.apploader.IAppLoader;
import com.meituan.msc.modules.apploader.events.AppLoadException;
import com.meituan.msc.modules.container.MSCActivity;
import com.meituan.msc.modules.devtools.DebugHelper;
import com.meituan.msc.modules.engine.MSCRuntime;
import com.meituan.msc.modules.engine.RuntimeManager;
import com.meituan.msc.modules.msi.MSIManagerModule;
import com.meituan.msc.modules.page.IPageModule;
import com.meituan.msc.modules.page.PageModule;
import com.meituan.msc.modules.page.render.webview.WebViewCacheManager;
import com.meituan.msc.modules.page.view.PageViewWrapper;
import com.meituan.msc.modules.preload.PreloadManager;
import com.meituan.msc.modules.preload.PreloadResultData;
import com.meituan.msc.modules.preload.PreloadTasksManager;
import com.meituan.msc.modules.reporter.MSCLog;
import com.meituan.msc.modules.update.MSCHornBasePackageReloadConfig;
import com.meituan.msc.modules.update.bean.AppMetaInfoWrapper;
import com.meituan.msc.modules.update.bean.CheckUpdateParams;
import com.meituan.msc.modules.update.metainfo.AppCheckUpdateManager;
import com.meituan.msc.modules.update.metainfo.CheckUpdateCallback;
import com.meituan.msc.modules.update.pkg.PackageLoadCallback;
import com.meituan.msc.modules.update.pkg.PackageLoadManager;
import com.meituan.msc.util.perf.PerfEventRecorder;
import com.meituan.msc.util.perf.PerfTrace;
import com.meituan.msi.api.ApiCallback;
import com.sankuai.meituan.retrofit2.Response;
import com.sankuai.meituan.retrofit2.ResponseBody;
import com.sankuai.meituan.retrofit2.mock.MockInterceptor;

import org.json.JSONArray;
import org.json.JSONException;
import org.json.JSONObject;

import java.io.UnsupportedEncodingException;
import java.net.URLDecoder;
import java.util.Collection;
import java.util.Collections;
import java.util.List;

import static com.meituan.android.cipstorage.CIPStorageCenter.MODE_MULTI_PROCESS;
import static com.meituan.msc.common.utils.IntentUtil.getBooleanExtra;
import static com.meituan.msc.common.utils.IntentUtil.getStringExtra;
import static com.meituan.msc.dev.performance.PerformanceTestManager.MSC_PERF_TEST_MODE;
import static com.meituan.msc.dev.performance.PerformanceTestManager.MSC_PERF_V8_PROFILER;
import static com.meituan.msc.dev.performance.PerformanceTestManager.MSC_PRINT_TRACE_TO_LOGCAT;

/**
 * 基础能力：
 * - TODO: 是否初始化 MSC SDK: msc_test_init_sdk=true
 * - TODO: 支持设置环境变量，即跳链中指定要设置的变量的key和value，然后存储起来，在其他地方读这些配置然后使用
 * - TODO: 支持预下载指定包
 * - 支持建立 mpium 的 WebSocket 链接
 * - 支持mock Horn开关的值（下次启动生效）
 * - TODO: 支持切拉包环境
 * - 关闭业务预热 - mock Horn开关
 * - 关闭基础库预热 - mock Horn开关
 * - 支持直接预热指定版本的基础库
 * - TODO: 支持删除指定包的功能
 * - 支持删除所有的包缓存
 * - 支持缓存指定小程序的MetaInfo信息
 * - 支持缓存指定版本的基础库包
 * - 支持缓存指定小程序加载所需的所有包
 */
@CommandGroup(name = "com.meituan.msc.dev")
public class DebugIntentHandler {
    /**
     * 预加载指定基础包版本流程日志TAG，线下
     */
    public static final String PRELOAD_DEBUG = "Preload Debug";
    public static final String MSC_TEST_PARAM_KEY_INIT_SDK = "msc_test_init_sdk";
    public static final String MSC_TEST_MOCK_HORN = "msc_test_mock_horn";
    public static final String MSC_TEST_DISABLE_PRE_LOAD = "msc_test_disable_pre_load";
    public static final String MSC_PRELOAD_BASE_VERSION = "msc_preload_base_version";
    @Deprecated
    public static final String MSC_PRELOAD_APP_ID = "msc_preload_app_id";
    public static final String MSC_PRELOAD_APP = "msc_preload_app";
    public static final String MSC_CLEAN_PRELOAD_APP = "msc_clean_preload_app";
    public static final String MSC_CLEAN_ALL_LOCAL_PACKAGES = "msc_clean_all_local_package";
    public static final String MSC_CLEAN_ALL_META_INFO = "msc_clean_all_meta_info";
    public static final String MSC_CACHE_META_INFO = "msc_cache_meta_info";
    public static final String MSC_CACHE_BASE_PACKAGE = "msc_cache_base_package";
    public static final String MSC_CACHE_BUSINESS_PACKAGE = "msc_cache_business_package";
    @Deprecated
    public static final String MSC_PRELOAD_APP_CHECK_UPDATE_URL = "msc_preload_app_checkupdateurl";
    public static final String MSC_PRELOAD_APP_TARGET_PATH = "msc_preload_app_targetpath";
    public static final String MSC_RELOAD_BASE = "msc_reload_base";
    public static final String MSC_PRINT_RUNTIME_LIST = "print_runtime_list";
    public static final String MSC_MOCK_PREFETCH_SUB_PKG_FAILED = "msc_mock_prefetch_sub_pkg_failed";
    public static final String MOCK_ID = "mockId";
    public static final String MOCK_GROUP_ID = "mockGroupId";
    public static final String MOCK_USER_ID = "mockUserId";

    private static final String TAG = "DebugIntentHandler";

    private static final DebugIntentHandler INSTANCE = new DebugIntentHandler();

    public static DebugIntentHandler getInstance() {
        return INSTANCE;
    }

    public void handleIntent(Context context, Intent intent) {
        if (intent == null) {
            return;
        }

        // 处理mock的Horn值
        // 给 appWhiteList 白名单设置为固定的值：app_1652965244713
        // 测试跳链：imeituan://www.meituan.com/msc/debug?msc_test_mock_horn=%5B%7B%22type%22%3A%22msc_preload%22%2C%22key%22%3A%22appWhiteList%22%2C%22value%22%3A%5B%22app_1652965244713%22%5D%7D%5D
        mockHorn(getStringExtra(intent, MSC_TEST_MOCK_HORN));

        // 关闭所有预热功能
        // 测试跳链: imeituan://www.meituan.com/msc/debug?msc_test_disable_pre_load=true
        Boolean disable = (getBooleanExtra(intent, MSC_TEST_DISABLE_PRE_LOAD));
        if (disable != null && disable) {
            disablePreload();
        }

        // 删除所有的本地包
        // 测试跳链: imeituan://www.meituan.com/msc/debug?msc_clean_all_local_package=true
        if (getBooleanExtra(intent, MSC_CLEAN_ALL_LOCAL_PACKAGES, false)) {
            cleanAllLocalPackages(context);
        }

        // 删除缓存的MetaInfo
        // 测试跳链: imeituan://www.meituan.com/msc/debug?msc_clean_all_meta_info=true
        if (getBooleanExtra(intent, MSC_CLEAN_ALL_META_INFO, false)) {
            cleanAllMetaInfoCache(context);
        }

        // 支持缓存指定小程序的MetaInfo信息
        // 测试跳链: imeituan://www.meituan.com/msc/debug?msc_cache_meta_info=true&checkUpdateUrl=https%3A%2F%2Fs3plus.sankuai.com%2Fv1%2Fmss_2044229a1b824dbe990c4a549a07bfec%2Fmsc-fe-internal%2FcheckUpdate%2F28e01137675fa4e34b8e7a0b052e4dcd.json&appId=7122f6e193de47c1
        if (getBooleanExtra(intent, MSC_CACHE_META_INFO, false)) {
            String appId = getStringExtra(intent, MSCParams.APP_ID);
            String checkUpdateUrl = getStringExtra(intent, MSCParams.CHECK_UPDATE_URL);
            cacheMetaInfo(appId, checkUpdateUrl);
        }

        // 支持缓存指定版本的基础库包
        // 测试跳链: imeituan://www.meituan.com/msc/debug?msc_cache_base_package=true&mscVersion=0.1.10
        if (getBooleanExtra(intent, MSC_CACHE_BASE_PACKAGE, false)) {
            String mscVersion = getStringExtra(intent, MSCParams.MSC_VERSION);
            cacheBasePackage(mscVersion);
        }

        // 支持缓存指定版本的业务包
        // 测试跳链: imeituan://www.meituan.com/msc/debug?msc_cache_business_package=true&checkUpdateUrl=https%3A%2F%2Fs3plus.sankuai.com%2Fv1%2Fmss_2044229a1b824dbe990c4a549a07bfec%2Fmsc-fe-internal%2FcheckUpdate%2F28e01137675fa4e34b8e7a0b052e4dcd.json&appId=7122f6e193de47c1
        if (getBooleanExtra(intent, MSC_CACHE_BUSINESS_PACKAGE, false)) {
            String mscVersion = getStringExtra(intent, MSCParams.MSC_VERSION);
            String appId = getStringExtra(intent, MSCParams.APP_ID);
            String checkUpdateUrl = getStringExtra(intent, MSCParams.CHECK_UPDATE_URL);
            String targetPath = IntentUtil.getStringExtra(intent, MSCParams.TARGET_PATH);
            cacheBusinessPackage(appId, targetPath, checkUpdateUrl, mscVersion);
        }

        // 是否初始化SDK
        if (getBooleanExtra(intent, MSC_TEST_PARAM_KEY_INIT_SDK, true)) {
            initMSCSDK(context);
        }

        // 建立自动化测试服务连接
        AutomatorManager.createAutomatorTestWebSocket(context, intent, null);

        // 处理性能测试模式选项
        Boolean enable = IntentUtil.getBooleanExtra(intent, MSC_PERF_TEST_MODE);
        if (enable != null) {
            enablePerfTestMode(context, enable);
        }
        handlePerformanceTestConfig(intent);

        // 预热指定版本的基础库 imeituan://www.meituan.com/msc/debug?msc_preload_base_version=1.0.0
        String mscVersion = IntentUtil.getStringExtra(intent, MSC_PRELOAD_BASE_VERSION);
        if (mscVersion != null) {
            preloadBasePackage(mscVersion);
        }

        // 预热指定业务 imeituan://www.meituan.com/msc/debug?msc_preload_app_id=948310208c7f4d4d
        String appId = IntentUtil.getStringExtra(intent, MSC_PRELOAD_APP_ID);
        String checkUpdateUrl = IntentUtil.getStringExtra(intent, MSC_PRELOAD_APP_CHECK_UPDATE_URL);
        String targetPath = IntentUtil.getStringExtra(intent, MSC_PRELOAD_APP_TARGET_PATH);
        if (!TextUtils.isEmpty(appId)) {
            preloadApp(appId, targetPath, checkUpdateUrl, mscVersion, null);
        }

        // 预热指定业务 imeituan://www.meituan.com/msc/debug?msc_preload_app=true&xxxx
        if (IntentUtil.getBooleanExtra(intent, MSC_PRELOAD_APP, false)) {
            preloadApp(IntentUtil.getStringExtra(intent, MSCParams.APP_ID),
                    IntentUtil.getStringExtra(intent, MSCParams.TARGET_PATH),
                    IntentUtil.getStringExtra(intent, MSCParams.CHECK_UPDATE_URL),
                    IntentUtil.getStringExtra(intent, MSCParams.MSC_VERSION),
                    null);
        }

        // 销毁已预热的业务 imeituan://www.meituan.com/msc/debug?msc_clean_preload_app=7122f6e193de47c1
        String cleanPreloadApp = IntentUtil.getStringExtra(intent, MSC_CLEAN_PRELOAD_APP);
        if (!TextUtils.isEmpty(cleanPreloadApp)) {
            cleanPreloadApp(cleanPreloadApp);
        }

        // 输出所有运行时状态到日志 imeituan://www.meituan.com/msc/debug?print_runtime_list=true
        boolean isPrintRuntimeList = IntentUtil.getBooleanExtra(intent, MSC_PRINT_RUNTIME_LIST, false);
        if (isPrintRuntimeList) {
            printRuntimeList();
        }

        // 触发基础库reload imeituan://www.meituan.com/msc/debug?msc_reload_base=0.1.10
        String reloadBaseVersion = IntentUtil.getStringExtra(intent, MSC_RELOAD_BASE);
        if (!TextUtils.isEmpty(reloadBaseVersion)) {
            triggerReloadBasePackage(reloadBaseVersion);
        }

        String mockId = IntentUtil.getStringExtra(intent, MOCK_ID);
        String mockGroupId = IntentUtil.getStringExtra(intent, MOCK_GROUP_ID);
        String mockUserId = IntentUtil.getStringExtra(intent, MOCK_USER_ID);
        if (mockId != null || mockGroupId != null || mockUserId != null) {
            MockRequestManager.getInstance().initMockRequest(mockId, mockUserId, mockGroupId);
        }

        // mock 子包预下载失败
        mockPrefetchSubPkgFailed(!IntentUtil.getBooleanExtra(intent, MSC_MOCK_PREFETCH_SUB_PKG_FAILED, false));
        MSCLog.i(TAG, "Finished to handle debug intent");
    }

    @Command
    private void mockPrefetchSubPkgFailed(@ArgumentName("disable") boolean disable) {
        DebugHelper.mockPrefetchSubPkgFailed = !disable;
    }

    @Command
    public static void cleanAllMetaInfoCache(Context context) {
        CIPStorageCenter.instance(context, "msc_adaptor", MODE_MULTI_PROCESS).clearByDefaultConfig();
        MSCLog.i(TAG, "Finished to clean all meta info cache.");
    }

    @Command
    private void triggerReloadBasePackage(@ArgumentName(MSCParams.MSC_VERSION) String mscVersion) {
        MSCHornBasePackageReloadConfig.get().triggerReloadBasePackage(new String[]{mscVersion});
    }

    @Command
    private void enablePerfTestMode(@ArgumentName("context") Context context, @ArgumentName("enable") boolean enable) {
        PerformanceTestManager.enablePerformanceTestMode(context, enable);
    }

    @Command
    public CompletableFuture<Void> cleanPreloadApp(@ArgumentName(MSCParams.APP_ID) String appId) {
        CompletableFuture<Void> completableFuture = new CompletableFuture<>();
        PreloadTasksManager.instance.cleanPreloadApp(appId, new Callback<Void>() {
            @Override
            public void onSuccess(Void data) {
                printRuntimeList();
                completableFuture.complete(null);
            }

            @Override
            public void onFail(String errMsg, Exception error) {
                completableFuture.completeExceptionally(error);
            }

            @Override
            public void onCancel() {
            }
        });
        return completableFuture;
    }

    @Command
    public JSONObject printRuntimeList() {
        JSONObject jsonObject = new JSONObject();
        JSONArray aliveArray = new JSONArray();
        JSONArray runningArray = new JSONArray();
        JSONArray preloadBaseArray = new JSONArray();
        JSONArray preloadBizArray = new JSONArray();

        try {
            jsonObject.put("alive", aliveArray);
            jsonObject.put("running", runningArray);
            jsonObject.put("preloadBase", preloadBaseArray);
            jsonObject.put("preloadBiz", preloadBizArray);
        } catch (JSONException e) {
            MSCLog.e(TAG, e, "printRuntimeList");
        }

        for (MSCRuntime runtime : RuntimeManager.getAllRunningEngines().values()) {
            String appId = runtime.getAppId();
            IAppLoader appLoader = runtime.getModule(IAppLoader.class);
            if (appId != null && RuntimeManager.getKeepAliveAppWithAppId(appId) != null) {
                aliveArray.put(appId);
            } else if (appLoader.isLaunched()) {
                runningArray.put(appId);
            } else if (appLoader.isBizPreloadReady()) {
                preloadBizArray.put(appId);
            } else {
                preloadBaseArray.put(runtime.getMSCAppModule().getBasePkgVersion());
            }
        }
        MSCLog.i(TAG, "printRuntimeList", jsonObject.toString());
        return jsonObject;
    }

    @Command
    public CompletableFuture<PreloadResultData> preloadApp(@ArgumentName(MSCParams.APP_ID) String appId,
                                                           @ArgumentName(MSCParams.TARGET_PATH) String targetPath,
                                                           @ArgumentName(MSCParams.CHECK_UPDATE_URL) String checkUpdateUrl,
                                                           @ArgumentName(MSCParams.MSC_VERSION) String mscVersion,
                                                           @ArgumentName("preloadWebViewPage") Boolean preloadWebViewPage
                                                           ) {
        CompletableFuture<PreloadResultData> completableFuture = new CompletableFuture<>();
        HornMockManager.getInstance().mockHornCurrentTime(new HornMockConfig("msc_preload", "appWhiteList", new String[]{appId}));
        // 性能测试模式下，使用网络拦截模式的checkUpdateUrl
        CheckUpdateMockManager.getInstance().addMockConfig(appId, checkUpdateUrl);
        // 子进程业务预热默认预热WebView
        boolean finalPreloadWebViewPage = preloadWebViewPage == null ? MSCMultiProcessConfig.get().inMultiProcessWhiteList(appId) && !MSCProcess.STANDARD.isCurrentProcess() : preloadWebViewPage;
        PreloadTasksManager.instance.cleanPreloadApp(appId, new Callback<Void>() {
            @Override
            public void onSuccess(Void data) {
                PerfTrace.begin("preloadApp");
                PreloadTasksManager.preloadMSCAppInProcess(appId, targetPath, finalPreloadWebViewPage,
                        CheckUpdateMockManager.getInstance().isEnable() ? null : checkUpdateUrl, mscVersion, new Callback<PreloadResultData>() {
                            @Override
                            public void onSuccess(PreloadResultData data) {
                                printRuntimeList();
                                PerfTrace.end("preloadApp");
                                completableFuture.complete(data);
                            }

                            @Override
                            public void onFail(String errMsg, Exception error) {
                                if(error == null){
                                    error = new AppLoadException(-1, errMsg);
                                }
                                completableFuture.completeExceptionally(error);
                            }

                            @Override
                            public void onCancel() {
                            }
                        });
            }

            @Override
            public void onFail(String errMsg, Exception error) {
                completableFuture.completeExceptionally(error);
            }

            @Override
            public void onCancel() {
            }
        });
        return completableFuture;
    }

    @Command
    public void initMSCSDK(Context context) {
        MSCEnvHelper.startHostInit(context.getApplicationContext());
    }

    @Command
    public CompletableFuture<MSCRuntime> preloadBasePackage(@ArgumentName(MSCParams.MSC_VERSION) String mscVersion) {
        PreloadManager.getInstance().init();
        if (mscVersion.isEmpty()) {
            // 如果指定了 msc_preload_base_version 参数，但是没有指定要预热的基础库版本，则使用当前最新版本
            if (PreloadTasksManager.instance.existAtLeastOneBasePackagePreloadEngine()) {
                // 如果已经预热了一个的话，就直接退出
                return CompletableFuture.completedFuture(null);
            }
        } else {
            // remove already preload runtime
            MSCRuntime unusedRuntime = RuntimeManager.findBasePreloadRuntime();
            if (unusedRuntime != null) {
                RuntimeManager.removeApp(unusedRuntime);
            }
        }
        // preload mscVersion runtime
        return PreloadTasksManager.instance.preloadBasePackageAsyncForDebug(PRELOAD_DEBUG, mscVersion, 0);
    }

    /**
     * 清空所有的HornMock配置（不包含Horn SDK的配置）
     * 跳链: imeituan://www.meituan.com/msc/debug?clearAllHornMock=true
     */
    @Command
    public void clearAllHornMock() {
        HornMockManager.getInstance().clearAllMockConfig();
    }

    /**
     * 禁用预热能力
     */
    @Command
    public void disablePreload() {
        HornMockConfig mockConfig = new HornMockConfig("msc_preload", "enablePreload", false);
        HornMockManager.getInstance().addMockConfig(mockConfig);
    }

    /**
     * mock horn 配置
     * <p>
     * 例如，要给 msc_mmp_routing_converter_config 配置增加键为 bike_mmp ，值为 {"appId": "bike_mmp","appLifeCyclePersist": true} 的配置，可使用如下的adb命令
     * adb shell am broadcast -p com.meituan.android.msc.sample -a com.meituan.msc.dev.command -e command com.meituan.msc.dev.mockHorn  -e config '\[\{\"type\":\"msc_mmp_routing_converter_config\",\"key\":\"bike_mmp\",\"value\":\{\"appId\":\"bike_mmp\",\"appLifeCyclePersist\":true\},\"persisted\":true\}\]'
     *
     * @param configString
     */
    @Command
    public void mockHorn(@ArgumentName("config") String configString) {
        if (TextUtils.isEmpty(configString)) {
            return;
        }
        List<HornMockConfig> configs = ConversionUtil.getGson().fromJson(configString, new TypeToken<List<HornMockConfig>>() {
        }.getType());

        for (HornMockConfig mockConfig : configs) {
            HornMockManager.getInstance().addMockConfig(mockConfig);
        }
    }

    /**
     * mock 单个Horn
     * 例如，要给 msc_mmp_routing_converter_config 配置增加键为 bike_mmp ，值为 {"appId": "bike_mmp","appLifeCyclePersist": true} 的配置，可使用如下的adb命令
     * adb shell am broadcast -p com.meituan.android.msc.sample -a com.meituan.msc.dev.command -e command com.meituan.msc.dev.mockSingleHorn  -e type msc_mmp_routing_converter_config -e key bike_mmp -e value '\{\"appId\":\"bike_mmp\",\"appLifeCyclePersist\":true\}'
     *
     * @param type  horn配置文件
     * @param key   key
     * @param value json格式的值
     */
    @Command
    public void mockSingleHorn(@ArgumentName("type") String type, @ArgumentName("key") String key, @ArgumentName("value") String value) {
        if (TextUtils.isEmpty(type) || TextUtils.isEmpty(key)) {
            return;
        }
        HornMockConfig config = new HornMockConfig(type, key, ConversionUtil.getGson().fromJson(value, Object.class));
        HornMockManager.getInstance().addMockConfig(config);
    }

    @Command
    public static void cleanAllLocalPackages(Context context) {
        final String DD_CIP_CHANNEL = "ddload";
        FileUtil.deleteDirectory(CIPStorageCenter.requestFilePath(context, DD_CIP_CHANNEL, null, CIPStorageConfig.DEFAULT_CONFIG));
        FileUtil.deleteDirectory(CIPStorageCenter.requestFilePath(context, DD_CIP_CHANNEL, null, CIPStorageConfig.CONFIG_NON_USER_CACHE));
        FileUtil.deleteDirectory(CIPStorageCenter.requestFilePath(context, DD_CIP_CHANNEL, null, CIPStorageConfig.CONFIG_NON_USER_STORAGE));
        MSCLog.i(TAG, "Finished to clean all local package.");
    }

    @Command
    public void cacheMetaInfo(@ArgumentName(MSCParams.APP_ID) String appId, @ArgumentName(MSCParams.CHECK_UPDATE_URL) String checkUpdateUrl) {

        // 性能测试模式下，使用网络拦截模式的checkUpdateUrl
        CheckUpdateMockManager.getInstance().addMockConfig(appId, checkUpdateUrl);
        CheckUpdateParams checkUpdateParams = new CheckUpdateParams(appId, CheckUpdateParams.Type.NETWORK);
        if (!CheckUpdateMockManager.getInstance().isEnable()) {
            checkUpdateParams.checkUpdateUrl = checkUpdateUrl;
        }
        AppCheckUpdateManager.getInstance().checkUpdate(checkUpdateParams, new CheckUpdateCallback<AppMetaInfoWrapper>() {
            @Override
            public void onSuccess(@NonNull AppMetaInfoWrapper data) {
                MSCLog.i(TAG, "Finished to cache meta info");
            }

            @Override
            public void onFail(String errMsg, AppLoadException error) {
                MSCLog.e(TAG, error, "getMetaInfoFromNetworkOrCache onFailed");
            }
        });
    }

    @Command
    public void cacheBasePackage(@ArgumentName(MSCParams.MSC_VERSION) String mscVersion) {
        PackageLoadManager.getInstance().loadLatestBasePackage(new PerfEventRecorder(true, false), null, mscVersion, null, new PackageLoadCallback<DDResource>() {
            @Override
            public void onSuccess(@NonNull DDResource data) {
                MSCLog.i(TAG, "Finished to cache base package");
            }

            @Override
            public void onFail(String errMsg, AppLoadException error) {
                MSCLog.e(TAG, "Failed to cache base package");
            }
        });
    }

    public void cacheBusinessPackage(@ArgumentName(MSCParams.APP_ID) String appId, @ArgumentName(MSCParams.TARGET_PATH) String targetPath, @ArgumentName(MSCParams.CHECK_UPDATE_URL) String checkUpdateUrl, @ArgumentName(MSCParams.MSC_VERSION) String mscVersion) {
        // 缓存业务包的低成本实现：先预热业务，再销毁预热的运行时
        // FIXME by chendacai: 有空优化为只下载包，不创建运行时
        if (!TextUtils.isEmpty(appId)) {
            preloadApp(appId, targetPath, checkUpdateUrl, mscVersion, null)
                    .thenCompose((PreloadResultData data) -> cleanPreloadApp(appId))
                    .thenRun(() -> {
                        MSCLog.i(TAG, "Finished to cache business package");
                    })
                    .exceptionally(e -> {
                        MSCLog.e(TAG, "Failed to cache business package");
                        return null;
                    });
        } else {
            MSCLog.d(TAG, "preloadApp appId is null");
        }
    }

    public static void handlePerformanceTestConfig(Intent intent) {
        Boolean enable = IntentUtil.getBooleanExtra(intent, MSC_PERF_V8_PROFILER);
        if (enable != null) {
            enableCpuProfiling(enable);
        }
        enable = IntentUtil.getBooleanExtra(intent, MSC_PRINT_TRACE_TO_LOGCAT);
        if (enable != null) {
            enablePrintTraceToLogcat(enable);
        }
    }

    @Command
    public static void enableCpuProfiling(@ArgumentName("enable") boolean enable) {
        PerformanceTestManager.enableCpuProfiling(enable);
    }

    @Command
    public static void enablePrintTraceToLogcat(@ArgumentName("enable") boolean enable) {
        PerformanceTestManager.enablePrintTraceToLogcat(enable);
    }

    /**
     * 锁定基础库版本
     * 测试跳链：imeituan://www.meituan.com/msc/debug?base_sdk_version
     *
     * @param mscVersion
     */
    @Command
    public static void lockMSCVersion(@ArgumentName("mscVersion") String mscVersion) {
        if (!TextUtils.isEmpty(mscVersion)) {
            PackageDebugAop.cacheMSCVersionLocked(mscVersion);
        }
    }

    @Command
    public static void openUrl(@ArgumentName("context") Context context, @ArgumentName("url") String url) {
        MSCLog.i(TAG, "openUrl 参数, url：", url);
        Intent intent = new Intent(Intent.ACTION_VIEW, Uri.parse(url));
        PackageManager packageManager = context.getPackageManager();
        if (packageManager == null) {
            throw new IllegalStateException("MSCNative.openUrl packageManager is null");
        }

        Intent hostIntent = new Intent(intent);
        hostIntent.setPackage(context.getPackageName());
        ResolveInfo resolveActivity = null;

        try {
            resolveActivity = packageManager.resolveActivity(hostIntent, PackageManager.MATCH_DEFAULT_ONLY);
        } catch (RuntimeException e) {
            String error = "MSCNative.openUrl error1:" + e.toString();
            MSCLog.e(TAG, error);
        }

        if (resolveActivity == null || resolveActivity.activityInfo == null) {
            try {
                resolveActivity = packageManager.resolveActivity(hostIntent, 0);
            } catch (RuntimeException e) {
                String error = "MSCNative.openUrl error2:" + e.toString();
                MSCLog.e(TAG, error);
            }
        }

        if (resolveActivity != null && resolveActivity.activityInfo != null) {
            hostIntent.addFlags(Intent.FLAG_ACTIVITY_NEW_TASK);
            context.startActivity(hostIntent);
        } else {
            throw new IllegalStateException("MSCNative.openUrl resolveActivity is null");
        }
    }

    /**
     * 启用AppMock
     * Debu面板跳链：imeituan://www.meituan.com/msc/debug?enableAppMock=true&uid=xxx
     * broadcast： adb shell am broadcast -p com.meituan.android.msc.sample -a com.meituan.msc.dev.command -e command com.meituan.msc.dev.enableAppMock -e uid xxx
     *
     * @param context
     * @param uid
     */
    @Command
    public static CompletableFuture<Void> enableAppMock(@ArgumentName("context") Context context, @ArgumentName("uid") String uid) {
        if (TextUtils.isEmpty(uid)) {
            throw new IllegalArgumentException("uid不能为空，请给uid参数指定一个mis账号");
        }
        String appMockConfigUlr = "https://appmock.sankuai.com/mw/register?_=0__0&uid=" + uid;
        CompletableFuture<Void> result = new CompletableFuture<>();
        NVAppMockManager.instance().registerMock(appMockConfigUlr, new NVAppMockManager.RegisterCallback() {
            @Override
            public void success() {
                CIPStorageCenter cipStorageCenter = CIPStorageCenter.instance(MSCEnvHelper.getContext(), context.getPackageName() + "_cipstoragecenter");
                cipStorageCenter.setBoolean(MockInterceptor.MOCK_ENABLE_KEY, true);
                cipStorageCenter.setString(MockInterceptor.MOCK_URL, appMockConfigUlr);
                result.complete(null);
            }

            @Override
            public void failed(String message) {
                result.completeExceptionally(new IllegalStateException(message));
            }
        });
        return result;
    }

    @Command
    public static void startProcess(@ArgumentName("shortName") String shortName) {
        MSCProcess targetProcess = null;
        if (!TextUtils.isEmpty(shortName)) {
            for (MSCProcess mscProcess : MSCProcess.values()) {
                if (TextUtils.equals(mscProcess.getShortName(), shortName)) {
                    targetProcess = mscProcess;
                    break;
                }
            }
        }
        if (targetProcess == null) {
            targetProcess = MSCProcess.STANDARD;
        }
        MSCProcess.startProcess(targetProcess);
    }

    @Command
    public static void enableMultiProcess(@ArgumentName("disable") boolean disable, @ArgumentName("appId") String appId) {
        HornMockManager.getInstance().addMockConfig(new HornMockConfig("msc_multi_process_config", "disableMultiProcess", disable));
        HornMockManager.getInstance().addMockConfig(new HornMockConfig("msc_multi_process_config", "appIdWhiteList", new String[]{appId}));

        // MMP 多进程全开
        HornMockManager.getInstance().addMockConfig(new HornMockConfig("mmp_config", "enableMultiProcess", !disable));
        HornMockManager.getInstance().addMockConfig(new HornMockConfig("mmp_config", "multiProcessWhiteList", disable ? null : Collections.singletonList(appId)));
    }

    @Command
    public static String getSDKVersion() {
        return BuildConfig.AAR_VERSION;
    }

    /**
     * 模拟将当前进程中的WebView Crash掉
     * adb命令： adb shell am broadcast -p com.meituan.android.msc.sample -a com.meituan.msc.dev.command -e command com.meituan.msc.dev.crashWebView
     */
    @Command
    public static void crashWebView() {
        Collection<MSCRuntime> runtimes = RuntimeManager.getAllRunningEngines().values();
        if (runtimes.isEmpty()) {
            ToastUtils.toastIfDebug("no running app in target process, cannot mock a crash");
        } else {
            try {
                for (MSCRuntime runtime : runtimes) {
                    WebViewCacheManager webViewCacheManager = runtime.webViewCacheManager;
                    if (!webViewCacheManager.isFirstWebViewCreated()) {
                        continue;
                    }
                    webViewCacheManager.getWebViewThroughCache(MSCEnvHelper.getContext(), runtime, "").loadUrl("chrome://crash");
                    ToastUtils.toastIfDebug("WebView will crash");
                    break;
                }
            } catch (Exception e) {
                ToastUtils.toastIfDebug(e.toString());
            }
        }
    }

    /**
     * 检查当前页面是否白屏
     * adb命令： adb shell am broadcast -p com.meituan.android.msc.sample -a com.meituan.msc.dev.command -e command com.meituan.msc.dev.isWhiteScreen
     *
     * @return
     */
    @RequiresApi(api = Build.VERSION_CODES.KITKAT)
    @Command
    public static boolean isWhiteScreen() {
        MSCActivity activity = TopActivityGetter.getTopMSCActivity();
        if (activity == null) {
            throw new IllegalStateException("Current activity is not MSCActivity!");
        }

        IPageModule pageModule = activity.getContainerController().getPageMangerModule().getTopPage();
        PageViewWrapper view = (PageViewWrapper) pageModule.asView();
        if (view == null) {
            throw new IllegalStateException("view is null");
        }
        View detectView = view;
        boolean isInnerWebView = false;
        View innerWebivew = null;
        if (view.webViewModuleRef != null && (innerWebivew = view.webViewModuleRef.get()) != null && innerWebivew.isAttachedToWindow()) {
            detectView = innerWebivew;
            isInnerWebView = true;
        }
        return pageModule.getRenderer().isWhiteScreen(isInnerWebView, detectView, true, false);
    }

    /**
     * mock 某个小程序的 checkUpdateUrl
     * adb命令： adb shell am broadcast -p com.meituan.android.msc.sample -a com.meituan.msc.dev.command -e command com.meituan.msc.dev.mockCheckUpdateUrl -e checkUpdateUrl https%3A%2F%2Fs3plus.sankuai.com%2Fv1%2Fmss_2044229a1b824dbe990c4a549a07bfec%2Fmsc-fe-internal%2FcheckUpdate%2Fd9eca02cde222bf390f007ba9223447e.json
     *
     * @param context
     * @param appId
     * @param checkUpdateUrl
     */
    @Command
    public static CompletableFuture<Void> mockCheckUpdateUrl(@ArgumentName("context") Context context, @ArgumentName("appId") String appId, @ArgumentName(MSCParams.CHECK_UPDATE_URL) String checkUpdateUrl) throws UnsupportedEncodingException {
        if (TextUtils.isEmpty(checkUpdateUrl)) {
            throw new IllegalArgumentException("checkUpdateUrl cannot be null");
        }
        if (checkUpdateUrl.contains("%3A%2F%2F")) {
            checkUpdateUrl = URLDecoder.decode(checkUpdateUrl, "UTF-8");
        }
        CheckUpdateMockManager.getInstance().enableCheckUpdateMock(context, true);
        if (!TextUtils.isEmpty(appId)) {
            CheckUpdateMockManager.getInstance().addMockConfig(appId, checkUpdateUrl);
            return CompletableFuture.completedFuture(null);
        } else {
            // 如果没有指定 appId 参数，从 checkUpdateUrl 中请求一下
            final String finalCheckUpdateUrl = checkUpdateUrl;
            return RequestUtil.get(checkUpdateUrl)
                    .thenAccept(new Consumer<Response<ResponseBody>>() {
                        @Override
                        public void accept(Response<ResponseBody> responseBodyResponse) {
                            String appId = null;
                            try {
                                appId = new JSONObject(responseBodyResponse.body().string())
                                        .optJSONArray("mscApps")
                                        .optJSONObject(0)
                                        .optString("appId");
                            } catch (JSONException e) {
                                throw new RuntimeException(e);
                            }
                            CheckUpdateMockManager.getInstance().addMockConfig(appId, finalCheckUpdateUrl);
                        }
                    });
        }
    }

    /**
     * 关闭 checkUpdateUrl mock能力
     */
    @Command
    public static void disableCheckUpdateUrlMock(@ArgumentName("context") Context context) {
        CheckUpdateMockManager.getInstance().enableCheckUpdateMock(context, false);
    }

    /**
     * 删除 checkUpdateUrl mock 配置，如果 appId 参数不存在，则删除所有配置
     *
     * @param appId
     */
    @Command
    public static void removeCheckUpdateUrlMock(@ArgumentName("appId") String appId) {
        if (TextUtils.isEmpty(appId)) {
            CheckUpdateMockManager.getInstance().clearMockConfigs();
        } else {
            CheckUpdateMockManager.getInstance().removeMockConfig(appId);
        }
    }

    /**
     * 调用Msi方法
     * adb shell am broadcast -p com.meituan.android.msc.sample -a com.meituan.msc.dev.command -e command com.meituan.msc.dev.invokeMsiMethod -e name navigateTo -e args '{\"url\":\"/pages/skuDetail/index?originalPoiId=0\&poiIdStr_originalPoiId=-mcCyxzU2gVMUJxEoldnCQE\&skuId=100422892477087\&__RST=1711555702397\&__FUNC=navigateTo\&__preFetcherId=gup1u25huo\"}'
     *
     * @param context
     * @param scope
     * @param name
     * @param args
     * @throws JSONException
     */
    @Command
    public static CompletableFuture<String> invokeMsiMethod(@ArgumentName("scope") String scope, @ArgumentName("name") String name, @ArgumentName("args") String args) throws JSONException {
        CompletableFuture<String> completableFuture = new CompletableFuture<>();
        MSCActivity activity = TopActivityGetter.getTopMSCActivity();
        if (activity == null) {
            throw new IllegalStateException("Current activity is not MSCActivity!");
        }

        PageModule pageModule = (PageModule) activity.getContainerController().getPageMangerModule().getTopPage();
        PageViewWrapper view = (PageViewWrapper) pageModule.asView();
        if (view == null) {
            throw new IllegalStateException("view is null");
        }

        if (TextUtils.isEmpty(scope)) {
            scope = "default";
        }
        JSONObject params = new JSONObject();
        params.put("scope", scope);
        params.put("name", name);
        params.put("args", new JSONObject(args));

        String viewId = String.valueOf(view.getViewId());
        JSONObject uiArgs = new JSONObject();
        uiArgs.put("pageId", viewId);
        params.put("uiArgs", uiArgs);

        ApiCallback<String> apiCallback = new ApiCallback<String>() {
            @Override
            public void onSuccess(String o) {
                completableFuture.complete(o);
            }

            @Override
            public void onFail(String o) {
                Throwable e = new RuntimeException(o);
                completableFuture.completeExceptionally(e);
            }
        };
        pageModule.getModule(MSIManagerModule.class).msiAsyncInvoke(params.toString(), apiCallback);
        return completableFuture;
    }
}
