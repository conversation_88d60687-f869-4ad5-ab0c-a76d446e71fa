package com.meituan.msc.dev.devtools;

import android.content.Context;
import android.content.SharedPreferences;
import android.os.Build;
import android.preference.PreferenceManager;
import android.support.annotation.RequiresApi;
import android.text.TextUtils;

import com.meituan.android.cipstorage.CIPStorageCenter;
import com.meituan.msc.common.constant.APPIDConstants;
import com.meituan.msc.common.utils.CIPStorageFileUtil;
import com.meituan.msc.common.utils.ToastUtils;
import com.meituan.msc.dev.utils.AppContextGetter;
import com.meituan.msc.extern.MSCEnvHelper;
import com.meituan.msc.lib.BuildConfig;
import com.meituan.msc.modules.devtools.DebugHelper;
import com.meituan.msc.modules.reporter.MSCLog;
import com.meituan.msc.modules.router.MMPRouterManager;
import com.sankuai.android.jarvis.Jarvis;
import com.squareup.picasso.Picasso;

import org.json.JSONException;
import org.json.JSONObject;

import java.util.HashMap;
import java.util.HashSet;
import java.util.Iterator;
import java.util.Map;
import java.util.Set;
import java.util.concurrent.CompletableFuture;


public class DebugManager {
    private static final String TAG = "DebugManager";

    public static final String MSC_DEBUG = "mmp_debug";
    public static final String MMP_DEBUG_WEBVIEW = "debug_webview";
    public static final String MMP_FORCE_WEBVIEW_SERVICE = "debug_webview_service";
    public static final String MMP_DEBUG_MULTIPROCESS = "debug_multiprocess";
    public static final String MSC_DEBUG_ENABLE_PRE_DOWNLOAD = "msc_debug_enable_pre_download";
    public static final String MMP_DEBUG_KEEP_ALIVE_TIME = "debug_keep_alive_time";
    public static final String MMP_DEBUG_KEEP_CACHED_VERSION = "debug_keep_cached_version";
    public static final String MMP_DEBUG_REPLACE_MMP_SDK = "debug_replace_mmp_sdk";
    public static final String MMP_DEBUG_NAVIGATE_IN_WIDGET = "debug_navigate_in_widget";
    public static final String MMP_DEBUG_IGNORE_WIDGET_API_FAIL = "debug_ignore_widget_api_fail";
    public static final String MMP_DEBUG_WIDGET_DEFAULT_URL = "debug_widget_default_url";
    public static final String MSC_DEBUG_ENABLE_MMP_TO_MSC_SET = "debug_enable_mmp_to_msc_set";
    public static final String MSC_DEBUG_ENABLE_MMP_TO_MSC_SET_INIT = "debug_enable_mmp_to_msc_set_init";
    public static final String MSC_DEBUG_ENABLE_CUSTOM_CONFIG_MMP2MSC = "debug_enable_custom_config_mmp2msc";
    public static final String MSC_DEBUG_MMP_MSC_ROUTE_CONFIG = "debug_mmp_msc_route_config";
    public static final String MSC_DEBUG_MMP_NAME_LIST = "debug_mmp_name_list";
    public static final String MSC_DEBUG_MMP_MSC_MAPPING = "debug_mmp_msc_mapping";
    public static final String MSC_PREFETCH_CRASH_CHECK = "prefetch_crash_check";
    public static String widgetDefaultUrl;

    public static String preloadAppId = APPIDConstants.XIAO_SHUO;

    public static String preloadPath = "/pages/index/index";
    //    public static String checkupdateUrl = "https://s3plus.sankuai.com/v1/mss_2044229a1b824dbe990c4a549a07bfec/msc-fe-internal/checkUpdate/25d3b244ccb8b92898c0fcf5fa87b7fc.json";
    private static final Map<String, String> MMPNameToAppIdMap = new HashMap<>();
    private static final Map<String, String> MMPAppIdToMSCAppIdMap = new HashMap<>();

    static {
        MMPNameToAppIdMap.put("免费小说", "86464ace2bce4d6c");
        MMPNameToAppIdMap.put("医药", "0493c7b31c6f45ce");
        MMPNameToAppIdMap.put("美团米粒", "b75b8f2e8db84d05");
        MMPNameToAppIdMap.put("拼好饭", "a8720b841a3d4b1d");
        MMPNameToAppIdMap.put("地址", "be7dcad4cf774fed");
        MMPNameToAppIdMap.put("外卖营销", "cdfd5e3f523f4b86");
        MMPNameToAppIdMap.put("地图", "bfceace2a83e4328");
        MMPNameToAppIdMap.put("骑行", "bike_mmp");
        MMPNameToAppIdMap.put("美团跑腿", "8f8521df2b5248d7");
        MMPNameToAppIdMap.put("POI门店", "bf83379bb031461a");
        MMPNameToAppIdMap.put("POI地图选点", "fd729f2551054cc2");
        MMPNameToAppIdMap.put("POI贡献列表", "d0f6049cef1d4900");
        MMPNameToAppIdMap.put("美团放心退", "453febf016114416");
        MMPNameToAppIdMap.put("美团访客", "a65f825845d949a6");
        MMPNameToAppIdMap.put("闪购", "bafee49867764599");
        MMPNameToAppIdMap.put("酒店店内", "4abf822bb5ec4196");
        MMPNameToAppIdMap.put("智能配送", "7fda774d6980468c");
        MMPNameToAppIdMap.put("美团优选", "gh_84b9766b95bc");
        MMPNameToAppIdMap.put("美团电商", "mmp_87dffc23944d");
        MMPNameToAppIdMap.put("助老打车", "b60925549b8a47d5");
        MMPNameToAppIdMap.put("美团圈圈", "bca74ebe2fc448e0");
        MMPNameToAppIdMap.put("悟空租车", "travel_zuche_wukong");
        MMPNameToAppIdMap.put("消息设置", "eb875e401c1046b4");
        MMPNameToAppIdMap.put("MLive直播", "0ff16d2b06ef438c");
        MMPNameToAppIdMap.put("lancer", "lancer_mmp");
        MMPNameToAppIdMap.put("交通汽车票", "04cc18db1ee046b9");
        MMPNameToAppIdMap.put("交通机票报销", "d15fb28f95c6430a");
        MMPNameToAppIdMap.put("打车", "mmp_ffd0ee8b449c");
        MMPNameToAppIdMap.put("拼场", "366710c93c9446e4");

        MMPAppIdToMSCAppIdMap.put("86464ace2bce4d6c", "73a62054aadc4526");
        MMPAppIdToMSCAppIdMap.put("0493c7b31c6f45ce", "61cbdaae3b504b9b");
        MMPAppIdToMSCAppIdMap.put("b75b8f2e8db84d05", "b75b8f2e8db84d05");
        MMPAppIdToMSCAppIdMap.put("a8720b841a3d4b1d", "3624e0d16e0f4c8a");
        MMPAppIdToMSCAppIdMap.put("be7dcad4cf774fed", "be7dcad4cf774fed");
        MMPAppIdToMSCAppIdMap.put("cdfd5e3f523f4b86", "cdfd5e3f523f4b86");
        MMPAppIdToMSCAppIdMap.put("bfceace2a83e4328", "bfceace2a83e4328");
        MMPAppIdToMSCAppIdMap.put("bike_mmp", "bike_mmp");
        MMPAppIdToMSCAppIdMap.put("8f8521df2b5248d7", "8f8521df2b5248d7");
        MMPAppIdToMSCAppIdMap.put("bf83379bb031461a", "bf83379bb031461a");
        MMPAppIdToMSCAppIdMap.put("fd729f2551054cc2", "fd729f2551054cc2");
        MMPAppIdToMSCAppIdMap.put("d0f6049cef1d4900", "d0f6049cef1d4900");
        MMPAppIdToMSCAppIdMap.put("453febf016114416", "453febf016114416");
        MMPAppIdToMSCAppIdMap.put("a65f825845d949a6", "a65f825845d949a6");
        MMPAppIdToMSCAppIdMap.put("bafee49867764599", "99486d09d0c94ce5");
        MMPAppIdToMSCAppIdMap.put("7fda774d6980468c", "7fda774d6980468c");
        MMPAppIdToMSCAppIdMap.put("gh_84b9766b95bc", "gh_84b9766b95bc");
        MMPAppIdToMSCAppIdMap.put("mmp_87dffc23944d", "33976e84dd654a2d");
        MMPAppIdToMSCAppIdMap.put("b60925549b8a47d5", "b60925549b8a47d5");
        MMPAppIdToMSCAppIdMap.put("bca74ebe2fc448e0", "bca74ebe2fc448e0");
        MMPAppIdToMSCAppIdMap.put("travel_zuche_wukong", "travel_zuche_wukong");
        MMPAppIdToMSCAppIdMap.put("eb875e401c1046b4", "eb875e401c1046b4");
        MMPAppIdToMSCAppIdMap.put("0ff16d2b06ef438c", "0ff16d2b06ef438c");
        MMPAppIdToMSCAppIdMap.put("lancer_mmp", "lancer_mmp");
        MMPAppIdToMSCAppIdMap.put("04cc18db1ee046b9", "04cc18db1ee046b9");
        MMPAppIdToMSCAppIdMap.put("d15fb28f95c6430a", "d15fb28f95c6430a");
        MMPAppIdToMSCAppIdMap.put("mmp_ffd0ee8b449c", "d1a4603ff20e40a7");
        MMPAppIdToMSCAppIdMap.put("366710c93c9446e4", "366710c93c9446e4");
    }

    public static class RouteDebugConfig {
        public String appName = "";
        public String mscAppId = "";
        public boolean appLifeCyclePersist = false;

        public RouteDebugConfig(String appName, String mscAppId, boolean persist) {
            this.appName = appName;
            this.mscAppId = mscAppId;
            this.appLifeCyclePersist = persist;
        }
    }

    private static boolean hasInit = false;

    /**
     * 获取mmpAppName和mmpAppId的映射关系, 内置MMPNameToAppIdMap
     * @return <mmpAppName,mmpAppId>
     */
    public static Map<String, String> getMMPNameToAppIdMap() {
        return getTwoStringMap(MSC_DEBUG_MMP_NAME_LIST, MMPNameToAppIdMap);
    }

    /**
     * 添加mmpAppName和mmpAppId的映射关系，持久存储，清缓存或卸载安装会清理掉此配置
     * @param mmpName mmpAppName
     * @param mmpAppId mpAppId
     */
    public static void addMMPNameToAppIdMap(String mmpName, String mmpAppId) {
        addItemToTwoStringMap(MSC_DEBUG_MMP_NAME_LIST, mmpName, mmpAppId);
    }

    /**
     * 获取mmpAppId和mscAppId的映射关系, 内置MMPAppIdToMSCAppIdMap
     * @return <mmpAppId,mscAppId>
     */
    public static Map<String, String> getMMPAppIdToMSCAppIdMap() {
        return getTwoStringMap(MSC_DEBUG_MMP_MSC_MAPPING, MMPAppIdToMSCAppIdMap);
    }

    public static void addMMPToMSCAppIdMap(String mmpAppId, String mscAppId) {
        addItemToTwoStringMap(MSC_DEBUG_MMP_MSC_MAPPING, mmpAppId, mscAppId);
    }

    private static Map<String, String> getTwoStringMap(String cipName, Map<String, String> defaultMap) {
        CIPStorageCenter cipStorageCenter = CIPStorageFileUtil.getCIPStorageCenter(AppContextGetter.getContext());
        String configString = cipStorageCenter.getString(cipName, "");
        if (TextUtils.isEmpty(configString)) {
            JSONObject jsonObject = new JSONObject();
            try {
                for (Map.Entry<String, String> entry : defaultMap.entrySet()) {
                    String key = entry.getKey();
                    String value = entry.getValue();
                    JSONObject jsonValue = new JSONObject();
                    jsonValue.put("appId", value);
                    jsonObject.put(key, jsonValue);
                }
            } catch (JSONException e) {
                MSCLog.e("getTwoStringMap error", e);
            }
            String spString = jsonObject.toString();
            cipStorageCenter.setString(cipName, spString);
            configString = spString;
        }
        Map<String, String> result = new HashMap<>();
        try {
            JSONObject jsonObject = new JSONObject(configString);
            if (jsonObject.length() > 0) {
                Iterator<String> keys = jsonObject.keys();
                while (keys.hasNext()) {
                    String key = keys.next();
                    JSONObject value = jsonObject.optJSONObject(key);
                    if (value == null) {
                        continue;
                    }
                    String appId = value.optString("appId");
                    if (TextUtils.isEmpty(appId)) {
                        continue;
                    }
                    result.put(key, appId);
                }
            }
        } catch (JSONException e) {
            MSCLog.e("getTwoStringMap error", e);
        }
        return result;

    }

    private static void addItemToTwoStringMap(String cipName, String key, String value) {
        CIPStorageCenter cipStorageCenter = CIPStorageFileUtil.getCIPStorageCenter(AppContextGetter.getContext());
        String configString = cipStorageCenter.getString(cipName, "");
        Map<String, String> result = new HashMap<>();
        try {
            JSONObject jsonObject = new JSONObject(configString);
            if (jsonObject.length() > 0) {
                Iterator<String> keys = jsonObject.keys();
                while (keys.hasNext()) {
                    String k = keys.next();
                    JSONObject v = jsonObject.optJSONObject(k);
                    if (v == null) {
                        continue;
                    }
                    String appId = v.optString("appId");
                    if (TextUtils.isEmpty(appId)) {
                        continue;
                    }
                    result.put(k, appId);
                }
            }
        } catch (JSONException e) {
            MSCLog.e("addItemToTwoStringMap error", e);
        }
        result.put(key, value);
        // 持久化存储
        JSONObject jsonObject = new JSONObject();
        try {
            for (Map.Entry<String, String> entry : result.entrySet()) {
                String appName = entry.getKey();
                String appId = entry.getValue();
                JSONObject jsonValue = new JSONObject();
                jsonValue.put("appId", appId);
                jsonObject.put(appName, jsonValue);
            }
        } catch (JSONException e) {
            MSCLog.e("addItemToTwoStringMap error", e);
        }
        String spString = jsonObject.toString();
        cipStorageCenter.setString(cipName, spString);
    }

    public static void saveDebugEnablePreDownload(boolean enablePreDownLoad) {
        PreferenceManager.getDefaultSharedPreferences(MSCEnvHelper.getContext())
                .edit()
                .putBoolean(MSC_DEBUG_ENABLE_PRE_DOWNLOAD, enablePreDownLoad)
                .apply();
    }

    public static void setMultiProcessMode(int forceMode) {
        DebugHelper.setForceMPMode(forceMode);
        saveMultiProcessMode();
    }

    public static void saveMultiProcessMode() {
        getSp().edit().putInt(MMP_DEBUG_MULTIPROCESS, DebugHelper.getForceMultiProcessMode()).apply();
    }

    public static void setUseMtWebView(boolean useMtWebView) {
        DebugHelper.useMtWebView = useMtWebView;
        getSp().edit().putBoolean("useMtWebView", useMtWebView).apply();
    }

    public static void setKeepAliveTime(long time) {
        DebugHelper.keepAliveTime = time;
        getSp().edit().putLong(MMP_DEBUG_KEEP_ALIVE_TIME, time).apply();
    }

//    public static void setNavigateInWidget(boolean enabled) {
//        DebugHelper.navigateInWidget = enabled;
//        getSp().edit().putBoolean(MMP_DEBUG_NAVIGATE_IN_WIDGET, enabled).apply();
//    }
//
//    public static void setIgnoreWidgetApiFail(boolean enabled) {
//        DebugHelper.ignoreWidgetApiFail = enabled;
//        getSp().edit().putBoolean(MMP_DEBUG_IGNORE_WIDGET_API_FAIL, enabled).apply();
//    }

    public static void setKeepCachedVersion(boolean enabled) {
        DebugHelper.keepCachedVersion = enabled;
        getSp().edit().putBoolean(MMP_DEBUG_KEEP_CACHED_VERSION, enabled).apply();
    }

    /**
     * 宿主设置debug开启关闭
     * @param enable
     */
    public static void enableDebug(boolean enable) {
        if (DebugHelper.isDebug() == enable) {
            return;
        }
        DebugHelper.setDebug(enable);
        getSp().edit()
                .putBoolean(MSC_DEBUG, enable)
                .apply();
    }

    public static boolean debugEnablePreDownload() {
        return getSp().getBoolean(MSC_DEBUG_ENABLE_PRE_DOWNLOAD, true);
    }

    // 注意不是MMPEnvHelper.getDefaultSharedPreferences()，那个是CIP存储的
    public static SharedPreferences getSp() {
        return PreferenceManager.getDefaultSharedPreferences(MSCEnvHelper.getContext());
    }

    public static void ensureInit() {
        //没有初始化过，从sp中取出上次设置的isDebug值；初始化过，直接返回isDebug值
        if (!hasInit) {
            if (!MSCEnvHelper.isInited()) {
                return;
            }
            hasInit = true;

            // FIXME by chendacai: 4/18/22 这块逻辑可以用AOP实现 不在线上包中出现，减少一次IO
            SharedPreferences sp = getSp();
            DebugHelper.setDebug(BuildConfig.DEBUG || sp.getBoolean(MSC_DEBUG, false));
            DebugHelper.debugWebView = DebugHelper.debugWebView || sp.getBoolean(MMP_DEBUG_WEBVIEW, false) || BuildConfig.DEBUG;    //debug service需要附带打开debug WebView，方便起见对sample等直接打开WebView调试
            DebugHelper.enableV8Inspect = DebugHelper.enableV8Inspect || sp.getBoolean(DebugHelper.MSC_ENABLE_V8_INSPECTOR, false);
            DebugHelper.forceWebViewService = DebugHelper.forceWebViewService || sp.getBoolean(MMP_FORCE_WEBVIEW_SERVICE, false);

//            if (DebugHelper.enablePreload == null && sp.contains(MMP_DEBUG_ENABLE_PRELOAD)) {
//                DebugHelper.enablePreload = sp.getBoolean(MMP_DEBUG_ENABLE_PRELOAD, true);
//            }
            if (DebugHelper.keepAliveTime == null && sp.contains(MMP_DEBUG_KEEP_ALIVE_TIME)) {
                DebugHelper.keepAliveTime = sp.getLong(MMP_DEBUG_KEEP_ALIVE_TIME, 0);
            }
//            DebugHelper.navigateInWidget = DebugHelper.navigateInWidget || sp.getBoolean(MMP_DEBUG_NAVIGATE_IN_WIDGET, false);
//            DebugHelper.ignoreWidgetApiFail = DebugHelper.ignoreWidgetApiFail || sp.getBoolean(MMP_DEBUG_IGNORE_WIDGET_API_FAIL, false);
            DebugHelper.keepCachedVersion = DebugHelper.keepCachedVersion || sp.getBoolean(MMP_DEBUG_KEEP_CACHED_VERSION, false);

            if (widgetDefaultUrl == null) {
                widgetDefaultUrl = sp.getString(MMP_DEBUG_WIDGET_DEFAULT_URL, null);
            }

            if (DebugHelper.useMtWebView == null) {
                if (sp.contains("useMtWebView")) {
                    DebugHelper.useMtWebView = sp.getBoolean("useMtWebView", false);
                }
            }

            if (DebugHelper.getForceMultiProcessMode() == DebugHelper.UNINITIALIZED) {
                DebugHelper.setForceMPMode(sp.getInt(MMP_DEBUG_MULTIPROCESS, DebugHelper.DEFAULT_MODE));
                if (DebugHelper.getForceMultiProcessMode() == DebugHelper.FORCE_MODE) {
                    ToastUtils.toastIfDebug("强制打开多进程");
                } else if (DebugHelper.getForceMultiProcessMode() == DebugHelper.FORBIDDEN_MODE) {
                    ToastUtils.toastIfDebug("强制关闭多进程");
                }
            }
        }
    }

    public static void clearImageCacheSync(Context context) {
        if (android.os.Build.VERSION.SDK_INT < android.os.Build.VERSION_CODES.N) {
            return;
        }

        Picasso.with(context).clearMemory();
        CompletableFuture<Object> future = new CompletableFuture<>();
        Jarvis.obtainExecutor().execute(new Runnable() {
            @RequiresApi(api = Build.VERSION_CODES.N)
            @Override
            public void run() {
                Picasso.clearDiskCache(context);
                future.complete(new Object());
            }
        });
        try {
            future.get();
        } catch (Exception e) {
            throw new RuntimeException(e);
        }
    }

    public static Map<String, RouteDebugConfig> convertRouteConfig(Map<String, MMPRouterManager.RouteConfig> config) {
        if (config == null) {
            return new HashMap<>();
        }
        MSCLog.d(TAG, "convertRouteConfig config size: " + config.size() + ", config:" + config.keySet());
        Map<String, RouteDebugConfig> result = new HashMap<>();
        for (Map.Entry<String, MMPRouterManager.RouteConfig> entry : config.entrySet()) {
            String mmpAppId = entry.getKey();
            MMPRouterManager.RouteConfig item = entry.getValue();
            String mmpAppName = "";
            //内置mmpAppName
            for (Map.Entry<String, String> entry1 : MMPNameToAppIdMap.entrySet()) {
                String appName = entry1.getKey();
                String appId = entry1.getValue();
                if (appId.equals(mmpAppId)) {
                    mmpAppName = appName;
                    break;
                }
            }
            RouteDebugConfig routeConfig = new RouteDebugConfig(mmpAppName, item.mscAppId, item.appLifeCyclePersist);
            result.put(mmpAppId, routeConfig);
        }
        MSCLog.d(TAG, "convertRouteConfig result size: " + result.size() + ", result:" + result.keySet());
        return result;
    }

    public static void setMMPRouteConfigToString(Map<String, RouteDebugConfig> config) {
        JSONObject jsonObject = new JSONObject();
        try {
            for (Map.Entry<String, RouteDebugConfig> entry : config.entrySet()) {
                String mmpAppId = entry.getKey();
                RouteDebugConfig routeConfig = entry.getValue();
                JSONObject jsonValue = new JSONObject();
                jsonValue.put("appName", routeConfig.appName);
                jsonValue.put("appId", routeConfig.mscAppId);
                jsonValue.put("appLifeCyclePersist", routeConfig.appLifeCyclePersist);
                jsonObject.put(mmpAppId, jsonValue);
            }
        } catch (JSONException e) {
            MSCLog.e("setMMPRouteConfigToString error", e);
        }
        String configString = jsonObject.toString();
        CIPStorageFileUtil.getCIPStorageCenter(AppContextGetter.getContext()).setString(MSC_DEBUG_MMP_MSC_ROUTE_CONFIG, configString);
    }

    public static Map<String, RouteDebugConfig> getMMPRouteConfigFromSP() {
        String configString = CIPStorageFileUtil.getCIPStorageCenter(AppContextGetter.getContext()).getString(MSC_DEBUG_MMP_MSC_ROUTE_CONFIG, "");
        if (TextUtils.isEmpty(configString)) {
            // 首次安装通过线上horn拉取到的config来作为支持的config，不包含用户手动添加的mmp到msc路由映射关系
            Map<String, RouteDebugConfig> config = DebugManager.convertRouteConfig(MMPRouterManager.getConfig());
            DebugManager.setMMPRouteConfigToString(config);
            return config;
        }
        Map<String, RouteDebugConfig> map = null;
        try {
            JSONObject jsonObject = new JSONObject(configString);
            if (jsonObject.length() > 0) {
                map = new HashMap<>();
                Iterator<String> keys = jsonObject.keys();
                while (keys.hasNext()) {
                    String key = keys.next();
                    JSONObject value = jsonObject.optJSONObject(key);
                    if (value == null) {
                        continue;
                    }
                    String appId = value.optString("appId");
                    if (TextUtils.isEmpty(appId)) {
                        continue;
                    }
                    String appName = value.optString("appName");
                    boolean appLifeCyclePersist = value.optBoolean("appLifeCyclePersist", false);
                    map.put(key, new RouteDebugConfig(appName, appId, appLifeCyclePersist));
                }
            }
        } catch (JSONException e) {
            MSCLog.e("MSCMPRouterManager processConfig error", e);
        }
        return map;
    }

    public static void enableMMPToMSCApp(String appId) {
        MSCLog.d(TAG, "addEnableMMPToMSCApp:", appId);
        Set<String> mmpAppIdSet = getEnableMMPToMSCApps();
        mmpAppIdSet.add(appId);
        CIPStorageFileUtil.getCIPStorageCenter(AppContextGetter.getContext()).setStringSet(MSC_DEBUG_ENABLE_MMP_TO_MSC_SET, mmpAppIdSet);
    }

    public static void disableMMPToMSCApp(String appId) {
        MSCLog.d(TAG, "removeEnableMMPToMSCApp:", appId);
        Set<String> mmpAppIdSet = getEnableMMPToMSCApps();
        mmpAppIdSet.remove(appId);
        CIPStorageFileUtil.getCIPStorageCenter(AppContextGetter.getContext()).setStringSet(MSC_DEBUG_ENABLE_MMP_TO_MSC_SET, mmpAppIdSet);
    }

    public static Set<String> getEnableMMPToMSCApps() {
        return CIPStorageFileUtil.getCIPStorageCenter(AppContextGetter.getContext()).getStringSet(MSC_DEBUG_ENABLE_MMP_TO_MSC_SET, new HashSet<>());
    }

    public static boolean isEnableMMPToMSCInit() {
        return CIPStorageFileUtil.getCIPStorageCenter(AppContextGetter.getContext()).getBoolean(MSC_DEBUG_ENABLE_MMP_TO_MSC_SET_INIT, false);
    }

    public static boolean enableCustomConfigMMPToMSC() {
        return CIPStorageFileUtil.getCIPStorageCenter(AppContextGetter.getContext()).getBoolean(MSC_DEBUG_ENABLE_CUSTOM_CONFIG_MMP2MSC, true);
    }
}
