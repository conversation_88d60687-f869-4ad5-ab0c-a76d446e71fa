package com.meituan.msc.dev.devtools;

import android.content.Context;
import android.text.TextUtils;
import android.view.Gravity;
import android.widget.LinearLayout;
import android.widget.ListView;
import android.widget.SimpleAdapter;
import android.widget.TextView;

import com.google.gson.JsonObject;
import com.google.gson.JsonParser;
import com.meituan.msc.dev.R;
import com.meituan.msc.dev.devtools.ui.PageDialog;

import java.util.ArrayList;
import java.util.HashMap;
import java.util.List;
import java.util.Map;


public class DescriptionDialog extends PageDialog {
    protected List<Map<String, String>> mLists = new ArrayList<>();
    protected LinearLayout.LayoutParams mParams;
    protected LinearLayout mLayout;
    protected TextView mTextView;

    public DescriptionDialog(Context context, String hint) {
        super(context);
        mParams = new LinearLayout.LayoutParams(LinearLayout.LayoutParams.MATCH_PARENT, LinearLayout.LayoutParams.WRAP_CONTENT);
        mLayout = new LinearLayout(context);
        mTextView = new TextView(context);
        mTextView.setGravity(Gravity.CENTER_HORIZONTAL);
        mTextView.setTextSize(20);
        mTextView.setLayoutParams(mParams);
        mTextView.setText(hint);
        mLayout.addView(mTextView);
        setTitleText(context.getResources().getString(R.string.description_title));
        setContentLayout(mLayout);
    }

    public void setDescriptionInfo(String descriptionInfo) {
        if (!TextUtils.isEmpty(descriptionInfo)) {
            mLayout.removeView(mTextView);
            JsonParser jsonParser = new JsonParser();
            JsonObject jsonObject = (JsonObject) jsonParser.parse(descriptionInfo);
            for (Map.Entry<String, com.google.gson.JsonElement> stringJsonElementEntry : jsonObject.entrySet()) {
                HashMap<String, String> map = new HashMap<>();
                insertInfo(map, stringJsonElementEntry);
                mLists.add(map);
            }
        }
    }

    private void insertInfo(Map<String, String> map, Map.Entry entry) {
        switch ((String) entry.getKey()) {
            case "buildId":
                map.put("description_key", "构建id");
                break;
            case "versionName":
                map.put("description_key", "构建的版本号");
                break;
            case "templateName":
                map.put("description_key", "构建模板名称");
                break;
            case "templateId":
                map.put("description_key", "构建模板id");
                break;
            case "version":
                map.put("description_key", "版本");
                break;
            default:
                break;
        }
        map.put("description_value", String.valueOf(entry.getValue()));
    }

    public void setListView(Context context) {
        ListView listView = new ListView(context);
        listView.setLayoutParams(mParams);
        SimpleAdapter adapter = new SimpleAdapter(context,
                mLists,
                R.layout.msc_description_info_item,
                new String[]{"description_key", "description_value"},
                new int[]{R.id.description_key, R.id.description_value});
        listView.setAdapter(adapter);
        mLayout.addView(listView);
    }
}