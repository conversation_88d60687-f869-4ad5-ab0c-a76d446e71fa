package com.meituan.msc.dev.devtools;

import android.net.LocalSocket;
import android.net.LocalSocketAddress;
import android.os.Handler;
import android.os.Message;
import android.text.TextUtils;

import com.meituan.msc.common.utils.IOUtil;
import com.meituan.msc.dev.devtools.debugger.utils.IDELogUtil;
import com.meituan.msc.modules.reporter.MSCLog;

import java.io.IOException;
import java.io.OutputStream;
import java.nio.charset.StandardCharsets;

public class LocalSocketClientTask implements Runnable {
    private static final String TAG = "LocalSocketClientTask";
    private LocalSocket socket;
    private final LocalSocketAddress localSocketAddress;
    private final Handler handler;
    private boolean enable;
    public static final String MSG_CONNECTED = "CONNECTED";

    public LocalSocketClientTask(LocalSocketAddress localSocketAddress, Handler handler) {
        this.localSocketAddress = localSocketAddress;
        this.handler = handler;
        this.initLocalSocket();
    }

    /**
     * 发数据
     *
     * @param message
     */
    public void send(String message) {
        MSCLog.i(IDELogUtil.tag(TAG), "[LocalSocketClientTask@send] message:", message);
        try {
            OutputStream os = socket.getOutputStream();
            os.write(message.getBytes(StandardCharsets.UTF_8));
            os.flush();
        } catch (IOException e) {
            MSCLog.e(IDELogUtil.tag(TAG), e, "[LocalSocketClientTask@send]", e);
        }
    }

    @Override
    public void run() {
        MSCLog.i(IDELogUtil.tag(TAG), "[LocalSocketClientTask@run] enable:", enable);
        try {
            while (enable) {
                byte[] response = IOUtil.readFully(socket.getInputStream(), socket.getInputStream().available());
                String responseString = new String(response);
                if (!TextUtils.isEmpty(responseString)) {
                    sendResult(responseString);
                }
            }
        } catch (IOException e) {
            MSCLog.e(IDELogUtil.tag(TAG), e, "[LocalSocketClientTask@run]", e);
        }
    }

    private void initLocalSocket() {
        MSCLog.i(IDELogUtil.tag(TAG), "[LocalSocketClientTask@initLocalSocket]");
        try {
            socket = new LocalSocket();
            socket.connect(localSocketAddress);
            enable = true;
            MSCLog.i(IDELogUtil.tag(TAG), "server name: " + localSocketAddress.getName() + ", Client isConnected: " + socket.isConnected());
            sendResult(MSG_CONNECTED);
        } catch (IOException e) {
            MSCLog.e(IDELogUtil.tag(TAG), e, "[LocalSocketClientTask@initLocalSocket]", e);
        }
    }

    private void sendResult(String message) {
        MSCLog.i(IDELogUtil.tag(TAG), "[LocalSocketClientTask@sendResult] message:", message);
        Message msg = handler.obtainMessage();
        msg.obj = message;
        handler.sendMessage(msg);
    }

    public void closeSocket() {
        MSCLog.i(IDELogUtil.tag(TAG), "[LocalSocketClientTask@closeSocket] socket:" , socket);
        enable = false;
        try {
            if (socket != null) {
                socket.close();
            }
        } catch (IOException e) {
            MSCLog.e(IDELogUtil.tag(TAG), e, "[LocalSocketClientTask@closeSocket]", e);
        }
    }
}
