package com.meituan.msc.dev.devtools;

import com.dianping.networklog.Logan;
import com.meituan.msc.dev.command.ArgumentName;
import com.meituan.msc.dev.command.Command;
import com.meituan.msc.dev.command.CommandGroup;
import com.meituan.msc.dev.utils.AppContextGetter;
import com.meituan.uuid.GetUUID;

import java.text.SimpleDateFormat;
import java.util.Date;
import java.util.Locale;

@CommandGroup(name = "logan")
public class LoganCommands {

    /**
     * 主动上报当天的Logan日志
     * 使用： adb shell am broadcast -p com.meituan.android.msc.sample -a com.meituan.msc.dev.command -e command logan.uploadLogan
     *
     */
    @Command
    public static String uploadLogan(@ArgumentName("bata")Boolean beta) {
        // 上报到测试环境
        if (beta != null && beta) {
            Logan.setBeta(true);
        }
        // 上报指定日期的日志,格式为2017-04-11
        SimpleDateFormat dateFormat = new SimpleDateFormat("yyyy-MM-dd", Locale.getDefault());
        String formattedDate = dateFormat.format(new Date());
        String unionId = GetUUID.getInstance().getSyncUUID(AppContextGetter.getContext(), null);
        Logan.s(new String[]{formattedDate}, unionId, Logan.SEND_LOGAN_ACTION, null, null);
        return unionId;
    }
}
