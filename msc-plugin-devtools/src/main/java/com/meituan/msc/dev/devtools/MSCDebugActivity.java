package com.meituan.msc.dev.devtools;

import android.Manifest;
import android.annotation.SuppressLint;
import android.app.Activity;
import android.app.ActivityManager;
import android.app.AlertDialog;
import android.app.Dialog;
import android.content.ClipData;
import android.content.ComponentName;
import android.content.Context;
import android.content.Intent;
import android.content.SharedPreferences;
import android.content.pm.PackageManager;
import android.graphics.Bitmap;
import android.graphics.Color;
import android.net.Uri;
import android.os.Bundle;
import android.preference.PreferenceManager;
import android.support.annotation.NonNull;
import android.support.v4.app.ActivityCompat;
import android.support.v4.content.ContextCompat;
import android.text.TextUtils;
import android.view.Gravity;
import android.view.LayoutInflater;
import android.view.MenuItem;
import android.view.View;
import android.view.View.OnClickListener;
import android.view.ViewGroup;
import android.widget.BaseAdapter;
import android.widget.Button;
import android.widget.CompoundButton;
import android.widget.EditText;
import android.widget.ImageView;
import android.widget.LinearLayout;
import android.widget.ListView;
import android.widget.RadioButton;
import android.widget.RadioGroup;
import android.widget.Switch;
import android.widget.TextView;

import com.dianping.nvnetwork.NVAppMockManager;
import com.google.zxing.BarcodeFormat;
import com.meituan.android.base.util.ZXingUtils;
import com.meituan.android.cipstorage.CIPStorageCenter;
import com.meituan.android.privacy.interfaces.MtClipboardManager;
import com.meituan.android.privacy.interfaces.Privacy;
import com.meituan.met.mercury.load.bean.ExtraParamsBean;
import com.meituan.msc.common.executor.MSCExecutors;
import com.meituan.msc.common.process.GlobalEngineMonitor;
import com.meituan.msc.common.process.MSCProcess;
import com.meituan.msc.common.process.ProcessMonitor;
import com.meituan.msc.common.utils.Constants;
import com.meituan.msc.common.utils.IntentUtil;
import com.meituan.msc.common.utils.Interceptors;
import com.meituan.msc.common.utils.MSCAppPropertyUtil;
import com.meituan.msc.common.utils.ToastUtils;
import com.meituan.msc.dev.R;
import com.meituan.msc.dev.aop.MSCWidgetFragmentAop;
import com.meituan.msc.dev.command.DevUriHandler;
import com.meituan.msc.dev.devtools.debugger.MSCDebugger;
import com.meituan.msc.dev.devtools.debugger.MSCDebuggerManager;
import com.meituan.msc.dev.devtools.debugger.utils.IDELogUtil;
import com.meituan.msc.dev.devtools.lockversion.ui.BasePackageUI;
import com.meituan.msc.dev.devtools.lockversion.ui.BizPackageListUI;
import com.meituan.msc.dev.devtools.preload.PreloadView;
import com.meituan.msc.dev.devtools.reporter.DevToolsConstants;
import com.meituan.msc.dev.devtools.ui.DebugView;
import com.meituan.msc.dev.modules.update.packageattachment.CodeCacheDevHelper;
import com.meituan.msc.dev.performance.PerformanceTestManager;
import com.meituan.msc.dev.utils.AppContextGetter;
import com.meituan.msc.dev.utils.DebugConfig;
import com.meituan.msc.dev.widget.TabWidgetActivity;
import com.meituan.msc.dev.widget.TestDialogActivity;
import com.meituan.msc.extern.MSCEnvHelper;
import com.meituan.msc.modules.api.appLifecycle.MSCAppLifecycle;
import com.meituan.msc.modules.api.appLifecycle.MSCAppLifecycleObserver;
import com.meituan.msc.modules.api.appLifecycle.MSCAppLifecycleParams;
import com.meituan.msc.modules.container.ContainerController;
import com.meituan.msc.modules.container.IContainerManager;
import com.meituan.msc.modules.devtools.DebugHelper;
import com.meituan.msc.modules.devtools.IDebugger;
import com.meituan.msc.modules.engine.MSCRuntime;
import com.meituan.msc.modules.engine.RuntimeManager;
import com.meituan.msc.modules.page.IPageModule;
import com.meituan.msc.modules.page.Page;
import com.meituan.msc.modules.page.render.BaseRenderer;
import com.meituan.msc.modules.page.render.webview.MSCWebViewRenderer;
import com.meituan.msc.modules.page.render.webview.WebViewCacheManager;
import com.meituan.msc.modules.preload.PreloadTasksManager;
import com.meituan.msc.modules.reporter.CommonTags;
import com.meituan.msc.modules.reporter.MSCLog;
import com.meituan.msc.modules.update.DDLoaderDebugHelper;
import com.meituan.msc.modules.update.metainfo.MetaFetchRulerManager;
import com.meituan.msc.modules.update.pkg.MSCPackageHelper;
import com.meituan.msi.ApiPortalGlobalEnv;
import com.sankuai.common.utils.ColorUtils;
import com.sankuai.meituan.android.ui.widget.SnackbarBuilder;
import com.sankuai.meituan.retrofit2.mock.MockInterceptor;

import java.lang.reflect.Field;
import java.util.ArrayList;
import java.util.Arrays;
import java.util.Collection;
import java.util.List;
import java.util.Map;
import java.util.concurrent.atomic.AtomicBoolean;
import java.util.concurrent.FutureTask;

import static com.meituan.msc.common.utils.IntentUtil.getIntExtra;
import static com.meituan.msc.common.utils.IntentUtil.getStringExtra;
import static com.meituan.msc.modules.page.render.webview.WebViewCacheManager.WebViewType.CHROME;
import static com.meituan.msc.modules.page.render.webview.WebViewCacheManager.WebViewType.MT_WEB_VIEW;
import static com.meituan.msc.modules.page.render.webview.WebViewCacheManager.WebViewType.MT_WEB_VIEW_SYSTEM;


/**
 * 从小程序中的悬浮窗进入的调试面板
 * 跳链: imeituan://www.meituan.com/msc/debug
 */
public class MSCDebugActivity extends Activity {
    private static final String TAG = "MSCDebugActivity";

    private static final int REQUEST_WRITE_EXTERNAL_STORAGE = 1;
    private static final int REQUEST_CODE_IDE_ATTACH = 10;
    private static final int REQUEST_CODE_CAMERA = 2;
    private static final String SP_LAST_SCAN_URL = "last_scan_url";

    private final List<Bean> mItemList = new ArrayList<>();
    private final MainAdapter mAdapter = new MainAdapter();
    private LinearLayout baseLayout;
    private LinearLayout performanceLayout;
    private LinearLayout renderLayout;
    private LinearLayout otherLayout;
    private TextView baseOption;
    private TextView performanceOption;
    private TextView renderOption;
    private TextView otherOption;
    private View baseOption_li;
    private View performanceOption_li;
    private View renderOption_li;
    private View otherOption_li;

    private String currentAppId;
    private int currentRuntimeId;
    private final List<ExtraParamsBean> bundleExtraParams = new ArrayList<>();
    private MSCAppLifecycleObserver observer = new MSCAppLifecycleObserver() {
        @Override
        public void onEvent(MSCAppLifecycle mscAppLifecycleName, MSCAppLifecycleParams params) {
            MSCLog.d("TestMSCAppLifecycleObserver",
                    "appId:", getAppId(),
                    "mscAppLifecycleName:", mscAppLifecycleName.toString(),
                    "targetPath:", params.targetPath,
                    "enterUri:", params.enterUri,
                    "leaveAppInfo:", params.leaveAppInfo);
        }

        @Override
        public String getAppId() {
            return "qa_mscdemo_2";
        }

        @Override
        public List<MSCAppLifecycle> getValidLifecycleList() {
            List<MSCAppLifecycle> mscAppLifecycleList = Arrays
                    .asList(MSCAppLifecycle.MSC_WILL_ENTER_APP_LIFECYCLE,
                            MSCAppLifecycle.MSC_DID_ENTER_APP_LIFECYCLE,
                            MSCAppLifecycle.MSC_WILL_LEAVE_APP_LIFECYCLE,
                            MSCAppLifecycle.MSC_LAUNCH_FAIL_APP_LIFECYCLE);
            return mscAppLifecycleList;
        }
    };

    private BaseRenderer getCurrentRender() {
        // FIXME 由于当前DebugView是挂在Activity上的，不是挂在Page中的，所以这里临时取toppage，待后面DebugView挂到Page对象后再通过传递pageId的方式获取Render
        MSCRuntime runtime = getCurrentRuntime();
        if (runtime != null) {
            IPageModule pageModule = runtime.getTopPageModule();
            if (pageModule != null) {
                return pageModule.getRenderer();
            }
        }
        return null;
    }

    private WebViewCacheManager.WebViewType getWebViewType() {
        BaseRenderer baseRenderer = getCurrentRender();
        if (baseRenderer instanceof MSCWebViewRenderer) {
            return CommonTags.getWebViewTypeFromRenderer(baseRenderer);
        } else {
            return null;
        }
    }

//    CheckBox debugMultiProcess;

    ProcessMonitor.ProcessDieListener processDieListener = new ProcessMonitor.ProcessDieListener() {
        @Override
        public void onProcessDie(MSCProcess diedProcess) {
            refreshData();
        }
    };

    private void extractIntentExtra(Intent intent) {
        currentAppId = getStringExtra(intent, DebugView.EXTRA_KEY_APP_ID);
        currentRuntimeId = getIntExtra(intent, DebugView.EXTRA_KEY_RUNTIME_ID, 0);
        MSCLog.i(TAG, currentAppId, currentRuntimeId);
    }

    private MSCRuntime getCurrentRuntime() {
        return RuntimeManager.getRuntimeWithId(currentRuntimeId, currentAppId);
    }

    @Override
    protected void onNewIntent(Intent intent) {
        super.onNewIntent(intent);
        DebugIntentHandler.getInstance().handleIntent(this, intent);
        DevUriHandler.getInstance().handleIntent(this, intent);
        extractIntentExtra(intent);
    }

    @Override
    public void onRequestPermissionsResult(int requestCode, @NonNull String[] permissions, @NonNull int[] grantResults) {
        if (requestCode == REQUEST_WRITE_EXTERNAL_STORAGE) {
            final int result;
            if (grantResults.length > 0) {
                result = grantResults[0];
            } else {
                result = PackageManager.PERMISSION_DENIED;
            }
            MSCLog.i(TAG, "write storage granted:", result == PackageManager.PERMISSION_GRANTED);
        } else if (requestCode == REQUEST_CODE_CAMERA && grantResults[0] == PackageManager.PERMISSION_GRANTED) {
            Intent intent = new Intent();
            intent.setData(Uri.parse("imeituan://www.meituan.com/scanQRCodeForResult?openAR=0&albumScanEnable=1&needResult=1"));
            intent.setPackage(getPackageName());
            startActivityForResult(intent, REQUEST_CODE_CAMERA);
        } else {
            ToastUtils.toast("需要权限");
        }
    }


    @Override
    protected void onCreate(Bundle savedInstanceState) {
        ActivityCompat.requestPermissions(this, new String[]{Manifest.permission.WRITE_EXTERNAL_STORAGE}, REQUEST_WRITE_EXTERNAL_STORAGE);
        if (PerformanceTestManager.isEnabledPerformanceTestMode()) {
            // 性能测试默认清除图片缓存
            DebugManager.clearImageCacheSync(this);
        }

        super.onCreate(savedInstanceState);
        // 处理跳链参数
        DebugIntentHandler.getInstance().handleIntent(this, getIntent());
        DevUriHandler.getInstance().handleIntent(this, getIntent());
        extractIntentExtra(getIntent());
        AppContextGetter.setContext(getApplicationContext());
        setContentView(R.layout.msc_debugview_activity);

        MSCPackageHelper.setPkgExtraParams(bundleExtraParams);
        findViewById(R.id.img_back).setOnClickListener(new View.OnClickListener() {
            @Override
            public void onClick(View v) {
                finish();
            }
        });

        baseLayout = findViewById(R.id.navigation_base_layout);
        performanceLayout = findViewById(R.id.navigation_performance_layout);
        renderLayout = findViewById(R.id.navigation_render_layout);
        otherLayout = findViewById(R.id.navigation_other_layout);

        baseOption = findViewById(R.id.navigation_base);
        performanceOption = findViewById(R.id.navigation_performance);
        renderOption = findViewById(R.id.navigation_render);
        otherOption = findViewById(R.id.navigation_other);
        baseOption_li = findViewById(R.id.navigation_base_li);
        performanceOption_li = findViewById(R.id.navigation_performance_li);
        renderOption_li = findViewById(R.id.navigation_render_li);
        otherOption_li = findViewById(R.id.navigation_other_li);

        baseOption.setOnClickListener(new View.OnClickListener() {
            @Override
            public void onClick(View v) {
                showBaseLayout();
                resetTextColors();
                baseOption.setTextColor(ColorUtils.parseColor("#43b3f9",67179249));
                baseOption_li.setVisibility(View.VISIBLE);
            }
        });

        performanceOption.setOnClickListener(new View.OnClickListener() {
            @Override
            public void onClick(View v) {
                showPerformanceLayout();
                resetTextColors();
                performanceOption.setTextColor(ColorUtils.parseColor("#43b3f9",67179249));
                performanceOption_li.setVisibility(View.VISIBLE);

            }
        });

        renderOption.setOnClickListener(new View.OnClickListener() {
            @Override
            public void onClick(View v) {
                showRenderLayout();
                resetTextColors();
                renderOption.setTextColor(ColorUtils.parseColor("#43b3f9",67179249));
                renderOption_li.setVisibility(View.VISIBLE);
            }
        });

        otherOption.setOnClickListener(new View.OnClickListener() {
            @Override
            public void onClick(View v) {
                showOtherLayout();
                resetTextColors();
                otherOption.setTextColor(ColorUtils.parseColor("#43b3f9",67179249));
                otherOption_li.setVisibility(View.VISIBLE);
            }
        });
        baseOption.setTextColor(ColorUtils.parseColor("#43b3f9",67179249));
        baseOption_li.setVisibility(View.VISIBLE);
        showBaseLayout();

        final EditText miniAppUrlTxt = findViewById(R.id.mini_app_path_txt);
        DebugManager.ensureInit();
        if (!TextUtils.isEmpty(DebugManager.widgetDefaultUrl)) {
            miniAppUrlTxt.setText(DebugManager.widgetDefaultUrl);
        }

        Button miniAppEnterBtn = findViewById(R.id.enter_button);
        miniAppEnterBtn.setOnClickListener(new View.OnClickListener() {
            @Override
            public void onClick(View v) {
                try {
                    String path = miniAppUrlTxt.getText().toString();
                    Uri uriPath = Uri.parse(path);
                    Intent intent = new Intent().setData(uriPath).setPackage(getPackageName());
                    startActivity(intent);
                } catch (Exception e) {
                    e.printStackTrace();
                }
            }
        });
        findViewById(R.id.btn_tab_widget).setOnClickListener(new View.OnClickListener() {
            @Override
            public void onClick(View v) {
                Intent intent = new Intent();
                intent.setComponent(new ComponentName(MSCDebugActivity.this, TabWidgetActivity.class));
                startActivity(intent);
            }
        });
        findViewById(R.id.btn_popup_window).setOnClickListener(new View.OnClickListener() {
            @Override
            public void onClick(View v) {
                Intent intent = new Intent();
                intent.setComponent(new ComponentName(MSCDebugActivity.this, TestDialogActivity.class));
                startActivity(intent);
            }
        });


        Button startWidgetBtn = findViewById(R.id.start_widget_activity);
        startWidgetBtn.setOnClickListener(new View.OnClickListener() {
            @Override
            public void onClick(View v) {
                String url = miniAppUrlTxt.getText().toString();
                Uri uri = Uri.parse(url);
                uri.buildUpon().appendQueryParameter("isWidget", "true");
                Intent intent = new Intent(Intent.ACTION_VIEW, uri);
                intent.setPackage(getPackageName());
                startActivity(intent);
            }
        });

        Switch box = findViewById(R.id.cb_guard);
        box.setChecked(Interceptors.getIsNeededMtGuardSignAndSiua());
        box.setOnCheckedChangeListener(new CompoundButton.OnCheckedChangeListener() {
            @Override
            public void onCheckedChanged(CompoundButton buttonView, boolean isChecked) {
                Interceptors.setIsNeededMtGuardSignAndSiua(isChecked);
            }
        });

        // TODO: 2022/5/18 tianbin 调整布局后展示
//        TextView tvScheme = findViewById(R.id.tv_scheme);
//        if (!TextUtils.isEmpty(getLaunchScheme())) {
//            tvScheme.setText("启动链接：" + getLaunchScheme());
//        }
        Switch prefetchCrashCheckSwitch = findViewById(R.id.prefetch_crash_check);
        prefetchCrashCheckSwitch.setChecked(DebugManager.getSp().getBoolean(DebugManager.MSC_PREFETCH_CRASH_CHECK, true));
        prefetchCrashCheckSwitch.setOnCheckedChangeListener(new CompoundButton.OnCheckedChangeListener() {
            @Override
            public void onCheckedChanged(CompoundButton buttonView, boolean isChecked) {
                DebugManager.getSp().edit().putBoolean(DebugManager.MSC_PREFETCH_CRASH_CHECK, isChecked).apply();
            }
        });


        RadioGroup ideRWidgetRadioGroup = findViewById(R.id.radio_group_widget_path_debug);
        ((RadioButton) ideRWidgetRadioGroup.getChildAt(MSCWidgetFragmentAop.widgetPathDebugMode)).setChecked(true);
        ideRWidgetRadioGroup.setOnCheckedChangeListener(new RadioGroup.OnCheckedChangeListener() {
            @Override
            public void onCheckedChanged(RadioGroup group, int checkedId) {
                for (int i = 0; i < group.getChildCount(); i++) {
                    if (group.getChildAt(i).getId() == checkedId) {
                        MSCWidgetFragmentAop.widgetPathDebugMode = i;
                        break;
                    }
                }
            }
        });

        TextView tvVersionInfo = findViewById(R.id.tv_versioninfo);
        if (!TextUtils.isEmpty(getPackageInfo()) && !DevToolsConstants.PACKAGEINFO_NULL.equalsIgnoreCase(getPackageInfo())) {
            tvVersionInfo.setText(getPackageInfo());
        } else {
            tvVersionInfo.setText("请先选择一个业务小程序哦");
        }

        if (!TextUtils.isEmpty(getCheckUpdateUrl())) {
            tvVersionInfo.setOnClickListener(new View.OnClickListener() {
                @Override
                public void onClick(View v) {
                    new AlertDialog.Builder(v.getContext()).setMessage(getCheckUpdateUrl()).show();
                }
            });
        }

        findViewById(R.id.description_button).setOnClickListener(new View.OnClickListener() {
            @Override
            public void onClick(View v) {
                String descriptionInfo = IntentUtil.getStringExtra(getIntent(), Constants.DESCRIPTION);
                String hint = IntentUtil.getStringExtra(getIntent(), Constants.VERSION) + getResources().getString(R.string.mmp_debug_description_not_preview);
                DescriptionDialog descriptionDialog = new DescriptionDialog(MSCDebugActivity.this, hint);
                if (getResources().getString(R.string.mmp_debug_preview).equals(IntentUtil.getStringExtra(getIntent(), Constants.VERSION))) {
                    descriptionDialog.setDescriptionInfo(descriptionInfo);
                    descriptionDialog.setListView(getBaseContext());
                }
                descriptionDialog.show();
            }
        });


        Switch debugWebViewService = findViewById(R.id.force_webview_service);
        debugWebViewService.setChecked(DebugHelper.forceWebViewService);
        debugWebViewService.setOnCheckedChangeListener(new CompoundButton.OnCheckedChangeListener() {
            @Override
            public void onCheckedChanged(CompoundButton buttonView, boolean isChecked) {
                SharedPreferences sharedPreferences = PreferenceManager.getDefaultSharedPreferences(MSCEnvHelper.getContext());
                SharedPreferences.Editor editor = sharedPreferences.edit();
                editor.putBoolean(DebugManager.MMP_FORCE_WEBVIEW_SERVICE, isChecked).apply();
                DebugHelper.forceWebViewService = isChecked;
                if (isChecked) {
                    ToastUtils.toast("WebViewService enable");
                } else {
                    ToastUtils.toast("WebViewService disable");
                }
            }
        });

        Switch debugWebView = findViewById(R.id.debug_webview_btn);
        debugWebView.setChecked(DebugHelper.debugWebView);
        debugWebView.setOnCheckedChangeListener(new CompoundButton.OnCheckedChangeListener() {
            @Override
            public void onCheckedChanged(CompoundButton buttonView, boolean isChecked) {
                SharedPreferences sharedPreferences = PreferenceManager.getDefaultSharedPreferences(MSCEnvHelper.getContext());
                SharedPreferences.Editor editor = sharedPreferences.edit();
                editor.putBoolean(DebugManager.MMP_DEBUG_WEBVIEW, isChecked).apply();
                if (isChecked) {
                    ToastUtils.toast("debugWebView enable");
                } else {
                    ToastUtils.toast("debugWebView disable");
                }
            }
        });

        // 锁定基础包版本
        (new BasePackageUI(this)).show();

        // 锁定业务版本包
        //开始选择前，先清空缓存信息
        (new BizPackageListUI(this)).show();

        (new PreloadView(this)).show();

        Switch keepCachedVersion = findViewById(R.id.cb_keep_cached_version);
        keepCachedVersion.setChecked(DebugHelper.keepCachedVersion);
        keepCachedVersion.setOnCheckedChangeListener(new CompoundButton.OnCheckedChangeListener() {
            @Override
            public void onCheckedChanged(CompoundButton buttonView, boolean isChecked) {
                DebugManager.setKeepCachedVersion(isChecked);
                if (isChecked) {
                    ToastUtils.toast("保持当前缓存包，正常启动不检查更新，不阻止checkUpdateUrl生效");
                }
            }
        });

        final boolean bizPackageUpdateFromTestEnv = DDLoaderDebugHelper.shouldCheckUpdateFromTestEnv();
        Switch bizPackageUpdateSwitch = findViewById(R.id.biz_package_update_switch);
        bizPackageUpdateSwitch.setChecked(bizPackageUpdateFromTestEnv);
        bizPackageUpdateSwitch.setOnCheckedChangeListener(new CompoundButton.OnCheckedChangeListener() {
            @Override
            public void onCheckedChanged(CompoundButton buttonView, boolean isChecked) {
                DDLoaderDebugHelper.setCheckUpdateFromTestEnv(isChecked);
                ToastUtils.toast("重启生效");
            }
        });

        Switch cbShowDebug = findViewById(R.id.cb_show_debug);
        cbShowDebug.setChecked(DebugManager.getSp().getBoolean(DebugManager.MSC_DEBUG, false));
        cbShowDebug.setOnCheckedChangeListener(new CompoundButton.OnCheckedChangeListener() {
            @Override
            public void onCheckedChanged(CompoundButton buttonView, boolean isChecked) {
                DebugHelper.setDebug(isChecked);
                DebugManager.getSp().edit().putBoolean(DebugManager.MSC_DEBUG, isChecked).apply();
            }
        });

        Switch cbMsiDebug = findViewById(R.id.msi_debug_switch);
        cbMsiDebug.setChecked(ApiPortalGlobalEnv.isDebug());
        cbMsiDebug.setOnCheckedChangeListener(new CompoundButton.OnCheckedChangeListener() {
            @Override
            public void onCheckedChanged(CompoundButton buttonView, boolean isChecked) {
                ApiPortalGlobalEnv.setDebug(isChecked);
            }
        });

        Switch preDownload = findViewById(R.id.cb_allow_pre_download);
        preDownload.setChecked(DebugManager.debugEnablePreDownload());
        preDownload.setOnCheckedChangeListener(new CompoundButton.OnCheckedChangeListener() {
            @Override
            public void onCheckedChanged(CompoundButton buttonView, boolean isChecked) {
                DebugManager.saveDebugEnablePreDownload(isChecked);
            }
        });

        Switch useMtWebView = findViewById(R.id.cb_useMtWebView);
        useMtWebView.setChecked(DebugHelper.getUseMtWebView());
        useMtWebView.setOnCheckedChangeListener(new CompoundButton.OnCheckedChangeListener() {
            @Override
            public void onCheckedChanged(CompoundButton buttonView, boolean isChecked) {
                DebugManager.setUseMtWebView(isChecked);
            }
        });

        WebViewCacheManager.WebViewType webViewType = getWebViewType();
        BaseRenderer renderer = getCurrentRender();
        String webViewTypeDescription;
        if (renderer instanceof MSCWebViewRenderer) {
            if (webViewType == MT_WEB_VIEW) {
                webViewTypeDescription = "自研浏览器";
            } else if (webViewType == CHROME || webViewType == MT_WEB_VIEW_SYSTEM) {
                webViewTypeDescription = "系统浏览器";
            } else {
                webViewTypeDescription = "未知";
            }
        } else {
            webViewTypeDescription = "非WebView渲染模式";
        }
        String webViewTypeStr = CommonTags.getWebViewType(webViewType);
        ((TextView) findViewById(R.id.lbl_webView_type)).setText("当前使用WebView：" + webViewTypeDescription + (TextUtils.isEmpty(webViewTypeStr) ? "" : "(" + webViewTypeStr + ")"));
        findViewById(R.id.btn_open_url).setOnClickListener(new View.OnClickListener() {
            @Override
            public void onClick(View view) {
                try {
                    String path = "imeituan://www.meituan.com/dev/mtwebview";
                    Uri uriPath = Uri.parse(path);
                    Intent intent = new Intent().setData(uriPath).setPackage(getPackageName());
                    startActivity(intent);
                } catch (Exception e) {
                    e.printStackTrace();
                }
            }
        });

        Switch forceSameLayerErrorDowngrade = findViewById(R.id.cb_forceSameLayerErrorDowngrade);
        forceSameLayerErrorDowngrade.setChecked(DebugHelper.forceSameLayerErrorDowngrade);
        forceSameLayerErrorDowngrade.setOnCheckedChangeListener(new CompoundButton.OnCheckedChangeListener() {
            @SuppressLint("WrongConstant")
            @Override
            public void onCheckedChanged(CompoundButton buttonView, boolean isChecked) {
                DebugHelper.forceSameLayerErrorDowngrade = isChecked;
            }
        });

        Switch mockPrefetchSubPkgFailed = findViewById(R.id.cb_mock_prefetch_subpkg_failed);
        mockPrefetchSubPkgFailed.setChecked(DebugHelper.mockPrefetchSubPkgFailed);
        mockPrefetchSubPkgFailed.setOnCheckedChangeListener(new CompoundButton.OnCheckedChangeListener() {
            @Override
            public void onCheckedChanged(CompoundButton buttonView, boolean isChecked) {
                DebugHelper.mockPrefetchSubPkgFailed = isChecked;
            }
        });

        Switch enableV8Debug = findViewById(R.id.cb_v8Inspect);
        enableV8Debug.setChecked(DebugHelper.enableV8Inspect);
        enableV8Debug.setOnCheckedChangeListener(new CompoundButton.OnCheckedChangeListener() {
            @Override
            public void onCheckedChanged(CompoundButton compoundButton, boolean isChecked) {
                DebugHelper.enableV8Inspect = isChecked;
                PreferenceManager.getDefaultSharedPreferences(MSCEnvHelper.getContext()).edit().putBoolean(DebugHelper.MSC_ENABLE_V8_INSPECTOR, isChecked).apply();
            }
        });

        Switch showPagePathSwitch = findViewById(R.id.cb_show_page_path);
        boolean showPagePath = PreferenceManager.getDefaultSharedPreferences(MSCEnvHelper.getContext()).getBoolean(DebugConfig.MSC_SHOW_PAGE_PATH, false);
        showPagePathSwitch.setChecked(showPagePath);
        showPagePathSwitch.setOnCheckedChangeListener(new CompoundButton.OnCheckedChangeListener() {
            @Override
            public void onCheckedChanged(CompoundButton compoundButton, boolean isChecked) {
                PreferenceManager.getDefaultSharedPreferences(MSCEnvHelper.getContext()).edit().putBoolean(DebugConfig.MSC_SHOW_PAGE_PATH, isChecked).apply();
            }
        });

        findViewById(R.id.btn_open_cip).setOnClickListener(new View.OnClickListener() {
            @Override
            public void onClick(View v) {
                try {
                    Intent intent = new Intent()
                            .setClassName(getApplicationContext(), "com.meituan.android.cips.mt.CIPSTestActivity")
                            .setPackage(getPackageName());
                    startActivity(intent);
                } catch (Exception e) {
                    e.printStackTrace();
                }
            }
        });

        Switch vConsoleCb = findViewById(R.id.cb_v_console);
        vConsoleCb.setChecked(PreferenceManager.getDefaultSharedPreferences(MSCEnvHelper.getContext()).getBoolean(DevToolsConstants.MSC_IDE_SP_V_CONSOLE_SWITCH, false));
        vConsoleCb.setOnCheckedChangeListener(new CompoundButton.OnCheckedChangeListener() {
            @Override
            public void onCheckedChanged(CompoundButton buttonView, boolean isChecked) {
                ToastUtils.toastIfDebug("%svConsole，重启App后生效", isChecked ? "已开启" : "已关闭");
                PreferenceManager.getDefaultSharedPreferences(MSCEnvHelper.getContext()).edit().putBoolean(DevToolsConstants.MSC_IDE_SP_V_CONSOLE_SWITCH, isChecked).apply();
            }
        });

        findViewById(R.id.btn_ide_attach).setOnClickListener(new View.OnClickListener() {
            @Override
            public void onClick(View v) {
                MSCRuntime runtime = getCurrentRuntime();
                if (runtime == null) {
                    return;
                }
                MSCDebugger debugger = (MSCDebugger) runtime.getModuleWithoutDelegate(IDebugger.class);
                if (debugger != null) {
                    if (debugger.isConnected()) {
                        ToastUtils.toastIfDebug("调试已开启，请勿重复操作");
                        return;
                    }
                    MSCDebuggerManager.stopDebug(runtime, "attach");
                }

                Intent intent = new Intent();
                intent.setData(Uri.parse("imeituan://www.meituan.com/scanQRCodeForResult?openAR=0&albumScanEnable=0&needResult=1"));
                intent.setPackage(getPackageName());
                startActivityForResult(intent, REQUEST_CODE_IDE_ATTACH);
            }
        });

        //关闭 Widget 调试能力
        // 同步按钮样式
        Button buttonWidgetDebug = findViewById(R.id.btn_ide_widget_debug);
        boolean isWidgetDebug = MSCEnvHelper.getDefaultSharedPreferences().getBoolean(DevToolsConstants.PARAMS_DEBUG_IS_WIDGET_DEBUG, true);
        String widgetDebugStr = isWidgetDebug ? "关闭" : "打开";
        buttonWidgetDebug.setText(widgetDebugStr);
        buttonWidgetDebug.setOnClickListener(new View.OnClickListener() {
            @Override
            public void onClick(View v) {
                boolean widgetDebug = MSCEnvHelper.getDefaultSharedPreferences().getBoolean(DevToolsConstants.PARAMS_DEBUG_IS_WIDGET_DEBUG, true);
                SharedPreferences.Editor edit = MSCEnvHelper.getDefaultSharedPreferences().edit();
                widgetDebug = modifyWidgetDebug(edit, buttonWidgetDebug, widgetDebug);

                MSCRuntime runtime = getCurrentRuntime();
                if (runtime == null) {
                    return;
                }
                MSCDebugger debugger = (MSCDebugger) runtime.getModuleWithoutDelegate(IDebugger.class);
                if (debugger != null) {
                    debugger.toggleWidgetDebugStatus(widgetDebug);
                }
            }
        });
        //同步 Toast 提醒 与按钮
        Button buttonFlipper = findViewById(R.id.btn_flipper);
        String flipperClientState = "";
        Object flipperClient = null;
        Class<?> flipperClientClass = null;
        Class<?> MRNFlipperManagerClass = null;
        try {
            Class<?> androidFlipperClientClass = Class.forName("com.facebook.flipper.android.AndroidFlipperClient");
            flipperClientClass = Class.forName("com.facebook.flipper.android.FlipperClientImpl");
            flipperClient = androidFlipperClientClass.getMethod("getInstance", Context.class).invoke(null, getApplicationContext());
            flipperClient = flipperClientClass.cast(flipperClient);
            MRNFlipperManagerClass = Class.forName("com.meituan.android.flipperplugin.MRNFlipperManager");
            MRNFlipperManagerClass.getMethod("init", Context.class).invoke(null, getApplicationContext());//默认打开
            flipperClientState = (String) flipperClientClass.getMethod("getState").invoke(flipperClient);
        } catch (Throwable th) {
            th.printStackTrace();
        }


        //得到 flipper 是否打开
        String flipperIsOpenStr = getFlipperIsOpen(flipperClientState) ? "关闭" : "打开";
        buttonFlipper.setText(flipperIsOpenStr);
        Object finalFlipperClient = flipperClient;// 用于在内部类中使用
        Class<?> finalFlipperClientClass = flipperClientClass;
        Class<?> finalMRNFlipperManagerClass = MRNFlipperManagerClass;


        buttonFlipper.setOnClickListener(new OnClickListener() {
            @Override
            public void onClick(View v) {
                try {
                    if (finalFlipperClient == null || finalFlipperClientClass == null || finalMRNFlipperManagerClass == null) {
                        MSCLog.e("MSCDebugActivity", "flipperClient, FlipperClientClass or MRNFlipperManager is null");
                        return;
                    }
                    // 获取 isInited 字段
                    Field field = finalMRNFlipperManagerClass.getDeclaredField("isInited");
                    // 由于 isInited 是 private 字段，需要设置为可访问
                    field.setAccessible(true);
                    String flipperClientState = (String) finalFlipperClientClass.getMethod("getState").invoke(finalFlipperClient);
                    if (!getFlipperIsOpen(flipperClientState)) {
                        finalMRNFlipperManagerClass.getMethod("init", Context.class).invoke(null, getApplicationContext());
                        // 获取 isInited 字段的值（它是一个 AtomicBoolean 对象）
                        AtomicBoolean isInitedValue = (AtomicBoolean) field.get(null);
                        if (isInitedValue != null && isInitedValue.get()) {
                            ToastUtils.toast("Flipper打开成功");
                            buttonFlipper.setText("关闭");
                        }
                    } else {
                        // 获取 isInited 字段的值（它是一个 AtomicBoolean 对象）
                        AtomicBoolean isInitedValue = (AtomicBoolean) field.get(null);
                        if (isInitedValue != null) {
                            isInitedValue.set(false);
                        }
                        finalFlipperClientClass.getMethod("stop").invoke(finalFlipperClient);
                        ToastUtils.toast("Flipper关闭成功");
                        buttonFlipper.setText("打开");
                    }
                } catch (Throwable th) {
                    th.printStackTrace();
                }
            }
        });

        Switch showCustomListReuse = findViewById(R.id.cb_show_custom_list_reuse);
        showCustomListReuse.setChecked(PreferenceManager.getDefaultSharedPreferences(MSCEnvHelper.getContext()).getBoolean(DevToolsConstants.MSC_CUSTOM_LIST_SHOW_REUSE, false));
        showCustomListReuse.setOnCheckedChangeListener(new CompoundButton.OnCheckedChangeListener() {
            @Override
            public void onCheckedChanged(CompoundButton buttonView, boolean isChecked) {
                ToastUtils.toast("%s，重进页面后生效", isChecked ? "已开启" : "已关闭");
                PreferenceManager.getDefaultSharedPreferences(MSCEnvHelper.getContext()).edit().putBoolean(DevToolsConstants.MSC_CUSTOM_LIST_SHOW_REUSE, isChecked).apply();
            }
        });

        final EditText et = findViewById(R.id.et_get_version);
        findViewById(R.id.btn_get_version).setOnClickListener(new View.OnClickListener() {
            @Override
            public void onClick(View v) {
                String appId = et.getText().toString();
                String value = MSCAppPropertyUtil.getBizVersion(appId);
                ToastUtils.toast("%s", "" + value);
            }
        });
        final EditText etKey = findViewById(R.id.et_key);
        final EditText etValue = findViewById(R.id.et_value);
        final EditText etAppId = findViewById(R.id.et_appid);

        findViewById(R.id.btn_add_pkg_download_params).setOnClickListener(new OnClickListener() {
            @Override
            public void onClick(View v) {
                String key = etKey.getText().toString();
                String value = etValue.getText().toString();
                String appId = etAppId.getText().toString();
                if (!TextUtils.isEmpty(appId)) {
                    MetaFetchRulerManager.getInstance().addPkgExtraParamPersist(appId, key, value);
                } else {
                    ExtraParamsBean extraParamsBean = new ExtraParamsBean(key, value);
                    List<ExtraParamsBean> extraParamsBeanList = new ArrayList<>();
                    extraParamsBeanList.add(extraParamsBean);
                    MSCPackageHelper.addPkgExtraParams(extraParamsBeanList);
                }
                bundleExtraParams.add(new ExtraParamsBean(key, value));
                ToastUtils.toast("添加自定义参数成功" + bundleExtraParams);
            }
        });

        findViewById(R.id.btn_check_update).setOnClickListener(new OnClickListener() {
            @Override
            public void onClick(View v) {
                MSCPackageHelper.batchCheckUpdate();
                ToastUtils.toast("触发包更新");
            }
        });

        findViewById(R.id.mmp_to_msc_button).setOnClickListener(new OnClickListener() {
            @Override
            public void onClick(View v) {
                Intent intent = new Intent();
                intent.setComponent(new ComponentName(MSCDebugActivity.this, MSCMMPRouterActivity.class));
                startActivity(intent);
            }
        });

        TextView currRuntimeSourceView = findViewById(R.id.current_runtime_source);
        String currRuntimeSource = getRuntimeSource();
        if (currRuntimeSource != null && !currRuntimeSource.isEmpty()) {
            switch (currRuntimeSource) {
                case DevToolsConstants.RUNTIME_SOURCE_NEW:
                    currRuntimeSourceView.setText(String.format(this.getString(R.string.msc_debug_runtime_type), "新建"));
                    break;
                case DevToolsConstants.RUNTIME_SOURCE_KEEPALIVE:
                    currRuntimeSourceView.setText(String.format(this.getString(R.string.msc_debug_runtime_type), "复用保活的"));
                    break;
                case DevToolsConstants.RUNTIME_SOURCE_BASEPRELOAD:
                    currRuntimeSourceView.setText(String.format(this.getString(R.string.msc_debug_runtime_type), "使用基础预热的"));
                    break;
                case DevToolsConstants.RUNTIME_SOURCE_BIZPRELOAD:
                    currRuntimeSourceView.setText(String.format(this.getString(R.string.msc_debug_runtime_type), "使用业务预热的"));
                    break;
                case DevToolsConstants.RUNTIME_SOURCE_COLDSTART:
                    currRuntimeSourceView.setText(String.format(this.getString(R.string.msc_debug_runtime_type), "使用冷启动的"));
                    break;
                default:
                    currRuntimeSourceView.setText("当前小程序的启动来源：" + currRuntimeSource);
                    break;
            }
        }

        String currLaunchScheme = getLaunchScheme();
        findViewById(R.id.copy_launch_scheme).setOnClickListener(new OnClickListener() {
            @Override
            public void onClick(View v) {
                if (currLaunchScheme != null && !currLaunchScheme.isEmpty()) {
                    MtClipboardManager clipboard = Privacy.createClipboardManager(MSCDebugActivity.this, DevToolsConstants.MTCLIPBOARDMANAGER_TOKEN);
                    ClipData clip = ClipData.newPlainText("Launch Scheme", currLaunchScheme);
                    clipboard.setPrimaryClip(clip);
                    if (clipboard.hasPrimaryClip()) {
                        ToastUtils.toast("已复制当前启动链接到剪切板！");
                        //把currLaunchScheme 转为二维码显示一个弹窗
                        showQrCodeDialog(currLaunchScheme);
                    } else {
                        ToastUtils.toast("复制启动链接失败！");
                    }
                } else {
                    ToastUtils.toast("启动链接为空！");
                }
            }
        });
        Button scannerButton = findViewById(R.id.scanner_button);
        scannerButton.setOnClickListener(new OnClickListener() {
            @Override
            public void onClick(View v) {
                CodeCacheDevHelper.cleanAllCodeCache();
                requestPermissionsOrGo(MSCDebugActivity.this, new String[]{Manifest.permission.CAMERA, Manifest.permission.READ_EXTERNAL_STORAGE}, REQUEST_CODE_CAMERA);
            }
        });

        Switch closeKeepAliveEngineCb = findViewById(R.id.close_keepAliveEngine);
        closeKeepAliveEngineCb.setChecked(PreferenceManager.getDefaultSharedPreferences(MSCEnvHelper.getContext()).getBoolean(DevToolsConstants.IS_CLOSE_KEEP_ALIVE_ENGINE, false));
        closeKeepAliveEngineCb.setOnCheckedChangeListener(new CompoundButton.OnCheckedChangeListener() {
            @Override
            public void onCheckedChanged(CompoundButton buttonView, boolean isChecked) {
                if (isChecked) {
                    ToastUtils.toast("关闭保活,重启App后生效");
                } else {
                    ToastUtils.toast("开启保活,重启App后生效");
                }
                PreferenceManager.getDefaultSharedPreferences(MSCEnvHelper.getContext()).edit().putBoolean(DevToolsConstants.IS_CLOSE_KEEP_ALIVE_ENGINE, isChecked).apply();
            }
        });

        // TODO: 2022/5/23 tianbin 注释多进程相关调试功能，暂时没有用到

//        findViewById(R.id.get_process_btn).setOnClickListener(new View.OnClickListener() {
//            @Override
//            public void onClick(View v) {
//                refreshData();
//            }
//        });

//        /**
//         * 增加多进程CheckBox，用于调试界面
//         */
//        debugMultiProcess = findViewById(R.id.debug_mp_btn);
//        debugMultiProcess.setChecked(DebugHelper.getForceMultiProcessMode() == DebugHelper.FORCE_MODE);
//        debugMultiProcess.setOnCheckedChangeListener(new CompoundButton.OnCheckedChangeListener() {
//            @Override
//            public void onCheckedChanged(CompoundButton buttonView, boolean isChecked) {
//                if (isChecked) {
//                    DebugManager.setMultiProcessMode(DebugHelper.FORCE_MODE);
//                    ToastUtils.toastIfDebug("强制开启多进程");
//                } else {
//                    DebugManager.setMultiProcessMode(DebugHelper.FORBIDDEN_MODE);
//                    ToastUtils.toastIfDebug("强制关闭多进程");
//                }
//            }
//        });

//        // 设置多进程模式为默认模式
//        findViewById(R.id.clear_multiProcess_status).setOnClickListener(new View.OnClickListener() {
//            @Override
//            public void onClick(View v) {
//                DebugManager.setMultiProcessMode(DebugHelper.DEFAULT_MODE);
//                ToastUtils.toastIfDebug("采用在线配置");
//                debugMultiProcess.setChecked(false);
//            }
//        });

        ListView listView = findViewById(R.id.list_view);
        listView.setAdapter(mAdapter);

        ProcessMonitor.registerStaticListenerForAllSubProcess(processDieListener);
        warnWithoutAop();
    }

    private static boolean modifyWidgetDebug(SharedPreferences.Editor edit, Button buttonWidgetDebug, boolean widgetDebug) {
        String widgetDebugStr = widgetDebug ? "关闭" : "打开";
        edit.putBoolean(DevToolsConstants.PARAMS_DEBUG_IS_WIDGET_DEBUG, !widgetDebug);
        edit.apply();
        ToastUtils.toast("Widget调试已经" + widgetDebugStr);
        String buttonWidgetDebugStr = widgetDebug ? "打开" : "关闭";
        buttonWidgetDebug.setText(buttonWidgetDebugStr);
        return !widgetDebug;
    }

    private static boolean getFlipperIsOpen(String flipperClientState) {
        if (TextUtils.isEmpty(flipperClientState)) {
            return false;
        }
        String[] split = flipperClientState.split("\n");
        if (split.length == 0) {
            return false;
        }
        String state = split[split.length-1];
        boolean isFlipperOpen = !state.equals("[Success] Stop client");
        return isFlipperOpen;
    }

    private void showQrCodeDialog(String launchScheme) {
        Dialog dialog = new Dialog(this);
        dialog.setTitle("启动链接的二维码");
        LinearLayout layout = new LinearLayout(this);
        layout.setPadding(10, 10, 10, 10);
        layout.setBackgroundColor(Color.WHITE);
        layout.setOrientation(LinearLayout.VERTICAL);
        ImageView imageView = new ImageView(this);
        createQrCodeAsync(launchScheme , imageView);
        layout.addView(imageView);
        TextView textView = new TextView(this);
        textView.setText("启动链接已复制到剪切板");
        layout.addView(textView);
//        增加关闭按钮
        Button closeButton = new Button(this);
        closeButton.setText("关闭");
        // 设置按钮的布局参数使其居中
        LinearLayout.LayoutParams buttonParams = new LinearLayout.LayoutParams(
                LinearLayout.LayoutParams.WRAP_CONTENT,
                LinearLayout.LayoutParams.WRAP_CONTENT);
        buttonParams.gravity = Gravity.CENTER_HORIZONTAL;  // 设置水平居中
        closeButton.setLayoutParams(buttonParams);
        closeButton.setOnClickListener(new View.OnClickListener() {
            @Override
            public void onClick(View v) {
                dialog.dismiss();
            }
        });
        layout.addView(closeButton);
        dialog.setContentView(layout);
        dialog.show();
    }

    private void createQrCodeAsync(String launchScheme, ImageView imageView) {
        MSCExecutors.submit(() -> {
            try {
                Bitmap bitmap = ZXingUtils.createBarCode(launchScheme, BarcodeFormat.QR_CODE, 600, 600);
                MSCExecutors.runOnUiThread(() -> imageView.setImageBitmap(bitmap));
            } catch (Exception e) {
                MSCExecutors.runOnUiThread(() -> ToastUtils.toast("生成二维码失败"));
            }
        });
    }

    public static void requestPermissionsOrGo(Activity activity, String[] permissions, int requestCode) {
        boolean hasAllPermission = true;
        int[] grantResults = new int[permissions.length];
        for (int i = 0; i < permissions.length; i++) {
            String permission = permissions[i];
            if (ContextCompat.checkSelfPermission(activity, permission) != PackageManager.PERMISSION_GRANTED) {
                hasAllPermission = false;
                break;
            } else {
                grantResults[i] = PackageManager.PERMISSION_GRANTED;
            }
        }

        if (hasAllPermission) {
            activity.onRequestPermissionsResult(requestCode, permissions, grantResults);
        } else {
            ActivityCompat.requestPermissions(activity, permissions, requestCode);
        }
    }

    private void showBaseLayout() {
        baseLayout.setVisibility(View.VISIBLE);
        performanceLayout.setVisibility(View.GONE);
        renderLayout.setVisibility(View.GONE);
        otherLayout.setVisibility(View.GONE);
    }

    private void showPerformanceLayout() {
        baseLayout.setVisibility(View.GONE);
        performanceLayout.setVisibility(View.VISIBLE);
        renderLayout.setVisibility(View.GONE);
        otherLayout.setVisibility(View.GONE);
    }

    private void showRenderLayout() {
        baseLayout.setVisibility(View.GONE);
        performanceLayout.setVisibility(View.GONE);
        renderLayout.setVisibility(View.VISIBLE);
        otherLayout.setVisibility(View.GONE);
    }

    private void showOtherLayout() {
        baseLayout.setVisibility(View.GONE);
        performanceLayout.setVisibility(View.GONE);
        renderLayout.setVisibility(View.GONE);
        otherLayout.setVisibility(View.VISIBLE);
    }

    private void resetTextColors() {
        baseOption.setTextColor(Color.BLACK);
        performanceOption.setTextColor(Color.BLACK);
        renderOption.setTextColor(Color.BLACK);
        otherOption.setTextColor(Color.BLACK);
        baseOption_li.setVisibility(View.GONE);
        performanceOption_li.setVisibility(View.GONE);
        renderOption_li.setVisibility(View.GONE);
        otherOption_li.setVisibility(View.GONE);
    }

    private String getLaunchScheme() {
        return getIntent().getStringExtra(Constants.LAUNCH_SCHEME);
    }

    private String getPackageInfo() {
        return getIntent().getStringExtra(Constants.PACKAGE_INFO);
    }

    private String getCheckUpdateUrl() {
        return getIntent().getStringExtra(Constants.CHECK_UPDATE_URL);
    }

    private String getRuntimeSource() {
        return getIntent().getStringExtra(CommonTags.TAG_RUNTIME_SOURCE);
    }

    @Override
    protected void onRestart() {
        super.onRestart();
//        debugMultiProcess.setChecked(DebugHelper.getForceMultiProcessMode() == DebugHelper.FORCE_MODE);
    }


    @Override
    protected void onResume() {
        super.onResume();
        refreshData();
    }

    @Override
    protected void onActivityResult(int requestCode, int resultCode, Intent data) {
        super.onActivityResult(requestCode, resultCode, data);
        if (requestCode == REQUEST_CODE_IDE_ATTACH && resultCode == Activity.RESULT_OK) {
            String resultUrl = data != null ? data.getStringExtra("result_url") : null;
            if (!TextUtils.isEmpty(resultUrl)) {
                Uri uri = Uri.parse(resultUrl);
                String debugSessionId = uri.getQueryParameter("debugSessionId");
                if (TextUtils.isEmpty(debugSessionId)) {
                    ToastUtils.toast("未获取到debug session id");
                    return;
                }
                MSCRuntime runtime = getCurrentRuntime();
                if (runtime == null) {
                    ToastUtils.toast("MSCRuntime null");
                    return;
                }
                MSCLog.i(IDELogUtil.keyTag(TAG), "IDE url:" + uri.toString() + ",runtime:" + runtime.getAppId());
                final String pikeEnv = uri.getQueryParameter(DevToolsConstants.PARAMS_DEBUG_PIKE_ENV);
                IContainerManager containerManager = runtime.getContainerManagerModule();
                if (containerManager != null && containerManager.getTopContainer() instanceof ContainerController) {
                    ContainerController controller = (ContainerController) containerManager.getTopContainer();
                    Intent intent = controller.getIntent();
                    intent.putExtra(DevToolsConstants.PARAMS_DEBUG_SESSION_ID, debugSessionId);
                    intent.putExtra(DevToolsConstants.PARAMS_DEBUG_MSC_VERSION, runtime.getMSCAppModule().getBasePkgVersion());
                    intent.putExtra(DevToolsConstants.PARAMS_DEBUG_RUN_MODE, DevToolsConstants.RUN_MODE_LIVE_DEBUG);
                    intent.putExtra(DevToolsConstants.PARAMS_DEBUG_PIKE_ENV, !TextUtils.isEmpty(pikeEnv) ? pikeEnv : DevToolsConstants.MSC_PIKE_ENV_PROD);
                    intent.putExtra(DevToolsConstants.PARAMS_SOURCE_MAP, uri.getBooleanQueryParameter(DevToolsConstants.PARAMS_SOURCE_MAP, false));
                    intent.putExtra(DevToolsConstants.PARAMS_DEBUG_IDE_DEBUG_TYPE, DevToolsConstants.MSC_IDE_DEBUG_TYPE_ATTACH);
                    MSCDebuggerManager.startDebug((ContainerController) runtime.getContainerManagerModule().getTopContainer());
                }
                finish();
            }
        }
        else if (requestCode == REQUEST_CODE_CAMERA && resultCode == Activity.RESULT_OK) {

            String resultUrl = data != null ? data.getStringExtra("result_url") : null;
            if (resultUrl != null) {
                if(resultUrl.contains("appmock.sankuai.com")){
                    NVAppMockManager.instance().registerMock(resultUrl, new NVAppMockManager.RegisterCallback() {
                        @Override
                        public void success() {
                            CIPStorageCenter cipStorageCenter = CIPStorageCenter.instance(MSCEnvHelper.getContext(), MSCEnvHelper.getContext().getPackageName() + "_cipstoragecenter");
                            NVAppMockManager.instance().mock(true);
                            cipStorageCenter.setBoolean(MockInterceptor.MOCK_ENABLE_KEY, true);
                            cipStorageCenter.setString(MockInterceptor.MOCK_URL, resultUrl);
                            ToastUtils.toast("appmock成功!");
                        }

                        @Override
                        public void failed(String message) {
                            ToastUtils.toast("appmock失败，提示信息：" + message);
                            NVAppMockManager.instance().mock(false);
                        }
                    });
                    return;
                }
                MSCEnvHelper.getDefaultSharedPreferences().edit()
                        .putString(SP_LAST_SCAN_URL, resultUrl)
                        .apply();
                startActivityFromScanResult(resultUrl);
            }
        }

    }

    private void startActivityFromScanResult(String resultUrl) {
        MSCLog.d(TAG , "startActivityFromScanResult:" + resultUrl);
        Intent intent = new Intent();
        Uri parsedUrl = Uri.parse(resultUrl);
        intent.setData(parsedUrl.buildUpon().appendQueryParameter("reload", "true").build());
        intent.setPackage(getPackageName());
        try {
            startActivity(intent);
        } catch (Exception e) {
            ToastUtils.toast("非法的url: " + resultUrl);
        }
    }

    private void warnWithoutAop() {
        SnackbarBuilder.builder(this, "当前App没有接入bytecode-manipulator插件或者没有配置msc的aop，一些调试相关功能会受到影响", ToastUtils.LENGTH_LONG)
                .setGravity(Gravity.BOTTOM | Gravity.CENTER)
                .showType(SnackbarBuilder.TYPE_SNACK_BAR)
                .show();
    }

    @Override
    protected void onDestroy() {
        super.onDestroy();
        ProcessMonitor.unregisterStaticListenerForAllSubProcess(processDieListener);
    }

    private void refreshData() {
        mItemList.clear();
//        mItemList.add(new TitleBean("保活状态中的引擎", "回收全部保活引擎", new View.OnClickListener() {
//            @Override
//            public void onClick(View v) {
//                GlobalEngineMonitor.getInstance().destroyAllKeepAliveEngine();
//                doubleRefresh();
//            }
//        }));

//        mItemList.add(new TitleBean("保活状态中的引擎"));
//        mItemList.addAll(getKeepAliveEngines());
        mItemList.add(new TitleBean("引擎（不含reload状态的）"));
        mItemList.addAll(getRunningEngines());
        mItemList.add(new TitleBean("进程"));
        mItemList.addAll(getProcessBeanList());
        mAdapter.notifyDataSetChanged();
    }

    private void doubleRefresh() {
        MSCExecutors.runOnUiThread(new Runnable() {
            @Override
            public void run() {
                refreshData();
            }
        });

        MSCExecutors.runOnUiThreadDelayed(new Runnable() {
            @Override
            public void run() {
                refreshData();
            }
        }, 1000);
    }

    /*
     * 自带ActionBar 返回点击事件
     */
    @Override
    public boolean onOptionsItemSelected(@NonNull MenuItem item) {
        if (item.getItemId() == android.R.id.home) {
            finish();
            return true;
        }
        return super.onOptionsItemSelected(item);
    }

    private List<EngineBean> getRunningEngines() {
        List<EngineBean> result = new ArrayList<>();
        for (MSCRuntime runtime : RuntimeManager.getAllRunningEngines().values()) {
            result.add(new EngineBean(runtime));
        }
        return result;
    }

    private List<EngineBean> getKeepAliveEngines() {
        List<EngineBean> result = new ArrayList<>();
        for (GlobalEngineMonitor.AppEngineRecord record : GlobalEngineMonitor.getInstance().getAppEngineRecords()) {
            if (record.isKeepAlive) {
                result.add(new EngineBean(record));
            }
        }
        return result;
    }

    private List<ProcessBean> getProcessBeanList() {
        ActivityManager am = ((ActivityManager) getApplicationContext().getSystemService(Context.ACTIVITY_SERVICE));
        List<ActivityManager.RunningAppProcessInfo> infoList = am.getRunningAppProcesses();
        Map<MSCProcess, MSCProcess.ProcessState> stateMap = MSCProcess.checkAllProcessStates();

        List<ProcessBean> lst = new ArrayList<>();
        for (ActivityManager.RunningAppProcessInfo info : infoList) {
            MSCProcess.ProcessState state = null;
            MSCProcess process = MSCProcess.getProcessByName(info.processName);
            if (process != null) {
                state = stateMap.get(process);
            }
            lst.add(new ProcessBean(info.processName, info.pid, state));
        }
        return lst;
    }

    private void killProcess(String itemName, final int pid) {
        MSCExecutors.Serialized.submit(new Runnable() {
            @Override
            public void run() {
                Runtime runtime = Runtime.getRuntime();
                try {
                    String command = "kill -9 " + pid + "\n";
                    //运行外部程序
                    Process proc = runtime.exec(command);
                    //阻塞当前线程，并等待外部程序中止后获取结果码,(结果码0:正常终止)
                    if (proc.waitFor() == 0) {
                        ToastUtils.toast("Process已经终止，进程id : " + pid);

                        doubleRefresh();
                    }
                } catch (Exception e) {
                    e.printStackTrace();
                }
            }
        });
    }

    abstract static class Bean {
        private static final int TYPE_TITLE = 0;
        private static final int TYPE_ENGINE = 1;
        private static final int TYPE_PROCESS = 2;

        abstract int getType();
    }

    static class TitleBean extends Bean {

        String title;
        String buttonText;
        View.OnClickListener onClickListener;

        TitleBean(String title) {
            this.title = title;
        }

        TitleBean(String title, String buttonText, View.OnClickListener onClickListener) {
            this.title = title;
            this.buttonText = buttonText;
            this.onClickListener = onClickListener;
        }

        int getType() {
            return Bean.TYPE_TITLE;
        }
    }

    static class EngineBean extends Bean {

        GlobalEngineMonitor.AppEngineRecord record;

        EngineBean(GlobalEngineMonitor.AppEngineRecord record) {
            this.record = record;
        }

        public EngineBean(MSCRuntime runtime) {
            this.record = new GlobalEngineMonitor.AppEngineRecord(runtime);
        }

        int getType() {
            return Bean.TYPE_ENGINE;
        }
    }

    static class ProcessBean extends Bean {
        String processName;
        int pid;
        MSCProcess.ProcessState state;

        ProcessBean(String processName, int pid, MSCProcess.ProcessState state) {
            this.processName = processName;
            this.pid = pid;
            this.state = state;
        }

        int getType() {
            return Bean.TYPE_PROCESS;
        }
    }

    /*
     * 父listView的适配器
     */
    class MainAdapter extends BaseAdapter {

        @Override
        public int getCount() {
            return mItemList.size();
        }

        @Override
        public Object getItem(int position) {
            return null;
        }

        @Override
        public long getItemId(int position) {
            return 0;
        }

        @Override
        public int getItemViewType(int position) {
            //0没有button，1有button
            Bean bean = mItemList.get(position);
            return bean.getType();
        }

        @Override
        public int getViewTypeCount() {
            return 3;
        }

        @Override
        public View getView(int position, View convertView, ViewGroup parent) {
            ViewHolder holder = new ViewHolder();
            Bean bean = mItemList.get(position);

            if (convertView == null) {
                convertView = LayoutInflater.from(getBaseContext()).inflate(R.layout.msc_debugview_listview_item, null);
                holder.title = convertView.findViewById(R.id.item_title_view);
                holder.summary = convertView.findViewById(R.id.item_summary_view);
                holder.button = convertView.findViewById(R.id.item_button_1);
                holder.button2 = convertView.findViewById(R.id.item_button_2);
                convertView.setTag(holder);
            } else {
                holder = (ViewHolder) convertView.getTag();
            }

            updateView(bean, holder);
            return convertView;
        }

        private void updateView(Bean bean, ViewHolder holder) {
            switch (bean.getType()) {
                case Bean.TYPE_TITLE:
                    TitleBean titleBean = (TitleBean) bean;
                    holder.title.setText(titleBean.title);
                    if (titleBean.onClickListener != null) {
                        holder.button.setOnClickListener(titleBean.onClickListener);
                        holder.button.setText(titleBean.buttonText);
                        holder.button.setVisibility(View.VISIBLE);
                    } else {
                        holder.button.setVisibility(View.GONE);
                    }
                    holder.button2.setVisibility(View.GONE);
                    holder.summary.setVisibility(View.GONE);
                    break;

                case Bean.TYPE_ENGINE:
                    final EngineBean engineBean = (EngineBean) bean;
                    holder.title.setText(engineBean.record.appId);
                    String processName = engineBean.record.process.getProcessName();
                    processName = processName.replace(MSCDebugActivity.this.getPackageName(), "");
                    if (processName.isEmpty()) {
                        processName = "主进程";
                    } else {
                        processName = "进程 " + processName;
                    }
                    String summary = processName;
                    if (engineBean.record.isKeepAlive) {
                        summary += "\n引擎保活";
                    }
                    holder.summary.setText(summary);

                    if (engineBean.record.isKeepAlive) {
                        holder.button.setOnClickListener(new View.OnClickListener() {
                            @Override
                            public void onClick(View v) {
                                // 注意，将视为非正常的引擎销毁，不会触发再次预加载等正常销毁后续逻辑，正常销毁实现较麻烦，暂不实现
                                GlobalEngineMonitor.getInstance().destroyCachedRuntime(engineBean.record.appId);
                                doubleRefresh();
                            }
                        });
                    } else {
                        holder.button.setText("销毁预热");
                        holder.button.setOnClickListener(new View.OnClickListener() {
                            @Override
                            public void onClick(View v) {
                                // 注意，将视为非正常的引擎销毁，不会触发再次预加载等正常销毁后续逻辑，正常销毁实现较麻烦，暂不实现
                                PreloadTasksManager.instance.cleanPreloadApp(engineBean.record.appId, null);
                                doubleRefresh();
                            }
                        });
                    }
                    holder.button2.setVisibility(View.GONE);
                    break;

                case Bean.TYPE_PROCESS:
                    final ProcessBean processBean = (ProcessBean) bean;
                    holder.title.setText(processBean.processName);

                    StringBuilder sb = new StringBuilder("pid: " + processBean.pid);
                    if (processBean.state != null) {
                        sb.append("\nState: " + processBean.state.name());
                    }
                    if (MSCProcess.MAIN == MSCProcess.getProcessByName(processBean.processName)) {
                        sb.append("\n主进程");
                    }
                    if (TextUtils.equals(MSCProcess.getCurrentProcessName(), processBean.processName)) {
                        sb.append("\n当前进程");
                    }
                    holder.summary.setText(sb);

                    holder.button.setText("杀死进程");
                    holder.button.setOnClickListener(new View.OnClickListener() {
                        @Override
                        public void onClick(View v) {
                            killProcess(processBean.processName, processBean.pid);
                            doubleRefresh();
                        }
                    });

                    holder.button2.setVisibility(View.VISIBLE);
                    holder.button2.setText(R.string.mmp_debug_crash_web_view);
                    holder.button2.setOnClickListener(new View.OnClickListener() {
                        @Override
                        public void onClick(View view) {
                            MSCProcess process = MSCProcess.getProcessByName(processBean.processName);
                            if (process == null) {
                                ToastUtils.toastIfDebug("Process is null");
                                return;
                            }
                            mockWebViewCrash();

                            // MSC未启用多进程，该流程不通
//                            new CrashWebViewTask().execute(process);
                        }
                    });
                    break;
            }
        }

        private class ViewHolder {
            TextView title;
            TextView summary;
            Button button;
            Button button2;
        }
    }

    private void mockWebViewCrash() {
        MSCExecutors.runOnUiThread(new Runnable() {
            @Override
            public void run() {
                Collection<MSCRuntime> runtimes = RuntimeManager.getAllRunningEngines().values();
                if (runtimes.isEmpty()) {
                    ToastUtils.toastIfDebug("no running app in target process, cannot mock a crash");
                } else {
                    try {
                        for (MSCRuntime runtime : runtimes) {
                            WebViewCacheManager webViewCacheManager = runtime.webViewCacheManager;
                            if (!webViewCacheManager.isFirstWebViewCreated()) {
                                continue;
                            }
                            webViewCacheManager.getWebViewThroughCache(MSCEnvHelper.getContext(), runtime, "").loadUrl("chrome://crash");
                            ToastUtils.toastIfDebug("WebView will crash");
                        }
                    } catch (Exception e) {
                        ToastUtils.toastIfDebug(e.toString());
                    }
                }
            }
        });
    }

    public Activity getActivity() {
        return this;
    }

//    public static class CrashWebViewTask extends IPCAsyncTask<Void, Void> {
//
//        @Override
//        public Void doOnRemote(Void... voids) throws Exception {
//            mockWebViewCrash();
//            return null;
//        }
//    }
}
