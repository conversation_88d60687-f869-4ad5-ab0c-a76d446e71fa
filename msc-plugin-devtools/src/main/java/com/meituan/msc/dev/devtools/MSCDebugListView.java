package com.meituan.msc.dev.devtools;

import android.content.Context;
import android.util.AttributeSet;
import android.widget.ListView;

public class MSCDebugListView extends ListView {
    public MSCDebugListView(Context context) {
        super(context);
    }

    public MSCDebugListView(Context context, AttributeSet attrs) {
        super(context, attrs);
    }

    public MSCDebugListView(Context context, AttributeSet attrs, int defStyleAttr) {
        super(context, attrs, defStyleAttr);
    }

    @Override
    protected void onMeasure(int widthMeasureSpec, int heightMeasureSpec) {
        int customHeightSpec =

                MeasureSpec.makeMeasureSpec(Integer.MAX_VALUE >> 2,MeasureSpec.AT_MOST);
        super.onMeasure(widthMeasureSpec, customHeightSpec);
    }
}