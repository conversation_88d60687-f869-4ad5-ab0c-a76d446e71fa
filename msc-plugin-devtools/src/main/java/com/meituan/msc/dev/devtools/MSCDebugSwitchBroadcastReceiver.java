package com.meituan.msc.dev.devtools;

import android.content.BroadcastReceiver;
import android.content.Context;
import android.content.Intent;
import android.preference.PreferenceManager;

import com.meituan.msc.modules.devtools.DebugHelper;
import com.meituan.msc.modules.reporter.MSCLog;

/**
 * <AUTHOR>
 * @since 2021/8/20.
 */

public class MSCDebugSwitchBroadcastReceiver extends BroadcastReceiver {
    private static final String MSC_DEBUG_SWITCH_ACTION = "com.meituan.msc.DEBUG_SWITCH";
    @Override
    public void onReceive(Context context, Intent intent) {
        if (MSC_DEBUG_SWITCH_ACTION.equals(intent.getAction())) {
            PreferenceManager.getDefaultSharedPreferences(context).edit().putBoolean(DebugManager.MSC_DEBUG, true).apply();
            DebugHelper.setDebug(true);

            boolean enablePreload = intent.getBooleanExtra("enablePreload", true);
//            PreferenceManager.getDefaultSharedPreferences(context).edit().putBoolean(DebugManager.MMP_DEBUG_ENABLE_PRELOAD, enablePreload).apply();
//            DebugHelper.enablePreload = enablePreload;

            MSCLog.d("mmp debug switch is enabled by broadcast, preload enabled: ", enablePreload);
        }
    }
}
