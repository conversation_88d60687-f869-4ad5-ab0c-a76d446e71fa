package com.meituan.msc.dev.devtools;

import android.app.Activity;
import android.app.Dialog;
import android.content.Context;
import android.graphics.Color;
import android.os.Bundle;
import android.support.annotation.Nullable;
import android.text.TextUtils;
import android.view.LayoutInflater;
import android.view.View;
import android.view.ViewGroup;
import android.widget.BaseAdapter;
import android.widget.Button;
import android.widget.CompoundButton;
import android.widget.EditText;
import android.widget.LinearLayout;
import android.widget.ListView;
import android.widget.Switch;
import android.widget.TextView;
import android.widget.Toast;

import com.meituan.android.cipstorage.CIPStorageCenter;
import com.meituan.msc.common.utils.CIPStorageFileUtil;
import com.meituan.msc.dev.R;
import com.meituan.msc.dev.utils.AppContextGetter;
import com.meituan.msc.modules.reporter.MSCLog;
import com.meituan.msc.modules.router.MMPRouterManager;

import java.text.Collator;
import java.util.ArrayList;
import java.util.Collections;
import java.util.Comparator;
import java.util.HashMap;
import java.util.HashSet;
import java.util.List;
import java.util.Locale;
import java.util.Map;
import java.util.Set;

public class MSCMMPRouterActivity extends Activity {
    // 代表当前有效的mmp->msc映射关系，支持一键切msc的最新数据
    private Map<String, DebugManager.RouteDebugConfig> config;
    private RouterAdapter mRouterAdapter;
    private static final String TAG = "MSCMMPRouterActivity";

    @Override
    protected void onCreate(@Nullable Bundle savedInstanceState) {
        super.onCreate(savedInstanceState);
        setContentView(R.layout.msc_debugview_router_activity);
        initListView();
        // 支持自定义添加小程序MMP到MSC映射关系
        initCustomAdd();
        findViewById(R.id.img_back).setOnClickListener(new View.OnClickListener() {
            @Override
            public void onClick(View v) {
                finish();
            }
        });
    }

    private void initCustomAdd() {
        Button btnCustomAdd = findViewById(R.id.btn_custom_add);
        View.OnClickListener customAddClickListener = v -> {
            Dialog dialog = new Dialog(MSCMMPRouterActivity.this);
            dialog.setContentView(R.layout.msc_dialog_add_custom_router);
            dialog.show();
            dialog.findViewById(R.id.btn_confirm).setOnClickListener(v1 -> confirmAddRouter(dialog));
            dialog.findViewById(R.id.btn_cancel).setOnClickListener(v2 -> dialog.dismiss());
        };
        refreshCustomAddButton(btnCustomAdd, customAddClickListener);

        // MSC迁移调试面板, 支持用户配置总开关, 默认开启  https://km.sankuai.com/collabpage/2704017479
        Switch customAddSwitch = findViewById(R.id.custom_config_switch);
        customAddSwitch.setChecked(DebugManager.enableCustomConfigMMPToMSC());
        // customAddSwitch 当被点击关闭时, 将开启的配置回退到horn开关下发的映射关系
        customAddSwitch.setOnCheckedChangeListener((buttonView, isChecked) -> {
            CIPStorageCenter cipStorageCenter = CIPStorageFileUtil.getCIPStorageCenter(AppContextGetter.getContext());
            cipStorageCenter.setBoolean(DebugManager.MSC_DEBUG_ENABLE_CUSTOM_CONFIG_MMP2MSC, isChecked);
            refreshCustomAddButton(btnCustomAdd, customAddClickListener);
            // 刷新各个item的状态
            mRouterAdapter.setData(getRouterData(!isChecked));
        });
    }

    private void refreshCustomAddButton(Button btnCustomAdd, View.OnClickListener customAddClickListener) {
        boolean lastChecked = DebugManager.enableCustomConfigMMPToMSC();
        if (!lastChecked) {
            btnCustomAdd.setBackgroundColor(Color.DKGRAY);
            btnCustomAdd.setOnClickListener(null);
        } else {
            btnCustomAdd.setBackgroundColor(Color.parseColor("#ffbb33"));
            btnCustomAdd.setOnClickListener(customAddClickListener);
        }
    }

    private void confirmAddRouter(Dialog dialog) {
        String mmpName = ((EditText) dialog.findViewById(R.id.et_mmp_name)).getText().toString();
        String mmpAppId = ((EditText) dialog.findViewById(R.id.et_mmp_app_id)).getText().toString();
        String mscAppId = ((EditText) dialog.findViewById(R.id.et_msc_app_id)).getText().toString();
        if (TextUtils.isEmpty(mmpName) || TextUtils.isEmpty(mmpAppId) || TextUtils.isEmpty(mscAppId)) {
            Toast.makeText(MSCMMPRouterActivity.this, "请检查必填项不能为空", Toast.LENGTH_SHORT).show();
            return;
        }
        String appLifeCycle = ((EditText) dialog.findViewById(R.id.et_app_life_cycle)).getText().toString();
        if (!appLifeCycle.isEmpty() && !appLifeCycle.equals("0") && !appLifeCycle.equals("1")) {
            Toast.makeText(MSCMMPRouterActivity.this, "appLifeCycle请填写0或1或不填", Toast.LENGTH_SHORT).show();
            return;
        }
        Map<String, String> mmpNameToAppIdMap = DebugManager.getMMPNameToAppIdMap();
        if (mmpNameToAppIdMap.containsKey(mmpName)) {
            Toast.makeText(MSCMMPRouterActivity.this, "小程序名称重复,请检查是否重复添加", Toast.LENGTH_SHORT).show();
            return;
        }
        if (config.containsKey(mmpAppId)) {
            Toast.makeText(MSCMMPRouterActivity.this, "mmpAppId重复,请检查是否重复添加", Toast.LENGTH_SHORT).show();
            return;
        }
        // 自定义配置的mmpAppName仅新增，不支持删除，可以通过清理缓存或卸载安装恢复内置的配置
        DebugManager.addMMPNameToAppIdMap(mmpName, mmpAppId);
        DebugManager.addMMPToMSCAppIdMap(mmpAppId, mscAppId);
        config.put(mmpAppId, new DebugManager.RouteDebugConfig(mmpName, mscAppId, appLifeCycle.equals("1")));
        DebugManager.enableMMPToMSCApp(mmpAppId);
        // 持久化存储信息并展示在列表中
        DebugManager.setMMPRouteConfigToString(config);
        mRouterAdapter.setData(getRouterData(false));
        dialog.dismiss();
    }

    private void initListView() {
        ListView listView = findViewById(R.id.router_list);
        mRouterAdapter = new RouterAdapter(this, getRouterData(false));
        listView.setAdapter(mRouterAdapter);
    }

    public Map<String, Boolean> getRouterData(boolean reset) {
        Set<String> mmpAppIdSet;
        CIPStorageCenter cipStorageCenter = CIPStorageFileUtil.getCIPStorageCenter(AppContextGetter.getContext());
        MSCLog.d(TAG, "DebugManager getRouterData reset:" + reset);
        if (reset) {
            config = DebugManager.convertRouteConfig(MMPRouterManager.getConfig());
            DebugManager.setMMPRouteConfigToString(config);
            mmpAppIdSet = new HashSet<>(config.keySet());
            cipStorageCenter.setStringSet(DebugManager.MSC_DEBUG_ENABLE_MMP_TO_MSC_SET, mmpAppIdSet);
        } else {
            cipStorageCenter.setBoolean(DebugManager.MSC_DEBUG_ENABLE_MMP_TO_MSC_SET_INIT, true);
            config = DebugManager.getMMPRouteConfigFromSP();
            mmpAppIdSet = DebugManager.getEnableMMPToMSCApps();
            if (mmpAppIdSet.isEmpty()) {
                mmpAppIdSet = new HashSet<>(config.keySet());
                cipStorageCenter.setStringSet(DebugManager.MSC_DEBUG_ENABLE_MMP_TO_MSC_SET, mmpAppIdSet);
            }
        }
        Map<String, Boolean> mmpToMSCMap = new HashMap<>();
        Map<String, String> mmpNameToAppIdMap = DebugManager.getMMPNameToAppIdMap();
        MSCLog.d(TAG, "DebugManager mmpNameToAppIdMap size: " + mmpNameToAppIdMap.size() + ", mmpNameToAppIdMap: " + mmpNameToAppIdMap);
        MSCLog.d(TAG, "DebugManager mmpAppIdSet size: " + mmpAppIdSet.size() + ", mmpAppIdSet: " + mmpAppIdSet);
        for (Map.Entry<String, String> entry : mmpNameToAppIdMap.entrySet()) {
            mmpToMSCMap.put(entry.getKey(), mmpAppIdSet.contains(entry.getValue()));
        }
        return mmpToMSCMap;
    }

    private class RouterAdapter extends BaseAdapter {
        private final LayoutInflater inflater;
        private Map<String, Boolean> data;
        private final List<String> nameList;

        public RouterAdapter(Context context, Map<String, Boolean> map) {
            inflater = LayoutInflater.from(context);
            data = map;
            MSCLog.d(TAG, "DebugManager RouterAdapter data.size:" + data.size() + ", data: " + data);
            nameList = new ArrayList<>(data.keySet());
            sortNameList();
        }

        public void setData(Map<String, Boolean> map) {
            data = map;
            MSCLog.d(TAG, "DebugManager setData data.size: " + data.size() + ", data: " + data);
            nameList.clear();
            nameList.addAll(data.keySet());
            sortNameList();
            notifyDataSetChanged();
        }

        private void sortNameList() {
            // 创建中文排序器
            Collator collator = Collator.getInstance(Locale.CHINA);
            // 排序实现
            Collections.sort(nameList, new Comparator<String>() {
                @Override
                public int compare(String s1, String s2) {
                    return collator.compare(s1, s2);
                }
            });
        }

        @Override
        public int getCount() {
            return nameList.size();
        }

        @Override
        public Object getItem(int position) {
            return nameList.get(position);
        }

        @Override
        public long getItemId(int position) {
            return position;
        }

        @Override
        public View getView(int position, View convertView, ViewGroup parent) {
            LinearLayout linearLayout;
            Switch checkBox;
            TextView textView;
            if (convertView == null) {
                convertView = inflater.inflate(R.layout.msc_debugview_router_item, parent, false);
                linearLayout = convertView.findViewById(R.id.router_item_ll);
                convertView.setTag(linearLayout);
            } else {
                linearLayout = (LinearLayout) convertView.getTag();
            }
            checkBox = linearLayout.findViewById(R.id.router_item_switch);
            textView = linearLayout.findViewById(R.id.router_item_tv);
            String name = nameList.get(position);
            Map<String, String> mmpNameToAppIdMap = DebugManager.getMMPNameToAppIdMap();
            Map<String, String> mmpToMSCAppIdMap = DebugManager.getMMPAppIdToMSCAppIdMap();
            String mmpAppId = mmpNameToAppIdMap.get(name);
            String mscAppId = mmpToMSCAppIdMap.get(mmpAppId);
            String displayText = String.format("%s(%s)", name, mmpAppId);
            // MSCLog.d(TAG, "DebugManager mmpAppId: " + displayText + " => mscAppId: " + mscAppId);
            textView.setText(name);
            checkBox.setChecked(Boolean.TRUE.equals(data.get(name)));
            CompoundButton.OnCheckedChangeListener buttonCheckListener = (buttonView, isChecked) -> {
                if (isChecked) {
                    config.put(mmpAppId, new DebugManager.RouteDebugConfig(name, mscAppId, false));
                    DebugManager.setMMPRouteConfigToString(config);
                    DebugManager.enableMMPToMSCApp(mmpAppId);
                } else {
                    config.remove(mmpAppId);
                    DebugManager.setMMPRouteConfigToString(config);
                    DebugManager.disableMMPToMSCApp(mmpAppId);
                }
            };
            boolean enable = DebugManager.enableCustomConfigMMPToMSC();
            checkBox.setOnCheckedChangeListener(enable ? buttonCheckListener : null);
            checkBox.setClickable(enable);
            return convertView;
        }

    }

}
