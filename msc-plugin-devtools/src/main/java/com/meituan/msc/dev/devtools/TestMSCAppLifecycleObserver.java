package com.meituan.msc.dev.devtools;

import com.meituan.msc.modules.api.appLifecycle.MSCAppLifecycle;
import com.meituan.msc.modules.api.appLifecycle.MSCAppLifecycleObserver;
import com.meituan.msc.modules.api.appLifecycle.MSCAppLifecycleParams;
import com.meituan.msc.modules.reporter.MSCLog;
import com.sankuai.meituan.serviceloader.annotation.ServiceLoaderInterface;
import java.util.Arrays;
import java.util.List;

@ServiceLoaderInterface(key = "test_msc_app_lifecycle", interfaceClass = MSCAppLifecycleObserver.class)
public class TestMSCAppLifecycleObserver implements MSCAppLifecycleObserver {

	@Override
	public void onEvent(MSCAppLifecycle mscAppLifecycleName, MSCAppLifecycleParams params) {
		MSCLog.d("TestMSCAppLifecycleObserver",
			"appId:", getAppId(),
			"mscAppLifecycleName:", mscAppLifecycleName.toString(),
			"targetPath:", params.targetPath,
			"enterUri:", params.enterUri,
			"leaveAppInfo:", params.leaveAppInfo);
	}

	@Override
	public String getAppId() {
		return "mscdemo2";
	}

	@Override
	public List<MSCAppLifecycle> getValidLifecycleList() {
		List<MSCAppLifecycle> mscAppLifecycleList = Arrays
			.asList(MSCAppLifecycle.MSC_WILL_ENTER_APP_LIFECYCLE,
				MSCAppLifecycle.MSC_DID_ENTER_APP_LIFECYCLE,
				MSCAppLifecycle.MSC_WILL_LEAVE_APP_LIFECYCLE,
				MSCAppLifecycle.MSC_LAUNCH_FAIL_APP_LIFECYCLE);
		return mscAppLifecycleList;
	}
}
