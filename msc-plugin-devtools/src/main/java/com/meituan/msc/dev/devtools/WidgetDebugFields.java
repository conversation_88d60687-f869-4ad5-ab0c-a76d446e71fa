package com.meituan.msc.dev.devtools;

public class WidgetDebugFields {
    // widget 路径调试模式
    // widget debug模式下，targetPath的选择: https://km.sankuai.com/collabpage/2426542978
    // 0:完全使用IDE的参数，1:完全使用业务传参，2:结合IDE和业务传参，相同参数使用业务的值
    public static final int PATH_DEBUG_MODE_WIDGET_IDE = 0;
    public static final int PATH_DEBUG_MODE_WIDGET_BUSINESS = 1;
    public static final int PATH_DEBUG_MODE_WIDGET_MERGE = 2;
}
