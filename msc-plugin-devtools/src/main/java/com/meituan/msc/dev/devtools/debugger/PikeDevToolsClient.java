package com.meituan.msc.dev.devtools.debugger;

import android.support.annotation.NonNull;
import android.support.annotation.Nullable;
import android.text.TextUtils;
import android.util.Pair;

import com.meituan.msc.dev.devtools.debugger.reporter.CDPMessageReportUtil;
import com.meituan.msc.dev.devtools.debugger.utils.CommonException;
import com.meituan.msc.dev.devtools.debugger.utils.IDELogUtil;
import com.meituan.msc.dev.devtools.debugger.utils.MSCDomainUtils;
import com.meituan.msc.dev.devtools.json.ObjectMapper;
import com.meituan.msc.dev.devtools.reporter.DevToolsConstants;
import com.meituan.msc.dev.devtools.websocket.SimpleSession;
import com.meituan.msc.dev.utils.RetryWithDelay;
import com.meituan.msc.modules.reporter.CommonTags;
import com.meituan.msc.modules.reporter.MSCCommonTagReporter;
import com.meituan.msc.modules.reporter.MSCLog;
import com.sankuai.android.jarvis.Jarvis;
import com.sankuai.common.utils.ExceptionUtils;

import org.json.JSONException;
import org.json.JSONObject;

import java.util.Arrays;
import java.util.HashSet;
import java.util.Set;
import java.util.UUID;
import java.util.concurrent.BlockingQueue;
import java.util.concurrent.ConcurrentHashMap;
import java.util.concurrent.LinkedBlockingQueue;
import java.util.concurrent.TimeUnit;
import java.util.concurrent.atomic.AtomicInteger;

import rx.Observable;
import rx.functions.Action1;
import rx.subjects.PublishSubject;

public class PikeDevToolsClient implements ChromeDevToolsClient {
    private static final String TAG = "PikeDevToolsClient";
    private static final int MAX_SENDING_MSG = 1;

    private final ObjectMapper objectMapper = new ObjectMapper();
    private final PublishSubject<Result> responseObservable = PublishSubject.create();
    private final MSCCommonTagReporter reporter;
    private final MSCDebugger debugger;
    private volatile boolean start = false;
    private final ConcurrentHashMap<Long, JSONObject> commandExtra;
    private UDSConnectionManager udsManager;
    private final CDPConnection connection;
    private final BlockingQueue<Pair<JSONObject, ResultCallback>> pendingMessageQueue;
    private final BlockingQueue<Pair<JSONObject, ResultCallback>> highPriorityPendingMessageQueue;
    // 消息优先级设定，发送消息的队列 对消息返回优先级高的优先发送
    private final Set<String> sendHighPrioritySet = new HashSet<>(Arrays.asList(
            ProtocolConstants.PROTOCOL_CSS_ENABLE,
            ProtocolConstants.PROTOCOL_CSS_GET_MATCHED_STYLES_FOR_NODE,
            ProtocolConstants.PROTOCOL_CSS_GET_COMPUTED_STYLE_FOR_NODE,
            ProtocolConstants.PROTOCOL_DOM_ENABLE,
            ProtocolConstants.PROTOCOL_DOM_GET_DOCUMENT,
            ProtocolConstants.PROTOCOL_RUNTIME_ENABLE,
            ProtocolConstants.PROTOCOL_RUNTIME_EVALUATE,
            ProtocolConstants.PROTOCOL_RUNTIME_EXECUTION_CONTEXT_CREATED,
            ProtocolConstants.PROTOCOL_OVERLAY_ENABLE,
            ProtocolConstants.PROTOCOL_DEBUGGER_ENABLE,
            ProtocolConstants.PROTOCOL_PAGE_ENABLE,
            ProtocolConstants.PROTOCOL_MSCAPPDATA_ENABLE,
            ProtocolConstants.PROTOCOL_MSCAPPDATA_GET_ACTIVE_PAGE,
            ProtocolConstants.PROTOCOL_MSCAPPDATA_GET_COMPONENTS,
            ProtocolConstants.PROTOCOL_NETWORK_ENABLE,
            ProtocolConstants.PROTOCOL_NETWORK_RESPONSE_RECEIVED,
            ProtocolConstants.PROTOCOL_NETWORK_REQUEST_WILL_BE_SENT));
    private volatile boolean messageLooping = false;
    private volatile boolean pause = false;
    private String ideVersion = "";
    private String debugSessionId = "";

    private final AtomicInteger sendingMsgCount = new AtomicInteger(0);


    public PikeDevToolsClient(CDPConnection connection, MSCCommonTagReporter reporter, MSCDebugger debugger) {
        this.connection = connection;
        this.reporter = reporter;
        this.debugger = debugger;
        this.commandExtra = new ConcurrentHashMap<>();
        this.pendingMessageQueue = new LinkedBlockingQueue<>();
        this.highPriorityPendingMessageQueue = new LinkedBlockingQueue<>();
    }

    public void setUDSManager(UDSConnectionManager manager) {
        this.udsManager = manager;
    }

    public void setIdeVersion(String ideVersion) {
        this.ideVersion = ideVersion;
    }

    public void setInspectorId(String debugSessionId) {
        this.debugSessionId = debugSessionId;
    }

    @Override
    public void startRemoteDebug(@Nullable final JSONObject params, @Nullable final ResultCallback callback) {
        start = true;
        startLooper();
        JSONObject object = new JSONObject();
        try {
            if (params != null) {
                object = params;
            }
            object.put("type", DevToolsConstants.MESSAGE_TYPE_START_REMOTE_DEBUG);
        } catch (JSONException e) {
            e.printStackTrace();
        }
        if (connection != null) {
            final JSONObject jsonObject = object;
            connection.send(object)
                    .retryWhen(new RetryWithDelay(2, 1, TimeUnit.SECONDS, true))
                    .subscribe(new Action1<String>() {
                        @Override
                        public void call(String response) {
                            notifySuccess(response, jsonObject, callback);
                        }
                    }, new Action1<Throwable>() {
                        @Override
                        public void call(Throwable throwable) {
                            notifyFailed(throwable, jsonObject, callback);
                        }
                    });
        }
        CDPMessageReportUtil.reportCDPTrace("", DevToolsConstants.CDP_TRACE_TYPE_START, null);
    }

    public void onCommand(long id, @NonNull String methodName, @Nullable JSONObject params, @Nullable String sessionId) {
        try {
            JSONObject extra = new JSONObject();
            extra.put("pausedOnMainThread", isMainThreadPaused());
            // 借助 commandExtra 作为载体传递值, 让返回response也携带 method 字段，方便追踪查数据
            extra.put("methodName", methodName);
            commandExtra.put(id, extra);
        } catch (Exception e) {
            e.printStackTrace();
        }
    }

    @Override
    public void sendEvent(@NonNull String method, @NonNull Object paramsObject, @Nullable String sessionId, @Nullable ResultCallback callback) {
        JSONObject params = objectMapper.convertValue(paramsObject, JSONObject.class);
        JSONObject eventObject = new JSONObject();
        JSONObject extra = new JSONObject();
        try {
            eventObject.put("method", method);
            eventObject.put("params", params);
            eventObject.put("time", System.currentTimeMillis());
            if (!TextUtils.isEmpty(sessionId)) {
                eventObject.put("sessionId", sessionId);
            }
            extra.put("pausedOnMainThread", isMainThreadPaused());
            eventObject.put(DevToolsConstants.PARAMS_DEBUG_CDP_MESSAGE_TYPE, "event");
            reportDebugCDPEvent(reporter, eventObject);
        } catch (JSONException e) {
            e.printStackTrace();
        }
        if (!start) {
            callbackForReporter(callback, false, eventObject, DevToolsConstants.CODE_REPORT_SEND_EVENT_RETURN);
            return;
        }
        callbackForReporter(callback, true, eventObject, DevToolsConstants.CODE_REPORT_SEND_EVENT);
        sendDebugMessage(eventObject.toString(), extra, callback, sendHighPrioritySet.contains(method));
    }

    @Override
    public void responseCommand(long id, Object resultObject, @Nullable Object errorObject, @Nullable String sessionId, @Nullable ResultCallback callback) {
        JSONObject result = objectMapper.convertValue(resultObject, JSONObject.class);
        JSONObject error = objectMapper.convertValue(errorObject, JSONObject.class);
        JSONObject response = new JSONObject();
        JSONObject extra = new JSONObject();
        String methodName = "";
        try {
            response.put("id", id);
            response.put("result", result);
            response.put("error", error);
            if (!TextUtils.isEmpty(sessionId)) {
                response.put("sessionId", sessionId);
            }
            if (commandExtra.containsKey(id)) {
                extra = commandExtra.remove(id);
                methodName = (String) extra.remove("methodName");
            }
            response.put("method", methodName);
            response.put(DevToolsConstants.PARAMS_DEBUG_CDP_MESSAGE_TYPE, "response");
        } catch (JSONException e) {
            e.printStackTrace();
        }
        if (!start) {
            callbackForReporter(callback, false, response, DevToolsConstants.CODE_REPORT_RESPONSE_COMMAND_RETURN);
            return;
        }
        callbackForReporter(callback, true, response, DevToolsConstants.CODE_REPORT_RESPONSE_COMMAND);
        sendDebugMessage(response.toString(), extra, callback, sendHighPrioritySet.contains(methodName));
    }

    private void callbackForReporter(@Nullable ResultCallback callback, boolean success, JSONObject object, int code) {
        try {
            if (callback != null) {
                JSONObject jsonObject = new JSONObject(object.toString());
                jsonObject.put(DevToolsConstants.PARAMS_DEBUG_QUEUE_COUNT, pendingMessageQueue.size() + highPriorityPendingMessageQueue.size());
                callback.onResult(new Result(success, jsonObject.toString(), code, null));
            }
        } catch (JSONException e) {
            e.printStackTrace();
        }
    }

    @Override
    public void stopRemoteDebug(@Nullable ResultCallback callback) {
        start = false;
        stopLooper();
        JSONObject object = new JSONObject();
        try {
            object.put("type", "stopRemoteDebug");
        } catch (JSONException e) {
            e.printStackTrace();
        }
        if (connection != null) {
            connection.send(object).timeout(2, TimeUnit.SECONDS).subscribe(new Action1<String>() {
                @Override
                public void call(String s) {
                    if (callback != null) {
                        callback.onResult(new Result(true, s, 0, null));
                    }
                }
            }, new Action1<Throwable>() {
                @Override
                public void call(Throwable throwable) {
                    if (callback != null) {
                        callback.onResult(new Result(false, null, 0, null));
                    }
                }
            });
        }
        CDPMessageReportUtil.reportCDPTrace("", DevToolsConstants.CDP_TRACE_TYPE_STOP, null);
    }

    @Override
    public void ping(@Nullable ResultCallback callback) {
        JSONObject object = new JSONObject();
        try {
            object.put("type", "ping");
        } catch (JSONException e) {
            e.printStackTrace();
        }
        addToQueue(object, callback, false);
    }

    @Override
    public void onAppRoute(String openType, String pagePath) {
        JSONObject object = new JSONObject();
        try {
            object.put("type", "onAppRoute");
            object.put("openType", openType);
            object.put("pagePath", pagePath);
            object.put(CommonTags.TAG_RENDER_TYPE, MSCDomainUtils.getRendererType(debugger.getRuntime()));
        } catch (JSONException e) {
            e.printStackTrace();
        }
        addToQueue(object, null, true);

        //页面切换时重置连接
        if (udsManager == null) {
            MSCLog.e("[MSCDebugger@pushPage]", "uds null");
            return;
        }
        udsManager.resetCloseCmd();
        notifyDocumentChange();
        udsManager.startWebView();
    }

    private void notifyDocumentChange() {
        if (udsManager == null) {
            return;
        }
        ChromeDevToolsClient proxy = udsManager.getProxy();
        if (proxy == null) {
            return;
        }
        proxy.sendEvent("DOM.documentUpdated", new JSONObject(), "", CDPMessageReportUtil.nativeEventCallback);
    }

    @Override
    public void pong(@Nullable ResultCallback callback) {
        JSONObject object = new JSONObject();
        try {
            object.put("type", DevToolsConstants.MESSAGE_TYPE_PONG);
        } catch (JSONException e) {
            e.printStackTrace();
        }
        addToQueue(object, callback, false);
    }

    @Deprecated
    @Override
    public SimpleSession getSession() {
        return null;
    }

    public Observable<Result> getResponseObservable() {
        return responseObservable.asObservable();
    }

    public void pause() {
        if (!pause) {
            MSCLog.i(IDELogUtil.keyTag(TAG), "pause message looper");
        }
        pause = true;
    }

    public void resume() {
        if (pause) {
            MSCLog.i(IDELogUtil.keyTag(TAG), "resume message looper");
        }
        pause = false;
    }

    private void sendDebugMessage(String message, JSONObject extra, @Nullable ResultCallback callback, boolean isHighPriority) {
        JSONObject object = new JSONObject();
        try {
            object.put("type", "sendDebugMessage");
            object.put("message", message);
            object.put("extra", extra);
            object.put(DevToolsConstants.PARAMS_DEBUG_IDE_VERSION, ideVersion);
            object.put(DevToolsConstants.PARAMS_DEBUG_IDE_MACHINE_ID, CDPMessageReportUtil.extractMachineId(debugSessionId));
            object.put(DevToolsConstants.PARAMS_DEBUG_OS_NAME, "Android");
            object.put(DevToolsConstants.PARAMS_DEBUG_MSC_DEBUG_SDK_VERSION, DevToolsConstants.MSC_IDE_VERSION);
        } catch (JSONException e) {
            e.printStackTrace();
        }
        addToQueue(object, callback, isHighPriority);
    }

    private void addToQueue(JSONObject object, ResultCallback callback, boolean isHighPriority) {
        if (isHighPriority) {
            highPriorityPendingMessageQueue.add(Pair.create(object, callback));
        } else {
            pendingMessageQueue.add(Pair.create(object, callback));
        }
    }

    public static void reportDebugCDPEvent(MSCCommonTagReporter reporter, JSONObject object) {
        if (reporter != null && object != null) {
            JSONObject event = new JSONObject();
            try {
                event.put("method", object.optString("method", ""));
                event.put("sessionId", object.optString("sessionId", ""));
                event.put("traceId", UUID.randomUUID().toString());
                event.put("time", System.currentTimeMillis());
            } catch (JSONException e) {
                e.printStackTrace();
            }
            reporter.record(DevToolsConstants.MSC_IDE_DEBUG_CDP_EVENT).tag("event", event).sendDelay();
        }
    }

    private void startLooper() {
        if (messageLooping) {
            MSCLog.e(IDELogUtil.tag(TAG), "already start looper");
            return;
        }
        messageLooping = true;
        Jarvis.newSingleThreadExecutor("cdp-msg-looper").execute(new Runnable() {
            @Override
            public void run() {
                while (messageLooping) {
                    if (connection == null || pause) {
                        continue;
                    }
                    if (sendingMsgCount.get() >= MAX_SENDING_MSG && !isMainThreadPaused()) {
                        continue;
                    }
                    Pair<JSONObject, ResultCallback> callbackMsg = takeMessage();
                    if (callbackMsg != null && callbackMsg.first != null && connection != null) {
                        sendingMsgCount.incrementAndGet();
                        final ResultCallback callback = callbackMsg.second;
                        CDPMessageReportUtil.reportCDPTrace("", DevToolsConstants.CDP_TRACE_TYPE_SEND,
                                callbackMsg.first);
                        connection.send(callbackMsg.first)
                                .retryWhen(new RetryWithDelay(2, 1, TimeUnit.SECONDS, true))
                                .subscribe(new Action1<String>() {
                                    @Override
                                    public void call(String response) {
                                        sendingMsgCount.decrementAndGet();
                                        notifySuccess(response, callbackMsg.first, callback);
                                        CDPMessageReportUtil.reportCDPTrace("", DevToolsConstants.CDP_TRACE_TYPE_SEND_SUCCESS,
                                                callbackMsg.first);
                                    }
                                }, new Action1<Throwable>() {
                                    @Override
                                    public void call(Throwable throwable) {
                                        sendingMsgCount.decrementAndGet();
                                        notifyFailed(throwable, callbackMsg.first, callback);
                                        CDPMessageReportUtil.reportCDPTrace("", DevToolsConstants.CDP_TRACE_TYPE_SEND_FAIL,
                                                callbackMsg.first);
                                    }
                                });
                    }
                }
            }
        });
    }

    private Pair<JSONObject, ResultCallback> takeMessage() {
        try {
            if (highPriorityPendingMessageQueue.isEmpty()) {
                return pendingMessageQueue.take();
            } else {
                return highPriorityPendingMessageQueue.take();
            }
        } catch (InterruptedException e) {
            e.printStackTrace();
        }
        return null;
    }

    private void notifySuccess(final String response, JSONObject jsonObject, final ResultCallback callback) {
        final Result result = new Result(true, response, 0, null);
        responseObservable.onNext(result);
        if (callback != null) {
            callback.onResult(new Result(true, jsonObject.toString(), DevToolsConstants.CODE_REPORT_CONNECTION_SEND_SUCCESS, null));
        }
    }

    private void notifyFailed(final Throwable throwable, JSONObject jsonObject, final ResultCallback callback) {
        if (throwable instanceof CommonException) {
            CommonException exception = (CommonException) throwable;
            final Result result = new Result(false, null, exception.getCode(), exception.getMessage());
            responseObservable.onNext(result);
            if (callback != null) {
                callback.onResult(new Result(false, jsonObject.toString(), DevToolsConstants.CODE_REPORT_CONNECTION_SEND_FAIL, exception.getMessage()));
            }
        } else {
            final Result result = new Result(false, null, DevToolsConstants.ERROR_CODE_UNKNOWN, ExceptionUtils.getStackTrace(throwable));
            responseObservable.onNext(result);
            if (callback != null) {
                callback.onResult(new Result(false, jsonObject.toString(), DevToolsConstants.CODE_REPORT_CONNECTION_SEND_FAIL, ExceptionUtils.getStackTrace(throwable)));
            }
        }
    }

    /**
     * 解决主线程stepOver失效问题
     * 根因在于pike消息回调在主线程执行，所以消息队列失效
     *
     * @return 主线程被断点断住 true
     */
    private boolean isMainThreadPaused() {
        if (udsManager != null) {
            return udsManager.isMainThreadPaused();
        }
        return false;
    }

    private void stopLooper() {
        messageLooping = false;
    }

}
