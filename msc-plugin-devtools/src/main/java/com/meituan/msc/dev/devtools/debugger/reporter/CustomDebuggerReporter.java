package com.meituan.msc.dev.devtools.debugger.reporter;

import android.content.Context;
import android.os.SystemClock;
import android.support.annotation.Keep;
import android.text.TextUtils;

import com.google.gson.annotations.SerializedName;
import com.meituan.msc.common.utils.CIPStorageFileUtil;
import com.meituan.msc.dev.devtools.debugger.utils.ConvertUtils;
import com.meituan.msc.dev.devtools.debugger.utils.GsonProvider;
import com.meituan.msc.dev.devtools.debugger.utils.IDELogUtil;
import com.meituan.msc.dev.devtools.reporter.DevToolsConstants;
import com.meituan.msc.extern.MSCEnvHelper;
import com.meituan.msc.lib.BuildConfig;
import com.meituan.msc.modules.engine.MSCRuntime;
import com.meituan.msc.modules.reporter.CommonTags;
import com.meituan.msc.modules.reporter.MSCLog;

import java.util.ArrayList;
import java.util.Collections;
import java.util.Comparator;
import java.util.HashMap;
import java.util.List;
import java.util.Map;

public class CustomDebuggerReporter extends DebuggerReporter{
    private static final String TAG = "CustomDebuggerReporter";
    private static final String KEY_MSC_IDE_EVENT_LIST = "msc.ide.connection.event.list";
    private long debuggerStartTime = -1;
    private final String debugTraceId;
    private final String ideVersion;
    private DebugEvents debugEvents;
    private final Object debugEventsLock = new Object();

    public CustomDebuggerReporter(MSCRuntime runtime, String runMode, String ideVersion, String debugType, String debugTraceId, String debugSessionId) {
        this.debugTraceId = debugTraceId;
        this.ideVersion = ideVersion;
        final Map<String, Object> tags = CommonTags.build(runtime).generateCommonTags();
        tags.put(DevToolsConstants.PARAMS_DEBUG_SDK_VERSION, DevToolsConstants.MSC_IDE_VERSION);
        tags.put(DevToolsConstants.PARAMS_DEBUG_TRACE_ID, debugTraceId);
        tags.put(DevToolsConstants.PARAMS_DEBUG_RUN_MODE, runMode);
        tags.put(DevToolsConstants.PARAMS_DEBUG_CONNECT_TYPE, "pike");
        tags.put(DevToolsConstants.PARAMS_DEBUG_IDE_VERSION, ideVersion);
        tags.put(DevToolsConstants.PARAMS_DEBUG_TYPE, debugType);
        tags.put(DevToolsConstants.PARAMS_DEBUG_SESSION_ID, debugSessionId);
        tags.put(DevToolsConstants.PARAMS_DEBUG_RUN_TYPE, getDebugRunType());
        tags.put(DevToolsConstants.PARAMS_DEBUG_IDE_PKG_TYPE, getIDERunType(ideVersion));
        commonTags(tags);
        reportLastDebugEvents(MSCEnvHelper.getContext());
        debugEvents = new DebugEvents(debugTraceId, getCommonTags());
        MSCLog.iSync(IDELogUtil.keyTag(TAG), "CustomDebuggerReporter CREATE:" + debugSessionId);
    }

    public void reportDebugStartEvent() {
        debuggerStartTime = SystemClock.elapsedRealtime();
        MSCLog.iSync(IDELogUtil.keyTag(TAG), "reportDebugStartEvent->" + debugTraceId);
        once(DevToolsConstants.MSC_IDE_DEBUG_START_EVENT).value(1).sendRealTime();
        addEvent(DevToolsConstants.MSC_IDE_DEBUG_START_EVENT);
    }

    public void reportDebugStartFailed(int errorCode, String errorMessage) {
        MSCLog.iSync(IDELogUtil.keyTag(TAG), "reportDebugStartFailed->errCode" + errorCode + ",errorMessage:" + errorMessage + ",traceId:" + debugTraceId);
        once(DevToolsConstants.MSC_IDE_DEBUG_START_FAILED)
                .tag("errorCode", errorCode)
                .tag("errorMessage", errorMessage)
                .value(1).sendRealTime();
        clearAllEvents();
    }

    public void reportDebugConnectedEvent() {
        MSCLog.iSync(IDELogUtil.keyTag(TAG), "reportDebugConnectedEvent->" + debugTraceId);
        once(DevToolsConstants.MSC_IDE_DEBUG_CONNECTED_EVENT).value(1).sendRealTime();
        reportDebugStartDuration();
        addEvent(DevToolsConstants.MSC_IDE_DEBUG_CONNECTED_EVENT);
    }

    public void reportDebugStopEvent(String reason) {
        MSCLog.iSync(IDELogUtil.keyTag(TAG), "reportDebugStopEvent->" + reason + ",traceId:" + debugTraceId);
        once(DevToolsConstants.MSC_IDE_DEBUG_STOP_EVENT).tag("reason", reason).value(1).sendRealTime();
        clearAllEvents();
    }

    public void reportDebugConnectInterruptEvent(int errorCode, String errorMessage) {
        if (debugEvents != null && debugEvents.events != null && debugEvents.events.size() > 0) {
            List<Event> eventList = debugEvents.events;
            Collections.sort(eventList, eventComparator);
            Event last = eventList.get(eventList.size() - 1);
            if (DevToolsConstants.MSC_IDE_DEBUG_ENTER_BACKGROUND_EVENT.equals(last.name)) {
                // 退后台
                MSCLog.iSync(IDELogUtil.keyTag(TAG), "reportDebugConnectInterruptEvent->errorCode:" + DevToolsConstants.CODE_INTERRUPT_BACKGROUND
                        + ",errorMessage:background ,traceId:" + debugTraceId);
                once(DevToolsConstants.MSC_IDE_DEBUG_CONNECTED_INTERRUPT_EVENT)
                        .tag("errorCode", DevToolsConstants.CODE_INTERRUPT_BACKGROUND)
                        .tag("errorMessage", "background")
                        .value(1).sendRealTime();
                clearAllEvents();
                return;
            } else if (DevToolsConstants.MSC_IDE_DEBUG_ENTER_FOREGROUND_EVENT.equals(last.name)) {
                // 当前时间距离回到前台时间小于10s
                if (eventList.size() >= 2) {
                    Event last2 = eventList.get(eventList.size() - 2);
                    long now = System.currentTimeMillis();
                    if (DevToolsConstants.MSC_IDE_DEBUG_ENTER_BACKGROUND_EVENT.equals(last2.name)
                            && now - last.time < 5000) {
                        MSCLog.iSync(IDELogUtil.keyTag(TAG), "reportDebugConnectInterruptEvent->errorCode:" + DevToolsConstants.CODE_INTERRUPT_BACKGROUND
                                + ",errorMessage:background->foreground ,traceId:" + debugTraceId);
                        once(DevToolsConstants.MSC_IDE_DEBUG_CONNECTED_INTERRUPT_EVENT)
                                .tag("errorCode", DevToolsConstants.CODE_INTERRUPT_BACKGROUND)
                                .tag("errorMessage", "background->foreground")
                                .value(1).sendRealTime();
                        clearAllEvents();
                        return;
                    }
                }
            }
        }
        MSCLog.iSync(IDELogUtil.keyTag(TAG), "reportDebugConnectInterruptEvent->errorCode:" + errorCode + ",errorMessage:" + errorMessage + ",traceId:" + debugTraceId);
        once(DevToolsConstants.MSC_IDE_DEBUG_CONNECTED_INTERRUPT_EVENT)
                .tag("errorCode", errorCode)
                .tag("errorMessage", errorMessage)
                .value(1).sendRealTime();
        clearAllEvents();
    }

    private void reportDebugStartDuration() {
        if (debuggerStartTime <= 0) {
            return;
        }
        long endTime = SystemClock.elapsedRealtime();
        long duration = endTime - debuggerStartTime;
        MSCLog.iSync(IDELogUtil.keyTag(TAG), "reportDebugStartDuration->duration:" + duration + ",start:" + debuggerStartTime + ",end:" + endTime + ",traceId" + debugTraceId);
        once(DevToolsConstants.MSC_IDE_DEBUG_START_DURATION).value(endTime - debuggerStartTime).sendRealTime();
    }

    public void reportDebugCrash() {
        MSCLog.iSync(IDELogUtil.keyTag(TAG), "reportDebugCrash->" + debugTraceId);
        addEvent(DevToolsConstants.MSC_IDE_DEBUG_CRASH_EVENT);
    }

    public void reportDebugEnterBackground() {
        MSCLog.iSync(IDELogUtil.keyTag(TAG), "reportDebugEnterBackground->" + debugTraceId);
        record(DevToolsConstants.MSC_IDE_DEBUG_ENTER_BACKGROUND_EVENT).value(1).sendRealTime();
        addEvent(DevToolsConstants.MSC_IDE_DEBUG_ENTER_BACKGROUND_EVENT);
    }

    public void reportDebugEnterForeground() {
        MSCLog.iSync(IDELogUtil.keyTag(TAG), "reportDebugEnterForeground:" + debugTraceId);
        record(DevToolsConstants.MSC_IDE_DEBUG_ENTER_FOREGROUND_EVENT).value(1).sendRealTime();
        addEvent(DevToolsConstants.MSC_IDE_DEBUG_ENTER_FOREGROUND_EVENT);
    }

    public void reportDebugTunnelReady() {
        MSCLog.iSync(IDELogUtil.keyTag(TAG), "reportDebugTunnelReady:" + debugTraceId);
        record(DevToolsConstants.MSC_IDE_DEBUG_PIKE_TUNNEL_READY_EVENT).value(1).sendRealTime();
    }

    public void reportDebugTunnelClosed() {
        MSCLog.iSync(IDELogUtil.keyTag(TAG), "reportDebugTunnelClosed:" + debugTraceId);
        record(DevToolsConstants.MSC_IDE_DEBUG_PIKE_TUNNEL_CLOSED_EVENT).value(1).sendRealTime();
    }

    public void reportDebugReloadEvent(String path) {
        MSCLog.iSync(IDELogUtil.keyTag(TAG), "reportDebugReloadEvent->path:" + path + ",traceId:" + debugTraceId);
        record(DevToolsConstants.MSC_IDE_DEBUG_CONNECTED_RELOAD_EVENT).tag("path", path).value(1).sendRealTime();
    }

    public void reportDebugReloadSuccess() {
        long versionCode = ConvertUtils.versionCode(ideVersion);
        if (versionCode < 11100) {
            MSCLog.e(IDELogUtil.tag(TAG), "reportDebugReloadSuccess->ideVersion < " + versionCode);
            return;
        }
        MSCLog.iSync(IDELogUtil.keyTag(TAG), "reportDebugReloadSuccess->traceId:" + debugTraceId);
        record(DevToolsConstants.MSC_IDE_DEBUG_RELOAD_COUNT).value(1).sendRealTime();
    }

    public void reportDebugReloadDuration(long duration) {
        long versionCode = ConvertUtils.versionCode(ideVersion);
        if (versionCode < 11100) {
            MSCLog.e(IDELogUtil.tag(TAG), "reportDebugReloadDuration->ideVersion < " + versionCode);
            return;
        }
        MSCLog.iSync(IDELogUtil.keyTag(TAG), "reportDebugReloadDuration->duration:" + duration + ",traceId:" + debugTraceId);
        record(DevToolsConstants.MSC_IDE_DEBUG_RELOAD_DURATION).value(duration).sendRealTime();
    }

    public void reportDebugIDEOffline() {
        MSCLog.iSync(IDELogUtil.keyTag(TAG), "reportDebugIDEOffline->" + debugTraceId);
        record(DevToolsConstants.MSC_IDE_DEBUG_IDE_OFFLINE_EVENT).value(1).sendRealTime();
    }

    private void addEvent(String name) {
        synchronized (debugEventsLock) {
            if (debugEvents == null) {
                return;
            }
            if (debugEvents.events == null) {
                debugEvents.events = new ArrayList<>();
            }
            MSCLog.i(IDELogUtil.keyTag(TAG), "addEvent->" + name);
            debugEvents.events.add(new Event(name, System.currentTimeMillis()));
        }
        Context context = MSCEnvHelper.getContext();
        if (context != null) {
            CIPStorageFileUtil.getCIPStorageCenter(context).setString(KEY_MSC_IDE_EVENT_LIST, GsonProvider.get().toJson(debugEvents));
        }
    }

    private void clearAllEvents() {
        synchronized (debugEventsLock) {
            debugEvents = null;
        }
        Context context = MSCEnvHelper.getContext();
        if (context != null) {
            CIPStorageFileUtil.getCIPStorageCenter(context).remove(KEY_MSC_IDE_EVENT_LIST);
        }
    }

    public static void reportLastDebugEvents(Context context) {
        if (context == null) {
            return;
        }
        String jsonStr = CIPStorageFileUtil.getCIPStorageCenter(context).getString(KEY_MSC_IDE_EVENT_LIST, "");
        MSCLog.i(IDELogUtil.keyTag(TAG),  "reportLastDebugEvents->" + jsonStr);
        if (TextUtils.isEmpty(jsonStr)) {
            return;
        }
        try {
            DebugEvents debug = GsonProvider.get().fromJson(jsonStr, DebugEvents.class);
            if (debug == null || TextUtils.isEmpty(debug.debugTraceId) || debug.events == null) {
                return;
            }

            final String debugTraceId = debug.debugTraceId;
            final Map<String, Object> params = debug.params;
            final List<Event> eventList = debug.events;
            Collections.sort(eventList, eventComparator);

            // crash
            boolean containsCrash = false;
            long crashTime = -1;
            for (Event event : eventList) {
                if (DevToolsConstants.MSC_IDE_DEBUG_CRASH_EVENT.equals(event.name)) {
                    containsCrash = true;
                    crashTime = event.time;
                    break;
                }
            }
            if (containsCrash) {
                CIPStorageFileUtil.getCIPStorageCenter(context).remove(KEY_MSC_IDE_EVENT_LIST);
                reportLastInterrupt(debugTraceId, DevToolsConstants.CODE_INTERRUPT_CRASH, "crash", params);
                reportLastCrash(debugTraceId, crashTime, params);
                return;
            }

            // 长时间在后台
            if (eventList.size() > 0) {
                Event lastEvent = eventList.get(eventList.size() - 1);
                if (DevToolsConstants.MSC_IDE_DEBUG_ENTER_BACKGROUND_EVENT.equals(lastEvent.name)) {
                    CIPStorageFileUtil.getCIPStorageCenter(context).remove(KEY_MSC_IDE_EVENT_LIST);
                    reportLastInterrupt(debugTraceId, DevToolsConstants.CODE_INTERRUPT_BACKGROUND, "background", params);
                    return;
                }
            }

            reportUnknownInterrupt(debug);
        } catch (Exception e) {
            e.printStackTrace();
        }
        CIPStorageFileUtil.getCIPStorageCenter(context).remove(KEY_MSC_IDE_EVENT_LIST);
    }

    private static void reportLastInterrupt(String lastDebugTraceId, int errorCode, String errorMessage, Map<String, Object> params) {
        MSCLog.i(IDELogUtil.keyTag(TAG), "reportLastInterrupt->lastDebugTraceId:" + lastDebugTraceId + ",errorCode:" + errorCode + ",errorMessage:" + errorMessage);
        DebuggerReporter reporter = new DebuggerReporter();
        if (params == null) {
            params = new HashMap<>();
            params.put(DevToolsConstants.PARAMS_DEBUG_TRACE_ID, lastDebugTraceId);
        }
        params.put("errorCode", errorCode);
        params.put("errorMessage", errorMessage);
        reporter.once(DevToolsConstants.MSC_IDE_DEBUG_CONNECTED_INTERRUPT_EVENT).tags(params).value(1).sendRealTime();
    }

    private static void reportUnknownInterrupt(DebugEvents debugEvents) {
        if (debugEvents == null || TextUtils.isEmpty(debugEvents.debugTraceId)) {
            return;
        }
        String lastDebugTraceId = debugEvents.debugTraceId;
        Map<String, Object> params = debugEvents.params;
        ArrayList<Event> events = debugEvents.events;
        DebuggerReporter reporter = new DebuggerReporter();
        if (params == null) {
            params = new HashMap<>();
            params.put(DevToolsConstants.PARAMS_DEBUG_TRACE_ID, lastDebugTraceId);
        }
        params.put("events", GsonProvider.get().toJson(events));
        params.put("assertType", "unknown_interrupt");
        reporter.once(DevToolsConstants.MSC_IDE_DEBUG_ASSERT_ERROR).tags(params).value(1).sendRealTime();
    }


    private static void reportLastCrash(String lastDebugTraceId, long crashTime, Map<String, Object> params) {
        if (crashTime <= 0 || TextUtils.isEmpty(lastDebugTraceId)) {
            return;
        }
        MSCLog.iSync(IDELogUtil.keyTag(TAG), "reportLastCrash->traceId:" + lastDebugTraceId + ",crashTime:" + crashTime);
        DebuggerReporter reporter = new DebuggerReporter();
        if (params == null) {
            params = new HashMap<>();
            params.put(DevToolsConstants.PARAMS_DEBUG_TRACE_ID, lastDebugTraceId);
        }
        params.put("crashTime", crashTime);
        reporter.once(DevToolsConstants.MSC_IDE_DEBUG_CRASH_EVENT).tags(params).value(1).sendRealTime();
    }

    @Keep
    private static class DebugEvents {
        @SerializedName("debugTraceId")
        public final String debugTraceId;

        @SerializedName("params")
        public Map<String, Object> params;

        @SerializedName("events")
        public ArrayList<Event> events;

        public DebugEvents(String debugTraceId, Map<String, Object> params) {
            this.debugTraceId = debugTraceId;
            this.params = params;
            this.events = new ArrayList<>();
        }
    }

    @Keep
    private static class Event {
        @SerializedName("name")
        public final String name;
        @SerializedName("time")
        public final long time;

        public Event(String name, long time) {
            this.name = name;
            this.time = time;
        }
    }

    private static final Comparator<Event> eventComparator = new Comparator<Event>() {
        @Override
        public int compare(Event o1, Event o2) {
            return Long.compare(o1.time, o2.time);
        }
    };

    private String getDebugRunType() {
        boolean isSnapshot = BuildConfig.AAR_VERSION.contains("SNAPSHOT");
        boolean isSample = false;
        Context context = MSCEnvHelper.getContext();
        if (context != null) {
            isSample = context.getPackageName().equals("com.meituan.android.msc.sample");
        }
        if (isSnapshot || isSample) {
            return "DEBUG";
        }
        return "TEST";
    }

    private String getIDERunType(String ideVersion) {
        if (!TextUtils.isEmpty(ideVersion) && ideVersion.contains("beta")) {
            return "BETA";
        }
        return "RELEASE";
    }
}
