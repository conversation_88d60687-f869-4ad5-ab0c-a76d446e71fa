/*
 * Copyright (c) Facebook, Inc. and its affiliates.
 *
 * This source code is licensed under the MIT license found in the
 * LICENSE file in the root directory of this source tree.
 */

package com.meituan.msc.dev.devtools.inspector.network;


public enum PrettyPrinterDisplayType {
  JSON(ResourceType.XHR),
  HTML(ResourceType.DOCUMENT),
  TEXT(ResourceType.DOCUMENT);

  private final ResourceType mResourceType;

  private PrettyPrinterDisplayType(ResourceType resourceType) {
    mResourceType = resourceType;
  }

  /**
   * Converts PrettyPrinterDisplayType values to the appropriate
   *  {@link ResourceType} values that <PERSON><PERSON><PERSON> understands
   */
  public ResourceType getResourceType() {
    return mResourceType;
  }
}
