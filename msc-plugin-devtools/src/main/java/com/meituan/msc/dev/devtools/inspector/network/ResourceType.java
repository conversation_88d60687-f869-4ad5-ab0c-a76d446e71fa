package com.meituan.msc.dev.devtools.inspector.network;

import com.meituan.msc.dev.devtools.json.annotation.JsonValue;

/**
 * Created by letty on 2020-05-12.
 **/

public enum ResourceType {
    DOCUMENT("Document"),
    STYLESHEET("Stylesheet"),
    IMAGE("Image"),
    FONT("Font"),
    SCRIPT("Script"),
    XHR("XHR"),
    WEBSOCKET("WebSocket"),
    OTHER("Other");

    private final String mProtocolValue;

    private ResourceType(String protocolValue) {
        mProtocolValue = protocolValue;
    }

    @JsonValue
    public String getProtocolValue() {
        return mProtocolValue;
    }
}