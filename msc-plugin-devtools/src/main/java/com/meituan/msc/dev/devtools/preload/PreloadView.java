package com.meituan.msc.dev.devtools.preload;

import android.app.Activity;
import android.support.design.widget.BottomSheetDialog;
import android.text.TextUtils;
import android.view.LayoutInflater;
import android.view.View;
import android.widget.Button;
import android.widget.EditText;

import com.meituan.msc.common.framework.Callback;
import com.meituan.msc.common.utils.ToastUtils;
import com.meituan.msc.dev.R;
import com.meituan.msc.dev.devtools.DebugManager;
import com.meituan.msc.dev.devtools.lockversion.util.BizPackageLockStoreUtil;
import com.meituan.msc.modules.preload.PreloadResultData;
import com.meituan.msc.modules.preload.PreloadTasksManager;
import com.meituan.msc.modules.reporter.MSCLog;

public class PreloadView {

    Activity mActivity;
    BottomSheetDialog bottomSheetDialog;

    public PreloadView(Activity activity) {
        this.mActivity = activity;
    }


    public void show() {
        Button preloadBtn = mActivity.findViewById(R.id.preload_button);
        preloadBtn.setOnClickListener(new View.OnClickListener() {
            @Override
            public void onClick(View v) {
                View view = LayoutInflater.from(mActivity).inflate(R.layout.msc_preload_input_layout, null);
                if (bottomSheetDialog == null) {
                    bottomSheetDialog = new BottomSheetDialog(mActivity);
                }
                bottomSheetDialog.setContentView(view);
                final EditText preloadAppIdEdit = view.findViewById(R.id.et_preload_app);
                if (!TextUtils.isEmpty(DebugManager.preloadAppId)) {
                    preloadAppIdEdit.setText(DebugManager.preloadAppId);
                }
                final EditText preloadPathEdit = view.findViewById(R.id.et_preload_path);
                if (!TextUtils.isEmpty(DebugManager.preloadPath)) {
                    preloadPathEdit.setText(DebugManager.preloadPath);
                }

                String appId = preloadAppIdEdit.getText().toString();
                String path = preloadPathEdit.getText().toString();

                // mscVersion已通过基础库锁版本指定
                // 锁包场景，预热使用锁定的checkUpdateUrl未支持
                final EditText checkUpdateUrlEdit = view.findViewById(R.id.et_preload_checkupdate_url);
                String checkUpdateUrl = BizPackageLockStoreUtil.getLockedAppCheckUpdateUrl(appId);
                if (TextUtils.isEmpty(checkUpdateUrl)) {
                    checkUpdateUrlEdit.setText(checkUpdateUrl);
                }

                Button preloadEnterBtn = view.findViewById(R.id.bt_preload);
                preloadEnterBtn.setOnClickListener(new View.OnClickListener() {
                    @Override
                    public void onClick(View v) {
                        try {
                            String appId = preloadAppIdEdit.getText().toString();
                            String path = preloadPathEdit.getText().toString();
//                            String checkupdateUrl = checkupdateUrlEdit.getText().toString();
//                            String version = versionEdit.getText().toString();
                            PreloadTasksManager.preloadMSCAppInProcess(appId, path, true, null, null, new Callback<PreloadResultData>() {
                                @Override
                                public void onSuccess(PreloadResultData data) {
                                    ToastUtils.toastIfDebug("preload success 业务预热成功", data);
                                }

                                @Override
                                public void onFail(String errMsg, Exception error) {
                                    MSCLog.i("preload", "preload error", errMsg);
                                }

                                @Override
                                public void onCancel() {

                                }
                            });
                        } catch (Exception e) {
                            e.printStackTrace();
                        }
                    }
                });

                bottomSheetDialog.show();
            }
        });
    }

}
