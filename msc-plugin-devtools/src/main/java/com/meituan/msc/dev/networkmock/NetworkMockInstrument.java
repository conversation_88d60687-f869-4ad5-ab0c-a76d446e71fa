package com.meituan.msc.dev.networkmock;

import android.app.Activity;
import android.app.Fragment;
import android.content.Context;
import android.content.Intent;
import android.net.Uri;
import android.os.Bundle;
import android.os.IBinder;
import android.os.PersistableBundle;
import android.os.UserHandle;
import android.support.annotation.NonNull;
import android.support.annotation.Nullable;
import android.text.TextUtils;

import com.google.gson.JsonArray;
import com.google.gson.JsonElement;
import com.google.gson.JsonObject;
import com.google.gson.JsonParser;
import com.meituan.msc.dev.devtools.DebugIntentHandler;
import com.sankuai.meituan.arbiter.hook.MTInstrumentation;

class NetworkMockInstrument extends MTInstrumentation {
    private final NetworkMockManager networkMockManager;

    public NetworkMockInstrument(NetworkMockManager networkMockManager) {
        this.networkMockManager = networkMockManager;
    }

    @Override
    public ActivityResult execStartActivity(Context who, IBinder contextThread, IBinder token, Activity target, Intent intent, int requestCode) {
        beforeExecStart(intent);
        return super.execStartActivity(who, contextThread, token, target, intent, requestCode);
    }

    @Override
    public ActivityResult execStartActivity(Context who, IBinder contextThread, IBinder token, Fragment target, Intent intent, int requestCode) {
        beforeExecStart(intent);
        return super.execStartActivity(who, contextThread, token, target, intent, requestCode);
    }

    @Override
    public ActivityResult execStartActivity(Context who, IBinder contextThread, IBinder token, Activity target, Intent intent, int requestCode, Bundle bundle) {
        beforeExecStart(intent);
        return super.execStartActivity(who, contextThread, token, target, intent, requestCode, bundle);
    }

    @Override
    public ActivityResult execStartActivity(Context who, IBinder contextThread, IBinder token, Fragment target, Intent intent, int requestCode, Bundle options) {
        beforeExecStart(intent);
        return super.execStartActivity(who, contextThread, token, target, intent, requestCode, options);
    }

    @Override
    public ActivityResult execStartActivity(Context who, IBinder contextThread, IBinder token, Activity target, Intent intent, int requestCode, Bundle options, UserHandle user) {
        beforeExecStart(intent);
        return super.execStartActivity(who, contextThread, token, target, intent, requestCode, options, user);
    }

    @Override
    public ActivityResult execStartActivity(Context who, IBinder contextThread, IBinder token, String target, Intent intent, int requestCode, Bundle options, UserHandle user) {
        beforeExecStart(intent);
        return super.execStartActivity(who, contextThread, token, target, intent, requestCode, options, user);
    }

    @Override
    public ActivityResult execStartActivity(Context who, IBinder contextThread, IBinder token, String target, Intent intent, int requestCode, Bundle options) {
        beforeExecStart(intent);
        return super.execStartActivity(who, contextThread, token, target, intent, requestCode, options);
    }

    @Override
    public void callActivityOnCreate(Activity activity, Bundle icicle) {
        beforeOnCreate(activity);
        super.callActivityOnCreate(activity, icicle);
    }

    @Override
    public void callActivityOnCreate(Activity activity, Bundle icicle, PersistableBundle persistentState) {
        beforeOnCreate(activity);
        super.callActivityOnCreate(activity, icicle, persistentState);
    }

    @Override
    public void callActivityOnNewIntent(Activity activity, Intent intent) {
        handleIntent(intent);
        super.callActivityOnNewIntent(activity, intent);
    }

    private void beforeOnCreate(@NonNull Activity activity) /*non-exception*/ {
        handleIntent(activity.getIntent());
    }

    private void beforeExecStart(@Nullable Intent intent) /*non-exception*/ {
        handleIntent(intent);
    }

    /************************************** 参数处理逻辑 ********************************************/

    private static final String PARAM_KEY_MOCK_MODE = "mscNetworkMockMode";
    private static final String PARAM_KEY_STORE_SD_CARD = "mscNetworkMockStoreSdCard";
    private static final String PARAM_KEY_MOCK_CONFIG = "mscNetworkMockConfig";
    private static final String PARAM_KEY_MOCK_SPEND_TIME = "mscNetworkMockSpendTime";


    private void handleIntent(Intent intent) {
        if (intent == null) {
            return;
        }
        DebugIntentHandler.handlePerformanceTestConfig(intent);

        Uri uri = intent.getData();
        if (uri == null) {
            return;
        }

        if (uri.isOpaque()) {
            return;
        }

        String mockModeString = uri.getQueryParameter(PARAM_KEY_MOCK_MODE);
        String defaultMockSpendTimeString = uri.getQueryParameter(PARAM_KEY_MOCK_SPEND_TIME);
        String mockConfigString = uri.getQueryParameter(PARAM_KEY_MOCK_CONFIG);
        String mscNetworkMockStoreSdCard = uri.getQueryParameter(PARAM_KEY_STORE_SD_CARD);

        if (!TextUtils.isEmpty(mscNetworkMockStoreSdCard)) {
            networkMockManager.setStoreDataToSdCard(Boolean.parseBoolean(mscNetworkMockStoreSdCard));
        }

        if (!TextUtils.isEmpty(mockModeString)) {
            networkMockManager.enable(true);

            String[] parts = mockModeString.split("-");
            if (parts.length >= 2) {
                String requestMatchModeString = parts[1];
                RequestMatchMode requestMatchMode;
                if ("url".equals(requestMatchModeString)) {
                    requestMatchMode = RequestMatchMode.URL;
                } else if ("noquery".equals(requestMatchModeString)) {
                    requestMatchMode = RequestMatchMode.URL_WITHOUT_QUERY;
                } else {
                    throw new IllegalArgumentException("不支持的参数值: " + requestMatchModeString);
                }
                networkMockManager.setRequestMatchMode(requestMatchMode);
            }
        }
        if (!TextUtils.isEmpty(defaultMockSpendTimeString)) {
            networkMockManager.enable(true);

            long defaultMockSpendTime = Long.parseLong(defaultMockSpendTimeString);
            if (defaultMockSpendTime > 0) {
                networkMockManager.setUserDefinedDefaultRequestSpendTime(defaultMockSpendTime);
            }
        }
        if (!TextUtils.isEmpty(mockConfigString)) {
            networkMockManager.enable(true);

            networkMockManager.setMockMode(NetworkMockMode.CONFIG);
            JsonParser parser = new JsonParser();
            JsonElement mockConfig = parser.parse(mockConfigString);
            if (mockConfig.isJsonArray()) {
                JsonArray config = mockConfig.getAsJsonArray();
                for (int i = 0; i < config.size(); i++) {
                    String key = config.get(i).getAsString();
                    networkMockManager.addUserDefinedMockConfig(key, null);
                }
            } else if (mockConfig.isJsonObject()) {
                JsonObject config = mockConfig.getAsJsonObject();
                for (String key : config.keySet()) {
                    networkMockManager.addUserDefinedMockConfig(key, config.get(key).getAsLong());
                }
            } else {
                throw new IllegalArgumentException(PARAM_KEY_MOCK_CONFIG + "参数不支持当前值类型");
            }
        }
    }
}
