package com.meituan.msc.dev.networkmock;

import android.Manifest;
import android.content.pm.PackageManager;
import android.os.Environment;
import android.os.SystemClock;
import android.support.annotation.NonNull;
import android.support.v4.content.ContextCompat;

import com.google.gson.reflect.TypeToken;
import com.meituan.android.cipstorage.CIPStorageCenter;
import com.meituan.msc.dev.utils.AppContextGetter;
import com.meituan.msc.dev.utils.DevStorage;
import com.meituan.msc.modules.reporter.MSCLog;
import com.sankuai.common.utils.MD5;
import com.sankuai.meituan.arbiter.hook.ArbiterHook;
import com.sankuai.meituan.retrofit2.Request;
import com.sankuai.meituan.retrofit2.raw.RawCall;
import com.sankuai.meituan.retrofit2.raw.RawResponse;

import java.io.File;
import java.io.FileInputStream;
import java.io.FileOutputStream;
import java.io.IOException;
import java.io.ObjectInputStream;
import java.io.ObjectOutputStream;
import java.util.HashMap;
import java.util.Map;

/**
 * MSC性能测试模式下的网络mock功能
 * <p>
 * 在第一次进入页面时，支持对所有网络进行数据缓存并记录网络耗时；并在下次进入页面时使用缓存的网络数据返回给上层，并sleep固定的时间。
 * 这样能减低每次网络的波动。
 * <p>
 * 使用方式：在性能测试模式中，在任何页面跳链中配置如下参数即可使用
 * - mscNetworkMockConfig={"https://yapi.sankuai.com/mock/694/api/v6/poirank/getpoihotsaleranklist":200}, 对指定的url使用指定的耗时（注意编码）
 * - mscNetworkMockMode=all, 对所有的网络请求进行mock，缺省值
 * - mscNetworkMockMode=config, 只对配置中的url进行mock
 * - mscNetworkMockMode=config-noquery, 只对配置中的url进行mock，并且使用去掉query的url来进行匹配
 * - mscNetworkMockSpendTime=300, 所有网络请求的耗时都设置为xxx
 * 使用手册详情：https://km.sankuai.com/collabpage/1501747703
 */
public class NetworkMockManager {
    private static final String TAG = "NetworkMockManager";

    private static final NetworkMockManager INSTANCE = new NetworkMockManager(NetworkMockWhiteList.getInstance());
    private static final String STORE_KEY_MOCK_CONFIG = "STORE_KEY_NETWORK_MOCK_CONFIG";

    public static NetworkMockManager getInstance() {
        return INSTANCE;
    }

    private boolean isEnable = false;
    private boolean isRegisteredInstrumentation = false;
    private final NetworkMockWhiteList whiteList;
    private Map<String, NetworkMockConfig> mockConfigMap;

    private NetworkMockMode mockMode = NetworkMockMode.ALL;
    private RequestMatchMode requestMatchMode = RequestMatchMode.URL;
    private Long userDefinedDefaultRequestSpendTime = null;
    private boolean storeDataToSdCard = false;
    private final Map<String, Long> userDefinedMockConfigMap = new HashMap<>();

    private NetworkMockManager(NetworkMockWhiteList whiteList) {
        this.whiteList = whiteList;
    }

    public boolean isEnable() {
        return isEnable;
    }

    public void enable(boolean enable) {
        isEnable = enable;
    }

    private void loadCachedMockConfig() {
        mockConfigMap = DevStorage.loadObject(STORE_KEY_MOCK_CONFIG, new TypeToken<Map<String, NetworkMockConfig>>() {
        }.getType(), new HashMap<>());
    }

    public void saveMockConfigToCache() {
        DevStorage.saveObject(STORE_KEY_MOCK_CONFIG, getMockConfigMap());
    }

    @NonNull
    private Map<String, NetworkMockConfig> getMockConfigMap() {
        if (mockConfigMap == null) {
            loadCachedMockConfig();
        }
        return mockConfigMap;
    }

    private NetworkMockConfig getMockConfig(Request request) {
        return getMockConfigMap().get(getConfigSaveKey(request));
    }

    private void setMockConfig(Request request, NetworkMockConfig mockConfig) {
        getMockConfigMap().put(getConfigSaveKey(request), mockConfig);
        saveMockConfigToCache();
    }

    public void addUserDefinedMockConfig(String url, Long spendTime) {
        userDefinedMockConfigMap.put(url, spendTime);
    }

    public void setStoreDataToSdCard(boolean storeDataToSdCard) {
        this.storeDataToSdCard = storeDataToSdCard;
    }

    public void setUserDefinedDefaultRequestSpendTime(long spendTime) {
        if (spendTime > 0) {
            userDefinedDefaultRequestSpendTime = spendTime;
        }
    }

    private String getConfigSaveKey(Request request) {
        return getMatchKeyOfRequest(request);
    }

    private String getMatchKeyOfRequest(Request request) {
        switch (requestMatchMode) {
            case URL:
                return request.url();
            case URL_WITHOUT_QUERY:
                return getUrlWithoutQuery(request.url());
            default:
                throw new UnsupportedOperationException();
        }
    }

    private String getUrlWithoutQuery(String url) {
        if (url == null) {
            return null;
        }
        int qMarkIndex = url.indexOf('?');
        if (qMarkIndex >= 0) {
            return url.substring(0, qMarkIndex);
        } else {
            return url;
        }
    }

    private String getResponseSaveKey(Request request) {
        return MD5.getMessageDigest(getConfigSaveKey(request).getBytes());
    }

    private File getResponseSaveDirectory() {
        File dir;
        String fileName = "network_mock_response";
        if (!storeDataToSdCard) {
            dir = CIPStorageCenter.requestExternalFilePath(AppContextGetter.getContext(), DevStorage.CIPS_MSC_DEV_CHANNEL, fileName);
        } else {
            if (ContextCompat.checkSelfPermission(AppContextGetter.getContext(), Manifest.permission.WRITE_EXTERNAL_STORAGE) != PackageManager.PERMISSION_GRANTED) {
                throw new RuntimeException("没有WRITE_EXTERNAL_STORAGE权限，请手动授权，或者使用 adb install -g 安装apk");
            }
            File publicDir = Environment.getExternalStoragePublicDirectory(Environment.DIRECTORY_DOCUMENTS);
            dir = new File(publicDir + File.separator + DevStorage.CIPS_MSC_DEV_CHANNEL, fileName);
        }
        dir.mkdirs();
        return dir;
    }

    private void saveResponse(Request request, SerializableResponse response) throws IOException {
        String responseSaveKey = getResponseSaveKey(request);
        try (ObjectOutputStream ois = new ObjectOutputStream(new FileOutputStream(new File(getResponseSaveDirectory(), responseSaveKey)))) {
            ois.writeObject(response);
        }
    }

    private SerializableResponse readResponse(Request request) throws IOException {
        String responseSaveKey = getResponseSaveKey(request);
        try (ObjectInputStream ois = new ObjectInputStream(new FileInputStream(new File(getResponseSaveDirectory(), responseSaveKey)))) {
            return (SerializableResponse) ois.readObject();
        } catch (ClassNotFoundException e) {
            return null;
        }
    }

    private void saveResponse(Request request, SerializableResponse response, long spendTime) throws IOException {
        if (mockMode == NetworkMockMode.CONFIG && !userDefinedMockConfigMap.containsKey(getMatchKeyOfRequest(request))) {
            // 如果只mock指定的url，且当前请求不在白名单中，则退出
            return;
        }

        if (!whiteList.mockIt(request)) {
            return;
        }

        // 保存响应
        saveResponse(request, response);

        // 保存耗时
        NetworkMockConfig mockConfig = new NetworkMockConfig();
        mockConfig.spendTime = spendTime;
        setMockConfig(request, mockConfig);
    }

    private RawResponse buildMockResponse(Request request) throws IOException {
        return readResponse(request);
    }

    private long getSpendTime(long lastSpendTime, Request request) {
        // 如果对指定的url配置了请求耗时，使用该设置
        String matchKey = getMatchKeyOfRequest(request);
        if (userDefinedMockConfigMap.containsKey(matchKey)) {
            Long spendTime = userDefinedMockConfigMap.get(matchKey);
            if (spendTime != null && spendTime > 0) {
                return spendTime;
            }
        }
        // 如果定义了缺省的请求耗时设置，使用该设置
        if (userDefinedDefaultRequestSpendTime != null) {
            return userDefinedDefaultRequestSpendTime;
        }
        return lastSpendTime;
    }

    /**
     * 当前线程睡眠指定的时间
     *
     * @param millis
     */
    private void sleep(long millis) {
        // 为了保证sleep的准确性，需要将线程的优先级设置为NORMAL或者MAX，然后再sleep
        // 因为sleep之后，当前线程会失去时间片，在有高密度CPU计算的时候sleep的话，会出现较大的误差，任务调用器一般会先将任务尽量执行完再来检查sleep的线程是否该唤醒了，
        // 将线程优先级调高，有助于让调度器多关注一下当前线程，减少sleep的误差
        Thread currentThread = Thread.currentThread();
        int currentPriority = currentThread.getPriority();
        try {
            currentThread.setPriority(Thread.MAX_PRIORITY);
            SystemClock.sleep(millis);
        } finally {
            currentThread.setPriority(currentPriority);
        }
    }

    private RawResponse interceptRequest(Request request, long startTime) throws IOException {
        if (mockMode == NetworkMockMode.CONFIG && !userDefinedMockConfigMap.containsKey(getMatchKeyOfRequest(request))) {
            // 如果只mock指定的url，且当前请求不在白名单中，则退出
            return null;
        }
        NetworkMockConfig networkMockConfig = getMockConfig(request);
        if (networkMockConfig != null) {
            if (!whiteList.mockIt(request)) {
                return null;
            }
            // mock 网络请求
            // 读取并生成请求数据
            RawResponse response = buildMockResponse(request);
            if (response == null) {
                return response;
            }
            // 睡眠剩余的时间
            long shouldSpendTime = getSpendTime(networkMockConfig.spendTime, request);
            long remainTime = shouldSpendTime - (System.currentTimeMillis() - startTime);
            if (remainTime > 0) {
                sleep(remainTime);
            } else {
                MSCLog.e(TAG, null, "mock network error, remain time is negative, ", request.url(), ", spendTime:", networkMockConfig.spendTime, ", remainTime:", remainTime);
            }
            long actualSpendTime = (System.currentTimeMillis() - startTime);
            MSCLog.i(TAG, "mock request, url:", request.url(),
                    ", actualSpendTime:", actualSpendTime, ", shouldSpendTime:", shouldSpendTime,
                    ", deviation:", (actualSpendTime - shouldSpendTime));
            return response;
        }
        return null;
    }

    public RequestMatchMode getRequestMatchMode() {
        return requestMatchMode;
    }

    public void setRequestMatchMode(RequestMatchMode requestMatchMode) {
        this.requestMatchMode = requestMatchMode;
    }

    public NetworkMockMode getMockMode() {
        return mockMode;
    }

    public void setMockMode(NetworkMockMode mockMode) {
        this.mockMode = mockMode;
    }

    public void registerInstrumentation() {
        if (isRegisteredInstrumentation) {
            return;
        }
        ArbiterHook.addMTInstrumentation(new NetworkMockInstrument(this));
        isRegisteredInstrumentation = true;
    }

    public RawResponse hookExecuteOfRawCall(RawCall call) throws IOException {
        if (!isEnable) {
            return call.execute();
        }
        long startTime = System.currentTimeMillis();
        Request request = call.request();
        RawResponse mockedResponse = interceptRequest(request, startTime);
        if (mockedResponse != null) {
            return mockedResponse;
        }
        // 不拦截，真实请求
        RawResponse realResponse = call.execute();
        long spendTime = System.currentTimeMillis() - startTime;
        MSCLog.i(TAG, "real request, url:", request.url(), ", spendTime:", spendTime);
        // 拷贝一个response 用于存储和返回给上层
        SerializableResponse serializableResponse = SerializableResponse.build(realResponse);
        saveResponse(request, serializableResponse, spendTime);
        return serializableResponse;
    }
}
