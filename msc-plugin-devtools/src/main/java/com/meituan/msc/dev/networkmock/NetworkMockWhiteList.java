package com.meituan.msc.dev.networkmock;

import com.sankuai.meituan.retrofit2.Request;

import java.util.ArrayList;
import java.util.List;

class NetworkMockWhiteList {
    private static final NetworkMockWhiteList INSTANCE = new NetworkMockWhiteList();

    public static NetworkMockWhiteList getInstance() {
        return INSTANCE;
    }

    private final List<String> pathWhiteList = new ArrayList<>();

    private NetworkMockWhiteList() {
        pathWhiteList.add("config/msc/checkList");
    }

    public boolean mockIt(Request request) {
        String url = request.url();
        for (String pathPattern : pathWhiteList) {
            if (url.contains(pathPattern)) {
                return false;
            }
        }
        return true;
    }
}
