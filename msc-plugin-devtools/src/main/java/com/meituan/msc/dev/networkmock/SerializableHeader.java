package com.meituan.msc.dev.networkmock;

import android.support.annotation.Keep;

import com.sankuai.meituan.retrofit2.Header;

import java.io.Serializable;
import java.util.ArrayList;
import java.util.List;

@Keep
class SerializableHeader implements Serializable {
    private final String name;
    private final String value;

    public SerializableHeader(String name, String value) {
        this.name = name;
        this.value = value;
    }

    public String getName() {
        return name;
    }

    public String getValue() {
        return value;
    }

    public static List<SerializableHeader> buildList(List<Header> headerList) {
        if (headerList == null) {
            return null;
        }
        List<SerializableHeader> result = new ArrayList<>(headerList.size());
        for (Header header : headerList) {
            result.add(new SerializableHeader(header.getName(), header.getValue()));
        }
        return result;
    }
}
