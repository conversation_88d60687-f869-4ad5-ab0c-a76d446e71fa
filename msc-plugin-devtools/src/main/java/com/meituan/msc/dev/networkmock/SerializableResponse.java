package com.meituan.msc.dev.networkmock;

import android.support.annotation.Keep;
import android.support.annotation.Nullable;

import com.sankuai.meituan.retrofit2.Header;
import com.sankuai.meituan.retrofit2.ResponseBody;
import com.sankuai.meituan.retrofit2.raw.RawResponse;

import java.io.IOException;
import java.io.Serializable;
import java.util.ArrayList;
import java.util.List;

@Keep
class SerializableResponse implements RawResponse, Serializable {
    private final String url;
    private final int code;
    private final String reason;
    private final List<SerializableHeader> headers;
    private final SerializableResponseBody body;
    private transient List<Header> cachedHeaders = null;

    public SerializableResponse(String url, int code, String reason, List<SerializableHeader> headers, SerializableResponseBody body) {
        this.url = url;
        this.code = code;
        this.reason = reason;
        this.headers = headers;
        this.body = body;
    }

    @Override
    public String url() {
        return url;
    }

    @Override
    public int code() {
        return code;
    }

    @Override
    public String reason() {
        return reason;
    }

    @Nullable
    @Override
    public List<Header> headers() {
        if (cachedHeaders == null && headers != null) {
            cachedHeaders = new ArrayList<>(headers.size());
            for (SerializableHeader header : headers) {
                cachedHeaders.add(new Header(header.getName(), header.getValue()));
            }
        }
        return cachedHeaders;
    }

    @Override
    public ResponseBody body() {
        return body;
    }

    public static SerializableResponse build(RawResponse response) throws IOException {
        return new SerializableResponse(response.url(), response.code(), response.reason(), SerializableHeader.buildList(response.headers()),
                SerializableResponseBody.build(response.body()));
    }
}
