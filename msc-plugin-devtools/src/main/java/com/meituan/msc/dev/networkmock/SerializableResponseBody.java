package com.meituan.msc.dev.networkmock;

import android.support.annotation.Keep;

import com.meituan.dio.utils.IOUtil;
import com.sankuai.meituan.retrofit2.ResponseBody;

import java.io.ByteArrayInputStream;
import java.io.IOException;
import java.io.InputStream;
import java.io.Serializable;

@Keep
class SerializableResponseBody extends ResponseBody implements Serializable {
    private final String contentType;
    private final long contentLength;
    private final byte[] bodyData;

    public SerializableResponseBody(String contentType, long contentLength, byte[] bodyData) {
        this.contentType = contentType;
        this.contentLength = contentLength;
        this.bodyData = bodyData;
    }

    @Override
    public String contentType() {
        return contentType;
    }

    @Override
    public long contentLength() {
        return contentLength;
    }

    @Override
    public InputStream source() {
        return new ByteArrayInputStream(bodyData);
    }

    public static SerializableResponseBody build(ResponseBody responseBody) throws IOException {
        byte[] bodyData = IOUtil.readAllData(responseBody.source());
        return new SerializableResponseBody(responseBody.contentType(), responseBody.contentLength(), bodyData);
    }
}