package com.meituan.msc.dev.performance;

import android.text.TextUtils;

import com.meituan.msc.modules.reporter.MSCLog;
import com.sankuai.common.utils.IOUtils;

import java.io.BufferedReader;
import java.io.File;
import java.io.FileFilter;
import java.io.InputStreamReader;
import java.io.RandomAccessFile;
import android.os.Process;
/**
 * Created by bunnyblue on 4/12/18.
 */

public class CpuSampler {
    private final int pid;
    private volatile RandomAccessFile sysStatFile;
    private volatile RandomAccessFile statFile;
    private long totalCpu;
    private long appUsage;
    private long appUsageTotal;

    public CpuSampler(int pid) {
        this.pid = pid;
    }

    public final double cupSample() {
        double max;
        try {
            if (this.statFile == null) {
                this.statFile = new RandomAccessFile("/proc/" + this.pid + "/stat", "r");
            }
            this.statFile.seek(0);
            String readLine = this.statFile.readLine();
            if (TextUtils.isEmpty(readLine)) {
                return 0.0d;
            }
            String[] appCpuInfos = readLine.split(" ");
            if (appCpuInfos.length < 17) {
                return 0.0d;
            }
            long totalCpu;
            long parseLong;
            if (this.sysStatFile == null) {
                this.sysStatFile = new RandomAccessFile("/proc/stat", "r");
            }
            this.sysStatFile.seek(0);
            readLine = this.sysStatFile.readLine();
            if (TextUtils.isEmpty(readLine)) {
                totalCpu = 0;
            } else {
                String[] cpuInfos = readLine.split(" ");
                if (cpuInfos.length < 9) {
                    totalCpu = 0;
                } else {
                    totalCpu = Long.parseLong(cpuInfos[2])
                            + Long.parseLong(cpuInfos[3]) + Long.parseLong(cpuInfos[4])
                            + Long.parseLong(cpuInfos[6]) + Long.parseLong(cpuInfos[5])
                            + Long.parseLong(cpuInfos[7]) + Long.parseLong(cpuInfos[8]) + Long.parseLong(cpuInfos[9]);
                }
            }

            if (this.totalCpu != 0) {
                max = Math.max(0.0d, ((double) ((Long.parseLong(appCpuInfos[13]) - this.appUsage) * 100)) / ((double) (totalCpu - this.totalCpu))) + Math.max(0.0d, ((double) ((Long.parseLong(appCpuInfos[14]) - this.appUsageTotal) * 100)) / ((double) (totalCpu - this.totalCpu)));
            } else {
                max = 0.0d;
            }
            try {
                this.totalCpu = totalCpu;
                this.appUsage = Long.parseLong(appCpuInfos[13]);
                this.appUsageTotal = Long.parseLong(appCpuInfos[14]);
                return max;
            } catch (Exception e) {

                MSCLog.e("CpuSampler", "read pid stat file error: " + e);
                return max;
            }
        } catch (Exception e) {
            max = 0.0d;
            MSCLog.e("CpuSampler", "read pid stat file error: " + e);
            return max;
        }
    }

    /**
     * 获取Cpu平均使用率
     */
    public static double getCpuUsagePercent() {
        BufferedReader cpuReader = null;
        double cpuUsagePercent=0;
        try {
            java.lang.Process process = Runtime.getRuntime().exec("top -n 1 -p " + Process.myPid());
            cpuReader = new BufferedReader(new InputStreamReader(process.getInputStream()));
            String temp = "";
            while ((temp = cpuReader.readLine()) != null) {
                if (temp.contains(Process.myPid() + "")) {
                    break;
                }
            }
            if (temp == null) {
                return 0;
            }
            temp = temp.trim();
            String[] arrays = temp.split("\\s+");
            if (arrays.length < 12) {
                return 0;
            }
            cpuUsagePercent= Double.parseDouble(arrays[8]) / getCpuCounts();
        }catch (Throwable ex){
            MSCLog.e("CpuUseRate", "get cpu usage percent error: " + ex);
            return cpuUsagePercent;
        }finally {
            IOUtils.close(cpuReader);
        }
        return cpuUsagePercent;
    }

    /**
     * 获取Cpu个数，用于计算Cpu平均使用率
     */
    private static int getCpuCounts() {
        int cpuCounts = new File("/sys/devices/system/cpu/").listFiles(new FileFilter() {
            public boolean accept(File pathname) {
                String path = pathname.getName();
                if (path.startsWith("cpu")) {
                    for (int i = 3; i < path.length(); i++) {
                        if (path.charAt(i) < '0' || path.charAt(i) > '9') {
                            return false;
                        }
                    }
                    return true;
                }
                return false;
            }
        }).length;
        return cpuCounts == 0 ? 1 : cpuCounts;
    }
}
