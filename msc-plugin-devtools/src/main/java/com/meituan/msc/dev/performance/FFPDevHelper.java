package com.meituan.msc.dev.performance;

import android.content.Context;

import com.meituan.android.common.weaver.dev.WeaverDev;
import com.meituan.msc.dev.devtools.horn.HornMockConfig;
import com.meituan.msc.dev.devtools.horn.HornMockManager;
import com.meituan.msc.dev.utils.DevStorage;
import com.sankuai.android.jarvis.Jarvis;

public class FFPDevHelper {
    public static final String ENABLE_FFP = "msc_enable_ffp";

    private static boolean isEnableFFP = false;

    public static void initStatusFromLocalCache(Context context) {
        isEnableFFP = DevStorage.getBoolean(ENABLE_FFP, false);
        if (isEnableFFP) {
            WeaverDev.devChangeSample();
        }
    }

    public static boolean isEnableFFP() {
        return isEnableFFP;
    }

    public static void enable(Context context, boolean enable) {
        if (isEnableFFP == enable) {
            return;
        }
        if (enable) {
            // 启动秒开2.0 在非主线程中执行
            Jarvis.obtainExecutor().execute(new Runnable() {
                @Override
                public void run() {
                    HornMockManager.getInstance().mockWithHornDebug(context, "ffp_config", "enable", true);
//                    HornMockManager.getInstance().mockWithHornDebug(context, "ffp_config", "timeout", Integer.MAX_VALUE);
                }
            });
            HornMockManager.getInstance().addMockConfig(new HornMockConfig("msc_render_android", "enableMetricxFPS", true));
            HornMockManager.getInstance().addMockConfig(new HornMockConfig("msc_fps_android_group", "enableFPSMonitor", true));
            WeaverDev.devChangeSample();
        } else {
            // TODO
        }
        DevStorage.setBoolean(ENABLE_FFP, enable);
        isEnableFFP = enable;
    }
}
