package com.meituan.msc.dev.performance;

import android.app.Activity;
import android.app.Application;
import android.content.Context;
import android.os.Bundle;
import android.os.Handler;
import android.os.Looper;
import android.support.annotation.NonNull;
import android.support.annotation.Nullable;
import android.support.v4.view.ScrollingView;
import android.util.Log;
import android.view.Choreographer;
import android.view.Display;
import android.view.View;
import android.view.ViewTreeObserver;
import android.view.Window;
import android.view.WindowManager;
import android.widget.HorizontalScrollView;
import android.widget.ScrollView;

import com.meituan.msc.common.executor.MSCExecutors;
import com.meituan.msc.common.utils.DisplayUtil;
import com.meituan.msc.common.utils.ScreenDensityUtil;
import com.meituan.msc.modules.page.render.rn.MSCFpsHornConfig;
import com.meituan.msc.modules.page.render.rn.StackSampler;
import com.sankuai.android.jarvis.Jarvis;

import org.json.JSONArray;
import org.json.JSONException;
import org.json.JSONObject;

import java.lang.ref.WeakReference;
import java.util.ArrayList;
import java.util.concurrent.ScheduledExecutorService;
import java.util.concurrent.ScheduledFuture;
import java.util.concurrent.TimeUnit;

public class FPSMonitor {
    public static final String THREAD_NAME_UI = "ui";
    private static final int MAX_SCROLL_GAP_MS = 80;
    private static final long START_MONITOR_DELAY = 1000;
    private static volatile boolean inited = false;
    private WeakReference<Activity> mActivityRef;
    private WeakReference<View> lastScrollingViewRef;
    private float lastEventX;
    private float lastEventY;
    private boolean isHorizontalScroll;
    private final FPSMonitor.FpsScrollChangeListener mFpsScrollChangeListener;
    private final Handler mMainHandler;
    // 是否已经开始监控
    private boolean mStarted = false;
    // 是否处于滑动状态
    private boolean isScrolling = false;

    private boolean mScrollFpsEnabled;
    private float mRefreshRate = -1f;

    private ScheduledExecutorService timerService;
    private ScheduledFuture<?> recalculateFpsFuture;
    private FPSMonitor.ThreadFPSMonitor uiFpsMonitor = new FPSMonitor.ThreadFPSMonitor();
    private static volatile FPSMonitor mInstance;

    public static FPSMonitor getInstance() {
        if (mInstance == null) {
            synchronized (FPSMonitor.class) {
                if (mInstance == null) {
                    mInstance = new FPSMonitor();
                }
            }
        }
        return mInstance;
    }

    public static void init(Context context) {
        if (!inited) {
            inited = true;
            getInstance().initActivityLifecycleListener(context);
        }
    }

    public float getScreenDensity() {
        if (!isActivityValid()) return 0;
        Activity activity = mActivityRef.get();
        return ScreenDensityUtil.getDensity(activity);
    }


    private FPSMonitor() {
        mFpsScrollChangeListener = new FPSMonitor.FpsScrollChangeListener();
        mMainHandler = new Handler(Looper.getMainLooper());
    }

    private boolean isActivityValid() {
        return mActivityRef != null && mActivityRef.get() != null
                && !mActivityRef.get().isFinishing() && !mActivityRef.get().isDestroyed();
    }

    private boolean isLastScrollingViewNotNull() {
        return lastScrollingViewRef != null && lastScrollingViewRef.get() != null;
    }

    public void setActivity(Activity activity) {
        if (mActivityRef == null || mActivityRef.get() != activity) {
            Log.d("FPSMonitor", (mActivityRef == null || mActivityRef.get() == null) ? "null" : mActivityRef.get().toString());
            unRegisterGlobalScrollCallback();
            mActivityRef = new WeakReference<>(activity);
            registerGlobalScrollCallback();
            Log.d("FPSMonitor", activity.toString());
        }
        initRefreshRate();
    }

    private void initRefreshRate() {
        if (mRefreshRate == -1f) {
            if (isActivityValid()) {
                WindowManager windowManager = mActivityRef.get().getWindowManager();
                if (windowManager != null) {
                    Display display = windowManager.getDefaultDisplay();
                    if (display != null) {
                        mRefreshRate = display.getRefreshRate();
                    }
                }
            }
            if (mRefreshRate <= 0) {
                mRefreshRate = 60;
            }
        }
    }

    private void initActivityLifecycleListener(Context context) {
        ((Application) context.getApplicationContext()).registerActivityLifecycleCallbacks(new Application.ActivityLifecycleCallbacks() {
            @Override
            public void onActivityCreated(@NonNull Activity activity, @Nullable Bundle savedInstanceState) {

            }

            @Override
            public void onActivityStarted(@NonNull Activity activity) {

            }

            @Override
            public void onActivityResumed(@NonNull Activity activity) {
                setActivity(activity);
                startMonitorFPS();
            }

            @Override
            public void onActivityPaused(@NonNull Activity activity) {
                stopMonitorFPS();
            }

            @Override
            public void onActivityStopped(@NonNull Activity activity) {

            }

            @Override
            public void onActivitySaveInstanceState(@NonNull Activity activity, @NonNull Bundle outState) {

            }

            @Override
            public void onActivityDestroyed(@NonNull Activity activity) {

            }
        });
    }


    private class ThreadFPSMonitor implements Choreographer.FrameCallback {
        // 用于存储实时FPS的数组，每秒钟记录一个值
        private ArrayList<Double> fpsArray = new ArrayList<>();
        private ArrayList<Double> scrollFpsArray = new ArrayList<>();
        private ArrayList<JSONObject> anrArray = new ArrayList<>();
        private volatile Choreographer choreographer = null;
        private volatile StackSampler stackSampler = new StackSampler();

        // 用于计算帧率的变量
        private long lastFrameTs = 0;
        private long frameDuration = 0;
        private int frameCount = 0;

        //用于计算滚动帧率的变量
        private int scrollFrameCount = 0;
        private long scrollFrameDuration = 0;

        @Override
        public void doFrame(long frameTime) {
            if (!mStarted || !isActivityValid()) {
                return;
            }

            if (lastFrameTs == 0) {
                lastFrameTs = frameTime;
            } else {
                if ((frameTime - lastFrameTs) > MSCFpsHornConfig.get().getLagThresholdNanos()) {
                    ArrayList<String> stackEntries = stackSampler.getThreadStackEntries();
                    JSONObject lagInfo = new JSONObject();
                    try {
                        lagInfo.put("isScroll", isScrolling);
                        lagInfo.put("log", stackEntries);
                    } catch (JSONException e) {
                        e.printStackTrace();
                    }
                    anrArray.add(lagInfo);
                }
                if (frameTime - lastFrameTs > 0) {
                    frameDuration += frameTime - lastFrameTs;
                    frameCount++;
                    if (isScrolling) {
                        scrollFrameDuration += frameTime - lastFrameTs;
                        scrollFrameCount++;
                    }
                }
                lastFrameTs = frameTime;
            }
            stackSampler.removeAllCallbacks();
            stackSampler.doSampleDelayed(MSCFpsHornConfig.get().getStackTraceSampleDelay());
            choreographer.postFrameCallback(this);
        }

        void start() {
            reset();
            choreographer = Choreographer.getInstance();
            choreographer.postFrameCallbackDelayed(this, START_MONITOR_DELAY);
        }

        void reset() {
            lastFrameTs = 0;
            frameDuration = 0;
            frameCount = 0;
            scrollFrameCount = 0;
            scrollFrameDuration = 0;
        }

        void clearAllFPSValues() {
            fpsArray.clear();
            scrollFpsArray.clear();
            anrArray.clear();
        }

        void calculateCurrentFPS() {
            double fps = getFPS();
            if (fps > 0) {
                fpsArray.add(fps);
            }
            double scrollFps = getScrollFPS();
            if (scrollFps > 0) {
                scrollFpsArray.add(scrollFps);
            }
            reset();
        }

        double getFPS() {
            double fps = -1;
            if (frameCount > 0 && frameDuration > 0) {
                fps = frameCount * 1000000000.0 / frameDuration;
                if (fps > mRefreshRate) {
                    fps = mRefreshRate;
                }
            }
            return fps;
        }

        double getScrollFPS() {
            double fps = -1;
            if (mScrollFpsEnabled) {
                long costTime = scrollFrameDuration;
                int count = scrollFrameCount;
                if (count > 0 && costTime > 0) {
                    fps = count * 1000000000.0 / costTime;
                    if (fps > mRefreshRate) {
                        fps = mRefreshRate;
                    }
                }
            }
            return fps;
        }

        ArrayList<Double> getFpsArray() {
            return fpsArray;
        }

        ArrayList<Double> getScrollFpsArray() {
            return scrollFpsArray;
        }

        ArrayList<JSONObject> getAnrArray() {
            return new ArrayList<>(anrArray);
        }

        double getAvgFps() {
            return calculateAvg(fpsArray);
        }

        double getAvgScrollFps() {
            return calculateAvg(scrollFpsArray);
        }

        double calculateAvg(ArrayList<Double> array) {
            double sum = 0;
            int size = 0;
            for (Double data : array) {
                if (data > 0) {
                    sum += data;
                    size++;
                }
            }
            if (size == 0) {
                return -1;
            }
            return sum / size;
        }

    }


    public int startMonitorFPS() {
        Log.d("FPSMonitor", "startMonitorFPS");
        start();
        return getCurrentScrollOffset();
    }

    public JSONObject stopMonitorFPS() {
        JSONObject result = getAllScrollFPSValues();
        stop();
        Log.d("FPSMonitor", "stopMonitorFPS: " + result.toString());
        return result;
    }

    public int getCurrentScrollOffset() {
        if (isActivityValid() && isLastScrollingViewNotNull()) {
            Activity activity = mActivityRef.get();
            View lastScrollingView = lastScrollingViewRef.get();
            if (lastScrollingView instanceof ScrollingView) {
                if (isHorizontalScroll) {
                    return (int) ScreenDensityUtil.px2dp(activity, ((ScrollingView) lastScrollingView).computeHorizontalScrollOffset() * 1.0);
                } else {
                    return (int) ScreenDensityUtil.px2dp(activity, ((ScrollingView) lastScrollingView).computeVerticalScrollOffset() * 1.0);
                }
            } else if (lastScrollingView instanceof ScrollView) {
                return (int) ScreenDensityUtil.px2dp(activity, ((ScrollView) lastScrollingView).getScrollY());
            } else if (lastScrollingView instanceof HorizontalScrollView) {
                return (int) ScreenDensityUtil.px2dp(activity, ((HorizontalScrollView) lastScrollingView).getScrollX());
            }
        }
        return 0;
    }


    public void start() {
        if (mStarted || isActivityValid()) {
            return;
        }
        mStarted = true;
        mScrollFpsEnabled = false;

        MSCExecutors.runOnUiThread(new Runnable() {
            @Override
            public void run() {
                uiFpsMonitor.start();
            }
        });


        timerService = Jarvis.newSingleThreadScheduledExecutor("recalculateThread");
        recalculateFpsFuture = timerService.scheduleAtFixedRate(
                new Runnable() {
                    @Override
                    public void run() {
                        caculateCurrentFPS();
                    }
                },
                START_MONITOR_DELAY, START_MONITOR_DELAY,
                TimeUnit.MILLISECONDS);

        registerGlobalScrollCallback();
    }

    public void stop() {
        if (!mStarted || !isActivityValid()) {
            return;
        }
        recalculateFpsFuture.cancel(true);
        timerService.shutdown();
        unRegisterGlobalScrollCallback();
        clearAllFPSValues();
        mStarted = false;
        if (lastScrollingViewRef != null) {
            lastScrollingViewRef.clear();
        }
    }

    public void setScrollingView(View view, float eventX, float eventY) {
        lastScrollingViewRef = new WeakReference<>(view);
        isHorizontalScroll = (lastEventX != eventX);
        lastEventX = eventX;
        lastEventY = eventY;
    }

    private void caculateCurrentFPS() {
        uiFpsMonitor.calculateCurrentFPS();
    }

    private void clearAllFPSValues() {
        uiFpsMonitor.clearAllFPSValues();
    }


    public JSONObject getAllScrollFPSValues() {
        JSONObject result = new JSONObject();
        try {
            result.put("FPS", new JSONArray(uiFpsMonitor.getScrollFpsArray()));
            result.put("ANR", new JSONArray(uiFpsMonitor.getAnrArray()));

        } catch (JSONException e) {
            e.printStackTrace();
        }
        return result;
    }

    private void registerGlobalScrollCallback() {
        if (!isActivityValid()) {
            return;
        }
        Window win = mActivityRef.get().getWindow();
        if (win == null) {
            return;
        }
        View decor = win.getDecorView();
        if (decor != null) {
            try {
                decor.getViewTreeObserver().addOnScrollChangedListener(mFpsScrollChangeListener);
            } catch (Exception ignore) {

            }
        }
    }

    private void unRegisterGlobalScrollCallback() {
        if (!isActivityValid()) {
            return;
        }
        Window win = mActivityRef.get().getWindow();
        if (win == null) {
            return;
        }
        View decor = win.getDecorView();
        if (decor != null) {
            try {
                decor.getViewTreeObserver().removeOnScrollChangedListener(mFpsScrollChangeListener);
            } catch (Exception ignore) {

            }
        }
    }

    private class FpsScrollChangeListener implements ViewTreeObserver.OnScrollChangedListener {
        private Runnable scrollStopped = new Runnable() {
            @Override
            public void run() {
                isScrolling = false;
                mScrollFpsEnabled = true;
            }
        };

        @Override
        public void onScrollChanged() {
            mMainHandler.removeCallbacks(scrollStopped);
            if (!isScrolling) {
                isScrolling = true;
            }
            mMainHandler.postDelayed(scrollStopped, MAX_SCROLL_GAP_MS);
        }
    }

    public JSONObject getScreenInfo() {
        JSONObject result = new JSONObject();
        if (isActivityValid()) {
            try {
                DisplayUtil.setDisplayMetrics(mActivityRef.get());
                result.put("width", DisplayUtil.getScreenWidthReal());
                result.put("height", DisplayUtil.getScreenHeightReal());
                result.put("scale", ScreenDensityUtil.getDensity(mActivityRef.get()));
            } catch (JSONException e) {
                e.printStackTrace();
            }
            Log.d("FPSMonitor", "screeninfo: " + result.toString());
        }
        return result;
    }

}
