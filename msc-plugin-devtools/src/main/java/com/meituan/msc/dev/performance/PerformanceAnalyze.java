package com.meituan.msc.dev.performance;

import android.app.Activity;
import android.view.View;

import com.meituan.msc.dev.command.ArgumentName;
import com.meituan.msc.dev.command.Command;
import com.meituan.msc.dev.command.CommandGroup;
import com.meituan.msc.devsupport.devtools.devtools.DevToolsHelper;
import com.meituan.msc.modules.container.ApplicationLifecycleMonitor;
import com.meituan.msc.modules.reporter.MSCLog;
import com.meituan.msc.util.perf.PerfTrace;

@CommandGroup(name = "performance")
public class PerformanceAnalyze {
    /**
     * adb shell am broadcast -p com.sankuai.meituan -a com.meituan.msc.dev.command -e command performance.analyzePage
     * @return
     */
    @Command
    public static String analyzePage(@ArgumentName("startTime") Long startTimeStamp, @ArgumentName("endTime") Long endTimeStamp) {
        // 获取当前Activity
        Activity activity = ApplicationLifecycleMonitor.ALL.getLastActivity();
        if (activity == null) {
            return null;
        }
        PerfTrace.instant("ViewInfoAnalyse");
        View rootView = activity.getWindow().findViewById(android.R.id.content);
        if (startTimeStamp == null) {
            startTimeStamp = 0L;
        }
        if (endTimeStamp == null) {
            endTimeStamp = 0L;
        }
        String dumpInfo = DevToolsHelper.getDevToolsHelper().dumpInfo(rootView, startTimeStamp, endTimeStamp);
        // TODO chdc 阻塞当前线程，在主线程执行该操作
        MSCLog.i("MSCViewInfo", dumpInfo);
        return dumpInfo;
    }
}
