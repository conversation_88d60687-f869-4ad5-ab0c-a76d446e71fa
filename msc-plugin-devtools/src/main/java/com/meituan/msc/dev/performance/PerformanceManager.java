package com.meituan.msc.dev.performance;

import android.content.Context;
import android.text.TextUtils;

import com.meituan.msc.modules.devtools.IPerformanceManager;
import com.meituan.msc.modules.manager.MSCModule;
import com.meituan.msc.modules.manager.ModuleName;
import com.meituan.msc.modules.reporter.ReporterFields;
import com.meituan.msc.common.utils.DeviceUtil;
import com.sankuai.android.jarvis.Jarvis;

import java.util.concurrent.ScheduledExecutorService;
import java.util.concurrent.ScheduledFuture;
import java.util.concurrent.TimeUnit;


/**
 * 用来管理PerformanceView的类
 */

@ModuleName(name = "PerformanceManager")
public class PerformanceManager extends MSCModule implements IPerformanceManager {
    private PerformanceView mPerformanceView;
    private PerformanceSwitchManager mPerformanceManagerSwitch = new PerformanceSwitchManager();
    private ScheduledExecutorService mScheduledExecutorService;
    private ScheduledFuture<?> cpuUpdateFuture;
    public boolean isPerformanceManagerOpened = false;

    /**
     * 打开性能面板
     * @param context
     * @param appId    小程序Id
     * @param isLaunch App启动过程 true 小程序启动 false 非小程序启动
     */
    public void showView(Context context, String appId, boolean isLaunch) {
        if (isLaunch) {
            if (mPerformanceManagerSwitch.isSwitchOpened(context, appId)) {
                open(context, appId);
            }
        } else {
            mPerformanceManagerSwitch.openSwitch(context, appId);
            open(context, appId);
        }
    }

    /**
     * 关闭性能面板
     * @param context
     * @param activityDestroy 小程序退出
     */
    public void closeView(Context context, String appId, boolean activityDestroy) {
        if (!activityDestroy) {
            mPerformanceManagerSwitch.closeSwitch(context, appId);
        }
        close();
    }

    /**
     * 开启PerformanceManager，更新状态
     */
    public void open(Context context, String appId) {
        isPerformanceManagerOpened = true;
        mPerformanceView = new PerformanceView(context);
        updateCpuAndMemory(context);
    }

    /**
     * 关闭PerformanceManager
     */
    public void close() {
        isPerformanceManagerOpened = false;
        //关闭Cpu、内存的定时
        if (cpuUpdateFuture != null) {
            cpuUpdateFuture.cancel(false);
            cpuUpdateFuture = null;
        }
        if (mScheduledExecutorService != null) {
            mScheduledExecutorService.shutdown();
            mScheduledExecutorService.isTerminated();
        }
        //关闭PerformanceView
        if (mPerformanceView != null) {
            mPerformanceView.removeView();
            mPerformanceView = null;
        }
    }

    /**
     * PerformanceView每1s更新CPU、内存参数的api
     */
    public void updateCpuAndMemory(final Context context) {
        //定时操作放到线程池中去做
        mScheduledExecutorService = Jarvis.newScheduledThreadPool("PerformanceManager-report", 1);
        cpuUpdateFuture = mScheduledExecutorService.scheduleAtFixedRate(new Runnable() {
            @Override
            public void run() {
                if (isPerformanceManagerOpened) {
                    mPerformanceView.setCpuUseRate(CpuSampler.getCpuUsagePercent());
                    mPerformanceView.setMemoryUsage(DeviceUtil.getUsedMemory(context) / (1024 * 1024 * 8));
                }
            }
        }, 0, 1000, TimeUnit.MILLISECONDS);
    }


    /**
     * PerformanceView更新Page相关的参数的api
     */
    @Override
    public void reportData(String type, long value) {
        if (isPerformanceManagerOpened) {
            if (TextUtils.equals(ReporterFields.REPORT_PAGE_DURATION_PAGE_START_FIRST_RENDER, type)) {
                mPerformanceView.setFirstRenderTime(value);
            } else if (TextUtils.equals(ReporterFields.REPORT_PAGE_LOAD_POINT_FIRST_RENDER, type)) {
                mPerformanceView.setPageSwitchingTime(value);
            } else if (TextUtils.equals(ReporterFields.REPORT_LAUNCH_POINT_FULL_FIRST_RENDER, type)) {
                mPerformanceView.setStartupTimeConsuming(value);
            }
        }
    }

    /**
     * PerformanceView更新帧率的api
     */
    public void updateFrameData(String type, long frameData) {
        mPerformanceView.setFrameRate(frameData);
    }

    /**
     * PerformanceView更新数据缓存的api，用FileModule提供的数据
     */
    public void updateDataCache(long cacheData) {
        mPerformanceView.setDataCache(cacheData);
    }

    /**
     * PerformanceView更新文件缓存的api，用StorageModule提供的数据
     */
    public void updateFileCache(long cacheData) {
        mPerformanceView.setFileCache(cacheData);
    }


    public boolean isPerformanceManagerOpened() {
        return isPerformanceManagerOpened;
    }
}
