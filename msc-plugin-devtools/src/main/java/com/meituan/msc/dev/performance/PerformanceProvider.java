package com.meituan.msc.dev.performance;

import com.meituan.msc.modules.devtools.IPerformanceManager;
import com.meituan.msc.modules.devtools.IPerformanceManagerProvider;
import com.sankuai.meituan.serviceloader.annotation.ServiceLoaderInterface;


@ServiceLoaderInterface(key = "msc_performance_provider", interfaceClass = IPerformanceManagerProvider.class)
public class PerformanceProvider implements IPerformanceManagerProvider {
    private PerformanceManager mPerformanceManager;

    @Override
    public IPerformanceManager getPerformance() {
        if (mPerformanceManager == null) {
            mPerformanceManager = new PerformanceManager();
        }
        return mPerformanceManager;
    }
}
