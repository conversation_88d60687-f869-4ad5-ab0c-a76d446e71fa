package com.meituan.msc.dev.performance;


import android.app.Activity;
import android.content.Context;
import android.view.Gravity;
import android.view.LayoutInflater;
import android.view.View;
import android.view.ViewGroup;
import android.widget.FrameLayout;
import android.widget.TextView;

import com.meituan.msc.dev.R;
import com.meituan.msc.common.executor.MSCExecutors;
import com.meituan.msc.common.utils.DisplayUtil;

/**
 * 开发者工具调试面板类
 */
public class PerformanceView extends View {
    private static final String TAG = "PerformanceView";
    private TextView mPerformanceData;
    private TextView mCpuUseRate;
    private TextView mMemoryUsage;
    private TextView mPageSwitchingTime;
    private TextView mStartupTimeConsuming;
    private TextView mFirstRenderTime;
    private TextView mFrameRate;
    private TextView mDataCache;
    private TextView mFileCache;
    private View mView;
    private ViewGroup mDecorView;

    public PerformanceView(Context context) {
        super(context);
        mView = LayoutInflater.from(context).inflate(R.layout.msc_performance_dialog, null);
        mPerformanceData = (TextView) mView.findViewById(R.id.performance_data);
        mCpuUseRate = (TextView) mView.findViewById(R.id.cpu_use_rate);
        mMemoryUsage = (TextView) mView.findViewById(R.id.memory_usage);
        mPageSwitchingTime = (TextView) mView.findViewById(R.id.page_switching_time);
        mStartupTimeConsuming = (TextView) mView.findViewById(R.id.startup_time_consuming);
        mFirstRenderTime = (TextView) mView.findViewById(R.id.firstrender_time);
        mFrameRate = (TextView) mView.findViewById(R.id.frame_rate);
        mDataCache = (TextView) mView.findViewById(R.id.data_cache);
        mFileCache = (TextView) mView.findViewById(R.id.file_cache);

        addView();
    }

    public void addView() {
        FrameLayout.LayoutParams layoutParam = new FrameLayout.LayoutParams(ViewGroup.LayoutParams.WRAP_CONTENT, ViewGroup.LayoutParams.WRAP_CONTENT);
        layoutParam.gravity = Gravity.RIGHT;
        layoutParam.width = (int) ( DisplayUtil.getScreenWidth(getContext())/2);
        Context context = getContext();
        if (context != null) {
            layoutParam.topMargin = DisplayUtil.getStatusBarHeight() + 200;
            mDecorView = (ViewGroup) ((Activity) context).getWindow().getDecorView();
            mDecorView.addView(mView, layoutParam);
        }
    }

    public void removeView() {
        mDecorView.removeView(mView);
    }

    /**
     * 设置Cpu使用率
     */
    public void setCpuUseRate(final double cpuRate) {
        MSCExecutors.runOnUiThread(new Runnable() {
            @Override
            public void run() {
                mCpuUseRate.setText(String.format(getResources().getString(R.string.mmp_unit_cpu_use_rate), cpuRate));
            }
        });
    }

    /**
     * 设置内存
     */
    public void setMemoryUsage(final long memoryUsage) {
        MSCExecutors.runOnUiThread(new Runnable() {
            @Override
            public void run() {
                mMemoryUsage.setText(String.format(getResources().getString(R.string.mmp_unit_memory_usage), memoryUsage));
            }
        });
    }

    /**
     * 设置页面切换耗时时间
     */
    public void setPageSwitchingTime(final long pageSwitchingTime) {
        MSCExecutors.runOnUiThread(new Runnable() {
            @Override
            public void run() {
                mPageSwitchingTime.setText(String.format(getResources().getString(R.string.mmp_unit_milli_second), pageSwitchingTime));
            }
        });
    }

    /**
     * 设置启动耗时
     */
    public void setStartupTimeConsuming(final long startupTimeConsuming) {
        MSCExecutors.runOnUiThread(new Runnable() {
            @Override
            public void run() {
                mStartupTimeConsuming.setText(String.format(getResources().getString(R.string.mmp_unit_milli_second), startupTimeConsuming));
            }
        });
    }

    /**
     * 设置初次渲染耗时
     */
    public void setFirstRenderTime(final long firstRenderTime) {
        MSCExecutors.runOnUiThread(new Runnable() {
            @Override
            public void run() {
                mFirstRenderTime.setText(String.format(getResources().getString(R.string.mmp_unit_milli_second), firstRenderTime));
            }
        });
    }

    /**
     * 设置帧率
     */
    public void setFrameRate(final long frameRate) {
        MSCExecutors.runOnUiThread(new Runnable() {
            @Override
            public void run() {
                mFrameRate.setText(String.format(getResources().getString(R.string.mmp_unit_frame_rate), frameRate));
            }
        });
    }

    /**
     * 设置数据缓存
     */
    public void setDataCache(final long cacheData) {
        MSCExecutors.runOnUiThread(new Runnable() {
            @Override
            public void run() {
                mDataCache.setText(String.format(getResources().getString(R.string.mmp_unit_cache_data), cacheData));
            }
        });
    }

    /**
     * 设置文件缓存
     */
    public void setFileCache(final long cacheData) {
        MSCExecutors.runOnUiThread(new Runnable() {
            @Override
            public void run() {
                mFileCache.setText(String.format(getResources().getString(R.string.mmp_unit_cache_data), cacheData));
            }
        });
    }

}
