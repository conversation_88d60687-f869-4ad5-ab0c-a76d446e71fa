package com.meituan.msc.dev.performance;

import android.app.Activity;
import android.app.Application;
import android.content.Context;
import android.os.Bundle;
import android.support.annotation.NonNull;
import android.support.annotation.Nullable;

import com.meituan.msc.modules.container.MSCActivity;

public class TopActivityGetter implements Application.ActivityLifecycleCallbacks {
    private static final TopActivityGetter INSTANCE = new TopActivityGetter();

    private TopActivityGetter() {
    }

    private Activity topActivity;

    @Override
    public void onActivityCreated(@NonNull Activity activity, @Nullable Bundle savedInstanceState) {

    }

    @Override
    public void onActivityStarted(@NonNull Activity activity) {

    }

    @Override
    public void onActivityResumed(@NonNull Activity activity) {
        topActivity = activity;
    }

    @Override
    public void onActivityPaused(@NonNull Activity activity) {

    }

    @Override
    public void onActivityStopped(@NonNull Activity activity) {

    }

    @Override
    public void onActivitySaveInstanceState(@NonNull Activity activity, @NonNull Bundle outState) {

    }

    @Override
    public void onActivityDestroyed(@NonNull Activity activity) {

    }

    public static void register(Context context) {
        ((Application) context.getApplicationContext()).registerActivityLifecycleCallbacks(INSTANCE);
    }

    public static Activity getTopActivity() {
        return INSTANCE.topActivity;
    }

    public static MSCActivity getTopMSCActivity() {
        return INSTANCE.topActivity instanceof MSCActivity ? (MSCActivity) INSTANCE.topActivity : null;
    }
}
