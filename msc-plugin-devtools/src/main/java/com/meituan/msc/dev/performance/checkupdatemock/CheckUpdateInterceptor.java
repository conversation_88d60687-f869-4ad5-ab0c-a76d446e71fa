package com.meituan.msc.dev.performance.checkupdatemock;

import android.support.annotation.NonNull;

import com.meituan.android.mercury.msc.adaptor.bean.MSCAppMetaInfo;
import com.meituan.android.mercury.msc.adaptor.bean.MSCMetaInfo;
import com.meituan.met.mercury.load.bean.MSCAppIdPublishId;
import com.meituan.msc.jse.bridge.ConversionUtil;
import com.sankuai.meituan.retrofit2.Header;
import com.sankuai.meituan.retrofit2.Headers;
import com.sankuai.meituan.retrofit2.Interceptor;
import com.sankuai.meituan.retrofit2.Request;
import com.sankuai.meituan.retrofit2.RequestBody;
import com.sankuai.meituan.retrofit2.ResponseBody;
import com.sankuai.meituan.retrofit2.raw.RawResponse;

import org.json.JSONArray;
import org.json.JSONException;
import org.json.JSONObject;

import java.io.ByteArrayInputStream;
import java.io.ByteArrayOutputStream;
import java.io.IOException;
import java.io.InputStream;
import java.util.ArrayList;
import java.util.Collections;
import java.util.HashSet;
import java.util.List;
import java.util.Set;


class CheckUpdateInterceptor implements Interceptor {
    public static final String TAG = "CheckUpdateInterceptor";

    public static final String MSC_UPDATE_PATH = "config/msc/checkList";

    private final CheckUpdateMockManager checkUpdateMockManager;

    public CheckUpdateInterceptor(CheckUpdateMockManager checkUpdateMockManager) {
        this.checkUpdateMockManager = checkUpdateMockManager;
    }

    @Override
    public RawResponse intercept(Chain chain) throws IOException {
        Request request = chain.request();
        if (checkUpdateMockManager.isEnable()) {
            String url = request.url();
            if (url.contains(MSC_UPDATE_PATH)) {
                // 只拦截MSC的请求
                RawResponse response = chain.proceed(request);
                String json = response.body().string();
                MSCMetaInfo metaInfo = ConversionUtil.fromJsonString(json, MSCMetaInfo.class);
                if (metaInfo != null) {
                    return buildNewResponse(changeMetaInfo(metaInfo, getAppIdListFromRequest(request)), response);
                }
                return response;
            }
        }
        return chain.proceed(request);
    }

    private List<String> getAppIdListFromRequest(Request request) throws IOException {
        RequestBody requestBody = request.body();
        ByteArrayOutputStream bos = new ByteArrayOutputStream();
        requestBody.writeTo(bos);
        String data = new String(bos.toByteArray());
        try {
            JSONObject json = new JSONObject(data);
            JSONArray jsonArray = json.optJSONArray("mscAppIds");
            if (jsonArray == null) {
                return null;
            }
            List<String> list = new ArrayList<>(jsonArray.length());
            for (int i = 0; i < jsonArray.length(); i++) {
                list.add(jsonArray.getString(i));
            }
            return list;
        } catch (JSONException e) {
            throw new RuntimeException(e);
        }
    }

    private MSCAppMetaInfo mockAppMetaInfo(String appId, MSCAppMetaInfo appMetaInfo) throws IOException {
        MSCAppMetaInfo mockedAppMetaInfo = checkUpdateMockManager.getMockedAppMetaInfo(appId, appMetaInfo);
        return mockedAppMetaInfo != null ? mockedAppMetaInfo : appMetaInfo;
    }

    @NonNull
    private MSCMetaInfo changeMetaInfo(@NonNull MSCMetaInfo metaInfo, List<String> toUpdateAppIds) throws IOException {
        boolean checkUpdateAll = toUpdateAppIds == null || toUpdateAppIds.isEmpty();
        if (toUpdateAppIds == null) {
            toUpdateAppIds = Collections.emptyList();
        }
        List<MSCAppMetaInfo> oldAppList = metaInfo.getMscApps();
        List<MSCAppMetaInfo> newMscApps = new ArrayList<>(oldAppList.size());
        Set<String> mockedAppList = checkUpdateMockManager.getMockedAppIds();
        Set<String> handledAppSet = new HashSet<>(mockedAppList);
        for (MSCAppMetaInfo appMetaInfo : oldAppList) {
            String appId = appMetaInfo.getAppId();
            MSCAppMetaInfo mockAppMetaInfo = mockAppMetaInfo(appId, appMetaInfo);
            if (mockAppMetaInfo != null) {
                newMscApps.add(mockAppMetaInfo);
            }
            toUpdateAppIds.remove(appId);
            handledAppSet.remove(appId);
        }
        for (String updateAppId : toUpdateAppIds) {
            MSCAppMetaInfo mockAppMetaInfo = mockAppMetaInfo(updateAppId, null);
            if (mockAppMetaInfo != null) {
                newMscApps.add(mockAppMetaInfo);
            }
        }
        if (checkUpdateAll && !handledAppSet.isEmpty()) {
            for (String updateAppId : handledAppSet) {
                MSCAppMetaInfo mockAppMetaInfo = mockAppMetaInfo(updateAppId, null);
                if (mockAppMetaInfo != null) {
                    newMscApps.add(mockAppMetaInfo);
                }
            }
        }
        oldAppList.clear();
        oldAppList.addAll(newMscApps);

        // 处理mscAppVersionsToDelete字段，被mock的App的信息不能被删掉
        List<MSCAppIdPublishId> oldMscAppVersionsToDelete = metaInfo.getMscAppVersionsToDelete();
        List<MSCAppIdPublishId> newMscAppVersionsToDelete = new ArrayList<>(oldMscAppVersionsToDelete.size());
        if (oldMscAppVersionsToDelete != null && !oldMscAppVersionsToDelete.isEmpty()) {
            for (MSCAppIdPublishId publishId : oldMscAppVersionsToDelete) {
                if (!mockedAppList.contains(publishId.getAppId())) {
                    newMscAppVersionsToDelete.add(publishId);
                }
            }
        }
        oldMscAppVersionsToDelete.clear();
        oldMscAppVersionsToDelete.addAll(newMscAppVersionsToDelete);

        return metaInfo;
    }

    private RawResponse buildNewResponse(@NonNull MSCMetaInfo metaInfo, RawResponse originalResponse) {
        final String url = originalResponse.url();
        final String reason = originalResponse.reason();
        final int code = originalResponse.code();
        final Headers strippedHeaders = Headers.of(originalResponse.headers()).newBuilder()
                .removeAll("Content-Encoding")
                .removeAll("Content-Length")
                .build();

        byte[] newResponseData = ConversionUtil.toJsonString(metaInfo).getBytes();
        final ResponseBody originalBody = originalResponse.body();
        final ResponseBody finalBody = new ResponseBody() {
            @Override
            public String contentType() {
                return originalBody.contentType();
            }

            @Override
            public long contentLength() {
                return -1;
            }

            @Override
            public InputStream source() {
                return new ByteArrayInputStream(newResponseData);
            }

            @Override
            public void close() {
                originalBody.close();
            }
        };
        return new RawResponse() {
            @Override
            public String url() {
                return url;
            }

            @Override
            public int code() {
                return code;
            }

            @Override
            public String reason() {
                return reason;
            }

            @Override
            public List<Header> headers() {
                return strippedHeaders.get();
            }

            @Override
            public ResponseBody body() {
                return finalBody;
            }
        };
    }
}
