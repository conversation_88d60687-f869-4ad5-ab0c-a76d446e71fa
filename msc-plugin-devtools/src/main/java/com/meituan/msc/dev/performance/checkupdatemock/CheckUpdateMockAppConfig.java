package com.meituan.msc.dev.performance.checkupdatemock;

import android.support.annotation.Keep;

import com.meituan.msc.common.support.java.util.Objects;

@Keep
class CheckUpdateMockAppConfig {
    public CheckUpdateMockAppConfig(String appId, String checkUpdateUrl) {
        this.appId = appId;
        this.checkUpdateUrl = checkUpdateUrl;
    }

    String appId;
    String checkUpdateUrl;

    @Override
    public boolean equals(Object o) {
        if (this == o) return true;
        if (o == null || getClass() != o.getClass()) return false;
        CheckUpdateMockAppConfig that = (CheckUpdateMockAppConfig) o;
        return Objects.equals(appId, that.appId) &&
                Objects.equals(checkUpdateUrl, that.checkUpdateUrl);
    }

    @Override
    public int hashCode() {
        return Objects.hash(appId, checkUpdateUrl);
    }
}
