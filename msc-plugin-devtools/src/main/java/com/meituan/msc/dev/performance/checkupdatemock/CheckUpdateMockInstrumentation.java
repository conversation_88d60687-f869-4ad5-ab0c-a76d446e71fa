package com.meituan.msc.dev.performance.checkupdatemock;

import android.app.Activity;
import android.content.Context;
import android.content.Intent;

import com.meituan.msc.modules.container.IntentInstrumentation;

public class CheckUpdateMockInstrumentation extends IntentInstrumentation {
    public CheckUpdateMockInstrumentation(Context context) {
        super(context);
    }

    @Override
    public void callActivityOnNewIntent(Activity activity, Intent intent) {
        if (!isIntentProcessed(intent)) {
            CheckUpdateMockManager.getInstance().handleCheckUpdateUrl(intent);
        }
        super.callActivityOnNewIntent(activity, intent);
    }

    @Override
    public boolean processIntent(Context context, Intent originalIntent, boolean isStartActivity) {
        CheckUpdateMockManager.getInstance().handleCheckUpdateUrl(originalIntent);
        return false;
    }
}
