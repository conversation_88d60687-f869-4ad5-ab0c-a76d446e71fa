package com.meituan.msc.dev.performance.checkupdatemock;

import android.content.Context;
import android.content.Intent;
import android.support.annotation.NonNull;
import android.text.TextUtils;

import com.google.gson.reflect.TypeToken;
import com.meituan.android.mercury.msc.adaptor.bean.MSCAppMetaInfo;
import com.meituan.android.mercury.msc.adaptor.bean.MSCMetaInfo;
import com.meituan.android.mercury.msc.adaptor.core.MSCMetaInfoCache;
import com.meituan.met.mercury.load.bean.MSCAppIdPublishId;
import com.meituan.msc.common.support.java.util.Objects;
import com.meituan.msc.common.utils.IntentUtil;
import com.meituan.msc.dev.utils.DevStorage;
import com.meituan.msc.extern.MSCCallFactory;
import com.meituan.msc.jse.bridge.ConversionUtil;
import com.meituan.msc.lib.interfaces.container.MSCParams;
import com.meituan.msc.modules.update.metainfo.AppCheckUpdateManager;
import com.sankuai.meituan.retrofit2.Request;
import com.sankuai.meituan.retrofit2.Retrofit;
import com.sankuai.meituan.retrofit2.raw.RawCall;
import com.sankuai.meituan.retrofit2.raw.RawResponse;

import java.io.IOException;
import java.util.Collections;
import java.util.HashMap;
import java.util.List;
import java.util.Map;
import java.util.Set;

public class CheckUpdateMockManager {
    private static final String TAG_INTENT_PROCESSED = "msc_check_update_mock_intent_processed";
    private static final String TAG = "CheckUpdateMockManager";
    private static final String STORE_KEY_MOCK_APP_CONFIG = "STORE_KEY_MOCK_APP_CONFIG";

    private static final CheckUpdateMockManager INSTANCE = new CheckUpdateMockManager();

    public static CheckUpdateMockManager getInstance() {
        return INSTANCE;
    }

    private Map<String, CheckUpdateMockAppConfig> mockAppConfigMap;
    private boolean isEnable = false;
    private boolean isRegisteredCheckUpdateInterceptor = false;

    public boolean isEnable() {
        return isEnable;
    }

    public void enableCheckUpdateMock(Context context, boolean enable) {
        isEnable = enable;
        if (enable && !isRegisteredCheckUpdateInterceptor) {
            Retrofit.addInterceptors(Collections.singletonList(new CheckUpdateInterceptor(this)));
            isRegisteredCheckUpdateInterceptor = true;
        }
        if (enable) {
            // 性能测试模式下，将全局的业务最低版本号配置失效，以兼容直接从业务发布列表中获取测试包的场景
            AppCheckUpdateManager.getInstance().clearBizMinVersionMap();
        }
    }

    public void clearMockConfigs() {
        // TODO by chendacai 清除之前设置的网络拦截器
        getMockAppConfigMap().clear();
        saveMockConfigToCache();
    }

    @NonNull
    private Map<String, CheckUpdateMockAppConfig> getMockAppConfigMap() {
        if (mockAppConfigMap == null) {
            loadCachedMockConfig();
        }
        return mockAppConfigMap;
    }

    private void loadCachedMockConfig() {
        mockAppConfigMap = DevStorage.loadObject(STORE_KEY_MOCK_APP_CONFIG, new TypeToken<Map<String, CheckUpdateMockAppConfig>>() {
        }.getType(), new HashMap<>());
    }

    private void saveMockConfigToCache() {
        DevStorage.saveObject(STORE_KEY_MOCK_APP_CONFIG, getMockAppConfigMap());
    }

    public MSCAppMetaInfo getMockedAppMetaInfo(String appId, MSCAppMetaInfo mergeAppMetaInfo) throws IOException {
        CheckUpdateMockAppConfig mockAppConfig = getMockAppConfigMap().get(appId);
        if (mockAppConfig == null) {
            return mergeAppMetaInfo;
        }
        return requestMetaInfoByCheckUpdateUrl(mockAppConfig.checkUpdateUrl);
    }

    /**
     * 请求 checkUpdateUrl 获取数据
     *
     * @param checkUpdateUrl
     * @return
     * @throws IOException
     */
    private MSCAppMetaInfo requestMetaInfoByCheckUpdateUrl(String checkUpdateUrl) throws IOException {
        Request.Builder requestBuilder = new Request.Builder().url(checkUpdateUrl);
        RawCall.Factory callFactory = MSCCallFactory.getApiCallFactory(false);
        RawResponse response = callFactory.get(requestBuilder.build()).execute();
        String json = response.body().string();
        MSCMetaInfo metaInfo;
        try {
            metaInfo = ConversionUtil.getGson().fromJson(json, MSCMetaInfo.class);
        } catch (Throwable e) {
            throw new IOException("Fail to parse response to MSCMetaInfo with the response:\n" + json, e);
        }
        if (metaInfo != null) {
            List<MSCAppMetaInfo> appMetaInfoList = metaInfo.getMscApps();
            if (appMetaInfoList != null && !appMetaInfoList.isEmpty()) {
                return appMetaInfoList.get(0);
            }
        }
        return null;
    }

    public Set<String> getMockedAppIds() {
        return Collections.unmodifiableSet(getMockAppConfigMap().keySet());
    }

    public void addMockConfig(String appId, String checkUpdateUrl) {
        if (!isEnable) {
            return;
        }
        if (TextUtils.isEmpty(appId) || TextUtils.isEmpty(checkUpdateUrl)) {
            return;
        }

        CheckUpdateMockAppConfig newConfig = new CheckUpdateMockAppConfig(appId, checkUpdateUrl);
        CheckUpdateMockAppConfig oldConfig = getMockAppConfigMap().get(appId);
        if (Objects.equals(newConfig, oldConfig)) {
            return;
        }
        getMockAppConfigMap().put(appId, newConfig);
        saveMockConfigToCache();
        // 清除对应app的本地缓存
        removeMetaInfoCache(appId);
    }

    public void removeMockConfig(String appId) {
        if (!isEnable) {
            return;
        }
        if (TextUtils.isEmpty(appId)) {
            return;
        }

        CheckUpdateMockAppConfig mockAppConfig = getMockAppConfigMap().remove(appId);
        if (mockAppConfig != null) {
            saveMockConfigToCache();
            // 清除对应app的本地缓存
            removeMetaInfoCache(appId);
        }
    }

    /**
     * 清除指定的app的本地缓存信息
     *
     * @param appId
     */
    public static void removeMetaInfoCache(String appId) {
        MSCAppIdPublishId cachedAppPublishInfo = MSCMetaInfoCache.getInstance().getAppIdIndexInfo(appId);
        if (cachedAppPublishInfo != null) {
            MSCMetaInfoCache.getInstance().deleteCached(Collections.singletonList(cachedAppPublishInfo));
        }
    }

    public void handleCheckUpdateUrl(Intent intent) {
        if (!isEnable) {
            return;
        }
        if (intent.hasExtra(TAG_INTENT_PROCESSED)) {
            return;
        }
        // 如果锁包了，那么将 checkUpdateUrl
        String appId = IntentUtil.getStringExtra(intent, MSCParams.APP_ID);
        String checkUpdateUrl = IntentUtil.getStringExtra(intent, MSCParams.CHECK_UPDATE_URL);

        if (!TextUtils.isEmpty(appId)) {
            if (!TextUtils.isEmpty(checkUpdateUrl)) {
                // 有 checkUpdateUrl，配置mock
                addMockConfig(appId, checkUpdateUrl);
            }
        }
        intent.putExtra(TAG_INTENT_PROCESSED, true);
    }
}
