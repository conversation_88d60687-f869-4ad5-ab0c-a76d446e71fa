package com.meituan.msc.dev.renderer;


import com.meituan.msc.dev.utils.DevStorage;
import com.meituan.msc.devsupport.devtools.IRendererTestManager;

/**
 * author: renjihai
 * date: 7/17/23
 * desc: 渲染相关线下测试
 */
public class RendererTestManager implements IRendererTestManager {
    public static volatile RendererTestManager sInstance;
    public static final String SP_KEY_PRINT_RENDERER_COMMANDS_ENABLE = "msc_renderer_enable_print_renderer_cmds";

    private boolean mIsPrintRendererCommandsEnable;

    public static RendererTestManager getInstance() {
        if (sInstance == null) {
            synchronized (RendererTestManager.class) {
                if (sInstance == null) {
                    sInstance = new RendererTestManager();
                }
            }
        }
        return sInstance;
    }

    public boolean isPrintRendererCommandsEnable() {
        return mIsPrintRendererCommandsEnable;
    }

    public void setPrintRendererCommandsEnable(boolean isEnable) {
        if (mIsPrintRendererCommandsEnable == isEnable) return;
        mIsPrintRendererCommandsEnable = isEnable;
        DevStorage.setBoolean(SP_KEY_PRINT_RENDERER_COMMANDS_ENABLE, isEnable);
    }

    private RendererTestManager() {
        mIsPrintRendererCommandsEnable = DevStorage.getBoolean(SP_KEY_PRINT_RENDERER_COMMANDS_ENABLE, false);
    }
}
