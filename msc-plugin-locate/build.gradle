apply from: '../gradle_msc_common.gradle'

android {
    compileSdkVersion project.compileSdkVersion
    buildToolsVersion project.buildToolsVersion


    defaultConfig {
        minSdkVersion 16
        targetSdkVersion 30
        versionCode 1
        versionName "1.0"

        testInstrumentationRunner "androidx.test.runner.AndroidJUnitRunner"
        consumerProguardFiles 'consumer-rules.pro'
    }

    buildTypes {
        release {
            minifyEnabled false
            proguardFiles getDefaultProguardFile('proguard-android-optimize.txt'), 'proguard-rules.pro'
        }
    }

    compileOptions {
        sourceCompatibility JavaVersion.VERSION_1_8
        targetCompatibility JavaVersion.VERSION_1_8
    }

}

dependencies {
    implementation fileTree(dir: 'libs', include: ['*.jar'])
    annotationProcessor('com.sankuai.meituan.serviceloader:processor:2.2.33')
//    implementation 'com.sankuai.meituan.serviceloader:annotation:2.2.33'
//    // 隐私组件接入文档-Android 定位SDK https://km.sankuai.com/page/1215848348
//    implementation "com.meituan.android.privacy:locate:${project.privacySdkVersion}"
//    compileOnly "com.meituan.android.privacy:impl:${project.privacySdkVersion}"
//    implementation 'com.sankuai.meituan.pylon:basemodule:3.0.22'
//    implementation ('com.meituan.android.cipstorage:library:0.9.23-embed')
    compileOnly project(':library')
}
