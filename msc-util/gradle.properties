# Project-wide Gradle settings.

# IDE (e.g. Android Studio) users:
# Gradle settings configured through the IDE *will override*
# any settings specified in this file.

# For more details on how to configure your build environment visit
# http://www.gradle.org/docs/current/userguide/build_environment.html

# Specifies the JVM arguments used for the daemon process.
# The setting is particularly useful for tweaking memory settings.
# org.gradle.jvmargs=-Xmx1536m

# When configured, <PERSON><PERSON><PERSON> will run in incubating parallel mode.
# This option should only be used with decoupled projects. More details, visit
# http://www.gradle.org/docs/current/userguide/multi_project_builds.html#sec:decoupled_projects
# org.gradle.parallel=true
# org.gradle.daemon=true
# org.gradle.configureondemand=true
android.useDeprecatedNdk=true
POM_NAME=util
GROUP=com.meituan.android.msc
POM_ARTIFACT_ID=msc-util
POM_PACKAGING=aar
POM_DESCRIPTION=å·¥å·æ¨¡å
# å¦æéè¦å¨sampleä¸­æå¼è¯¦ç»åç¹ï¼è¯·åæ³¨éä¸é¢çä»£ç 
#TRACE_LEVEL=2