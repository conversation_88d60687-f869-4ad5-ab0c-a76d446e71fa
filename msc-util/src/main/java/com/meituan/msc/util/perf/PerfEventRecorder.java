package com.meituan.msc.util.perf;

import android.support.annotation.NonNull;

import com.meituan.msc.modules.reporter.MSCLog;
import com.meituan.msc.modules.reporter.ProcessMonitor;
import com.meituan.msc.util.perf.metrics.DurableEndEvent;
import com.meituan.msc.util.perf.metrics.ExtraEvent;
import com.meituan.msc.util.perf.metrics.InstantEvent;
import com.meituan.msc.util.perf.metrics.MetricsEvent;

import org.json.JSONObject;

import java.util.ArrayList;
import java.util.List;
import java.util.Map;
import java.util.UUID;
import java.util.concurrent.ConcurrentHashMap;
import java.util.concurrent.Executor;

/**
 * 一个runtime一个PerfEventRecorder，这样便于流程上的串联。
 * 考虑2个特殊情况：1.preload时，runtime还未创建；2.activity创建时，还没有关联的runtime。
 * 上述情况下，先把PerfEvents缓存起来，然后批量添加到PerfEventRecorder中。
 * @deprecated 使用PerfTrace
 */
@Deprecated
public class PerfEventRecorder implements IPerfEventRecorder {

    private static final String TAG = "PerfTrace";

    private final JavaPerfEventRecorder recorder = new JavaPerfEventRecorder();
    private String sid;
    private final boolean enablePrintEvent;
    private final boolean enableReportOnline;

    public PerfEventRecorder() {
        this(true, false);
    }

    public PerfEventRecorder(boolean enablePrintEvent, boolean enableReportOnline) {
        this.enablePrintEvent = enablePrintEvent;
        this.sid = UUID.randomUUID().toString();
        this.enableReportOnline = enableReportOnline;
    }

    public boolean isPrintEventEnabled() {
        return enablePrintEvent;
    }

    public String getSid() {
        return sid;
    }

    public void setSid(String sid) {
        this.sid = sid;
    }

    private void reportPerfEvent(PerfEvent event) {
        // no-op, for aop hook
    }

    private void reportMetricsEvent(@NonNull IMetricsEventReporter reporter, MetricsEvent event) {
        reporter.reportMetricsEvent(event);
    }

    @Override
    public void addEvent(PerfEvent event) {
        recorder.addEvent(event);
    }

    @Override
    public void clearEvents() {
        MSCLog.i(TAG, "clearEvents");
        recorder.clearEvents();
    }

    @Override
    public List<PerfEvent> getEvents() {
        return recorder.getEvents();
    }

    public void reportEventsSync(final IMetricsEventReporter reporter) {
        List<PerfEvent> toReportEvents = getEvents();
        List<PerfEvent> reportToMetricEvents = new ArrayList<>(toReportEvents.size());
        for (PerfEvent event : toReportEvents) {
            event.sid = sid;
            if (!event.isFlushed()) {
                reportPerfEvent(event);
                if (enableReportOnline) {
                    reportToMetricEvents.add(event);
                }
                event.setFlushed(true);
            }
        }
        if (enableReportOnline && reporter != null) {
            List<MetricsEvent> metricsEvents = getMetricsEvents(reportToMetricEvents);
            for (MetricsEvent event : metricsEvents) {
                reportMetricsEvent(reporter, event);
            }
        }
    }

    private static List<MetricsEvent> getMetricsEvents(List<PerfEvent> toReportEvents) {
        List<MetricsEvent> metricsEvents = new ArrayList<>(toReportEvents.size());
        for (PerfEvent event : toReportEvents) {
            if (!event.isShouldReport()) {
                continue;
            }
            if (PerfEventPhase.INSTANT.equals(event.ph)) {
                metricsEvents.add(new InstantEvent(event));
            } else if (PerfEventPhase.COMPLETE.equals(event.ph)) {
                metricsEvents.add(new ExtraEvent(event));
            } else if (PerfEventPhase.END.equals(event.ph)) {
                PerfEvent beginEvent = findBeginEvent(toReportEvents, event);
                if (beginEvent != null) {
                    metricsEvents.add(new DurableEndEvent(beginEvent, event));
                }
            }
        }
        return metricsEvents;
    }

    private static PerfEvent findBeginEvent(List<PerfEvent> toReportEvents, PerfEvent endEvent) {
        for (PerfEvent event : toReportEvents) {
            if (event.name.equals(endEvent.name) && PerfEventPhase.BEGIN.equals(event.ph)) {
                return event;
            }
        }
        return null;
    }

    public void beginDurableEvent(String name) {
        beginDurableEvent(name, null);
    }

    public void endDurableEvent(String name) {
        endDurableEvent(name, null);
    }

    public void beginDurableEvent(String name, ConcurrentHashMap<String, Object> extra) {
        ProcessMonitor.startCPUStatForCurrentThread();
        PerfEvent event = new PerfEvent(name, PerfEventPhase.BEGIN);
        if (extra != null) {
            event.extra = new JSONObject(extra);
        }
        addEvent(event);
    }

    public void endDurableEvent(String name, ConcurrentHashMap<String, Object> extra) {
        PerfEvent event = new PerfEvent(name, PerfEventPhase.END);
        if (extra != null) {
            event.extra = new JSONObject(extra);
        }
        addEvent(event);
    }

    public void recordInstantEvent(String name) {
        recordInstantEvent(name, null);
    }

    public void recordInstantEvent(String name, Map<String, Object> extra) {
        PerfEvent event = new PerfEvent(name, PerfEventPhase.INSTANT);
        if (extra != null) {
            event.extra = new JSONObject(extra);
        }
        addEvent(event);
    }
}
