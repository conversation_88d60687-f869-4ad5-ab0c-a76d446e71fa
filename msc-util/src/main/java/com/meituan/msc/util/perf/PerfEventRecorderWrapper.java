package com.meituan.msc.util.perf;

import android.annotation.SuppressLint;
import android.os.Trace;

//为了兼容TraceEvent和现有埋点，这逻辑实在是坑
public class PerfEventRecorderWrapper {

    private final IPerfEventCreator creator;

    public PerfEventRecorderWrapper(IPerfEventCreator creator) {
        this.creator = creator;
    }

    public void reportEventsSync(final IMetricsEventReporter reporter) {
        //TODO
    }

    private TraceEvent transfer(PerfEvent event) {
        return new TraceEvent(event);
    }

    @SuppressLint("UnclosedTrace")
    public TraceEvent begin(String eventName) {
        Trace.beginSection(eventName);
        return transfer(creator.begin(eventName));
    }

    public TraceEvent end(String eventName) {
        Trace.endSection();
        return transfer(creator.end(eventName));
    }

    public TraceEvent duration(String eventName, long startTimeInNs, long durationTimeInNs) {
        return transfer(creator.duration(eventName, startTimeInNs, durationTimeInNs));
    }

    public TraceEvent duration(String eventName, long startTimeInNs) {
        return duration(eventName, startTimeInNs, PerfEvent.currentTimeNanos() - startTimeInNs);
    }

    public TraceEvent instant(String eventName) {
        return transfer(creator.instant(eventName));
    }

    public TraceEvent instant(String eventName, long unixTs) {
        return transfer(creator.instant(eventName, unixTs));
    }
}
