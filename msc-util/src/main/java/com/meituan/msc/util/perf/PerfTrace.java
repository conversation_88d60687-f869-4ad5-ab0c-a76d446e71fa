package com.meituan.msc.util.perf;

import android.os.Trace;

import com.meituan.msc.modules.reporter.MSCLog;
import com.meituan.msc.trace.interfaces.MSCTrace;
import com.sankuai.android.jarvis.Jarvis;

import java.util.ArrayList;
import java.util.List;
import java.util.concurrent.ExecutorService;

/**
 * 性能打点静态类
 */
public final class PerfTrace {
    private static final long DIFFERENCE_WITH_SYSTEM_NANO_TIME = System.nanoTime() - currentTime();
    private static final long TRIM_EVENT_INTERVAL = 1000 * 20;// 20秒

    private static final PerfEventRecorderWrapper onlineWrapper = new PerfEventRecorderWrapper(MSCTrace.online());
    private static final PerfEventRecorderWrapper offlineWrapper = new PerfEventRecorderWrapper(MSCTrace.offline());

    private static final ExecutorService sExecutor = Jarvis.newSingleThreadExecutor("msc-perf-recorder");
    private static volatile long lastTrimTime = -1;

    private PerfTrace() {
    }

    public static ExecutorService getExecutor() {
        return sExecutor;
    }

    public static void enterPerfMode() {
        MSCTrace.enterPerfMode();
    }

    public static PerfEventRecorderWrapper online() {
        return onlineWrapper;
    }

    public static String buildEventName(String className, String methodName) {
        return className + "." + methodName;
    }

    //do not delete, used by ASM
    public static void methodBegin(String className, String methodName, int hashCode) {
        addJavaMethodPerfEvent(className, methodName, hashCode, PerfEventPhase.BEGIN);
    }

    //do not delete, used by ASM
    public static void methodEnd(String className, String methodName, int hashCode) {
        addJavaMethodPerfEvent(className, methodName, hashCode, PerfEventPhase.END);
    }

    private static void addJavaMethodPerfEvent(String className, String methodName, int hashCode, String phase) {
        String name = className;
        if (hashCode != 0) {
            name += "@" + Integer.toHexString(hashCode);
        }
        name += "." + methodName;
        PerfEvent perfEvent = new PerfEvent(name, phase);
        MSCTrace.addJavaMethodEvent(perfEvent);
    }

    public static TraceEvent begin(String eventName) {
        Trace.beginSection(eventName);
        return offlineWrapper.begin(eventName);
    }

    public static TraceEvent end(String eventName) {
        Trace.endSection();
        return offlineWrapper.end(eventName);
    }

    public static TraceEvent duration(String eventName, long startTimeInNs, long durationTimeInNs) {
        return offlineWrapper.duration(eventName, startTimeInNs, durationTimeInNs);
    }

    public static TraceEvent duration(String eventName, long startTimeInNs) {
        return offlineWrapper.duration(eventName, startTimeInNs);
    }

    public static TraceEvent instant(String eventName) {
        return offlineWrapper.instant(eventName);
    }

    public static TraceEvent instant(String eventName, long unixTs) {
        return offlineWrapper.instant(eventName, unixTs);
    }

    public static void addJsPerfEvent(PerfEvent perfEvent) {
        MSCTrace.addJsEvent(perfEvent);
    }

    public static long currentTime() {
        return MSCTrace.currentTimeNanos();
    }

    public static long currentTimeMillis() {
        return MSCTrace.currentTimeMillis();
    }

    public static List<TraceEvent> getOnlineEvents() {
        List<PerfEvent> perfEvents = getOnlinePerfEvents();
        List<TraceEvent> traceEvents = new ArrayList<>(perfEvents.size());
        for (PerfEvent item : perfEvents) {
            traceEvents.add(new TraceEvent(item));
        }
        return traceEvents;
    }

    public static List<PerfEvent> getOnlinePerfEvents() {
        return MSCTrace.getOnlineEvents();
    }

    public static List<PerfEvent> getOfflinePerfEvents() {
        return MSCTrace.getOfflineEvents();
    }

    public static long convertSystemNanoTime(long systemNanoTime) {
        return systemNanoTime - DIFFERENCE_WITH_SYSTEM_NANO_TIME;
    }

    public static void trimOnlineTrace() {
        if (!MSCTrace.isInPerfMode()) {
            // 线上才裁剪
            long currentTime = System.currentTimeMillis();
            if (currentTime - lastTrimTime > TRIM_EVENT_INTERVAL) {
                if (MSCTrace.online() instanceof IPerfEventRecorder) {
                    ((IPerfEventRecorder) MSCTrace.online()).clearEvents();
                    MSCLog.i("Trimmed online trace");
                }
                lastTrimTime = currentTime;
            }
        }
    }
}
