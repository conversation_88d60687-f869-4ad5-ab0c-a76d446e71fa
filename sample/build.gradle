apply plugin: 'com.android.application'
//apply plugin: 'me.tatarka.retrolambda'
apply plugin: 'MSCTrace'
apply plugin: 'service_loader'
apply plugin: 'bytecode-manipulator'
//apply plugin: 'dynloader_uploader'
//apply plugin: 'WMRouter'

//使用文档：https://km.sankuai.com/collabpage/1563926864
MSCTrace {
    entryApplicationClass "com.meituan.msc.lib.hera.sample.HeraApplication"
    permittedNames = [
            "com.meituan.msc.jse.bridge",
//            "com.meituan.metrics.fsp.sampler.FspViewAreaSampler",
//            "com.facebook.react.uimanager.UIViewOperationQueue",
//            "com.sankuai.waimai.business.restaurant.base.manager.order.WMOrderManager",
//            "com.sankuai.meituan.arbiter.hook.MTInstrumentation",
//            "com.sankuai.meituan.mbc.dsp.core.Dsp",
//            "com.sankuai.meituan.mbc.dsp.DspActivity",
//            "com.meituan.android.mrn.container",
//            "com.meituan.android.common.weaver.impl.natives.FFPInstrument",
//            "com.meituan.android.common.weaver.impl.natives.NativeEndPoint",
//            "com.meituan.msc.mmpviews.scroll.ScrollShellView",
//            "com.meituan.msc.common.utils.WhiteScreenUtil",
//            "com.meituan.msc.mmpviews.text.MPTextView",
//            "com.bumptech.glide.request.GenericRequest",
//            "com.sankuai.waimai.alita.core.event.autorunner",
//            "com.sankuai.waimai.alita.core.engine.AlitaJSEngineManager",
//            "com.sankuai.waimai.alita.core.engine.AlitaJSEngine",
//            "com.facebook.react.bridge.Arguments",
//            "com.meituan.msc.mmpviews.shell.MPBaseViewGroup",
//            "com.meituan.msc.mmpviews.list.msclist",
//            "com.meituan.msc.uimanager.NativeViewHierarchyManager",
//            "com.meituan.msc.modules.page.render.webview",
//            "com.meituan.msc.modules.engine.requestPrefetch",
//            "com.meituan.msc.extern.MSCCallFactory",
//            "com.meituan.msc.modules.api.network.RequestPrefetchApi",
//            "com.meituan.msc.modules.api.network.FetchDataParam",
//            "com.meituan.msc.modules.api.network.FetchTokenResponse",
//            "com.meituan.msi.bean.MsiContext",
//            "com.meituan.msi.api.ApiRequest",
//            "com.meituan.msi.api.ApiResponse",
    ]
    checkThread false
    printLog true
    mscOnly true
    mtOnly true
    ignoreAndroid true
    ignoreJava true

    enable false
    enableOnDebug false
}

apply plugin: 'mscpreset'
mscpreset {
    enable true
    // eva上的appName
    appName "group"
    // eva上的appName
    debug false
}

//dynloader_uploader {
//    enable true
//    // 默认debug模式不开启
//    enableOnDebug = false
//    // 指定要上传的so
////    if (PRO_ENGINE == "tx") {
//        soUploadList = ["lib/armeabi/libliteavsdk.so", "lib/armeabi/libtraeimp-rtmp.so", "lib/armeabi/libtxffmpeg.so", "lib/armeabi/libtxplayer.so", "lib/armeabi/libtxsdl.so",
//                        "lib/armeabi-v7a/libliteavsdk.so", "lib/armeabi-v7a/libtraeimp-rtmp.so", "lib/armeabi-v7a/libtxffmpeg.so", "lib/armeabi-v7a/libtxplayer.so", "lib/armeabi-v7a/libtxsdl.so",
//        ]
////    } /*else {
////       soUploadList = ["lib/armeabi/libijkffmpeg.so", "lib/armeabi/libijkplayer.so", "lib/armeabi/libijksdl.so",
////                       "lib/armeabi-v7a/libijkffmpeg.so", "lib/armeabi-v7a/libijkplayer.so", "lib/armeabi-v7a/libijksdl.so",]
////   }*/
//
//    assetsDirUploadList = [
//    ]
//}
service_loader {
    enable true
    enableOnDebug true
    // debug期间是否开启对声明的实现类检查（检查接口和实现类是否真实存在）
    checkServicesOnDebug true
}

configurations {
    all*.exclude group: 'com.sankuai.meituan', module: 'buildconfig'
    all*.exclude group: 'com.dianping.android.sdk', module: 'mainboard'
    all*.exclude group: 'com.sankuai.meituan.kernel', module: 'image'
    // 下线netsingleton & netmodule & net-impl
    all*.exclude group: 'com.sankuai.meituan.pylon', module: 'netsingleton'
    all*.exclude group: 'com.sankuai.meituan.pylon', module: 'netmodule'
    all*.exclude group: 'com.sankuai.meituan.kernel', module: 'net-impl'

    all*.exclude group: 'com.squareup.okhttp', module: 'okhttp'
    all*.exclude group: 'com.meituan.android.common', module: 'babel'
    all*.exclude group: 'com.meituan.android.snare', module: 'snare'
    all*.exclude group: 'com.meituan.android.crashreporter', module: 'library'
    all*.exclude group: 'com.meituan.metrics', module: 'metrics'
    all*.exclude group: 'com.meituan.android.sniffer'
    all*.exclude group: 'com.facebook.react.modules.image.ImageLoaderModule'
    all*.exclude group: 'com.meituan.android.common', module: 'tcreporter'
    //noinspection DuplicatePlatformClasses
    all*.exclude module: 'xpp3'

    // gradle升级3.2.1
    all*.exclude group: 'com.google.android', module: 'android'
    all*.exclude group: 'org.apache.httpcomponents', module: 'httpclient'
    all*.exclude group: 'commons-logging', module: 'commons-logging'
    all*.exclude group: 'org.json', module: 'json'
    all*.exclude group: 'com.meituan.android.knb', module: 'titans-knbweb-delegate'

    if (excludeDevTools.toBoolean()) {
        all*.exclude group: 'com.meituan.android.msc', module: 'msc-plugin-devtools'
    }
    // 下线netsingleton & netmodule & net-impl
    all*.exclude group: 'com.sankuai.meituan.pylon', module: 'netsingleton'
    all*.exclude group: 'com.sankuai.meituan.pylon', module: 'netmodule'
    all*.exclude group: 'com.sankuai.meituan.kernel', module: 'net-impl'
    all*.exclude group: 'com.sankuai.meituan.pylon', module: 'model'
    all*.exclude group: 'com.meituan.android.favorite', module: 'library'
//    all*.exclude group: 'com.meituan.android.abtest', module: 'abtestv2'
    all*.exclude group: 'com.sankuai.android.jarvis', module: 'core'
    all*.exclude group: 'com.sankuai.meituan.dev', module: 'devtools'
    all*.exclude group: 'com.meituan.android.mmp', module: 'mmp'
}

android {
    compileSdkVersion project.compileSdkVersion
    buildToolsVersion project.buildToolsVersion
// 获取应用 id
    def getApplicationId = { ->
        def appId = project.hasProperty("share") && "true".contentEquals(project.property("share")) ? "com.sankuai.meituan" : "com.meituan.android.msc.sample"
        println "appid===****{$appId}"
        return appId
    }

    defaultConfig {
        minSdkVersion project.minSdkVersion
        targetSdkVersion project.targetSdkVersion

        versionCode 120021400
        versionName "12.21.400"
        multiDexEnabled true
        applicationId getApplicationId()
        ndk {
            abiFilters "x86", "x86_64", "armeabi-v7a", "arm64-v8a"
        }

        buildConfigField "boolean", "isMT", "false"
        manifestPlaceholders = [
                TencentMapSDK: "com.sankuai.meituan".contentEquals("${getApplicationId()}") ?
                        "00e3d061e7debe5f88aec44e0b549b76" : "7WOBZ-R6VK6-VUESY-MHM4H-DDFXF-GQB5U",
                hostName     : 'www.meituan.com',
                schemaName   : 'aimeituan'

        ]
    }
    flavorDimensions "full"
    aaptOptions.cruncherEnabled = false

    productFlavors {

        meituan {
            buildConfigField "boolean", "isMT", "true"
            dimension 'full'
        }

        zero {
            dimension 'full'
            matchingFallbacks = ['zero', 'meituan']
        }
//        dianping {
//            dimension 'full'
//        }
    }


    signingConfigs {
        sankuai {
            storeFile file("keystore/meituan-debug.keystore")
            storePassword "1234567"
            keyAlias "meituan"
            keyPassword "12345678"
        }
    }

    buildTypes {
        release {
            minifyEnabled true
            proguardFiles getDefaultProguardFile('proguard-android.txt'), 'proguard-rules.pro'
            signingConfig signingConfigs.sankuai
            manifestPlaceholders = [
                    "kitefly_token" : "55507bb5ce08881827921b6c",
                    "mtguard_appkey": "1ce5de1a-3ab5-47c6-8993-6dde7f6336af",
                    "mtguard_pic"   : "ms_com.sankuai.meituan",
                    "mtguard_sec"   : "ppd_com.sankuai.meituan.xbt"
            ]
        }
        debug {
            debuggable true
            minifyEnabled false
            signingConfig signingConfigs.sankuai
            manifestPlaceholders = [
                    "kitefly_token" : "55507bb5ce08881827921b6c",
                    "mtguard_appkey": "1ce5de1a-3ab5-47c6-8993-6dde7f6336af",
                    "mtguard_pic"   : "ms_com.sankuai.meituan",
                    "mtguard_sec"   : "ppd_com.sankuai.meituan.xbt"
            ]
        }
    }

    packagingOptions {
        exclude 'META-INF/DEPENDENCIES'
        exclude 'META-INF/NOTICE'
        exclude 'META-INF/LICENSE'
        exclude 'NOTICE'
        exclude 'LICENSE'
        exclude 'META-INF/LICENSE.txt'
        exclude 'META-INF/NOTICE.txt'
        exclude 'NOTICE.txt'
        exclude 'LICENSE.txt'
        exclude 'META-INF/MANIFEST.MF'
        exclude "META-INF/*.kotlin_module"
        exclude "kotlin/**"
        exclude 'META-INF/rxjava.properties'
        pickFirst 'lib/**/libc++_shared.so'
        pickFirst 'lib/**/libanimated-webp.so'
        pickFirst '**/**.so'

        doNotStrip '**.so'
    }

    lintOptions {
        abortOnError false
    }

    compileOptions {
        sourceCompatibility JavaVersion.VERSION_1_8
        targetCompatibility JavaVersion.VERSION_1_8
    }
    configurations.all {
        resolutionStrategy.cacheChangingModulesFor 0, 'seconds'
//        resolutionStrategy.force 'com.sankuai.meituan.serviceloader:annotation:2.2.34'
    }

    dexOptions {
        incremental true
        javaMaxHeapSize "4g"
    }
}

WaimaiBytecodeManipulator {
    callSite true // 开启方法调用替换功能，可以把指定类或其所有子类的某个方法调用替换成另一个指定的方法
    methodWrap true // 开启方法返回值替换功能，可以把指定方法的返回值替换成任意的其他结果
    methodEntry true // 开启方法入口插入代码功能，可以在指定方法的第一行插入代码
    methodExit true // 开启方法出口插入代码功能，可以在指定方法的出口（支持多个return语句的情况）处插入代码，也支持修改返回值
    annotationInclude "com.meituan.msc.dev.aop.*" // 可选，设置插桩配置类所在范围，防止第三方SDK中添加未经审核的插桩配置代码
//    annotationExclude "com.test.anno.aop.*" // 可选，排除指定包下的插桩配置代码
}

dependencies {
    implementation project(path: ':jse')
//    implementation project(path: ':render')
    compile ("com.meituan.android.msc:msc-render:${project.renderVersion}"){
        exclude group: 'com.meituan.android.msc', module: 'interface'
        exclude group: 'com.meituan.android.msc', module: 'library'
        exclude group: 'com.meituan.android.msc', module: 'msc-util'
        exclude group: 'com.meituan.msc', module: 'jse'
    }
//    zeroCompile project(':mmp-plugin-user')
//    zeroCompile fileTree(include: ['tencent_map_sdk_4.1.4.4.d05c186.jar'], dir: '../libs')

    meituanApi project(':msc-mt')
    zeroApi project(':library')
    implementation project(':msc-benchmark')
    implementation project(':tech-stack-statistics')
//    dianpingCompile project(':mmp-dp')

    api('com.meituan.android.mtwebkit:devpanel:0.1.11')

    if (!excludeDevTools.toBoolean()) {
        api project(':msc-plugin-devtools')
//        api project(':msc-renderer-devtools')
        api "com.meituan.android.msc:msc-render-devtools:${project.renderVersion}"
    }
//    dianpingCompile project(':mmp-plugin-devtools')
//    debugCompile project(':mmp-plugin-live')
//
//    debugCompile project(':mmp-plugin-camera')
//    zeroImplementation project(':mmp-plugin-map')
//    zeroImplementation project(':mmp-plugin-option')
//    compile project(':mmp-plugin-contact')

    implementation('com.sankuai.meituan.multiprocess:library:0.0.28')

    implementation('com.dianping.android.sdk:dpjscore:*********-debugger') {
        force = true
    }
    implementation('com.meituan.android.msi:map:4.1209.1')
    implementation('com.sankuai.meituan.video.container:msi-video:1.0.22') {
        force = true
    }
    implementation('com.sankuai.meituan.mapsdk:mapsdk-mtmap:4.1209.4-mt')
    implementation('com.meituan.android.msi:msi-network:0.0.18')
    implementation('com.sankuai.meituan.kernel:net-msi:0.1.27')

//    meituanCompile('com.meituan.android.mmp:mmp:0.1.391.1-youxuan-meituan')
//    implementation('com.sankuai.meituan.serviceloader:serviceloader:2.2.27') {
//        force = true
//    }

//    implementation('com.sankuai.meituan.serviceloader:annotation:2.2.34') {
//        force = true
//    }

//    annotationProcessor('com.sankuai.meituan.serviceloader:processor:2.2.27')

    implementation('com.squareup.picasso:picasso:10000.**********') {
        force = true
    }

    compile 'com.android.support:multidex:1.0.3'
    compile 'com.android.support:support-annotations:26.0.2'

    implementation('com.meituan.android.httpdns:httpdns-base:1.5.8') {
        force = true
    }
    implementation('com.meituan.android.httpdns:httpdns:1.4.25') {
        force = true
    }
    implementation('com.meituan.android.httpdns:httpdns-okhttp3:1.4.25') {
        force = true
    }
    implementation("com.meituan.android.loader:dynloader:${DYNLOADER_VERSION}") {
        force = true
    }
    implementation("com.meituan.android.loader:dynloader-interface:${DYNLOADER_VERSION}") {
        force = true
    }
    api 'com.meituan.android.cipstorage:library:*********-embed'

    // mtWebView
    implementation "com.meituan.android.mtwebkit:library:$MT_WEBVIEW_VERSION"
    //
    meituanImplementation 'com.meituan.android.terminus:library:10.0.12'
    meituanImplementation('com.sankuai.meituan.dev:switchtestenv:0.0.44') {
        force = true
    }
    zeroImplementation('com.sankuai.meituan.dev:switchtestenv:10000.0.43-empty') {
        force = true
    }

    zeroImplementation("com.meituan.android.common.locate:interface:*******") {
        force = true
    }
    zeroImplementation("com.meituan.android.common.locate:impl:*******") {
        force = true
    }

    meituanImplementation("com.meituan.android.common.locate:interface:2.1237.1-mt") {
        force = true
    }
    meituanImplementation("com.meituan.android.common.locate:impl:2.1237.1-mt") {
        force = true
    }

//    if (enableLive) {
//        implementation('com.meituan.android.msi:msi-live:12.20.400'){
//            force = true
//        }
//        implementation('com.meituan.android.common:fingerprint:2.0.19') {
//            force = true
//        }
//        //美团Android平台直播组件
//        implementation('com.sankuai.meituan.mtlive:ugc-tx:4.0.0-mt') {
//            force = true
//        }
//        implementation('com.sankuai.meituan.mtlive:ugc-library:4.0.0') {
//            force = true
//        }
//        implementation('com.sankuai.meituan.mtlive:pusher-mlvb:4.0.0-mt') {
//            force = true
//        }
//        implementation('com.sankuai.meituan.mtlive:pusher-library:4.0.0') {
//            force = true
//        }
//        implementation('com.sankuai.meituan.mtlive:player-mlvb:4.0.3-mt') {
//            force = true
//        }
//        implementation('com.sankuai.meituan.mtlive:player-library:3.0.144') {
//            force = true
//        }
//        implementation('com.sankuai.meituan.mtlive:mtrtc-tx:4.0.0-mt') {
//            force = true
//        }
//        implementation('com.sankuai.meituan.mtlive:mtrtc-library:4.0.0') {
//            force = true
//        }
//        implementation('com.sankuai.meituan.mtlive:livecore:3.0.80') {
//            force = true
//        }
//        implementation('com.sankuai.meituan.mtlive:riverrun-player:3.0.3') {
//            force = true
//        }
//        implementation('com.sankuai.meituan.mtlive:player-streamlake:1.0.152-mt') {
//            force = true
//        }
//    }

///////////////////////merge from aimeituan///////////////
    meituanCompile 'com.dianping.android.sdk:nvreqresp:1.0.13'
    compile 'com.meituan.android.walle:payload_reader:1.1.8'
    implementation('com.meituan.android.abtest:abtestv2:1.0.6')
    //yoda
    implementation('com.meituan.android.yoda:library:1.18.0.209') {
        exclude group: 'com.sankuai.titans', module: 'titans-result'
    }
//    implementation('com.sankuai.titans:titans-result:1.0.8') {
//        force = true
//    }
    implementation('com.meituan.android.mtguard:mtguard:6.5.0') {
        force = true
    }
    implementation('com.sankuai.meituan.kernel:net:3.0.34') {
        force = true
    }

    // walle
    api 'com.meituan.android.common.channel:reader:0.9.8'
    ////////////////////////
    implementation('com.android.support:support-v4:26.0.2') {
        force = true
    }
    implementation('com.android.support:appcompat-v7:26.0.2') {
        force = true
    }
    implementation('com.android.support:support-annotations:26.0.2') {
        force = true
    }
    implementation('com.android.support:recyclerview-v7:26.0.2') {
        force = true
    }
    implementation('com.android.support:design:26.0.2') {
        force = true
    }
    implementation('com.android.support:support-compat:26.0.2') {
        force = true
    }
    implementation('com.android.support:support-fragment:26.0.2') {
        force = true
    }
    implementation('com.android.support:support-core-utils:26.0.2') {
        force = true
    }
    implementation('com.android.support:support-core-ui:26.0.2') {
        force = true
    }
    implementation("com.meituan.android.common.metricx:metricx:${project.metricxVersion}") {
        force = true
    }
    implementation("com.meituan.android.common.metricx:utils:${project.metricxVersion}") {
        force = true
    }
    implementation("com.meituan.android.common.metricx:babel:${project.babelVersion}") {
        force = true
    }
    implementation("com.meituan.android.common.metricx:sniffer:4.17.3") {
        force = true
    }
    implementation("com.meituan.android.common.metricx:base:${project.metricxVersion}") {
        force = true
    }
    implementation('com.meituan.robust:robust:0.8.40') {
        force = true
    }

    implementation('com.dianping.android.sdk:networklog:2.3.5') {
        force = true
    }

    implementation('com.sankuai.android.jarvis:library:0.1.33') {
        force = true
    }

    implementation('com.meituan.android.downloadmanager:library:2.1.11') {
        force = true
    }

    implementation('com.meituan.android.common:horn:0.3.55') {
        force = true
    }
    implementation('com.meituan.android.common:horn-devtools:0.3.72') {
        force = true
    }

    compile 'com.dianping.android.sdk:logreportswitcher:1.1.22'

    implementation('com.sankuai.meituan.pylon:util:3.1.8') {
        exclude group: 'com.meituan.android.base.transformation', module: 'RoundedCornersTransformation'
        force = true
    }

    implementation('com.sankuai.meituan.retrofit2:utils-nvnetwork:1.7.24') {
        force = true
    }

    /** 底层网络通道 */
    //shark
    implementation('com.dianping.android.sdk:nvnetwork:7.0.32') {
        force = true
    }
    implementation('com.dianping.android.sdk:sharkpush:3.0.7') {
        force = true
    }
    // 美团 AB 测试等相关工具
    implementation ('com.sankuai.meituan.pylon:basemodule:3.0.39') {
        force = true
    }
    implementation('com.meituan.dio:picassohelper:0.1.2') {
        force = true
    }

    zeroImplementation('com.meituan.passport:basemodule:5.40.7') {
        force = true
    }

    implementation('com.sankuai.meituan.serviceloader:annotation:2.2.30') {
        force = true
    }

    annotationProcessor('com.sankuai.meituan.serviceloader:processor:2.2.27')

    implementation("com.meituan.android.privacy:impl:0.7.14.0") {
        force = true
    }

    implementation("com.meituan.android.privacy:locate:0.7.30") {
        force = true
    }

    implementation("com.meituan.android.privacy:interface:0.7.14.0") {
        force = true
    }

    implementation("com.meituan.android.mtv8:mtv8:${MTV8_VERSION}") {
        force = true
    }
//    implementation('com.meituan.android.mtv8:v8-release:0.2.21.1-local-test') {
//        force = true
//    }
    implementation("com.meituan.android.mtv8:v8-debug:${MTV8_VERSION}") {
        force = true
    }
    // 美团隐私合规SDK Debug功能使用指南 https://km.sankuai.com/page/1224363184
    implementation("com.meituan.android.privacy:debug:${project.privacySdkVersion}") {
        force = true
    }

    implementation("com.meituan.android.common.metricx:base:${project.metricxVersion}") {
        force = true
    }

    implementation("com.meituan.android.bsdiff.zip:library:1.0.14") {
        force = true
    }

    implementation("com.meituan.met.mercury:load:1.3.62") {
        force = true
    }

    api("com.sankuai.titans:titans-widget:2.0.48-mt") {
        force = true
    }

    api("com.sankuai.titans:titans-result:1.0.16") {
        force = true
    }

    implementation("com.meituan.android.msi:library:${project.msiVersion}")

    implementation("com.meituan.android.mtplayer:video:********") {
        force = true
    }
    /** 网络门面层组件 */
    implementation("com.sankuai.meituan.retrofit2:retrofit-mt:1.10.20") {
        force = true
    }
    /** 网络通道callfactory */
    implementation("com.sankuai.meituan.retrofit2:callfactory-okhttp3:1.10.2") {
        force = true
    }
    // MSC 安全基线接入 https://km.sankuai.com/collabpage/2213075052
    // 接入文档：https://km.sankuai.com/collabpage/1746951251#id-2.4%20MSC
    implementation 'com.meituan.android.risk:mtretrofit:********'

    implementation("com.meituan.android.clipboard:library:0.0.12") {
        force = true
    }

    implementation("com.sankuai.meituan.arbiter:arbiter:********") {
        force = true
    }
    implementation 'com.meituan.android.mrn:flipper-plugin:0.0.4'

    implementation("com.meituan.android.common.ffp:shell-common:${FFP_VERSION}") {
        exclude group: 'com.meituan.android.common.ffp', module: 'impl-knb'
    }
    implementation("com.meituan.android.common.ffp:impl:${FFP_VERSION}") {
        force = true
    }

    implementation('com.meituan.android.knb:knb-web:11.39.12') {
        force = true
    }

    implementation('com.meituan.met.mercury:push:1.3.21') {
        force = true
    }
    implementation('com.meituan.met.mercury:ddd-devtools:1.3.21') {
        force = true
    }
    implementation('com.dianping.android.sdk:pike:1.3.13') {
        force = true
    }
    implementation('com.meituan.android.knb:titans-base:20.23.1') {
        force = true
    }
    implementation('com.meituan.android.common.analyse:library:4.60.9'){
        force = true
    }

    // 解决编译冲突 Caused by: com.android.tools.r8.utils.AbortException:
    // Error: Type com.sankuai.titans.protocol.jsbridge.IJsBridgeManager
    // is referenced as an interface from `com.sankuai.titans.adapter.base.JsBridgeManagerImpl`.
    implementation('com.meituan.android.knb:titans-protocol:21.0.5') {
        force = true
    }
    implementation('com.sankuai.meituan.mtplayer:vod-library:2.2.79')
    implementation('com.sankuai.meituan.mtplayer:vod-ks:2.2.79')
    implementation('com.sankuai.meituan.riverrun:mtffmpeg_core:0.0.11')
    implementation('com.sankuai.meituan.mtlive:livecore:3.0.118')
    implementation('com.meituan.android.degrade:library:12.32.200.2') {
        exclude group: 'com.meituan.android.common', module: 'unionid'
        exclude group: 'com.meituan.android.aurora', module: 'core'
        exclude group: 'com.meituan.android.msi', module: 'library'
    }
}

apply from: file('dependencies_dddpreset.gradle')
