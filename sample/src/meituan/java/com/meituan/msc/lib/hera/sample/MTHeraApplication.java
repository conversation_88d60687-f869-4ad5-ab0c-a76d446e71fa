package com.meituan.msc.lib.hera.sample;

import android.app.Application;
import android.content.Context;
import android.content.SharedPreferences;
import android.os.Build;
import android.os.Trace;
import android.support.annotation.Keep;
import android.support.v4.util.ArrayMap;
import android.text.TextUtils;
import android.util.Log;

import com.dianping.networklog.Logan;
import com.dianping.nvnetwork.NVAppMockManager;
import com.dianping.nvnetwork.NVGlobal;
import com.dianping.sdk.pike.PikeGlobal;
import com.google.gson.Gson;
import com.meituan.android.base.BaseConfig;
import com.meituan.android.base.ICityController;
import com.meituan.android.cipstorage.CIPStorageCenter;
import com.meituan.android.common.babel.Babel;
import com.meituan.android.common.babel.BabelConfig;
import com.meituan.android.common.horn.Horn;
import com.meituan.android.common.horn.HornCallback;
import com.meituan.android.common.horn.HornConfiguration;
import com.meituan.android.common.horn.extra.sharkpush.ISharkPushService;
import com.meituan.android.common.horn.extra.sharkpush.SharkPushServiceMgr;
import com.meituan.android.common.horn.extra.uuid.IUUIDService;
import com.meituan.android.common.horn.extra.uuid.UUIDServiceMgr;
import com.meituan.android.common.metricx.PreloadInjection;
import com.meituan.android.common.metricx.helpers.AppBus;
import com.meituan.android.common.statistics.Interface.IEnvironment;
import com.meituan.android.common.statistics.Statistics;
import com.meituan.android.common.weaver.impl.WeaverProxy;
import com.meituan.android.common.weaver.impl.shellcommon.CommonShell;
import com.meituan.android.loader.impl.DynLoaderInit;
import com.meituan.android.loader.impl.DynParamsProvider;
import com.meituan.android.privacy.impl.LifeCycleMonitor;
import com.meituan.android.privacy.impl.PrivacyProxy;
import com.meituan.android.privacy.interfaces.PrivacyConfig;
import com.meituan.android.singleton.ApplicationSingleton;
import com.meituan.android.singleton.CityControllerSingleton;
import com.meituan.android.singleton.ContextSingleton;
import com.meituan.android.singleton.MasterLocatorSingleton;
import com.meituan.android.singleton.RetrofitCallFactorySingleton;
import com.meituan.android.singleton.UUIDProviderSingleton;
import com.meituan.android.singleton.UserCenterSingleton;
import com.meituan.met.mercury.load.core.DDLoaderContext;
import com.meituan.met.mercury.load.core.DDLoaderManager;
import com.meituan.met.mercury.load.core.IPushService;
import com.meituan.met.mercury.load.core.LoaderEnvironment;
import com.meituan.met.mercury.push.DDPushSyncService;
import com.meituan.metrics.Environment;
import com.meituan.metrics.Metrics;
import com.meituan.metrics.config.MetricsConfig;
import com.meituan.metrics.interceptor.MetricsDefaultInterceptor;
import com.meituan.metrics.interceptor.MetricsInterceptor;
import com.meituan.metrics.model.AbstractEvent;
import com.meituan.metrics.sampler.fps.FpsEvent;
import com.meituan.metrics.sampler.fps.ScrollHitchEvent;
import com.meituan.metrics.sampler.memory.MemoryEvent;
import com.meituan.metrics.speedmeter.MetricsLaunchFunnelTask;
import com.meituan.metrics.speedmeter.MetricsSpeedMeterType;
import com.meituan.metrics.speedmeter.SpeedMeterEvent;
import com.meituan.msc.common.config.MSCConfig;
import com.meituan.msc.common.executor.MSCExecutors;
import com.meituan.msc.common.executor.UiRunnable;
import com.meituan.msc.common.process.MSCProcess;
import com.meituan.msc.common.process.WebViewDirectoryFixer;
import com.meituan.msc.dev.requestPrefetch.DataPrefetchInstrument;
import com.meituan.msc.dev.utils.AppContextGetter;
import com.meituan.msc.extern.MSCEnvHelper;
import com.meituan.msc.extern.MSCHostInitializer;
import com.meituan.msc.modules.container.ApplicationLifecycleMonitor;
import com.meituan.msc.modules.container.router.MSCInstrumentation;
import com.meituan.msc.modules.devtools.DebugHelper;
import com.meituan.msc.modules.update.DDLoaderDebugHelper;
import com.meituan.mtwebkit.internal.AndroidReflectHelper;
import com.meituan.mtwebkit.internal.MTWebViewCIPStorage;
import com.meituan.mtwebkit.internal.MTWebViewConfigManager;
import com.meituan.mtwebkit.internal.MTWebViewMSCConfigManager;
import com.meituan.mtwebkit.internal.MTWebViewManager;
import com.meituan.mtwebkit.internal.env.IMTWebViewEnvInfo;
import com.meituan.mtwebkit.internal.env.MTWebViewEnvManager;
import com.meituan.passport.UserCenter;
import com.meituan.snare.ExceptionHandler;
import com.meituan.snare.Strategy;
import com.meituan.uuid.GetUUID;
import com.sankuai.android.jarvis.Jarvis;
import com.sankuai.android.spawn.locate.LocationCache;
import com.sankuai.common.utils.FileUtils;
import com.sankuai.common.utils.ProcessUtils;
import com.sankuai.meituan.arbiter.hook.ArbiterHook;
import com.sankuai.meituan.config.MeituanConfig;
import com.sankuai.meituan.dev.horn.HornDeveloperKit;
import com.sankuai.meituan.kernel.net.IAnalyseInfor;
import com.sankuai.meituan.kernel.net.impl.INetFactoryImpl;
import com.sankuai.meituan.mtlive.core.ILiveLicenseProvider;
import com.sankuai.meituan.mtlive.core.ILiveMonitorProvider;
import com.sankuai.meituan.mtlive.core.MTLiveConfigEx;
import com.sankuai.meituan.mtlive.core.MTLiveEngineEx;
import com.sankuai.meituan.player.vodlibrary.MTVodEnv;
import com.sankuai.meituan.serviceloader.ServiceLoader;
import com.sankuai.meituan.serviceloader.annotation.ServiceLoaderInterface;
import com.squareup.picasso.Picasso;

import org.json.JSONArray;
import org.json.JSONException;
import org.json.JSONObject;

import java.io.File;
import java.io.FileWriter;
import java.io.IOException;
import java.util.HashMap;
import java.util.Map;
import java.util.concurrent.TimeUnit;

public class MTHeraApplication extends HeraApplication {
    //到综正式license
    private static final String TX_LICENSE_KEY_DZ = "11f624a43e9530b3e7b1838bc9853d3c";
    private static final String TX_LICENSE_URL_DZ = "http://license.vod2.myqcloud.com/license/v1/ecb19b517b077f9e721016fe8de7a1b6/TXLiveSDK.licence";

    private static final int appId = 10;
    private static final int wnsId = 202519;
//    private ICustomEventDispatch customEventDispatch;

    private static MTHeraApplication sInstance;

    private final boolean enableLive = false;

    @Override
    public void onCreate() {
        sInstance = this;

        System.out.println("MTHeraApplication: onCreate");
        Trace.beginSection("MTHeraApplication.onCreate");
        Trace.beginSection("super.onCreate");
        super.onCreate();
        Trace.endSection();

        if (!ProcessUtils.getCurrentProcessName().equals(getPackageName())
                && !ProcessUtils.getCurrentProcessName().contains(MSCProcess.PROCESS_SUFFIX_NAME)) {
            System.out.println("abort init on process: " + ProcessUtils.getCurrentProcessName());
            return;
        }

        AppContextGetter.setContext(this);
        ApplicationSingleton.bindInstance(this);//patch for passport
        DebugHelper.setDebug(true);
        BaseConfig.channel = BaseConfig.UNDEFINED_CHANNEL;
        //坑。 网络库通过渠道号来判断是否是测试包，渠道号不能为空手动初始化一下 位置不要挪 初始化前后不一致有概率导致测试拦截器内部空指针

        ContextSingleton.bindInstance(this);

        ServiceLoader.setContext(this);

        // horn需要在babel之前
        Horn.init(this, new HornConfiguration() {

            @Override
            public IUUIDService uuidService() {
                return UUIDServiceMgr.get().service();
            }

            @Override
            public ISharkPushService sharkPushService() {
                return SharkPushServiceMgr.get().service();
            }
        });
        Horn.debug(this, false);

        HornDeveloperKit.init(this); // 进入horn调试面板需要此行，并因此需要初始化horn，否则不需要初始化horn
        Babel.init(getApplicationContext(), new BabelConfig() {
            @Override
            public String getAppVersion() {
                return "appVersion";
            }

            @Override
            public String getToken() {
                return "token";
            }

            @Override
            public String getApkHash() {
                return "apkHash";
            }

            @Override
            public String getChannel() {
                return "channel";
            }

            @Override
            public String getUuid() {
                return "uuid";
            }
        });
//        UriAnnotationHandler.setAddNotFoundHandler(false);
//        UriSourceTools.setDisableExportedControl(true);
        UUIDInit.init(this);

        try {
            Picasso.setModulesEnabled(false);
        } catch (Exception e) {
        }
        initNVGlobal();
        try {
            MeituanConfig.INSTANCE.init(this);
        } catch (Exception e) {
            e.printStackTrace();
        }
        DebugHelper.useMtWebView = true;
        ServiceLoader.setContext(this);
        ServiceLoader.init(this, new ServiceLoader.OnErrorListener() {
            @Override
            public void onError(Throwable error) {
                Log.e("bunny", "onError: ", error);
            }
        });

        initNetwork();
        try {
            ArbiterHook.injectInstrumentationHook(this);
            ArbiterHook.setDebug(true);
            ArbiterHook.addMTInstrumentation(new MSCInstrumentation(this));
            ArbiterHook.addMTInstrumentation(new DataPrefetchInstrument());
        } catch (Exception e) {
            e.printStackTrace();
        }
        // 对线程没有要求, 独立App没有MTShell时，从多个Shell组合中选择合适的Shell
        WeaverProxy.init(this, new CommonShell());

        Statistics.initStatistics(this, new IEnvironment() {
            @Override
            public String getAppName() {
                return "Test";
            }

            @Override
            public String getCh() {
                return "test";
            }

            @Override
            public String getLch() {
                return null;
            }

            @Override
            public String getLat() {
                return null;
            }

            @Override
            public String getLng() {
                return null;
            }

            @Override
            public String getPushId() {
                return null;
            }

            @Override
            public String getSubcid() {
                return "";
            }

            @Override
            public String getImsi() {
                return "";
            }

            @Override
            public String getUid() {
                return "1234";
            }

            @Override
            public String getLoginType() {
                return null;
            }

            @Override
            public int getAppId() {
                return 10;
            }

            @Override
            public String getCityId() {
                return "";
            }

            @Override
            public String getMno() {
                return "";
            }

            @Override
            public String getPs() {
                return "";
            }
        });

        initMtWebView();
        // 现在主线程初始化，否则预拉取调用Location Provider会有NPE Crash
        MasterLocatorSingleton.getInstance();
        // 初始化msc
        MSCEnvHelper.startHostInit(this);
        Trace.endSection();
        System.out.println("MTHeraApplication: onCreate end111");
    }

    private void initMtWebView() {
//        initMMP();
        System.out.println("mtwebview_load");

        // MTWebView 配置
        // 这个不用了 mtwebview_load
        Horn.register("mtwebview_load", new HornCallback() {
            @Override
            public void onChanged(boolean enable, String result) {
                System.out.println("mtwebview_load " + result);
                System.out.println("mtwebview_load " + enable);
                if (enable) {
                    try {
                        //    MTWebViewManager.onHornConfig(new JSONObject(result));
                    } catch (Exception ignore) {

                    }
                }
            }
        }, new HashMap<String, Object>() {{
            put("buildFingerprint", Build.FINGERPRINT);
        }});
        // 新增下面这两个
        Horn.register("mtwebview_msc_use", new HornCallback() {
            @Override
            public void onChanged(boolean enable, String result) {
                System.out.println("[mtwebview_msc_use] enable:" + enable + " result:" + result);
                if (enable) {
                    try {
                        MTWebViewMSCConfigManager.onMscIdHornConfig(new JSONObject(result));
                    } catch (JSONException e) {
                        e.printStackTrace();
                    }
                }
            }
        });
        // 注释掉下面的Horn注册，让所有业务都用上自研内核
//        Horn.register("mtwebview_msc_use_config", new HornCallback() {
//            @Override
//            public void onChanged(boolean enable, String result) {
//                System.out.println("[mtwebview_msc_use_config] enable:" + enable + " result:" + result);
//                if (enable) {
//                    try {
//                        MTWebViewMSCConfigManager.onHornConfig(new JSONObject(result));
//                    } catch (JSONException e) {
//                        e.printStackTrace();
//                    }
//                }
//            }
//        });
        // 禁用强制降级配置
        MTWebViewCIPStorage.getKV(AndroidReflectHelper.is64Bit()).setBoolean(MTWebViewConfigManager.FORCE_DOWNGRADE_KEY, false);
        // 临时使用 diva 获取自研内核新版本 TODO 后续MSC在App版给sample申请信息并将内核拉包环境写死diva测试环境，然后内核在diva测试环境发新版
        MTWebViewCIPStorage.getKV(AndroidReflectHelper.is64Bit()).setBoolean(MTWebViewConfigManager.USE_DIVA_DOWNLOAD_KEY, false);
        // 启用MSC的自研内核开关
        CIPStorageCenter.instance(MTWebViewEnvManager.getContext(), "mtplatform_mtwebview_msc", CIPStorageCenter.MODE_MULTI_PROCESS).setBoolean("msc_use_mtwebview_enable_key", true);
//        MTWebViewConfigManager.setUseDivaDownload(false);
        // 配置自研内核的环境信息
        MTWebViewManager.init(new IMTWebViewEnvInfo() {
            @Override
            public Context getApplicationContext() {
                return MTHeraApplication.this;
            }

            @Override
            public String getUUID() {
                return "";
            }

            @Override
            public String getUserID() {
                return "";
            }

            @Override
            public String getChannel() {
                return "demoApp";
            }

            @Override
            public long getCityID() {
                return 0;
            }

            @Override
            public int getAppID() {
                return -1;
            }
        });
    }

    @ServiceLoaderInterface(key = "SampleMSCInitializer", interfaceClass = MSCHostInitializer.class)
    @Keep
    public static class Initializer implements MSCHostInitializer {

        @Override
        public void init(Context context) {
            sInstance.initMMP();
        }
    }

    private volatile static boolean sSampleHostInited;

    public void initMMP() {
        if (sSampleHostInited) {
            return;
        }
        sSampleHostInited = true;

        // 在本方法被Application主动调用时，用于触发MeituanHelper中的初始化（独立app一般属于此情况）
        // 也可选择不主动调用而由Activity等被动触发（模拟美团app中的情况）
        MSCEnvHelper.startHostInit(this);
        initDDDDebug();
        //初始化Pike用来下线小程序
        PikeGlobal.init(this, appId, new PikeGlobal.UnionidCallback() {
            @Override
            public String unionid() {
                return MSCEnvHelper.getEnvInfo().getUUID();
            }
        });
        DDLoaderManager.init(this, new LoaderEnvironment() {
            @Override
            protected String getUuid() {
                return MSCEnvHelper.getEnvInfo().getUUID();
            }

            @Override
            protected String getUserId() {
                return MSCEnvHelper.getEnvInfo().getUserID();
            }

            @Override
            protected String getChannel() {
                return MSCEnvHelper.getEnvInfo().getChannel();
            }

            @Override
            protected int getMobileAppId() {
                return MSCEnvHelper.getEnvInfo().getMobileAppId();
            }

            @Override
            public boolean enableDebug() {
                return DDLoaderDebugHelper.enableDDLoaderDebug();
            }

            @Override
            public IPushService getPushImpl() {
                return DDPushSyncService.getInstance("group");
            }
        });
//        new HeraInitInterface().init(this);
        MSCEnvHelper.setCustomUserAgentSuffix("app", "meituan");
//        MSCEnvHelper.addBuildInPackage("testappid", "file:///android_asset/youxuan.zip");
//        MSCEnvHelper.addBuildInPackage("testappid1", "file:///android_asset/demo.zip");
//        MSCEnvHelper.addBuildInPackage("b5aa7771ad8a4870", "file:///android_asset/mmppreset_b5aa7771ad8a4870_953.zip");
//        MMPEnvHelper.addBuildInPackage("fmp_miniprogram_demo", "file:///android_asset/fmp_miniprogram_demo.zip");
//        MSCEnvHelper.setDefaultAppID("testappid");
//        MSCEnvHelper.setFusionPageManager(new FusionPageManager().addMpContainerSupportStackChange("testappid","mmp://www.meituan.com/mmp"));

        //在主进程中初始化框架配置，启动框架服务进程
//        if (HeraTrace.isMainProcess(this)){
//            HeraConfig config = new HeraConfig.Builder()
//                    .addExtendsApi(new ApiOpenLink(getApplicationContext()))
//                    .addExtendsApi(new ApiOpenPageForResult())
//                    .setDebug(true)
//                    .build();
//            HeraService.start(this.getApplicationContext(), config);
//        }

        //  com.meituan.android.singleton.NetInit.init(false);

//        customEventDispatch = registerCustomEvent("custRandomMessage");
//        MMPEnvHelper.registerCustomApi("sampleCustApi", null, SampleCustomEventApi.SampleCustApi::new);

        WebViewDirectoryFixer.fixMultiProcessWebView();
        CrashReporterInit.init(this);
//        new KNBInit().init(this);
//        LegacyPreloadManager.forcePreload();

        MSCExecutors.submit(new Runnable() {
            @Override
            public void run() {
//                if (MSCEnvHelper.getDefaultSharedPreferences().getBoolean(DebugManager.MMP_DEBUG_ENABLE_PRELOAD, false)) {
                // 调试设置，用于防止预加载干扰调试
//                    new MSCInitInterface().asyncInit(MTHeraApplication.this);
//                } else {
//                    MMPHornPreloadConfig.get().registerHorn(MTHeraApplication.this, false, false);
//                }

                //初始化地图sdk 用户unitu map 选择位置 显示位置
//                MapsInitializer.initMapSDK(MTHeraApplication.this, MapsInitializer.MAP_MTMAP, -1, null, null);
            }
        });
        MSCConfig.registerHorn();
        MSCExecutors.postOnUiThread(new UiRunnable() {
            @Override
            public void run() {
//                MTGuard.init(getApplicationContext());
//                initMTPay(MTHeraApplication.this);

            }
        });

        CrashReporterInit.init(this);
        UserCat.init(this);
        initLive();
        initPrivacy();

        initMetrics(this);
        initVideo();
    }

    private void initDDDDebug() {
        DebugHelper.setDebug(true);
        DDLoaderDebugHelper.setDDLoaderDebugEnable(true);
        DDLoaderContext.setApplicationId("com.sankuai.meituan");
        // TODO: 2022/4/13 tianbin 需要搭配appVersionName一起使用
//        DDLoaderContext.setAppVersionCode(110019200);
    }

    /**
     * https://km.sankuai.com/page/1215866077#id-3.2%E5%88%9D%E5%A7%8B%E5%8C%96
     */
    private void initPrivacy() {
        PrivacyProxy.init(this, new PrivacyConfig() {
            @Override
            public int getAppLogo() {
                return R.drawable.ic_logo;
            }

            @Override
            public String uuid() {
                return BaseConfig.uuid;// uuid
            }

            @Override
            public String app() {
                //如果使用不存在标识请求，接口无数据
                return "group";// 应用注册标识，链接内appKey：https://privacy.sankuai.com/api/admin/app/appKeys
            }
        });

        ApplicationLifecycleMonitor.ALL.addReEnterForegroundListener(new Runnable() {
            @Override
            public void run() {
                LifeCycleMonitor.onAppForeground();
            }
        });

        ApplicationLifecycleMonitor.ALL.addEnterBackgroundListener(new Runnable() {
            @Override
            public void run() {
                LifeCycleMonitor.onAppBackground();
            }
        });
    }

    private void initNVGlobal() {
        NVGlobal.init(this, appId, new NVGlobal.UnionidCallback() {
            @Override
            public String unionid() {
//                String uuid = BaseConfig.uuid;
//                if (uuid == null) {
//                    return "";
//                }
//                return uuid;
                return MSCEnvHelper.getEnvInfo().getUUID();
            }
        });
        NVGlobal.setBackgroundMode(false);
        NVGlobal.setMultiProcessSupport(true);
        NVGlobal.setDebug(true);
        NVAppMockManager.instance().mock(true);
    }

    private void initLive() {
        if (!enableLive) {
            return;
        }

        Map<String, String> map = getHornLicense(this);
        if (map == null) {
            return;
        }
        String licenseUrl = map.get("licenseUrl");
        String licenseKey = map.get("licenseKey");
        if (TextUtils.isEmpty(licenseKey)) {
            licenseKey = TX_LICENSE_KEY_DZ;
        }
        if (TextUtils.isEmpty(licenseUrl)) {
            licenseUrl = TX_LICENSE_URL_DZ;
        }

        final String fLicenseKey = licenseKey;
        final String fLicenseUrl = licenseUrl;


        MTLiveEngineEx.getInstance().setLiveLicenseProvider(new ILiveLicenseProvider() {
            @Override
            public String getLicenseUrl() {
                return fLicenseUrl;
            }

            @Override
            public String getLicenseKey() {
                return fLicenseKey;
            }
        });

        boolean isDebug = "qatest".equals(com.sankuai.meituan.BuildConfig.FLAVOR)
                || "meituanInternal".equals(com.sankuai.meituan.BuildConfig.FLAVOR)
                || com.sankuai.meituan.BuildConfig.DEBUG;
        try {
            MTLiveEngineEx.getInstance().init(this,
                    new MTLiveConfigEx.Builder()
                            .debug(isDebug)
                            .monitorProvider(new ILiveMonitorProvider() {
                                @Override
                                public String getUUID() {
                                    return GetUUID.getInstance().getSyncUUID(MTHeraApplication.this, null);
                                }

                                @Override
                                public String getLocatedCityName() {
                                    return null;
                                }

                                @Override
                                public int getAppId() {
                                    return 10;
                                }

                                @Override
                                public String getUserId() {
                                    final UserCenter userCenter = UserCenter.getInstance(MTHeraApplication.this);
                                    long id = 0;
                                    if (userCenter != null && userCenter.isLogin()) {
                                        id = userCenter.getUser().id;
                                    }
                                    return String.valueOf(id);
                                }
                            }).build()
            );
        } catch (Throwable th) {
        }
        AppBus.getInstance().register((AppBus.OnBackgroundListener) () -> MTVodEnv.getInstance().setBackground(true));
        AppBus.getInstance().register((AppBus.OnForegroundListener) () -> MTVodEnv.getInstance().setBackground(false));
    }

    private static final String LIVE_SDK_LISENCE = "live_sdk_lisence";

    private Map<String, String> getHornLicense(Context context) {
        final String lisenceInfo = Horn.accessCache(LIVE_SDK_LISENCE);
        if (!TextUtils.isEmpty(lisenceInfo) && context != null && context.getApplicationInfo() != null) {
            try {
                JSONObject info = new JSONObject(lisenceInfo);
                JSONArray whiteList = info.getJSONArray("mLiveLicense");
                for (int i = 0; i < whiteList.length(); i++) {
                    String license = whiteList.optString(i);
                    Gson gson = new Gson();
                    Map<String, String> map = new HashMap<>();
                    map = gson.fromJson(license, map.getClass());
                    String bundleName = map.get("bundleName");

                    if (bundleName != null && bundleName.equals(context.getApplicationInfo().packageName)) {
                        return map;
                    }
                }
            } catch (Exception e) {
//                Logger.getMetricxLogger().e("getHornLicense", e);
                Logan.w("MTLiveAsyncTask fail", Logan.CODE_LOG);
            }
        }
        // 兜底
        Map<String, String> map = new HashMap<>();
        map.put("licenseKey", TX_LICENSE_KEY_DZ);
        map.put("licenseUrl", TX_LICENSE_URL_DZ);
        return map;
    }

    private void initNetwork() {
        RetrofitCallFactorySingleton.init(this, new INetFactoryImpl(), new IAnalyseInfor() {
            @Override
            public long userId() {
                return UserCenterSingleton.getInstance().getUserId();
            }

            @Override
            public long cityId() {
                return CityControllerSingleton.getInstance().getCityId();
            }

            @Override
            public String UUID() {
                String uuid = null;
                if (UUIDProviderSingleton.getInstance() != null) {
                    uuid = UUIDProviderSingleton.getInstance().getUUID();
                }
                return (uuid == null) ? "" : uuid;
            }

            @Override
            public int appId() {
                return 10;
            }
        });
        DynLoaderInit.init(this, new DynParamsProvider() {
            @Override
            public long getUserID(Context context) {
                return 0;
            }

            @Override
            public String getChannel(Context context) {
                return "mt-live";
            }

            @Override
            public String getUUID(Context context) {
                return "";
            }
        });

    }

//    private static void initMTPay(Application application) {
////        MetricsSpeedMeterTask task = MetricsSpeedMeterTask.createCustomSpeedMeterTask(METRICS_PAY_SDK_INIT_SPEED);
////        task.recordStep("paySDKInit start");
//        final LocationCache locationCache = LocationCacheSingleton.getInstance();
//        final ICityController cityController = CityControllerSingleton.getInstance();
//        final UserCenter userCenter = UserCenter.getInstance(application);
//        final FingerprintManager fingerprintManager = FingerprintManagerSingleton.getInstance();
//
//        MTPayConfig.config(application.getApplicationContext(), new MTPayProvider() {
//            @Override
//            public void dppv(long time, String command, int network, int tunnel, int code, int requestBytes, int responseBytes, int responseTime, String ip) {
////                HttpApiMonitorService.getInstance(getApplicationContext()).pv3(time, command, network, tunnel, code, requestBytes, responseBytes, responseTime, ip);
//            }
//
//            @Override
//            public String getChannel() {
//                return BaseConfig.channel;
//            }
//
//            @Override
//            public Location getLocation() {
//                return locationCache.getCachedLocation();
//            }
//
//            @Override
//            public String getCityId() {
//                return String.valueOf(cityController.getCityId());
//            }
//
//            @Override
//            public String getDeviceId() {
//                return BaseConfig.deviceId;
//            }
//
//            @Override
//            public String getUuid() {
//                return BaseConfig.uuid;
//            }
//
//            @Override
//            public String getUserId() {
//                return String.valueOf(userCenter.isLogin() ? userCenter.getUser().id : -1);
//            }
//
//            @Override
//            public String getAppName() {
//                return Consts.APPNAME;
//            }
//
//            @Override
//            public String getAppVersionName() {
//                return BaseConfig.versionName;
//            }
//
//            @Override
//            public int getAppVersionCode() {
//                return BaseConfig.versionCode;
//            }
//
//            @Override
//            public String getCampaign() {
//                return UtmCampaignUtils.getUtmCampaign(userCenter.getLoginType());
//            }
//
//            @Override
//            public String getFingerprint() {
//                return fingerprintManager.fingerprint();
//            }
//
//            @Override
//            public String getUserToken() {
//                return userCenter.isLogin() ? userCenter.getUser().token : "";
//            }
//
//            @Override
//            public String getWechatKey() {
//                return OauthManager.WEIXIN_APP_ID;
//            }
//
//            @Override
//            public String getPlatform() {
//                return Consts.PLATFORM;
//            }
//
//            @Override
//            public String getOsVersion() {
//                return BaseConfig.os;
//            }
//
//            @Override
//            public Bitmap createCode128(String str, int codeWidth, int codeHeight) {
////                return ZXingUtils.createBarCode(str, BarcodeFormat.CODE_128, codeWidth, codeHeight);
//                return null;
//            }
//
//            @Override
//            public Bitmap createQRCODE(String str, int codeWidth, int codeHeight) {
////                return ZXingUtils.createBarCode(str, BarcodeFormat.QR_CODE, codeWidth, codeHeight);
//                return null;
//            }
//
//            @Override
//            public boolean isAppMockOn() {
//                return PreferenceManager.getDefaultSharedPreferences(application.getApplicationContext()).getBoolean("enable_dianping_mock", false);
//            }
//
//            @Override
//            public String getAppMockUrl() {
//                return PreferenceManager.getDefaultSharedPreferences(application.getApplicationContext()).getString("dianping_mock_url", "");
//            }
//
//            public PayBaseImageLoaderInterface getImageLoader() {
////                return new PayMTPicassoImageLoader(application.getApplicationContext());
//                return null;
//
//            }
//
//            public LoginInterface getAccountLogin() {
//                return new MtLoginImpl();
//            }
//        });
////        task.recordStep("payConfig done");
//        MTPayConfig.configUserLockExceptionHandler(new MTPayUserLockExceptionHandler() {
//            @Override
//            public void handleUserLockException(final Activity activity, int code, String message) {
//                //指定com.meituan.android.library作为组件名
//                String componentName = com.meituan.android.library.BuildConfig.APPLICATION_ID;
//                //用来统计登出信息，支付的登出是通用网络拦截，url不确定
//                LogoutInfo logoutInfo = new LogoutInfo(componentName, new LogoutInfo.NativeUrlData("url unknown", code), null);
//                UserLockHandler.getInstance().newUserUnlock(activity, code, message, new UserLockHandler.Callback() {
//                    @Override
//                    public void callback(boolean unlockFinish, Throwable e) {
//                        if (unlockFinish) {
//                            activity.finish();
//                        }
//                    }
//                }, logoutInfo);
//
//            }
//        });
////        task.recordStep("configUserLockExceptionHandler done").report();
//    }

//    ICustomEventDispatch registerCustomEvent(String customEventName) {
//        String newName = "customEvent_" + customEventName;
//        if (MSCInnerHelper.getCustomEventDispatchs().containsKey(newName)) {
//            return MSCInnerHelper.getCustomEventDispatchs().get(newName);
//        }
//        ICustomEventDispatch customEventDispatch = new CustomEventDispatch(newName);
//        MSCInnerHelper.getCustomEventDispatchs().put(newName, customEventDispatch);
//        return customEventDispatch;
//    }

    private void initMetrics(Application app) {

        final boolean isMainProcess = ProcessUtils.isMainProcess(app);
        if (isMainProcess) {
            MetricsLaunchFunnelTask.getInstance().init(app);
            //如果本次不是点击Icon冷启动的话，主动触发一次上报。
            Jarvis.newSingleThreadScheduledExecutor("LaunchFunnelReport-Thread").schedule(new Runnable() {
                @Override
                public void run() {
                    //主动触发一次历史数据上报
                    MetricsLaunchFunnelTask.getInstance().report();
                }
            }, 20_000, TimeUnit.MILLISECONDS);
            //广告状态先记为unknown
            MetricsLaunchFunnelTask.getInstance().recordDimension("ad", "-1");
        } else {//由于此时T1还没结束，只有cache Step T1，没有写入内存的点，所以可以不用清除数据。
            MetricsLaunchFunnelTask.getInstance().setEnable(false);
        }
        Metrics.getInstance().init(app, new MetricsConfig() {

            @Override
            public String getUuid() {
                return GetUUID.getInstance().getSyncUUID(app, null);
            }

            @Override
            public String getChannel() {
                return BaseConfig.channel;
            }

            @Override
            public long getCityId() {
                long cityid = -1;
                try {
                    ICityController cityController = CityControllerSingleton.getInstance();
                    cityid = cityController.getCityId();
                } catch (Throwable e) {

                }
                return cityid;
            }

            @Override
            public String getAppName() {
                return isTestEnvironment() ? "androidtest" : "group";
            }

            @Override
            public String getApkHash() {
                return "apkHash";
            }

            @Override
            public String getBuildVersion() {
                String buildTime = BaseConfig.getBuildTime();
                if (!TextUtils.isEmpty(buildTime)) {
                    try {
                        String[] builds = buildTime.split("\\.");
                        //为了对齐iOS，格式改为appVersion.buildId，ex：10.2.201.11233
                        return com.sankuai.meituan.BuildConfig.VERSION_NAME + "." + builds[1];
                    } catch (Throwable throwable) {
                        return "";
                    }
                }
                return "";
            }

            @Override
            public String getUserId() {
                final UserCenter userCenter = UserCenter.getInstance(app);
                long id = 0;
                if (userCenter != null && userCenter.isLogin()) {
                    id = userCenter.getUser().id;
                }
                return String.valueOf(id);
            }

            @Override
            public String getToken() {
                return isTestEnvironment() ? "55507bb5ce08881827921b6c" : "566a3fa581e6e3b434f44a75";
            }

            @Override
            public Strategy getReportStrategy() {
                return new Strategy() {
                    @Override
                    public boolean needReport(int exceptionType, Thread thread, Throwable ex, ExceptionHandler handler) {
                        final String SP_PREFIX = "MTStrategy";
                        final int crashReportCount = 5;
                        final long TIME_ONE_HOUR = 3600 * 1000;
                        SharedPreferences statusPreferences = handler.getContext().getSharedPreferences(SP_PREFIX + "_" + handler.getName(), Context.MODE_PRIVATE);
                        String key = "anr_crash_report_count";
                        long curTime = System.currentTimeMillis();
                        long baseReportTime = statusPreferences.getLong("baseCrashReportTime", 0);
                        long timeOffset = curTime - baseReportTime;
                        int reportCount = statusPreferences.getInt(key, 0);
                        SharedPreferences.Editor edit = statusPreferences.edit();

                        if (timeOffset > 0 && timeOffset < TIME_ONE_HOUR) {
                            if (reportCount < crashReportCount) {
                                edit.putInt(key, ++reportCount);
                                // 策略里面改成commit提交吧 测试时候发现崩溃的时候apply的话容易丢失计数
                                return edit.commit();
                            } else {
                                return false;
                            }
                        } else {
                            edit.putLong("baseCrashReportTime", curTime);
                            edit.putInt(key, 1);
                            // 策略里面改成commit提交吧 测试时候发现崩溃的时候apply的话容易丢失计数
                            return edit.commit();
                        }
                    }
                };
            }

            @Override
            public Environment.IStatisticSessionGetter getStatisticSessionGetter() {
                return new Environment.IStatisticSessionGetter() {
                    @Override
                    public String getSession() {
                        return Statistics.getSession();
                    }
                };
            }

            @Override
            public boolean isAnrEnable() {
                if (isMainProcess) {
                    return true;
                }

                String processName = ProcessUtils.getCurrentProcessName(app);
                return processName != null && (processName.contains("MgcProcess") || processName.contains("PinProcess"));
            }

            @Override
            public boolean isLagEnable() {
                return isMainProcess;
            }

            @Override
            public boolean isSampleEnable() {
                return isMainProcess;
            }

            @Override
            public boolean isBigImageEnable() {
                return isMainProcess;
            }

            @Override
            public boolean isStartupTimerEnable() {
                return isMainProcess;
            }

            @Override
            public boolean isTrafficNativeHookEnable() {
                return isTestEnvironment();
            }

            boolean isTestEnvironment() {
                return com.sankuai.meituan.BuildConfig.DEBUG
                        || "speedCompilation".equals(com.sankuai.meituan.BuildConfig.FLAVOR)
                        || "meituanInternal".equals(com.sankuai.meituan.BuildConfig.FLAVOR)
                        || "qatest".equals(com.sankuai.meituan.BuildConfig.FLAVOR);
            }

            @Override
            public boolean isSignalAnrDetectorEnable() {
                return false;
            }

            @Override
            public String getAnrOption() {
                JSONObject jsonObject = new JSONObject();
                return jsonObject.toString();
            }
        }).setDebug(true);
//        Metrics.getInstance().setReportCategory(METRICS_REPORT_CATEGORY);
        Metrics.getInstance().addInterceptor(new MetricsDefaultInterceptor() {
            static final String HOMEPAGE = "com.meituan.android.pt.homepage.activity.MainActivity";

            @Override
            public void onNewFpsEvent(FpsEvent event) {
                if (event == null) {
                    return;
                }
                if (TextUtils.equals(event.getType(), "scroll") && TextUtils.equals(event.getName(), HOMEPAGE)) {
                    Map<String, Object> map = new ArrayMap<>();
                    map.put("page", event.getName());
                    map.put("scrollFpsAvg", event.getAvgFps());
                    map.put("scrollFpsMin", event.getMinFps());
                    map.put("channel", BaseConfig.channel);
                    long cityid = -1;
                    try {
                        ICityController cityController = CityControllerSingleton.getInstance();
                        cityid = cityController.getCityId();
                    } catch (Throwable e) {

                    }
                    map.put("cityId", cityid);
                    Babel.log("homepage_fps", "homepage_fps", map);
                }

            }

            @Override
            public void onNewSpeedMeterEvent(SpeedMeterEvent event) {
                super.onReportSpeedMeterEvent(event);
                if (event == null) {
                    return;
                }
                //用于上报T2、T3时间戳
                if (MetricsSpeedMeterType.LAUNCH == event.getType()) {
                    PreloadInjection.notifyPreloadEnd("T2", "");
                } else if (MetricsSpeedMeterType.PAGE == event.getType() && HOMEPAGE.equals(event.getPageName())) {
                    PreloadInjection.notifyPreloadEnd("T3", "");
                }
            }

            @Override
            public void onReportMemoryEvent(MemoryEvent event) {
                super.onReportMemoryEvent(event);
            }

            @Override
            public void onReportFpsEvent(FpsEvent event) {
                super.onReportFpsEvent(event);
                if (!("mobile.fps.scroll.avg.v2.n".equals(event.getLocalEventType()))) {
                    return;
                }
                Map<String, Object> details = event.getDetails();
                try {
                    File file = CIPStorageCenter.requestExternalFilePath(Metrics.getInstance().getContext(), "msc", "metrics.json");
                    File fileDir = file.getParentFile();
                    if (!fileDir.exists()) {
                        fileDir.mkdirs();
                    }
                    if (file.exists()) {
                        file.delete();
                    }
                    Log.e("MSCLOG", " benchmark_write_file1:  " + file.getAbsolutePath());
                    FileUtils.writeFile(file, new Gson().toJson(details), false);
                } catch (Throwable e) {
//                    System.out.println(QA_TAG + " benchmark_write_failed " + Log.getStackTraceString(e).replaceAll("\\n", ";"));
                }
                Log.e("MSCLOG", "onReportEvent " + event.getLocalEventType() + " " + event.getPageName());
                Log.e("MSCLOG", "details " + details);
            }
        });
//
//        babelInterceptor();
//
//        interceptCustomTraffic();
    }

    private void initVideo() {
        MTLiveEngineEx.getInstance().init(getApplicationContext(),
                new MTLiveConfigEx.Builder()
                        .debug(DebugHelper.isDebug())
                        .monitorProvider(new ILiveMonitorProvider() {
                            @Override
                            public String getUUID() {
                                // 用户UUID 必须
                                return GetUUID.getInstance().getSyncUUID(getApplicationContext(), null);
                            }
                            @Override
                            public String getLocatedCityName() {
                                // 城市（有就传 没有就不传）
                                return null;
                            }

                            @Override
                            public int getAppId() {
                                //app id 必须
                                return 10;
                            }

                            @Override
                            public String getUserId() {
                                // user id 没有可传null
                                final UserCenter userCenter = UserCenter.getInstance(getApplicationContext());
                                long id = 0;
                                if (userCenter != null && userCenter.isLogin()) {
                                    id = userCenter.getUser().id;
                                }
                                return String.valueOf(id);
                            }
                        }).build()
        );
    }
}
