include ':tech-stack-interface'
include ':tech-stack-statistics'
//include ':msc-renderer-devtools'
include ':common-interface'
include ':msc-util'
//include ':render'
include ':jse'
include ':interface'
include ':library'
include ':msc-plugin-devtools'
include ':msc-plugin-locate'
include ':msc-test-aop'
include ':msc-benchmark'

include ':msc-mt'
include ':msc-dp'
include ':sample'

// 下面的配置用于配置 DependencySubstitutionPlugin 插件
// 详见：https://km.sankuai.com/page/200615922
buildscript {
    repositories {
        maven {
            url "http://depot.sankuai.com/nexus/content/groups/public/"
        }
    }
    dependencies {
        // WARNING: 不要使用 1.0.7 版本，会和 nammu 插件冲突
        classpath "com.dianping.gradle.gdp:dependency-substitution:1.0.3"
    }
}
apply plugin: "com.dianping.dependency.substitution"
