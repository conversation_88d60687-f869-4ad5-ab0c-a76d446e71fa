buildscript {
    repositories {
        maven {
            url "http://depot.sankuai.com/nexus/content/groups/public/"
        }
    }
    dependencies {
        classpath "com.dianping.gradle.gdp:dependency-substitution:1.0.3"
    }
}

/**
 * 要指定某个模块设置模块依赖的时候，直接反注释下面的配置就行了
 * 注意：打包前要将下面的配置都反注释掉
 *
 * Substitution插件配置说明
 * 项目名称: litho
 * 项目路径: lithodynamic/library
 * 组件的GroupID: com.meituan.android.lithodynamic
 * 组件的ModuleName: library
 */
GDP.projectConfigs.addAll([
//        new ProjectConfig("csslib", "msc-css-lib/android-module", "com.meituan.android.msc", "csslib"),
// new ProjectConfig("msc-trace", "msc-trace/trace", "com.meituan.msc.trace", "trace"),
// new ProjectConfig("trace-interface", "msc-trace/trace-interface", "com.meituan.msc.trace", "trace-interface"),
//    new ProjectConfig("blink", "msc-renderer/android-module", "com.meituan.android.msc", "blink"),
//    new ProjectConfig("msc-render", "msc-native-renderer-android/render", "com.meituan.android.msc", "msc-render"),
])
